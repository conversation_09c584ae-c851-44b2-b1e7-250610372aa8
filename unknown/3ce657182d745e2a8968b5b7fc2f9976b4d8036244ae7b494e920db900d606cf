package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.service.report.TraceReportService;
import net.airuima.rbase.web.rest.report.dto.ForwardTraceResultDTO;
import net.airuima.rbase.web.rest.report.dto.ForwardTraceStepDetailDTO;
import net.airuima.rbase.web.rest.report.dto.ReverseTraceReportRequestDTO;
import net.airuima.rbase.web.rest.report.dto.ReverseTraceReportResultDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 追溯报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Tag(name = "追溯报表Resource")
@RestController
@RequestMapping("/api/report/trace-report")
@AuthorityRegion("报表看板")
public class TraceReportResource {
    private final Logger log = LoggerFactory.getLogger(TraceReportResource.class);

    private final TraceReportService traceReportService;

    public TraceReportResource(TraceReportService traceReportService) {
        this.traceReportService = traceReportService;
    }

    /**
     * 生成过程正向追溯（列表）
     *
     * @param category  单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serNumber 单据号
     * @return : org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.ForwardTraceResultDTO>
     * <AUTHOR>
     **/
    @Operation(summary = "生成过程正向追溯（列表）")
    @Parameters({
            @Parameter(name = "category", description = "类型：0:订单号，1:工单号，2:子工单号，3：容器号，4：SN"),
            @Parameter(name = "serNumber", description = "编码")
    })
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/forward-trace/category/{category}/serNumber/{serNumber}")
    public ResponseEntity<ResponseData<List<ForwardTraceResultDTO>>> workSheetTraceReport(@PathVariable("category") Integer category, @PathVariable("serNumber") String serNumber) {
        try {
            List<ForwardTraceResultDTO> forwardTraceResultDTOList = traceReportService.workSheetTraceReport(category, serNumber);
            return ResponseData.ok(forwardTraceResultDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /**
     * 生成过程正向追溯（列表）-导出
     *
     * @param category  单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * @param response
     * <AUTHOR>
     **/
    @Operation(summary = "生成过程正向追溯（列表）-导出")
    @Parameters({
            @Parameter(name = "category", description = "类型：0:订单号，1:工单号，2:子工单号，3：容器号，4：SN"),
            @Parameter(name = "serNumber", description = "编码")
    })
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/forward-trace/export/category/{category}/serNumber/{serialNumber}/excelType/{excelType}")
    public void export(@PathVariable("category") Integer category,
                       @PathVariable("serialNumber") String serialNumber,
                       @PathVariable("excelType") String excelType,
                       HttpServletResponse response) throws IOException {
       traceReportService.workSheetTraceReportExport(category, serialNumber, excelType, response);
    }

    /**
     * 生成过程正向追溯（工序详情）
     *
     * @param wsId 工单ID/子工单ID
     * @param category   单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * @return : org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.ForwardTraceStepDetailDTO>
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/forward-trace/detail/{wsId}")
    @Operation(summary = "通过条件正向追溯生产过程明细数据",description = "工序生产数据(包含人、机、料、法等数据)追溯",parameters = {
            @Parameter(name = "wsId", description = "工单投产粒度投产时为工单主键ID，子工单投产粒度投产时为子工单主键ID",required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH),
            @Parameter(name = "category",required = true, description = "查询序列号类型",example = "0:订单号，1:工单号，2:子工单号，3：容器号，4：SN",schema = @Schema(type = "integer",format = "int32"),in = ParameterIn.QUERY),
            @Parameter(name = "serNumber", description = "查询序列号",required = true,schema = @Schema(type = "string"),in = ParameterIn.QUERY)
    })
    public ResponseEntity<ResponseData<List<ForwardTraceStepDetailDTO>>> stepDetail(@PathVariable("wsId") Long wsId, @RequestParam("category") int category, @RequestParam("serialNumber") String serialNumber) {
        try {
            List<ForwardTraceStepDetailDTO> forwardTraceResultDTOList = traceReportService.stepDetail(wsId,category,serialNumber);
            return ResponseData.ok(forwardTraceResultDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /**
     * 生成过程正向追溯（工序详情）-导出
     *
     * @param wsId 工单ID/子工单ID
     * @param category   单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * <AUTHOR>
     **/
    @Operation(summary = "生成过程正向追溯（工序详情）-导出")
    @Parameters({
            @Parameter(name = "wsId", description = "工单/子工单ID"),
            @Parameter(name = "category", description = "类型：0:订单号，1:工单号，2:子工单号，3：容器号，4：SN"),
            @Parameter(name = "serNumber", description = "编码")
    })
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/forward-trace/export/wsId/{wsId}")
    public void exportStepDetail(@PathVariable("wsId") Long wsId,
                                 @RequestParam("category") int category,
                                 @RequestParam("serialNumber") String serialNumber,
                                 @RequestParam(value = "excelType",required = false) String excelType,
                                 HttpServletResponse response) {
        traceReportService.exportStepDetail(wsId,category,serialNumber,excelType,response);
    }


    /**
     * 反向追溯详情 物料-设备-sn 导出
     * @param reverseTraceReportRequestDto 反向追溯请求详情
     * @param response
     */
    @Operation(summary = "生成过程反向追溯（物料-设备-sn）导出")
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_REVERSE_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/export-reverse-trace")
    public void exportReverseTraceReport(@RequestBody ReverseTraceReportRequestDTO reverseTraceReportRequestDto, HttpServletResponse response) throws Exception{
      traceReportService.exportReverseTraceReport(reverseTraceReportRequestDto,response);
    }


    /**
     * 反向追溯详情 物料-设备-sn
     * @param reverseTraceReportRequestDto 反向追溯请求详情
     * @return
     */
    @Operation(summary = "通过条件反向追溯生产过程明细数据")
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_REVERSE_TRACE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/reverse-trace")
    public ResponseEntity<ResponseData<ReverseTraceReportResultDTO>> reverseTraceReport(@Parameter(schema = @Schema(implementation = ReverseTraceReportRequestDTO.class)) @RequestBody ReverseTraceReportRequestDTO reverseTraceReportRequestDto){

        try {
            ReverseTraceReportResultDTO reverseTraceReport = traceReportService.getReverseTraceReport(reverseTraceReportRequestDto);
            return ResponseData.ok(reverseTraceReport);
        }catch (ResponseException e){
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if ("WORK_SHEET_TRACE_REPORT_READ".equals(authority)) {
            return "浏览生产正向追溯报表";
        } else if ("WORK_SHEET_REVERSE_TRACE_REPORT_READ".equals(authority)) {
            return "浏览生产反向追溯报表";
        }
        return "";
    }

}
