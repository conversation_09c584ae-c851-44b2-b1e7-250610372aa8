package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.report.WorkSheetProgressReportDTO;
import net.airuima.rbase.web.rest.report.dto.OrganizationChartResultDTO;
import net.airuima.rbase.web.rest.report.dto.WorkLineFinishInfoChartResultDTO;
import net.airuima.rbase.web.rest.report.dto.WorkOrderDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.PedigreeStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.ProductionCycleStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityLineChartDataDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityOrganizationQueryDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityQueryDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO;
import net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产子工单Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SubWorkSheetRepository extends LogicDeleteableRepository<SubWorkSheet>,
        EntityGraphJpaSpecificationExecutor<SubWorkSheet>, EntityGraphJpaRepository<SubWorkSheet, Long> {


    /**
     * 根据总工单主键ID获取对应的子工单列表
     *
     * @param workSheetId
     * @param deleted
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 通过总工单主键ID及状态区间获取子工单列表
     *
     * @param workSheetId 总工单主键ID
     * @param status      完成状态
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单列表
     * <AUTHOR>
     * @date 2021-04-29
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkSheetIdAndStatusLessThanAndDeleted(Long workSheetId, Integer status, Long deleted);

    /**
     * 通过总工单主键ID及状态区间获取子工单列表
     *
     * @param workSheetId 工单主键id
     * @param status      状态
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkSheetIdAndStatusGreaterThanEqualAndDeleted(Long workSheetId, Integer status, Long deleted);

    /**
     * 根据总工单主键ID获取对应的第一个子工单
     *
     * @param workSheetId 总工单主键ID
     * @param deleted     删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单
     * @throws
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<SubWorkSheet> findTop1ByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 根据总工单主键ID获取子工单的总投产数
     *
     * @param workSheetId 总工单主键ID
     * @param deleted     删除标志
     * @return java.lang.Long 总投产数
     * <AUTHOR>
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(number) from SubWorkSheet where status not in (-2,5) and workSheet.id=?1 and deleted=?2")
    Long statsNumberByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 根据子工单号获取子工单
     *
     * @param serialNumber 子工单号
     * @param deleted      删除
     * @return java.util.Optional<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单
     * <AUTHOR>
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Optional<SubWorkSheet> findBySerialNumberAndDeleted(String serialNumber, Long deleted);


    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select sw from SubWorkSheet sw where sw.serialNumber=?1 and sw.deleted=?2")
    Optional<SubWorkSheet> findBySerialNumberAndDeletedWhenDataFilter(String serialNumber, Long deleted);

    /**
     * 根据子工单号集合获取子工单
     *
     * @param serialNumber 子工单号列表
     * @param deleted      删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单列表
     * <AUTHOR>
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findBySerialNumberInAndDeleted(List<String> serialNumber, Long deleted);

    /**
     * 通过id查询子工单
     *
     * @param id      主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.aps.SubWorkSheet>  子工单
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Optional<SubWorkSheet> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过子工单的主键id、状态修改子工单的信息
     *
     * @param status  工单状态(0:正常态;1:暂停态;2:终止态;3:中途结单)
     * @param id      子工单主键Id
     * @param deleted 逻辑删除
     */
    @Modifying(clearAutomatically = true)
    @Query("update SubWorkSheet set status = ?1 where id in ?2 and deleted = ?3")
    void updateStatus(int status, Long id, Long deleted);

    /**
     * 通过总工单查找所有子工单数量
     *
     * @param workSheetId 总工单主键id
     * @param deleted     逻辑删除
     * @return java.lang.Long 数量
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 通过总工单查找所有已经完成的子工单数量
     *
     * @param workSheetId 工单主键ID
     * @param status 状态
     * @param deleted 删除标记
     * @return java.lang.Long 数量
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndStatusGreaterThanEqualAndDeleted(Long workSheetId, Integer status, Long deleted);

    /**
     * 通过总工单查找所有子工单不合格数量
     *
     * @param workSheetId 总工单id
     * @return java.lang.Long 数量
     */
    @DataFilter(isSkip = true)
    @Query("select sum(subWs.unqualifiedNumber) from SubWorkSheet subWs where subWs.workSheet.id=?1 and subWs.deleted=0")
    Long countSubWorkSheetUnqualifiedNumber(Long workSheetId);


    /**
     * 通过子工单号模糊查询子工单列表
     *
     * @param text     子工单号
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单分页
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select subWorkSheet from SubWorkSheet subWorkSheet where subWorkSheet.serialNumber like concat('%',?1,'%')  and subWorkSheet.deleted=0L")
    Page<SubWorkSheet> findPageBySerialNumber(String text, Pageable pageable);

    /**
     * 通过总工单主键ID逻辑删除子工单
     *
     * @param workSheetId 总工单主键ID
     * @return void
     * <AUTHOR>
     * @date 2021-06-03
     **/
    @Modifying
    @Query("update SubWorkSheet sws set sws.deleted=sws.id where sws.workSheet.id=?1")
    void batchDeleteByWorkSheetId(Long workSheetId);

    /**
     * 通过产线ID、完成状态获取子工单
     *
     * @param workLineId 生产线主键ID
     * @param status     状态
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021-06-18
     **/
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkLineIdAndStatusLessThanAndDeleted(Long workLineId, Integer status, Long deleted);

    /**
     * 通过产线ID、0:已下单;1:投产中 2:已暂停 状态获取正常单 子工单
     *
     * @param workLineId 生产线主键ID
     * @param deleted    逻辑删除
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单分页
     * <AUTHOR>
     * @date 2021-06-18
     **/
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select subWorkSheet from SubWorkSheet subWorkSheet where subWorkSheet.workLine.id = ?1 and subWorkSheet.status in (0,1,2) and subWorkSheet.workSheet.category = 1 and subWorkSheet.deleted = ?2 order by subWorkSheet.planEndDate asc ")
    Page<SubWorkSheet> findByWorkLineIdAndStatusLessThanOrderByPlanEndDateAsc(Long workLineId, Long deleted, Pageable pageable);

    /**
     * 通过工单主键id列表获取子工单列表
     *
     * @param workSheetIdList 工单主键id列表
     * @param deleted         逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021/9/1
     */
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkSheetIdInAndDeleted(List<Long> workSheetIdList, Long deleted);

    /**
     * 获取所有子工单
     *
     * @param status  完成状态(0:未完成;1:完成)
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021/10/19
     */
    @DataFilter(isSkip = true)
    @Query("select subWorkSheet from SubWorkSheet subWorkSheet where subWorkSheet.status < ?1 and subWorkSheet.workLine.id = ?2 and subWorkSheet.deleted = ?3")
    List<SubWorkSheet> findByStatusAndWorkLineIdAndDeleted(Integer status, Long workLineId, Long deleted);

    /**
     * 获取所有子工单
     *
     * @param status  完成状态(0:未完成;1:完成)
     * @param organizationId 组织架构主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021/10/19
     */
    @DataFilter(isSkip = true)
    @Query("select subWorkSheet from SubWorkSheet subWorkSheet where subWorkSheet.status < ?1 and subWorkSheet.workSheet.organizationId = ?2 and subWorkSheet.deleted = ?3")
    List<SubWorkSheet> findByStatusAndDeletedAndWorkSheetOrganizationId(Integer status, Long organizationId, Long deleted);

    /**
     * 获取所有子工单
     *
     * @param status  工单状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021/10/19
     */
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByStatusLessThanAndDeleted(Integer status, Long deleted);

    /**
     * 获取当前总工单所有已开工的子工单
     *
     * @param workSheetId 工单主键id
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2021/12/17
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SubWorkSheet> findByWorkSheetIdAndDeletedAndActualStartDateIsNotNull(Long workSheetId, Long deleted);

    /**
     * 通过子工单主键id列表获取子工单列表
     *
     * @param subWorkSheetIdList 子工单列表
     * @param deleted            逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2022/1/10
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SubWorkSheet> findByIdInAndDeleted(List<Long> subWorkSheetIdList, Long deleted);

    /**
     * 通过生产线主键ID、状态列表及逻辑删除获取子工单列表
     *
     * @param workLineId 生产线I主键D
     * @param statuses   状态列表
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     */
    @DataFilter(isSkip = true)
    List<SubWorkSheet> findByWorkLineIdAndStatusInAndDeleted(Long workLineId, List<Integer> statuses, Long deleted);

    /**
     * 根据生产线获取工单进度集合(子工单投产)
     *
     * @param workLineId 产线主键ID
     * @return org.springframework.data.domain.Page<net.airuima.dto.report.WorkSheetProgressReportDTO> 工单进度集合
     * <AUTHOR>
     * @date 2022/12/30
     **/
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.dto.report.WorkSheetProgressReportDTO(ws.id, sws.id, sws.serialNumber, sws.status, p.code, p.name, p.specification, sws.number, sws.unqualifiedNumber + sws.qualifiedNumber, sws.planEndDate) " +
            "from SubWorkSheet sws " +
            "left join WorkSheet ws on sws.workSheet.id = ws.id " +
            "left join Pedigree p on ws.pedigree.id = p.id " +
            "where sws.status in (0, 1) and sws.workLine.id = ?1")
    Page<WorkSheetProgressReportDTO> workSheetProgressBySubWorkSheet(Long workLineId, Pageable pageable);

    /**
     * 根据生产线获取工单进度集合(工单投产)
     *
     * @param workLineId 产线主键ID
     * @return org.springframework.data.domain.Page<net.airuima.dto.report.WorkSheetProgressReportDTO> 工单进度集合
     * <AUTHOR>
     * @date 2022/12/30
     **/
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.dto.report.WorkSheetProgressReportDTO(ws.id, ws.serialNumber, ws.status, p.code, p.name, p.specification, ws.number, ws.unqualifiedNumber + ws.qualifiedNumber, ws.planEndDate) " +
            "from WorkSheet ws " +
            "left join Pedigree p on ws.pedigree.id = p.id " +
            "where ws.status in (0, 1) and ws.workLine.id = ?1")
    Page<WorkSheetProgressReportDTO> workSheetProgressByWorkSheet(Long workLineId, Pageable pageable);

    /**
     * 根据总工单主键ID、状态获取对应的子工单列表
     *
     * @param workSheetId  总工单主键ID
     * @param status 状态
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SubWorkSheet> findByWorkSheetIdAndStatusNotInAndDeleted(Long workSheetId, List<Integer> status, Long deleted);

    /**
     * 根据子工单id修改custom1
     *
     * @param subWorkSheetIdList 子工单主键ID集合
     * <AUTHOR>
     * @date 2022/10/11
     */
    @Modifying
    @Query("update SubWorkSheet set custom1=null where id in (?1) and deleted=0L")
    void updateCustom1ByIdIn(List<Long> subWorkSheetIdList);


    /**
     * 子工单数量查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return java.lang.Integer 子工单数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(s.id) from SubWorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.workSheet.category=1")
    Integer countSubWorkSheetNumber(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 子工单数量查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return java.lang.Integer 子工单数量
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(s.id) from SubWorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.workSheet.category=1 and s.status!=-2")
    Integer countSubWorkSheetNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 按照工单状态分组获取查询条件下的工单状态的统计个数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 工单状态汇总数据
     * <AUTHOR>
     * @date 2023/10/24
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(s.status,count(s.id)) from SubWorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.workSheet.category=1 group by s.status")
    List<WsStatusNumberDTO> countStatusNumberGroupByStatus(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 按照日期分组获取查询条件下的工单状态的统计个数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @param statuses       状态列表
     * @return java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 工单状态汇总数据
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(DATE_FORMAT(s.planEndDate,'%m-%d'),count(s.id)) from  SubWorkSheet  s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.status in(?6) and s.deleted=0L and s.workSheet.category=1 group by DATE_FORMAT(s.planEndDate,'%m-%d')")
    List<WsStatusNumberDTO> countStatusNumberGroupByPlanEndDate(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, List<Integer> statuses);


    /**
     * 按照日期分组获取查询条件下的工单逾期状态的统计个数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @param compareDate    比较逾期日期
     * @param statuses       状态列表
     * @return java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 工单状态汇总数据
     * <AUTHOR>
     * @date 2023/10/24
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(DATE_FORMAT(s.planEndDate,'%m-%d'),count(s.id)) from  SubWorkSheet  s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.planEndDate<?6 and s.status in(?7)  and s.deleted=0L and s.workSheet.category=1 group by DATE_FORMAT(s.planEndDate,'%m-%d')")
    List<WsStatusNumberDTO> countExpiredNumberGroupByPlanEndDate(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, LocalDateTime compareDate, List<Integer> statuses);


    /**
     * 计划完成数量查询(取消除外)
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系id
     * @return java.lang.Integer 计划完成数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select sum(s.number) from SubWorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.workSheet.category=1 and s.deleted = ?6 and s.status!=-2")
    Integer countSubWorkSheetPlanNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 实际完成数量查询(取消除外)
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return java.lang.Integer 实际完成数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select sum(s.qualifiedNumber + s.unqualifiedNumber) from SubWorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.workSheet.category=1 and s.status <> -2")
    Integer countSubWorkSheetActualFinishNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 查询按部门分组完成数据
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键d
     * @param pedigreeId     产品谱系主键id
     * @param deleted        删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.OrganizationChartResultDTO> 部门分组完成数据
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query(value = "select new net.airuima.rbase.web.rest.report.dto.OrganizationChartResultDTO(w.organizationId, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.number))   from SubWorkSheet s left join WorkSheet w ON s.workSheet.id = w.id  where ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  w.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or w.pedigree.id = ?5 ) and s.deleted = ?6 and s.status <> -2 group by w.organizationId")
    @FetchMethod
    List<OrganizationChartResultDTO> findOrganizationChart(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 查询按产线分组实际完成数据
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param pedigreeId     产品谱系id
     * @param deleted        删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.WorkLineFinishInfoChartResultDTO> 产线分组完成数据
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkLineFinishInfoChartResultDTO(wl.name,sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.number))  from SubWorkSheet s inner JOIN WorkLine wl ON wl.id = s.workLine.id where ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.status <> -2 group by wl.name ")
    List<WorkLineFinishInfoChartResultDTO> findWorkLineChart(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 根据生产线 以及 工单状态 获取当天的 子工单信息
     *
     * @param workLineId 产线主键ID
     * @param startDate 开始时间
     * @param endDate 截止时间
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.WorkOrderDTO> 子工单信息
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkOrderDTO(sw.serialNumber,sw.status,sw.workSheet.pedigree.code,sw.workSheet.pedigree.name," +
            "sw.workSheet.pedigree.specification,sw.workSheet.organizationId,sw.createdDate,sw.planStartDate,sw.planEndDate,sw.actualStartDate,sw.actualEndDate,sw.number,sw.qualifiedNumber) " +
            "from SubWorkSheet sw where sw.workLine.id = ?1 and sw.status in (3,5) and sw.deleted = 0 and sw.actualEndDate between ?2 and ?3")
    Page<WorkOrderDTO> findByWorkLineAndStatusAndDateNow(Long workLineId, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);


    /**
     * 根据生产线 以及 工单状态 获取当天的 子工单总数
     *
     * @param workLineId 产线主键ID
     * @param startDate 开始时间
     * @param endDate 截止时间
     * @return  java.lang.Long 子工单总数
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(sw.id) from SubWorkSheet sw where sw.workLine.id = ?1 and sw.status in (3,5) and sw.deleted = 0 and sw.actualEndDate between ?2 and ?3")
    Long countByWorkLineAndStatusAndDateNow(Long workLineId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 产量数量统计查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityQueryDTO 产量数量统计查询结果
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityQueryDTO(count(s.id),sum(s.number),sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.qualifiedNumber)) " +
            "from SubWorkSheet s where s.workSheet.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) " +
            "and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5)")
    ProductionCapacityQueryDTO findProductionCapacityNumberData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按部门统计查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityOrganizationQueryDTO 产量按部门统计查询结果
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityOrganizationQueryDTO(s.workSheet.organizationId,sum(s.qualifiedNumber + s.unqualifiedNumber)) from SubWorkSheet s where s.workSheet.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) group by s.workSheet.organizationId")
    @FetchMethod
    List<ProductionCapacityOrganizationQueryDTO> findProductionCapacityOrganizationData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按产线统计查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityWorkLineDTO 产量按产线统计查询结果
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO(s.workLine.name,sum(s.qualifiedNumber + s.unqualifiedNumber)) from SubWorkSheet s where s.workSheet.category=1and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) group by s.workLine.id")
    List<ProductionCapacityWorkLineDTO> findProductionCapacityWorkLineData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按产品型号统计查询
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityWorkLineDTO 产量按产品型号统计查询结果
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityLineChartDataDTO(s.workSheet.pedigree.name,sum(s.qualifiedNumber + s.unqualifiedNumber),DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')) from SubWorkSheet s where s.workSheet.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.workSheet.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.workSheet.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) and date(s.actualEndDate) is not null group by s.workSheet.pedigree.id, DATE_FORMAT(s.actualEndDate,'%Y-%m-%d') order by DATE_FORMAT(s.actualEndDate,'%Y-%m-%d') asc ")
    List<ProductionCapacityLineChartDataDTO> findProductionCapacityLineChartData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 根据部门主键id 产线主键id 以及当前时间，获取计划时间小于当前时间的 产线统计
     *
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param nowDateTime    当前时间
     * @return List<WorkLineStatisticsDTO>
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO(sub.workSheet.organizationId,sub.workLine,count(sub.id),sum(sub.number)) from SubWorkSheet sub where sub.status in (0,1) and sub.workSheet.category > -1 and (?1 is null or sub.workSheet.organizationId = ?1) and (?2 is null or sub.workLine.id = ?2) and sub.planEndDate < ?3 and sub.deleted = 0 group by sub.workLine.id")
    List<WorkLineStatisticsDTO> countByNowBeforeAndStatus(Long organizationId, Long workLineId, LocalDateTime nowDateTime);

    /**
     * 通过部门主键id 产线主键id 获取以当前开始结束时间 范围时间内 工单今日已完成数
     *
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @return List<WorkLineStatisticsDTO>
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO(sub.workSheet.organizationId,sub.workLine,sum(sub.qualifiedNumber + sub.unqualifiedNumber),sum(sub.qualifiedNumber),sum(sub.unqualifiedNumber),sum(sub.reworkQualifiedNumber)) from SubWorkSheet sub where sub.deleted = 0 and sub.workSheet.category > -1 and (?1 is null or sub.workSheet.organizationId = ?1) and (?2 is null or sub.workLine.id = ?2) and sub.status = 3 and sub.actualEndDate between ?3 and ?4 group by sub.workLine.id")
    List<WorkLineStatisticsDTO> countByNowAndFinishNumber(Long organizationId, Long workLineId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取当前车间下所有投产中、已暂停、已下单的子工单总数
     *
     * @param status         工单状态列表
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @return java.lang.Long 逾期数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByStatusInAndWorkSheetOrganizationIdAndWorkSheetCategoryGreaterThanAndDeleted(List<Integer> status, Long organizationId, Integer category, Long deleted);

    /**
     * 子工单中，计划完工日期<当前日期的 逾期数量
     *
     * @param nowDateTime    当前时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态列表
     * @return java.lang.Long 逾期数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateBeforeAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime nowDateTime, Long organizationId, Long deleted, List<Integer> status, Integer category);

    /**
     * 通过 部门id 获取指定开始结束时间内 工单状态为投产中、已暂停、已下单 今日计划完成
     *
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态列表
     * @return java.lang.Long 数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateBetweenAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime startDateTime, LocalDateTime endDateTime, Long organizationId, Long deleted, List<Integer> status, Integer category);

    /**
     * 获取 未来计划完成总数：计划完工日期>当前日期的
     *
     * @param nowDateTime    当前时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态
     * @return java.lang.Long 数量
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateAfterAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime nowDateTime, Long organizationId, Long deleted, List<Integer> status, Integer category);

    /**
     * 产品谱系生产直通率产品趋势
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.web.rest.report.dto.digitalworkshop.PedigreeStatisticsDTO> 品谱系生产直通率产品趋势
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.PedigreeStatisticsDTO(s.workSheet.pedigree.name,DATE_FORMAT(s.actualEndDate,'%m-%d'),sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.qualifiedNumber),sum(s.reworkQualifiedNumber)) from SubWorkSheet s  where 1=1 and s.workSheet.category > -1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 )  and  ( ?3 is null or s.workSheet.organizationId = ?3 )  and ( ?4 is null or s.workSheet.workLine.id = ?4 ) and s.deleted = ?5 and s.status in (3,5)  and s.workSheet.pedigree.name is not null and date(s.actualEndDate) is not null group by s.workSheet.pedigree.id, DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')")
    List<PedigreeStatisticsDTO> getPedigreeStatisticsChartData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long deleted);


    /**
     * 产品谱系平均使用时长
     *
     * @param organizationId 部门主键id
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @return java.util.List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> 产品谱系平均使用时长
     */
    @Query(value = "SELECT pe.id as pedigreeId,pe.`code` as pedigreeCode,pe.`name` as pedigreeName, " +
            " CASE" +
            "        WHEN COUNT(sw.id) > 0 THEN ROUND(ROUND(SUM(TIMESTAMPDIFF(SECOND, sw.actual_start_date, sw.actual_end_date)) / 60.0, 4) / sum(sw.number), 1)" +
            "        ELSE 0 " +
            "    END AS averageUsageDuration FROM " +
            " procedure_sub_work_sheet sw " +
            " INNER JOIN procedure_work_sheet ws on ws.id = sw.work_sheet_id" +
            " INNER JOIN base_pedigree pe on pe.id = ws.pedigree_id" +
            " WHERE " +
            " sw.deleted = 0 " +
            " AND ws.organization_id = ?1 " +
            " AND sw.`status` = 3 " +
            " AND ws.category > -1 " +
            " AND sw.actual_end_date IS NOT NULL" +
            " AND sw.actual_end_date >= ?2 " +
            " and sw.actual_end_date < ?3 " +
            " GROUP BY " +
            " ws.pedigree_id ", nativeQuery = true)
    List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> findByPedigreeAverageTimeConsumptionInfo(Long organizationId, LocalDateTime startDateTime, LocalDateTime endDateTime);

    /**
     * 通过车间（产线） 获取 今日工单排产 及之前未完成的
     *
     * @param workLineId  产线
     * @param endDateTime 结束时间
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     */
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<SubWorkSheet> findByStatusInAndWorkLineIdAndPlanEndDateBeforeAndPlanEndDateIsNotNullAndDeletedAndWorkSheetCategoryGreaterThanOrderByPlanEndDateDesc(List<Integer> status, Long workLineId, LocalDateTime endDateTime, Long deleted, Integer category);


    /**
     * 通过工单主键ID及逻辑删除获取不等于特定状态的子工单个数
     *
     * @param workSheetId 工单主键ID
     * @param status      状态
     * @param deleted     逻辑删除
     * @return java.util.Optional<java.lang.Integer> 个数
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndStatusNotAndDeleted(Long workSheetId, Integer status, Long deleted);

    /**
     * 通过工单主键ID列表及逻辑删除获取不等于特定状态的子工单个数
     *
     * @param workSheetId 工单主键ID列表
     * @param status      状态
     * @param deleted     逻辑删除
     * @return java.util.Optional<java.lang.Integer> 个数
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdInAndStatusNotAndDeleted(List<Long> workSheetId, Integer status, Long deleted);

    /**
     * 通过工单主键ID获取异常结单及正常结单的子工单个数
     *
     * @param workSheetId 工单主键ID
     * @param deleted     逻辑删除
     * @return java.util.Optional<java.lang.Integer> 个数
     */
    @DataFilter(isSkip = true)
    @Query("select count(sw.id) from SubWorkSheet sw where sw.workSheet.id=?1 and sw.status>=3 and sw.deleted=?2")
    Long countFinishNumberByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 通过工单主键ID列表获取异常结单及正常结单的子工单个数
     *
     * @param workSheetIdList 工单主键ID列表
     * @param deleted         逻辑删除
     * @return java.util.Optional<java.lang.Integer> 个数
     */
    @DataFilter(isSkip = true)
    @Query("select count(sw.id) from SubWorkSheet sw where sw.workSheet.id in ?1 and sw.status>=3 and sw.deleted=?2")
    Long countFinishNumberByWorkSheetIdInAndDeleted(List<Long> workSheetIdList, Long deleted);

    @DataFilter(isSkip = true)
    @EntityGraph(value = "subWorkSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Override
    SubWorkSheet getReferenceById(Long aLong);
}
