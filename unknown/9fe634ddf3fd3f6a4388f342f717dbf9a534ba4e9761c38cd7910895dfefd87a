package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsRequestDTO;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO;
import net.airuima.util.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Tag(name = "产品生产在制看板Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/report/online-product-statistics-report")
@AuthorityRegion("报表看板")
public class OnlineProductStatisticsReportResource {

    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;

    /**
     * 获取生产在制看板或生产在制质量看板数据
     *
     * @param onlineProductStatisticsRequestDTO 请求参数DTO
     * @return OnlineProductStatisticsResultDTO 结果数据
     */
    @Operation(summary = "获取生产在制看板或生产在制质量看板数据")
    @PreAuthorize("hasAnyAuthority('ONLINE_PRODUCT_STATISTICS_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    @PostMapping("/detail")
    public ResponseEntity<ResponseData<OnlineProductStatisticsResultDTO>> findDetail(@RequestBody OnlineProductStatisticsRequestDTO onlineProductStatisticsRequestDTO) {
        try {
            return ResponseData.ok(workSheetStepStatisticsServices[0].findOnlineProductStatisticsInfo(onlineProductStatisticsRequestDTO));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    public String getAuthorityDescription(String authority) {
        if (!StringUtils.hasLength(authority)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        } else if ("ONLINE_PRODUCT_STATISTICS_REPORT_READ".equals(authority)) {
            return "浏览在制产品状态看板";
        }
        return "";
    }
}
