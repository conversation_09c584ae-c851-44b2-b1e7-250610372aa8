package net.airuima.rbase.web.rest.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.service.report.ProductionCapacityReportService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.web.rest.report.dto.ExportGraphDTO;
import net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartResultDTO;
import net.airuima.rbase.web.rest.report.dto.production.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计报表Resource
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Tag(name = "产量统计报表Resource")
@RestController
@RequestMapping("/api/report/production-capacity-report")
@AuthorityRegion("报表看板")
public class ProductionCapacityReportResource {

    private final ProductionCapacityReportService productionCapacityReportService;

    public ProductionCapacityReportResource(ProductionCapacityReportService productionCapacityReportService) {
        this.productionCapacityReportService = productionCapacityReportService;
    }


    /**
     * 产量统计图形数据(产量趋势和报表统计数字)
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO> 产量统计报表统计图表返回结果
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @PostMapping("/chart")
    public ResponseEntity<ResponseData<ProductionCapacityReportChartResultDTO>> productionCapacityReportChart(
            @RequestBody ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        try {
            ProductionCapacityReportChartResultDTO productionCapacityReportChartResultDto = productionCapacityReportService.getProductionCapacityReportChart(productionCapacityReportRequestDto);
            return ResponseData.ok(productionCapacityReportChartResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工序和工序组产出推移图数据
     *
     * @param productionCapacityReportRequestDto 工序和工序组产出推移图请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.StepProductCapacityReportChartResultDTO> 工序和工序组产出推移图数据
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @PostMapping("/step/chart")
    public ResponseEntity<ResponseData<StepProductCapacityReportChartResultDTO>> stepProductionCapacityReportChart(
            @RequestBody ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        try {
            StepProductCapacityReportChartResultDTO resultDto = productionCapacityReportService.getStepProductionCapacityReportChart(productionCapacityReportRequestDto);
            return ResponseData.ok(resultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据产品谱系获取工序产出推移图数据的工艺路线
     * @param pedigreeId 产品谱系id
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData< java.util.List< net.airuima.domain.base.process.WorkFlow>>> 工艺路线列表
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @GetMapping("/step/chart/work-flow/{pedigreeId}")
    public ResponseEntity<ResponseData<List<WorkFlow>>> stepProductionCapacityReportChartWorkFlow(@PathVariable("pedigreeId") Long pedigreeId) {
        try {
            List<WorkFlow> workFlowList = productionCapacityReportService.getStepProductionCapacityReportChartWorkFlow(pedigreeId);
            return ResponseData.ok(workFlowList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 产量统计分布图形数据(产量分布 部门产量和产线产量tab切换条形图数据)
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO> 产量统计分布图表返回结果
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @PostMapping("/distribution")
    public ResponseEntity<ResponseData<ProductionCapacityReportDistributionResultDTO>> productionCapacityReportDistribution(
            @RequestBody ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        try {
            ProductionCapacityReportDistributionResultDTO productionCapacityReportDistributionResultDto = productionCapacityReportService.getProductionCapacityReportDistribution(productionCapacityReportRequestDto);
            return ResponseData.ok(productionCapacityReportDistributionResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 产量统计表格数据
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO> 产量统计报表表格数据返回结果
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @PostMapping("/table")
    public ResponseEntity<ResponseData<ProductionCapacityReportTableResultDTO>> productionCapacityReportTable(
            @RequestBody ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        try {
            ProductionCapacityReportTableResultDTO productionCapacityReportTableResultDto = productionCapacityReportService.getProductionCapacityReportTable(productionCapacityReportRequestDto);
            return ResponseData.ok(productionCapacityReportTableResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 产量统计表格数据导出
     *
     * @param file 图形区域文件
     * @param requestParam 请求参数
     */
    @PreAuthorize("hasAnyAuthority('PRODUCTION_CAPACITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ProductionStatistics")
    @PostMapping("/export")
    public void productionCapacityReportTableExport(@RequestParam("file") MultipartFile file,
                                                    @RequestParam("requestParam") String requestParam,@RequestParam(value = "excelType",required = false) String excelType, HttpServletResponse response) throws IOException {
        ProductionCapacityReportRequestDTO productionCapacityReportRequestDto = JSON.parseObject(requestParam, new TypeReference<ProductionCapacityReportRequestDTO>() {
        });
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        ProductionCapacityReportTableResultDTO productionCapacityReportTableResultDto = productionCapacityReportService.getProductionCapacityReportTable(productionCapacityReportRequestDto);
        sheetsList.add(ExcelUtils.createSheetParams("产量明细" , ProductionCapacityReportTableItemDTO.class, productionCapacityReportTableResultDto.getProductionCapacityReportTableItemList()));
        sheetsList.add(ExcelUtils.createSheetParams("统计图表", ExportGraphDTO.class, new ArrayList<>(Arrays.asList(new ExportGraphDTO(file.getBytes())))));
        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
        String fileName = "产量统计报表" + System.currentTimeMillis() + prefix;
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?ExcelType.HSSF:ExcelType.XSSF);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));
        workbook.write(response.getOutputStream());
    }


    public String getAuthorityDescription(String authority) {
        if (StringUtils.isEmpty(authority)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        } else if ("PRODUCTION_CAPACITY_REPORT_READ".equals(authority)) {
            return "浏览产量统计报表";
        }
        return "";
    }

}
