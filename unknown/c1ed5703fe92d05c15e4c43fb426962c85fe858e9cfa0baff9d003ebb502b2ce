package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.domain.procedure.report.WorkSheetStatistics;
import net.airuima.rbase.service.report.WorkSheetStatisticsService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单产量统计Resource
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Tag(name = "工单产量统计Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-sheet-statisticss")
public class WorkSheetStatisticsResource extends BaseResource<WorkSheetStatistics> {

    private final WorkSheetStatisticsService workSheetStatisticsService;

    public WorkSheetStatisticsResource(WorkSheetStatisticsService workSheetStatisticsService) {
        this.workSheetStatisticsService = workSheetStatisticsService;
        this.mapUri = "/api/work-sheet-statisticss";
    }

}
