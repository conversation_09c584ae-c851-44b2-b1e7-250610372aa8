package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工单定制工序Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WsStepRepository extends LogicDeleteableRepository<WsStep>,
        EntityGraphJpaSpecificationExecutor<WsStep>, EntityGraphJpaRepository<WsStep, Long> {

    /**
     * 根据总工单主键ID获取定制工序信息
     *
     * @param workSheetId 总工单主键ID
     * @param deleted
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     */
    @Query("SELECT ws FROM WsStep ws LEFT JOIN FETCH ws.step step LEFT JOIN FETCH step.stepGroup WHERE ws.workSheet.id = :workSheetId AND ws.deleted = :deleted")
    @DataFilter(isSkip = true)
    @Cacheable(cacheNames = {"wsStepQueryCache"},key = "#workSheetId")
    List<WsStep> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 根据子工单主键ID获取定制工序信息
     *
     * @param subWorkSheetId 子工单主键id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     */
    @DataFilter(isSkip = true)
    @Query("SELECT ws FROM WsStep ws LEFT JOIN FETCH ws.step step LEFT JOIN FETCH step.stepGroup WHERE ws.subWorkSheet.id = :subWorkSheetId AND ws.deleted = :deleted")
    @Cacheable(cacheNames = {"wsStepQueryCache"},key = "#subWorkSheetId")
    List<WsStep> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 通过子工单主键id和工序查找工序对应的流程信息
     *
     * @param subWsId 子工单主键id
     * @param stepId  工序主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序
     */
    @DataFilter(isSkip = true)
    Optional<WsStep> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWsId, Long stepId, Long deleted);

    /**
     * 通过总工单主键ID和工序获取对应的总工单定制流程
     *
     * @param wsId    总工单主键ID
     * @param stepId  工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序
     * <AUTHOR>
     * @date 2021-01-11
     **/
    @DataFilter(isSkip = true)
    Optional<WsStep> findByWorkSheetIdAndStepIdAndDeleted(Long wsId, Long stepId, Long deleted);

    /**
     * 根据总工单主键ID删除定制工序
     *
     * @param workSheetId 总工单主键ID
     */
    @Modifying
    @Query("delete from WsStep where workSheet.id=?1")
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#workSheetId")
    void deleteByWorkSheetId(Long workSheetId);

    /**
     * 根据子工单主键ID删除定制工序
     *
     * @param subWorkSheetId 子工单主键ID
     */
    @Modifying
    @Query("delete from WsStep where subWorkSheet.id=?1")
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#subWorkSheetId")
    void deleteBySubWorkSheetId(Long subWorkSheetId);

    /**
     * 通过工单主键ID+工序类型+删除标识查询定制工序
     *
     * @param workSheetId 工单主键ID
     * @param category    工序类型
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     * <AUTHOR>
     * @date 2023/3/31
     **/
    @DataFilter(isSkip = true)
    List<WsStep> findByWorkSheetIdAndCategoryAndDeleted(Long workSheetId, Integer category, Long deleted);


    /**
     * 通过子工单主键ID+工序类型+删除标识查询定制工序
     *
     * @param subWorkSheetId 子工单主键ID
     * @param category    工序类型
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     * <AUTHOR>
     * @date 2023/3/31
     **/
    @DataFilter(isSkip = true)
    List<WsStep> findBySubWorkSheetIdAndCategoryAndDeleted(Long subWorkSheetId, Integer category, Long deleted);

    /**
     * 通过工单主键ID+工序主键ID集合+删除标识查询定制工序集合
     *
     * @param workSheetId 工单主键ID
     * @param stepIdList  工序主键ID集合
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     * <AUTHOR>
     * @date 2023/4/21
     **/
    @DataFilter(isSkip = true)
    List<WsStep> findByWorkSheetIdAndStepIdInAndDeleted(Long workSheetId, List<Long> stepIdList, Long deleted);


    /**
     * 子工单主键IO+工序主键ID集合+删除标识查询定制工序集合
     *
     * @param subWorkSheetId 子工单主键IO
     * @param stepIdList  工序ID集合
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStep> 生产工单定制工序列表
     * <AUTHOR>
     * @date 2023/4/21
     **/
    @DataFilter(isSkip = true)
    List<WsStep> findBySubWorkSheetIdAndStepIdInAndDeleted(Long subWorkSheetId, List<Long> stepIdList, Long deleted);


    /**
     * 通过工单主键ID及逻辑删除获取工单工序快照个数
     * @param workSheetId 工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<java.lang.Integer 工序快照个数
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);
}
