package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class WorkSheetHistoryDTO {

    /**
     * 工单
     */
    private WorkSheet workSheet;

    /**
     * 工单基础履历列表
     */
    private List<HistoryDTO> historyDTOList;

    /**
     * 工单投产履历列表
     */
    private List<StepHistoryDTO> stepHistoryDTOList;

    /**
     * 工单基础履历列表
     */
    public static class HistoryDTO {

        /**
         * 0,创建
         * 1,分单
         * 2,暂停
         * 3,恢复
         * 4,完成
         */
        private Integer type;

        /**
         * 备注
         */
        private String note;

        /**
         * 子工单
         */
        private SubWorkSheet subWorkSheet;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 操作人
         */
        private StaffDTO staffDTO;

        public HistoryDTO() {
        }

        public HistoryDTO(Integer type, String note, SubWorkSheet subWorkSheet, LocalDateTime localDateTime, StaffDTO staffDTO) {
            this.type = type;
            this.note = note;
            this.subWorkSheet = subWorkSheet;
            this.localDateTime = localDateTime;
            this.staffDTO = staffDTO;
        }

        public StaffDTO getStaffDTO() {
            return staffDTO;
        }

        public HistoryDTO setStaffDTO(StaffDTO staffDTO) {
            this.staffDTO = staffDTO;
            return this;
        }

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public String getNote() {
            return note;
        }

        public HistoryDTO setNote(String note) {
            this.note = note;
            return this;
        }

        public SubWorkSheet getSubWorkSheet() {
            return subWorkSheet;
        }

        public HistoryDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
            this.subWorkSheet = subWorkSheet;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }
    }

    /**
     * 工单投产履历列表
     */
    public static class StepHistoryDTO {
        /**
         * 0,投产
         * 1,回退
         */
        private Integer type;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 生产详情
         */
        private BatchWorkDetail batchWorkDetail;

        /**
         * 员工信息
         */
        private List<StaffDTO> staffDTOList;

        /**
         * 设备信息
         */
        private List<FacilityDTO> facilityDTOList;

        /**
         * 物料信息
         */
        private List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList;

        /**
         * 动态数据信息
         */
        private List<StepDynamicDataColumnGetDTO> stepDynamicDataColumnGetDTOList;

        /**
         * 不良项目信息
         */
        private List<UnqualifiedItem> unqualifiedItemList;

        /**
         * 易损件信息
         */
        private List<WearingPart> wearingPartList;

        public Integer getType() {
            return type;
        }

        public StepHistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public StepHistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public BatchWorkDetail getBatchWorkDetail() {
            return batchWorkDetail;
        }

        public StepHistoryDTO setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
            this.batchWorkDetail = batchWorkDetail;
            return this;
        }

        public List<StaffDTO> getStaffDTOList() {
            return staffDTOList;
        }

        public StepHistoryDTO setStaffDTOList(List<StaffDTO> staffDTOList) {
            this.staffDTOList = staffDTOList;
            return this;
        }

        public List<FacilityDTO> getFacilityDTOList() {
            return facilityDTOList;
        }

        public StepHistoryDTO setFacilityDTOList(List<FacilityDTO> facilityDTOList) {
            this.facilityDTOList = facilityDTOList;
            return this;
        }

        public List<BatchWorkDetailMaterialBatch> getBatchWorkDetailMaterialBatchList() {
            return batchWorkDetailMaterialBatchList;
        }

        public StepHistoryDTO setBatchWorkDetailMaterialBatchList(List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList) {
            this.batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchList;
            return this;
        }

        public List<StepDynamicDataColumnGetDTO> getStepDynamicDataColumnGetDTOList() {
            return stepDynamicDataColumnGetDTOList;
        }

        public StepHistoryDTO setStepDynamicDataColumnGetDTOList(List<StepDynamicDataColumnGetDTO> stepDynamicDataColumnGetDTOList) {
            this.stepDynamicDataColumnGetDTOList = stepDynamicDataColumnGetDTOList;
            return this;
        }

        public List<UnqualifiedItem> getUnqualifiedItemList() {
            return unqualifiedItemList;
        }

        public StepHistoryDTO setUnqualifiedItemList(List<UnqualifiedItem> unqualifiedItemList) {
            this.unqualifiedItemList = unqualifiedItemList;
            return this;
        }

        public List<WearingPart> getWearingPartList() {
            return wearingPartList;
        }

        public StepHistoryDTO setWearingPartList(List<WearingPart> wearingPartList) {
            this.wearingPartList = wearingPartList;
            return this;
        }
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetHistoryDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public WorkSheetHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }

    public List<StepHistoryDTO> getStepHistoryDTOList() {
        return stepHistoryDTOList;
    }

    public WorkSheetHistoryDTO setStepHistoryDTOList(List<StepHistoryDTO> stepHistoryDTOList) {
        this.stepHistoryDTOList = stepHistoryDTOList;
        return this;
    }
}
