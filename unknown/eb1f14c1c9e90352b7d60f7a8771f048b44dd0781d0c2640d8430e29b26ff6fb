package net.airuima.rbase.repository.procedure.report;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.report.WorkSheetStepStatistics;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsRequestDTO;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单工序生产在制统计Repository
 * <AUTHOR>
 * @date 2023/12/19
 */
@Repository
public interface WorkSheetStepStatisticsRepository extends LogicDeleteableRepository<WorkSheetStepStatistics>,
        EntityGraphJpaSpecificationExecutor<WorkSheetStepStatistics>, EntityGraphJpaRepository<WorkSheetStepStatistics, Long> {

    /**
     * 通过子工单主键id、工序信息、及逻辑删除获取工单工序在制信息
     * @param subWorkSheetId 子工单主键ID
     * @param stepId 工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.report.WorkSheetStepStatistics> 子工单工序在制信息
     * <AUTHOR>
     * @date 2023/12/19
     */
    @DataFilter(isSkip = true)
    Optional<WorkSheetStepStatistics> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId,Long stepId,Long deleted);


    /**
     * 通过工单主键id、工序信息、及逻辑删除获取工单工序在制信息
     * @param workSheetId  工单主键ID
     * @param stepId 工序主键ID
     * @param deleted   逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.report.WorkSheetStepStatistics> 工单工序在制信息
     * <AUTHOR>
     * @date 2023/12/19
     */
    @DataFilter(isSkip = true)
    Optional<WorkSheetStepStatistics> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId,Long stepId,Long deleted);

    /**
     * 通过子工单主键ID、逻辑删除获取工序在制信息列表
     * @param subWorkSheetId 子工单主键ID
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.WorkSheetStepStatistics>工单工序在制信息
     * <AUTHOR>
     * @date 2023/12/19
     */
    @DataFilter(isSkip = true)
    List<WorkSheetStepStatistics> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId,Long deleted);


    /**
     * 通过工单主键ID、逻辑删除获取工序在制信息列表
     * @param workSheetId 工单主键ID
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.WorkSheetStepStatistics>工单工序在制信息
     * <AUTHOR>
     * @date 2023/12/19
     */
    @DataFilter(isSkip = true)
    List<WorkSheetStepStatistics> findByWorkSheetIdAndDeleted(Long workSheetId,Long deleted);


    /**
     * 工单投产模式下获取报表结果
     * @param dto   请求报表参数DTO
     * @return java.util.List<net.airuima.domain.procedure.report.WorkSheetStepStatistics> 工单在制工序列表
     * <AUTHOR>
     * @date 2023/12/19
     */
    @EntityGraph(value = "workSheetStepStatisticsEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select wss from WorkSheetStepStatistics wss where " +
            "(:#{#dto.workLineId} is null or wss.workSheet.workLine.id =:#{#dto.workLineId}) " +
            "and (:#{#dto.pedigreeId} is null or wss.workSheet.pedigree.id =:#{#dto.pedigreeId}) " +
            "and (:#{#dto.organizationId} is null or wss.workSheet.organizationId =:#{#dto.organizationId}) " +
            "and (:#{#dto.workSheetId} is null or wss.workSheet.id =:#{#dto.workSheetId}) and wss.deleted=0L order by wss.createdDate asc")
    List<WorkSheetStepStatistics> findReportInfoByConditionWhenWorkSheetMode(@Param("dto") OnlineProductStatisticsRequestDTO dto);

    /**
     * 子工单投产模式下获取报表结果
     * @param dto   请求报表参数DTO
     * @return java.util.List<net.airuima.domain.procedure.report.WorkSheetStepStatistics> 子工单在制工序列表
     * <AUTHOR>
     * @date 2023/12/19
     */
    @EntityGraph(value = "workSheetStepStatisticsEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select wss from WorkSheetStepStatistics wss where " +
            "(:#{#dto.workLineId} is null or wss.subWorkSheet.workLine.id =:#{#dto.workLineId}) " +
            "and (:#{#dto.pedigreeId} is null or wss.subWorkSheet.workSheet.pedigree.id =:#{#dto.pedigreeId}) " +
            "and (:#{#dto.organizationId} is null or wss.subWorkSheet.workSheet.organizationId =:#{#dto.organizationId}) " +
            "and (:#{#dto.workSheetId} is null or wss.subWorkSheet.workSheet.id =:#{#dto.workSheetId}) and wss.deleted=0L order by wss.createdDate asc")
    List<WorkSheetStepStatistics> findReportInfoByConditionWhenSubWorkSheetMode(@Param("dto") OnlineProductStatisticsRequestDTO dto);

    /**
     * 初始化除在制数据外的其他在线看板数据
     */
    @Modifying
    @Query("update WorkSheetStepStatistics wss set wss.inputNumber=0,wss.unqualifiedNumber=0,wss.qualifiedNumber=0,wss.transferNumber=0 where  wss.deleted=0")
    void initWorkSheetStepStatisticsNumber();
}
