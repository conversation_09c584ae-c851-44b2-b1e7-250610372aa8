package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表看板表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良报表看板表格数据DTO")
public class UnqualifiedReportTableResultDTO extends PageDTO {

    /**
     * 不良报表看板表格记录
     */
    @Schema(description = "不良报表看板表格记录")
    List<UnqualifiedReportTableItemDTO> unqualifiedReportTableItemDtoList;


    public List<UnqualifiedReportTableItemDTO> getUnqualifiedReportTableItemDtoList() {
        return unqualifiedReportTableItemDtoList;
    }

    public UnqualifiedReportTableResultDTO setUnqualifiedReportTableItemDtoList(List<UnqualifiedReportTableItemDTO> unqualifiedReportTableItemDtoList) {
        this.unqualifiedReportTableItemDtoList = unqualifiedReportTableItemDtoList;
        return this;
    }

}
