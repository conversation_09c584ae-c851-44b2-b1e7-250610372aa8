package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsRequestDTO;
import net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionChartQueryDTO;
import net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionNumberQueryDTO;
import net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionChartQueryDTO;
import net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionNumberQueryDTO;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划Repository
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Repository
public interface ProductionPlanRepository extends LogicDeleteableRepository<ProductionPlan>,
        EntityGraphJpaSpecificationExecutor<ProductionPlan>, EntityGraphJpaRepository<ProductionPlan, Long> {


    /**
     * 根据主键id和删除标记查询生产计划
     *
     * @param id      生产计划主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.id = ?1 and p.deleted = ?2")
    Optional<ProductionPlan> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 根据计划日期 产品谱系主键id 产线主键id 和删除标记查询生产计划
     *
     * @param planDate   计划日期
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param deleted    删除标记
     * @return java.util.Optional<net.airuima.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.planDate = ?1 and p.pedigree.id = ?2 and p.workLine.id = ?3 and p.stepGroup is null and p.deleted = ?4")
    Optional<ProductionPlan> findByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(LocalDate planDate, Long pedigreeId, Long workLineId, Long deleted);


    /**
     * 根据计划日期 产品谱系主键id 工序组id 和删除标记查询生产计划
     *
     * @param planDate   计划日期
     * @param pedigreeId 产品谱系主键id
     * @param stepGroupId 工序组id
     * @param deleted    删除标记
     * @return java.util.Optional<net.airuima.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.planDate = ?1 and ((?2 is null and p.pedigree is null) or (p.pedigree.id = ?2)) and p.stepGroup.id = ?3 and p.workLine is null and p.deleted = ?4")
    Optional<ProductionPlan> findByPlanDateAndPedigreeIdAndStepGroupIdAndDeleted(LocalDate planDate, Long pedigreeId, Long stepGroupId, Long deleted);



    /**
     * 根据计划日期 产品谱系主键id 产线主键id 和删除标记查询已确认的产线生产计划
     *
     * @param planDate   计划日期
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param deleted    删除标记
     * @return java.util.Optional<net.airuima.rmes.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.planDate = ?1 and p.pedigree.id = ?2 and p.workLine.id = ?3 and p.stepGroup is null and p.status=true and p.deleted = ?4")
    Optional<ProductionPlan> findConfirmedWorkLinePlanByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(LocalDate planDate, Long pedigreeId, Long workLineId, Long deleted);


    /**
     * 根据计划日期 产品谱系主键id 工序组主键id 和删除标记查询已确认的工序组生产计划
     *
     * @param planDate    计划日期
     * @param pedigreeId  产品谱系主键id
     * @param workLineId 生产线主键ID
     * @param stepGroupId 工序组主键id
     * @param deleted     删除标记
     * @return java.util.Optional<net.airuima.rmes.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.planDate = ?1 and p.pedigree.id = ?2 and p.workLine.id = ?3 and p.stepGroup.id=4 and p.status=true and p.deleted = ?4")
    Optional<ProductionPlan> findConfirmedStepGroupPlanByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(LocalDate planDate, Long pedigreeId,Long workLineId, Long stepGroupId, Long deleted);

    /**
     * 查询产线交付报表统计数字
     * @param workLineId 产线主键id
     * @param pedigreeId 产品谱系id
     * @param startDate 开始查询时间
     * @param endDate 结束查询时间
     * @param deleted 删除标记
     * @return net.airuima.web.rest.report.dto.worklinecompletion.WorkLineCompletionNumberQueryDTO 查询产线交付报表统计数字数据
     */
    @EntityGraph(value = "productionPlanEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionNumberQueryDTO(sum(p.planNumber),sum (p.actualNumber)) from ProductionPlan p where 1=1 and (?1 is null or p.workLine.id = ?1) and (?2 is null or p.pedigree.id = ?2) and (?3 is null or p.planDate >= ?3) and (?4 is null or p.planDate <= ?4) and p.deleted = ?5 and p.workLine.id is not null")
    WorkLineCompletionNumberQueryDTO findWorkLineCompletionChartNumberData(Long workLineId, Long pedigreeId, LocalDate startDate, LocalDate endDate, Long deleted);


    /**
     * 查询产线达成报表图形数据
     * @param workLineId 产线主键id
     * @param pedigreeId 产品谱系主键id
     * @param startDate 开始查询时间
     * @param endDate 结束查询时间
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.worklinecompletion.WorkLineCompletionChartQueryDTO> 产线达成报表图形数据
     */
    @EntityGraph(value = "productionPlanEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionChartQueryDTO(p.workLine.name,sum(p.planNumber),sum (p.actualNumber)) from ProductionPlan p where 1=1 and (?1 is null or p.workLine.id = ?1) and (?2 is null or p.pedigree.id = ?2) and (?3 is null or p.planDate >= ?3) and (?4 is null or p.planDate <= ?4)and p.deleted = ?5 and p.workLine.id is not null group by p.workLine.id")
    List<WorkLineCompletionChartQueryDTO> findWorkLineCompletionChartData(Long workLineId, Long pedigreeId, LocalDate startDate, LocalDate endDate, Long deleted);

    /**
     * 根据计划日期 产品谱系主键id 工序组主键id 和删除标记查询生产计划
     *
     * @param planDate    计划日期
     * @param pedigreeId  产品谱系主键id
     * @param workLineId 生产线主键ID
     * @param stepGroupId 工序组主键id
     * @param deleted     删除标记
     * @return java.util.Optional<net.airuima.domain.procedure.aps.ProductionPlan> 生产计划
     */
    @DataFilter(isSkip = true)
    @Query("select p from ProductionPlan p where p.planDate = ?1 and p.pedigree.id = ?2 and p.workLine.id=?3 and p.stepGroup.id = ?4 and p.deleted = ?5")
    Optional<ProductionPlan> findByPlanDateAndPedigreeIdAndWorkLineIdAndStepGroupIdAndDeleted(LocalDate planDate, Long pedigreeId,Long workLineId, Long stepGroupId, Long deleted);


    /**
     * 生产在制看板获取工序生产计划
     * @param dto 动态查询条件DTO
     * @param planDate 计划日期
     * @return 分组汇总的数据列表
     */
    @EntityGraph(value = "productionPlanEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.domain.procedure.aps.ProductionPlan(p.pedigree,p.stepGroup,sum(p.planNumber)) from ProductionPlan p where p.category=0 " +
            "and p.deleted=0L and p.status=true and p.planDate=:planDate " +
            "and(:#{#dto.workLineId} is null or p.workLine.id =:#{#dto.workLineId}) " +
            "and (:#{#dto.pedigreeId} is null or p.pedigree.id =:#{#dto.pedigreeId}) " +
            "group by p.pedigree,p.stepGroup")
    List<ProductionPlan> findByCondition(@Param("dto") OnlineProductStatisticsRequestDTO dto,@Param("planDate") LocalDate planDate);

    /**
     * 工序达成统计数字查询
     * @param stepGroupId 工序组主键id
     * @param pedigreeId 产品谱系主键id
     * @param startDate 开始查询时间
     * @param endDate 结束查询时间
     * @param deleted 删除标记
     * @return net.airuima.web.rest.report.dto.stepcompletion.StepCompletionNumberQueryDTO 工序达成统计数字查询
     */
    @EntityGraph(value = "productionPlanEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionNumberQueryDTO(sum(p.planNumber),sum (p.actualNumber)) from ProductionPlan p where 1=1 and (?1 is null or p.stepGroup.id = ?1) and (?2 is null or p.pedigree.id = ?2) and (?3 is null or p.planDate >= ?3) and (?4 is null or p.planDate <= ?4)and p.deleted = ?5 and p.stepGroup.id is not null")
    StepCompletionNumberQueryDTO findStepCompletionChartNumberData(Long stepGroupId, Long pedigreeId, LocalDate startDate, LocalDate endDate, Long deleted);

    /**
     * 工序达成条形图和折线图数据查询
     * @param stepGroupId 工序组主键id
     * @param pedigreeId 产品谱系主键id
     * @param startDate 开始查询时间
     * @param endDate 结束查询时间
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.stepcompletion.StepCompletionChartQueryDTO> 工序达成条形图和折线图数据
     */
    @EntityGraph(value = "productionPlanEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionChartQueryDTO(p.stepGroup.name,sum(p.planNumber),sum (p.actualNumber)) from ProductionPlan p where 1=1 and (?1 is null or p.stepGroup.id = ?1) and (?2 is null or p.pedigree.id = ?2) and (?3 is null or p.planDate >= ?3) and (?4 is null or p.planDate <= ?4)and p.deleted = ?5 and p.stepGroup.id is not null group by p.stepGroup.id")
    List<StepCompletionChartQueryDTO> findStepCompletionChartData(Long stepGroupId, Long pedigreeId, LocalDate startDate, LocalDate endDate, Long deleted);


    /**
     *  通过 产线主键id，开启标识，以及状态，和 计划时间，获取指定计划时间内的 计划产量总数
     * @param workLineId 产线主键id
     * @param category 开启标识
     * @param status 状态
     * @param planDate 计划时间
     * @return java.lang.Long 总数
     */
    @DataFilter(isSkip = true)
    @Query("select sum(pp.planNumber) from ProductionPlan pp where pp.workLine.id = ?1 and pp.category = ?2 and pp.planDate = ?4 and pp.status = ?3 and pp.deleted = 0 group by pp.workLine.id")
    Long sumByPlanDateAndCategoryAndWorkLineId(Long workLineId,Integer category,Boolean status,LocalDate planDate);

}
