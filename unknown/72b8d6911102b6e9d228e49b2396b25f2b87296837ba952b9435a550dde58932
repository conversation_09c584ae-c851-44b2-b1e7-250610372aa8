package net.airuima.rbase.repository.procedure.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.scene.WorkCellStaffStatus;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 员工工位状态Repository
 * <AUTHOR>
 * @date 2022/9/15
 */
@Repository
public interface WorkCellStaffStatusRepository extends LogicDeleteableRepository<WorkCellStaffStatus>,
        EntityGraphJpaSpecificationExecutor<WorkCellStaffStatus>, EntityGraphJpaRepository<WorkCellStaffStatus, Long> {

    /**
     * 通过工位主键ID及员工主键ID找到最新状态
     * @param workCellId 工位ID
     * @param staffId 员工ID
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.procedure.scene.WorkCellStaffStatus  员工工位状态
     */
    WorkCellStaffStatus findByWorkCellIdAndStaffIdAndDeleted(Long workCellId,Long staffId,Long deleted);

}
