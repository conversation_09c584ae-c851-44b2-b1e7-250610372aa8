package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单投料单Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WsMaterialRepository extends LogicDeleteableRepository<WsMaterial>,
        EntityGraphJpaSpecificationExecutor<WsMaterial>, EntityGraphJpaRepository<WsMaterial, Long> {

    /**
     * 根据总工单主键ID获取投料单
     *
     * @param workSheetId 总工单主键ID
     * @param deleted     删除标志
     * @return java.util.List<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "wsMaterialEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    List<WsMaterial> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 根据工单主键ID分组获取投料单原始物料信息
     * @param workSheetId 工单主键ID
     * @return java.util.List<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单列表
     * <AUTHOR>
     * @date 2023/3/29
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select wsMaterial from WsMaterial wsMaterial where wsMaterial.workSheet.id=?1 and wsMaterial.deleted=0 group by wsMaterial.originMaterialId")
    List<WsMaterial> findByWorkSheetIdAndDeletedGroupByOriginMaterialId(Long workSheetId);

    /**
     * 通过工单和物料查找唯一投料单
     *
     * @param workSheetId 工单主键id
     * @param materialId  物料主键id
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WsMaterial> findByWorkSheetIdAndMaterialIdAndDeleted(Long workSheetId, Long materialId, Long deleted);


    /**
     * 通过工单、物料、原始物料查找唯一投料单
     *
     * @param workSheetId 工单主键id
     * @param materialId  物料主键id
     * @param originMaterialId 原始物料主键ID
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WsMaterial> findByWorkSheetIdAndMaterialIdAndOriginMaterialIdAndDeleted(Long workSheetId, Long materialId,Long originMaterialId, Long deleted);


    /**
     * 通过工单和原始物料主键ID获取投料单信息
     *
     * @param workSheetId      总工单主键ID
     * @param originMaterialId 原始物料主键ID
     * @param deleted          逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单列表
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterial> findByWorkSheetIdAndOriginMaterialIdAndDeleted(Long workSheetId, Long originMaterialId, Long deleted);

    /**
     * 通过工单主键ID数组获取投料单
     *
     * @param ids     工单主键ID列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsMaterial> 工单投料单列表
     * <AUTHOR>
     * @date 2021-02-08
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterial> findByWorkSheetIdInAndDeleted(List<Long> ids, Long deleted);

    /**
     * 根据工单主键ID删除投料单
     *
     * @param wsId
     * @return void
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @Modifying
    @Query("delete from WsMaterial where workSheet.id = ?1")
    void deleteByWorkSheetId(Long wsId);

}
