package net.airuima.rbase.repository.procedure.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.scene.WorkCellStepFacility;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序设备Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkCellStepFacilityRepository extends LogicDeleteableRepository<WorkCellStepFacility>,
        EntityGraphJpaSpecificationExecutor<WorkCellStepFacility>, EntityGraphJpaRepository<WorkCellStepFacility, Long> {


    /**
     * 根据工位 工序 删除 所有关联信息
     *
     * @param workCellId 工位主键id
     * @param stepId     工序主键id
     */
    @Modifying
    @Query("delete from WorkCellStepFacility where workCell.id = ?1 and step.id = ?2")
    void deleteByWorkCellIdAndStepId(Long workCellId, Long stepId);

    /**
     * 根据工位 工序 删除 所有关联信息
     *
     * @param workCellId 工位主键id
     * @param stepId     工序主键id
     */
    @Modifying
    @Query("delete from WorkCellStepFacility where workCell.id = ?1 and step.id is null")
    void deleteByWorkCellId(Long workCellId);

    /**
     * 根据工位和工序获取绑定设备列表
     *
     * @param workCellId 工位主键id
     * @param stepId     工序主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.base.scene.WorkCellStepFacility> 工位工序设备列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.domain.base.scene.WorkCellStepFacility(wsf.facilityId) from  WorkCellStepFacility wsf where wsf.workCell.id=?1 and wsf.step.id=?2 and wsf.deleted=?3")
    List<WorkCellStepFacility> findByWorkCellIdAndStepIdAndDeleted(Long workCellId, Long stepId, Long deleted);

    /**
     * 据工位获取绑定设备列表
     *
     * @param workCellId 工位主键ID
     * @return java.util.List<net.airuima.domain.base.scene.WorkCellStepFacility> 工位工序设备列表
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.domain.base.scene.WorkCellStepFacility(wsf.facilityId)  from  WorkCellStepFacility wsf where wsf.workCell.id=?1 and wsf.step.id is null and wsf.deleted=?2")
    List<WorkCellStepFacility> findByWorkCellIdAndDeleted(Long workCellId, Long deleted);


    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCellStepFacility w where w.workCell.workLine.id = ?1 and w.deleted = ?2 group by w.workCell, w.facilityId")
    @FetchMethod
    List<WorkCellStepFacility> findByWorkLineIdGroupByWorkCellIdAndFacilityId(Long workLineId, Long deleted);

    /**
     * 通过设备ID获取工位工序设备列表
     * @param facilityId 设备ID
     * @param deleted 逻辑删除
     * @return 工位工序设备关系列表
     */
    @DataFilter(isSkip = true)
    List<WorkCellStepFacility> findByFacilityIdAndDeleted(Long facilityId,Long deleted);



    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCellStepFacility w where w.workCell.workStation.id = ?1 and w.deleted = ?2 group by w.workCell, w.facilityId")
    @FetchMethod
    List<WorkCellStepFacility> findByWorkStationIdGroupByWorkCellIdAndFacilityId(Long workStationId, Long deleted);
}
