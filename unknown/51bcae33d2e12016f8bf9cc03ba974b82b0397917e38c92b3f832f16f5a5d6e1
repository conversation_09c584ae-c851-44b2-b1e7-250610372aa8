package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序产出推移图数据DTO
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
@Schema(description = "工序产出推移图数据DTO")
public class StepProductCapacityReportChartDTO {

    /**
     * 类型 目标产出 plan   实际产出 actual
     */
    @Schema(description = "类型 目标产出 plan  实际产出 actual")
    private String name;

    /**
     * 图表名称
     */
    @Schema(description = "图表")
    private String chartName;

    /**
     * id
     */
    @JsonIgnore
    @Schema(description = "id")
    private Long id;


    /**
     * 产量(合格数)
     */
    @Schema(description = "产量")
    private Long number;

    public Long getId() {
        return id;
    }

    public StepProductCapacityReportChartDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public StepProductCapacityReportChartDTO() {
    }

    public StepProductCapacityReportChartDTO(String name, Long id, String chartName, Long number) {
        this.name = name;
        this.id = id;
        this.chartName = chartName;
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public StepProductCapacityReportChartDTO setName(String name) {
        this.name = name;
        return this;
    }


    public String getChartName() {
        return chartName;
    }

    public StepProductCapacityReportChartDTO setChartName(String chartName) {
        this.chartName = chartName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StepProductCapacityReportChartDTO setNumber(Long number) {
        this.number = number;
        return this;
    }
}
