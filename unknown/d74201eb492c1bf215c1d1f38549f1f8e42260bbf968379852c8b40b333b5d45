package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合格率看板表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
@Schema(description = "合格率看板表格数据DTO")
public class WorkSheetQualifiedRateReportTableResultDTO extends PageDTO {

    /**
     * 合格率看板表格数据明细集合
     */
    @Schema(description = "合格率看板表格数据明细集合")
    private List<WorkSheetQualifiedRateReportTableItemDTO> qualifiedRateReportTableItemList;

    public List<WorkSheetQualifiedRateReportTableItemDTO> getQualifiedRateReportTableItemList() {
        return qualifiedRateReportTableItemList;
    }

    public WorkSheetQualifiedRateReportTableResultDTO setQualifiedRateReportTableItemList(List<WorkSheetQualifiedRateReportTableItemDTO> qualifiedRateReportTableItemList) {
        this.qualifiedRateReportTableItemList = qualifiedRateReportTableItemList;
        return this;
    }
}
