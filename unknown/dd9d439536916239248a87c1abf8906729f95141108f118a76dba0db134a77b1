package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.service.report.CapacityReportService;
import net.airuima.util.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 质量报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Tag(name = "产量查询Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/report/capacity-report")
public class CapacityReportResource {

    @Autowired
    private CapacityReportService capacityReportService;

    /**
     * 产品分类日产量增长趋势
     *
     * @param workLineId 生产线ID
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2023/1/4
     **/
    @PostMapping("/product-category-daily-output-growth-trend")
    public ResponseEntity<ResponseData<List<Map<String, Object>>>> productCategoryDailyOutputGrowthTrend(@RequestParam("workLineId") Long workLineId) {
        return ResponseData.ok(capacityReportService.productCategoryDailyOutputGrowthTrend(workLineId));
    }

}
