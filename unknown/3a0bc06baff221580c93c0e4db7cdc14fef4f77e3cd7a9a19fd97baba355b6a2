package net.airuima.rbase.web.rest.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.service.report.UnqualifiedReportService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.web.rest.report.dto.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-19
 */
@Tag(name = "不良报表Resource")
@RestController
@RequestMapping("/api/report/work-sheet-unqualified-report")
@AuthorityRegion("报表看板")
public class UnqualifiedReportResource {

    private final UnqualifiedReportService unqualifiedReportService;

    public UnqualifiedReportResource(UnqualifiedReportService unqualifiedReportService) {
        this.unqualifiedReportService = unqualifiedReportService;
    }

    /**
     * 不良统计报表统计图表
     *
     * @param unqualifiedRequestDto 不良统计报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.UnqualifiedReportChartResultDTO> 不良统计报表统计图形返回结果
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_UNQUALIFIED_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("BadStatistics")
    @PostMapping("/chart")
    public ResponseEntity<ResponseData<UnqualifiedReportChartResultDTO>> workSheetStatusReportChart(
            @RequestBody UnqualifiedReportRequestDTO unqualifiedRequestDto) {
        try {
            UnqualifiedReportChartResultDTO unqualifiedReportChartResultDto = unqualifiedReportService.getUnqualifiedReportChart(unqualifiedRequestDto);
            return ResponseData.ok(unqualifiedReportChartResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 不良统计报表表格数据
     *
     * @param unqualifiedRequestDto 不良统计报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.UnqualifiedReportTableResultDTO> 不良统计报表表格数据返回结果
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_UNQUALIFIED_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("BadStatistics")
    @PostMapping("/table")
    public ResponseEntity<ResponseData<UnqualifiedReportTableResultDTO>> workSheetStatusReportTable(
            @RequestBody UnqualifiedReportRequestDTO unqualifiedRequestDto) {
        try {
            UnqualifiedReportTableResultDTO unqualifiedReportTableResultDto = unqualifiedReportService.getUnqualifiedReportTable(unqualifiedRequestDto);
            return ResponseData.ok(unqualifiedReportTableResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 不良统计报表表格数据导出
     *
     * @param file 图形区域文件
     * @param requestParam 请求参数
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_UNQUALIFIED_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("BadStatistics")
    @PostMapping("/export")
    public void workSheetStatusReportTableExport(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("requestParam") String requestParam,
                                                 @RequestParam(value = "excelType",required = false) String excelType,
                                                 HttpServletResponse response) throws IOException {
        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
        UnqualifiedReportRequestDTO unqualifiedRequestDto = JSON.parseObject(requestParam, new TypeReference<>() {
        });
        UnqualifiedReportTableResultDTO unqualifiedReportTableResultDto = unqualifiedReportService.getUnqualifiedReportTable(unqualifiedRequestDto);
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(ExcelUtils.createSheetParams("不良明细",  UnqualifiedReportTableItemDTO.class, unqualifiedReportTableResultDto.getUnqualifiedReportTableItemDtoList()));
        sheetsList.add(ExcelUtils.createSheetParams("统计图表",  ExportGraphDTO.class, new ArrayList<>(Arrays.asList(new ExportGraphDTO(file.getBytes())))));
        String fileName = "不良统计报表" + System.currentTimeMillis() +prefix;
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?ExcelType.HSSF:ExcelType.XSSF);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        workbook.write(response.getOutputStream());
    }


    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if ("WORK_SHEET_UNQUALIFIED_REPORT_READ".equals(authority)) {
            return "浏览不良统计报表";
        }
        return "";
    }
}
