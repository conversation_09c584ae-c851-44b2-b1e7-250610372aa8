package net.airuima.rbase.repository.procedure.report;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.report.WorkSheetStatistics;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;


/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单产量统计 Repository
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Repository
public interface WorkSheetStatisticsRepository extends LogicDeleteableRepository<WorkSheetStatistics>,
        EntityGraphJpaSpecificationExecutor<WorkSheetStatistics>, EntityGraphJpaRepository<WorkSheetStatistics, Long> {

    /**
     * 根据记录日期和工单主键id和删除标记查询工单统计记录
     *
     * @param recordDate  记录日期
     * @param workSheetId 工单主键id
     * @param deleted     删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.report.WorkSheetStatistics> 工单统计记录
     */
    @DataFilter(isSkip = true)
    @Query("select w from WorkSheetStatistics w where w.recordDate = ?1 and w.workSheet.id = ?2 and w.deleted = ?3")
    Optional<WorkSheetStatistics> findByRecordDateAndWorkSheetIdAndDeleted(LocalDate recordDate, Long workSheetId, Long deleted);


}
