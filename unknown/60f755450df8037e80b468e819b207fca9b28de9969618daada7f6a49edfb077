package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.CascadeWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单级联关系Repository
 * <AUTHOR>
 */
@Repository
public interface CascadeWorkSheetRepository extends LogicDeleteableRepository<CascadeWorkSheet>,
        EntityGraphJpaSpecificationExecutor<CascadeWorkSheet>, EntityGraphJpaRepository<CascadeWorkSheet, Long> {

    /**
     * 通过上级工单ID及逻辑删除获取下级工单列表
     * @param superiorWorkSheetId 上级工单ID
     * @param deleted 逻辑删除
     * @return 下级工单列表
     */
    @FetchMethod
    @Query("select cw.subordinateWorkSheet from CascadeWorkSheet cw where cw.superiorWorkSheet.id=?1 and cw.deleted=?2")
    List<WorkSheet> findSubordinateWorkSheetBySuperiorWorkSheetId(Long superiorWorkSheetId,Long deleted);
}
