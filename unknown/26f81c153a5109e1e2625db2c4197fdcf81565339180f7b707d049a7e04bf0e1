package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系最新下单定制工序Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface CustomPedigreeStepRepository extends LogicDeleteableRepository<CustomPedigreeStep>,
        EntityGraphJpaSpecificationExecutor<CustomPedigreeStep>, EntityGraphJpaRepository<CustomPedigreeStep, Long> {

    /**
     * 根据产品谱系主键ID获取最新正常工单定制工序
     *
     * @param pedigreeId 产品谱系主键ID
     * @param deleted    删除标志
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep> 产品谱系最新下单定制工序列表
     */
    @DataFilter(isSkip = true)
    List<CustomPedigreeStep> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    /**
     * 根据产品谱系主键ID删除谱系默认流程
     *
     * @param pedigreeId 产品谱系主键ID
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @Modifying
    @Query("delete from CustomPedigreeStep where pedigree.id=?1")
    void deleteByPedigreeId(Long pedigreeId);
}
