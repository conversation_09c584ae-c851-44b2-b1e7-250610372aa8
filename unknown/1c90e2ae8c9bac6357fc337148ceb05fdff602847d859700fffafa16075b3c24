package net.airuima.rbase.web.rest.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.service.report.WorkSheetScheduleReportService;
import net.airuima.rbase.web.rest.report.dto.InProcessWsScheduleReportRequestDTO;
import net.airuima.rbase.web.rest.report.dto.InProcessWsScheduleReportResultDTO;
import net.airuima.rbase.web.rest.report.dto.InProgressFinishedWorkOrderResultDTO;
import net.airuima.rbase.web.rest.report.dto.WorkOrderDTO;
import net.airuima.util.ResponseData;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/26
 */

@Tag(name = "工单进度Resource")
@RestController
@RequestMapping("/api/report/work-sheet-schedule-report")
@AuthorityRegion("报表看板")
public class WorkSheetScheduleReportResource {


    @Autowired
    private WorkSheetScheduleReportService workSheetScheduleReportService;


    /**
     * 根据投产工单进行在制工单进度查询
     *
     * @param inProcessWsScheduleReportRequestDto 请求参数
     * @return 在制工单进度报表结果
     */
    @PostMapping("/in-process-schedule")
    @Operation(summary= "根据投产工单进行在制工单进度查询")
    @PreAuthorize("hasAnyAuthority('IN_PROCESS_WS_SCHEDULE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("WorksheetProgress")
    public ResponseEntity<ResponseData<InProcessWsScheduleReportResultDTO>> inProcessSchedule(@RequestBody InProcessWsScheduleReportRequestDTO inProcessWsScheduleReportRequestDto){
        try {
            InProcessWsScheduleReportResultDTO inProcessWsScheduleReportResultDTO = workSheetScheduleReportService.inProcessSchedule(inProcessWsScheduleReportRequestDto);
            return ResponseData.ok(inProcessWsScheduleReportResultDTO);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 今日已完成工单信息
     * @param workLineId 生产线
     * @param currentPage 当前页数
     * @param pageSize  每页显示数
     * <AUTHOR>
     * @date  2023/6/26
     * @return void
     */
    @GetMapping("/in-progress-Finished-work-order")
    public ResponseEntity<ResponseData<InProgressFinishedWorkOrderResultDTO>> inProgressFinishedWorkOrder(@RequestParam(value = "workLineId") Long workLineId,
                                                                                                          @RequestParam(value = "currentPage",defaultValue = "0") Integer currentPage,
                                                                                                          @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        try {
            InProgressFinishedWorkOrderResultDTO inProgressFinishedWorkOrder = workSheetScheduleReportService.inProgressFinishedWorkOrder(workLineId, currentPage, pageSize);
            return ResponseData.ok(inProgressFinishedWorkOrder);
        }catch (Exception e){
            return ResponseData.error(e);
        }
    }


    /**
     * 今日已完成工单信息
     * @param workLineId 生产线
     * <AUTHOR>
     * @date  2023/6/26
     * @return void
     */
    @GetMapping("/export-in-progress-Finished-work-order")
    public void exportInProgressFinishedWorkOrder(@RequestParam(value = "workLineId") Long workLineId, @RequestParam(value = "excelType",required = false) String excelType,HttpServletResponse response)throws Exception{
        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
        List<WorkOrderDTO> workOrderDTOList = workSheetScheduleReportService.exportInProgressFinishedWorkOrder(workLineId);
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        exportParams.setFreezeCol(Constants.INT_TWO);
        if(StringUtils.isNotBlank(excelType) && excelType.equals("xls")){
            exportParams.setType(ExcelType.HSSF);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, WorkOrderDTO.class, workOrderDTOList);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + URLEncoder.encode("今日已完成工单"+prefix, StandardCharsets.UTF_8));
        response.setCharacterEncoding("utf-8");
        workbook.write(response.getOutputStream());
    }

    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if ("IN_PROCESS_WS_SCHEDULE_REPORT_READ".equals(authority)) {
            return "浏览在制工单进度报表";
        }
        return "";
    }

}
