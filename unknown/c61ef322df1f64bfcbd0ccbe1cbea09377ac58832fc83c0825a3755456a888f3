package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.service.report.DigitalWorkshopReportService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.*;
import net.airuima.rbase.web.rest.report.dto.perform.StaffRankDataDTO;
import net.airuima.util.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "数字化车间驾驶仓Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/report/digital-workshop")
@FuncInterceptor("OrgCockpit || ProductionCockpit || ProcessCockpit")
@AuthorityRegion("报表看板")
public class DigitalWorkshopReportResource {


    @Autowired
    private DigitalWorkshopReportService digitalWorkshopReportService;


    /**
     * 通过车间（部门） 获取 各产线的 工单计划 在制数量信息
     *
     * @param organizationId 部门id
     * @return List<WorkLineStatisticsDTO>
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping("/organization-work-line-statistics")
    @Operation(summary = "通过车间（部门） 获取 各产线的 工单计划 在制数量信息")
    public ResponseEntity<ResponseData<List<WorkLineStatisticsDTO>>> orgWorkLineStatisticsCharts(@RequestParam(value = "organizationId",required = false) Long organizationId) {
        return ResponseData.ok(digitalWorkshopReportService.workLineStatisticsCharts(new DigitalWorkshopBaseDTO().setOrganizationId(organizationId)));
    }

    /**
     * 通过车间（部门） 获取 各工单状态对应总数
     *
     * @param organizationId 部门id
     * @return List<WorksheetStatisticsDTO>
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping("/organization-worksheet-statistics")
    @Operation(summary = "通过车间（部门） 获取 各工单状态对应总数")
    public ResponseEntity<ResponseData<WorksheetStatisticsDTO>> worksheetStatisticsCharts(@RequestParam(value = "organizationId",required = false) Long organizationId) {
        return ResponseData.ok(digitalWorkshopReportService.worksheetStatisticsCharts(organizationId));
    }


    /**
     * 通过车间（部门） 获取 谱系对应不良项，工序对应不良
     *
     * @param organizationId 部门id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping("/organization-unqualified-statistics")
    @Operation(summary = "通过车间（部门） 获取 谱系对应不良项，工序对应不良")
    public ResponseEntity<ResponseData<UnqualifiedStatisticsDTO>> orgUnqualifiedStatisticsCharts(@RequestParam(value = "organizationId",required = false) Long organizationId) {
        return ResponseData.ok(digitalWorkshopReportService.unqualifiedStatisticsCharts(new DigitalWorkshopBaseDTO().setOrganizationId(organizationId)));
    }

    /**
     * 通过车间（部门） 获取 谱系生产直通率，产品趋势
     *
     * @param organizationId 部门id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（部门） 获取 谱系生产直通率，产品趋势")
    @GetMapping("/organization-pedigree-statistics")
    public ResponseEntity<ResponseData<List<PedigreeStatisticsDTO>>> orgPedigreeStatisticsCharts(@RequestParam(value = "organizationId",required = false) Long organizationId) {
        return ResponseData.ok(digitalWorkshopReportService.pedigreeStatisticsCharts(new DigitalWorkshopBaseDTO().setOrganizationId(organizationId)));
    }


    /**
     * 通过车间（部门） 获取 今日产品生产周期
     *
     * @param organizationId 部门id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（部门） 获取 今日产品生产周期")
    @GetMapping("/organization-production-cycle-statistics")
    public ResponseEntity<ResponseData<ProductionCycleStatisticsDTO>> productionCycleStatisticsCharts(@RequestParam(value = "organizationId",required = false) Long organizationId) {
        return ResponseData.ok(digitalWorkshopReportService.productionCycleStatisticsCharts(organizationId));
    }


    /**
     * 通过车间（产线） 获取 员工报工数
     *
     * @param workLineId 部门id
     * @return List<StaffRankDataDTO>
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（产线） 获取 员工报工数")
    @GetMapping("/work-line-staff-rank-statistics/{top}")
    public ResponseEntity<ResponseData<List<StaffRankDataDTO>>> staffRankDataStatisticsCharts(@RequestParam(value = "workLineId",required = false) Long workLineId, @PathVariable("top") int top) {
        return ResponseData.ok(digitalWorkshopReportService.staffRankDataStatisticsCharts(workLineId, top));
    }

    /**
     * 通过车间（产线） 获取  工单计划 在制数量信息
     *
     * @param workLineId 产线id
     * @return List<WorkLineStatisticsDTO>
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping("/work-line-statistics")
    @Operation(summary = "通过车间（产线） 工单计划 在制数量信息")
    public ResponseEntity<ResponseData<WorkLineStatisticsDTO>> workLineStatisticsCharts(@RequestParam(value = "workLineId",required = false) Long workLineId) {
        try {
            List<WorkLineStatisticsDTO> workLineStatisticsDTOList = digitalWorkshopReportService.workLineStatisticsCharts(new DigitalWorkshopBaseDTO().setWorkLineId(workLineId));
            if (ValidateUtils.isValid(workLineStatisticsDTOList)) {
                return ResponseData.ok(workLineStatisticsDTOList.get(Constants.INT_ZERO));
            }
            return ResponseData.ok(new WorkLineStatisticsDTO());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过车间（产线） 获取 获取 谱系对应不良项，工序对应不良
     *
     * @param workLineId 产线id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（产线） 获取 获取 谱系对应不良项，工序对应不良")
    @GetMapping("/work-line-unqualified-statistics")
    public ResponseEntity<ResponseData<UnqualifiedStatisticsDTO>> unqualifiedStatisticsCharts(@RequestParam(value = "workLineId",required = false) Long workLineId) {
        try {
            return ResponseData.ok(digitalWorkshopReportService.unqualifiedStatisticsCharts(new DigitalWorkshopBaseDTO().setWorkLineId(workLineId)));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 通过车间（产线） 获取 谱系生产直通率，产品趋势
     *
     * @param workLineId 产线id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（产线） 获取 谱系生产直通率，产品趋势")
    @GetMapping("/work-line-pedigree-statistics")
    public ResponseEntity<ResponseData<List<PedigreeStatisticsDTO>>> pedigreeStatisticsCharts(@RequestParam(value = "workLineId",required = false) Long workLineId) {
        return ResponseData.ok(digitalWorkshopReportService.pedigreeStatisticsCharts(new DigitalWorkshopBaseDTO().setWorkLineId(workLineId)));
    }

    /**
     * 通过车间（产线） 获取 今日工单排产
     *
     * @param workLineId 产线id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（产线） 获取 今日工单排产")
    @GetMapping("/work-line-ws-schedule-statistics")
    public ResponseEntity<ResponseData<List<WorkOrderSchedulingDTO>>> wsScheduleStatisticsCharts(@RequestParam(value = "workLineId",required = false) Long workLineId) {
        return ResponseData.ok(digitalWorkshopReportService.wsScheduleStatisticsCharts(workLineId));
    }


    /**
     * 通过车间（产线） 获取 工位工序统计信息
     *
     * @param workLineId 产线id
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORKSHOP_COCKPIT_READ') or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过车间（产线） 获取 工位工序统计信息")
    @GetMapping("/work-line-wc-step-statistics")
    public ResponseEntity<ResponseData<List<WorkCellStepStatisticsDTO>>> workCellStepStatisticsCharts(@RequestParam("workLineId") Long workLineId) {
        return ResponseData.ok(digitalWorkshopReportService.workCellStepStatisticsCharts(workLineId));
    }

    public String getAuthorityDescription(String authority) {
        if (StringUtils.isEmpty(authority)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        } else if ("WORKSHOP_COCKPIT_READ".equals(authority)) {
            return "浏览车间驾驶舱";
        }
        return "";
    }
}
