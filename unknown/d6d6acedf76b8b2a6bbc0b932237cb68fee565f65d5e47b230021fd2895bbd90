package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.HideAuthority;
import net.airuima.rbase.dto.report.QualityReportDTO;
import net.airuima.rbase.service.report.QualityReportService;
import net.airuima.util.ResponseContent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 质量报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Tag(name = "质量报表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/report/quality-report")
@AuthorityRegion("报表看板")
@HideAuthority
public class QualityReportResource {

    private final QualityReportService qualityReportService;

    public QualityReportResource(QualityReportService qualityReportService) {
        this.qualityReportService = qualityReportService;
    }

    /**
     * 工单良率报表
     *
     * @param requestInfo 请求参数
     * @return
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_QUALITY_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/work-sheet-quality-report")
    public ResponseEntity<ResponseContent<QualityReportDTO.ResponseInfo>> workSheetQualityReport(
            @RequestBody QualityReportDTO.RequestInfo requestInfo) {
        try {
            return qualityReportService.workSheetQualityReport(requestInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if ("WORK_SHEET_QUALITY_REPORT_READ".equals(authority)) {
            return "浏览工单良率报表";
        }
        return "";
    }

}
