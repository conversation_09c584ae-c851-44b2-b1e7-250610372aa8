package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.report.PageDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.util.NumberUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/7/5
 */
@Schema(description = "反向追溯报表")
public class ReverseTraceReportResultDTO extends PageDTO {

    /**
     * 反向追溯物料批次列表
     */
    @Schema(description = "反向追溯物料批次列表")
    private List<ReverseTraceMaterial> reverseTraceMaterials;

    /**
     * 反向追溯设备列表
     */
    @Schema(description = "反向追溯设备列表")
    private List<ReverseTraceFacility> reverseTraceFacilities;

    /**
     * 反向追溯易损件列表
     */
    @Schema(description = "反向追溯易损件列表")
    private List<ReverseTraceWearingPart> reverseTraceWearingParts;

    public List<ReverseTraceMaterial> getReverseTraceMaterials() {
        return reverseTraceMaterials;
    }

    public void setReverseTraceMaterials(List<ReverseTraceMaterial> reverseTraceMaterials) {
        this.reverseTraceMaterials = reverseTraceMaterials;
    }

    public List<ReverseTraceFacility> getReverseTraceFacilities() {
        return reverseTraceFacilities;
    }

    public ReverseTraceReportResultDTO setReverseTraceFacilities(List<ReverseTraceFacility> reverseTraceFacilities) {
        this.reverseTraceFacilities = reverseTraceFacilities;
        return this;
    }

    public List<ReverseTraceWearingPart> getReverseTraceWearingParts() {
        return reverseTraceWearingParts;
    }

    public ReverseTraceReportResultDTO setReverseTraceWearingParts(List<ReverseTraceWearingPart> reverseTraceWearingParts) {
        this.reverseTraceWearingParts = reverseTraceWearingParts;
        return this;
    }

    @Schema(name = "物料作为追溯条件查询返回的反向追溯生产信息")
    public static class ReverseTraceMaterial {

        /**
         * 工单详情信息
         */
        @Schema(description = "工单详细信息",implementation = ReverseWorkSheetInfo.class)
        @ExcelEntity(name = "工单详情信息")
        private ReverseWorkSheetInfo reverseWorkSheetInfo;


        /**
         * 反向追溯物料详情列表
         */
        @ArraySchema(schema = @Schema(description = "工序生产物料明细信息列表",implementation = ReverseTraceMaterialInfo.class))
        @ExcelCollection(name = "反向追溯物料详情工序列表")
        private List<ReverseTraceMaterialInfo> reverseTraceMaterialInfos;

        public ReverseTraceMaterial() {
        }

        public ReverseWorkSheetInfo getReverseWorkSheetInfo() {
            return reverseWorkSheetInfo;
        }

        public void setReverseWorkSheetInfo(ReverseWorkSheetInfo reverseWorkSheetInfo) {
            this.reverseWorkSheetInfo = reverseWorkSheetInfo;
        }

        public List<ReverseTraceMaterialInfo> getReverseTraceMaterialInfos() {
            return reverseTraceMaterialInfos;
        }

        public ReverseTraceMaterial setReverseTraceMaterialInfos(List<ReverseTraceMaterialInfo> reverseTraceMaterialInfos) {
            this.reverseTraceMaterialInfos = reverseTraceMaterialInfos;
            return this;
        }
    }

    @Schema(name = "设备作为追溯条件查询返回的反向追溯生产信息")
    public static class ReverseTraceFacility {
        /**
         * 工单详情信息
         */
        @Schema(description = "工单详细信息",implementation = ReverseWorkSheetInfo.class)
        @ExcelEntity(name = "工单详情信息")
        private ReverseWorkSheetInfo reverseWorkSheetInfo;


        /**
         * 反向追溯设备详情列表
         */
        @ArraySchema(schema = @Schema(description = "工序生产设备明细信息列表",implementation = ReverseTraceFacilityInfo.class))
        @ExcelCollection(name = "反向追溯设备详情工序列表")
        private List<ReverseTraceFacilityInfo> reverseTraceFacilityInfos;

        public ReverseWorkSheetInfo getReverseWorkSheetInfo() {
            return reverseWorkSheetInfo;
        }

        public ReverseTraceFacility setReverseWorkSheetInfo(ReverseWorkSheetInfo reverseWorkSheetInfo) {
            this.reverseWorkSheetInfo = reverseWorkSheetInfo;
            return this;
        }

        public List<ReverseTraceFacilityInfo> getReverseTraceFacilityInfos() {
            return reverseTraceFacilityInfos;
        }

        public ReverseTraceFacility setReverseTraceFacilityInfos(List<ReverseTraceFacilityInfo> reverseTraceFacilityInfos) {
            this.reverseTraceFacilityInfos = reverseTraceFacilityInfos;
            return this;
        }
    }

    @Schema(description = "易损件作为追溯条件查询返回的反向追溯生产信息")
    public static class ReverseTraceWearingPart {
        /**
         * 工单详情信息
         */
        @Schema(description = "工单详细信息",implementation = ReverseWorkSheetInfo.class)
        @ExcelEntity(name = "工单详情信息")
        private ReverseWorkSheetInfo reverseWorkSheetInfo;


        /**
         * 反向追溯易损件详情列表
         */
        @ArraySchema(schema = @Schema(description = "工序生产易损件明细信息列表",implementation = ReverseTraceWearingPartInfo.class))
        @ExcelCollection(name = "反向追溯易损件详情工序列表")
        private List<ReverseTraceWearingPartInfo> reverseTraceWearingPartInfos;


        public ReverseWorkSheetInfo getReverseWorkSheetInfo() {
            return reverseWorkSheetInfo;
        }

        public ReverseTraceWearingPart setReverseWorkSheetInfo(ReverseWorkSheetInfo reverseWorkSheetInfo) {
            this.reverseWorkSheetInfo = reverseWorkSheetInfo;
            return this;
        }

        public List<ReverseTraceWearingPartInfo> getReverseTraceWearingPartInfos() {
            return reverseTraceWearingPartInfos;
        }

        public ReverseTraceWearingPart setReverseTraceWearingPartInfos(List<ReverseTraceWearingPartInfo> reverseTraceWearingPartInfos) {
            this.reverseTraceWearingPartInfos = reverseTraceWearingPartInfos;
            return this;
        }
    }

    @Schema(description = "工单详细信息")
    public static class ReverseWorkSheetInfo {

        /**
         * 工单编码
         */
        @Schema(description = "工单号",type = "string")
        @Excel(name = "工单编码")
        private String wsSerialNumber;

        /**
         * 子工单编码
         */
        @Schema(description = "子工单号",type = "string")
        @Excel(name = "子工单编码")
        private String subWsSerialNumber;


        /**
         * 单据进度
         */
        @Schema(description = "生产进度",type = "number",format = "double")
        private BigDecimal progress;

        /**
         * 产品谱系编码
         */
        @Schema(description = "产品谱系编码",type = "string")
        @Excel(name = "产品谱系编码")
        private String pedigreeCode;

        /**
         * 产品谱系名称
         */
        @Schema(description = "产品谱系名称",type = "string")
        @Excel(name = "产品谱系名称")
        private String pedigreeName;

        /**
         * 投产数
         */
        @Schema(description = "投产数量",type = "integer",format = "int32")
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 完成数
         */
        @Schema(description = "完成数量",type = "integer",format = "int32")
        @Excel(name = "完成数")
        private Integer finishNumber;

        /**
         * 下达日期
         */
        @Schema(description = "下单日期", type = "string",format = "date-time",example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Excel(name = "下达日期", format = "yyyy-MM-dd HH:mm:ss")
        private Instant createdDate;

        /**
         * 计划开工日期
         */
        @Schema(description = "计划开工日期", type = "string",format = "date-time",example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Excel(name = "计划开工日期", format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime planStartDate;

        /**
         * 计划完工日期
         */
        @Schema(description = "计划完工日期",type = "string",format = "date-time", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Excel(name = "计划完工日期", format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime planEndDate;

        /**
         * 创建人
         */
        @Schema(description = "工单创建人",type = "string")
        @Excel(name = "创建人")
        private String staffName;

        @Schema(description = "预留字段1")
        private String custom1;

        @Schema(description = "预留字段2")
        private String custom2;


        @Schema(description = "预留字段3")
        private String custom3;


        @Schema(description = "预留字段4")
        private String custom4;


        @Schema(description = "预留字段5")
        private String custom5;

        public ReverseWorkSheetInfo() {
        }

        public ReverseWorkSheetInfo(SubWorkSheet subWorkSheet) {
            this.wsSerialNumber = subWorkSheet.getWorkSheet().getSerialNumber();
            this.subWsSerialNumber = subWorkSheet.getSerialNumber();
            this.number = subWorkSheet.getNumber();
            this.finishNumber = subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber();
            this.createdDate = subWorkSheet.getCreatedDate();
            this.planStartDate = subWorkSheet.getPlanStartDate();
            this.planEndDate = subWorkSheet.getPlanEndDate();
            this.staffName = subWorkSheet.getCreatedBy();
            this.pedigreeCode = subWorkSheet.getWorkSheet().getPedigree().getCode();
            this.pedigreeName = subWorkSheet.getWorkSheet().getPedigree().getName();
            this.custom1 = subWorkSheet.getCustom1();
            this.custom2 = subWorkSheet.getCustom2();
            this.custom3 = subWorkSheet.getCustom3();
            this.custom4 = subWorkSheet.getCustom4();
            this.custom5 = subWorkSheet.getCustom5();
            if (subWorkSheet.getNumber() == Constants.INT_ZERO) {
                this.progress = BigDecimal.ZERO;
            } else {
                this.progress = NumberUtils.divide(subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber(), subWorkSheet.getNumber(), Constants.INT_FOUR);
            }
        }

        public ReverseWorkSheetInfo(WorkSheet workSheet) {
            this.wsSerialNumber = workSheet.getSerialNumber();
            this.pedigreeCode = workSheet.getPedigree().getCode();
            this.pedigreeName = workSheet.getPedigree().getName();
            this.progress = workSheet.getProgress();
            this.number = workSheet.getNumber();
            this.finishNumber = workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber();
            this.createdDate = workSheet.getCreatedDate();
            this.planStartDate = workSheet.getPlanStartDate();
            this.planEndDate = workSheet.getPlanEndDate();
            this.staffName = workSheet.getCreatedBy();
            this.custom1 = workSheet.getCustom1();
            this.custom2 = workSheet.getCustom2();
            this.custom3 = workSheet.getCustom3();
            this.custom4 = workSheet.getCustom4();
            this.custom5 = workSheet.getCustom5();
        }

        public BigDecimal getProgress() {
            return progress;
        }

        public void setProgress(BigDecimal progress) {
            this.progress = progress;
        }

        public String getPedigreeCode() {
            return pedigreeCode;
        }

        public void setPedigreeCode(String pedigreeCode) {
            this.pedigreeCode = pedigreeCode;
        }

        public String getPedigreeName() {
            return pedigreeName;
        }

        public void setPedigreeName(String pedigreeName) {
            this.pedigreeName = pedigreeName;
        }

        public String getWsSerialNumber() {
            return wsSerialNumber;
        }

        public void setWsSerialNumber(String wsSerialNumber) {
            this.wsSerialNumber = wsSerialNumber;
        }

        public String getSubWsSerialNumber() {
            return subWsSerialNumber;
        }

        public void setSubWsSerialNumber(String subWsSerialNumber) {
            this.subWsSerialNumber = subWsSerialNumber;
        }

        public Integer getNumber() {
            return number;
        }

        public void setNumber(Integer number) {
            this.number = number;
        }

        public Integer getFinishNumber() {
            return finishNumber;
        }

        public void setFinishNumber(Integer finishNumber) {
            this.finishNumber = finishNumber;
        }

        public Instant getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(Instant createdDate) {
            this.createdDate = createdDate;
        }

        public LocalDateTime getPlanStartDate() {
            return planStartDate;
        }

        public void setPlanStartDate(LocalDateTime planStartDate) {
            this.planStartDate = planStartDate;
        }

        public LocalDateTime getPlanEndDate() {
            return planEndDate;
        }

        public void setPlanEndDate(LocalDateTime planEndDate) {
            this.planEndDate = planEndDate;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getCustom1() {
            return custom1;
        }

        public ReverseWorkSheetInfo setCustom1(String custom1) {
            this.custom1 = custom1;
            return this;
        }

        public String getCustom2() {
            return custom2;
        }

        public ReverseWorkSheetInfo setCustom2(String custom2) {
            this.custom2 = custom2;
            return this;
        }

        public String getCustom3() {
            return custom3;
        }

        public ReverseWorkSheetInfo setCustom3(String custom3) {
            this.custom3 = custom3;
            return this;
        }

        public String getCustom4() {
            return custom4;
        }

        public ReverseWorkSheetInfo setCustom4(String custom4) {
            this.custom4 = custom4;
            return this;
        }

        public String getCustom5() {
            return custom5;
        }

        public ReverseWorkSheetInfo setCustom5(String custom5) {
            this.custom5 = custom5;
            return this;
        }
    }

    public static class ReverseTraceMaterialInfo {

        /**
         * 工序详情
         */
        @Schema(description = "工序生产信息",implementation = ProcessStepInfo.class)
        @ExcelEntity(name = "工序详情信息")
        private ProcessStepInfo processStepInfo;

        /**
         * 工序生产物料信息
         */
        @ExcelCollection(name = "工序详情对应物料信息")
        @ArraySchema(schema = @Schema(description = "物料信息列表",implementation = ProcessStepMaterialInfo.class))
        private List<ProcessStepMaterialInfo> processStepMaterialInfos;

        public ReverseTraceMaterialInfo() {
        }

        public ProcessStepInfo getProcessStepInfo() {
            return processStepInfo;
        }

        public ReverseTraceMaterialInfo setProcessStepInfo(ProcessStepInfo processStepInfo) {
            this.processStepInfo = processStepInfo;
            return this;
        }

        public List<ProcessStepMaterialInfo> getProcessStepMaterialInfos() {
            return processStepMaterialInfos;
        }

        public ReverseTraceMaterialInfo setProcessStepMaterialInfos(List<ProcessStepMaterialInfo> processStepMaterialInfos) {
            this.processStepMaterialInfos = processStepMaterialInfos;
            return this;
        }
    }

    public static class ReverseTraceFacilityInfo {
        /**
         * 工序详情
         */
        @Schema(description = "工序生产信息",implementation = ProcessStepInfo.class)
        @ExcelEntity(name = "工序详情信息")
        private ProcessStepInfo processStepInfo;

        @ArraySchema(schema = @Schema(description = "设备信息列表",implementation = ProcessStepMaterialInfo.class))
        private List<ProcessStepFacilityInfo> processStepFacilityInfos;

        public ProcessStepInfo getProcessStepInfo() {
            return processStepInfo;
        }

        public ReverseTraceFacilityInfo setProcessStepInfo(ProcessStepInfo processStepInfo) {
            this.processStepInfo = processStepInfo;
            return this;
        }

        public List<ProcessStepFacilityInfo> getProcessStepFacilityInfos() {
            return processStepFacilityInfos;
        }

        public ReverseTraceFacilityInfo setProcessStepFacilityInfos(List<ProcessStepFacilityInfo> processStepFacilityInfos) {
            this.processStepFacilityInfos = processStepFacilityInfos;
            return this;
        }
    }

    public static class ReverseTraceWearingPartInfo {
        /**
         * 工序详情
         */
        @Schema(description = "工序生产信息",implementation = ProcessStepInfo.class)
        @ExcelEntity(name = "工序详情信息")
        private ProcessStepInfo processStepInfo;

        @ArraySchema(schema = @Schema(description = "易损件信息列表",implementation = ProcessStepMaterialInfo.class))
        @ExcelCollection(name = "工序详情对应易损件信息")
        private List<ProcessStepWearingPartInfo> processStepWearingPartInfos;

        public ProcessStepInfo getProcessStepInfo() {
            return processStepInfo;
        }

        public ReverseTraceWearingPartInfo setProcessStepInfo(ProcessStepInfo processStepInfo) {
            this.processStepInfo = processStepInfo;
            return this;
        }

        public List<ProcessStepWearingPartInfo> getProcessStepWearingPartInfos() {
            return processStepWearingPartInfos;
        }

        public ReverseTraceWearingPartInfo setProcessStepWearingPartInfos(List<ProcessStepWearingPartInfo> processStepWearingPartInfos) {
            this.processStepWearingPartInfos = processStepWearingPartInfos;
            return this;
        }
    }

    @Schema(name = "工序生产信息")
    public static class ProcessStepInfo {
        /**
         * 工序编码
         */
        @Schema(description = "工序编码",type = "string")
        @Excel(name = "工序编码")
        private String stepCode;
        /**
         * 工序名称
         */
        @Schema(description = "工序名称",type = "string")
        @Excel(name = "工序名称")
        private String stepName;
        /**
         * 开始时间
         */
        @Schema(description = "工序生产开始时间",type = "string",format = "date-time" ,example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Excel(name = "开始时间", format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startDate;

        /**
         * 结束时间
         */
        @Schema(description = "工序生产完成时间",type = "string",format = "date-time", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Excel(name = "结束时间")
        private LocalDateTime endDate;

        /**
         * 投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32")
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 完成数
         */
        @Schema(description = "工序完成数",type = "integer",format = "int32")
        @Excel(name = "完成数")
        private Integer qualifiedNumber;


        /**
         * 合格率
         */
        @Schema(description = "工序合格率",type = "number",format = "double")
        @Excel(name = "合格率", numFormat = "#.##%")
        private Double rate;

        /**
         * 0 未完成，1 完成
         */
        @Schema(description = "是否完成",type = "integer",example = "0:未完成，1:完成")
        @Excel(name = "是否完成", replace = {"未完成_0", "完成_1"})
        private Integer isFinish;

        @Schema(description = "预留字段1")
        private String custom1;

        @Schema(description = "预留字段2")
        private String custom2;


        @Schema(description = "预留字段3")
        private String custom3;


        @Schema(description = "预留字段4")
        private String custom4;


        @Schema(description = "预留字段5")
        private String custom5;

        public ProcessStepInfo() {
        }

        public ProcessStepInfo(BatchWorkDetail batchWorkDetail) {
            this.stepCode = batchWorkDetail.getStep().getCode();
            this.stepName = batchWorkDetail.getStep().getName();
            this.startDate = batchWorkDetail.getStartDate();
            this.endDate = batchWorkDetail.getEndDate();
            this.number = batchWorkDetail.getInputNumber();
            this.qualifiedNumber = batchWorkDetail.getQualifiedNumber();
            this.rate = batchWorkDetail.getInputNumber() != Constants.INT_ZERO ? NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getInputNumber(), Constants.INT_FOUR).doubleValue() : Constants.INT_ZERO;
            this.isFinish = batchWorkDetail.getFinish();
            this.custom1 = batchWorkDetail.getCustom1();
            this.custom2 = batchWorkDetail.getCustom2();
            this.custom3 = batchWorkDetail.getCustom3();
            this.custom4 = batchWorkDetail.getCustom4();
            this.custom5 = batchWorkDetail.getCustom5();
        }

        public String getStepCode() {
            return stepCode;
        }

        public ProcessStepInfo setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public ProcessStepInfo setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public LocalDateTime getStartDate() {
            return startDate;
        }

        public ProcessStepInfo setStartDate(LocalDateTime startDate) {
            this.startDate = startDate;
            return this;
        }

        public LocalDateTime getEndDate() {
            return endDate;
        }

        public ProcessStepInfo setEndDate(LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public ProcessStepInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public ProcessStepInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Double getRate() {
            return rate;
        }

        public ProcessStepInfo setRate(Double rate) {
            this.rate = rate;
            return this;
        }

        public Integer getIsFinish() {
            return isFinish;
        }

        public ProcessStepInfo setIsFinish(Integer isFinish) {
            this.isFinish = isFinish;
            return this;
        }
    }

    @Schema(description = "工序生产物料信息")
    public static class ProcessStepMaterialInfo {
        /**
         * 容器号
         */
        @Schema(description = "容器号",type = "string")
        @Excel(name = "容器号")
        private String containerCode;

        /**
         * sn
         */
        @Schema(description = "sn",type = "string")
        @Excel(name = "sn")
        private String sn;

        /**
         * 员工编码
         */
        @Schema(description = "员工编码",type = "string")
        @Excel(name = "员工编码")
        private String staffCode;

        /**
         * 员工名称
         */
        @Schema(description = "员工名称",type = "string")
        @Excel(name = "员工名称")
        private String staffName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码",type = "string")
        @Excel(name = "工位编码")
        private String workCellCode;
        /**
         * 工位名称
         */
        @Schema(description = "工位名称",type = "string")
        @Excel(name = "工位名称")
        private String workCellName;

        /**
         * 料
         */
        @ArraySchema(schema = @Schema(description = "物料信息列表",implementation = MaterialBatchInfo.class))
        @ExcelCollection(name = "料")
        private List<MaterialBatchInfo> materialBatchInfos;

        /**
         * 投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32")
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 合格数
         */
        @Schema(description = "工序合格数",type = "integer",format = "int32")
        @Excel(name = "合格数")
        private Integer qualifiedNumber;


        /**
         * 料
         */
        @Schema(name = "物料信息")
        public static class MaterialBatchInfo {

            /**
             * 物料编码
             */
            @Schema(description = "物料编码",type = "string")
            @Excel(name = "物料编码")
            private String code;
            /**
             * 物料名称
             */
            @Schema(description = "物料名称",type = "string")
            @Excel(name = "物料名称")
            private String name;

            /**
             * 批次
             */
            @Schema(description = "物料批次",type = "string")
            @Excel(name = "批次")
            private String batch;

            /**
             * 供应商名称
             */
            @Schema(description = "供应商名称",type = "string")
            @Excel(name = "供应商名称")
            private String supplierName;

            /**
             * 数量
             */
            @Schema(description = "数量",type = "number",format = "double")
            @Excel(name = "数量")
            private Double number;


            public MaterialBatchInfo() {
            }


            public MaterialBatchInfo(BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch) {
                this.code = batchWorkDetailMaterialBatch.getMaterialDto().getCode();
                this.name = batchWorkDetailMaterialBatch.getMaterialDto().getName();
                this.batch = batchWorkDetailMaterialBatch.getMaterialBatch();
                this.number = batchWorkDetailMaterialBatch.getNumber();
                this.supplierName = batchWorkDetailMaterialBatch.getSupplierDTO().getName();
            }

            public MaterialBatchInfo(ProcessStepMaterialBatchDTO processStepMaterialBatch) {
                this.code = processStepMaterialBatch.getMaterialDto().getCode();
                this.name = processStepMaterialBatch.getMaterialDto().getName();
                this.batch = processStepMaterialBatch.getBatch();
                this.number = processStepMaterialBatch.getNumber();
                this.supplierName = processStepMaterialBatch.getSupplierDTO().getName();
            }

            public String getBatch() {
                return batch;
            }

            public MaterialBatchInfo setBatch(String batch) {
                this.batch = batch;
                return this;
            }

            public String getSupplierName() {
                return supplierName;
            }

            public MaterialBatchInfo setSupplierName(String supplierName) {
                this.supplierName = supplierName;
                return this;
            }

            public String getCode() {
                return code;
            }

            public MaterialBatchInfo setCode(String code) {
                this.code = code;
                return this;
            }

            public String getName() {
                return name;
            }

            public MaterialBatchInfo setName(String name) {
                this.name = name;
                return this;
            }

            public Double getNumber() {
                return number;
            }

            public void setNumber(Double number) {
                this.number = number;
            }
        }

        public ProcessStepMaterialInfo() {
        }


        public ProcessStepMaterialInfo(BatchWorkDetail batchWorkDetail) {
            this.staffCode = batchWorkDetail.getOperatorDto().getCode();
            this.staffName = batchWorkDetail.getOperatorDto().getName();
            this.workCellCode = batchWorkDetail.getWorkCell().getCode();
            this.workCellName = batchWorkDetail.getWorkCell().getName();
            this.number = batchWorkDetail.getInputNumber();
            this.qualifiedNumber = batchWorkDetail.getQualifiedNumber();
        }

        public ProcessStepMaterialInfo(ProcessStepMaterialBatchDTO processStepMaterialBatch) {
            this.sn = processStepMaterialBatch.getSn();
            this.containerCode = processStepMaterialBatch.getContainerCode();
            this.staffCode = processStepMaterialBatch.getStaffDto().getCode();
            this.staffName = processStepMaterialBatch.getStaffDto().getName();
            this.workCellCode = processStepMaterialBatch.getWorkCellCode();
            this.workCellName = processStepMaterialBatch.getWorkCellName();
            this.number = processStepMaterialBatch.getInputNumber();
            this.qualifiedNumber = processStepMaterialBatch.getQualifiedNumber();
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ProcessStepMaterialInfo setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }

        public String getSn() {
            return sn;
        }

        public ProcessStepMaterialInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public String getStaffCode() {
            return staffCode;
        }

        public ProcessStepMaterialInfo setStaffCode(String staffCode) {
            this.staffCode = staffCode;
            return this;
        }

        public String getStaffName() {
            return staffName;
        }

        public ProcessStepMaterialInfo setStaffName(String staffName) {
            this.staffName = staffName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public ProcessStepMaterialInfo setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public ProcessStepMaterialInfo setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public List<MaterialBatchInfo> getMaterialBatchInfos() {
            return materialBatchInfos;
        }

        public ProcessStepMaterialInfo setMaterialBatchInfos(List<MaterialBatchInfo> materialBatchInfos) {
            this.materialBatchInfos = materialBatchInfos;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public ProcessStepMaterialInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public ProcessStepMaterialInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }
    }

    @Schema(name = "工序生产设备信息")
    public static class ProcessStepFacilityInfo {

        /**
         * 容器号
         */
        @Schema(description = "容器号",type = "string")
        @Excel(name = "容器号")
        private String containerCode;

        /**
         * sn
         */
        @Schema(description = "sn",type = "string")
        @Excel(name = "sn")
        private String sn;

        /**
         * 员工编码
         */
        @Schema(description = "员工编码",type = "string")
        @Excel(name = "员工编码")
        private String staffCode;

        /**
         * 员工名称
         */
        @Schema(description = "员工名称",type = "string")
        @Excel(name = "员工名称")
        private String staffName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码",type = "string")
        @Excel(name = "工位编码")
        private String workCellCode;
        /**
         * 工位名称
         */
        @Schema(description = "工位名称",type = "string")
        @Excel(name = "工位名称")
        private String workCellName;

        /**
         * 设备
         */
        @ArraySchema(schema = @Schema(description = "设备信息列表",implementation = FacilityInfo.class))
        @ExcelCollection(name = "设备")
        private List<FacilityInfo> facilityInfos;

        /**
         * 投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32")
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 合格数
         */
        @Schema(description = "工序合格数",type = "integer",format = "int32")
        @Excel(name = "合格数")
        private Integer qualifiedNumber;

        public ProcessStepFacilityInfo() {
        }

        public ProcessStepFacilityInfo(SnWorkDetail snWorkDetail) {
            this.containerCode = Optional.ofNullable(snWorkDetail.getContainerDetail()).map(ContainerDetail::getContainerCode).orElse(null);
            this.sn = snWorkDetail.getSn();
            this.staffCode = snWorkDetail.getOperatorDto().getCode();
            this.staffName = snWorkDetail.getOperatorDto().getName();
            this.workCellCode = snWorkDetail.getWorkCell().getCode();
            this.workCellName = snWorkDetail.getWorkCell().getName();
            this.number = Constants.INT_ONE;
            this.qualifiedNumber = snWorkDetail.getResult();
        }

        public ProcessStepFacilityInfo(ContainerDetail containerDetail) {
            this.containerCode = containerDetail.getContainerCode();
            this.staffCode = containerDetail.getStaffDto().getCode();
            this.staffName = containerDetail.getStaffDto().getName();
            this.workCellCode = containerDetail.getWorkCell().getCode();
            this.workCellName = containerDetail.getWorkCell().getName();
            this.number = containerDetail.getInputNumber();
            this.qualifiedNumber = containerDetail.getQualifiedNumber();
        }

        public ProcessStepFacilityInfo(BatchWorkDetail batchWorkDetail) {
            this.staffCode = batchWorkDetail.getOperatorDto().getCode();
            this.staffName = batchWorkDetail.getOperatorDto().getName();
            this.workCellCode = batchWorkDetail.getWorkCell().getCode();
            this.workCellName = batchWorkDetail.getWorkCell().getName();
            this.number = batchWorkDetail.getInputNumber();
            this.qualifiedNumber = batchWorkDetail.getQualifiedNumber();
        }


        @Schema(name = "设备信息")
        public static class FacilityInfo {

            @Schema(description = "设备编码",type = "string")
            @Excel(name = "设备编码")
            private String code;

            @Schema(description = "设备名称",type = "string")
            @Excel(name = "设备名称")
            private String name;

            public FacilityInfo() {
            }

            public FacilityInfo(FacilityDTO facilityDto) {
                this.code = facilityDto.getCode();
                this.name = facilityDto.getName();
            }

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }


        public String getContainerCode() {
            return containerCode;
        }

        public void setContainerCode(String containerCode) {
            this.containerCode = containerCode;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public String getStaffCode() {
            return staffCode;
        }

        public void setStaffCode(String staffCode) {
            this.staffCode = staffCode;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public void setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public void setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
        }

        public List<FacilityInfo> getFacilityInfos() {
            return facilityInfos;
        }

        public ProcessStepFacilityInfo setFacilityInfos(List<FacilityInfo> facilityInfos) {
            this.facilityInfos = facilityInfos;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public void setNumber(Integer number) {
            this.number = number;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public void setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
        }
    }

    @Schema(name = "工序生产易损件信息")
    public static class ProcessStepWearingPartInfo {

        /**
         * 容器号
         */
        @Schema(description = "容器号",type = "string")
        @Excel(name = "容器号")
        private String containerCode;

        /**
         * sn
         */
        @Schema(description = "sn",type = "string")
        @Excel(name = "sn")
        private String sn;

        /**
         * 员工编码
         */
        @Schema(description = "员工编码",type = "string")
        @Excel(name = "员工编码")
        private String staffCode;

        /**
         * 员工名称
         */
        @Schema(description = "员工名称",type = "string")
        @Excel(name = "员工名称")
        private String staffName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码",type = "string")
        @Excel(name = "工位编码")
        private String workCellCode;
        /**
         * 工位名称
         */
        @Schema(description = "工位名称",type = "string")
        @Excel(name = "工位名称")
        private String workCellName;

        /**
         * 易损件
         */
        @ArraySchema(schema = @Schema(description = "易损件信息列表",implementation = WearingPartInfo.class))
        @ExcelCollection(name = "易损件")
        private List<WearingPartInfo> wearingPartInfos;

        /**
         * 投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32")
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 合格数
         */
        @Schema(description = "工序合格数",type = "integer",format = "int32")
        @Excel(name = "合格数")
        private Integer qualifiedNumber;

        public ProcessStepWearingPartInfo() {
        }

        public ProcessStepWearingPartInfo(SnWorkDetail snWorkDetail) {
            this.containerCode = Optional.ofNullable(snWorkDetail.getContainerDetail()).map(ContainerDetail::getContainerCode).orElse(null);
            this.sn = snWorkDetail.getSn();
            this.staffCode = snWorkDetail.getOperatorDto().getCode();
            this.staffName = snWorkDetail.getOperatorDto().getName();
            this.workCellCode = snWorkDetail.getWorkCell().getCode();
            this.workCellName = snWorkDetail.getWorkCell().getName();
            this.number = Constants.INT_ONE;
            this.qualifiedNumber = snWorkDetail.getResult();
        }

        public ProcessStepWearingPartInfo(ContainerDetail containerDetail) {
            this.containerCode = containerDetail.getContainerCode();
            this.staffCode = containerDetail.getStaffDto().getCode();
            this.staffName = containerDetail.getStaffDto().getName();
            this.workCellCode = containerDetail.getWorkCell().getCode();
            this.workCellName = containerDetail.getWorkCell().getName();
            this.number = containerDetail.getInputNumber();
            this.qualifiedNumber = containerDetail.getQualifiedNumber();
        }

        public ProcessStepWearingPartInfo(BatchWorkDetail batchWorkDetail) {
            this.staffCode = batchWorkDetail.getOperatorDto().getCode();
            this.staffName = batchWorkDetail.getOperatorDto().getName();
            this.workCellCode = batchWorkDetail.getWorkCell().getCode();
            this.workCellName = batchWorkDetail.getWorkCell().getName();
            this.number = batchWorkDetail.getInputNumber();
            this.qualifiedNumber = batchWorkDetail.getQualifiedNumber();
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ProcessStepWearingPartInfo setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }

        public String getSn() {
            return sn;
        }

        public ProcessStepWearingPartInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public String getStaffCode() {
            return staffCode;
        }

        public ProcessStepWearingPartInfo setStaffCode(String staffCode) {
            this.staffCode = staffCode;
            return this;
        }

        public String getStaffName() {
            return staffName;
        }

        public ProcessStepWearingPartInfo setStaffName(String staffName) {
            this.staffName = staffName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public ProcessStepWearingPartInfo setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public ProcessStepWearingPartInfo setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public List<WearingPartInfo> getWearingPartInfos() {
            return wearingPartInfos;
        }

        public ProcessStepWearingPartInfo setWearingPartInfos(List<WearingPartInfo> wearingPartInfos) {
            this.wearingPartInfos = wearingPartInfos;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public ProcessStepWearingPartInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public ProcessStepWearingPartInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        @Schema(name = "易损件信息")
        public static class WearingPartInfo {

            /**
             * 易损件编码
             */
            @Schema(description = "易损件编码",type = "string")
            @Excel(name = "易损件编码")
            private String code;

            /**
             * 易损件名称
             */
            @Schema(description = "易损件名称",type = "string")
            @Excel(name = "易损件名称")
            private String name;

            public WearingPartInfo() {
            }

            public WearingPartInfo(WearingPart wearingPart) {
                this.code = wearingPart.getCode();
                this.name = wearingPart.getName();
            }

            public String getCode() {
                return code;
            }

            public WearingPartInfo setCode(String code) {
                this.code = code;
                return this;
            }

            public String getName() {
                return name;
            }

            public WearingPartInfo setName(String name) {
                this.name = name;
                return this;
            }
        }


    }

}
