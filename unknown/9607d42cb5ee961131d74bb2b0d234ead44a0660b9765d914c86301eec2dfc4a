package net.airuima.rbase.web.rest.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.service.report.StaffPerformStatisticReportService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.web.rest.report.dto.ExportGraphDTO;
import net.airuima.rbase.web.rest.report.dto.perform.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计看板Resource
 *
 * <AUTHOR>
 * @date 2023/06/27
 */

@Tag(name = "报工统计看板Resource")
@RestController
@RequestMapping("/api/report/staff-perform-statistic-report")
@AuthorityRegion("报表看板")
public class StaffPerformStatisticReportResource {


    private final StaffPerformStatisticReportService staffPerformStatisticReportService;

    public StaffPerformStatisticReportResource(StaffPerformStatisticReportService staffPerformStatisticReportService) {
        this.staffPerformStatisticReportService = staffPerformStatisticReportService;
    }


    /**
     * 报工统计看板排行榜数据
     *
     * @param staffPerformStatisticRequestDto 工报工统计看板请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.StaffPerformStatisticReportRankResultDTO> 工单报表统计图表返回结果
     */
    @PreAuthorize("hasAnyAuthority('STAFF_PERFORM_STATISTIC_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ReportedStatistics")
    @PostMapping("/rank/{top}")
    public ResponseEntity<ResponseData<StaffPerformStatisticReportRankResultDTO>> staffPerformStatisticReportRank(@RequestBody StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto, @PathVariable("top") int top) {
        try {
            StaffPerformStatisticReportRankResultDTO staffPerformStatisticReportRankResultDto = staffPerformStatisticReportService.getStaffPerformStatisticRank(staffPerformStatisticRequestDto,top);
            return ResponseData.ok(staffPerformStatisticReportRankResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 报工统计看板报工分布数据
     *
     * @param staffPerformStatisticRequestDto 报工统计看板请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.StaffPerformStatisticReportTableResultDTO> 工单报表表格数据返回结果
     */
    @PreAuthorize("hasAnyAuthority('STAFF_PERFORM_STATISTIC_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ReportedStatistics")
    @PostMapping("/distribution")
    public ResponseEntity<ResponseData<List<DistributionChartDataDTO>>> staffPerformStatisticReportDistributionChart(@RequestBody StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto) {
        try {
            List<DistributionChartDataDTO> distributionChartDataDTOList = staffPerformStatisticReportService.getStaffPerformStatisticDistribution(staffPerformStatisticRequestDto);
            return ResponseData.ok(distributionChartDataDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 报工统计看板表格数据
     *
     * @param staffPerformStatisticRequestDto 工报工统计看板请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.StaffPerformStatisticReportRankResultDTO> 工单报表统计表格返回结果
     */
    @PreAuthorize("hasAnyAuthority('STAFF_PERFORM_STATISTIC_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ReportedStatistics")
    @PostMapping("/table")
    public ResponseEntity<ResponseData<StaffPerformStatisticReportTableResultDTO>> staffPerformStatisticReportTable(@RequestBody StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto) {
        try {
            StaffPerformStatisticReportTableResultDTO staffPerformStatisticReportTableResultDto = staffPerformStatisticReportService.getStaffPerformStatisticTable(staffPerformStatisticRequestDto);
            return ResponseData.ok(staffPerformStatisticReportTableResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 报工统计看板表格数据导出
     *
     * @param file 图形区域文件
     * @param requestParam 请求参数
     */
    @PreAuthorize("hasAnyAuthority('STAFF_PERFORM_STATISTIC_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("ReportedStatistics")
    @PostMapping("/export")
    public void staffPerformStatisticReportTableExport(@RequestParam("file") MultipartFile file,
                                                       @RequestParam("requestParam") String requestParam,
                                                       @RequestParam(value = "excelType",required = false) String excelType,
                                                       HttpServletResponse response) throws IOException {
        StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto = JSON.parseObject(requestParam, new TypeReference<StaffPerformStatisticRequestDTO>() {
        });
        StaffPerformStatisticReportTableResultDTO staffPerformStatisticReportTableResultDto = staffPerformStatisticReportService.getStaffPerformStatisticTable(staffPerformStatisticRequestDto);
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(ExcelUtils.createSheetParams("报工明细",  StaffPerformStatisticReportTableItemDTO.class, staffPerformStatisticReportTableResultDto.getStaffPerformStatisticReportTableItemList()));
        sheetsList.add(ExcelUtils.createSheetParams("统计图表",  ExportGraphDTO.class, new ArrayList<>(List.of(new ExportGraphDTO(file.getBytes())))));
        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
        String fileName = "报工统计报表" + System.currentTimeMillis() + prefix;
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?ExcelType.HSSF:ExcelType.XSSF);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));
        workbook.write(response.getOutputStream());
    }


    public String getAuthorityDescription(String authority) {
        if (StringUtils.isEmpty(authority)) {
            return "";
        } else if ("STAFF_PERFORM_STATISTIC_REPORT_READ".equals(authority)) {
            return "报工统计报表";
        }
        return "";
    }


}
