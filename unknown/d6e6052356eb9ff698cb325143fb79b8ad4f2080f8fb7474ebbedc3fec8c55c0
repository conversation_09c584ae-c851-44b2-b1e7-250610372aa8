package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import java.time.LocalDateTime;

/**
 * 订单列表请求DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class OrderListRequestDTO {

    /**
     * 业务单号
     */
    private String code;

    /**
     * 物料批次号
     */
    private String batch;

    /**
     * 0, "销售订单"
     * 1, "工单"
     * 2, "子工单"
     * 3, "容器"
     * 4, "sn"
     * 5, "物料"
     * 6, "设备"
     * 7, "易损件"
     * 8, "人员"
     */
    private Integer type;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    public String getBatch() {
        return batch;
    }

    public OrderListRequestDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public String getCode() {
        return code;
    }

    public OrderListRequestDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public OrderListRequestDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public OrderListRequestDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public OrderListRequestDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }
}
