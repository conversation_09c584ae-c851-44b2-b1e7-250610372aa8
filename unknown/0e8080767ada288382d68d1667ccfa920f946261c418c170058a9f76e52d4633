package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.aps.InProcessScheduleReportDTO;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/20
 */
@Schema(description = "投产工单在制看板DTO")
public class InProcessWsScheduleReportResultDTO extends PageDTO {

    /**
     * 在制工单看板表头工位列表
     */
    @Schema(description = "在制工单看板表头工位列表")
    private List<WorkCellDTO> workCellList;

    /**
     * 在制工单详情信息
     */
    @Schema(description = "在制工单详情信息")
    private List<InProcessWsDTO> inProcessWsDtoList;

    public List<WorkCellDTO> getWorkCellList() {
        return workCellList;
    }

    public InProcessWsScheduleReportResultDTO setWorkCellList(List<WorkCellDTO> workCellList) {
        this.workCellList = workCellList;
        return this;
    }

    public List<InProcessWsDTO> getInProcessWsDtoList() {
        return inProcessWsDtoList;
    }

    public InProcessWsScheduleReportResultDTO setInProcessWsDtoList(List<InProcessWsDTO> inProcessWsDtoList) {
        this.inProcessWsDtoList = inProcessWsDtoList;
        return this;
    }

    /**
     * 在制工单信息
     */
    @Schema(description = "在制工单信息")
    public static class InProcessWsDTO {
        /**
         * 投产工单
         */
        @Schema(description = "投产工单")
        private String serialNumber;
        /**
         * 产品谱系编码
         */
        @Schema(description = "产品谱系编码")
        private String pedigreeCode;
        /**
         * 产品谱系名称
         */
        @Schema(description = "产品谱系名称")
        private String pedigreeName;

        /**
         * 规格型号
         */
        @Schema(description = "规格型号")
        private String specification;
        /**
         * 下达时间
         */
        @Schema(description = "下达时间")
        private Instant createDate;
        /**
         * 计划开工时间
         */
        @Schema(description = "计划开工时间")
        private LocalDateTime planStartDate;
        /**
         * 计划完工时间
         */
        @Schema(description = "计划完工时间")
        private LocalDateTime planEndDate;
        /**
         * 工单投产数量
         */
        @Schema(description = "工单投产数量")
        private Integer number;

        /**
         * 工单状态
         */
        @Schema(description = "工单状态：-1 逾期，0:已下单;1:投产中;2:已暂停;")
        private Integer status;

        private List<WorkCellStepDTO> workCellStepDtoList;

        public InProcessWsDTO() {
        }

        public InProcessWsDTO(SubWorkSheet subWorkSheet) {
            this.serialNumber = subWorkSheet.getSerialNumber();
            this.pedigreeCode = subWorkSheet.getWorkSheet().getPedigree().getCode();
            this.pedigreeName = subWorkSheet.getWorkSheet().getPedigree().getName();
            this.specification = subWorkSheet.getWorkSheet().getPedigree().getSpecification();
            this.createDate = subWorkSheet.getCreatedDate();
            this.planStartDate = subWorkSheet.getPlanStartDate();
            this.planEndDate = subWorkSheet.getPlanEndDate();
            this.number = subWorkSheet.getNumber();
            boolean isOverdue = subWorkSheet.getWorkSheet().getIsOverdue();
            if (isOverdue) {
                this.status = Constants.APPROVE;
            } else {
                this.status = subWorkSheet.getStatus();
            }
        }

        public InProcessWsDTO(WorkSheet workSheet) {
            this.serialNumber = workSheet.getSerialNumber();
            this.pedigreeCode = workSheet.getPedigree().getCode();
            this.pedigreeName = workSheet.getPedigree().getName();
            this.specification = workSheet.getPedigree().getSpecification();
            this.createDate = workSheet.getCreatedDate();
            this.planStartDate = workSheet.getPlanStartDate();
            this.planEndDate = workSheet.getPlanEndDate();
            this.number = workSheet.getNumber();
            boolean isOverdue = workSheet.getIsOverdue();
            if (isOverdue) {
                this.status = Constants.APPROVE;
            } else {
                this.status = workSheet.getStatus();
            }
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public InProcessWsDTO setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public String getPedigreeCode() {
            return pedigreeCode;
        }

        public InProcessWsDTO setPedigreeCode(String pedigreeCode) {
            this.pedigreeCode = pedigreeCode;
            return this;
        }

        public String getPedigreeName() {
            return pedigreeName;
        }

        public InProcessWsDTO setPedigreeName(String pedigreeName) {
            this.pedigreeName = pedigreeName;
            return this;
        }

        public String getSpecification() {
            return specification;
        }

        public InProcessWsDTO setSpecification(String specification) {
            this.specification = specification;
            return this;
        }

        public LocalDateTime getCreateDate() {
            return LocalDateTime.ofInstant(createDate, ZoneId.systemDefault());
        }

        public InProcessWsDTO setCreateDate(Instant createDate) {
            this.createDate = createDate;
            return this;
        }

        public LocalDateTime getPlanStartDate() {
            return planStartDate;
        }

        public InProcessWsDTO setPlanStartDate(LocalDateTime planStartDate) {
            this.planStartDate = planStartDate;
            return this;
        }

        public LocalDateTime getPlanEndDate() {
            return planEndDate;
        }

        public InProcessWsDTO setPlanEndDate(LocalDateTime planEndDate) {
            this.planEndDate = planEndDate;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public InProcessWsDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getStatus() {
            return status;
        }

        public InProcessWsDTO setStatus(Integer status) {
            this.status = status;
            return this;
        }

        public List<WorkCellStepDTO> getWorkCellStepDtoList() {
            return workCellStepDtoList;
        }

        public InProcessWsDTO setWorkCellStepDtoList(List<WorkCellStepDTO> workCellStepDtoList) {
            this.workCellStepDtoList = workCellStepDtoList;
            return this;
        }
    }

    /**
     * 工位对应工序信息
     */
    @Schema(description = "工位对应工序信息DTO")
    public static class WorkCellStepDTO {

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;
        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellName;

        /**
         * 工序信息
         */
        @Schema(description = "工序信息")
        private List<WorkCellStepInputNumberDTO> workCellStepInputNumberDtoList;

        public String getWorkCellCode() {
            return workCellCode;
        }

        public WorkCellStepDTO setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public WorkCellStepDTO setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public List<WorkCellStepInputNumberDTO> getWorkCellStepInputNumberDtoList() {
            return workCellStepInputNumberDtoList;
        }

        public WorkCellStepDTO setWorkCellStepInputNumberDtoList(List<WorkCellStepInputNumberDTO> workCellStepInputNumberDtoList) {
            this.workCellStepInputNumberDtoList = workCellStepInputNumberDtoList;
            return this;
        }
    }

    /**
     * 工序信息
     */
    @Schema(description = "工序信息DTO")
    public static class WorkCellStepInputNumberDTO {
        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;
        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;
        /**
         * 当前工位在工序投产数量
         */
        @Schema(description = "当前工位在工序投产数量")
        private Long inputNumber;
        /**
         * 当前工序是否完成 0，未完成，1：完成，2 投产中
         */
        @Schema(description = "当前工序是否完成")
        private Integer isFinish;

        public WorkCellStepInputNumberDTO() {
        }

        public WorkCellStepInputNumberDTO(WorkCellStep workCellStep) {
            this.stepCode = workCellStep.getStep().getCode();
            this.stepName = workCellStep.getStep().getName();
            this.inputNumber = Constants.LONG_ZERO;
            this.isFinish = Constants.INT_ZERO;
        }

        public WorkCellStepInputNumberDTO(InProcessScheduleReportDTO inProcessScheduleReportDto) {
            this.stepCode = inProcessScheduleReportDto.getStepCode();
            this.stepName = inProcessScheduleReportDto.getStepName();
            this.inputNumber = inProcessScheduleReportDto.getInputNumber();
            this.isFinish = this.inputNumber == Constants.INT_ZERO ? Constants.INT_ZERO : inProcessScheduleReportDto.getIsFinish() == Constants.INT_ZERO ? Constants.INT_TWO : Constants.INT_ONE;
        }

        public String getStepCode() {
            return stepCode;
        }

        public WorkCellStepInputNumberDTO setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public WorkCellStepInputNumberDTO setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public Long getInputNumber() {
            return inputNumber;
        }

        public WorkCellStepInputNumberDTO setInputNumber(Long inputNumber) {
            this.inputNumber = inputNumber;
            return this;
        }

        public Integer getIsFinish() {
            return isFinish;
        }

        public WorkCellStepInputNumberDTO setIsFinish(Integer isFinish) {
            this.isFinish = isFinish;
            return this;
        }
    }

    /**
     * 工位DTO
     */
    @Schema(description = "工位DTO")
    public static class WorkCellDTO {

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位顺序
         */
        @Schema(description = "工位顺序")
        private Integer orderNumber;

        public String getWorkCellCode() {
            return workCellCode;
        }

        public WorkCellDTO setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public WorkCellDTO setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public Integer getOrderNumber() {
            return orderNumber;
        }

        public WorkCellDTO setOrderNumber(Integer orderNumber) {
            this.orderNumber = orderNumber;
            return this;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            WorkCellDTO that = (WorkCellDTO) o;
            return Objects.equals(workCellCode, that.workCellCode) && Objects.equals(workCellName, that.workCellName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(workCellCode, workCellName);
        }
    }
}
