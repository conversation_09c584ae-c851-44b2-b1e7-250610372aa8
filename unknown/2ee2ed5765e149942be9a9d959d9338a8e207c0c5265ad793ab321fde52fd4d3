package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.util.NumberUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 质量报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Schema(description = "生产过程正向追溯工序详情DTO")
@FetchEntity
public class ForwardTraceStepDetailExportDTO implements Serializable {
    /**
     * 单据号
     */
    @Schema(description = "单据号")
    @Excel(name = "单据号", orderNum = "1")
    private String serialNumber;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工序编码", orderNum = "1")
    private String stepCode;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    @Excel(name = "工序名称", orderNum = "2")
    private String stepName;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "工序开始时间", orderNum = "3")
    private LocalDateTime startDate;

    /**
     * 工序结束时间
     */
    @Schema(description = "工序结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "工序结束时间", orderNum = "4")
    private LocalDateTime endDate;

    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    @Excel(name = "工序投产数", orderNum = "5")
    private Integer stepInputNumber;

    /**
     * 工序合格数
     */
    @Schema(description = "工序合格数")
    @Excel(name = "工序合格数", orderNum = "6")
    private Integer stepQualifiedNumber;

    /**
     * 工序不合格数
     */
    @Schema(description = "工序不合格数")
    @Excel(name = "工序不合格数", orderNum = "7")
    private Integer stepUnqualifiedNumber;

    /**
     * 工序合格率
     */
    @Schema(description = "工序合格率")
    @Excel(name = "工序合格率", orderNum = "8")
    private BigDecimal stepQualifiedRate;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    @Excel(name = "容器编码", orderNum = "8",funcKey = "Container")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    @Excel(name = "SN", orderNum = "9",funcKey = "Single")
    private String sn;

    /**
     * 员工Id
     */
    @Schema(description = "员工Id")
    private Long staffId;

    /**
     * 员工DTO
     **/
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    @Excel(name = "员工编码", orderNum = "10")
    private String staffCode;

    /**
     * 员工名称
     */
    @Schema(description = "员工姓名")
    @Excel(name = "员工姓名", orderNum = "11")
    private String staffName;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    @Excel(name = "工位编码", orderNum = "12")
    private String workCellCode;

    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    @Excel(name = "工位名称", orderNum = "13")
    private String workCellName;

    /**
     * 物料ID
     */
    @Schema(description = "物料ID")
    private Long materialId;

    /**
     * 物料DTO
     **/
    @Schema(description = "物料DTO")
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    @Excel(name = "物料编码", orderNum = "14")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    @Excel(name = "物料名称", orderNum = "15")
    private String materialName;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    @Excel(name = "物料批次", orderNum = "16")
    private String materialBatch;

    /**
     * 物料数量
     */
    @Schema(description = "物料数量")
    @Excel(name = "物料数量", orderNum = "17")
    private Double materialNumber;

    /**
     * 易损件编码
     */
    @Schema(description = "易损件编码")
    @Excel(name = "易损件编码", orderNum = "18",funcKey = "WearingPart")
    private String wearingPartCode;

    /**
     * 易损件名称
     */
    @Schema(description = "易损件名称")
    @Excel(name = "易损件名称", orderNum = "19",funcKey = "WearingPart")
    private String wearingPartName;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private Long facilityId;
    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    @Excel(name = "设备编码", orderNum = "20",funcKey = "FBase")
    private String facilityCode;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    @Excel(name = "设备名称", orderNum = "21",funcKey = "FBase")
    private String facilityName;

    /**
     * 不良项目名称
     */
    @Schema(description = "不良项目名称")
    @Excel(name = "不良项目名称", orderNum = "22")
    private String unqualifiedItemName;
    /**
     * 不良项目编码
     */
    @Schema(description = "不良项目编码")
    @Excel(name = "不良项目编码", orderNum = "23")
    private String unqualifiedItemCode;
    /**
     * 不良项目数量
     */
    @Schema(description = "不良项目数量")
    @Excel(name = "不良项目数量", orderNum = "24")
    private Integer unqualifiedItemNumber;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数", orderNum = "25")
    private Integer inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "26")
    private Integer qualifiedNumber;

    @Schema(description = "预留字段1")
    private String custom1;

    @Schema(description = "预留字段2")
    private String custom2;


    @Schema(description = "预留字段3")
    private String custom3;


    @Schema(description = "预留字段4")
    private String custom4;


    @Schema(description = "预留字段5")
    private String custom5;


    public String getStepCode() {
        return stepCode;
    }

    public ForwardTraceStepDetailExportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public ForwardTraceStepDetailExportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public ForwardTraceStepDetailExportDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public ForwardTraceStepDetailExportDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public Integer getStepInputNumber() {
        return stepInputNumber;
    }

    public ForwardTraceStepDetailExportDTO setStepInputNumber(Integer stepInputNumber) {
        this.stepInputNumber = stepInputNumber;
        return this;
    }

    public Integer getStepQualifiedNumber() {
        return stepQualifiedNumber;
    }

    public ForwardTraceStepDetailExportDTO setStepQualifiedNumber(Integer stepQualifiedNumber) {
        this.stepQualifiedNumber = stepQualifiedNumber;
        return this;
    }

    public BigDecimal getStepQualifiedRate() {
        return NumberUtils.divide(this.getStepQualifiedNumber(), this.getStepInputNumber(), Constants.INT_TWO);
    }

    public ForwardTraceStepDetailExportDTO setStepQualifiedRate(BigDecimal stepQualifiedRate) {
        this.stepQualifiedRate = stepQualifiedRate;
        return this;
    }

    public Integer getStepUnqualifiedNumber() {
        return stepUnqualifiedNumber;
    }

    public ForwardTraceStepDetailExportDTO setStepUnqualifiedNumber(Integer stepUnqualifiedNumber) {
        this.stepUnqualifiedNumber = stepUnqualifiedNumber;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public ForwardTraceStepDetailExportDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ForwardTraceStepDetailExportDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public ForwardTraceStepDetailExportDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public ForwardTraceStepDetailExportDTO setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public ForwardTraceStepDetailExportDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public ForwardTraceStepDetailExportDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public ForwardTraceStepDetailExportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public ForwardTraceStepDetailExportDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public ForwardTraceStepDetailExportDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public ForwardTraceStepDetailExportDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public ForwardTraceStepDetailExportDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getMaterialName() {
        return materialName;
    }

    public ForwardTraceStepDetailExportDTO setMaterialName(String materialName) {
        this.materialName = materialName;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public ForwardTraceStepDetailExportDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public Double getMaterialNumber() {
        return materialNumber;
    }

    public ForwardTraceStepDetailExportDTO setMaterialNumber(Double materialNumber) {
        this.materialNumber = materialNumber;
        return this;
    }

    public String getWearingPartCode() {
        return wearingPartCode;
    }

    public ForwardTraceStepDetailExportDTO setWearingPartCode(String wearingPartCode) {
        this.wearingPartCode = wearingPartCode;
        return this;
    }

    public String getWearingPartName() {
        return wearingPartName;
    }

    public ForwardTraceStepDetailExportDTO setWearingPartName(String wearingPartName) {
        this.wearingPartName = wearingPartName;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public ForwardTraceStepDetailExportDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public ForwardTraceStepDetailExportDTO setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public String getFacilityCode() {
        return facilityCode;
    }

    public ForwardTraceStepDetailExportDTO setFacilityCode(String facilityCode) {
        this.facilityCode = facilityCode;
        return this;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public ForwardTraceStepDetailExportDTO setFacilityName(String facilityName) {
        this.facilityName = facilityName;
        return this;
    }

    public String getUnqualifiedItemCode() {
        return unqualifiedItemCode;
    }

    public ForwardTraceStepDetailExportDTO setUnqualifiedItemCode(String unqualifiedItemCode) {
        this.unqualifiedItemCode = unqualifiedItemCode;
        return this;
    }

    public String getUnqualifiedItemName() {
        return unqualifiedItemName;
    }

    public ForwardTraceStepDetailExportDTO setUnqualifiedItemName(String unqualifiedItemName) {
        this.unqualifiedItemName = unqualifiedItemName;
        return this;
    }

    public Integer getUnqualifiedItemNumber() {
        return unqualifiedItemNumber;
    }

    public ForwardTraceStepDetailExportDTO setUnqualifiedItemNumber(Integer unqualifiedItemNumber) {
        this.unqualifiedItemNumber = unqualifiedItemNumber;
        return this;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public ForwardTraceStepDetailExportDTO setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ForwardTraceStepDetailExportDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public ForwardTraceStepDetailExportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getCustom1() {
        return custom1;
    }

    public ForwardTraceStepDetailExportDTO setCustom1(String custom1) {
        this.custom1 = custom1;
        return this;
    }

    public String getCustom2() {
        return custom2;
    }

    public ForwardTraceStepDetailExportDTO setCustom2(String custom2) {
        this.custom2 = custom2;
        return this;
    }

    public String getCustom3() {
        return custom3;
    }

    public ForwardTraceStepDetailExportDTO setCustom3(String custom3) {
        this.custom3 = custom3;
        return this;
    }

    public String getCustom4() {
        return custom4;
    }

    public ForwardTraceStepDetailExportDTO setCustom4(String custom4) {
        this.custom4 = custom4;
        return this;
    }

    public String getCustom5() {
        return custom5;
    }

    public ForwardTraceStepDetailExportDTO setCustom5(String custom5) {
        this.custom5 = custom5;
        return this;
    }
}
