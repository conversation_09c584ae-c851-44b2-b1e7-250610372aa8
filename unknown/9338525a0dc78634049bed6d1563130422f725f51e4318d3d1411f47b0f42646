package net.airuima.rbase.dto.organization;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * 供应商信息DTO
 * <AUTHOR>
 * @date 2022/11/7 18:56
 */
@Schema(description = "供应商信息DTO")
public class SupplierDTO extends AbstractDto implements Serializable {
    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String name;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String code;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Forbidden
    private Boolean isEnable;

    /**
     * 供应商地址
     */
    @Schema(description = "供应商地址")
    private String address;

    /**
     * 供应商联系电话
     */
    @Schema(description = "供应商联系电话")
    private String phoneNumber;

    /**
     * 供应商联系人
     */
    @Schema(description = "供应商联系人")
    private String contact;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    public String getName() {
        return name;
    }

    public SupplierDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SupplierDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Boolean getEnable() {
        return isEnable;
    }

    public SupplierDTO setEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public SupplierDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public SupplierDTO setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public String getContact() {
        return contact;
    }

    public SupplierDTO setContact(String contact) {
        this.contact = contact;
        return this;
    }

    public String getNote() {
        return note;
    }

    public SupplierDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
