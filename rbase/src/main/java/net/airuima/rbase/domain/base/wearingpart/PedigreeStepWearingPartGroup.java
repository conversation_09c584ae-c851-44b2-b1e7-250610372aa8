package net.airuima.rbase.domain.base.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "产品谱系工序易损件规则(PedigreeStepWearingPartGroup)", description = "产品谱系工序易损件规则表")
@Entity
@Table(name = "base_pedigree_step_wearing_part_group",uniqueConstraints = {@UniqueConstraint(name = "base_pedigree_step_wearing_part_group_unique",columnNames = {"pedigree_id","work_flow_id","step_id","group_id","work_cell_id","deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序易损件规则表")
@NamedEntityGraph(name = "pedigreeStepWearingPartGroupEntityGraph",
        attributeNodes = {@NamedAttributeNode("pedigree"),
                @NamedAttributeNode("workFlow"),
                @NamedAttributeNode("wearingPartGroup"),
                @NamedAttributeNode(value = "step",subgraph = "stepEntityGraph"),
                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "stepEntityGraph",
                        attributeNodes = {@NamedAttributeNode("stepGroup")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class PedigreeStepWearingPartGroup extends CustomBaseEntity {

    /**
     * 产品谱系
     */
    @Schema(description = "产品谱系")
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺流程
     */
    @Schema(description = "工艺流程")
    @ManyToOne
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序
     */
    @Schema(description = "工序")
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 易损件类型
     */
    @Schema(description = "易损件类型id")
    @ManyToOne
    @JoinColumn(name = "group_id")
    private WearingPartGroup wearingPartGroup;

    /**
     * 工位
     */
    @Schema(description = "工位")
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private int number;

    /**
     * 台位号/设备号
     */
    @Schema(description = "台位号/设备号")
    @Column(name = "platform_number")
    private String platformNumber;

    /**
     * 是否启用
     */
    @Column(name = "is_enable")
    @Schema(description = "是否启用")
    private Boolean isEnable;

    public PedigreeStepWearingPartGroup(){

    }

    public PedigreeStepWearingPartGroup(Long id){
        this.id = id;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepWearingPartGroup setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepWearingPartGroup setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepWearingPartGroup setStep(Step step) {
        this.step = step;
        return this;
    }

    public WearingPartGroup getWearingPartGroup() {
        return wearingPartGroup;
    }

    public PedigreeStepWearingPartGroup setWearingPartGroup(WearingPartGroup wearingPartGroup) {
        this.wearingPartGroup = wearingPartGroup;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public PedigreeStepWearingPartGroup setNumber(int number) {
        this.number = number;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public PedigreeStepWearingPartGroup setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public String getPlatformNumber() {
        return platformNumber;
    }

    public PedigreeStepWearingPartGroup setPlatformNumber(String platformNumber) {
        this.platformNumber = platformNumber;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeStepWearingPartGroup setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepWearingPartGroup pedigreeStepWearingPartGroup = (PedigreeStepWearingPartGroup) o;
        if (pedigreeStepWearingPartGroup.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepWearingPartGroup.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
