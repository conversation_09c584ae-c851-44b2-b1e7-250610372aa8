package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.constant.Constants;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.TeamDTO;
import net.airuima.rbase.util.NumberUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量工序生产详情实体Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "批量工序生产详情(BatchWorkDetail)", description = "批量工序生产详情实体")
@Entity
@Table(name = "procedure_batch_work_detail", uniqueConstraints = {@UniqueConstraint(name = "procedure_batch_work_detail_step_id_work_sheet_id_unique", columnNames = {"step_id", "work_sheet_id", "deleted"}),
        @UniqueConstraint(name = "procedure_batch_work_detail_step_id_sub_work_sheet_id_unique", columnNames = {"step_id", "sub_work_sheet_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "stepEntityGraph",
                        attributeNodes = {@NamedAttributeNode("stepGroup")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class BatchWorkDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    @Column(name = "input_number")
    private int inputNumber;

    /**
     * 工序实际完成数量
     */
    @Schema(description = "工序实际完成数量", required = true)
    @Column(name = "finish_number", nullable = false)
    private int finishNumber;

    /**
     * 工序合格数量
     */
    @Schema(description = "工序合格数量")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 工序不合格数量
     */
    @Schema(description = "工序不合格数量")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 待流转数量
     */
    @Schema(description = "待流转数量")
    @Column(name = "transfer_number")
    private int transferNumber;

    /**
     * 有效合格数
     */
    @Schema(description = "有效合格数")
    @Column(name = "effect_number")
    private double effectNumber;

    /**
     * 工时(工序完成耗时)
     */
    @Schema(description = "工时(工序完成耗时)")
    @Column(name = "work_hour")
    private double workHour;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 完成日期
     */
    @Schema(description = "完成日期")
    @Column(name = "end_date")
    private LocalDateTime endDate;

    /**
     * 是否完成(1:完成;0:未完成)
     */
    @Schema(description = "是否完成(1:完成;0:未完成)")
    @Column(name = "finish")
    private int finish;

    /**
     * 工序
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 班组ID
     */
    @Schema(description = "班组ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "team_id")
    private Long teamId;

    /**
     * 班组DTO
     */
    @FetchField(serviceName = "teamService", paramKey = "teamId")
    @Transient
    private TeamDTO teamDTO = new TeamDTO();

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 乐观锁版本
     */
    @Schema(description = "乐观锁版本",hidden = true)
    @Column(name = "version")
    private Long version;

    /**
     * 动态数据DTO
     */
    @Schema(description = "动态数据DTO")
    @Column(name = "dynamic_data")
    @Type(JsonType.class)
    private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO;

    /**
     * 来源于哪个工艺路线
     */
    @Transient
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workFlowId;

    /**
     * 合格率
     */
    @Transient
    @Schema(description = "合格率")
    private BigDecimal passRate;

    public Long getTeamId() {
        return teamId;
    }

    public BatchWorkDetail setTeamId(Long teamId) {
        this.teamId = teamId;
        return this;
    }

    public TeamDTO getTeamDTO() {
        return teamDTO;
    }

    public BatchWorkDetail setTeamDTO(TeamDTO teamDTO) {
        this.teamDTO = teamDTO;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public BatchWorkDetail setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public BatchWorkDetail setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public BatchWorkDetail setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public BatchWorkDetail setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getTransferNumber() {
        return transferNumber;
    }

    public BatchWorkDetail setTransferNumber(int transferNumber) {
        this.transferNumber = transferNumber;
        return this;
    }

    public double getEffectNumber() {
        return effectNumber;
    }

    public BatchWorkDetail setEffectNumber(double effectNumber) {
        this.effectNumber = effectNumber;
        return this;
    }

    public double getWorkHour() {
        return workHour;
    }

    public BatchWorkDetail setWorkHour(double workHour) {
        this.workHour = workHour;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public BatchWorkDetail setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public BatchWorkDetail setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public int getFinish() {
        return finish;
    }

    public BatchWorkDetail setFinish(int finish) {
        this.finish = finish;
        return this;
    }


    public Long getOperatorId() {
        return operatorId;
    }

    public BatchWorkDetail setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public BatchWorkDetail setStep(Step step) {
        this.step = step;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public BatchWorkDetail setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public BatchWorkDetail setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public BatchWorkDetail setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public BatchWorkDetail setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public BatchWorkDetail setVersion(Long version) {
        this.version = version;
        return this;
    }

    public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDTO() {
        return stepDynamicDataColumnGetDTO;
    }

    public BatchWorkDetail setStepDynamicDataColumnGetDTO(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO) {
        this.stepDynamicDataColumnGetDTO = stepDynamicDataColumnGetDTO;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public BatchWorkDetail setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public BigDecimal getPassRate() {
        return this.inputNumber == Constants.INT_ZERO ? new BigDecimal(Constants.INT_ZERO):  NumberUtils.divide(this.qualifiedNumber,this.inputNumber,Constants.INT_TWO);
    }

    public BatchWorkDetail setPassRate(BigDecimal passRate) {
        this.passRate = passRate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BatchWorkDetail batchWorkDetail = (BatchWorkDetail) o;
        if (batchWorkDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), batchWorkDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
