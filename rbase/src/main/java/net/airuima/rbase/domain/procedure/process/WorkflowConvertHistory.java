package net.airuima.rbase.domain.procedure.process;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺历史
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Schema(name = "转工艺历史(ConvertWorkflowHistory)", description = "转工艺历史")
@Entity
@Table(name = "procedure_workflow_convert_history")
@DynamicInsert
@DynamicUpdate
@NamedEntityGraph(name = "convertWorkflowHistoryEntityGraph",
        attributeNodes = {@NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")})
        }
)
public class WorkflowConvertHistory extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @Schema(description = "子工单")
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 旧工序
     */
    @Schema(description = "旧工序")
    @ManyToOne
    @JoinColumn(name = "origin_step_id")
    private Step originStep;

    /**
     * 新工序
     */
    @Schema(description = "新工序")
    @ManyToOne
    @JoinColumn(name = "convert_step_id")
    private Step convertStep;

    /**
     * 旧工艺路线
     */
    @Schema(description = "旧工艺路线")
    @ManyToOne
    @JoinColumn(name = "origin_work_flow_id")
    private WorkFlow originWorkFlow;

    /**
     * 新工艺路线
     */
    @Schema(description = "新工艺路线")
    @ManyToOne
    @JoinColumn(name = "convert_work_flow_id")
    private WorkFlow convertWorkFlow;

    /**
     * 转工艺时间
     */
    @Schema(description = "转工艺时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;


    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkflowConvertHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WorkflowConvertHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getOriginStep() {
        return originStep;
    }

    public WorkflowConvertHistory setOriginStep(Step originStep) {
        this.originStep = originStep;
        return this;
    }

    public Step getConvertStep() {
        return convertStep;
    }

    public WorkflowConvertHistory setConvertStep(Step convertStep) {
        this.convertStep = convertStep;
        return this;
    }

    public WorkFlow getOriginWorkFlow() {
        return originWorkFlow;
    }

    public WorkflowConvertHistory setOriginWorkFlow(WorkFlow originWorkFlow) {
        this.originWorkFlow = originWorkFlow;
        return this;
    }

    public WorkFlow getConvertWorkFlow() {
        return convertWorkFlow;
    }

    public WorkflowConvertHistory setConvertWorkFlow(WorkFlow convertWorkFlow) {
        this.convertWorkFlow = convertWorkFlow;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public WorkflowConvertHistory setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkflowConvertHistory instance = (WorkflowConvertHistory) o;
        if (instance.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), instance.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
