package net.airuima.rbase.domain.procedure.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Objects;


/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 序最新使用易损件Domain
 * <AUTHOR>
 */
@Schema(name = "工序最新使用易损件(LatestStepWearingPart)", description = "工序最新使用易损件表")
@Entity
@Table(name = "procedure_latest_step_wearing_part")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class LatestStepWearingPart extends CustomBaseEntity {

    /**
     * 工序易损件规则
     */
    @NotNull
    @Schema(description = "工序易损件规则ID")
    @ManyToOne
    @JoinColumn(name = "step_wearing_part_rule_id", nullable = false)
    private PedigreeStepWearingPartGroup pedigreeStepWearingPartGroup;


    /**
     * 易损件
     */
    @Schema(description = "易损件")
    @ManyToOne
    @JoinColumn(name = "wearing_part_id")
    private WearingPart wearingPart;

    public LatestStepWearingPart(){

    }

    public LatestStepWearingPart(PedigreeStepWearingPartGroup pedigreeStepWearingPartGroup,WearingPart wearingPart){
        this.pedigreeStepWearingPartGroup = pedigreeStepWearingPartGroup;
        this.wearingPart = wearingPart;
        this.deleted = Constants.LONG_ZERO;
    }
    public PedigreeStepWearingPartGroup getPedigreeStepWearingPartGroup() {
        return pedigreeStepWearingPartGroup;
    }

    public LatestStepWearingPart setPedigreeStepWearingPartGroup(PedigreeStepWearingPartGroup pedigreeStepWearingPartGroup) {
        this.pedigreeStepWearingPartGroup = pedigreeStepWearingPartGroup;
        return this;
    }

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public LatestStepWearingPart setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LatestStepWearingPart latestStepWearingPart = (LatestStepWearingPart) o;
        if (latestStepWearingPart.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), latestStepWearingPart.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
