package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良现象Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系工序不良现象(PedigreeStepUnqualifiedItem)", description = "产品谱系工序不良现象")
@Entity
@Table(name = "base_pedigree_step_unqualified_item", uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "work_flow_id","step_id", "client_id", "unqualified_item_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序不良现象数据")
@FetchEntity
@NamedEntityGraph(name = "pedigreeStepUnqualifiedItemEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step",subgraph = "stepEntityGraph"),
        @NamedAttributeNode(value = "unqualifiedItem",subgraph = "unqualifiedItemEntityGraph"),
        @NamedAttributeNode("priorityElementConfig")}, subgraphs = {
        @NamedSubgraph(name = "unqualifiedItemEntityGraph",
                attributeNodes = {@NamedAttributeNode("unqualifiedGroup")}),
        @NamedSubgraph(name = "stepEntityGraph",
                attributeNodes = {@NamedAttributeNode("stepGroup")})})
public class PedigreeStepUnqualifiedItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 不良项目
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良id")
    @JoinColumn(name = "unqualified_item_id", nullable = false)
    private UnqualifiedItem unqualifiedItem;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();


    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;


    /**
     * 条件优先级配置
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;


    public Long getClientId() {
        return clientId;
    }

    public PedigreeStepUnqualifiedItem setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeStepUnqualifiedItem setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeStepUnqualifiedItem setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepUnqualifiedItem setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepUnqualifiedItem setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepUnqualifiedItem setStep(Step step) {
        this.step = step;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public PedigreeStepUnqualifiedItem setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeStepUnqualifiedItem setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = (PedigreeStepUnqualifiedItem) o;
        if (pedigreeStepUnqualifiedItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepUnqualifiedItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
