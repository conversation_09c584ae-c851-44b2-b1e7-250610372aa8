
package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "产品谱系工序检测项目(PedigreeStepCheckItem)", description = "产品谱系工序检测项目")
@Entity
@Table(name = "base_pedigree_step_check_item")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序检测项目")
@FetchEntity
@NamedEntityGraph(name = "pedigreeStepCheckItemEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "pedigreeStepCheckRule", subgraph = "pedigreeStepCheckRuleEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "pedigreeStepCheckRuleEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree"),
                                @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class PedigreeStepCheckItem extends CustomBaseEntity implements Serializable {
    /**
     * 检测规则id
     */
    @ManyToOne
    @Schema(description = "检测规则id")
    @JoinColumn(name = "pedigree_step_check_rule_id")
    private PedigreeStepCheckRule pedigreeStepCheckRule;

    /**
     * 合格范围(开闭区间或者OK)
     */
    @NotNull
    @Schema(description = "合格范围(开闭区间或者OK)")
    @Column(name = "qualified_range", nullable = false)
    private String qualifiedRange;

    /**
     * 检验数据录入 0按抽检方案数 1 自定义抽检数量，2 自定义抽样方案
     */
    @Schema(description = "检验数据录入 0按抽检方案数 1 自定义抽检数量，2 自定义抽样方案")
    @Column(name = "inspect_number_case")
    private int inspectNumberCase;

    /**
     * 自定义检验项目的抽检数量
     */
    @Schema(description = "自定义检验项目的抽检数量")
    @Column(name = "customize_inspect_number")
    private int customizeInspectNumber = Constants.INT_ONE;

    /**
     * 抽样方案
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "抽样方案")
    @Column(name = "sample_case_id")
    private Long sampleCaseId;

    @FetchField(mapUri = "/api/sample-cases", serviceId = "mom", paramKey = "sampleCaseId")
    @Transient
    private SampleCaseDTO sampleCase = new SampleCaseDTO();

    /**
     * 检测项目
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "检测项目")
    @Column(name = "check_item_id")
    private Long checkItemId;

    @FetchField(mapUri = "/api/check-items", serviceId = "mom", paramKey = "checkItemId")
    @Transient
    private CheckItemDTO checkItem = new CheckItemDTO();

    /**
     * 是否管控其检查结果(0:不管控；1：管控)
     */
    @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
    @Column(name = "control", nullable = false)
    private boolean control;

    public int getInspectNumberCase() {
        return inspectNumberCase;
    }

    public PedigreeStepCheckItem setInspectNumberCase(int inspectNumberCase) {
        this.inspectNumberCase = inspectNumberCase;
        return this;
    }

    public PedigreeStepCheckRule getPedigreeStepCheckRule() {
        return pedigreeStepCheckRule;
    }

    public PedigreeStepCheckItem setPedigreeStepCheckRule(PedigreeStepCheckRule pedigreeStepCheckRule) {
        this.pedigreeStepCheckRule = pedigreeStepCheckRule;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public PedigreeStepCheckItem setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public Long getCheckItemId() {
        return checkItemId;
    }

    public PedigreeStepCheckItem setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public PedigreeStepCheckItem setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public boolean getControl() {
        return control;
    }

    public PedigreeStepCheckItem setControl(boolean control) {
        this.control = control;
        return this;
    }

    public int getCustomizeInspectNumber() {
        return customizeInspectNumber;
    }

    public PedigreeStepCheckItem setCustomizeInspectNumber(int customizeInspectNumber) {
        this.customizeInspectNumber = customizeInspectNumber;
        return this;
    }

    public Long getSampleCaseId() {
        return sampleCaseId;
    }

    public PedigreeStepCheckItem setSampleCaseId(Long sampleCaseId) {
        this.sampleCaseId = sampleCaseId;
        return this;
    }

    public SampleCaseDTO getSampleCase() {
        return sampleCase;
    }

    public PedigreeStepCheckItem setSampleCase(SampleCaseDTO sampleCase) {
        this.sampleCase = sampleCase;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepCheckItem pedigreeStepCheckItem = (PedigreeStepCheckItem) o;
        if (pedigreeStepCheckItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepCheckItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}

