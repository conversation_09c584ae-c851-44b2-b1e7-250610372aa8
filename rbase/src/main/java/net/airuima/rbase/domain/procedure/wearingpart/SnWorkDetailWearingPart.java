package net.airuima.rbase.domain.procedure.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "单支生产详情易损件(SnWorkDetailWearingPart)",description = "单支生产详情易损件表")
@Entity
@Table(name = "procedure_sn_work_detail_wearing_part",uniqueConstraints = @UniqueConstraint(name = "procedure_sn_work_detail_wearing_part_unique",columnNames = {"sn_work_detail_id","wearing_part_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@AuditEntity(value = "单支生产详情易损件表")
@NamedEntityGraph(name = "snWorkDetailWearingPartEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "snWorkDetail",subgraph = "snWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "snWorkDetailEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class SnWorkDetailWearingPart extends CustomBaseEntity {

    /**
     * SN工作详情
     */
    @NotNull
    @Schema(description = "SN工作详情")
    @ManyToOne
    @JoinColumn(name = "sn_work_detail_id",nullable = false)
    
    private SnWorkDetail snWorkDetail;

    /**
     * 易损件基础信息
     */
    @Schema(description = "易损件基础信息")
    @ManyToOne
    @JoinColumn(name = "wearing_part_id")
    private WearingPart wearingPart;

    /**
     * 次数
     */
    @Schema(description = "次数")
    @Column(name = "times")
    private int times;

    /**
     * 时长
     */
    @Schema(description = "时长")
    @Column(name = "duration")
    private int duration;

    public SnWorkDetail getSnWorkDetail() {
        return snWorkDetail;
    }

    public SnWorkDetailWearingPart setSnWorkDetail(SnWorkDetail snWorkDetail) {
        this.snWorkDetail = snWorkDetail;
        return this;
    }

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public SnWorkDetailWearingPart setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public SnWorkDetailWearingPart setTimes(int times) {
        this.times = times;
        return this;
    }

    public int getDuration() {
        return duration;
    }

    public SnWorkDetailWearingPart setDuration(int duration) {
        this.duration = duration;
        return this;
    }

}
