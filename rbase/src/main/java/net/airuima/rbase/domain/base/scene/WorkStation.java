package net.airuima.rbase.domain.base.scene;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工站Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工站(WorkStation)", description = "工站")
@Entity
@Table(name = "base_work_station", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "工站数据")
@NamedEntityGraph(name = "workStationEntityGraph",attributeNodes = {@NamedAttributeNode("workLine")})
public class WorkStation extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工站名称
     */
    @NotNull
    @Schema(description = "工站名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @NotNull
    @Schema(description = "编码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 工站顺序号
     */
    @Schema(description = "工站顺序号")
    @Column(name = "order_number")
    private int orderNumber;

    /**
     * 生产线id
     */
    @ManyToOne
    @Schema(description = "生产线id")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public WorkStation setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkStation setCode(String code) {
        this.code = code;
        return this;
    }

    public int getOrderNumber() {
        return orderNumber;
    }

    public WorkStation setOrderNumber(int orderNumber) {
        this.orderNumber = orderNumber;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WorkStation setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkStation setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkStation workStation = (WorkStation) o;
        if (workStation.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workStation.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
