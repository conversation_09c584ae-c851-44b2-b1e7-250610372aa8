package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 计划下单记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "计划下单记录")
@Entity
@Table(name = "rbase_procedure_planned_order")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
public class PlannedOrder extends CustomBaseEntity implements Serializable {

    /**
     * 销售订单详情
     */
    @Schema(description = "销售订单详情")
    @ManyToOne
    @JoinColumn(name = "sale_order_detail_id")
    private SaleOrderDetail saleOrderDetail;

    /**
     * 计划下单用户
     */
    @Schema(description = "计划下单用户")
    @Column(name = "plan_user_id")
    private Long planUserId;

    @Transient
    @FetchField(serviceName = "userService",paramKey ="planUserId")
    private UserDTO planUserDto = new UserDTO();

    /**
     * 派单用户
     */
    @Schema(description = "派单用户")
    @Column(name = "allocate_user_id")
    private Long allocateUserId;

    @Transient
    @FetchField(serviceName = "userService",paramKey ="allocateUserId")
    private UserDTO allocateUserDto = new UserDTO();

    /**
     * 计划订单待排状态：0待分配，1：已分配;2:已取消
     */
    @Schema(description = "计划订单待排状态：0待分配，1：已分配;2:已取消")
    @Column(name = "status")
    private int status;

    public SaleOrderDetail getSaleOrderDetail() {
        return saleOrderDetail;
    }

    public PlannedOrder setSaleOrderDetail(SaleOrderDetail saleOrderDetail) {
        this.saleOrderDetail = saleOrderDetail;
        return this;
    }

    public Long getPlanUserId() {
        return planUserId;
    }

    public PlannedOrder setPlanUserId(Long planUserId) {
        this.planUserId = planUserId;
        return this;
    }

    public UserDTO getPlanUserDto() {
        return planUserDto;
    }

    public PlannedOrder setPlanUserDto(UserDTO planUserDto) {
        this.planUserDto = planUserDto;
        return this;
    }

    public Long getAllocateUserId() {
        return allocateUserId;
    }

    public PlannedOrder setAllocateUserId(Long allocateUserId) {
        this.allocateUserId = allocateUserId;
        return this;
    }

    public UserDTO getAllocateUserDto() {
        return allocateUserDto;
    }

    public PlannedOrder setAllocateUserDto(UserDTO allocateUserDto) {
        this.allocateUserDto = allocateUserDto;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public PlannedOrder setStatus(int status) {
        this.status = status;
        return this;
    }
}
