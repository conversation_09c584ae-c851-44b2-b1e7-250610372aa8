package net.airuima.rbase.domain.procedure.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单工序生产在制统计Domain
 * <AUTHOR>
 * @date 2023/12/19
 */
@Schema(name = "工单工序生产在制统计", description = "工单工序生产在制统计")
@Entity
@Table(name = "procedure_work_sheet_step_statistics",uniqueConstraints = {@UniqueConstraint(name = "procedure_work_sheet_step_statistics_ws_unique", columnNames = {"work_sheet_id", "step_id", "deleted"}),
        @UniqueConstraint(name = "procedure_work_sheet_step_statistics_sub_ws_unique", columnNames = {"sub_work_sheet_id","step_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "workSheetStepStatisticsEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "pedigreeStepStatistics", subgraph = "pedigreeStepStatisticsEntityGraph"),
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "pedigreeStepStatisticsEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "stepEntityGraph",
                        attributeNodes = {@NamedAttributeNode("stepGroup")})})
public class WorkSheetStepStatistics extends CustomBaseEntity implements Serializable {


    /**
     * 产品谱系工序生产在制统计
     */
    @ManyToOne
    @Schema(description = "产品谱系工序生产在制统计")
    @JoinColumn(name = "pedigree_step_statistics_id")
    private PedigreeStepStatistics pedigreeStepStatistics;

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;


    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    @Column(name = "input_number")
    private int inputNumber;

    /**
     * 工序合格数量
     */
    @Schema(description = "工序合格数量")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 工序不合格数量
     */
    @Schema(description = "工序不合格数量")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 下交数量
     */
    @Schema(description = "下交数量")
    @Column(name = "transfer_number")
    private int transferNumber;

    /**
     * 在制数量
     */
    @Schema(description = "在制数量")
    @Column(name = "online_number")
    private int onlineNumber;

    public PedigreeStepStatistics getPedigreeStepStatistics() {
        return pedigreeStepStatistics;
    }

    public WorkSheetStepStatistics setPedigreeStepStatistics(PedigreeStepStatistics pedigreeStepStatistics) {
        this.pedigreeStepStatistics = pedigreeStepStatistics;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetStepStatistics setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WorkSheetStepStatistics setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkSheetStepStatistics setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public WorkSheetStepStatistics setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetStepStatistics setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkSheetStepStatistics setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getTransferNumber() {
        return transferNumber;
    }

    public WorkSheetStepStatistics setTransferNumber(int transferNumber) {
        this.transferNumber = transferNumber;
        return this;
    }

    public int getOnlineNumber() {
        return onlineNumber;
    }

    public WorkSheetStepStatistics setOnlineNumber(int onlineNumber) {
        this.onlineNumber = onlineNumber;
        return this;
    }
}
