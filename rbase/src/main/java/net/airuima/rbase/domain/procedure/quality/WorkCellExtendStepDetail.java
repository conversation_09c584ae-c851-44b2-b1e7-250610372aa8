package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工位宽放工位过站记录详情
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工位宽放工位过站记录详情")
@Entity
@Table(name = "procedure_work_cell_extend_step_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "工位宽放工位过站记录详情")
@FetchEntity
public class WorkCellExtendStepDetail extends CustomBaseEntity implements Serializable {

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "staff_id")
    private Long staffId;

    /**
     * 员工DTO
     */
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom", tableName = "staff", foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();


    /**
     * 子工单
     */
    @Schema(description = "子工单")
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 宽放触发工位
     */
    @Schema(description = "宽放触发工位")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "extend_work_cell_id")
    private WorkCell extendWorkCell;

    /**
     * 下交工位
     */
    @Schema(description = "下交工位")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;


    /**
     * 工序
     */
    @Schema(description = "工序")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工序详情id
     */
    @ManyToOne
    @Schema(description = "工序详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @JoinColumn(name = "batch_work_detail_id")
    private BatchWorkDetail batchWorkDetail;

    /**
     * 容器详情id
     */
    @ManyToOne
    @Schema(description = "容器详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @JoinColumn(name = "container_detail_id")
    private ContainerDetail containerDetail;

    /**
     * sn详情id
     */
    @ManyToOne
    @Schema(description = "sn详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @JoinColumn(name = "sn_work_detail_id")
    private SnWorkDetail snWorkDetail;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Column(name = "input_number")
    private int inputNumber;


    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Column(name = "qualified_number")
    private int qualifiedNumber;


    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    /**
     * 状态:0 未处理，1 已处理
     */
    @Schema(description = "状态:0 未处理，1 已处理")
    @Column(name = "status")
    private Boolean status;

    public Long getStaffId() {
        return staffId;
    }

    public WorkCellExtendStepDetail setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public WorkCellExtendStepDetail setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WorkCellExtendStepDetail setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkCellExtendStepDetail setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellExtendStepDetail setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkCellExtendStepDetail setStep(Step step) {
        this.step = step;
        return this;
    }

    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public WorkCellExtendStepDetail setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public WorkCellExtendStepDetail setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public SnWorkDetail getSnWorkDetail() {
        return snWorkDetail;
    }

    public WorkCellExtendStepDetail setSnWorkDetail(SnWorkDetail snWorkDetail) {
        this.snWorkDetail = snWorkDetail;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public WorkCellExtendStepDetail setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkCellExtendStepDetail setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkCellExtendStepDetail setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public WorkCellExtendStepDetail setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public Boolean getStatus() {
        return status;
    }

    public WorkCellExtendStepDetail setStatus(Boolean status) {
        this.status = status;
        return this;
    }

    public WorkCell getExtendWorkCell() {
        return extendWorkCell;
    }

    public WorkCellExtendStepDetail setExtendWorkCell(WorkCell extendWorkCell) {
        this.extendWorkCell = extendWorkCell;
        return this;
    }
}
