package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良品管理记录表Domain
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Schema(name = "不良品管理记录表(InspectUnqualified)", description = "不良品管理记录表")
@Entity
@Table(name = "procedure_inspect_unqualified")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@NamedEntityGraph(
        name = "inspectUnqualifiedEntityGraph",
        attributeNodes = {
                @NamedAttributeNode(value = "checkHistory", subgraph = "checkHistoryEntityGraph")
        },
        subgraphs = {
                @NamedSubgraph(name = "checkHistoryEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph", attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})
        }
)
public class InspectUnqualified extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不良品数量
     */
    @Schema(description = "不良品数量", required = true)
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 检测历史
     */
    @NotNull
    @Schema(description = "检测历史", required = true)
    @ManyToOne
    @JoinColumn(name = "check_history_id", nullable = false)
    private CheckHistory checkHistory;

    /**
     * 处理结果:0待处理，1维修分析
     */
    @Schema(description = "处理结果:0待处理，1维修分析", required = true)
    @Column(name = "deal_result", nullable = false)
    private int dealResult;

    /**
     * 处理人id
     */
    @Schema(description = "处理人id")
    @Column(name = "deal_staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealStaffId;

    /**
     * 处理人DTO
     */
    @Schema(description = "处理人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "dealStaffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "deal_staff_id")
    @Transient
    private StaffDTO dealStaffDto = new StaffDTO();

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    @Column(name = "deal_time")
    private LocalDateTime dealTime;


    public int getNumber() {
        return number;
    }

    public InspectUnqualified setNumber(int number) {
        this.number = number;
        return this;
    }

    public CheckHistory getCheckHistory() {
        return checkHistory;
    }

    public InspectUnqualified setCheckHistory(CheckHistory checkHistory) {
        this.checkHistory = checkHistory;
        return this;
    }

    public int getDealResult() {
        return dealResult;
    }

    public InspectUnqualified setDealResult(int dealResult) {
        this.dealResult = dealResult;
        return this;
    }

    public Long getDealStaffId() {
        return dealStaffId;
    }

    public InspectUnqualified setDealStaffId(Long dealStaffId) {
        this.dealStaffId = dealStaffId;
        return this;
    }

    public StaffDTO getDealStaffDto() {
        return dealStaffDto;
    }

    public InspectUnqualified setDealStaffDto(StaffDTO dealStaffDto) {
        this.dealStaffDto = dealStaffDto;
        return this;
    }

    public LocalDateTime getDealTime() {
        return dealTime;
    }

    public InspectUnqualified setDealTime(LocalDateTime dealTime) {
        this.dealTime = dealTime;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InspectUnqualified inspectUnqualified = (InspectUnqualified) o;
        if (inspectUnqualified.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), inspectUnqualified.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
