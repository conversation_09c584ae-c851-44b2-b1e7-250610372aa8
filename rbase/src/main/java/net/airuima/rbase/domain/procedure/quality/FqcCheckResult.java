package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "FQC检测结果表(FqcCheckResult)", description = "FQC检测结果表")
@Entity
@Table(name = "procedure_fqc_check_result", uniqueConstraints = @UniqueConstraint(name = "fqc_check_result_unique", columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "fqcCheckResultEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class FqcCheckResult extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @Schema(description = "编号")
    @Column(name = "code")
    private String code;

    /**
     * 抽检人ID
     */
    @Schema(description = "操作员工id")
    @Column(name = "check_staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkStaffId;

    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "checkStaffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "check_staff_id")
    @Transient
    private StaffDTO checkStaffDto = new StaffDTO();

    /**
     * 抽检工位
     */
    @ManyToOne
    @Schema(description = "抽检工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 子工单id
     */
    @ManyToOne
    @Schema(description = "子工单id")
    @JoinColumn(name = "sub_ws_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 原始子工单Id
     */
    @ManyToOne
    @Schema(description = "原始子工单Id")
    @JoinColumn(name = "original_sub_ws_id")
    private SubWorkSheet originalSubWorkSheet;

    /**
     * 工序id
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 抽检数
     */
    @Schema(description = "抽检数")
    @Column(name = "check_number")
    private double checkNumber;

    /**
     * 不良数
     */
    @Schema(description = "不良数")
    @Column(name = "unqualified_number")
    private double unqualifiedNumber;

    /**
     * 发生原因
     */
    @Schema(description = "发生原因")
    @Column(name = "reason")
    private String reason;

    /**
     * 处理方式(0:批退;1:放行)
     */
    @Schema(description = "处理方式(0:批退;1:放行)")
    @Column(name = "type")
    private int type;

    /**
     * 状态(0:未处理;1:已处理)
     */
    @Schema(description = "状态(0:未处理;1:已处理)")
    @Column(name = "status")
    private int status;

    /**
     * 处理人ID
     */
    @Schema(description = "处理人ID")
    @Column(name = "deal_staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealStaffId;

    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "dealStaffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "deal_staff_id")
    @Transient
    private StaffDTO dealStaffDto = new StaffDTO();

    /**
     * 处理途径(0工位处理,1网页处理)
     */
    @Schema(description = "处理途径(0工位处理,1网页处理)")
    @Column(name = "deal_way")
    private int dealWay;

    /**
     * 是否合格(0:否;1:是)
     */
    @Schema(description = "是否合格(0:否;1:是)")
    @Column(name = "result")
    private boolean result;

    /**
     * 是否锁总工单(0:否;1:是)
     */
    @Schema(description = "是否锁总工单(0:否;1:是)")
    @Column(name = "lock_ws")
    private boolean lockWs;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public String getCode() {
        return code;
    }

    public FqcCheckResult setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getCheckStaffId() {
        return checkStaffId;
    }

    public FqcCheckResult setCheckStaffId(Long checkStaffId) {
        this.checkStaffId = checkStaffId;
        return this;
    }

    public StaffDTO getCheckStaffDto() {
        return checkStaffDto;
    }

    public FqcCheckResult setCheckStaffDto(StaffDTO checkStaffDto) {
        this.checkStaffDto = checkStaffDto;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public FqcCheckResult setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public FqcCheckResult setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public SubWorkSheet getOriginalSubWorkSheet() {
        return originalSubWorkSheet;
    }

    public FqcCheckResult setOriginalSubWorkSheet(SubWorkSheet originalSubWorkSheet) {
        this.originalSubWorkSheet = originalSubWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public FqcCheckResult setStep(Step step) {
        this.step = step;
        return this;
    }

    public double getCheckNumber() {
        return checkNumber;
    }

    public FqcCheckResult setCheckNumber(double checkNumber) {
        this.checkNumber = checkNumber;
        return this;
    }

    public double getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public FqcCheckResult setUnqualifiedNumber(double unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public FqcCheckResult setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public int getType() {
        return type;
    }

    public FqcCheckResult setType(int type) {
        this.type = type;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public FqcCheckResult setStatus(int status) {
        this.status = status;
        return this;
    }

    public Long getDealStaffId() {
        return dealStaffId;
    }

    public FqcCheckResult setDealStaffId(Long dealStaffId) {
        this.dealStaffId = dealStaffId;
        return this;
    }

    public StaffDTO getDealStaffDto() {
        return dealStaffDto;
    }

    public FqcCheckResult setDealStaffDto(StaffDTO dealStaffDto) {
        this.dealStaffDto = dealStaffDto;
        return this;
    }

    public int getDealWay() {
        return dealWay;
    }

    public FqcCheckResult setDealWay(int dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public FqcCheckResult setResult(boolean result) {
        this.result = result;
        return this;
    }

    public boolean getLockWs() {
        return lockWs;
    }

    public FqcCheckResult setLockWs(boolean lockWs) {
        this.lockWs = lockWs;
        return this;
    }

    public String getNote() {
        return note;
    }

    public FqcCheckResult setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FqcCheckResult FqcCheckResult = (FqcCheckResult) o;
        if (FqcCheckResult.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), FqcCheckResult.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
