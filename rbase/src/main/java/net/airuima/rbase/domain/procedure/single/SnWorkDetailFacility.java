package net.airuima.rbase.domain.procedure.single;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情设备Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "单支生产详情设备(SnWorkDetailEquipment)", description = "单支生产详情设备实体")
@Entity
@Table(name = "procedure_sn_work_detail_facility", uniqueConstraints = @UniqueConstraint(name = "procedure_sn_work_detail_facility_unique_index",
        columnNames = {"sn_work_detail_id", "facility_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "snWorkDetailEquipmentEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "snWorkDetail",subgraph = "snWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "snWorkDetailEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class SnWorkDetailFacility extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SN工作详情
     */
    @NotNull
    @ManyToOne
    @Schema(description = "SN工作详情", required = true)
    @JoinColumn(name = "sn_work_detail_id", nullable = false)
    private SnWorkDetail snWorkDetail;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @Column(name = "facility_id")
    private Long facilityId;

    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    @Transient
    private FacilityDTO facilityDto = new FacilityDTO();


    public SnWorkDetail getSnWorkDetail() {
        return snWorkDetail;
    }

    public SnWorkDetailFacility setSnWorkDetail(SnWorkDetail snWorkDetail) {
        this.snWorkDetail = snWorkDetail;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public SnWorkDetailFacility setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public SnWorkDetailFacility setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnWorkDetailFacility snWorkDetailFacility = (SnWorkDetailFacility) o;
        if (snWorkDetailFacility.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snWorkDetailFacility.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
