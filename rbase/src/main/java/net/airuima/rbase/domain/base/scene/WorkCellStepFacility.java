package net.airuima.rbase.domain.base.scene;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序设备Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工位工序设备(WorkCellStepEquipment)", description = "工位工序设备实体")
@Entity
@Table(name = "base_work_cell_step_facility")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "工位工序设备数据")
@NamedEntityGraph(name = "workCellStepEquipmentEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph"),
        @NamedAttributeNode(value = "step",subgraph = "stepEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "stepEntityGraph",
                        attributeNodes = {@NamedAttributeNode("stepGroup")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
@FetchEntity
public class WorkCellStepFacility extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工位
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工位", required = true)
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @Column(name = "facility_id")
    private Long facilityId;

    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    @Transient
    private FacilityDTO facilityDto = new FacilityDTO();


    @Transient
    private Set<FacilityDTO> facilityDtos;

    public Long getFacilityId() {
        return facilityId;
    }

    public WorkCellStepFacility setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public WorkCellStepFacility() {

    }

    public WorkCellStepFacility(WorkCell workCell, Step step, Long facilityId) {
        this.workCell = workCell;
        this.step = step;
        this.facilityId = facilityId;
    }

    public WorkCellStepFacility(WorkCell workCell, Long facilityId) {
        this.workCell = workCell;
        this.facilityId = facilityId;
    }

    public WorkCellStepFacility(Long facilityId) {
        this.facilityId = facilityId;
    }

    public WorkCellStepFacility setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public Set<FacilityDTO> getFacilityDtos() {
        return facilityDtos;
    }

    public WorkCellStepFacility setFacilityDtos(Set<FacilityDTO> facilityDtos) {
        this.facilityDtos = facilityDtos;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellStepFacility setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkCellStepFacility setStep(Step step) {
        this.step = step;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkCellStepFacility workCellStepFacility = (WorkCellStepFacility) o;
        if (workCellStepFacility.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workCellStepFacility.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
