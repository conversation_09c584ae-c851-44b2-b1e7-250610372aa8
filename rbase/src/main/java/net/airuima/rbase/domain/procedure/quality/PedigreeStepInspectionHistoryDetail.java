package net.airuima.rbase.domain.procedure.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 工序检查配置历史详情处理表
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工序检查配置历史详情处理表", description = "工序检查配置历史详情处理表")
@Entity
@Table(name = "rbase_procedure_pedigree_step_inspection_history_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "产品谱系属性数据")
@FetchEntity
public class PedigreeStepInspectionHistoryDetail extends CustomBaseEntity implements Serializable {


    /**
     * 工序检查历史记录
     */
    @ManyToOne
    @JoinColumn(name = "pedigree_step_inspection_history_id")
    @Schema(description = "工序检查历史记录")
    private PedigreeStepInspectionHistory pedigreeStepInspectionHistory;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    @Column(name = "sn")
    private String sn;

    /**
     * Sn结果：0不合格，1合格 (可能为空容器没有合格不合格说法)
     */
    @Schema(description = "Sn结果：0不合格，1合格")
    @Column(name = "result")
    private Integer result;

    /**
     * 处理方式：0退回重做，1：允许下交
     */
    @Schema(description = "处理方式：0退回重做，1：允许下交")
    @Column(name = "handle_type")
    private Integer handleType;

    /**
     * 处理状态：0待处理,1已处理
     */
    @Schema(description = "0待处理,1已处理")
    @Column(name = "status")
    private Integer status;

    public PedigreeStepInspectionHistory getPedigreeStepInspectionHistory() {
        return pedigreeStepInspectionHistory;
    }

    public PedigreeStepInspectionHistoryDetail setPedigreeStepInspectionHistory(PedigreeStepInspectionHistory pedigreeStepInspectionHistory) {
        this.pedigreeStepInspectionHistory = pedigreeStepInspectionHistory;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public PedigreeStepInspectionHistoryDetail setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public PedigreeStepInspectionHistoryDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getResult() {
        return result;
    }

    public PedigreeStepInspectionHistoryDetail setResult(Integer result) {
        this.result = result;
        return this;
    }

    public Integer getHandleType() {
        return handleType;
    }

    public PedigreeStepInspectionHistoryDetail setHandleType(Integer handleType) {
        this.handleType = handleType;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public PedigreeStepInspectionHistoryDetail setStatus(Integer status) {
        this.status = status;
        return this;
    }
}
