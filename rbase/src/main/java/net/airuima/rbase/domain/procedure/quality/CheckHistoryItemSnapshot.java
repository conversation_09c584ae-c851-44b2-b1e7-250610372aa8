package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 检测历史条件详情记录表Domain
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Schema(name = "检测历史条件详情记录表(CheckHistoryItemSnapshot)", description = "检测历史条件详情记录表")
@Entity
@Table(name = "procedure_check_history_item_snapshot")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@NamedEntityGraph(name = "checkHistoryItemSnapshotEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "checkHistory",subgraph = "checkHistoryEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "checkHistoryEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
@FetchEntity
public class CheckHistoryItemSnapshot extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 首检巡检历史
     */
    @Schema(description = "首检巡检历史")
    @ManyToOne
    @JoinColumn(name = "check_history_id")
    private CheckHistory checkHistory;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO variety = new VarietyDTO();

    /**
     * 检测项目
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "检测项目")
    @Column(name = "check_item_id")
    private Long checkItemId;

    @FetchField(mapUri = "/api/check-items", serviceId = "mom", paramKey = "checkItemId")
    @Transient
    private CheckItemDTO checkItem = new CheckItemDTO();

    /**
     * 检验方法:目测0/检测仪器1
     */
    @Schema(description = "检验方法:目测0/检测仪器1")
    @Column(name = "check_way")
    private int checkWay;

    /**
     * 分析方法:定性0/定量1
     */
    @Schema(description = "分析方法:定性0/定量1")
    @Column(name = "analyse_way")
    private int analyseWay;

    /**
     * 检测仪器
     */
    @Schema(description = "检测仪器")
    @Column(name = "facility")
    private String facility;

    /**
     * 合格范围(数学开闭区间或者OK)
     */
    @NotNull
    @Schema(description = "合格范围(数学开闭区间或者OK)", required = true)
    @Column(name = "qualified_range", nullable = false)
    private String qualifiedRange;


    public CheckHistory getCheckHistory() {
        return checkHistory;
    }

    public CheckHistoryItemSnapshot setCheckHistory(CheckHistory checkHistory) {
        this.checkHistory = checkHistory;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public CheckHistoryItemSnapshot setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVariety() {
        return variety;
    }

    public CheckHistoryItemSnapshot setVariety(VarietyDTO variety) {
        this.variety = variety;
        return this;
    }

    public Long getCheckItemId() {
        return checkItemId;
    }

    public CheckHistoryItemSnapshot setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public CheckHistoryItemSnapshot setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public int getCheckWay() {
        return checkWay;
    }

    public CheckHistoryItemSnapshot setCheckWay(int checkWay) {
        this.checkWay = checkWay;
        return this;
    }

    public int getAnalyseWay() {
        return analyseWay;
    }

    public CheckHistoryItemSnapshot setAnalyseWay(int analyseWay) {
        this.analyseWay = analyseWay;
        return this;
    }

    public String getFacility() {
        return facility;
    }

    public CheckHistoryItemSnapshot setFacility(String facility) {
        this.facility = facility;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public CheckHistoryItemSnapshot setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CheckHistoryItemSnapshot checkHistoryItemSnapshot = (CheckHistoryItemSnapshot) o;
        if (checkHistoryItemSnapshot.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), checkHistoryItemSnapshot.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
