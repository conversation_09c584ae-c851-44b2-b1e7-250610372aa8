package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 产品谱系工序FPP配置Domain
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工序FPP配置Domain", description = "工序FPP配置Domain")
@Entity
@Table(name = "base_pedigree_step_fpp_config")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "工序FPP配置数据")
public class PedigreeStepFppConfig extends CustomBaseEntity implements Serializable {

    /**
     * 产品谱系工序配置
     */
    @OneToOne
    @Schema(description = "产品谱系工序配置")
    @JoinColumn(name = "pedigree_step_id")
    @JsonIgnoreProperties({"pedigreeStepFppConfig"})
    private PedigreeStep pedigreeStep;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Column(name = "enable")
    private Boolean enable;

    /**
     * 最大测试次数(0表示)
     */
    @Schema(description = "最大测试次数")
    @Column(name = "max_test_times")
    private Integer maxTestTimes;


    /**
     * 不合格后连续合格多少次算合格
     */
    @Schema(description = "不合格后连续合格多少次算合格")
    @Column(name = "consecutive_pass_times")
    private Integer consecutivePassTimes;

    public PedigreeStep getPedigreeStep() {
        return pedigreeStep;
    }

    public PedigreeStepFppConfig setPedigreeStep(PedigreeStep pedigreeStep) {
        this.pedigreeStep = pedigreeStep;
        return this;
    }

    public Boolean getEnable() {
        return enable;
    }

    public PedigreeStepFppConfig setEnable(Boolean enable) {
        this.enable = enable;
        return this;
    }

    public Integer getMaxTestTimes() {
        return maxTestTimes;
    }

    public PedigreeStepFppConfig setMaxTestTimes(Integer maxTestTimes) {
        this.maxTestTimes = maxTestTimes;
        return this;
    }

    public Integer getConsecutivePassTimes() {
        return consecutivePassTimes;
    }

    public PedigreeStepFppConfig setConsecutivePassTimes(Integer consecutivePassTimes) {
        this.consecutivePassTimes = consecutivePassTimes;
        return this;
    }
}
