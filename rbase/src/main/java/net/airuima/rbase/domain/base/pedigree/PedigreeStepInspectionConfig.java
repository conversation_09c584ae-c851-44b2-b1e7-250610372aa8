package net.airuima.rbase.domain.base.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 产品谱系工序配置-检查配置
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工序检查配置表", description = "工序检查配置表")
@Entity
@Table(name = "rbase_base_pedigree_step_inspection_config")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "工序检查配置数据")
public class PedigreeStepInspectionConfig extends CustomBaseEntity implements Serializable {

    /**
     * 产品谱系工序配置
     */
    @ManyToOne
    @Schema(description = "产品谱系工序配置")
    @JoinColumn(name = "pedigree_step_id")
    private PedigreeStep pedigreeStep;

    /**
     * 检查类型(0:质量检查;1:设备检查)
     */
    @Schema(description = "检查类型(0:质量检查;1:设备检查)")
    @Column(name = "inspection_type")
    private Integer inspectionType;

    /**
     * NG处理方式(0:返工处理;1:重新调试)
     */
    @Schema(description = "NG处理方式(0:返工处理;1:重新调试)")
    @Column(name = "handling_type")
    private Integer handlingType;

    /**
     * 最大NG次数
     */
    @Schema(description = "最大NG次数")
    @Column(name = "max_ng_count")
    private Integer maxNgCount;

    /**
     * 超次策略 0:人工确认放行
     */
    @Schema(description = "超次策略 0:人工确认放行")
    @Column(name = "override_strategy")
    private Integer overrideStrategy;

    public PedigreeStepInspectionConfig() {
    }

    public PedigreeStep getPedigreeStep() {
        return pedigreeStep;
    }

    public PedigreeStepInspectionConfig setPedigreeStep(PedigreeStep pedigreeStep) {
        this.pedigreeStep = pedigreeStep;
        return this;
    }

    public Integer getInspectionType() {
        return inspectionType;
    }

    public PedigreeStepInspectionConfig setInspectionType(Integer inspectionType) {
        this.inspectionType = inspectionType;
        return this;
    }

    public Integer getHandlingType() {
        return handlingType;
    }

    public PedigreeStepInspectionConfig setHandlingType(Integer handlingType) {
        this.handlingType = handlingType;
        return this;
    }

    public Integer getMaxNgCount() {
        return maxNgCount;
    }

    public PedigreeStepInspectionConfig setMaxNgCount(Integer maxNgCount) {
        this.maxNgCount = maxNgCount;
        return this;
    }

    public Integer getOverrideStrategy() {
        return overrideStrategy;
    }

    public PedigreeStepInspectionConfig setOverrideStrategy(Integer overrideStrategy) {
        this.overrideStrategy = overrideStrategy;
        return this;
    }
}
