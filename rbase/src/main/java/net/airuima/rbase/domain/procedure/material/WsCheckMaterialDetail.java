package net.airuima.rbase.domain.procedure.material;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料明细表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "工单核料明细表(WsCheckMaterialDetail)", description = "工单核料明细表")
@Entity
@Table(name = "procedure_ws_check_material_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "wsCheckMaterialDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})})
public class WsCheckMaterialDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单核料Id
     */
    @ManyToOne
    @Schema(description = "工单核料Id")
    @JoinColumn(name = "ws_check_material_id")
    private WsCheckMaterial wsCheckMaterial;

    /**
     * 总工单id
     */
    @ManyToOne
    @Schema(description = "总工单id")
    @JoinColumn(name = "ws_id")
    private WorkSheet workSheet;

    /**
     * 物料Id
     */
    @Schema(description = "物料Id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @Column(name = "batch")
    private String batch;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 已核料数量
     */
    @Schema(description = "已核料数量")
    @Column(name = "checked_number")
    private double checkedNumber;

    /**
     * 未核料数量
     */
    @Schema(description = "未核料数量")
    @Column(name = "uncheck_number")
    private double uncheckNumber;

    public WsCheckMaterial getWsCheckMaterial() {
        return wsCheckMaterial;
    }

    public WsCheckMaterialDetail setWsCheckMaterial(WsCheckMaterial wsCheckMaterial) {
        this.wsCheckMaterial = wsCheckMaterial;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsCheckMaterialDetail setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsCheckMaterialDetail setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsCheckMaterialDetail setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WsCheckMaterialDetail setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public WsCheckMaterialDetail setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public WsCheckMaterialDetail setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public double getCheckedNumber() {
        return checkedNumber;
    }

    public WsCheckMaterialDetail setCheckedNumber(double checkedNumber) {
        this.checkedNumber = checkedNumber;
        return this;
    }

    public double getUncheckNumber() {
        return uncheckNumber;
    }

    public WsCheckMaterialDetail setUncheckNumber(double uncheckNumber) {
        this.uncheckNumber = uncheckNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsCheckMaterialDetail WsCheckMaterialDetail = (WsCheckMaterialDetail) o;
        if (WsCheckMaterialDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), WsCheckMaterialDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
