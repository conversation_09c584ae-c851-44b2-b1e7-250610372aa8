package net.airuima.rbase.domain.procedure.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Schema(name = "员工不良明细(StaffPerformUnqualified)", description = "员工不良明细")
@Entity
@Table(name = "procedure_staff_perform_unqualified_item")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "staffPerformUnqualifiedItemEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "staffPerform",subgraph = "staffPerformEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "staffPerformEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class StaffPerformUnqualifiedItem extends CustomBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工产量
     */
    @Schema(description = "员工产量")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "staff_perform_id")
    private StaffPerform staffPerform;

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 不良数量
     */
    @Schema(description = "不良数量")
    @Column(name = "number")
    private int number;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;
    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    public StaffPerform getStaffPerform() {
        return staffPerform;
    }

    public StaffPerformUnqualifiedItem setStaffPerform(StaffPerform staffPerform) {
        this.staffPerform = staffPerform;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public StaffPerformUnqualifiedItem setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public StaffPerformUnqualifiedItem setNumber(int number) {
        this.number = number;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public StaffPerformUnqualifiedItem setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public StaffPerformUnqualifiedItem setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }
}
