package net.airuima.rbase.domain.base.process;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程框图Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工艺流程框图(WorkFlow)", description = "工艺流程框图实体")
@Entity
@Table(name = "base_work_flow", uniqueConstraints = @UniqueConstraint(name = "base_work_flow_unique", columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "工艺流程框图数据")
public class WorkFlow extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 框图编码
     */
    @Schema(description = "框图编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    /**
     * 流程框图类型(0:正常生产流程;1:返在线修流程;2:离线返修流程)
     */
    @Schema(description = "流程框图类型(0:正常生产流程;1:在线返修流程;2:离线返修流程)", required = true)
    @Column(name = "category", nullable = false)
    private int category;

    public WorkFlow() {
    }

    public WorkFlow(Long id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }

    public WorkFlow setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkFlow setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkFlow setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkFlow setCategory(int category) {
        this.category = category;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkFlow workFlow = (WorkFlow) o;
        if (workFlow.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workFlow.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
