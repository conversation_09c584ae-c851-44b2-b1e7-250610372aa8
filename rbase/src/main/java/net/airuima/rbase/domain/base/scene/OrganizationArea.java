package net.airuima.rbase.domain.base.scene;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 部门区域表Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "部门区域表(OrganizationArea)", description = "部门区域表")
@Entity
@Table(name = "base_organization_area", uniqueConstraints = {
        @UniqueConstraint(name = "base_organization_area_unique", columnNames = {"code", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "部门区域数据")
public class OrganizationArea extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", required = true)
    @Column(name = "organization_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private long organizationId;

    @Transient
    @Schema(description = "部门DTO")
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 区域编码
     */
    @NotNull
    @Schema(description = "区域编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 区域名称
     */
    @NotNull
    @Schema(description = "区域名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    public long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(long organizationId) {
        this.organizationId = organizationId;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public void setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
    }

    public String getCode() {
        return code;
    }

    public OrganizationArea setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public OrganizationArea setName(String name) {
        this.name = name;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrganizationArea organizationArea = (OrganizationArea) o;
        if (organizationArea.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), organizationArea.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
