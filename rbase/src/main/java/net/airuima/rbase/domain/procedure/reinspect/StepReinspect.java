package net.airuima.rbase.domain.procedure.reinspect;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检Domain
 * <AUTHOR>
 */

@Schema(name = "工序不良复检(StepReinspect)", description = "工序不良复检")
@Entity
@Table(name = "procedure_step_reinspect")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "stepReinspectEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")})})
public class StepReinspect extends CustomBaseEntity implements Serializable {

    /**
     * 不良复检序号
     */
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 工单
     */
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    @Schema(description = "工单")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 容器详情
     */
    @ManyToOne
    @JoinColumn(name = "container_detail_id")
    @Schema(description = "容器详情")
    private ContainerDetail containerDetail;

    /**
     * sn生产状态
     */
    @ManyToOne
    @JoinColumn(name = "sn_work_status_id")
    @Schema(description = "sn生产状态")
    private SnWorkStatus snWorkStatus;

    /**
     * 不良产生工位
     */
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    @Schema(description = "不良产生工位")
    private WorkCell workCell;

    /**
     * 不良产生工序
     */
    @ManyToOne
    @JoinColumn(name = "step_id")
    @Schema(description = "不良产生工序")
    private Step step;

    /**
     * 原始不良项目
     */
    @ManyToOne
    @JoinColumn(name = "origin_unqualified_item_id")
    @Schema(description = "原始不良项目")
    private UnqualifiedItem originUnqualifiedItem;

    /**
     * 不良数量
     */
    @Column(name = "number")
    @Schema(description = "不良数量")
    private int number;

    /**
     * 发起人ID
     */
    @Schema(description = "发起人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "sponsor_id", nullable = false)
    private Long sponsorId;

    /**
     * 发起人DTO
     */
    @Schema(description = "发起人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "sponsorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "sponsor_id")
    @Transient
    private StaffDTO sponsorDto = new StaffDTO();

    /**
     * 处理人ID
     */
    @Schema(description = "处理人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "processor_id", nullable = true)
    private Long processorId;

    /**
     * 处理人DTO
     */
    @Schema(description = "处理人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "processorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "processor_id")
    @Transient
    private StaffDTO processorDto = new StaffDTO();

    /**
     * 发起时间
     */
    @Schema(description = "发起时间")
    @Column(name = "sponsor_date")
    private LocalDateTime sponsorDate;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    @Column(name = "process_date")
    private LocalDateTime processDate;

    /**
     * 处理状态(false:待处理;true:已处理)
     */
    @Schema(description = "处理状态(false:待处理;true:已处理)")
    @Column(name = "status")
    private boolean status;

    /**
     * 是否为最后一个工序
     */
    @Schema(description = "是否为最后一个工序")
    @Column(name = "last_step")
    private boolean lastStep;

    public String getSerialNumber() {
        return serialNumber;
    }

    public StepReinspect setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public StepReinspect setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public StepReinspect setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public StepReinspect setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public SnWorkStatus getSnWorkStatus() {
        return snWorkStatus;
    }

    public StepReinspect setSnWorkStatus(SnWorkStatus snWorkStatus) {
        this.snWorkStatus = snWorkStatus;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public StepReinspect setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StepReinspect setStep(Step step) {
        this.step = step;
        return this;
    }

    public UnqualifiedItem getOriginUnqualifiedItem() {
        return originUnqualifiedItem;
    }

    public StepReinspect setOriginUnqualifiedItem(UnqualifiedItem originUnqualifiedItem) {
        this.originUnqualifiedItem = originUnqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public StepReinspect setNumber(int number) {
        this.number = number;
        return this;
    }

    public Long getSponsorId() {
        return sponsorId;
    }

    public StepReinspect setSponsorId(Long sponsorId) {
        this.sponsorId = sponsorId;
        return this;
    }

    public StaffDTO getSponsorDto() {
        return sponsorDto;
    }

    public StepReinspect setSponsorDto(StaffDTO sponsorDto) {
        this.sponsorDto = sponsorDto;
        return this;
    }

    public Long getProcessorId() {
        return processorId;
    }

    public StepReinspect setProcessorId(Long processorId) {
        this.processorId = processorId;
        return this;
    }

    public StaffDTO getProcessorDto() {
        return processorDto;
    }

    public StepReinspect setProcessorDto(StaffDTO processorDto) {
        this.processorDto = processorDto;
        return this;
    }

    public LocalDateTime getSponsorDate() {
        return sponsorDate;
    }

    public StepReinspect setSponsorDate(LocalDateTime sponsorDate) {
        this.sponsorDate = sponsorDate;
        return this;
    }

    public LocalDateTime getProcessDate() {
        return processDate;
    }

    public StepReinspect setProcessDate(LocalDateTime processDate) {
        this.processDate = processDate;
        return this;
    }

    public boolean isStatus() {
        return status;
    }

    public StepReinspect setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public boolean getLastStep() {
        return lastStep;
    }

    public StepReinspect setLastStep(boolean lastStep) {
        this.lastStep = lastStep;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepReinspect stepReinspect = (StepReinspect) o;
        if (stepReinspect.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepReinspect.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }


}
