package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.qms.VarietyDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工位宽放历史
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工位宽放历史")
@Entity
@Table(name = "procedure_work_cell_extend_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "工位宽放历史")
@FetchEntity
public class WorkCellExtendHistory extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 检测类型
     */
    @Schema(description = "检测类型(首检0/巡检1)")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO varietyDto = new VarietyDTO();

    /**
     * 延长时间(H)
     */
    @Schema(description = "延长时间(H)")
    @Column(name = "extend_time")
    private double extendTime;

    /**
     * 0：允许后续工序生产,1：限制当前工序生产，2：允许指定类型后续工序生产
     */
    @Schema(description = "0：允许后续工序生产,1：限制当前工序生产，2：允许指定类型后续工序生产")
    @Column(name = "extend_rule")
    private int extendRule;

    /**
     * 指定宽放工序类型：0,1,2,3
     */
    @Schema(description = "指定宽放工序类型：0,1,2,3")
    @Column(name = "extend_step_category")
    private String extendStepCategory;

    /**
     * 结果(0:未检测或者检测未通过;1:已检测且通过)
     */
    @Schema(description = "结果(0:未检测或者检测未通过;1:已检测且通过)")
    @Column(name = "result")
    private Boolean result;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @Column(name = "end_time")
    private LocalDateTime endTime;

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellExtendHistory setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCellExtendHistory setCategory(int category) {
        this.category = category;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public WorkCellExtendHistory setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVarietyDto() {
        return varietyDto;
    }

    public WorkCellExtendHistory setVarietyDto(VarietyDTO varietyDto) {
        this.varietyDto = varietyDto;
        return this;
    }

    public double getExtendTime() {
        return extendTime;
    }

    public WorkCellExtendHistory setExtendTime(double extendTime) {
        this.extendTime = extendTime;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public WorkCellExtendHistory setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public WorkCellExtendHistory setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public WorkCellExtendHistory setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkCellExtendHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WorkCellExtendHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public String getExtendStepCategory() {
        return extendStepCategory;
    }

    public WorkCellExtendHistory setExtendStepCategory(String extendStepCategory) {
        this.extendStepCategory = extendStepCategory;
        return this;
    }

    public int getExtendRule() {
        return extendRule;
    }

    public WorkCellExtendHistory setExtendRule(int extendRule) {
        this.extendRule = extendRule;
        return this;
    }
}
