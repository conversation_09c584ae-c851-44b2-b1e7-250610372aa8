package net.airuima.rbase.domain.base.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良原因Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "不良原因(UnqualifiedCause)", description = "不良原因")
@Entity
@Table(name = "base_unqualified_cause", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "不良原因数据")
public class UnqualifiedCause extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @NotNull
    @Schema(description = "名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @NotNull
    @Schema(description = "编码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public UnqualifiedCause setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedCause setCode(String code) {
        this.code = code;
        return this;
    }

    public String getNote() {
        return note;
    }

    public UnqualifiedCause setNote(String note) {
        this.note = note;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public UnqualifiedCause setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedCause unqualifiedCause = (UnqualifiedCause) o;
        if (unqualifiedCause.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), unqualifiedCause.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
