package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.pedigree.SnRuleDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序上料规则Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系工序上料规则(PedigreeStepMaterialRule)", description = "产品谱系工序上料规则")
@Entity
@Table(name = "base_pedigree_step_material_rule", uniqueConstraints = @UniqueConstraint(columnNames = {"client_id", "pedigree_id", "work_flow_id", "step_id", "material_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "产品谱系工序上料规则数据")
@NamedEntityGraph(name = "pedigreeStepMaterialRuleEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode("priorityElementConfig"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph",attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
public class PedigreeStepMaterialRule extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 产品谱系ID
     */
    @ManyToOne
    @Schema(description = "产品谱系ID")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;


    /**
     * 工序ID
     */
    @ManyToOne
    @Schema(description = "工序ID")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 物料id
     */
    @NotNull
    @Schema(description = "物料id")
    @Column(name = "material_id")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long materialId;

    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 扣料比例
     */
    @Schema(description = "扣料比例")
    @Column(name = "proportion")
    private double proportion;

    /**
     * 是否扣数(1:扣数;0:不扣数)
     */
    @Schema(description = "是否扣数(1:扣数;0:不扣数)")
    @Column(name = "is_deduct")
    private boolean isDeduct;

    /**
     * 是否核物料(1:核对物料编码;0:不核对物料编码)
     */
    @Schema(description = "是否核物料(1:核对物料编码;0:不核对物料编码)")
    @Column(name = "is_check_material")
    private boolean isCheckMaterial;

    /**
     * 是否核物料批次(1:核对物料批次;0:不核对物料批次)
     */
    @Schema(description = "是否核物料批次(1:核对物料批次;0:不核对物料批次)")
    @Column(name = "is_check_material_batch")
    private boolean isCheckMaterialBatch;

    @Schema(description = "物料管控粒度(0:单只序列号;1:批次号)")
    @Column(name = "control_material_granularity")
    private int controlMaterialGranularity;

    @Schema(description = "批次号/序列号规则（正则表达式方式）")
    @Column(name = "serial_number_rule")
    @Type(JsonType.class)
    private List<List<SnRuleDTO>> serialNumberRule;

    @Schema(description = "定义需要扫码多少次序列号，可不管控或指定数量（当管控粒度为单只，进行管控，为0不进行管控）")
    @Column(name = "control_sn_count")
    private int controlSnCount;


    /**
     * 是否启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "enable", nullable = false)
    private Boolean enable;


    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    public Boolean getEnable() {
        return enable;
    }

    public PedigreeStepMaterialRule setEnable(Boolean enable) {
        this.enable = enable;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public PedigreeStepMaterialRule setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeStepMaterialRule setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeStepMaterialRule setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepMaterialRule setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepMaterialRule setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepMaterialRule setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public PedigreeStepMaterialRule setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public PedigreeStepMaterialRule setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public double getProportion() {
        return proportion;
    }

    public PedigreeStepMaterialRule setProportion(double proportion) {
        this.proportion = proportion;
        return this;
    }

    public boolean getIsDeduct() {
        return isDeduct;
    }

    public PedigreeStepMaterialRule setIsDeduct(boolean isDeduct) {
        this.isDeduct = isDeduct;
        return this;
    }

    public boolean getIsCheckMaterial() {
        return isCheckMaterial;
    }

    public PedigreeStepMaterialRule setIsCheckMaterial(boolean isCheckMaterial) {
        this.isCheckMaterial = isCheckMaterial;
        return this;
    }

    public boolean getIsCheckMaterialBatch() {
        return isCheckMaterialBatch;
    }

    public PedigreeStepMaterialRule setIsCheckMaterialBatch(boolean isCheckMaterialBatch) {
        this.isCheckMaterialBatch = isCheckMaterialBatch;
        return this;
    }

    public int getControlMaterialGranularity() {
        return controlMaterialGranularity;
    }

    public PedigreeStepMaterialRule setControlMaterialGranularity(int controlMaterialGranularity) {
        this.controlMaterialGranularity = controlMaterialGranularity;
        return this;
    }

    public List<List<SnRuleDTO>> getSerialNumberRule() {
        return serialNumberRule;
    }

    public PedigreeStepMaterialRule setSerialNumberRules(List<List<SnRuleDTO>> serialNumberRules) {
        this.serialNumberRule = serialNumberRules;
        return this;
    }

    public int getControlSnCount() {
        return controlSnCount;
    }

    public PedigreeStepMaterialRule setControlSnCount(int controlSnCount) {
        this.controlSnCount = controlSnCount;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepMaterialRule pedigreeStepMaterialRule = (PedigreeStepMaterialRule) o;
        if (pedigreeStepMaterialRule.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepMaterialRule.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }


}
