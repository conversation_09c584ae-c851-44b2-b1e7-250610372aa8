package net.airuima.rbase.domain.procedure.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果明细表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "FQC检测结果明细表(FqcCheckResultDetail)", description = "FQC检测结果明细表")
@Entity
@Table(name = "procedure_fqc_check_result_detail", uniqueConstraints = @UniqueConstraint(name = "fqc_check_result_detail_unique", columnNames = {"fqc_check_result_id", "sn", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "fqcCheckResultDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "fqcCheckResult",subgraph = "fqcCheckResultEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "fqcCheckResultEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class FqcCheckResultDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * sn
     */
    @Schema(description = "sn")
    @Column(name = "sn")
    private String sn;

    /**
     * 是否为抽检SN
     */
    @Schema(description = "是否为抽检SN")
    @Column(name = "is_check")
    private boolean isCheck;

    /**
     * 是否合格(0:否;1:是)
     */
    @Schema(description = "是否合格(0:否;1:是)")
    @Column(name = "result")
    private boolean result;

    /**
     * fqc检测结果id
     */
    @ManyToOne
    @Schema(description = "fqc检测结果id")
    @JoinColumn(name = "fqc_check_result_id")
    private FqcCheckResult fqcCheckResult;

    /**
     * 不良现象
     */
    @ManyToOne
    @Schema(description = "不良现象")
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    public String getSn() {
        return sn;
    }

    public FqcCheckResultDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public FqcCheckResultDetail setCheck(boolean check) {
        isCheck = check;
        return this;
    }

    public boolean isResult() {
        return result;
    }

    public FqcCheckResultDetail setResult(boolean result) {
        this.result = result;
        return this;
    }

    public FqcCheckResult getFqcCheckResult() {
        return fqcCheckResult;
    }

    public FqcCheckResultDetail setFqcCheckResult(FqcCheckResult fqcCheckResult) {
        this.fqcCheckResult = fqcCheckResult;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public FqcCheckResultDetail setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FqcCheckResultDetail FqcCheckResultDetail = (FqcCheckResultDetail) o;
        if (FqcCheckResultDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), FqcCheckResultDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
