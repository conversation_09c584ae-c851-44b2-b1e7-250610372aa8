package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工单定制工序Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "生产工单定制工序(WsStep)", description = "生产工单定制工序实体")
@Entity
@Table(name = "procedure_ws_step", uniqueConstraints = {@UniqueConstraint(name = "procedure_ws_step_work_sheet_workflow_step_unique",
        columnNames = {"work_sheet_id","step_id", "deleted"}), @UniqueConstraint(name = "procedure_ws_step_sub_work_sheet_workflow_step_unique",
        columnNames = {"sub_work_sheet_id", "step_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wsStepEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "stepEntityGraph",
                        attributeNodes = {@NamedAttributeNode("stepGroup")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")})})
public class WsStep extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 工艺路线
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;


    /**
     * 前置工序列表，分号隔开
     */
    @Schema(description = "前置工序列表，分号隔开")
    @Column(name = "pre_step_id")
    private String preStepId;

    /**
     * 后置工序列表，分号隔开
     */
    @Schema(description = "后置工序列表，分号隔开")
    @Column(name = "after_step_id")
    private String afterStepId;

    /**
     * 工序id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序id", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 流转方式(0:连续流转;1:独立流转;2:分拆流转)
     */
    @Schema(description = "流转方式(0:连续流转;1:独立流转;2:分拆流转)")
    @Column(name = "transfer_type")
    private int transferType;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    @Column(name = "category")
    private int category;

    /**
     * 请求模式
     */
    @Schema(description = "请求模式(0:工单;1,容器;2,单支)")
    @Column(name = "request_mode")
    private int requestMode;

    /**
     * 报工方式(0:员工报工;1:班组报工)
     */
    @Schema(description = "报工方式(0:员工报工;1:班组报工)")
    @Column(name = "request_method")
    private int requestMethod;

    /**
     * 管控模式(0:批量;1:单支)
     */
    @Schema(description = "管控模式(0:批量;1:单支)")
    @Column(name = "control_mode")
    private int controlMode;

    /**
     * 是否管控物料(0:不管控;1:管控)
     */
    @Schema(description = "是否管控物料(0:不管控;1:管控)")
    @Column(name = "is_control_material")
    private boolean isControlMaterial;

    /**
     * 是否绑定容器(0:否;1:是)
     */
    @Schema(description = "是否绑定容器(0:否;1:是)")
    @Column(name = "is_bind_container")
    private boolean isBindContainer;

    /**
     * 投产比例
     */
    @Schema(description = "投产比例")
    @Column(name = "input_rate")
    @ColumnDefault("1")
    private double inputRate;

    public WsStep(){

    }

    public WsStep(WorkFlow workFlow, String preStepId, String afterStepId, Step step, int category, int requestMode, int controlMode, boolean isControlMaterial, boolean isBindContainer, double inputRate, int requestMethod) {
        this.workFlow = workFlow;
        this.preStepId = preStepId;
        this.afterStepId = afterStepId;
        this.step = step;
        this.category = category;
        this.requestMode = requestMode;
        this.controlMode = controlMode;
        this.isControlMaterial = isControlMaterial;
        this.isBindContainer = isBindContainer;
        this.inputRate = inputRate;
        this.requestMethod = requestMethod;
    }

    public int getRequestMethod() {
        return requestMethod;
    }

    public WsStep setRequestMethod(int requestMethod) {
        this.requestMethod = requestMethod;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public WsStep setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public String getPreStepId() {
        return preStepId;
    }

    public WsStep setPreStepId(String preStepId) {
        this.preStepId = preStepId;
        return this;
    }

    public String getAfterStepId() {
        return afterStepId;
    }

    public WsStep setAfterStepId(String afterStepId) {
        this.afterStepId = afterStepId;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsStep setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WsStep setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WsStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getTransferType() {
        return transferType;
    }

    public WsStep setTransferType(int transferType) {
        this.transferType = transferType;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WsStep setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getRequestMode() {
        return requestMode;
    }

    public WsStep setRequestMode(int requestMode) {
        this.requestMode = requestMode;
        return this;
    }

    public int getControlMode() {
        return controlMode;
    }

    public WsStep setControlMode(int controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public boolean getIsControlMaterial() {
        return isControlMaterial;
    }

    public WsStep setIsControlMaterial(boolean isControlMaterial) {
        this.isControlMaterial = isControlMaterial;
        return this;
    }

    public boolean getIsBindContainer() {
        return isBindContainer;
    }

    public WsStep setIsBindContainer(boolean isBindContainer) {
        this.isBindContainer = isBindContainer;
        return this;
    }

    public double getInputRate() {
        return inputRate;
    }

    public WsStep setInputRate(double inputRate) {
        this.inputRate = inputRate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsStep wsStep = (WsStep) o;
        if (wsStep.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wsStep.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
