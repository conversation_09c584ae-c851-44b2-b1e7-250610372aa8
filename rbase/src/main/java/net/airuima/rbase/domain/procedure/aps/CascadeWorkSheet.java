package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单级联关系Domain
 * <AUTHOR>
 */

@Schema(name = "工单级联关系(CascadeWorkSheet)", description = "工单级联关系实体")
@Entity
@Table(name = "procedure_cascade_work_sheet", uniqueConstraints = @UniqueConstraint(name = "procedure_cascade_work_sheet_unique", columnNames = {"superior_work_sheet_id", "subordinate_work_sheet_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class CascadeWorkSheet extends CustomBaseEntity implements Serializable {

    /**
     * 上级工单(如成品工单)
     */
    @NotNull
    @ManyToOne
    @Schema(description = "上级工单", required = true)
    @JoinColumn(name = "superior_work_sheet_id", nullable = false)
    private WorkSheet superiorWorkSheet;


    /**
     * 下级工单(如半成品工单)
     */
    @NotNull
    @ManyToOne
    @Schema(description = "下级工单", required = true)
    @JoinColumn(name = "subordinate_work_sheet_id", nullable = false)
    private WorkSheet subordinateWorkSheet;

    public CascadeWorkSheet(){

    }

    public CascadeWorkSheet(WorkSheet superiorWorkSheet, WorkSheet subordinateWorkSheet) {
        this.superiorWorkSheet = superiorWorkSheet;
        this.subordinateWorkSheet = subordinateWorkSheet;
    }

    public WorkSheet getSuperiorWorkSheet() {
        return superiorWorkSheet;
    }

    public CascadeWorkSheet setSuperiorWorkSheet(WorkSheet superiorWorkSheet) {
        this.superiorWorkSheet = superiorWorkSheet;
        return this;
    }

    public WorkSheet getSubordinateWorkSheet() {
        return subordinateWorkSheet;
    }

    public CascadeWorkSheet setSubordinateWorkSheet(WorkSheet subordinateWorkSheet) {
        this.subordinateWorkSheet = subordinateWorkSheet;
        return this;
    }
}
