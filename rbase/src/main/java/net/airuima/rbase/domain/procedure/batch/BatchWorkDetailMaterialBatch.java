package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情物料批次Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "批量生产详情物料批次(BatchWorkDetailMaterialBatch)", description = "批量生产详情物料批次实体")
@Entity
@Table(name = "procedure_batch_work_detail_material_batch", uniqueConstraints = @UniqueConstraint(name = "procedure_batch_work_detail_material_batch_unique",
        columnNames = {"material_batch", "batch_work_detail_id", "material_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "batchWorkDetailMaterialBatchEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class BatchWorkDetailMaterialBatch extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    @Column(name = "material_batch")
    private String materialBatch;

    /**
     * 批次流水号
     */
    @Schema(description = "批次流水号")
    @Column(name = "serial")
    private String serial;

    /**
     * 工单批次详情
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工单批次详情", required = true)
    @JoinColumn(name = "batch_work_detail_id", nullable = false)
    private BatchWorkDetail batchWorkDetail;

    /**
     * 物料id
     */
    @NotNull
    @Schema(description = "物料id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();


    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 用料数量
     */
    @Schema(description = "用料数量")
    @Column(name = "number")
    private double number;

    /**
     * 扣料方式 0：不扣 1：工单扣料，2：工位扣料
     */
    @Schema(description = "扣料方式")
    @Column(name = "type")
    private int type;

    @Schema(description = "物料批次信息")
    public record MaterialBatchBaseRecordInfo(
            @Schema(description = "物料名称",type = "string")
            String materialName,
            @Schema(description = "物料编码",type = "string")
            String materialCode,
            @Schema(description = "物料批次",type = "string")
            String materialBatch,
            @Schema(description = "用料数量",type = "number",format = "double")
            double number){}

    public double getNumber() {
        return number;
    }

    public BatchWorkDetailMaterialBatch setNumber(double number) {
        this.number = number;
        return this;
    }

    public int getType() {
        return type;
    }

    public BatchWorkDetailMaterialBatch setType(int type) {
        this.type = type;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public BatchWorkDetailMaterialBatch setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public String getSerial() {
        return serial;
    }

    public BatchWorkDetailMaterialBatch setSerial(String serial) {
        this.serial = serial;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public BatchWorkDetailMaterialBatch setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public BatchWorkDetailMaterialBatch setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public BatchWorkDetailMaterialBatch setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public BatchWorkDetailMaterialBatch setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public BatchWorkDetailMaterialBatch setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = (BatchWorkDetailMaterialBatch) o;
        if (batchWorkDetailMaterialBatch.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), batchWorkDetailMaterialBatch.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
