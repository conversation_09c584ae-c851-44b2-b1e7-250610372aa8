package net.airuima.rbase.domain.base.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "易损件(WearingPart)", description = "易损件表")
@Entity
@Table(name = "base_wearing_part", uniqueConstraints = @UniqueConstraint(name = "base_wearing_part_unique", columnNames = {"code","serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "易损件实体数据")
public class WearingPart extends CustomBaseEntity {

    /**
     * 易损件类型
     */
    @NotNull
    @Schema(description = "易损件类型id")
    @ManyToOne
    @JoinColumn(name = "group_id", nullable = false)
    private WearingPartGroup wearingPartGroup;
    /**
     * 易损件名称
     */
    @NotNull
    @Schema(description = "易损件名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 易损件编码
     */
    @NotNull
    @Schema(description = "易损件编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 易损件流水号
     */
    @Schema(description = "易损件流水号")
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 易损件使用类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
     */
    @Schema(description = "易损件使用类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）")
    @Column(name = "category")
    private int category;

    /**
     * 最大重置次数
     */
    @Schema(description = "最大重置次数")
    @Column(name = "max_reset_number")
    private int maxResetNumber;

    /**
     * 累计重置次数
     */
    @Schema(description = "累计重置次数")
    @Column(name = "accumulate_reset_number")
    private int accumulateResetNumber;

    /**
     * 最大使用次数
     */
    @Schema(description = "最大使用次数")
    @Column(name = "max_use_number")
    private int maxUseNumber;

    /**
     * 累计使用次数
     */
    @Schema(description = "累计使用次数")
    @Column(name = "accumulate_use_number")
    private int accumulateUseNumber;

    /**
     * 最大使用时长(秒为单位)
     */
    @Schema(description = "最大使用时长(秒为单位)")
    @Column(name = "max_use_time")
    private int maxUseTime;

    /**
     * 累计使用时长(秒为单位)
     */
    @Schema(description = "累计使用时长(秒为单位)")
    @Column(name = "accumulate_use_time")
    private int accumulateUseTime;

    /**
     * 易损件使用状态（0可用，1在用，2超期，3报废）
     */
    @Schema(description = "易损件使用状态（0可用，1在用，2超期，3报废）")
    @Column(name = "status")
    private int status;

    /**
     * 易损件重置方式（0:手动，1:自动）
     */
    @Schema(description = "易损件重置方式（0:手动，1:自动）")
    @Column(name = "reset_way")
    private int resetWay;

    /**
     * 失效期
     */
    @Schema(description = "失效期")
    @Column(name = "expire_date")
    private LocalDateTime expireDate;

    /**
     * 新增时需要生成的易损件流水号个数
     */
    @Transient
    @Schema(description = "新增时需要生成的易损件流水号个数")
    private Integer number;

    public WearingPart(){

    }

    public WearingPart(WearingPart wearingPart) {
        this.wearingPartGroup = wearingPart.getWearingPartGroup();
        this.name = wearingPart.getName();
        this.code = wearingPart.getCode();
        this.category = wearingPart.getCategory();
        this.maxResetNumber = wearingPart.getMaxResetNumber();
        this.accumulateResetNumber = wearingPart.accumulateResetNumber;
        this.maxUseNumber = wearingPart.maxUseNumber;
        this.accumulateUseNumber = wearingPart.accumulateUseNumber;
        this.maxUseTime = wearingPart.maxUseTime;
        this.accumulateUseTime = wearingPart.accumulateUseTime;
        this.status = wearingPart.getStatus();
        this.resetWay = wearingPart.getResetWay();
        this.expireDate = wearingPart.getExpireDate();

    }

    public WearingPart(Long id) {
        this.id = id;
    }

    public WearingPartGroup getWearingPartGroup() {
        return wearingPartGroup;
    }

    public WearingPart setWearingPartGroup(WearingPartGroup wearingPartGroup) {
        this.wearingPartGroup = wearingPartGroup;
        return this;
    }

    public String getName() {
        return name;
    }

    public WearingPart setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WearingPart setCode(String code) {
        this.code = code;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WearingPart setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WearingPart setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getMaxResetNumber() {
        return maxResetNumber;
    }

    public WearingPart setMaxResetNumber(int maxResetNumber) {
        this.maxResetNumber = maxResetNumber;
        return this;
    }

    public int getAccumulateResetNumber() {
        return accumulateResetNumber;
    }

    public WearingPart setAccumulateResetNumber(int accumulateResetNumber) {
        this.accumulateResetNumber = accumulateResetNumber;
        return this;
    }

    public int getMaxUseNumber() {
        return maxUseNumber;
    }

    public WearingPart setMaxUseNumber(int maxUseNumber) {
        this.maxUseNumber = maxUseNumber;
        return this;
    }

    public int getAccumulateUseNumber() {
        return accumulateUseNumber;
    }

    public WearingPart setAccumulateUseNumber(int accumulateUseNumber) {
        this.accumulateUseNumber = accumulateUseNumber;
        return this;
    }

    public int getMaxUseTime() {
        return maxUseTime;
    }

    public WearingPart setMaxUseTime(int maxUseTime) {
        this.maxUseTime = maxUseTime;
        return this;
    }

    public int getAccumulateUseTime() {
        return accumulateUseTime;
    }

    public WearingPart setAccumulateUseTime(int accumulateUseTime) {
        this.accumulateUseTime = accumulateUseTime;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public WearingPart setStatus(int status) {
        this.status = status;
        return this;
    }

    public int getResetWay() {
        return resetWay;
    }

    public WearingPart setResetWay(int resetWay) {
        this.resetWay = resetWay;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public WearingPart setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WearingPart setNumber(Integer number) {
        this.number = number;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WearingPart wearingPart = (WearingPart) o;
        if (wearingPart.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wearingPart.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
