package net.airuima.rbase.domain.procedure.single;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情物料批次Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "单支生产详情物料批次(SnWorkDetailMaterialBatch)", description = "单支生产详情物料批次实体")
@Entity
@Table(name = "procedure_sn_work_detail_material_batch", uniqueConstraints = @UniqueConstraint(name = "base_sn_work_detail_material_batch_unique",
        columnNames = {"material_batch", "sn_work_detail_id", "material_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "snWorkDetailMaterialBatchEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "snWorkDetail",subgraph = "snWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "snWorkDetailEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class SnWorkDetailMaterialBatch extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料id
     */
    @NotNull
    @Schema(description = "物料id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 物料DTO
     */
    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    @Column(name = "material_batch")
    private String materialBatch;

    /**
     * SN工作详情
     */
    @NotNull
    @ManyToOne
    @Schema(description = "SN工作详情", required = true)
    @JoinColumn(name = "sn_work_detail_id", nullable = false)
    private SnWorkDetail snWorkDetail;

    /**
     * 用料数量
     */
    @Schema(description = "用料数量")
    @Column(name = "number")
    private double number;

    /**
     * 扣料方式 0：不扣 1：工单扣料，2：工位扣料
     */
    @Schema(description = "扣料方式")
    @Column(name = "type")
    private int type;


    public Long getMaterialId() {
        return materialId;
    }

    public SnWorkDetailMaterialBatch setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public SnWorkDetailMaterialBatch setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public SnWorkDetailMaterialBatch setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public SnWorkDetailMaterialBatch setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public SnWorkDetailMaterialBatch setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public SnWorkDetail getSnWorkDetail() {
        return snWorkDetail;
    }

    public SnWorkDetailMaterialBatch setSnWorkDetail(SnWorkDetail snWorkDetail) {
        this.snWorkDetail = snWorkDetail;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public SnWorkDetailMaterialBatch setNumber(double number) {
        this.number = number;
        return this;
    }

    public int getType() {
        return type;
    }

    public SnWorkDetailMaterialBatch setType(int type) {
        this.type = type;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnWorkDetailMaterialBatch snWorkDetailMaterialBatch = (SnWorkDetailMaterialBatch) o;
        if (snWorkDetailMaterialBatch.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snWorkDetailMaterialBatch.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
