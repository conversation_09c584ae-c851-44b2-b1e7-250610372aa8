package net.airuima.rbase.domain.procedure.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产品谱系工序生产在制统计Domain
 * <AUTHOR>
 * @date 2023/12/19
 */
@Schema(name = "产品谱系工序生产在制统计", description = "产品谱系工序生产在制统计")
@Entity
@Table(name = "procedure_pedigree_step_statistics",uniqueConstraints = @UniqueConstraint(name = "procedure_pedigree_step_statistics_unique", columnNames = {"work_line_id","pedigree_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "pedigreeStepStatisticsEntityGraph",
        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")})

public class PedigreeStepStatistics extends CustomBaseEntity implements Serializable {

    /**
     * 生产线
     */
    @ManyToOne
    @Schema(description = "生产线")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 产品谱系
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 计划数量
     */
    @Schema(description = "计划数量")
    @Column(name = "plan_number")
    private int planNumber;


    /**
     * 目标良率
     */
    @Schema(description = "目标良率")
    @Column(name = "target_qualified_rate")
    private double targetQualifiedRate;

    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    @Column(name = "input_number")
    private int inputNumber;

    /**
     * 工序合格数量
     */
    @Schema(description = "工序合格数量")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 工序不合格数量
     */
    @Schema(description = "工序不合格数量")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 下交数量
     */
    @Schema(description = "下交数量")
    @Column(name = "transfer_number")
    private int transferNumber;

    /**
     * 在制数量
     */
    @Schema(description = "在制数量")
    @Column(name = "online_number")
    private int onlineNumber;

    public WorkLine getWorkLine() {
        return workLine;
    }

    public PedigreeStepStatistics setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepStatistics setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepStatistics setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getPlanNumber() {
        return planNumber;
    }

    public PedigreeStepStatistics setPlanNumber(int planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public double getTargetQualifiedRate() {
        return targetQualifiedRate;
    }

    public PedigreeStepStatistics setTargetQualifiedRate(double targetQualifiedRate) {
        this.targetQualifiedRate = targetQualifiedRate;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public PedigreeStepStatistics setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public PedigreeStepStatistics setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public PedigreeStepStatistics setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getTransferNumber() {
        return transferNumber;
    }

    public PedigreeStepStatistics setTransferNumber(int transferNumber) {
        this.transferNumber = transferNumber;
        return this;
    }

    public int getOnlineNumber() {
        return onlineNumber;
    }

    public PedigreeStepStatistics setOnlineNumber(int onlineNumber) {
        this.onlineNumber = onlineNumber;
        return this;
    }
}
