package net.airuima.rbase.domain.procedure.aps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.util.NumberUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产总工单Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(description = "生产工单(WorkSheet)")
@Entity
@Table(name = "procedure_work_sheet", uniqueConstraints = @UniqueConstraint(name = "procedure_work_sheet_unique", columnNames = {"serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "生产总工单数据")
@NamedEntityGraph(name = "workSheetEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree"), @NamedAttributeNode("workLine"), @NamedAttributeNode("workFlow"), @NamedAttributeNode("saleOrder")})
public class WorkSheet extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    @NotNull
    @Schema(description = "工单号", requiredMode = Schema.RequiredMode.REQUIRED, type = "string", maxLength = 50)
    @Column(name = "serial_number", nullable = false)
    private String serialNumber;

    /**
     * 工单投产数
     */
    @Schema(description = "工单投产数", requiredMode = Schema.RequiredMode.REQUIRED, type = "integer", format = "int32", minimum = "1")
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 工单合格数
     */
    @Schema(description = "工单合格数", requiredMode = Schema.RequiredMode.REQUIRED, type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "qualified_number", nullable = false)
    private int qualifiedNumber;

    /**
     * 工单不合格数
     */
    @Schema(description = "工单不合格数", type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "unqualified_number", nullable = false)
    private int unqualifiedNumber;

    /**
     * 返工合格数
     */
    @Schema(description = "返工合格数", type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "rework_qualified_number")
    private int reworkQualifiedNumber;

    /**
     * 子工单粒度投产时为子工单总个数/工单粒度投产时为工序总个数
     */
    @Schema(description = "子工单粒度投产时为子工单总个数/工单粒度投产时为工序总个数", requiredMode = Schema.RequiredMode.NOT_REQUIRED, type = "integer", format = "int32", defaultValue = "0", minimum = "1")
    @Column(name = "step_number", nullable = false)
    private int stepNumber;

    /**
     * 子工单粒度投产时为已完成子工单个数/工单粒度投产时为已完成工序个数
     */
    @Schema(description = "子工单粒度投产时为已完成子工单个数/工单粒度投产时为已完成工序个数", requiredMode = Schema.RequiredMode.NOT_REQUIRED, type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "step_comp_number", nullable = false)
    private int stepCompNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @Column(name = "plan_start_date", nullable = true)
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划结单日期")
    @Column(name = "plan_end_date", nullable = true)
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Schema(description = "实际开工日期")
    @Column(name = "actual_start_date", nullable = true)
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Schema(description = "实际完成日期")
    @Column(name = "actual_end_date")
    private LocalDateTime actualEndDate;

    /**
     * 工单类型 -1: 返工单 0:返修单,1:正常单,2: 外协单
     */
    @Schema(description = "工单类型", required = true)
    @Column(name = "category", nullable = false)
    private int category;

    @Schema(description = "子工单生成状态((0:未生成;1:部分生成;2:全部生成)", required = true)
    @Column(name = "generate_sub_ws_status", nullable = false)
    private int generateSubWsStatus;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单，6：收货中)
     */
    @Schema(description = "工单状态(-2:已取消;-1:审批中;0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)", required = true)
    @Column(name = "status", nullable = false)
    private int status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 产品谱系(最小层级)
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系(最小层级)", required = true)
    @JoinColumn(name = "pedigree_id", nullable = false)
    private Pedigree pedigree;

    /**
     * 流程框图
     */
    @ManyToOne
    @Schema(description = "流程框图")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 组织架构id
     */
    @Schema(description = "组织架构id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 组织架构DTO
     */
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    @Transient
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 生产线
     */
    @ManyToOne
    @Schema(description = "生产线")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 物料方案id
     */
    @Schema(description = "物料方案id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "bom_info_id")
    private Long bomInfoId;

    /**
     * 物料方案DTO
     */
    @FetchField(mapUri = "/api/bom-info", serviceId = "mom", paramKey = "bomInfoId")
    @Transient
    private BomInfoDTO bomInfoDto = new BomInfoDTO();

    /**
     * 乐观锁版本
     */
    @Schema(description = "乐观锁版本")
    @Column(name = "version")
    private Long version;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 结单原因
     */
    @Schema(description = "结单原因")
    @Column(name = "statement_reason")
    private String statementReason;


    /**
     * 降级总数
     */
    @Schema(description = "降级总数")
    @Column(name = "down_grade_number")
    private int downGradeNumber;

    /**
     * 流程实例ID
     */
    @Schema(description = "流程实例ID")
    @Column(name = "process_instance_id")
    private String processInstanceId;

    /**
     * 订单号
     */
    @ManyToOne
    @Schema(description = "订单号")
    @JoinColumn(name = "sale_order_id")
    private SaleOrder saleOrder;

    /**
     * 工单关联销售订单详情
     */
    @OneToMany(mappedBy = "workSheet")
    @JsonIgnoreProperties("workSheet")
    private List<WsSaleOrderDetail> wsSaleOrderDetails = Lists.newArrayList();

    /**
     * 优先级
     */
    @Schema(description = "优先级", required = true)
    @Column(name = "priority", nullable = false)
    private int priority;

    /**
     * 交付日期
     */
    @Schema(description = "交付日期")
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * 工单进度
     */
    @Schema(description = "工单进度")
    @Transient
    private BigDecimal progress;

    /**
     * 是否逾期
     */
    @Schema(description = "是否逾期")
    @Transient
    private boolean isOverdue;

    /**
     * 逾期时长
     */
    @Schema(description = "逾期时长")
    @Transient
    private String overdueTime;

    /**
     * 直通率
     */
    @Schema(description = "直通率")
    @Transient
    private BigDecimal fpy;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    @Transient
    private BigDecimal fty;

    /**
     * 产品阶段(0:量产;1:试产)
     */
    @Schema(description = "产品阶段(0:量产;1:试产)")
    @Column(name = "stage")
    private int stage;

    /**
     * 原始型号
     */
    @Schema(description = "原始型号")
    @ManyToOne
    @JoinColumn(name="origin_pedigree_id")
    private Pedigree originPedigree;

    /**
     * 工单入库数量
     */
    @Schema(description = "工单入库数量")
    @Column(name = "inventory_number")
    private int inventoryNumber;

    public Pedigree getOriginPedigree() {
        return originPedigree;
    }

    public WorkSheet setOriginPedigree(Pedigree originPedigree) {
        this.originPedigree = originPedigree;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheet setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public WorkSheet() {
    }

    public WorkSheet(Long id) {
        this.id = id;
    }

    public int getNumber() {
        return number;
    }

    public WorkSheet setNumber(int number) {
        this.number = number;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public WorkSheet setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public WorkSheet setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheet setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkSheet setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public WorkSheet setReworkQualifiedNumber(int reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        return this;
    }

    public int getStepNumber() {
        return stepNumber;
    }

    public WorkSheet setStepNumber(int stepNumber) {
        this.stepNumber = stepNumber;
        return this;
    }

    public int getStepCompNumber() {
        return stepCompNumber;
    }

    public WorkSheet setStepCompNumber(int stepCompNumber) {
        this.stepCompNumber = stepCompNumber;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkSheet setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheet setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public WorkSheet setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
        return this;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public WorkSheet setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkSheet setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public WorkSheet setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WorkSheet setNote(String note) {
        this.note = note;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkSheet setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getBomInfoId() {
        return bomInfoId;
    }

    public WorkSheet setBomInfoId(Long bomInfoId) {
        this.bomInfoId = bomInfoId;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public WorkSheet setVersion(Long version) {
        this.version = version;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public WorkSheet setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public WorkSheet setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WorkSheet setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public WorkSheet setOrganizationDTO(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public BomInfoDTO getBomInfoDto() {
        return bomInfoDto;
    }

    public WorkSheet setBomInfoDto(BomInfoDTO bomInfoDto) {
        this.bomInfoDto = bomInfoDto;
        return this;
    }

    public int getGenerateSubWsStatus() {
        return generateSubWsStatus;
    }

    public WorkSheet setGenerateSubWsStatus(int generateSubWsStatus) {
        this.generateSubWsStatus = generateSubWsStatus;
        return this;
    }

    public String getStatementReason() {
        return statementReason;
    }

    public WorkSheet setStatementReason(String statementReason) {
        this.statementReason = statementReason;
        return this;
    }

    public int getDownGradeNumber() {
        return downGradeNumber;
    }

    public WorkSheet setDownGradeNumber(int downGradeNumber) {
        this.downGradeNumber = downGradeNumber;
        return this;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public WorkSheet setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }

    public SaleOrder getSaleOrder() {
        return saleOrder;
    }

    public WorkSheet setSaleOrder(SaleOrder saleOrder) {
        this.saleOrder = saleOrder;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public WorkSheet setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public BigDecimal getProgress() {
        if (this.getStepNumber() == Constants.INT_ZERO) {
            return BigDecimal.ZERO;
        }
        return NumberUtils.divide(this.getStepCompNumber(), this.getStepNumber(), Constants.INT_FOUR);
    }

    public WorkSheet setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public WorkSheet setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public boolean getIsOverdue() {
        if (null != this.getActualEndDate() && null != this.getPlanEndDate() && this.getActualEndDate().isAfter(this.getPlanEndDate())) {
            return Boolean.TRUE;
        }
        if (this.status == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
            return Boolean.FALSE;
        }
        if (null == this.getActualEndDate() && null != this.getPlanEndDate() && this.getPlanEndDate().isBefore(LocalDateTime.now())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public WorkSheet setIsOverdue(boolean overdue) {
        isOverdue = overdue;
        return this;
    }

    public WorkSheet setOverdueTime(String overdueTime) {
        this.overdueTime = overdueTime;
        return this;
    }

    /**
     * 逾期时长
     */
    public String getOverdueTime() {
        if (this.getPlanEndDate() == null) {
            return Constants.EMPTY;
        }
        LocalDateTime endTime = Objects.isNull(this.getActualEndDate()) ? LocalDateTime.now() : this.getActualEndDate();
        long seconds = Duration.between(this.getPlanEndDate(), endTime).getSeconds() * 1000;
        if (seconds <= Constants.INT_ZERO) {
            return Constants.EMPTY;
        }
        return DateUtils.formatTime(seconds);
    }

    public BigDecimal getFpy() {
        return this.number > net.airuima.constant.Constants.INT_ZERO ? NumberUtils.divide((this.qualifiedNumber - this.reworkQualifiedNumber), this.number, net.airuima.constant.Constants.INT_TWO) : BigDecimal.ZERO;
    }

    public WorkSheet setFpy(BigDecimal fpy) {
        this.fpy = fpy;
        return this;
    }

    public BigDecimal getFty() {
        return this.number > net.airuima.constant.Constants.INT_ZERO ? NumberUtils.divide(this.qualifiedNumber, this.number, net.airuima.constant.Constants.INT_TWO) : BigDecimal.ZERO;
    }

    public WorkSheet setFty(BigDecimal fty) {
        this.fty = fty;
        return this;
    }

    public int getStage() {
        return stage;
    }

    public WorkSheet setStage(int stage) {
        this.stage = stage;
        return this;
    }

    public List<WsSaleOrderDetail> getWsSaleOrderDetails() {
        return wsSaleOrderDetails;
    }

    public WorkSheet setWsSaleOrderDetails(List<WsSaleOrderDetail> wsSaleOrderDetails) {
        this.wsSaleOrderDetails = wsSaleOrderDetails;
        return this;
    }

    public int getInventoryNumber() {
        return inventoryNumber;
    }

    public WorkSheet setInventoryNumber(int inventoryNumber) {
        this.inventoryNumber = inventoryNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkSheet workSheet = (WorkSheet) o;
        if (workSheet.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workSheet.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
