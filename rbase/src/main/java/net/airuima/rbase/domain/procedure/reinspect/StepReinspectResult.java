package net.airuima.rbase.domain.procedure.reinspect;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检结果Domain
 * <AUTHOR>
 */
@Schema(name = "工序不良复检结果(StepReinspectResult)", description = "工序不良复检结果")
@Entity
@Table(name = "procedure_step_reinspect_result")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class StepReinspectResult  extends CustomBaseEntity implements Serializable {

    /**
     * 复检历史
     */
    @ManyToOne
    @JoinColumn(name = "step_reinspect_id")
    @Schema(description = "复检历史")
    private StepReinspect stepReinspect;

    /**
     * 复判不良项目
     */
    @ManyToOne
    @JoinColumn(name = "target_unqualified_item_id")
    @Schema(description = "复判不良项目")
    private UnqualifiedItem targetUnqualifiedItem;


    /**
     * 数量
     */
    @Column(name = "number")
    @Schema(description = "数量")
    private int number;

    /**
     * 处理结果:0:放行;1:返工;2:报废
     */
    @Schema(description = "处理结果:0:放行;1:返工;2:报废")
    @Column(name = "result")
    private int result;


    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

    public StepReinspect getStepReinspect() {
        return stepReinspect;
    }

    public StepReinspectResult setStepReinspect(StepReinspect stepReinspect) {
        this.stepReinspect = stepReinspect;
        return this;
    }

    public UnqualifiedItem getTargetUnqualifiedItem() {
        return targetUnqualifiedItem;
    }

    public StepReinspectResult setTargetUnqualifiedItem(UnqualifiedItem targetUnqualifiedItem) {
        this.targetUnqualifiedItem = targetUnqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public StepReinspectResult setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getResult() {
        return result;
    }

    public StepReinspectResult setResult(int result) {
        this.result = result;
        return this;
    }

    public String getNote() {
        return note;
    }

    public StepReinspectResult setNote(String note) {
        this.note = note;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepReinspectResult stepReinspectResult = (StepReinspectResult) o;
        if (stepReinspectResult.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepReinspectResult.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
