package net.airuima.rbase.domain.procedure.scene;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 下个待做工序生产信息
 *
 * <AUTHOR>
 * @since 1.8.1
 */
@Schema(name = "下个待做工序生产信息(NextTodoStep)", description = "下个待做工序生产信息")
@Entity
@Table(name = "procedure_next_todo_step")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class NextTodoStep extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN号")
    @Column(name = "sn")
    private String sn;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private int number;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public NextTodoStep setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public NextTodoStep setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public NextTodoStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public NextTodoStep setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public NextTodoStep setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public NextTodoStep setNumber(int number) {
        this.number = number;
        return this;
    }
}
