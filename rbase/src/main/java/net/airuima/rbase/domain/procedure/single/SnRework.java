package net.airuima.rbase.domain.procedure.single;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.Container;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 容器在线返修单SN关联Domain
 *
 * <AUTHOR>
 * @date 2021-01-11
 */
@Schema(name = "容器在线返修单SN关联(SnRework)", description = "容器在线返修单SN关联实体")
@Entity
@Table(name = "procedure_sn_rework")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "snReworkEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "snWorkStatus",subgraph = "snWorkStatusEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "snWorkStatusEntityGraph",
                        attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})})
public class SnRework extends CustomBaseEntity implements Serializable {
    @ManyToOne
    @Schema(description = "容器")
    @JoinColumn(name = "container_id")
    private Container container;

    @ManyToOne
    @Schema(description = "关联在线返修单")
    @JoinColumn(name = "rework_ws_id")
    private WsRework wsRework;

    @ManyToOne
    @Schema(description = "SN生产状态")
//    @NotNull
    @JoinColumn(name = "sn_work_status_id")
    private SnWorkStatus snWorkStatus;

    @Schema(description = "绑定状态(1:绑定;0:解绑)")
    private int status;

    public Container getContainer() {
        return container;
    }

    public SnRework setContainer(Container container) {
        this.container = container;
        return this;
    }

    public WsRework getWsRework() {
        return wsRework;
    }

    public SnRework setWsRework(WsRework wsRework) {
        this.wsRework = wsRework;
        return this;
    }

    public SnWorkStatus getSnWorkStatus() {
        return snWorkStatus;
    }

    public SnRework setSnWorkStatus(SnWorkStatus snWorkStatus) {
        this.snWorkStatus = snWorkStatus;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SnRework setStatus(int status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnRework snRework = (SnRework) o;
        if (snRework.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snRework.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
