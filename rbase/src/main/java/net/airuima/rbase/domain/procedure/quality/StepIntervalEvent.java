package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序间隔异常Domain
 * <AUTHOR>
 */

@Schema(name = "工序间隔异常(StepIntervalEvent)", description = "工序间隔异常")
@Entity
@Table(name = "procedure_step_interval_event")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "stepIntervalEventEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class StepIntervalEvent extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 责任人ID
     */
    @Schema(description = "责任人ID")
    @Column(name = "owner_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ownerId;

    /**
     * 责任人DTO
     */
    @Schema(description = "责任人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "ownerId")
    @FetchDataFilter(schema = "organization",tableName = "staff",foreignKey = "owner_id")
    @Transient
    private StaffDTO ownerDto = new StaffDTO();

    /**
     * 异常工位
     */
    @ManyToOne
    @Schema(description = "异常工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 异常工序
     */
    @ManyToOne
    @Schema(description = "异常工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 前置工序
     */
    @ManyToOne
    @Schema(description = "前置工序")
    @JoinColumn(name = "pre_step_id")
    private Step preStep;

    /**
     * 请求容器
     */
    @ManyToOne
    @Schema(description = "请求容器")
    @JoinColumn(name = "request_container_id")
    private Container requestContainer;

    /**
     * 前置容器
     */
    @ManyToOne
    @Schema(description = "前置容器")
    @JoinColumn(name = "pre_container_id")
    private Container preContainer;

    /**
     * sn
     */
    @Schema(description = "sn")
    @Column(name = "sn")
    private String  sn;

    /**
     * 发生时间
     */
    @Schema(description = "发生时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    /**
     * 处理状态:0:待处理:1:放行
     */
    @Schema(description = "处理状态:0:待处理:1:放行")
    @Column(name = "status")
    private int status;

    /**
     * 处理人ID
     */
    @Schema(description = "处理人ID")
    @Column(name = "deal_staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealStaffId;

    /**
     * 处理人DTO
     */
    @Schema(description = "处理人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "dealStaffId")
    @FetchDataFilter(schema = "organization",tableName = "staff",foreignKey = "deal_staff_id")
    @Transient
    private StaffDTO dealStaffDto = new StaffDTO();;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    @Column(name = "deal_time")
    private LocalDateTime dealTime;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public StepIntervalEvent setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public StepIntervalEvent setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public StepIntervalEvent setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
        return this;
    }

    public StaffDTO getOwnerDto() {
        return ownerDto;
    }

    public StepIntervalEvent setOwnerDto(StaffDTO ownerDto) {
        this.ownerDto = ownerDto;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public StepIntervalEvent setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StepIntervalEvent setStep(Step step) {
        this.step = step;
        return this;
    }

    public Step getPreStep() {
        return preStep;
    }

    public StepIntervalEvent setPreStep(Step preStep) {
        this.preStep = preStep;
        return this;
    }

    public Container getRequestContainer() {
        return requestContainer;
    }

    public StepIntervalEvent setRequestContainer(Container requestContainer) {
        this.requestContainer = requestContainer;
        return this;
    }

    public Container getPreContainer() {
        return preContainer;
    }

    public StepIntervalEvent setPreContainer(Container preContainer) {
        this.preContainer = preContainer;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public StepIntervalEvent setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public StepIntervalEvent setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public StepIntervalEvent setStatus(int status) {
        this.status = status;
        return this;
    }

    public Long getDealStaffId() {
        return dealStaffId;
    }

    public StepIntervalEvent setDealStaffId(Long dealStaffId) {
        this.dealStaffId = dealStaffId;
        return this;
    }

    public StaffDTO getDealStaffDto() {
        return dealStaffDto;
    }

    public StepIntervalEvent setDealStaffDto(StaffDTO dealStaffDto) {
        this.dealStaffDto = dealStaffDto;
        return this;
    }

    public LocalDateTime getDealTime() {
        return dealTime;
    }

    public StepIntervalEvent setDealTime(LocalDateTime dealTime) {
        this.dealTime = dealTime;
        return this;
    }

    public String getNote() {
        return note;
    }

    public StepIntervalEvent setNote(String note) {
        this.note = note;
        return this;
    }
}
