package net.airuima.rbase.domain.base.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.quality.InspectionEscalateDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 * 质检人员设置
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Schema(name = "质检人员设置", description = "质检人员设置")
@Entity
@Table(name = "base_inspection_assignment_config")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
public class InspectionAssignmentConfig extends CustomBaseEntity implements Serializable {

    /**
     * 质检类型(首检0/巡检1/抽检2/终检3/末检4/来料检5/破坏性检验6)
     */
    @NotNull
    @Schema(description = "质检类型(首检0/巡检1/抽检2/终检3/末检4/来料检5/破坏性检验6)")
    @Column(name = "inspection_type", nullable = false)
    private int inspectionType;

    /**
     * 条件优先级配置
     */
    @ManyToOne
    @Schema(description = "条件优先级配置")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    /**
     * 生产线id
     */
    @ManyToOne
    @Schema(description = "生产线id")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 产品谱系ID
     */
    @ManyToOne
    @Schema(description = "产品谱系ID")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线ID
     */
    @ManyToOne
    @Schema(description = "工艺路线ID")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序ID
     */
    @ManyToOne
    @Schema(description = "工序ID")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 物料ID
     */
    @Schema(description = "物料id")
    @Column(name = "material_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDTO = new MaterialDTO();

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(serviceName = "clientServices", paramKey = "clientId")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId", tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 分配质检人员类型(按部门随机分配0/按角色随机分配1/指定人员2)
     */
    @NotNull
    @Schema(description = "分配质检人员类型(按部门随机分配0/按角色随机分配1/指定人员2)")
    @Column(name = "assignment_type", nullable = false)
    private int assignmentType;

    /**
     * 分配质检人员(部门ID/角色ID/人员ID)
     */
    @Schema(description = "分配质检人员")
    @Column(name = "assignment_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long assignmentId;

    /**
     * 升级策略配置
     */
    @Schema(description = "升级策略配置")
    @Type(JsonType.class)
    @Column(name = "inspection_escalate")
    private InspectionEscalateDTO inspectionEscalateDTO;

    /**
     * 是否启用
     */
    @NotNull
    @Schema(description = "是否启用")
    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;

    public int getInspectionType() {
        return inspectionType;
    }

    public InspectionAssignmentConfig setInspectionType(int inspectionType) {
        this.inspectionType = inspectionType;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public InspectionAssignmentConfig setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public InspectionAssignmentConfig setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public InspectionAssignmentConfig setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public InspectionAssignmentConfig setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public InspectionAssignmentConfig setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public InspectionAssignmentConfig setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDTO() {
        return materialDTO;
    }

    public InspectionAssignmentConfig setMaterialDTO(MaterialDTO materialDTO) {
        this.materialDTO = materialDTO;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public InspectionAssignmentConfig setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public InspectionAssignmentConfig setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public InspectionAssignmentConfig setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public InspectionAssignmentConfig setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public int getAssignmentType() {
        return assignmentType;
    }

    public InspectionAssignmentConfig setAssignmentType(int assignmentType) {
        this.assignmentType = assignmentType;
        return this;
    }

    public Long getAssignmentId() {
        return assignmentId;
    }

    public InspectionAssignmentConfig setAssignmentId(Long assignmentId) {
        this.assignmentId = assignmentId;
        return this;
    }

    public InspectionEscalateDTO getInspectionEscalateDTO() {
        return inspectionEscalateDTO;
    }

    public InspectionAssignmentConfig setInspectionEscalateDTO(InspectionEscalateDTO inspectionEscalateDTO) {
        this.inspectionEscalateDTO = inspectionEscalateDTO;
        return this;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public InspectionAssignmentConfig setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InspectionAssignmentConfig inspectionAssignmentConfig = (InspectionAssignmentConfig) o;
        if (inspectionAssignmentConfig.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), inspectionAssignmentConfig.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
