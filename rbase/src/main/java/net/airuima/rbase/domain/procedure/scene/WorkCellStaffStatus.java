package net.airuima.rbase.domain.procedure.scene;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 员工工位状态Domain
 * <AUTHOR>
 * @date 2022/9/15
 */
@Schema(name = "员工工位状态(UnqualifiedEvent)", description = "员工工位状态")
@Entity
@Table(name = "procedure_work_cell_staff_status", uniqueConstraints = @UniqueConstraint(name = "procedure_work_cell_staff_status_unique", columnNames = {"staff_id","work_cell_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "workCellStaffStatusEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WorkCellStaffStatus extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    @Column(name = "staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 员工DTO
     */
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 最新登陆时间
     */
    @Schema(description = "最新登陆时间")
    @Column(name = "latest_login_time")
    private LocalDateTime latestLoginTime;

    /**
     * 最新登出时间
     */
    @Schema(description = "最新登出时间")
    @Column(name = "latest_logout_time")
    private LocalDateTime latestLogoutTime;

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellStaffStatus setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public WorkCellStaffStatus setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public WorkCellStaffStatus setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public LocalDateTime getLatestLoginTime() {
        return latestLoginTime;
    }

    public WorkCellStaffStatus setLatestLoginTime(LocalDateTime latestLoginTime) {
        this.latestLoginTime = latestLoginTime;
        return this;
    }

    public LocalDateTime getLatestLogoutTime() {
        return latestLogoutTime;
    }

    public WorkCellStaffStatus setLatestLogoutTime(LocalDateTime latestLogoutTime) {
        this.latestLogoutTime = latestLogoutTime;
        return this;
    }
}
