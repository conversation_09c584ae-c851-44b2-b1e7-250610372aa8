package net.airuima.rbase.domain.base.scene;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 区域工位
 *
 * <AUTHOR>
 * @date 2023/4/13 9:47
 **/
@Schema(name = "区域工位(AreaWorkCell)", description = "区域工位")
@Entity
@Table(name = "base_area_work_cell")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "areaWorkCellEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "area"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class AreaWorkCell extends CustomBaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 部门区域ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "部门区域ID")
    @JoinColumn(name = "area_id", nullable = false)
    private OrganizationArea area;

    /**
     * 工位ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工位ID")
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    @Transient
    private Set<WorkCell> workCellSet;

    public OrganizationArea getArea() {
        return area;
    }

    public AreaWorkCell setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public AreaWorkCell setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public AreaWorkCell setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public Set<WorkCell> getWorkCellSet() {
        return workCellSet;
    }

    public AreaWorkCell setWorkCellSet(Set<WorkCell> workCellSet) {
        this.workCellSet = workCellSet;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AreaWorkCell areaWorkCell = (AreaWorkCell) o;
        if (areaWorkCell.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), areaWorkCell.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
