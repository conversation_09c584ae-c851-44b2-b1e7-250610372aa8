
package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测个数未达规定数量时按照全检来进行；检测比例换算出来的检测数向上取整
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "产品谱系工序检测规则(PedigreeStepCheckRule)", description = "产品谱系工序检测规则")
@Entity
@Table(name = "base_pedigree_step_check_rule", uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "work_sheet_id", "work_sheet_category", "step_group_id", "step_id", "work_flow_id", "client_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序检测规则")
@NamedEntityGraph(name = "pedigreeStepCheckRuleEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode(value = "stepGroup"),
        @NamedAttributeNode(value = "workFlow"),
        @NamedAttributeNode(value = "priorityElementConfig"),
        @NamedAttributeNode(value = "step",subgraph = "stepEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")}),
        @NamedSubgraph(name = "workCellEntityGraph",
                attributeNodes = {@NamedAttributeNode("workLine"),
                        @NamedAttributeNode(value = "workStation",
                                subgraph = "workStationEntityGraph")}),
        @NamedSubgraph(name = "workStationEntityGraph",
                attributeNodes = {@NamedAttributeNode("workLine")})})
public class PedigreeStepCheckRule extends CustomBaseEntity implements Serializable {

    /**
     * 编码
     */
    @Schema(description = "编码")
    @Column(name = "code")
    private String code;

    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    /**
     * 检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)
     */
    @Schema(description = "检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 判定方式(0:数量;1:比例)
     */
    @Schema(description = "判定方式(0:数量;1:比例)")
    @Column(name = "judge_way")
    private Integer judgeWay;

    /**
     * 检测基数
     */
    @Schema(description = "检测基数")
    @Column(name = "base_number")
    private Integer baseNumber;

    /**
     * 检测比例
     */
    @Schema(description = "检测比例")
    @Column(name = "rate", nullable = false)
    private Double rate;

    /**
     * 合格比例
     */
    @Schema(description = "合格比例")
    @Column(name = "qualified_rate")
    private Double qualifiedRate;

    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    @Column(name = "qualified_number")
    private Integer qualifiedNumber;

    /**
     * 产品谱系id
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工序id
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    @JoinColumn(name = "work_sheet_id")
    @ManyToOne
    private WorkSheet workSheet;

    /**
     * 工位id
     */
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id")
    @ManyToOne
    private WorkCell workCell;

    /**
     * 工单类型(0:离线返修单;1:正常单;)
     */
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)")
    @Column(name = "work_sheet_category")
    private Integer workSheetCategory;

    /**
     * 工序组id
     */
    @Schema(description = "工序组id")
    @JoinColumn(name = "step_group_id")
    @ManyToOne
    private StepGroup stepGroup;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();
    /**
     * 工艺路线id
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note", nullable = true)
    private String note;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    @Column(name = "variety", nullable = true)
    private Integer variety;

    /**
     * 抽样方案
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "抽样方案")
    @Column(name = "sample_case_id")
    private Long sampleCaseId;

    @FetchField(mapUri = "/api/sample-cases", serviceId = "mom", paramKey = "sampleCaseId")
    @Transient
    private SampleCaseDTO sampleCase = new SampleCaseDTO();

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO varietyObj = new VarietyDTO();

    /**
     * 名称
     */
    @Schema(description = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
    @Column(name = "is_enable", nullable = false)
    private boolean isEnable;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    @Column(name = "expiry_date")
    private LocalDate expiryDate;



    /**
     * 物料属性ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料属性ID")
    @Column(name = "attribute_id")
    private Long attributeId;

    @FetchField(mapUri = "/api/material-attributes", serviceId = "mom", paramKey = "attributeId")
    @Transient
    public MaterialAttributeDTO materialAttributeDto = new MaterialAttributeDTO();

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;


    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();


    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDto = new SupplierDTO();


    public Long getAttributeId() {
        return attributeId;
    }

    public PedigreeStepCheckRule setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
        return this;
    }

    public MaterialAttributeDTO getMaterialAttributeDto() {
        return materialAttributeDto;
    }

    public PedigreeStepCheckRule setMaterialAttributeDto(MaterialAttributeDTO materialAttributeDto) {
        this.materialAttributeDto = materialAttributeDto;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public PedigreeStepCheckRule setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public PedigreeStepCheckRule setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public PedigreeStepCheckRule setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDto() {
        return supplierDto;
    }

    public PedigreeStepCheckRule setSupplierDto(SupplierDTO supplierDto) {
        this.supplierDto = supplierDto;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public PedigreeStepCheckRule setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeStepCheckRule setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public PedigreeStepCheckRule setCode(String code) {
        this.code = code;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public PedigreeStepCheckRule setCategory(int category) {
        this.category = category;
        return this;
    }

    public PedigreeStepCheckRule setBaseNumber(Integer baseNumber) {
        this.baseNumber = baseNumber;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public PedigreeStepCheckRule setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Integer getWorkSheetCategory() {
        return workSheetCategory;
    }

    public PedigreeStepCheckRule setWorkSheetCategory(Integer workSheetCategory) {
        this.workSheetCategory = workSheetCategory;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public PedigreeStepCheckRule setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public PedigreeStepCheckRule setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeStepCheckRule setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public Integer getBaseNumber() {
        return baseNumber;
    }

    public Double getRate() {
        return rate;
    }

    public PedigreeStepCheckRule setRate(Double rate) {
        this.rate = rate;
        return this;
    }

    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public PedigreeStepCheckRule setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepCheckRule setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Integer getJudgeWay() {
        return judgeWay;
    }

    public PedigreeStepCheckRule setJudgeWay(Integer judgeWay) {
        this.judgeWay = judgeWay;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public PedigreeStepCheckRule setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepCheckRule setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepCheckRule setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getNote() {
        return note;
    }

    public PedigreeStepCheckRule setNote(String note) {
        this.note = note;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public PedigreeStepCheckRule setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public Long getSampleCaseId() {
        return sampleCaseId;
    }

    public PedigreeStepCheckRule setSampleCaseId(Long sampleCaseId) {
        this.sampleCaseId = sampleCaseId;
        return this;
    }

    public SampleCaseDTO getSampleCase() {
        return sampleCase;
    }

    public PedigreeStepCheckRule setSampleCase(SampleCaseDTO sampleCase) {
        this.sampleCase = sampleCase;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public PedigreeStepCheckRule setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVarietyObj() {
        return varietyObj;
    }

    public PedigreeStepCheckRule setVarietyObj(VarietyDTO varietyObj) {
        this.varietyObj = varietyObj;
        return this;
    }

    public String getName() {
        return name;
    }

    public PedigreeStepCheckRule setName(String name) {
        this.name = name;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeStepCheckRule setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public PedigreeStepCheckRule setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepCheckRule that = (PedigreeStepCheckRule) o;
        return category == that.category && judgeWay == that.judgeWay && baseNumber == that.baseNumber && Double.compare(that.rate, rate) == 0 && Double.compare(that.qualifiedRate, qualifiedRate) == 0 && qualifiedNumber == that.qualifiedNumber && Objects.equals(pedigree, that.pedigree) && Objects.equals(step, that.step) && Objects.equals(workFlow, that.workFlow) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(category, judgeWay, baseNumber, rate, qualifiedRate, qualifiedNumber, pedigree, step, workFlow, note);
    }
}

