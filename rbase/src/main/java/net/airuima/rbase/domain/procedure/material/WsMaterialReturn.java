package net.airuima.rbase.domain.procedure.material;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Schema(name = "工单退料表(WsMaterialReturn)", description = "工单退料表")
@Entity
@Table(name = "procedure_work_sheet_material_return")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "WsMaterialReturnEntityGraph",attributeNodes = {
    @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})
})
public class WsMaterialReturn extends CustomBaseEntity implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 物料
     */
    @Schema(description = "物料", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 操作员工
     */
    @Schema(description = " 操作员工")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;


    /**
     * 操作员工DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @Column(name = "batch")
    private String batch;

    /**
     * 退料数量
     */
    @Schema(description = "退料数量")
    @Column(name = "number")
    private double number;

    /**
     * 记录日期时间
     */
    @Schema(description = "记录日期")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    /**
     * 记录日期(方便统计）
     */
    @Schema(description = "记录日期(方便统计）")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    /**
     * 退料原因
     */
    @Column(name = "note")
    @Schema(description = "退料原因")
    private String note;

    public WsMaterialReturn() {
    }

    public WsMaterialReturn(WorkSheet workSheet, Long materialId, Long operatorId,String batch, double number) {
        this.workSheet = workSheet;
        this.materialId = materialId;
        this.operatorId = operatorId;
        this.batch = batch;
        this.number = number;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsMaterialReturn setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsMaterialReturn setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsMaterialReturn setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WsMaterialReturn setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public WsMaterialReturn setNumber(double number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public WsMaterialReturn setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WsMaterialReturn setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WsMaterialReturn setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WsMaterialReturn setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WsMaterialReturn setNote(String note) {
        this.note = note;
        return this;
    }
}
