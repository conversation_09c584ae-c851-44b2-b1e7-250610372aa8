package net.airuima.rbase.domain.base.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良现象原因关系Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "不良现象原因关系(UnqualifiedItemCause)", description = "不良现象原因关系")
@Entity
@Table(name = "base_unqualified_item_cause", uniqueConstraints = @UniqueConstraint(columnNames = {"unqualified_item_id", "unqualified_cause_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "不良现象原因关系数据")
public class UnqualifiedItemCause extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不良现象ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良现象ID")
    @JoinColumn(name = "unqualified_item_id", nullable = false)
    private UnqualifiedItem unqualifiedItem;

    /**
     * 不良原因ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良原因ID")
    @JoinColumn(name = "unqualified_cause_id", nullable = false)
    private UnqualifiedCause unqualifiedCause;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public UnqualifiedItemCause setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public UnqualifiedCause getUnqualifiedCause() {
        return unqualifiedCause;
    }

    public UnqualifiedItemCause setUnqualifiedCause(UnqualifiedCause unqualifiedCause) {
        this.unqualifiedCause = unqualifiedCause;
        return this;
    }

    public String getNote() {
        return note;
    }

    public UnqualifiedItemCause setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedItemCause unqualifiedItemCause = (UnqualifiedItemCause) o;
        if (unqualifiedItemCause.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), unqualifiedItemCause.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
