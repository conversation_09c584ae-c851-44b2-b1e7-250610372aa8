package net.airuima.rbase.domain.procedure.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Schema(name = "员工产量(StaffPerform)", description = "员工产量")
@Entity
@Table(name = "procedure_staff_perform")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "staffPerformEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class StaffPerform extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "staff_id")
    private Long staffId;

    /**
     * 员工DTO
     */
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();


    /**
     * 子工单
     */
    @Schema(description = "子工单")
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 工位
     */
    @Schema(description = "工位")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;


    /**
     * 工序
     */
    @Schema(description = "工序")
    @NotNull
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工序详情id
     */
    @Schema(description = "工序详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "batch_work_detail_id")
    private Long batchWorkDetailId;

    /**
     * 容器详情id
     */
    @Schema(description = "容器详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "container_detail_id")
    private Long containerDetailId;

    /**
     * sn详情id
     */
    @Schema(description = "sn详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "sn_work_detail_id")
    private Long snWorkDetailId;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Column(name = "input_number")
    private int inputNumber;


    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Column(name = "qualified_number")
    private int qualifiedNumber;


    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    /**
     * 耗时
     */
    @Schema(description = "耗时")
    @Column(name = "work_hour")
    private double workHour;

    public Long getStaffId() {
        return staffId;
    }

    public StaffPerform setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public StaffPerform setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public StaffPerform setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public StaffPerform setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StaffPerform setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getBatchWorkDetailId() {
        return batchWorkDetailId;
    }

    public StaffPerform setBatchWorkDetailId(Long batchWorkDetailId) {
        this.batchWorkDetailId = batchWorkDetailId;
        return this;
    }

    public Long getContainerDetailId() {
        return containerDetailId;
    }

    public StaffPerform setContainerDetailId(Long containerDetailId) {
        this.containerDetailId = containerDetailId;
        return this;
    }

    public Long getSnWorkDetailId() {
        return snWorkDetailId;
    }

    public StaffPerform setSnWorkDetailId(Long snWorkDetailId) {
        this.snWorkDetailId = snWorkDetailId;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public StaffPerform setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public StaffPerform setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public StaffPerform setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public StaffPerform setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public StaffPerform setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public StaffPerform setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public double getWorkHour() {
        return workHour;
    }

    public StaffPerform setWorkHour(double workHour) {
        this.workHour = workHour;
        return this;
    }
}
