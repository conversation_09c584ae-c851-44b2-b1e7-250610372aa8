package net.airuima.rbase.domain.base.priority;

import com.alibaba.fastjson.JSON;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.constant.Constants;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 条件优先级配置Domain
 *
 * <AUTHOR>
 * @date 2022-10-21
 */
@Schema(name = "条件优先级配置(PriorityElementConfig)", description = "条件优先级配置")
@Entity
@Table(name = "base_priority_element_config")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class PriorityElementConfig extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条件对象
     */
    @NotNull
    @Schema(description = "条件对象", required = true)
    @Column(name = "target", nullable = false)
    private int target;

    /**
     * 条件组合，对应优先级元素[1,2,3]
     */
    @NotNull
    @Schema(description = "条件组合，对应优先级元素[1,2,3]", required = true)
    @Type(JsonType.class)
    @Column(name = "combination", nullable = false)
    private List<Integer> combination;

    /**
     * 优先级(数字越小优先级越高)
     */
    @Schema(description = "优先级(数字越小优先级越高)")
    @Column(name = "priority")
    private int priority;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public PriorityElementConfig(){

    }

    public PriorityElementConfig(Map<String, String> map){
        this.target = Optional.ofNullable(map.get("target")).map(Integer::parseInt).orElse(0);
        this.priority = Optional.ofNullable(map.get("priority")).map(Integer::parseInt).orElse(0);
        this.combination = Optional.ofNullable(map.get("combination")).map(cb-> JSON.parseArray(map.get("combination"),Integer.class)).orElse(List.of(1));
        this.note = map.get("note");
        this.deleted = Constants.LONG_ZERO;

    }

    public PriorityElementConfig(Long id){
        this.id = id;
    }
    public int getTarget() {
        return target;
    }

    public PriorityElementConfig setTarget(int target) {
        this.target = target;
        return this;
    }

    public List<Integer> getCombination() {
        return combination;
    }

    public PriorityElementConfig setCombination(List<Integer> combination) {
        this.combination = combination;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public PriorityElementConfig setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public String getNote() {
        return note;
    }

    public PriorityElementConfig setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PriorityElementConfig priorityElementConfig = (PriorityElementConfig) o;
        if (priorityElementConfig.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), priorityElementConfig.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
