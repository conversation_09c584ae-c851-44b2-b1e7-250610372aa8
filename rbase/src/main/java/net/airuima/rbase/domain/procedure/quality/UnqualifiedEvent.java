package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 预警停线事件表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "预警停线事件表(UnqualifiedEvent)", description = "预警停线事件表")
@Entity
@Table(name = "procedure_unqualified_event", uniqueConstraints = @UniqueConstraint(name = "unqualified_event_unique", columnNames = {"serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "unqualifiedEventEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class UnqualifiedEvent extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预警单号
     */
    @Schema(description = "预警单号")
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 事件类型0:警告1:停线
     */
    @Schema(description = "事件类型0:警告1:停线")
    @Column(name = "event_type")
    private int eventType;

    /**
     * 是否已处理(-1:审批中;0:未处理;1:已处理)
     */
    @Schema(description = "是否已处理(-1:审批中;0:未处理;1:已处理)")
    @Column(name = "status")
    private int status;

    /**
     * 责任人ID
     */
    @Schema(description = "责任人ID")
    @Column(name = "owner_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ownerId;

    /**
     * 责任人DTO
     */
    @Schema(description = "责任人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "ownerId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "owner_id")
    @Transient
    private StaffDTO ownerDto = new StaffDTO();

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "ws_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_ws_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 不良项目
     */
    @ManyToOne
    @Schema(description = "不良项目")
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    @Column(name = "qualified_rate")
    private double qualifiedRate;

    /**
     * 不良占有率
     */
    @Schema(description = "不良占有率")
    @Column(name = "unqualified_rate")
    private double unqualifiedRate;

    /**
     * 不良个数
     */
    @Schema(description = "不良数量")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 处理人ID
     */
    @Schema(description = "处理人ID")
    @Column(name = "deal_staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealStaffId;

    /**
     * 处理人DTO
     */
    @Schema(description = "处理人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "dealStaffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "deal_staff_id")
    @Transient
    private StaffDTO dealStaffDto = new StaffDTO();

    /**
     * 处理意见0:恢复生产1:暂停生产
     */
    @Schema(description = "处理意见0:恢复生产1:暂停生产")
    @Column(name = "deal_way")
    private int dealWay;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    @Column(name = "deal_time")
    private LocalDateTime dealTime;

    /**
     * 预警原因
     */
    @Schema(description = "预警原因")
    @Column(name = "reason")
    private String reason;

    /**
     * 预警原因类型
     */
    @Schema(description = "预警原因类型(0:不良超标;1:合格率不达标)")
    @Column(name = "reason_type")
    private int reasonType;

    /**
     * 发生时间
     */
    @Schema(description = "发生时间")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    public String getSerialNumber() {
        return serialNumber;
    }

    public UnqualifiedEvent setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public int getEventType() {
        return eventType;
    }

    public UnqualifiedEvent setEventType(int eventType) {
        this.eventType = eventType;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public UnqualifiedEvent setStatus(int status) {
        this.status = status;
        return this;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public UnqualifiedEvent setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public UnqualifiedEvent setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public UnqualifiedEvent setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public UnqualifiedEvent setStep(Step step) {
        this.step = step;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public UnqualifiedEvent setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public UnqualifiedEvent setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public double getQualifiedRate() {
        return qualifiedRate;
    }

    public UnqualifiedEvent setQualifiedRate(double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public Long getDealStaffId() {
        return dealStaffId;
    }

    public UnqualifiedEvent setDealStaffId(Long dealStaffId) {
        this.dealStaffId = dealStaffId;
        return this;
    }

    public StaffDTO getOwnerDto() {
        return ownerDto;
    }

    public UnqualifiedEvent setOwnerDto(StaffDTO ownerDto) {
        this.ownerDto = ownerDto;
        return this;
    }

    public StaffDTO getDealStaffDto() {
        return dealStaffDto;
    }

    public UnqualifiedEvent setDealStaffDto(StaffDTO dealStaffDto) {
        this.dealStaffDto = dealStaffDto;
        return this;
    }

    public int getDealWay() {
        return dealWay;
    }

    public UnqualifiedEvent setDealWay(int dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public LocalDateTime getDealTime() {
        return dealTime;
    }

    public UnqualifiedEvent setDealTime(LocalDateTime dealTime) {
        this.dealTime = dealTime;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public UnqualifiedEvent setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public int getReasonType() {
        return reasonType;
    }

    public UnqualifiedEvent setReasonType(int reasonType) {
        this.reasonType = reasonType;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public UnqualifiedEvent setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public double getUnqualifiedRate() {
        return unqualifiedRate;
    }

    public UnqualifiedEvent setUnqualifiedRate(double unqualifiedRate) {
        this.unqualifiedRate = unqualifiedRate;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public UnqualifiedEvent setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedEvent UnqualifiedEvent = (UnqualifiedEvent) o;
        if (UnqualifiedEvent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), UnqualifiedEvent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
