package net.airuima.rbase.domain.base.process;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线配置
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Schema(name = "转工艺路线配置", description = "转工艺路线配置")
@Entity
@Table(name = "base_work_flow_convert_config",uniqueConstraints = @UniqueConstraint(columnNames = {"client_id","pedigree_id", "origin_work_flow_id", "step_id","target_work_flow_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "转工艺路线配置")
@NamedEntityGraph(name = "convertWorkflowEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree")})
public class WorkFlowConvertConfig extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系id
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id", nullable = false)
    private Pedigree pedigree;

    /**
     * 原始工艺路线id
     */
    @ManyToOne
    @Schema(description = "原始工艺路线id")
    @JoinColumn(name = "origin_work_flow_id")
    private WorkFlow originWorkFlow;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;


    /**
     * 目标工艺路线id
     */
    @ManyToOne
    @Schema(description = "目标工艺路线id")
    @JoinColumn(name = "target_work_flow_id", nullable = false)
    private WorkFlow targetWorkFlow;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "enable", nullable = false)
    private Boolean enable;


    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;


    public Pedigree getPedigree() {
        return pedigree;
    }

    public WorkFlowConvertConfig setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getOriginWorkFlow() {
        return originWorkFlow;
    }

    public WorkFlowConvertConfig setOriginWorkFlow(WorkFlow originWorkFlow) {
        this.originWorkFlow = originWorkFlow;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public WorkFlowConvertConfig setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public WorkFlowConvertConfig setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkFlowConvertConfig setStep(Step step) {
        this.step = step;
        return this;
    }

    public WorkFlow getTargetWorkFlow() {
        return targetWorkFlow;
    }

    public WorkFlowConvertConfig setTargetWorkFlow(WorkFlow targetWorkFlow) {
        this.targetWorkFlow = targetWorkFlow;
        return this;
    }

    public Boolean getEnable() {
        return enable;
    }

    public WorkFlowConvertConfig setEnable(Boolean enable) {
        this.enable = enable;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public WorkFlowConvertConfig setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkFlowConvertConfig instance = (WorkFlowConvertConfig) o;
        if (instance.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), instance.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
