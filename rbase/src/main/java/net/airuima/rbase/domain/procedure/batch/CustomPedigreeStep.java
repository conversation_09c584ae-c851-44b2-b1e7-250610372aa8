package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系最新下单定制工序Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系最新下单定制工序(CustomPedigreeStep)", description = "产品谱系最新下单定制工序实体")
@Entity
@Table(name = "procedure_custom_pedigree_step", uniqueConstraints = @UniqueConstraint(name = "procedure_custom_pedigree_step_unique",
        columnNames = {"pedigree_id", "step_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "customPedigreeStepEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class CustomPedigreeStep extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系", required = true)
    @JoinColumn(name = "pedigree_id", nullable = false)
    private Pedigree pedigree;

    /**
     * 工序
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 前置工序列表，分号隔开
     */
    @Schema(description = "前置工序列表，分号隔开")
    @Column(name = "pre_step_id")
    private String preStepId;

    /**
     * 后置工序列表，分号隔开
     */
    @Schema(description = "后置工序列表，分号隔开")
    @Column(name = "after_step_id")
    private String afterStepId;

    /**
     * 流程框图
     */
    @NotNull
    @ManyToOne
    @Schema(description = "流程框图", required = true)
    @JoinColumn(name = "workflow_id", nullable = false)
    private WorkFlow workFlow;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    @Column(name = "category")
    private int category;

    /**
     * 请求模式
     */
    @Schema(description = "请求模式")
    @Column(name = "request_mode")
    private int requestMode;

    /**
     * 管控模式(0:批量;1:单支)
     */
    @Schema(description = "管控模式(0:批量;1:单支)")
    @Column(name = "control_mode")
    private int controlMode;

    /**
     * 是否管控物料(0:不管控;1:管控)
     */
    @Schema(description = "是否管控物料(0:不管控;1:管控)")
    @Column(name = "is_control_material")
    private boolean isControlMaterial;

    /**
     * 是否绑定容器(0:否;1:是)
     */
    @Schema(description = "是否绑定容器(0:否;1:是)")
    @Column(name = "is_bind_container")
    private boolean isBindContainer;

    /**
     * 投产比例
     */
    @Schema(description = "投产比例")
    @Column(name = "input_rate")
    @ColumnDefault("1")
    private double inputRate;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public CustomPedigreeStep setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public CustomPedigreeStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getPreStepId() {
        return preStepId;
    }

    public CustomPedigreeStep setPreStepId(String preStepId) {
        this.preStepId = preStepId;
        return this;
    }

    public String getAfterStepId() {
        return afterStepId;
    }

    public CustomPedigreeStep setAfterStepId(String afterStepId) {
        this.afterStepId = afterStepId;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public CustomPedigreeStep setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public CustomPedigreeStep setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getRequestMode() {
        return requestMode;
    }

    public CustomPedigreeStep setRequestMode(int requestMode) {
        this.requestMode = requestMode;
        return this;
    }

    public int getControlMode() {
        return controlMode;
    }

    public CustomPedigreeStep setControlMode(int controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public boolean getIsControlMaterial() {
        return isControlMaterial;
    }

    public CustomPedigreeStep setIsControlMaterial(boolean isControlMaterial) {
        this.isControlMaterial = isControlMaterial;
        return this;
    }

    public boolean getIsBindContainer() {
        return isBindContainer;
    }

    public CustomPedigreeStep setIsBindContainer(boolean isBindContainer) {
        this.isBindContainer = isBindContainer;
        return this;
    }

    public double getInputRate() {
        return inputRate;
    }

    public CustomPedigreeStep setInputRate(double inputRate) {
        this.inputRate = inputRate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CustomPedigreeStep customPedigreeStep = (CustomPedigreeStep) o;
        if (customPedigreeStep.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), customPedigreeStep.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
