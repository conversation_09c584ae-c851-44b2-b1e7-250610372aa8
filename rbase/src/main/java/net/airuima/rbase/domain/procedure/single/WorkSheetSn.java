package net.airuima.rbase.domain.procedure.single;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@Schema(name = "工单SN关联(WorkSheetSn)", description = "工单SN关联实体")
@Entity
@Table(name = "procedure_work_sheet_sn", uniqueConstraints = {@UniqueConstraint(name = "procedure_work_sheet_sn_unique_index", columnNames = {"sn",  "deleted"})})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "workSheetSnEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")})})
public class WorkSheetSn extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN")
    @Column(name = "sn", nullable = false)
    private String sn;

    public WorkSheetSn() {

    }

    public WorkSheetSn(WorkSheet workSheet, SubWorkSheet subWorkSheet, String sn) {
        this.workSheet = workSheet;
        this.subWorkSheet = subWorkSheet;
        this.sn = sn;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetSn setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WorkSheetSn setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public WorkSheetSn setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
