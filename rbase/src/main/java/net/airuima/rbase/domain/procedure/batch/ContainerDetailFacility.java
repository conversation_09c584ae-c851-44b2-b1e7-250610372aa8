package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器设备生产详情Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "容器设备生产详情(ContainerDetailEquipment)", description = "容器设备生产详情")
@Entity
@Table(name = "procedure_container_detail_facility", uniqueConstraints = @UniqueConstraint(name = "procedure_container_detail_facility_unique_index", columnNames = {"container_detail_id", "facility_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "containerDetailEquipmentEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "containerDetail",subgraph = "containerDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "containerDetailEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph")
                                ,@NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class ContainerDetailFacility extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器详情id
     */
    @ManyToOne
    @Schema(description = "容器详情id")
    @JoinColumn(name = "container_detail_id")
    
    private ContainerDetail containerDetail;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @Column(name = "facility_id")
    private Long facilityId;

    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    @Transient
    private FacilityDTO facilityDto = new FacilityDTO();

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public ContainerDetailFacility setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public ContainerDetailFacility setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public ContainerDetailFacility setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContainerDetailFacility containerDetailFacility = (ContainerDetailFacility) o;
        if (containerDetailFacility.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), containerDetailFacility.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
