package net.airuima.rbase.domain.base.scene;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位员工关系Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工位员工关系(WorkCellStaff)", description = "工位员工关系")
@Entity
@Table(name = "base_work_cell_staff", uniqueConstraints = @UniqueConstraint(columnNames = {"staff_id", "work_cell_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "工位员工数据")
@NamedEntityGraph(name = "workCellStaffEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WorkCellStaff extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull
    @Schema(description = "员工id")
    @Column(name = "staff_id", nullable = false)
    private Long staffId;

    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 工位id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Column(name = "is_enable")
    private boolean isEnable;

    /**
     * 员工对应的所有工位
     */
    @Transient
    private Set<WorkCell> workCells;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Set<WorkCell> getWorkCells() {
        return workCells;
    }

    public WorkCellStaff setWorkCells(Set<WorkCell> workCells) {
        this.workCells = workCells;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public WorkCellStaff setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public WorkCellStaff setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellStaff setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkCellStaff setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkCellStaff workCellStaff = (WorkCellStaff) o;
        if (workCellStaff.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workCellStaff.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
