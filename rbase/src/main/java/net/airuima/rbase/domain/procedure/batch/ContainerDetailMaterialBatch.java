package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情物料批次表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "容器工作详情物料批次表(ContainerDetailMaterialBatch)", description = "容器工作详情物料批次表")
@Entity
@Table(name = "procedure_container_detail_material_batch")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "containerDetailMaterialBatchEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "containerDetail",subgraph = "containerDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "containerDetailEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph")
                                ,@NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class ContainerDetailMaterialBatch extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器详情ID
     */
    @ManyToOne
    @Schema(description = "容器详情ID")
    @JoinColumn(name = "container_detail_id")
    private ContainerDetail containerDetail;

    /**
     * 物料ID
     */
    @Schema(description = "物料ID", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    @Column(name = "batch")
    private String batch;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 用料数量
     */
    @Schema(description = "用料数量")
    @Column(name = "number")
    private double number;

    /**
     * 扣料方式 0：不扣 1：工单扣料，2：工位扣料
     */
    @Schema(description = "扣料方式")
    @Column(name = "type")
    private int type;

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public ContainerDetailMaterialBatch setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public ContainerDetailMaterialBatch setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public ContainerDetailMaterialBatch setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public ContainerDetailMaterialBatch setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public ContainerDetailMaterialBatch setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public ContainerDetailMaterialBatch setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public ContainerDetailMaterialBatch setNumber(double number) {
        this.number = number;
        return this;
    }

    public int getType() {
        return type;
    }

    public ContainerDetailMaterialBatch setType(int type) {
        this.type = type;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContainerDetailMaterialBatch ContainerDetailMaterialBatch = (ContainerDetailMaterialBatch) o;
        if (ContainerDetailMaterialBatch.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), ContainerDetailMaterialBatch.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
