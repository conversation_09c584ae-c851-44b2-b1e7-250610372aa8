package net.airuima.rbase.domain.procedure.quality;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Cache;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 单支FPP状态Domain
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "单支FPP状态(SnStepFppStatus)", description = "单支FPP状态实体")
@Entity
@Table(name = "procedure_sn_step_fpp_status", uniqueConstraints = @UniqueConstraint(name = "procedure_sn_step_fpp_status_unique",
        columnNames = {"sn", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class SnStepFppStatus extends CustomBaseEntity implements Serializable {

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;


    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 当前工艺路线
     */
    @ManyToOne
    @Schema(description = "当前工艺路线")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;


    /**
     * 当前测试工序
     */
    @ManyToOne
    @Schema(description = "当前测试工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 当前测试总次数
     */
    @Schema(description = "当前测试总次数")
    @Column(name = "test_count")
    private int testCount;


    /**
     * 返修次数
     */
    @Schema(description = "返修次数")
    @Column(name = "rework_time")
    private int reworkTime;


    /**
     * 状态
     */
    @Schema(description = "状态(0:测试中,1：测试结果合格,2：测试结果不合格，3：超次数上限不合格)")
    @Column(name = "status")
    private int status;

    /**
     * 测试结果列表
     */
    @Schema(description = "测试结果列表")
    @Column(name = "latest_result")
    @Type(JsonType.class)
    private List<Boolean> latestResults = new ArrayList<>();

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    public String getSn() {
        return sn;
    }

    public SnStepFppStatus setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SnStepFppStatus setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SnStepFppStatus setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public SnStepFppStatus setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public SnStepFppStatus setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getTestCount() {
        return testCount;
    }

    public SnStepFppStatus setTestCount(int testCount) {
        this.testCount = testCount;
        return this;
    }

    public int getReworkTime() {
        return reworkTime;
    }

    public SnStepFppStatus setReworkTime(int reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SnStepFppStatus setStatus(int status) {
        this.status = status;
        return this;
    }

    public List<Boolean> getLatestResults() {
        return latestResults;
    }

    public SnStepFppStatus setLatestResults(List<Boolean> latestResults) {
        this.latestResults = latestResults;
        return this;
    }
    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public SnStepFppStatus setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SnStepFppStatus that)) return false;
        return testCount == that.testCount && reworkTime == that.reworkTime && status == that.status && Objects.equals(sn, that.sn) && Objects.equals(workSheet, that.workSheet) && Objects.equals(subWorkSheet, that.subWorkSheet) && Objects.equals(workFlow, that.workFlow) && Objects.equals(step, that.step) && Objects.equals(latestResults, that.latestResults) && Objects.equals(recordDate, that.recordDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sn, workSheet, subWorkSheet, workFlow, step, testCount, reworkTime, status, latestResults, recordDate);
    }
}
