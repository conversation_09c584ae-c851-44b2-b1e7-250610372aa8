package net.airuima.rbase.domain.base.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.process.StepGroup;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Schema(name = "产品谱系工序目标良率(PedigreeStepPassRate)", description = "产品谱系工序目标良率")
@Entity
@Table(name = "base_pedigree_step_pass_rate")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "产品谱系工序目标良率")
@NamedEntityGraph(name = "pedigreeStepPassRateEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class PedigreeStepPassRate extends CustomBaseEntity implements Serializable {

    /**
     * 产品谱系ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系ID")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工序组别
     */
    @ManyToOne
    @Schema(description = "工序组别")
    @JoinColumn(name = "step_group_id")
    private StepGroup stepGroup;

    /**
     * 目标合格率
     */
    @Schema(description = "目标合格率")
    @Column(name = "target_pass_rate")
    private double targetPassRate;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepPassRate setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public PedigreeStepPassRate setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    public double getTargetPassRate() {
        return targetPassRate;
    }

    public PedigreeStepPassRate setTargetPassRate(double targetPassRate) {
        this.targetPassRate = targetPassRate;
        return this;
    }
}
