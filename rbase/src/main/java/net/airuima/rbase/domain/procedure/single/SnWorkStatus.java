package net.airuima.rbase.domain.procedure.single;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产状态Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "单支生产状态(SnWorkStatus)", description = "单支生产状态实体")
@Entity
@Table(name = "procedure_sn_work_status", uniqueConstraints = @UniqueConstraint(name = "procedure_sn_work_status_ws_unique",
        columnNames = {"sn", "work_sheet_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "snWorkStatusEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "latestSnWorkDetail",subgraph = "snWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "snWorkDetailEntityGraph",
                        attributeNodes = {
                                @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class SnWorkStatus extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * YSN
     */
    @Schema(description = "YSN")
    @Column(name = "ysn")
    private String ysn;

    /**
     * 返修次数
     */
    @Schema(description = "返修次数")
    @Column(name = "rework_time")
    private int reworkTime;

    /**
     * 生产状态: 0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废
     */
    @Schema(description = "生产状态: 0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废;6-待维修分析;7,退库")
    @Column(name = "status")
    private int status;

    /**
     * 是否工单详情(1:更新工单详情数据;0:不更新工单详情数据)
     */
    @Schema(description = "是否工单详情(1:更新工单详情数据;0:不更新工单详情数据)")
    @Column(name = "is_update_batch_work_detail")
    private boolean isUpdateBatchWorkDetail;

    /**
     * 生产开始时间
     */
    @Schema(description = "生产开始时间")
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 返修开始时间
     */
    @Schema(description = "返修开始时间")
    @Column(name = "rework_start_date")
    private LocalDateTime reworkStartDate;

    /**
     * 最终完成时间
     */
    @Schema(description = "最终完成时间")
    @Column(name = "end_date")
    private LocalDateTime endDate;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 最新完成SN工作详情
     */
    @ManyToOne
    @Schema(description = "最新完成SN工作详情")
    @JoinColumn(name = "latest_sn_work_detail_id")
    private SnWorkDetail latestSnWorkDetail;

    /**
     * 当前流程框图
     */
    @ManyToOne
    @Schema(description = "当期流程框图")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 最新不合格SN详情
     */
    @ManyToOne
    @Schema(description = "最新不合格SN详情")
    @JoinColumn(name = "latest_rework_sn_work_detail_id")
    private SnWorkDetail latestReworkSnWorkDetail;

    @ManyToOne
    @Schema(description = "最新不良项目")
    @JoinColumn(name = "latest_unqualified_item_id")
    private UnqualifiedItem latestUnqualifiedItem;


    public String getSn() {
        return sn;
    }

    public SnWorkStatus setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getYsn() {
        return ysn;
    }

    public SnWorkStatus setYsn(String ysn) {
        this.ysn = ysn;
        return this;
    }

    public int getReworkTime() {
        return reworkTime;
    }

    public SnWorkStatus setReworkTime(int reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SnWorkStatus setStatus(int status) {
        this.status = status;
        return this;
    }

    public boolean getIsUpdateBatchWorkDetail() {
        return isUpdateBatchWorkDetail;
    }

    public SnWorkStatus setIsUpdateBatchWorkDetail(boolean isUpdateBatchWorkDetail) {
        this.isUpdateBatchWorkDetail = isUpdateBatchWorkDetail;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public SnWorkStatus setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getReworkStartDate() {
        return reworkStartDate;
    }

    public SnWorkStatus setReworkStartDate(LocalDateTime reworkStartDate) {
        this.reworkStartDate = reworkStartDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public SnWorkStatus setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SnWorkStatus setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SnWorkStatus setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public SnWorkDetail getLatestSnWorkDetail() {
        return latestSnWorkDetail;
    }

    public SnWorkStatus setLatestSnWorkDetail(SnWorkDetail latestSnWorkDetail) {
        this.latestSnWorkDetail = latestSnWorkDetail;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public SnWorkStatus setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public SnWorkDetail getLatestReworkSnWorkDetail() {
        return latestReworkSnWorkDetail;
    }

    public SnWorkStatus setLatestReworkSnWorkDetail(SnWorkDetail latestReworkSnWorkDetail) {
        this.latestReworkSnWorkDetail = latestReworkSnWorkDetail;
        return this;
    }

    public UnqualifiedItem getLatestUnqualifiedItem() {
        return latestUnqualifiedItem;
    }

    public SnWorkStatus setLatestUnqualifiedItem(UnqualifiedItem latestUnqualifiedItem) {
        this.latestUnqualifiedItem = latestUnqualifiedItem;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnWorkStatus snWorkStatus = (SnWorkStatus) o;
        if (snWorkStatus.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snWorkStatus.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
