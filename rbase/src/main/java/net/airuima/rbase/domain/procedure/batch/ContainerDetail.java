package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.TeamDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器生产详情Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "容器生产详情(ContainerDetail)", description = "容器生产详情")
@Entity
@Table(name = "procedure_container_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "containerDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class ContainerDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量工作详情id
     */
    @ManyToOne
    @Schema(description = "批量工作详情id")
    @JoinColumn(name = "batch_work_detail_id")
    private BatchWorkDetail batchWorkDetail;

    /**
     * 容器id
     */
    @ManyToOne
    @Schema(description = "容器id")
    @JoinColumn(name = "container_id")
    private Container container;

    /**
     * 操作员工id
     */
    @Schema(description = "操作员工id")
    @Column(name = "staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 班组ID
     */
    @Schema(description = "班组ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "team_id")
    private Long teamId;

    /**
     * 班组DTO
     */
    @FetchField(serviceName = "teamService", paramKey = "teamId")
    @Transient
    private TeamDTO teamDTO = new TeamDTO();

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    /**
     * 绑定日期
     */
    @Schema(description = "绑定日期")
    @Column(name = "bind_time")
    private LocalDateTime bindTime;

    /**
     * 解绑日期
     */
    @Schema(description = "解绑日期")
    @Column(name = "unbind_time")
    private LocalDateTime unbindTime;

    /**
     * 绑定状态
     */
    @Schema(description = "绑定状态(0:解绑;1:绑定)")
    @Column(name = "status")
    private int status;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Column(name = "input_number")
    private int inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 待流转数量
     */
    @Schema(description = "待流转数量")
    @Column(name = "transfer_number")
    private int transferNumber;

    /**
     * 前置容器详情id
     */
    @Schema(description = "前置容器详情DTO")
    @JsonIgnore
    @Type(JsonType.class)
    @Column(name = "pre_container_detail_info")
    private List<PreContainerDetailInfo> preContainerDetailInfoList;

    /**
     * 工位id
     */
    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;
    /**
     * 原始容器列表
     */
    @Schema(description = "原始容器列表")
    @Column(name = "pre_container_code")
    private String preContainerCodeList;

    /**
     * 转换容器列表
     */
    @Schema(description = "转换容器列表")
    @Column(name = "after_container_code")
    private String afterContainerCodeList;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Schema(description = "容器生产状态：0:正常态,1:待维修")
    @Column(name = "main_tain_status")
    private int maintainStatus;

    /**
     * 动态元数据列数据DTO
     */
    @Schema(description = "动态元数据列数据DTO")
    @Column(name = "dynamic_data")
    @Type(JsonType.class)
    private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO;

    public Long getTeamId() {
        return teamId;
    }

    public ContainerDetail setTeamId(Long teamId) {
        this.teamId = teamId;
        return this;
    }

    public TeamDTO getTeamDTO() {
        return teamDTO;
    }

    public ContainerDetail setTeamDTO(TeamDTO teamDTO) {
        this.teamDTO = teamDTO;
        return this;
    }

    public int getMaintainStatus() {
        return maintainStatus;
    }

    public ContainerDetail setMaintainStatus(int maintainStatus) {
        this.maintainStatus = maintainStatus;
        return this;
    }

    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public ContainerDetail setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }

    public Container getContainer() {
        return container;
    }

    public ContainerDetail setContainer(Container container) {
        this.container = container;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public ContainerDetail setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public ContainerDetail setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public ContainerDetail setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public ContainerDetail setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public ContainerDetail setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
        return this;
    }

    public LocalDateTime getUnbindTime() {
        return unbindTime;
    }

    public ContainerDetail setUnbindTime(LocalDateTime unbindTime) {
        this.unbindTime = unbindTime;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public ContainerDetail setStatus(int status) {
        this.status = status;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public ContainerDetail setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ContainerDetail setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public ContainerDetail setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getTransferNumber() {
        return transferNumber;
    }

    public ContainerDetail setTransferNumber(int transferNumber) {
        this.transferNumber = transferNumber;
        return this;
    }

    public List<PreContainerDetailInfo> getPreContainerDetailInfoList() {
        return preContainerDetailInfoList;
    }

    public ContainerDetail setPreContainerDetailInfoList(List<PreContainerDetailInfo> preContainerDetailInfoList) {
        this.preContainerDetailInfoList = preContainerDetailInfoList;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public ContainerDetail setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public String getPreContainerCodeList() {
        return preContainerCodeList;
    }

    public ContainerDetail setPreContainerCodeList(String preContainerCodeList) {
        this.preContainerCodeList = preContainerCodeList;
        return this;
    }

    public String getAfterContainerCodeList() {
        return afterContainerCodeList;
    }

    public ContainerDetail setAfterContainerCodeList(String afterContainerCodeList) {
        this.afterContainerCodeList = afterContainerCodeList;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public ContainerDetail setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDTO() {
        return stepDynamicDataColumnGetDTO;
    }

    public ContainerDetail setStepDynamicDataColumnGetDTO(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO) {
        this.stepDynamicDataColumnGetDTO = stepDynamicDataColumnGetDTO;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContainerDetail ContainerDetail = (ContainerDetail) o;
        if (ContainerDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), ContainerDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
