package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良管理记录详情表Domain
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Schema(name = "不良管理记录详情表(InspectUnqualifiedDetail)", description = "不良管理记录详情表")
@Entity
@Table(name = "procedure_inspect_unqualified_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@NamedEntityGraph(
        name = "inspectUnqualifiedDetailEntityGraph",
        attributeNodes = {
                @NamedAttributeNode(value = "inspectUnqualified", subgraph = "inspectUnqualifiedEntityGraph")
        },
        subgraphs = {
                @NamedSubgraph(name = "inspectUnqualifiedEntityGraph", attributeNodes = {@NamedAttributeNode(value = "checkHistory", subgraph = "checkHistoryEntityGraph")}),
                @NamedSubgraph(name = "checkHistoryEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
                                @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
                                @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph", attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})
        }
)
public class InspectUnqualifiedDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不良品管理记录
     */
    @NotNull
    @Schema(description = "不良品管理记录", required = true)
    @ManyToOne
    @JoinColumn(name = "inspect_unqualified_id", nullable = false)
    private InspectUnqualified inspectUnqualified;

    /**
     * sn
     */
    @Schema(description = "sn")
    @Column(name = "sn")
    private String sn;

    /**
     * 是否为虚拟SN(0:否;1:是)
     */
    @Schema(description = "是否为虚拟SN(0:否;1:是)")
    @Column(name = "is_virtual", nullable = false)
    private boolean virtual;

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    @ManyToOne
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 检测项目
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "检测项目")
    @Column(name = "check_item_id")
    private Long checkItemId;

    @FetchField(mapUri = "/api/check-items", serviceId = "mom", paramKey = "checkItemId")
    @Transient
    private CheckItemDTO checkItem = new CheckItemDTO();

    /**
     * 缺陷原因
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "缺陷原因")
    @Column(name = "defect_id")
    private Long defectId;

    @FetchField(mapUri = "/api/defects", serviceId = "mom", paramKey = "defectId")
    @Transient
    private DefectDTO defect = new DefectDTO();

    /**
     * 检验数据
     */
    @Schema(description = "检验数据")
    @Column(name = "check_data")
    private String checkData;

    /**
     * 处理结果:0不合格，1合格
     */
    @Schema(description = "处理结果:0不合格，1合格", required = true)
    @Column(name = "check_result", nullable = false)
    private boolean checkResult;

    public boolean getVirtual() {
        return virtual;
    }

    public InspectUnqualifiedDetail setVirtual(boolean virtual) {
        this.virtual = virtual;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public InspectUnqualifiedDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public InspectUnqualifiedDetail setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public InspectUnqualified getInspectUnqualified() {
        return inspectUnqualified;
    }

    public InspectUnqualifiedDetail setInspectUnqualified(InspectUnqualified inspectUnqualified) {
        this.inspectUnqualified = inspectUnqualified;
        return this;
    }

    public Long getCheckItemId() {
        return checkItemId;
    }

    public InspectUnqualifiedDetail setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public InspectUnqualifiedDetail setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public Long getDefectId() {
        return defectId;
    }

    public InspectUnqualifiedDetail setDefectId(Long defectId) {
        this.defectId = defectId;
        return this;
    }

    public DefectDTO getDefect() {
        return defect;
    }

    public InspectUnqualifiedDetail setDefect(DefectDTO defect) {
        this.defect = defect;
        return this;
    }

    public String getCheckData() {
        return checkData;
    }

    public InspectUnqualifiedDetail setCheckData(String checkData) {
        this.checkData = checkData;
        return this;
    }

    public boolean getCheckResult() {
        return checkResult;
    }

    public InspectUnqualifiedDetail setCheckResult(boolean checkResult) {
        this.checkResult = checkResult;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InspectUnqualifiedDetail inspectUnqualifiedDetail = (InspectUnqualifiedDetail) o;
        if (inspectUnqualifiedDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), inspectUnqualifiedDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
