package net.airuima.rbase.domain.procedure.single;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.TeamDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支工序生产详情Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "单支工序生产详情(SnWorkDetail)", description = "单支工序生产详情实体")
@Entity
@Table(name = "procedure_sn_work_detail", uniqueConstraints = {@UniqueConstraint(name = "procedure_sn_work_detail_sn_work_sheet_id_step_id_unique",
        columnNames = {"sn", "work_sheet_id", "rework_time","step_id", "deleted"}), @UniqueConstraint(name = "procedure_sn_work_detail_sn_sub_work_sheet_id_step_id_unique",
        columnNames = {"sn", "sub_work_sheet_id","rework_time", "step_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "snWorkDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class SnWorkDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器工作详情id
     */
    @ManyToOne
    @Schema(description = "容器工作详情id")
    @JoinColumn(name = "container_work_detail_id")
    private ContainerDetail containerDetail;

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * YSN
     */
    @Schema(description = "YSN")
    @Column(name = "ysn")
    private String ysn;


    /**
     * 生产开始时间
     */
    @Schema(description = "生产开始时间")
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 生产完成时间
     */
    @Schema(description = "生产完成时间")
    @Column(name = "end_date")
    private LocalDateTime endDate;

    /**
     * 生产完成耗时
     */
    @Schema(description = "生产完成耗时")
    @Column(name = "work_hour")
    private double workHour;

    /**
     * 返修次数
     */
    @Schema(description = "返修次数")
    @Column(name = "rework_time")
    private int reworkTime;

    /**
     * 结果(0:不合格;1:合格)
     */
    @Schema(description = "结果(0:不合格;1:合格)")
    @Column(name = "result")
    private int result;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    @Column(name = "operator_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 班组ID
     */
    @Schema(description = "班组ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "team_id")
    private Long teamId;

    /**
     * 班组DTO
     */
    @FetchField(serviceName = "teamService", paramKey = "teamId")
    @Transient
    private TeamDTO teamDTO = new TeamDTO();

    /**
     * 工位
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工位", required = true)
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 不良项目
     */
    @ManyToOne
    @Schema(description = "不良项目", required = true)
    @JoinColumn(name = "unqualified_item_id", nullable = true)
    private UnqualifiedItem unqualifiedItem;

    /**
     * 动态元数据列数据DTO
     */
    @Schema(description = "动态元数据列数据DTO")
    @Column(name = "dynamic_data")
    @Type(JsonType.class)
    private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO;

    public Long getTeamId() {
        return teamId;
    }

    public SnWorkDetail setTeamId(Long teamId) {
        this.teamId = teamId;
        return this;
    }

    public TeamDTO getTeamDTO() {
        return teamDTO;
    }

    public SnWorkDetail setTeamDTO(TeamDTO teamDTO) {
        this.teamDTO = teamDTO;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public SnWorkDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getYsn() {
        return ysn;
    }

    public SnWorkDetail setYsn(String ysn) {
        this.ysn = ysn;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public SnWorkDetail setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public SnWorkDetail setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public double getWorkHour() {
        return workHour;
    }

    public SnWorkDetail setWorkHour(double workHour) {
        this.workHour = workHour;
        return this;
    }

    public int getReworkTime() {
        return reworkTime;
    }

    public SnWorkDetail setReworkTime(int reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public int getResult() {
        return result;
    }

    public SnWorkDetail setResult(int result) {
        this.result = result;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public SnWorkDetail setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }


    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SnWorkDetail setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SnWorkDetail setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public SnWorkDetail setStep(Step step) {
        this.step = step;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public SnWorkDetail setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public SnWorkDetail setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public SnWorkDetail setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public SnWorkDetail setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDTO() {
        return stepDynamicDataColumnGetDTO;
    }

    public SnWorkDetail setStepDynamicDataColumnGetDTO(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO) {
        this.stepDynamicDataColumnGetDTO = stepDynamicDataColumnGetDTO;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnWorkDetail snWorkDetail = (SnWorkDetail) o;
        if (snWorkDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snWorkDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
