package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.qms.VarietyDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 记录工位首中末最新检测状态及结果，用于配合检测时机表做发起判断用，适用于首检、过程检
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "检测结果最新状态(LatestCheckResult)", description = "检测结果最新状态")
@Entity
@Table(name = "procedure_latest_check_result", uniqueConstraints = @UniqueConstraint(columnNames = {"work_cell_id","category","variety","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "latestCheckResultEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
@FetchEntity
public class LatestCheckResult extends CustomBaseEntity implements Serializable {

    /**
     * 最新检测单据号
     */
    @Schema(description = "最新检测单据号")
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 检测类型
     */
    @Schema(description = "检测类型(首检0/巡检1)")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示")
    @Column(name = "display")
    private boolean display = true;

    /**
     * 更新日期
     */
    @NotNull
    @Schema(description = "更新日期")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    /**
     * 下次检测日期
     */
    @Schema(description = "下次检测日期")
    @Column(name = "next_check_date")
    private LocalDateTime nextCheckDate;

    /**
     * 检测结果(0:不合格;1:合格)
     */
    @Schema(description = "检测结果(0:不合格;1:合格)")
    @Column(name = "result", nullable = false)
    private boolean result;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    @Column(name = "variety")
    private Integer variety;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO varietyObj = new VarietyDTO();

    /**
     * 处理方式(待处理0/通过1/重检2/放行3)
     */
    @Schema(description = "处理方式(待处理0/通过1/重检2/放行3)", required = true)
    @Column(name = "deal_way", nullable = false)
    private int dealWay;

    /**
     * 是否已处理结果(0:否;1:是)
     */
    @Schema(description = "是否已处理结果(0:否;1:是)")
    @Column(name = "status", nullable = false)
    private boolean status;

    /**
     * 延长时间
     */
    @Schema(description = "延长时间")
    @Column(name = "extend_time")
    private double extendTime;

    public double getExtendTime() {
        return extendTime;
    }

    public LatestCheckResult setExtendTime(double extendTime) {
        this.extendTime = extendTime;
        return this;
    }

    public boolean getStatus() {
        return status;
    }

    public LatestCheckResult setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public LatestCheckResult setCategory(int category) {
        this.category = category;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public LatestCheckResult setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public LatestCheckResult setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getNextCheckDate() {
        return nextCheckDate;
    }

    public LatestCheckResult setNextCheckDate(LocalDateTime nextCheckDate) {
        this.nextCheckDate = nextCheckDate;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public LatestCheckResult setResult(boolean result) {
        this.result = result;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public LatestCheckResult setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public LatestCheckResult setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public LatestCheckResult setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public LatestCheckResult setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVarietyObj() {
        return varietyObj;
    }

    public LatestCheckResult setVarietyObj(VarietyDTO varietyObj) {
        this.varietyObj = varietyObj;
        return this;
    }

    public boolean getDisplay() {
        return display;
    }

    public LatestCheckResult setDisplay(boolean display) {
        this.display = display;
        return this;
    }

    public int getDealWay() {
        return dealWay;
    }

    public LatestCheckResult setDealWay(int dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public LatestCheckResult setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LatestCheckResult that = (LatestCheckResult) o;
        return category == that.category &&
                result == that.result &&
                Objects.equals(recordDate, that.recordDate) &&
                Objects.equals(nextCheckDate, that.nextCheckDate) &&
                Objects.equals(subWorkSheet, that.subWorkSheet) &&
                Objects.equals(workCell, that.workCell);
    }

    @Override
    public int hashCode() {
        return Objects.hash(category, recordDate, nextCheckDate, result, subWorkSheet, workCell);
    }
}
