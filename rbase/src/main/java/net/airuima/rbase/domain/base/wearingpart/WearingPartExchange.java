package net.airuima.rbase.domain.base.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Schema(name = "易损件种类替代(WearingPartExchange)", description = "易损件种类替代表")
@Entity
@Table(name = "base_wearing_part_exchange",uniqueConstraints = @UniqueConstraint(name = "base_wearing_part_exchange_origin_group_id_exchange_group_id_unique",columnNames = {"origin_group_id ", "exchange_group_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "易损件种类替代")
public class WearingPartExchange extends CustomBaseEntity {

    /**
     * 原始易损件类型
     */
    @NotNull
    @Schema(description = "原始易损件类型id")
    @ManyToOne
    @JoinColumn(name = "origin_group_id",nullable = false)
    private WearingPartGroup originWearingPartGroup;

    /**
     * 替换易损件类型
     */
    @NotNull
    @Schema(description = "替换易损件类型id")
    @ManyToOne
    @JoinColumn(name = "exchange_group_id",nullable = false)
    private WearingPartGroup exchangeWearingPartGroup;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

    public WearingPartGroup getOriginWearingPartGroup() {
        return originWearingPartGroup;
    }

    public WearingPartExchange setOriginWearingPartGroup(WearingPartGroup originWearingPartGroup) {
        this.originWearingPartGroup = originWearingPartGroup;
        return this;
    }

    public WearingPartGroup getExchangeWearingPartGroup() {
        return exchangeWearingPartGroup;
    }

    public WearingPartExchange setExchangeWearingPartGroup(WearingPartGroup exchangeWearingPartGroup) {
        this.exchangeWearingPartGroup = exchangeWearingPartGroup;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WearingPartExchange setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WearingPartExchange wearingPartExchange = (WearingPartExchange) o;
        if (wearingPartExchange.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wearingPartExchange.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
