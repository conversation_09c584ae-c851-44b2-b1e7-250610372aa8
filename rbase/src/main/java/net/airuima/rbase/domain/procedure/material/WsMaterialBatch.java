package net.airuima.rbase.domain.procedure.material;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.wip.WipWarehouseDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单领料明细表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "工单领料明细表(WsMaterialBatch)", description = "工单领料明细表")
@Entity
@Table(name = "procedure_ws_material_batch", uniqueConstraints = @UniqueConstraint(columnNames = {"ws_id", "warehouse_id", "material_id", "batch", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "wsMaterialBatchEntityGraph")
public class WsMaterialBatch extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总工单id
     */
    @ManyToOne
    @Schema(description = "总工单id")
    @JoinColumn(name = "ws_id")
    private WorkSheet workSheet;

    /**
     * 线边仓Id
     */
    @Schema(description = "线边仓Id")
    @Column(name = "warehouse_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long warehouseId;

    @Transient
    @FetchField(mapUri = "/api/wip-warehouses", serviceId = "mom", paramKey = "warehouseId")
    private WipWarehouseDTO wipWarehouseDTO = new WipWarehouseDTO();

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @Column(name = "batch")
    private String batch;

    /**
     * 物料Id
     */
    @Schema(description = "物料Id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId", tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();

    /**
     * 领料总数
     */
    @Schema(description = "领料总数")
    @Column(name = "number")
    private double number;

    /**
     * 剩余总数
     */
    @Schema(description = "剩余总数")
    @Column(name = "left_number")
    private double leftNumber;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsMaterialBatch setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WsMaterialBatch setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsMaterialBatch setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsMaterialBatch setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public WsMaterialBatch setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public WsMaterialBatch setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public WsMaterialBatch setNumber(double number) {
        this.number = number;
        return this;
    }

    public double getLeftNumber() {
        return leftNumber;
    }

    public WsMaterialBatch setLeftNumber(double leftNumber) {
        this.leftNumber = leftNumber;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public WsMaterialBatch setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public WipWarehouseDTO getWipWarehouseDTO() {
        return wipWarehouseDTO;
    }

    public WsMaterialBatch setWipWarehouseDTO(WipWarehouseDTO wipWarehouseDTO) {
        this.wipWarehouseDTO = wipWarehouseDTO;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsMaterialBatch WsMaterialBatch = (WsMaterialBatch) o;
        if (WsMaterialBatch.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), WsMaterialBatch.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
