package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序配置Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系工序配置(PedigreeStep)", description = "产品谱系工序配置")
@Entity
@Table(name = "base_pedigree_step", uniqueConstraints = @UniqueConstraint(columnNames = {"client_id", "pedigree_id", "work_flow_id", "step_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序配置数据")
@NamedEntityGraph(name = "pedigreeStepEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph"),
        @NamedAttributeNode("priorityElementConfig")}, subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
public class PedigreeStep extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线
     */
    @ManyToOne
    @Schema(description = "工艺路线")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 请求模式
     */
    @Schema(description = "请求模式")
    @Column(name = "request_mode")
    private int requestMode;

    /**
     * 管控模式(0:批量;1:单支)
     */
    @Schema(description = "管控模式(0:批量;1:单支)")
    @Column(name = "control_mode")
    private int controlMode;

    /**
     * 是否管控物料(0:不管控;1:管控)
     */
    @Schema(description = "是否管控物料(0:不管控;1:管控)")
    @Column(name = "is_control_material")
    private boolean isControlMaterial;

    /**
     * 是否绑定容器(0:否;1:是)
     */
    @Schema(description = "是否绑定容器(0:否;1:是)")
    @Column(name = "is_bind_container")
    private boolean isBindContainer;

    /**
     * 投产比例
     */
    @Schema(description = "投产比例")
    @Column(name = "input_rate")
    @ColumnDefault("1")
    private double inputRate;

    /**
     * 标准工时（精确到秒）
     */
    @Schema(description = "标准工时（精确到秒）")
    @Column(name = "standard_time")
    private BigDecimal standardTime;

    /**
     * 工序理论日产出数量
     */
    @Schema(description = "工序理论日产出数量")
    @Column(name = "standard_daily_output")
    private long standardDailyOutput;

    /**
     * 是否启用(0:否;1:是)
     */
    @NotNull
    @Schema(description = "是否启用(0:否;1:是)", required = true)
    @Column(name = "is_enable", nullable = false)
    private boolean enable;


    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 工序检查配置列表
     */
    @OneToMany(mappedBy = "pedigreeStep")
    @JsonIgnoreProperties("pedigreeStep")
    private List<PedigreeStepInspectionConfig> inspectionConfigs = Lists.newArrayList();

    /**
     * 工序FPP配置
     */
    @Schema(description = "工序FPP配置")
    @OneToOne(mappedBy = "pedigreeStep", cascade = CascadeType.ALL)
    @JsonIgnoreProperties({"pedigreeStep"})
    private PedigreeStepFppConfig pedigreeStepFppConfig;

    /**
     * 报工方式(0:员工报工;1:班组报工)
     */
    @Schema(description = "报工方式(0:员工报工;1:班组报工)")
    @Column(name = "request_method")
    private int requestMethod;

    public int getRequestMethod() {
        return requestMethod;
    }

    public PedigreeStep setRequestMethod(int requestMethod) {
        this.requestMethod = requestMethod;
        return this;
    }

    public List<PedigreeStepInspectionConfig> getInspectionConfigs() {
        return inspectionConfigs;
    }

    public PedigreeStep setInspectionConfigs(List<PedigreeStepInspectionConfig> inspectionConfigs) {
        this.inspectionConfigs = inspectionConfigs;
        return this;
    }

    public PedigreeStepFppConfig getPedigreeStepFppConfig() {
        return pedigreeStepFppConfig;
    }

    public PedigreeStep setPedigreeStepFppConfig(PedigreeStepFppConfig pedigreeStepFppConfig) {
        this.pedigreeStepFppConfig = pedigreeStepFppConfig;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeStep setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStep setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStep setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getRequestMode() {
        return requestMode;
    }

    public PedigreeStep setRequestMode(int requestMode) {
        this.requestMode = requestMode;
        return this;
    }

    public int getControlMode() {
        return controlMode;
    }

    public PedigreeStep setControlMode(int controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public boolean getIsControlMaterial() {
        return isControlMaterial;
    }

    public PedigreeStep setIsControlMaterial(boolean isControlMaterial) {
        this.isControlMaterial = isControlMaterial;
        return this;
    }

    public boolean getIsBindContainer() {
        return isBindContainer;
    }

    public PedigreeStep setIsBindContainer(boolean isBindContainer) {
        this.isBindContainer = isBindContainer;
        return this;
    }

    public double getInputRate() {
        return inputRate;
    }

    public PedigreeStep setInputRate(double inputRate) {
        this.inputRate = inputRate;
        return this;
    }

    public BigDecimal getStandardTime() {
        return standardTime;
    }

    public PedigreeStep setStandardTime(BigDecimal standardTime) {
        this.standardTime = standardTime;
        return this;
    }

    public long getStandardDailyOutput() {
        return standardDailyOutput;
    }

    public PedigreeStep setStandardDailyOutput(long standardDailyOutput) {
        this.standardDailyOutput = standardDailyOutput;
        return this;
    }

    public boolean isEnable() {
        return enable;
    }

    public PedigreeStep setEnable(boolean enable) {
        this.enable = enable;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public PedigreeStep setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeStep setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStep pedigreeStep = (PedigreeStep) o;
        if (pedigreeStep.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStep.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
