package net.airuima.rbase.domain.base.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修工序不良现象规则Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "在线返修工序不良现象规则(OnlineReworkRule)", description = "在线返修工序不良现象规则")
@Entity
@Table(name = "base_online_rework_rule", uniqueConstraints = @UniqueConstraint(columnNames = {"step_id", "unqualified_item_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "在线返修工序不良现象规则数据")
public class OnlineReworkRule extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工序id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 不良现象id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良现象id")
    @JoinColumn(name = "unqualified_item_id", nullable = false)
    private UnqualifiedItem unqualifiedItem;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public Step getStep() {
        return step;
    }

    public OnlineReworkRule setStep(Step step) {
        this.step = step;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public OnlineReworkRule setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public String getNote() {
        return note;
    }

    public OnlineReworkRule setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OnlineReworkRule onlineReworkRule = (OnlineReworkRule) o;
        if (onlineReworkRule.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), onlineReworkRule.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
