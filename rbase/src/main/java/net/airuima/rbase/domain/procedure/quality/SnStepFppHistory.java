package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.quality.FppDataDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 单支FPP历史Domain
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "单支FPP历史(SnStepFppHistory)", description = "单支FPP历史实体")
@Entity
@Table(name = "procedure_sn_step_fpp_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class SnStepFppHistory extends CustomBaseEntity implements Serializable {

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;


    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 当前工艺路线
     */
    @ManyToOne
    @Schema(description = "当前工艺路线")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;


    /**
     * 当前测试工序
     */
    @ManyToOne
    @Schema(description = "当前测试工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 当前测试总次数
     */
    @Schema(description = "当前测试总次数")
    @Column(name = "test_time")
    private int testTime;


    /**
     * 返修次数
     */
    @Schema(description = "返修次数")
    @Column(name = "rework_time")
    private int reworkTime;

    /**
     * 测试结果
     */
    @Schema(description = "测试结果")
    @Column(name = "result")
    private boolean result;

    /**
     * 测试数据明细
     */
    @Schema(description = "测试数据明细")
    @Type(JsonType.class)
    @Column(name = "test_data")
    private List<FppDataDTO> testDataList;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 测试员工ID
     */
    @Schema(description = "测试员工ID")
    @JoinColumn(name = "staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;


    /**
     * 测试员工DTO
     */
    @Transient
    @FetchField(serviceName = "staffService", paramKey = "staffId")
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    public String getSn() {
        return sn;
    }

    public SnStepFppHistory setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SnStepFppHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SnStepFppHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public SnStepFppHistory setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public SnStepFppHistory setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getTestTime() {
        return testTime;
    }

    public SnStepFppHistory setTestTime(int testTime) {
        this.testTime = testTime;
        return this;
    }

    public int getReworkTime() {
        return reworkTime;
    }

    public SnStepFppHistory setReworkTime(int reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public boolean isResult() {
        return result;
    }

    public SnStepFppHistory setResult(boolean result) {
        this.result = result;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public SnStepFppHistory setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public List<FppDataDTO> getTestDataList() {
        return testDataList;
    }

    public SnStepFppHistory setTestDataList(List<FppDataDTO> testDataList) {
        this.testDataList = testDataList;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public SnStepFppHistory setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public SnStepFppHistory setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public SnStepFppHistory setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SnStepFppHistory that)) return false;
        return testTime == that.testTime && reworkTime == that.reworkTime && result == that.result && Objects.equals(sn, that.sn) && Objects.equals(workSheet, that.workSheet) && Objects.equals(subWorkSheet, that.subWorkSheet) && Objects.equals(workFlow, that.workFlow) && Objects.equals(step, that.step) && Objects.equals(testDataList, that.testDataList) && Objects.equals(workCell, that.workCell) && Objects.equals(staffId, that.staffId) && Objects.equals(staffDto, that.staffDto) && Objects.equals(recordDate, that.recordDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sn, workSheet, subWorkSheet, workFlow, step, testTime, reworkTime, result, testDataList, workCell, staffId, staffDto, recordDate);
    }
}
