package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单投料单Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工单投料单(WsMaterial)", description = "工单投料单实体")
@Entity
@Table(name = "procedure_ws_material", uniqueConstraints = @UniqueConstraint(name = "procedure_ws_material_unique",
        columnNames = {"work_sheet_id", "material_id", "origin_material_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "wsMaterialEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})})
public class WsMaterial extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投料数量
     */
    @Schema(description = "投料数量")
    @Column(name = "number")
    private double number;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 当前物料
     */
    @NotNull
    @Schema(description = "当前物料id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 当前物料DTO
     */
    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 原始物料id
     */
    @NotNull
    @Schema(description = "原始物料id", required = true)
    @Column(name = "origin_material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long originMaterialId;


    /**
     * 原始物料DTO
     */
    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "originMaterialId")
    private MaterialDTO originMaterialDto = new MaterialDTO();

    /**
     *  是否倒冲物料
     */
    @Schema(description = "是否倒冲物料",requiredMode = Schema.RequiredMode.REQUIRED,type = "boolean",format = "boolean",defaultValue = "false")
    @Column(name = "back_flush")
    private Boolean backFlush;

    public Boolean getBackFlush() {
        return backFlush;
    }

    public WsMaterial setBackFlush(Boolean backFlush) {
        this.backFlush = backFlush;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public WsMaterial setNumber(double number) {
        this.number = number;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsMaterial setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public Long getOriginMaterialId() {
        return originMaterialId;
    }

    public WsMaterial setOriginMaterialId(Long originMaterialId) {
        this.originMaterialId = originMaterialId;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }


    public WsMaterial setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsMaterial setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public MaterialDTO getOriginMaterialDto() {
        return originMaterialDto;
    }

    public WsMaterial setOriginMaterialDto(MaterialDTO originMaterialDto) {
        this.originMaterialDto = originMaterialDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WsMaterial that = (WsMaterial) o;

        if (that.getId() == null || getId() == null) {
            return Objects.equals(workSheet, that.workSheet) && Objects.equals(materialId, that.materialId) && Objects.equals(originMaterialId, that.originMaterialId);
        }
        return Objects.equals(getId(), that.getId());

    }

    @Override
    public int hashCode() {
        if (getId() == null) {
            return Objects.hash(workSheet, materialId, originMaterialId);
        }else {
            return Objects.hashCode(getId());
        }
    }

}
