package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品型号不良种类流程框图Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品型号不良种类流程框图(PedigreeReworkWorkFlow)", description = "产品型号不良种类流程框图")
@Entity
@Table(name = "base_pedigree_rework_work_flow", uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "work_flow_id", "unqualified_group_id", "client_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品型号不良种类流程框图数据")
@FetchEntity
@NamedEntityGraph(name = "pedigreeReworkWorkFlowEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class PedigreeReworkWorkFlow extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系id
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id", nullable = false)
    private Pedigree pedigree;

    /**
     * 流程框图id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "流程框图id")
    @JoinColumn(name = "work_flow_id", nullable = false)
    private WorkFlow workFlow;

    /**
     * 不良项目类别Id
     */
    @ManyToOne
    @Schema(description = "不良项目类别Id")
    @JoinColumn(name = "unqualified_group_id")
    private UnqualifiedGroup unqualifiedGroup;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    private boolean isEnable;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();


    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    public Long getClientId() {
        return clientId;
    }

    public PedigreeReworkWorkFlow setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeReworkWorkFlow setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeReworkWorkFlow setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeReworkWorkFlow setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeReworkWorkFlow setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public UnqualifiedGroup getUnqualifiedGroup() {
        return unqualifiedGroup;
    }

    public PedigreeReworkWorkFlow setUnqualifiedGroup(UnqualifiedGroup unqualifiedGroup) {
        this.unqualifiedGroup = unqualifiedGroup;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeReworkWorkFlow setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeReworkWorkFlow pedigreeReworkWorkFlow = (PedigreeReworkWorkFlow) o;
        if (pedigreeReworkWorkFlow.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeReworkWorkFlow.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
