package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
@Schema(name = "(子)工单定制工序指定工位", description = "(子)工单定制工序指定工位")
@Entity
@Table(name = "procedure_ws_step_work_cell",uniqueConstraints = {@UniqueConstraint(name = "procedure_ws_step_work_cell_sub_ws_unique",columnNames = {"sub_work_sheet_id", "step_id","work_cell_id", "deleted"}),
        @UniqueConstraint(name = "procedure_ws_step_work_cell_ws_unique",columnNames = {"work_sheet_id", "step_id", "work_cell_id","deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wsStepWorkCellEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WsStepWorkCell extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序id", required = true)
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 工序id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序id", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId",tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 产品谱系Id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系Id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsStepWorkCell setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WsStepWorkCell setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WsStepWorkCell setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WsStepWorkCell setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public WsStepWorkCell setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public WsStepWorkCell setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public WsStepWorkCell setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }
}
