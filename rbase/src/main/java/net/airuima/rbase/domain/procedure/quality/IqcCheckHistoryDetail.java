package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.GbtDetailDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验详情详情
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Schema(name = "来料检验详情", description = "来料检验详情")
@Entity
@Table(name = "procedure_iqc_check_history_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "来料检验详情")
public class IqcCheckHistoryDetail extends CustomBaseEntity implements Serializable {

    /**
     * 来料检验id
     */
    @Schema(description = "来料检验")
    @ManyToOne
    @JoinColumn(name = "check_history_id")
    private IqcCheckHistory checkHistory;

    /**
     * 检测项目
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "检测项目")
    @Column(name = "check_item_id")
    private Long checkItemId;

    @FetchField(mapUri = "/api/check-items", serviceId = "mom", paramKey = "checkItemId")
    @Transient
    private CheckItemDTO checkItem = new CheckItemDTO();

    /**
     * 序列号
     */
    @Column(name = "sn")
    @Schema(description = "序列号")
    private String sn;

    /**
     * 检测结果值(数字或OK、NG)
     */
    @Column(name = "check_data")
    @Schema(description = "检测结果值(数字或OK、NG)")
    private String checkData;

    /**
     * 判定标准(开闭区间或者OK)
     */
    @Column(name = "qualified_range")
    @Schema(description = "判定标准(开闭区间或者OK)")
    private String qualifiedRange;

    /**
     * 结果(0:不合格;1:合格)
     */
    @Column(name = "result")
    @Schema(description = "结果(0:不合格;1:合格)")
    private Boolean result;

    /**
     * 缺陷原因
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "缺陷原因")
    @Column(name = "defect_id")
    private Long defectId;

    @FetchField(mapUri = "/api/defects", serviceId = "mom", paramKey = "defectId")
    @Transient
    private DefectDTO defect = new DefectDTO();

    /**
     * 检验项目缺陷列表
     */
    @Schema(description = "检验项目缺陷列表")
    @Transient
    private List<DefectDTO> checkItemDefectList;

    /**
     * 是否管控其检查结果(0:不管控；1：管控)
     */
    @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
    @Transient
    private Boolean control;

    /**
     * 抽样方案
     */
    @Transient
    private SampleCaseDTO sampleCase;

    /**
     * 检测个数
     */
    @Schema(description = "检测个数")
    @Transient
    private int checkNumber;

    /**
     * 国标抽样方案明细
     */
    @Transient
    private GbtDetailDTO gbtDetail;

    public List<DefectDTO> getCheckItemDefectList() {
        return checkItemDefectList;
    }

    public IqcCheckHistoryDetail setCheckItemDefectList(List<DefectDTO> checkItemDefectList) {
        this.checkItemDefectList = checkItemDefectList;
        return this;
    }

    public Boolean getControl() {
        return control;
    }

    public IqcCheckHistoryDetail setControl(Boolean control) {
        this.control = control;
        return this;
    }

    public IqcCheckHistory getCheckHistory() {
        return checkHistory;
    }

    public IqcCheckHistoryDetail setCheckHistory(IqcCheckHistory checkHistory) {
        this.checkHistory = checkHistory;
        return this;
    }

    public Long getCheckItemId() {
        return checkItemId;
    }

    public IqcCheckHistoryDetail setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public IqcCheckHistoryDetail setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public IqcCheckHistoryDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getCheckData() {
        return checkData;
    }

    public IqcCheckHistoryDetail setCheckData(String checkData) {
        this.checkData = checkData;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public IqcCheckHistoryDetail setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public IqcCheckHistoryDetail setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Long getDefectId() {
        return defectId;
    }

    public IqcCheckHistoryDetail setDefectId(Long defectId) {
        this.defectId = defectId;
        return this;
    }

    public DefectDTO getDefect() {
        return defect;
    }

    public IqcCheckHistoryDetail setDefect(DefectDTO defect) {
        this.defect = defect;
        return this;
    }

    public int getCheckNumber() {
        return checkNumber;
    }

    public IqcCheckHistoryDetail setCheckNumber(int checkNumber) {
        this.checkNumber = checkNumber;
        return this;
    }

    public SampleCaseDTO getSampleCase() {
        return sampleCase;
    }

    public IqcCheckHistoryDetail setSampleCase(SampleCaseDTO sampleCase) {
        this.sampleCase = sampleCase;
        return this;
    }

    public GbtDetailDTO getGbtDetail() {
        return gbtDetail;
    }

    public IqcCheckHistoryDetail setGbtDetail(GbtDetailDTO gbtDetail) {
        this.gbtDetail = gbtDetail;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        IqcCheckHistoryDetail iqcCheckHistoryDetail = (IqcCheckHistoryDetail) o;
        if (iqcCheckHistoryDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), iqcCheckHistoryDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
