package net.airuima.rbase.domain.procedure.wearingpart;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "易损件重置历史(WearingPartResetHistory)", description = "易损件重置历史表")
@Entity
@Table(name = "procedure_wearing_part_reset_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "易损件重置历史")
@FetchEntity
public class WearingPartResetHistory extends CustomBaseEntity {

    /**
     * 易损件基础信息
     */
    @NotNull
    @Schema(description = "易损件基础信息")
    @ManyToOne
    @JoinColumn(name = "wearing_part_id",nullable = false)
    private WearingPart wearingPart;

    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "staff_id")
    private Long staffId;


    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private  StaffDTO staffDto = new StaffDTO();

    /**
     * 重置次数
     */
    @Schema(description = "重置次数")
    @Column(name = "reset_number")
    private int resetNumber;

    /**
     *备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public WearingPartResetHistory setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public WearingPartResetHistory setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public WearingPartResetHistory setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public int getResetNumber() {
        return resetNumber;
    }

    public WearingPartResetHistory setResetNumber(int resetNumber) {
        this.resetNumber = resetNumber;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WearingPartResetHistory setNote(String note) {
        this.note = note;
        return this;
    }

}
