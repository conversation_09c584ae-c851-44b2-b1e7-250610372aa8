package net.airuima.rbase.domain.procedure.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "容器生产详情易损件(ContainerDetailWearingPart)", description = "容器生产详情易损件表")
@Entity
@Table(name = "procedure_container_detail_wearing_part",uniqueConstraints = @UniqueConstraint(name = "procedure_container_detail_wearing_part_unique",columnNames = {"container_detail_id","wearing_part_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@AuditEntity(value = "容器生产详情易损件表")
@NamedEntityGraph(name = "containerDetailWearingPartEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "containerDetail",subgraph = "containerDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "containerDetailEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph"),@NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")})})
public class ContainerDetailWearingPart extends CustomBaseEntity {
    /**
     * 容器
     */
    @NotNull
    @Schema(description = "容器")
    @ManyToOne
    @JoinColumn(name = "container_detail_id",nullable = false)
    
    private ContainerDetail containerDetail;

    /**
     * 易损件基础信息
     */
    @Schema(description = "易损件基础信息")
    @ManyToOne
    @JoinColumn(name = "wearing_part_id")
    private WearingPart wearingPart;

    /**
     * 次数
     */
    @Schema(description = "次数")
    @Column(name = "times")
    private int times;

    /**
     * 时长
     */
    @Schema(description = "时长")
    @Column(name = "duration")
    private int duration;

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public ContainerDetailWearingPart setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public ContainerDetailWearingPart setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public ContainerDetailWearingPart setTimes(int times) {
        this.times = times;
        return this;
    }

    public int getDuration() {
        return duration;
    }

    public ContainerDetailWearingPart setDuration(int duration) {
        this.duration = duration;
        return this;
    }

}
