package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情设备Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "批量生产详情设备(BatchWorkDetailEquipment)", description = "批量生产详情设备实体")
@Entity
@Table(name = "procedure_batch_work_detail_facility", uniqueConstraints = @UniqueConstraint(name = "procedure_batch_work_detail_facility_unique_index",
        columnNames = {"batch_work_detail_id", "facility_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "batchWorkDetailEquipmentEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class BatchWorkDetailFacility extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单批量生产详情
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工单批量生产详情", required = true)
    @JoinColumn(name = "batch_work_detail_id", nullable = false)
    private BatchWorkDetail batchWorkDetail;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @Column(name = "facility_id")
    private Long facilityId;

    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    @Transient
    private FacilityDTO facilityDto = new FacilityDTO();

    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public BatchWorkDetailFacility setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public BatchWorkDetailFacility setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public BatchWorkDetailFacility setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BatchWorkDetailFacility batchWorkDetailFacility = (BatchWorkDetailFacility) o;
        if (batchWorkDetailFacility.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), batchWorkDetailFacility.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
