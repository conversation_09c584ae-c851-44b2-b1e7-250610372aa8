package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 工单关联销售订单详情
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工单关联销售订单详情(SaleOrderDetail)", description = "工单关联销售订单详情")
@Entity
@Table(name = "rbase_procedure_ws_sale_order_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@FetchEntity
public class WsSaleOrderDetail extends CustomBaseEntity implements Serializable {

    /**
     * 销售订单详情id
     */
    @ManyToOne
    @JoinColumn(name = "sale_order_detail_id")
    @Schema(description = "销售订单详情id")
    private SaleOrderDetail saleOrderDetail;

    /**
     * 工单id
     */
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    @Schema(description = "工单id")
    private WorkSheet workSheet;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private int number;

    public SaleOrderDetail getSaleOrderDetail() {
        return saleOrderDetail;
    }

    public WsSaleOrderDetail setSaleOrderDetail(SaleOrderDetail saleOrderDetail) {
        this.saleOrderDetail = saleOrderDetail;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsSaleOrderDetail setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public WsSaleOrderDetail setNumber(int number) {
        this.number = number;
        return this;
    }
}
