package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.util.NumberUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产子工单Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "生产子工单(SubWorkSheet)", description = "生产子工单实体")
@Entity
@Table(name = "procedure_sub_work_sheet", uniqueConstraints = @UniqueConstraint(name = "procedure_sub_work_sheet_unique", columnNames = {"serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "subWorkSheetEntityGraph", attributeNodes = {
        @NamedAttributeNode("workLine"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}, subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph", attributeNodes = {
                @NamedAttributeNode("pedigree"), @NamedAttributeNode("workLine"), @NamedAttributeNode("workFlow"), @NamedAttributeNode("saleOrder")})})
public class SubWorkSheet extends CustomBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 总工单
     */
    @NotNull
    @ManyToOne
    @Schema(description = "总工单", required = true)
    @JoinColumn(name = "work_sheet_id", nullable = false)
    private WorkSheet workSheet;

    /**
     * 生产线
     */
    @ManyToOne
    @Schema(description = "生产线")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 子工单号
     */
    @NotNull
    @Schema(description = "子工单号", required = true)
    @Column(name = "serial_number", nullable = false)
    private String serialNumber;

    /**
     * 乐观锁版本
     */
    @Schema(description = "乐观锁版本")
    @Column(name = "version")
    private Long version;

    /**
     * 流程框图
     */
    @ManyToOne
    @Schema(description = "流程框图")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 投产数
     */
    @NotNull
    @Schema(description = "投产数", required = true)
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 合格数
     */
    @NotNull
    @Schema(description = "合格数", required = true)
    @Column(name = "qualified_number", nullable = false)
    private int qualifiedNumber;

    /**
     * 不合格数
     */
    @NotNull
    @Schema(description = "不合格数", required = true)
    @Column(name = "unqualified_number", nullable = false)
    private int unqualifiedNumber;

    /**
     * 在线返修合格数
     */
    @NotNull
    @Schema(description = "在线返修合格数", required = true)
    @Column(name = "rework_qualified_number", nullable = false)
    private int reworkQualifiedNumber;

    /**
     * 工序个数
     */
    @Schema(description = "工序个数", required = true)
    @Column(name = "step_number", nullable = false)
    private int stepNumber;

    /**
     * 工序完成个数
     */
    @Schema(description = "工序完成个数", required = true)
    @Column(name = "step_comp_number", nullable = false)
    private int stepCompNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @Column(name = "plan_start_date")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划结单日期")
    @Column(name = "plan_end_date")
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Schema(description = "实际开工日期")
    @Column(name = "actual_start_date")
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Schema(description = "实际完成日期")
    @Column(name = "actual_end_date")
    private LocalDateTime actualEndDate;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)
     */
    @NotNull
    @Schema(description = "工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)", required = true)
    @Column(name = "status", nullable = false)
    private int status;

    /**
     * erp 上传状态(0:未上传,1：已上传)
     */
    @NotNull
    @Schema(description = "erp 上传状态(0:未上传,1：已上传)")
    @Column(name = "sync_status")
    private int syncStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 结单原因
     */
    @Schema(description = "结单原因")
    @Column(name = "statement_reason")
    private String statementReason;

    /**
     * 降级总数
     */
    @Schema(description = "降级总数")
    @Column(name = "down_grade_number")
    private int downGradeNumber;

    /**
     * 优先级
     */
    @Schema(description = "优先级", required = true)
    @Column(name = "priority", nullable = false)
    private int priority;

    /**
     * 完成进度
     */
    @Schema(description = "完成进度")
    @Transient
    private BigDecimal progress;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    @Transient
    private BigDecimal fty;

    public SubWorkSheet() {
    }

    public SubWorkSheet(Long id) {
        this.id = id;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SubWorkSheet setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SubWorkSheet setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public SubWorkSheet setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public SubWorkSheet setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public SubWorkSheet setVersion(Long version) {
        this.version = version;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public SubWorkSheet setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public SubWorkSheet setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public SubWorkSheet setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public int getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public SubWorkSheet setReworkQualifiedNumber(int reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        return this;
    }

    public int getStepNumber() {
        return stepNumber;
    }

    public SubWorkSheet setStepNumber(int stepNumber) {
        this.stepNumber = stepNumber;
        return this;
    }

    public int getStepCompNumber() {
        return stepCompNumber;
    }

    public SubWorkSheet setStepCompNumber(int stepCompNumber) {
        this.stepCompNumber = stepCompNumber;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public SubWorkSheet setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public SubWorkSheet setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public SubWorkSheet setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
        return this;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public SubWorkSheet setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SubWorkSheet setStatus(int status) {
        this.status = status;
        return this;
    }

    public int getSyncStatus() {
        return syncStatus;
    }

    public SubWorkSheet setSyncStatus(int syncStatus) {
        this.syncStatus = syncStatus;
        return this;
    }

    public String getNote() {
        return note;
    }

    public SubWorkSheet setNote(String note) {
        this.note = note;
        return this;
    }

    public String getStatementReason() {
        return statementReason;
    }

    public SubWorkSheet setStatementReason(String statementReason) {
        this.statementReason = statementReason;
        return this;
    }

    public int getDownGradeNumber() {
        return downGradeNumber;
    }

    public SubWorkSheet setDownGradeNumber(int downGradeNumber) {
        this.downGradeNumber = downGradeNumber;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public SubWorkSheet setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public BigDecimal getProgress() {
        if (this.getStepNumber() == Constants.INT_ZERO) {
            return BigDecimal.ZERO;
        }
        return NumberUtils.divide(this.getStepCompNumber(), this.getStepNumber(), Constants.INT_FOUR);
    }

    public SubWorkSheet setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    public BigDecimal getFty() {
        return this.number > net.airuima.constant.Constants.INT_ZERO ? NumberUtils.divide(this.qualifiedNumber, this.number, net.airuima.constant.Constants.INT_TWO) : BigDecimal.ZERO;
    }

    public SubWorkSheet setFty(BigDecimal fty) {
        this.fty = fty;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SubWorkSheet subWorksheet = (SubWorkSheet) o;
        if (subWorksheet.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), subWorksheet.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
