package net.airuima.rbase.domain.base.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 生产工序预警标准domain
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Schema(name = "生产工序预警标准(PedigreeStepUnqualifiedGroupStandard)", description = "生产工序预警标准")
@Entity
@Table(name = "base_step_warning_standard")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "stepWarningStandardEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class StepWarningStandard extends CustomBaseEntity implements Serializable {

    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    /**
     * 产品谱系id
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线id
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    @JoinColumn(name = "work_sheet_id")
    @ManyToOne
    private WorkSheet workSheet;

    /**
     * 工单类型(0:离线返修单;1:正常单;)
     */
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)")
    @JoinColumn(name = "work_sheet_category")
    private Integer workSheetCategory;

    /**
     * 工序组id
     */
    @Schema(description = "工序组id")
    @JoinColumn(name = "step_group_id")
    @ManyToOne
    private StepGroup stepGroup;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 工序id
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 预警基数
     */
    @Schema(description = "预警基数")
    @Column(name = "base_number")
    private int baseNumber;

    /**
     * 警告判断标准
     */
    @Schema(description = "警告判断标准")
    @Column(name = "waring_rate")
    private double waringRate;

    /**
     * 停线判断标准
     */
    @Schema(description = "停线判断标准")
    @Column(name = "stop_rate")
    private double stopRate;

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public StepWarningStandard setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public StepWarningStandard setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Integer getWorkSheetCategory() {
        return workSheetCategory;
    }

    public StepWarningStandard setWorkSheetCategory(Integer workSheetCategory) {
        this.workSheetCategory = workSheetCategory;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public StepWarningStandard setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public StepWarningStandard setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public StepWarningStandard setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public StepWarningStandard setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public StepWarningStandard setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StepWarningStandard setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getNote() {
        return note;
    }

    public StepWarningStandard setNote(String note) {
        this.note = note;
        return this;
    }

    public int getBaseNumber() {
        return baseNumber;
    }

    public StepWarningStandard setBaseNumber(int baseNumber) {
        this.baseNumber = baseNumber;
        return this;
    }

    public double getWaringRate() {
        return waringRate;
    }

    public StepWarningStandard setWaringRate(double waringRate) {
        this.waringRate = waringRate;
        return this;
    }

    public double getStopRate() {
        return stopRate;
    }

    public StepWarningStandard setStopRate(double stopRate) {
        this.stopRate = stopRate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepWarningStandard stepWarningStandard = (StepWarningStandard) o;
        if (stepWarningStandard.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepWarningStandard.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
