package net.airuima.rbase.domain.base.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良种类Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "不良种类(UnqualifiedGroup)", description = "不良种类")
@Entity
@Table(name = "base_unqualified_group", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "不良种类数据")
public class UnqualifiedGroup extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @NotNull
    @Schema(description = "名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @NotNull
    @Schema(description = "编码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public UnqualifiedGroup setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedGroup setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public UnqualifiedGroup setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedGroup unqualifiedGroup = (UnqualifiedGroup) o;
        if (unqualifiedGroup.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), unqualifiedGroup.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
