package net.airuima.rbase.domain.base.scene;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工位(WorkCell)", description = "工位")
@Entity
@Table(name = "base_work_cell", uniqueConstraints = {@UniqueConstraint(columnNames = {"code", "deleted"}),@UniqueConstraint(columnNames = {"ip","code","deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "工位数据")
@NamedEntityGraph(name = "workCellEntityGraph",attributeNodes = {
        @NamedAttributeNode("workLine"),@NamedAttributeNode(value = "workStation",subgraph = "workStationEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WorkCell extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工位名
     */
    @Schema(description = "工位名")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    @Column(name = "code", nullable = false)
    private String code;

    @Schema(description = "工位IP")
    @Column(name = "ip")
    private String ip;

    /**
     * 工位顺序号
     */
    @Schema(description = "工位顺序号")
    @Column(name = "order_number")
    private int orderNumber;

    /**
     * 工位类型
     */
    @Schema(description = "工位类型")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 是否接入自动设备(0:不接入;1:接入)
     */
    @Schema(description = "是否接入自动设备(0:不接入;1:接入)")
    @Column(name = "is_connect_equipment", nullable = false)
    private boolean isConnectEquipment;


    /**
     * 请求生产模式(0:系统发起;1:设备发起)
     */
    @Schema(description = "请求生产模式(0:系统发起;1:设备发起)")
    @Column(name = "facility_step_mode")
    private int facilityStepMode;

    /**
     * 工位在工站中的位置
     */
    @Schema(description = "工位在工站中的位置")
    @Column(name = "position")
    private int position;

    /**
     * 生产线
     */
    @ManyToOne
    @Schema(description = "生产线")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 工站id
     */
    @ManyToOne
    @Schema(description = "工站id")
    @JoinColumn(name = "work_station_id")
    private WorkStation workStation;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    /**
     * 组织架构id
     */
    @Schema(description = "组织架构id")
    @Column(name = "organization_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId",tableName = "organization")
    @Transient
    private OrganizationDTO organizationDto = new OrganizationDTO();

    @Schema(description = "工序列表", required = true)
    @ManyToMany(fetch = FetchType.EAGER, cascade = CascadeType.MERGE)
    @Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @JoinTable(name = "base_work_cell_step", joinColumns = @JoinColumn(name = "work_cell_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "step_id", referencedColumnName = "id"))
    @JsonIgnoreProperties("stepGroup")
    private Set<Step> steps;

    public WorkCell(){

    }

    public WorkCell(Long id){
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public WorkCell setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkCell setCode(String code) {
        this.code = code;
        return this;
    }

    public int getOrderNumber() {
        return orderNumber;
    }

    public WorkCell setOrderNumber(int orderNumber) {
        this.orderNumber = orderNumber;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCell setCategory(int category) {
        this.category = category;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkCell setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public boolean getIsConnectEquipment() {
        return isConnectEquipment;
    }

    public WorkCell setIsConnectEquipment(boolean isConnectEquipment) {
        this.isConnectEquipment = isConnectEquipment;
        return this;
    }

    public int getPosition() {
        return position;
    }

    public WorkCell setPosition(int position) {
        this.position = position;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WorkCell setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public WorkStation getWorkStation() {
        return workStation;
    }

    public WorkCell setWorkStation(WorkStation workStation) {
        this.workStation = workStation;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkCell setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public WorkCell setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Set<Step> getSteps() {
        return steps;
    }

    public WorkCell setSteps(Set<Step> steps) {
        this.steps = steps;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public WorkCell setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public int getFacilityStepMode() {
        return facilityStepMode;
    }

    public WorkCell setFacilityStepMode(int facilityStepMode) {
        this.facilityStepMode = facilityStepMode;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkCell workCell = (WorkCell) o;
        if (workCell.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workCell.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
