package net.airuima.rbase.domain.base.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 分段下单SN复用配置Domain
 * <AUTHOR>
 * @date 2021-02-23
 */
@Schema(description = "产品谱系SN复用配置(PedigreeReuseConfig)")
@Entity
@Table(name = "base_pedigree_sn_reuse_config",uniqueConstraints = @UniqueConstraint(name = "base_pedigree_reuse_config", columnNames = {"pedigree_id","reuse_pedigree_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "pedigreeSnReuseConfigEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("reusePedigree")})
public class PedigreeSnReuseConfig extends CustomBaseEntity implements Serializable {

    @NotNull
    @Schema(description = "投产谱系")
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    @NotNull
    @Schema(description = "复用谱系")
    @ManyToOne
    @JoinColumn(name = "reuse_pedigree_id")
    private Pedigree reusePedigree;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeSnReuseConfig setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Pedigree getReusePedigree() {
        return reusePedigree;
    }

    public PedigreeSnReuseConfig setReusePedigree(Pedigree reusePedigree) {
        this.reusePedigree = reusePedigree;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PedigreeSnReuseConfig that = (PedigreeSnReuseConfig) o;
        return Objects.equals(pedigree, that.pedigree) &&
                Objects.equals(reusePedigree, that.reusePedigree);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pedigree, reusePedigree);
    }
}
