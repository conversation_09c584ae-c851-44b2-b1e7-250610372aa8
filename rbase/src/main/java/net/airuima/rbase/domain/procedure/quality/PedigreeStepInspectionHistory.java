package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepInspectionConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工序检查历史记录表
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "工序检查历史记录表", description = "工序检查历史记录表")
@Entity
@Table(name = "rbase_procedure_pedigree_step_inspection_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@AuditEntity(value = "产品谱系属性数据")
@FetchEntity
public class PedigreeStepInspectionHistory extends CustomBaseEntity implements Serializable {

    /**
     * 生产工单
     */
    @ManyToOne
    @Schema(description = "生产工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子生产工单
     */
    @ManyToOne
    @Schema(description = "子生产工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;


    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;


    @Schema(description = "提出人")
    @JoinColumn(name = "staff_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    @Transient
    @FetchField(serviceName = "staffService", paramKey = "staffId")
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 检查类型(0:质量检查;1:设备检查)
     */
    @Schema(description = "检查类型(0:质量检查;1:设备检查)")
    @Column(name = "inspection_type")
    private Integer inspectionType;

    /**
     * 检查结果：0退回重做，1允许下交，2挑选
     */
    @Schema(description = "检查结果：0退回重做，1允许下交，2挑选")
    @Column(name = "inspection_result")
    private Integer inspectionResult;

    /**
     * 检查时间
     */
    @Schema(description = "检查时间")
    @Column(name = "inspection_time")
    private LocalDateTime inspectionTime;

    /**
     * 检查人
     */
    @Schema(description = "检查人")
    @Column(name = "inspector_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inspectorId;

    @Transient
    @FetchField(serviceName = "staffService", paramKey = "inspectorId")
    private StaffDTO inspectorDto = new StaffDTO();

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public PedigreeStepInspectionHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public PedigreeStepInspectionHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepInspectionHistory setStep(Step step) {
        this.step = step;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public PedigreeStepInspectionHistory setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public PedigreeStepInspectionHistory setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public PedigreeStepInspectionHistory setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public Integer getInspectionType() {
        return inspectionType;
    }

    public PedigreeStepInspectionHistory setInspectionType(Integer inspectionType) {
        this.inspectionType = inspectionType;
        return this;
    }

    public Integer getInspectionResult() {
        return inspectionResult;
    }

    public PedigreeStepInspectionHistory setInspectionResult(Integer inspectionResult) {
        this.inspectionResult = inspectionResult;
        return this;
    }

    public LocalDateTime getInspectionTime() {
        return inspectionTime;
    }

    public PedigreeStepInspectionHistory setInspectionTime(LocalDateTime inspectionTime) {
        this.inspectionTime = inspectionTime;
        return this;
    }

    public Long getInspectorId() {
        return inspectorId;
    }

    public PedigreeStepInspectionHistory setInspectorId(Long inspectorId) {
        this.inspectorId = inspectorId;
        return this;
    }

    public StaffDTO getInspectorDto() {
        return inspectorDto;
    }

    public PedigreeStepInspectionHistory setInspectorDto(StaffDTO inspectorDto) {
        this.inspectorDto = inspectorDto;
        return this;
    }

    public String getNote() {
        return note;
    }

    public PedigreeStepInspectionHistory setNote(String note) {
        this.note = note;
        return this;
    }
}
