package net.airuima.rbase.domain.base.process;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工序Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "生产工序(Step)", description = "生产工序实体")
@Entity
@Table(name = "base_step", uniqueConstraints = @UniqueConstraint(name = "procedure_work_sheet_unique", columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "生产工序数据")
@NamedEntityGraph(name = "stepEntityGraph",attributeNodes = {@NamedAttributeNode("stepGroup")})
public class Step extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    @Column(name = "category")
    private int category;

    /**
     * 工序组别
     */
    @ManyToOne
    @Schema(description = "工序组别", required = false)
    @JoinColumn(name = "step_group_id", nullable = true)
    private StepGroup stepGroup;

    public Step() {
    }

    public Step(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public Step setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public Step setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public Step setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public Step setCategory(int category) {
        this.category = category;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public Step setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Step step = (Step) o;
        if (step.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), step.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
