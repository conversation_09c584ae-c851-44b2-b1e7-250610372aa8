package net.airuima.rbase.domain.base.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "易损件种类(WearingPartGroup)", description = "易损件种类表")
@Entity
@Table(name = "base_wearing_part_group",uniqueConstraints = @UniqueConstraint(name = "base_wearing_part_group_unique",columnNames = {"code", "deleted"} ))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "易损件种类表")
public class WearingPartGroup extends CustomBaseEntity {

    /**
     * 类型编码
     */
    @NotNull
    @Schema(description = "类型编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 类型名称
     */
    @NotNull
    @Schema(description = "类型名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 易损件管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
     */
    @Schema(description = "易损件管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）")
    @Column(name = "category")
    private int category;

    /**
     * 最大重置次数
     */
    @Schema(description = "最大重置次数")
    @Column(name = "max_reset_number")
    private int maxResetNumber;

    /**
     * 最大使用次数
     */
    @Schema(description = "最大使用次数")
    @Column(name = "max_use_number")
    private int maxUseNumber;

    /**
     * 最大使用时长(秒为单位)
     */
    @Schema(description = "最大使用时长(秒为单位)")
    @Column(name = "max_use_time")
    private int maxUseTime;

    /**
     * 有效期（天数）
     */
    @Schema(description = "有效期（天数）")
    @Column(name = "effective_day")
    private int effectiveDay;

    /**
     * 易损件重置方式（0:手动，1:自动）
     */
    @Schema(description = "易损件重置方式（0:手动，1:自动）")
    @Column(name = "reset_way")
    private int resetWay;

    /**
     * 0，禁用 1，启用
     */
    @Schema(description = "0，禁用 1，启用")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    /**
     * 是否自动记录易损件(false，否 true，是)
     */
    @Schema(description = "是否自动记录易损件(false，否 true，是)")
    @Column(name = "auto_work_record")
    private boolean autoWorkRecord = Boolean.FALSE;

    /**
     * 易损件粒度（0:单支序列号，1:批次号）
     */
    @Schema(description = "易损件粒度(0:单支序列号，1:批次号)")
    @Column(name = "granularity")
    private int granularity;


    public String getCode() {
        return code;
    }

    public WearingPartGroup setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public WearingPartGroup setName(String name) {
        this.name = name;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WearingPartGroup setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getMaxResetNumber() {
        return maxResetNumber;
    }

    public WearingPartGroup setMaxResetNumber(int maxResetNumber) {
        this.maxResetNumber = maxResetNumber;
        return this;
    }

    public int getMaxUseNumber() {
        return maxUseNumber;
    }

    public WearingPartGroup setMaxUseNumber(int maxUseNumber) {
        this.maxUseNumber = maxUseNumber;
        return this;
    }

    public int getMaxUseTime() {
        return maxUseTime;
    }

    public WearingPartGroup setMaxUseTime(int maxUseTime) {
        this.maxUseTime = maxUseTime;
        return this;
    }

    public int getEffectiveDay() {
        return effectiveDay;
    }

    public WearingPartGroup setEffectiveDay(int effectiveDay) {
        this.effectiveDay = effectiveDay;
        return this;
    }

    public int getResetWay() {
        return resetWay;
    }

    public WearingPartGroup setResetWay(int resetWay) {
        this.resetWay = resetWay;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WearingPartGroup setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public boolean getAutoWorkRecord() {
        return autoWorkRecord;
    }

    public WearingPartGroup setAutoWorkRecord(boolean autoWorkRecord) {
        this.autoWorkRecord = autoWorkRecord;
        return this;
    }

    public int getGranularity() {
        return granularity;
    }

    public WearingPartGroup setGranularity(int granularity) {
        this.granularity = granularity;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WearingPartGroup wearingPartGroup = (WearingPartGroup) o;
        if (wearingPartGroup.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wearingPartGroup.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
