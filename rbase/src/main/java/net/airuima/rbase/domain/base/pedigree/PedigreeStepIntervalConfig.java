package net.airuima.rbase.domain.base.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * 产品谱系工序间隔配置Domain
 *
 * <AUTHOR>
 * @date 2022/4/11 14:39
 */
@Schema(name = "产品谱系工序间隔时长配置(pedigreeStepIntervalConfig)", description = "产品谱系工序间隔配置")
@Entity
@Table(name = "base_pedigree_step_interval_config",
        uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "work_flow_id", "step_id", "pre_step_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系工序间隔时长配置")
@NamedEntityGraph(name = "pedigreeStepIntervalConfigEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph"),
        @NamedAttributeNode(value = "preStep", subgraph = "stepEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
public class PedigreeStepIntervalConfig extends CustomBaseEntity implements Serializable {

    /**
     * 产品谱系
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 前置工序
     */
    @ManyToOne
    @Schema(description = "前置工序id")
    @JoinColumn(name = "pre_step_id", nullable = false)
    private Step preStep;

    /**
     * 时长间隔
     */
    @Schema(description = "时长间隔")
    @Column(name = "duration", nullable = false)
    private String duration;

    /**
     * 单位(0:秒，1:分钟,2:小时,3:天)
     */
    @Schema(description = "单位(0:秒，1:分钟,2:小时,3:天)")
    @Column(name = "duration_unit")
    private int durationUnit;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepIntervalConfig setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepIntervalConfig setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepIntervalConfig setStep(Step step) {
        this.step = step;
        return this;
    }

    public Step getPreStep() {
        return preStep;
    }

    public PedigreeStepIntervalConfig setPreStep(Step preStep) {
        this.preStep = preStep;
        return this;
    }

    public String getDuration() {
        return duration;
    }

    public PedigreeStepIntervalConfig setDuration(String duration) {
        this.duration = duration;
        return this;
    }

    public int getDurationUnit() {
        return durationUnit;
    }

    public PedigreeStepIntervalConfig setDurationUnit(int durationUnit) {
        this.durationUnit = durationUnit;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepIntervalConfig pedigreeStepIntervalConfig = (PedigreeStepIntervalConfig) o;
        if (pedigreeStepIntervalConfig.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepIntervalConfig.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
