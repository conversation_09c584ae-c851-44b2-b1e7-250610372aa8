package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 销售订单表详情表
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "销售订单详情表(SaleOrderDetail)", description = "销售订单详情表")
@Entity
@Table(name = "rbase_procedure_sale_order_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@FetchEntity
public class SaleOrderDetail extends CustomBaseEntity implements Serializable {

    /**
     * 销售订单
     */
    @ManyToOne
    @JoinColumn(name = "sale_order_id")
    @Schema(description = "销售订单")
    private SaleOrder saleOrder;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 产品谱系ID
     */
    @Schema(description = "产品谱系ID")
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 订单数量
     */
    @Schema(description = "订单数量")
    @Column(name = "number")
    private int number;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Column(name = "production_quantity")
    private int productionQuantity;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    @Column(name = "finish_number")
    private int finishNumber;


    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @Column(name = "plan_start_date")
    private LocalDate planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划完工日期")
    @Column(name = "plan_end_date")
    private LocalDate planEndDate;

    public String getContractNo() {
        return contractNo;
    }

    public SaleOrderDetail setContractNo(String contractNo) {
        this.contractNo = contractNo;
        return this;
    }

    public SaleOrder getSaleOrder() {
        return saleOrder;
    }

    public SaleOrderDetail setSaleOrder(SaleOrder saleOrder) {
        this.saleOrder = saleOrder;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public SaleOrderDetail setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public SaleOrderDetail setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getProductionQuantity() {
        return productionQuantity;
    }

    public SaleOrderDetail setProductionQuantity(int productionQuantity) {
        this.productionQuantity = productionQuantity;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public SaleOrderDetail setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public LocalDate getPlanStartDate() {
        return planStartDate;
    }

    public SaleOrderDetail setPlanStartDate(LocalDate planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDate getPlanEndDate() {
        return planEndDate;
    }

    public SaleOrderDetail setPlanEndDate(LocalDate planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }
}
