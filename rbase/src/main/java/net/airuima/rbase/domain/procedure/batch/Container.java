package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "容器(Container)", description = "容器")
@Entity
@Table(name = "base_container", uniqueConstraints = @UniqueConstraint(name = "container_code_deleted_unique", columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class Container extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器名称
     */
    @Schema(description = "容器名称")
    @Column(name = "name")
    private String name;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    @Column(name = "code")
    private String code;

    /**
     * 是否占用(0:否;1:是)
     */
    @Schema(description = "是否占用(0:否;1:是)")
    @Column(name = "status")
    private boolean status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public Container() {
    }

    public Container(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public Container setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public Container setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getStatus() {
        return status;
    }

    public Container setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public String getNote() {
        return note;
    }

    public Container setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Container Container = (Container) o;
        if (Container.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), Container.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
