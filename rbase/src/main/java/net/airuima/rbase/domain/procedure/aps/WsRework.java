package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 返工单关联正常单Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "返工单关联正常单(WsRework)", description = "返工单关联正常单实体")
@Entity
@Table(name = "procedure_ws_rework", uniqueConstraints = @UniqueConstraint(name = "procedure_ws_rework_unique", columnNames = {"original_work_sheet_id", "rework_work_sheet_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "wsReworkEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "originalWorkSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})})
public class WsRework extends CustomBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 原始工单
     */
    @NotNull
    @ManyToOne
    @Schema(description = "原始工单", required = true)
    @JoinColumn(name = "original_work_sheet_id", nullable = false)
    private WorkSheet originalWorkSheet;

    /**
     * 返修工单
     */
    @NotNull
    @ManyToOne
    @Schema(description = "返修工单", required = true)
    @JoinColumn(name = "rework_work_sheet_id", nullable = false)
    private WorkSheet reworkWorkSheet;

    /**
     * 不良种类
     */
    @ManyToOne
    @Schema(description = "不良种类", required = false)
    @JoinColumn(name = "unqualified_group_id", nullable = true)
    private UnqualifiedGroup unqualifiedGroup;

    /**
     * 产生不良的工序 id
     */
    @ManyToOne
    @Schema(description = "产生不良的工序 id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 原始子工单列表
     */
    @Schema(description = "原始子工单列表", required = true)
    @Column(name = "org_sub_work_sheet_list")
    private String orgSubWorkSheetList;


    public WorkSheet getOriginalWorkSheet() {
        return originalWorkSheet;
    }

    public WsRework setOriginalWorkSheet(WorkSheet originalWorkSheet) {
        this.originalWorkSheet = originalWorkSheet;
        return this;
    }

    public WorkSheet getReworkWorkSheet() {
        return reworkWorkSheet;
    }

    public WsRework setReworkWorkSheet(WorkSheet reworkWorkSheet) {
        this.reworkWorkSheet = reworkWorkSheet;
        return this;
    }

    public UnqualifiedGroup getUnqualifiedGroup() {
        return unqualifiedGroup;
    }

    public WsRework setUnqualifiedGroup(UnqualifiedGroup unqualifiedGroup) {
        this.unqualifiedGroup = unqualifiedGroup;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WsRework setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getOrgSubWorkSheetList() {
        return orgSubWorkSheetList;
    }

    public WsRework setOrgSubWorkSheetList(String orgSubWorkSheetList) {
        this.orgSubWorkSheetList = orgSubWorkSheetList;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsRework wsRework = (WsRework) o;
        if (wsRework.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wsRework.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
