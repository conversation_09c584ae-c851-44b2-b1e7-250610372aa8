package net.airuima.rbase.domain.procedure.material;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工位上料表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "工单工位上料表(WsWorkCellMaterialBatch)", description = "工单工位上料表")
@Entity
@Table(name = "procedure_ws_work_cell_material_batch", uniqueConstraints = @UniqueConstraint(name = "procedure_ws_work_cell_material_batch_unique",
        columnNames = {"ws_id", "work_cell_id", "material_id", "batch", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "wsWorkCellMaterialBatchEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")},
        subgraphs = {@NamedSubgraph(name = "workSheetEntityGraph", attributeNodes = {
                @NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WsWorkCellMaterialBatch extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单Id
     */
    @ManyToOne
    @Schema(description = "工单Id")
    @JoinColumn(name = "ws_id")
    private WorkSheet workSheet;

    /**
     * 工位id
     */
    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 物料Id
     */
    @Schema(description = "物料Id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 批次
     */
    @Schema(description = "批次")
    @Column(name = "batch")
    private String batch;

    /**
     * 工位领料总数
     */
    @Schema(description = "工位领料总数")
    @Column(name = "number")
    private double number;

    /**
     * 工位剩余总数
     */
    @Schema(description = "工位剩余总数")
    @Column(name = "left_number")
    private double leftNumber;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsWorkCellMaterialBatch setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WsWorkCellMaterialBatch setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsWorkCellMaterialBatch setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsWorkCellMaterialBatch setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WsWorkCellMaterialBatch setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public WsWorkCellMaterialBatch setNumber(double number) {
        this.number = number;
        return this;
    }

    public double getLeftNumber() {
        return leftNumber;
    }

    public WsWorkCellMaterialBatch setLeftNumber(double leftNumber) {
        this.leftNumber = leftNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsWorkCellMaterialBatch WsWorkCellMaterialBatch = (WsWorkCellMaterialBatch) o;
        if (WsWorkCellMaterialBatch.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), WsWorkCellMaterialBatch.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
