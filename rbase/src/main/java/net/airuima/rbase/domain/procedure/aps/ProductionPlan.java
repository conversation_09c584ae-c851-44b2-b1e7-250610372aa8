package net.airuima.rbase.domain.procedure.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.scene.WorkLine;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划Domain
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Schema(name = "生产计划", description = "生产计划实体")
@Entity
@Table(name = "procedure_production_plan")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "productionPlanEntityGraph",attributeNodes = {
        @NamedAttributeNode("workLine"),@NamedAttributeNode("pedigree")})
public class ProductionPlan extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生产线
     */
    @ManyToOne
    @Schema(description = "生产线")
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 工序组别
     */
    @ManyToOne
    @Schema(description = "工序组别", required = false)
    @JoinColumn(name = "step_group_id")
    private StepGroup stepGroup;

    /**
     * 产品谱系
     */
    @NotFound
    @ManyToOne
    @Schema(description = "产品谱系")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 计划粒度(0:工序组,1:生产线)
     */
    @Column(name = "category")
    @Schema(description = "计划粒度(0:工序组,1:生产线)")
    private int category;


    /**
     * 计划状态(0:待确认;1:已确认)
     */
    @Column(name = "status")
    @Schema(description = "计划状态(0:待确认;1:已确认)")
    private Boolean status;

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    @Column(name = "plan_number")
    private int planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    @Column(name = "actual_number")
    private int actualNumber;

    /**
     * 计划日期
     */
    @Schema(description = "计划日期")
    @Column(name = "plan_date", nullable = false)
    private LocalDate planDate;

    /**
     * 备注
     */
    @Column(name = "note")
    @Schema(description = "备注")
    private String note;

    public ProductionPlan(){

    }
    public ProductionPlan(Pedigree pedigree,StepGroup stepGroup,Long planNumber){
        this.pedigree = pedigree;
        this.stepGroup = stepGroup;
        this.planNumber = Objects.nonNull(planNumber)?planNumber.intValue():Constants.INT_ZERO;
    }


    public WorkLine getWorkLine() {
        return workLine;
    }

    public ProductionPlan setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public ProductionPlan setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public ProductionPlan setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public ProductionPlan setCategory(int category) {
        this.category = category;
        return this;
    }

    public Boolean getStatus() {
        return status;
    }

    public ProductionPlan setStatus(Boolean status) {
        this.status = status;
        return this;
    }

    public int getPlanNumber() {
        return planNumber;
    }

    public ProductionPlan setPlanNumber(int planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public int getActualNumber() {
        return actualNumber;
    }

    public ProductionPlan setActualNumber(int actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public LocalDate getPlanDate() {
        return planDate;
    }

    public ProductionPlan setPlanDate(LocalDate planDate) {
        this.planDate = planDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ProductionPlan setNote(String note) {
        this.note = note;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProductionPlan productionPlan = (ProductionPlan) o;
        if (productionPlan.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), productionPlan.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
