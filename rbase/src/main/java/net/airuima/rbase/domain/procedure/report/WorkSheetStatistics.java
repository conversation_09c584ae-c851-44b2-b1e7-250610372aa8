package net.airuima.rbase.domain.procedure.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单产量统计Domain
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Schema(name = "工单产量统计", description = "工单产量统计")
@Entity
@Table(name = "procedure_work_sheet_statistics")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "workSheetStatisticsEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})})
public class WorkSheetStatistics extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Column(name = "input_number")
    private int inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Column(name = "unqualified_number")
    private int unqualifiedNumber;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetStatistics setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public WorkSheetStatistics setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetStatistics setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkSheetStatistics setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public WorkSheetStatistics setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkSheetStatistics workSheetStatistics = (WorkSheetStatistics) o;
        if (workSheetStatistics.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workSheetStatistics.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }


}
