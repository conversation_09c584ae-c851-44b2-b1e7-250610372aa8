package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Schema(name = "回退历史表(procedure_roll_back_history)", description = "回退历史表")
@Entity
@Table(name = "procedure_roll_back_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "回退历史表")
@FetchEntity
@NamedEntityGraph(name = "rollBackHistoryEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")})
        }
)
public class RollBackHistory extends CustomBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id", nullable = false)
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 操作员工ID
     */
    @Schema(description = "操作员工ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long staffId;

    /**
     * 操作员工DTO
     */
    @Schema(description = "操作员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private transient StaffDTO staffDto = new StaffDTO();

    /**
     * 最小粒度投产数量
     */
    @Schema(description = "最小粒度投产数量")
    @Column(name = "number")
    private int number;

    /**
     * 回退原因
     */
    @Schema(description = "回退原因")
    @Column(name = "note")
    private String note;

    /**
     * 工序
     */
    @Schema(description = "工序")
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工位
     */
    @Schema(description = "工位")
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 最小粒度合格数量
     */
    @Schema(description = "最小粒度合格数量")
    @Column(name = "qualified_number")
    private int qualifiedNumber;

    /**
     * 回退容器编码
     */
    @Schema(description = "回退容器编码")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * 回退SN
     */
    @Schema(description = "回退SN")
    @Column(name = "sn")
    private String sn;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public RollBackHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public RollBackHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RollBackHistory setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public RollBackHistory setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public RollBackHistory setNumber(int number) {
        this.number = number;
        return this;
    }

    public String getNote() {
        return note;
    }

    public RollBackHistory setNote(String note) {
        this.note = note;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public RollBackHistory setStep(Step step) {
        this.step = step;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public RollBackHistory setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RollBackHistory setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public RollBackHistory setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public RollBackHistory setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
