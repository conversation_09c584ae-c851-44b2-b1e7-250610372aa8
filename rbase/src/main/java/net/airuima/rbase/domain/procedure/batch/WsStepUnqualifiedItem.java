package net.airuima.rbase.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工序不良项目统计Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工单工序不良项目统计(WsStepUnqualifiedItem)", description = "工单工序不良项目统计实体")
@Entity
@Table(name = "procedure_ws_step_unqualified_item", uniqueConstraints = {@UniqueConstraint(name = "procedure_ws_step_unqualified_item_unique",
        columnNames = {"work_sheet_id", "step_id", "unqualified_item_id", "deleted"}), @UniqueConstraint(name = "procedure_sub_ws_step_unqualified_item_unique",
        columnNames = {"sub_work_sheet_id", "step_id", "unqualified_item_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wsStepUnqualifiedItemEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")})})
@FetchEntity
public class WsStepUnqualifiedItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不良数量
     */
    @Schema(description = "不良数量", required = true)
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    /**
     * 标识是否已生成在线返修单0:否;1:是
     */
    @Schema(description = "标识是否已生成在线返修单0:否;1:是")
    @Column(name = "flag")
    private boolean flag;

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序id
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 不良项目
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良项目", required = true)
    @JoinColumn(name = "unqualified_item_id", nullable = false)
    private UnqualifiedItem unqualifiedItem;

    /**
     * 已返修数
     */
    @Schema(description = "已返修数")
    @Column(name = "repair_count")
    private int repairCount;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 不良描述
     */
    @Schema(description = "不良描述")
    @Column(name = "description")
    private String description;

    /**
     * 不良图片集合
     */
    @ArraySchema(schema = @Schema(description = "不良图片集合",type = "object",implementation = DocumentDTO.class))
    @Transient
    private List<DocumentDTO> documentDTOList;

    public String getDescription() {
        return description;
    }

    public WsStepUnqualifiedItem setDescription(String description) {
        this.description = description;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public WsStepUnqualifiedItem setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
        return this;
    }

    public int getRepairCount() {
        return repairCount;
    }

    public WsStepUnqualifiedItem setRepairCount(int repairCount) {
        this.repairCount = repairCount;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public WsStepUnqualifiedItem setNumber(int number) {
        this.number = number;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public WsStepUnqualifiedItem setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public boolean getFlag() {
        return flag;
    }

    public WsStepUnqualifiedItem setFlag(boolean flag) {
        this.flag = flag;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsStepUnqualifiedItem setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public WsStepUnqualifiedItem setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WsStepUnqualifiedItem setStep(Step step) {
        this.step = step;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public WsStepUnqualifiedItem setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WsStepUnqualifiedItem setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WsStepUnqualifiedItem setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsStepUnqualifiedItem wsStepUnqualifiedItem = (WsStepUnqualifiedItem) o;
        if (wsStepUnqualifiedItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wsStepUnqualifiedItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
