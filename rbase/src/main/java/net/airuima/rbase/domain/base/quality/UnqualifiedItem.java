package net.airuima.rbase.domain.base.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良项目Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "不良项目(UnqualifiedItem)", description = "不良项目")
@Entity
@Table(name = "base_unqualified_item", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "不良项目数据")
@NamedEntityGraph(name = "unqualifiedItemEntityGraph",attributeNodes = {@NamedAttributeNode("unqualifiedGroup")})
public class UnqualifiedItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不合格项目代码
     */
    @NotNull
    @Schema(description = "不合格项目代码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 不合格项目名称
     */
    @NotNull
    @Schema(description = "不合格项目名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private Boolean isEnable;

    /**
     * 不良种类
     */
    @ManyToOne
    @Schema(description = "不良种类")
    @JoinColumn(name = "unqualified_group_id")
    private UnqualifiedGroup unqualifiedGroup;

    /**
     * 处理方式(0,在线返修;1,在线调整;2,报废;3,维修分析）
     */
    @Schema(description = "处理方式(0,在线返修;1,在线调整;2,报废;3,维修分析）")
    @Column(name = "deal_way")
    private int dealWay;

    public UnqualifiedItem() {

    }

    public UnqualifiedItem(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedItem setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public UnqualifiedItem setName(String name) {
        this.name = name;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public UnqualifiedItem setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public UnqualifiedGroup getUnqualifiedGroup() {
        return unqualifiedGroup;
    }

    public UnqualifiedItem setUnqualifiedGroup(UnqualifiedGroup unqualifiedGroup) {
        this.unqualifiedGroup = unqualifiedGroup;
        return this;
    }

    public int getDealWay() {
        return dealWay;
    }

    public UnqualifiedItem setDealWay(int dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedItem unqualifiedItem = (UnqualifiedItem) o;
        if (unqualifiedItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), unqualifiedItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
