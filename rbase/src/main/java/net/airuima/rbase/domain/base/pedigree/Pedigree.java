package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系(Pedigree)", description = "产品谱系")
@Entity
@Table(name = "base_pedigree", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "产品谱系数据")
@NamedEntityGraph(name = "pedigreeEntityGraph",attributeNodes = {@NamedAttributeNode("parent")})
public class Pedigree extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 父级id
     */
    @ManyToOne
    @Schema(description = "父级id")
    @JoinColumn(name = "parent_id")
    @NotFound(action = NotFoundAction.IGNORE)
    private Pedigree parent;

    /**
     * 当前层级类型
     */
    @Schema(description = "当前层级类型")
    @Column(name = "type", nullable = false)
    private int type;

    /**
     * 是否启用(0:不启用;1:启用)
     */
    @Schema(description = "是否启用(0:不启用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Column(name = "specification")
    private String specification;

    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    @Transient
    private List<Pedigree> childPedigreeList;

    public Pedigree() {
    }

    public Pedigree(Long pedigreeId) {
        this.id = pedigreeId;
    }

    public String getName() {
        return name;
    }

    public Pedigree setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public Pedigree setCode(String code) {
        this.code = code;
        return this;
    }

    public Pedigree getParent() {
        return parent;
    }

    public Pedigree setParent(Pedigree parent) {
        this.parent = parent;
        return this;
    }

    public int getType() {
        return type;
    }

    public Pedigree setType(int type) {
        this.type = type;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public Pedigree setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public Pedigree setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public Pedigree setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public Pedigree setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public List<Pedigree> getChildPedigreeList() {
        return childPedigreeList;
    }

    public Pedigree setChildPedigreeList(List<Pedigree> childPedigreeList) {
        this.childPedigreeList = childPedigreeList;
        return this;
    }
}
