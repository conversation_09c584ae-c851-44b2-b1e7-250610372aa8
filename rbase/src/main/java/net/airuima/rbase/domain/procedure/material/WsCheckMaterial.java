package net.airuima.rbase.domain.procedure.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "工单核料表(WsCheckMaterial)", description = "工单核料表")
@Entity
@Table(name = "procedure_ws_check_material", uniqueConstraints = @UniqueConstraint(name = "procedure_ws_check_material_unique", columnNames = {"code","ws_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wsCheckMaterialEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {
                @NamedAttributeNode("pedigree")})})
public class WsCheckMaterial extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 核料凭证号
     */
    @Schema(description = "核料凭证号")
    @Column(name = "code")
    private String code;

    /**
     * 总工单id
     */
    @ManyToOne
    @Schema(description = "总工单id")
    @JoinColumn(name = "ws_id")
    private WorkSheet workSheet;

    /**
     * 核料状态(0:未核料;1:部分核料;2:全部核料)
     */
    @Schema(description = "核料状态(0:未核料;1:部分核料;2:全部核料)")
    @Column(name = "status")
    private int status;

    /**
     * 核料时间
     */
    @Schema(description = "核料时间")
    @Column(name = "check_date")
    private LocalDateTime checkDate;

    /**
     * 0,正常工单核料;1,在线返修单核料
     */
    @Schema(description = "0,正常工单核料;1,在线返修单核料")
    @Column(name = "type")
    private int type;

    public String getCode() {
        return code;
    }

    public WsCheckMaterial setCode(String code) {
        this.code = code;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsCheckMaterial setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public WsCheckMaterial setStatus(int status) {
        this.status = status;
        return this;
    }

    public LocalDateTime getCheckDate() {
        return checkDate;
    }

    public WsCheckMaterial setCheckDate(LocalDateTime checkDate) {
        this.checkDate = checkDate;
        return this;
    }

    public int getType() {
        return type;
    }

    public WsCheckMaterial setType(int type) {
        this.type = type;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WsCheckMaterial WsCheckMaterial = (WsCheckMaterial) o;
        if (WsCheckMaterial.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), WsCheckMaterial.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
