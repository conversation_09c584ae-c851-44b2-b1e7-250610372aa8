package net.airuima.rbase.domain.base.process;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组别Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工序组别(StepGroup)", description = "工序组别实体")
@Entity
@Table(name = "base_step_group", uniqueConstraints = @UniqueConstraint(name = "base_step_group_unique", columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "工序组别数据")
public class StepGroup extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工序组别名称
     */
    @Schema(description = "工序组别名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 工序组别编码
     */
    @Schema(description = "工序组别编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    public StepGroup() {
    }
    public StepGroup(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public StepGroup setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public StepGroup setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public StepGroup setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepGroup stepGroup = (StepGroup) o;
        if (stepGroup.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepGroup.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
