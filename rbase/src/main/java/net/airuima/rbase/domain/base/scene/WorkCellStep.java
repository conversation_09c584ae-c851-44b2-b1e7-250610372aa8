package net.airuima.rbase.domain.base.scene;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.process.Step;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "工位工序(WorkCellStep)", description = "工位工序")
@Entity
@Table(name = "base_work_cell_step", uniqueConstraints = @UniqueConstraint(columnNames = {"work_cell_id", "step_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "workCellStepEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WorkCellStep extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工位id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id", nullable = false)
    private WorkCell workCell;

    /**
     * 工序id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Column(name = "is_enable")
    private boolean isEnable;

    @Transient
    @JsonIgnoreProperties("stepGroup")
    private Set<Step> steps;

    public Set<Step> getSteps() {
        return steps;
    }

    public WorkCellStep setSteps(Set<Step> steps) {
        this.steps = steps;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellStep setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkCellStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkCellStep setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkCellStep workCellStep = (WorkCellStep) o;
        if (workCellStep.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workCellStep.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
