package net.airuima.rbase.domain.procedure.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * 检测任务详情记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "检测任务详情记录(InspectTaskDetail)", description = "检测任务详情记录")
@Entity
@Table(name = "procedure_inspect_task_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
public class InspectTaskDetail extends CustomBaseEntity implements Serializable {

    @Schema(description = "检测任务id")
    @ManyToOne
    @JoinColumn(name = "inspect_task_id")
    private InspectTask inspectTask;


    @Schema(description = "sn")
    @Column(name = "sn")
    private String sn;

    public InspectTask getInspectTask() {
        return inspectTask;
    }

    public InspectTaskDetail setInspectTask(InspectTask inspectTask) {
        this.inspectTask = inspectTask;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public InspectTaskDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
