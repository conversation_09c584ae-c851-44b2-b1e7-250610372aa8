package net.airuima.rbase.domain.base.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良项目预警标准Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "产品谱系工序不良项目预警标准(PedigreeStepUnqualifiedItemStandard)", description = "产品谱系工序不良项目预警标准")
@Entity
@Table(name = "base_unqualified_item_warning_standard", uniqueConstraints = @UniqueConstraint(name = "base_unqualified_item_warning_standard_unique",
        columnNames = {"pedigree_id", "work_sheet_id", "work_sheet_category", "step_group_id","step_id","work_flow_id","client_id","unqualified_item_id","unqualified_group_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "unqualifiedItemWarningStandardEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class UnqualifiedItemWarningStandard extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系id
     */
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工序id
     */
    @ManyToOne
    @Schema(description = "工序id")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 不良现象id
     */
    @ManyToOne
    @Schema(description = "不良现象id")
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    /**
     * 工单id
     */
    @ManyToOne
    @Schema(description = "工单id")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 工单类型(0:离线返修单;1:正常单;)
     */
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)")
    @Column(name = "work_sheet_category")
    private Integer workSheetCategory;

    /**
     * 工序组id
     */
    @Schema(description = "工序组id")
    @JoinColumn(name = "step_group_id")
    @ManyToOne
    private StepGroup stepGroup;

    /**
     * 工艺路线id
     */
    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId",tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();
    /**
     * 不良种类id
     */
    @ManyToOne
    @Schema(description = "不良种类id")
    @JoinColumn(name = "unqualified_group_id")
    private UnqualifiedGroup unqualifiedGroup;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 预警基数
     */
    @Schema(description = "预警基数")
    @Column(name = "base_number")
    private int baseNumber;

    /**
     * 预警数量
     */
    @Schema(description = "预警数量")
    @Column(name = "waring_number")
    private int waringNumber;

    /**
     * 是否触发首检
     **/
    @Schema(description = "是否触发首检")
    @Column(name = "is_fai")
    private boolean isFai;

    /**
     * 警告判断标准
     */
    @Schema(description = "警告判断标准")
    @Column(name = "waring_rate")
    private double waringRate;

    /**
     * 停线判断标准
     */
    @Schema(description = "停线判断标准")
    @Column(name = "stop_rate")
    private double stopRate;


    public Pedigree getPedigree() {
        return pedigree;
    }

    public UnqualifiedItemWarningStandard setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public UnqualifiedItemWarningStandard setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public UnqualifiedItemWarningStandard setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Integer getWorkSheetCategory() {
        return workSheetCategory;
    }

    public UnqualifiedItemWarningStandard setWorkSheetCategory(Integer workSheetCategory) {
        this.workSheetCategory = workSheetCategory;
        return this;
    }

    public StepGroup getStepGroup() {
        return stepGroup;
    }

    public UnqualifiedItemWarningStandard setStepGroup(StepGroup stepGroup) {
        this.stepGroup = stepGroup;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public UnqualifiedItemWarningStandard setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public UnqualifiedItemWarningStandard setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public UnqualifiedItemWarningStandard setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public UnqualifiedGroup getUnqualifiedGroup() {
        return unqualifiedGroup;
    }

    public UnqualifiedItemWarningStandard setUnqualifiedGroup(UnqualifiedGroup unqualifiedGroup) {
        this.unqualifiedGroup = unqualifiedGroup;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public UnqualifiedItemWarningStandard setStep(Step step) {
        this.step = step;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public UnqualifiedItemWarningStandard setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public String getNote() {
        return note;
    }

    public UnqualifiedItemWarningStandard setNote(String note) {
        this.note = note;
        return this;
    }

    public int getBaseNumber() {
        return baseNumber;
    }

    public UnqualifiedItemWarningStandard setBaseNumber(int baseNumber) {
        this.baseNumber = baseNumber;
        return this;
    }

    public double getWaringRate() {
        return waringRate;
    }

    public UnqualifiedItemWarningStandard setWaringRate(double waringRate) {
        this.waringRate = waringRate;
        return this;
    }

    public double getStopRate() {
        return stopRate;
    }

    public UnqualifiedItemWarningStandard setStopRate(double stopRate) {
        this.stopRate = stopRate;
        return this;
    }

    public int getWaringNumber() {
        return waringNumber;
    }

    public UnqualifiedItemWarningStandard setWaringNumber(int waringNumber) {
        this.waringNumber = waringNumber;
        return this;
    }

    public boolean getIsFai() {
        return isFai;
    }

    public void setIsFai(boolean fai) {
        isFai = fai;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = (UnqualifiedItemWarningStandard) o;
        if (unqualifiedItemWarningStandard.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), unqualifiedItemWarningStandard.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
