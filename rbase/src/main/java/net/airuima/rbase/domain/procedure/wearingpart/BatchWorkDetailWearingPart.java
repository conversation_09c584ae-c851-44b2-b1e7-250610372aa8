package net.airuima.rbase.domain.procedure.wearingpart;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/22
 */
@Schema(name = "批量生产详情易损件(BatchWorkDetailWearingPart)", description = "批量生产详情易损件表")
@Entity
@Table(name = "procedure_batch_work_detail_wearing_part",uniqueConstraints = @UniqueConstraint(name = "procedure_batch_work_detail_wearing_part_unique",columnNames = {"batch_work_detail_id","wearing_part_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "批量生产详情易损件")
@FetchEntity
@NamedEntityGraph(name = "batchWorkDetailWearingPartEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")})})
public class BatchWorkDetailWearingPart extends CustomBaseEntity {

    /**
     * 易损件批量生产
     */
    @NotNull
    @Schema(description = "易损件批量生产")
    @ManyToOne
    @JoinColumn(name = "batch_work_detail_id",nullable = false)
    private BatchWorkDetail batchWorkDetail;

    /**
     * 易损件基础信息
     */
    @Schema(description = "易损件基础信息")
    @ManyToOne
    @JoinColumn(name = "wearing_part_id")
    private WearingPart wearingPart;

    /**
     * 次数
     */
    @Schema(description = "次数")
    @Column(name = "times")
    private int times;

    /**
     * 时长
     */
    @Schema(description = "时长")
    @Column(name = "duration")
    private int duration;


    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public BatchWorkDetailWearingPart setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public BatchWorkDetailWearingPart setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public BatchWorkDetailWearingPart setTimes(int times) {
        this.times = times;
        return this;
    }

    public int getDuration() {
        return duration;
    }

    public BatchWorkDetailWearingPart setDuration(int duration) {
        this.duration = duration;
        return this;
    }

}
