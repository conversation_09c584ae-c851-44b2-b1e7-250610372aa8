package net.airuima.rbase.proxy.oem;

import net.airuima.config.bean.BeanDefine;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.oem.OemAutoConfigDTO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class RbaseOemProxy {

    /**
     * 获取产品谱系配置的工序外协分单配置
     *
     * @param pedigree 产品谱系
     * @return OemAutoConfigDTO
     */
    @BeanDefine(value = "oemAutoConfigService", funcKey = "StepOem")
    public OemAutoConfigDTO findOemAutoConfigByPedigree(Pedigree pedigree) {
        return null;
    }


    /**
     * 获取 投产工单 已工序外协数量
     *
     * @param workSheetId    工单
     * @param subWorkSheetId 子工单
     * @param stepId         工序
     * @return 工序外协数量
     */
    @BeanDefine(value = "oemOrderService", funcKey = "StepOem")
    public Integer originalStepProcessNumber(Long workSheetId, Long subWorkSheetId, Long stepId) {
        return Constants.INT_ZERO;
    }

    /**
     * 递归创建工序外协工单
     *
     * @param workSheet         工单
     * @param subWorkSheet      子工单
     * @param step              外协工序
     * @param stepProcessNumber 剩余外协数量
     * @param isFinished        是否上道工序完成
     */
    @BeanDefine(value = "oemOrderService", funcKey = "StepOem")
    public void generateStepProcessOemOrder(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step, List<String> currentStepOemBindSns, Integer stepProcessNumber, Boolean isFinished) {
        return;
    }

    /**
     * 获取投产工单绑定的 工序外协sn
     *
     * @param workSheetId    工单id
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @return List<String>
     */
    @BeanDefine(value = "oemOrderSnService", funcKey = "StepOem")
    public List<String> getStepOemOrderBindSn(Long workSheetId, Long subWorkSheetId, Long stepId) {
        return Collections.emptyList();
    }

    /**
     * 更新缓存
     *
     * @param id    主键
     * @param cache 缓存内容
     */
    @BeanDefine(value = "oemInspectService", funcKey = "StepOem")
    public void updateCache(Long id,String cache){
        return;
    }

}
