package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.DefectDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class RbaseDefectProxy {

    @BeanDefine(value = "defectRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Optional<DefectDTO> findByIdAndDeleted(Long id, Long deleted) {
        return Optional.empty();
    }

}
