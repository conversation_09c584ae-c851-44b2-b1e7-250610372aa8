package net.airuima.rbase.proxy.event;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.control.EventConfigDTO;
import net.airuima.rbase.dto.control.EventRecordDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseEventProxy {

    /**
     *
     * 通过配置编码以及逻辑删除获取唯一配置记录
     * @param code 配置编码
     * @return EventConfigDTO 事件配置记录
     */
    @BeanDefine(value = "eventConfigService", funcKey = "Event")
    public EventConfigDTO findByCode(String code){
        return null;
    }


    /**
     * 通过预警对象编码获取未处理的第一条记录
     *
     * @param warningTargetCode 预警对象
     * @return EventRecordDTO 未处理的事件记录
     */
    @BeanDefine(value = "eventRecordService", funcKey = "Event")
    public EventRecordDTO findTopUnprocessedEventByTargetCode(String warningTargetCode) {
        return null;
    }

    /**
     * 通过预警对象编码获取所有的事件记录信息
     *
     * @param warningTargetCode 预警对象
     * @return EventRecordDTO 未处理的事件记录
     */
    @BeanDefine(value = "eventRecordService", funcKey = "Event")
    public List<EventRecordDTO> findAllByWarningTargetCode(String warningTargetCode) {
        return null;
    }

    /**
     * 通过预警对象编码列表和处理状态返回第一条记录
     * @param warningTargetCodeList  预警对象编码列表
     * @return  EventRecordDTO 未处理的事件记录
     */
    @BeanDefine(value = "eventRecordService", funcKey = "Event")
    public EventRecordDTO findTopUnprocessedEventByTargetCodes(List<String> warningTargetCodeList) {
        return null;
    }
}
