package net.airuima.rbase.proxy.dynamicdata;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.base.process.Step;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseStepDynamicDataVisibleProxy {

    @BeanDefine(value = "stepDynamicDataVisibleRepository",funcKey = "StepDynamicData")
    public List<Step> findByStepIdAndDeleted(Long stepId, Long deleted){
        return new ArrayList<>();
    }
}
