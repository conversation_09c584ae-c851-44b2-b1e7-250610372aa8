package net.airuima.rbase.proxy.rwms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.sync.SyncWmsReceiveOrderDTO;
import org.springframework.stereotype.Component;

/**
 * 入库单代理类
 */
@Component
public class RbaseReceiveOrderProxy {

    /**
     * 同步入库单数据
     *
     * @param syncWmsReceiveOrderDTO 同步入库单DTO
     */
    @BeanDefine(value = "receiveOrderService", funcKey = "RwmsService")
    public BaseDTO saveCustom(@ObjectField("net.airuima.rwms.web.rest.procedure.storage.dto.ReceiveOrderDTO") SyncWmsReceiveOrderDTO syncWmsReceiveOrderDTO) {
        return new BaseDTO();
    }
}
