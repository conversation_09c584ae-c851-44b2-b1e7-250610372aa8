package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.VarietyDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class RbaseVarietyProxy {

    @BeanDefine(value = "varietyRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public VarietyDTO findByCodeAndIsEnableAndDeleted(String code, Boolean isEnable, Long deleted) {
        return null;
    }

    @BeanDefine(value = "varietyRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Optional<VarietyDTO> findByIdAndDeleted(Long id, Long deleted){
        return Optional.empty();
    }
}
