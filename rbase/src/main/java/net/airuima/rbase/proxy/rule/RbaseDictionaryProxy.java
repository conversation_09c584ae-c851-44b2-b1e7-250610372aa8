package net.airuima.rbase.proxy.rule;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/7/17
 */
@Component
public class RbaseDictionaryProxy {

    @BeanDefine(value = "dictionaryRepository")
    public Optional<DictionaryDTO> findByCodeAndDeleted(String code, Long deleted){
        return Optional.empty();
    }
}
