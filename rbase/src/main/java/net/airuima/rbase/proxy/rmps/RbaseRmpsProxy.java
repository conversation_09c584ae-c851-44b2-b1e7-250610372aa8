package net.airuima.rbase.proxy.rmps;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.rmps.PackageYsnRelationDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerPackageWorkSheetSnResultDTO;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetSyncDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.WorkSheetSyncMpsDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseRmpsProxy {

    /**
     * 通过（子）工单号获取SN列表
     * @param serialNumber (子)工单号
     * @return sn列表
     */
    @BeanDefine(value = "packageRelationService",funcKey = "RmpsService")
    public List<String> findWorkSheetProcessSn(String serialNumber, boolean subWsProductionMode){
        return new ArrayList<>();
    }


    /**
     * 通过SN列表获取YSN
     * @param snList SN列表
     * @return  List<PackageYsnRelationDTO>
     */
    @BeanDefine(value = "packageRelationService",funcKey = "RmpsService")
    public List<PackageYsnRelationDTO> findYsn(List<String>snList){
        return new ArrayList<>();
    }


    /**
     * 通过SN获取启用状态的(子)工单号和SN关联关系
     * @param sn
     * @return RworkerPackageWorkSheetSnResultDTO
     */
    @BeanDefine(value = "packageRelationService",funcKey = "RmpsService")
    public RworkerPackageWorkSheetSnResultDTO findWorkSheetSn(String sn){
        return null;
    }

    /**
     * 通过SN或者YSN获取SN
     * @param sn SN或者YSN
     * @return BaseResultDTO
     */
    @BeanDefine(value = "packageRelationService",funcKey = "RmpsService")
    public BaseResultDTO findSn(String sn, String serialNumber){
        return null;
    }


    @BeanDefine(value = "packageRelationService",funcKey = "RmpsService")
    public BaseResultDTO validateProcessSn(String sn, String workSheetSerialNumber) {
        return new BaseResultDTO(Constants.OK);
    }


    /**
     * 同步工单到RMPS
     * @param workSheetSyncDto 同步工单参数
     */
    @BeanDefine(value = "packageWorkSheetService",funcKey = "RmpsService")
    public void sync(@ObjectField("net.airuima.rmps.web.rest.procedure.dto.WorkSheetSyncDTO") WorkSheetSyncMpsDTO workSheetSyncDto){

    }
}
