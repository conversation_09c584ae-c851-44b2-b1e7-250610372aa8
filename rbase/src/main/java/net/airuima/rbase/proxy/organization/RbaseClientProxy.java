package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseClientProxy {

    @BeanDefine("clientRepository")
    public ClientDTO findByCodeAndDeleted(String code, Long deleted){
        return null;
    }


    @BeanDefine("clientRepository")
    public List<ClientDTO> findByCodeIn(List<String> codeList){
        return null;
    }

    @BeanDefine("clientService")
    public ClientDTO findByIdAndDeleted(Long id, Long deleted){
        return null;
    }
}
