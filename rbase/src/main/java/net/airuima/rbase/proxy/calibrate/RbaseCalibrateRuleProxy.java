package net.airuima.rbase.proxy.calibrate;

import net.airuima.config.annotation.DataFilter;
import net.airuima.config.bean.BeanDefine;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.dto.calibrate.CalibrateRuleDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseCalibrateRuleProxy {

    /**
     * 通过工位主键ID、设备主键ID列表查询校准规则
     * @param workCellId 工位主键ID
     * @param facilityIds 设备主键ID列表
     * @param deleted 逻辑删除
     */
    @BeanDefine(value = "calibrateRuleRepository",funcKey = "FCalibration")
    public List<CalibrateRuleDTO> findByWorkCellIdAndFacilityIdInAndDeleted(Long workCellId, List<Long> facilityIds, Long deleted){
        return new ArrayList<>();
    }

    /**
     *
     * 通过工位主键ID获取没有设备的校准规则
     * @param workCellId 工位主键ID
     * @param deleted  逻辑删除
     */
    @BeanDefine(value = "calibrateRuleRepository",funcKey = "FCalibration")
    public List<CalibrateRuleDTO> findByWorkCellIdAndFacilityIdIsNullAndDeleted(Long workCellId,Long deleted){
        return new ArrayList<>();
    }
}
