package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.DefectDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RbaseDefectCheckItemProxy {

    @BeanDefine(value = "defectCheckItemRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public List<DefectDTO> findByCheckItemId(Long checkItemId) {
        return null;
    }

}
