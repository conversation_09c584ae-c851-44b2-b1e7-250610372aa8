package net.airuima.rbase.proxy.message;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.message.SendMessageDTO;
import net.airuima.rbase.dto.message.TaskFeignDTO;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseTaskMessageProxy {

    /**
     * 添加系统任务
     * @param taskDTO 添加的任务对象
     * @return 返回添加的对象
     */
    @BeanDefine(value = "taskService", funcKey = "Message")
    public Object addTaskFromSystem(@ObjectField("net.airuima.message.web.rest.dto.TaskDTO$TaskEditDTO") TaskFeignDTO taskDTO) {
        return null;
    }


    /**
     * 通过消息组发送消息
     * @param sendMessageDTO 传入发送消息配置组信息
     */
    @BeanDefine(value = "messageService", funcKey = "Message")
    public void groupMessage(@ObjectField("net.airuima.message.web.rest.dto.MessageDTO$MessageGroupSendDTO") SendMessageDTO sendMessageDTO) {

    }
}
