package net.airuima.rbase.proxy.optical;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.ocmes.GlueDetailDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseGlueMaterialProxy {

    /**
     *验证当前物料是否是胶水
     */
    @BeanDefine(value = "glueConfigService",funcKey = "Glue")
    public List<Long> isGlueMaterial(List<Long> materialIds) {
        return new ArrayList<>();
    }


    /**
     * 通过物料ID列表获取可用的胶水列表
     */
    @BeanDefine(value = "glueMaterialBatchService",funcKey = "Glue")
    public List<GlueDetailDTO> findAvailableByMaterialIds(List<Long> materialIds) {
        return new ArrayList<>();
    }
}
