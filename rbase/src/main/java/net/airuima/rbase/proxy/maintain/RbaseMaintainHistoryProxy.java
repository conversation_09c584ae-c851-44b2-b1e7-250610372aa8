package net.airuima.rbase.proxy.maintain;

import net.airuima.config.annotation.DataFilter;
import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaintainHistoryProxy {

    @BeanDefine(value = "maintainHistoryService", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDTO> batchSaveInstance(@ObjectField(value = "net.airuima.maintain.domain.procedure.MaintainHistory", isArray = true) List<MaintainHistoryDTO> maintainHistoryList) {
        return new ArrayList<>();
    }

    @BeanDefine(value = "maintainHistoryService", funcKey = "RepaireAnalysis")
    public MaintainHistoryDTO saveInstance(@ObjectField(value = "net.airuima.maintain.domain.procedure.MaintainHistory") MaintainHistoryDTO maintainHistory) {
        return null;
    }

    /**
     * 通过子工单ID获取待维修分析任务个数
     *
     * @param subWorkSheetId 子工单ID
     * @param status         状态
     * @param deleted        逻辑删除
     * @return 个数
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public long countBySubWorkSheetIdAndStatusLessThanAndDeleted(long subWorkSheetId, int status, long deleted) {
        return Constants.LONG_ZERO;
    }


    /**
     * 通过工单ID获取待维修分析任务个数
     *
     * @param workSheetId 工单ID
     * @param status      状态
     * @param deleted     逻辑删除
     * @return 个数
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public long countByWorkSheetIdAndStatusLessThanAndDeleted(long workSheetId, int status, long deleted) {
        return Constants.LONG_ZERO;
    }

    /**
     * 通过子工单ID获取待维修分析任务个数
     *
     * @param subWorkSheetId 子工单ID
     * @param stepId         工序id
     * @param status         状态
     * @param deleted        逻辑删除
     * @return 个数
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public long countBySubWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(long subWorkSheetId, long stepId,int status, long deleted) {
        return Constants.LONG_ZERO;
    }


    /**
     * 通过工单ID获取待维修分析任务个数
     *
     * @param workSheetId 工单ID
     * @param stepId       工序id
     * @param status      状态
     * @param deleted     逻辑删除
     * @return 个数
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public long countByWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(long workSheetId, long stepId,int status, long deleted) {
        return Constants.LONG_ZERO;
    }

    /**
     * 通过子工单主键ID获取子工单为单位开出的最新的一条维修分析历史记录
     *
     * @param subWorkSheetId 子工单主键ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * <AUTHOR>
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1BySubWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(Long subWorkSheetId, Long deleted) {
        return Optional.empty();
    }

    /**
     * 通过工单主键ID获取工单为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * @date 2023/3/14
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1ByWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(Long workSheetId,Long deleted){
        return Optional.empty();
    }

    /**
     * 根据工单id以及状态获取唯一维修分析历史信息
     * @param workSheetId  工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1ByWorkSheetIdAndStatusAndDeleted(Long workSheetId,Integer status,Long deleted){
        return Optional.empty();
    }

    /**
     * 根据子工单id以及状态获取唯一维修分析历史信息
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1BySubWorkSheetIdAndStatusAndDeleted(Long subWorkSheetId,Integer status,Long deleted){
        return Optional.empty();
    }

    /**
     * 通过容器主键ID获取容器为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param containerId 容器主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(Long containerId,Long deleted){
        return Optional.empty();
    }


    /**
     * 据工单主键ID和状态查询第一条 维修分析记录
     * @param workSheetId 工单ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1ByWorkSheetIdAndStatusLessThanAndDeleted(Long workSheetId,int status,Long deleted){
        return Optional.empty();
    }


    /**
     * 根据子工单主键ID和状态查询第一条 维修分析记录
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1BySubWorkSheetIdAndStatusLessThanAndDeleted(Long subWorkSheetId,int status,Long deleted){
        return Optional.empty();
    }


    /**
     *
     *  通过工单主键ID、状态获取工单为单位开出的最新的一条维修分析历史记录
     * @param workSheetId 工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1ByWorkSheetIdAndStatusNotAndDeleted(Long workSheetId,Integer status,Long deleted){
        return Optional.empty();
    }

    /**
     *
     *  通过子工单主键ID、状态获取子工单为单位开出的最新的一条维修分析历史记录
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findTop1BySubWorkSheetIdAndStatusNotAndDeleted(Long subWorkSheetId,Integer status,Long deleted){
        return Optional.empty();
    }

    /**
     * 根据sn以及完成状态 获取维修分析历史
     * @param snList sn列表
     * @param status 完成状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/9
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDTO> findBySnWorkStatusSnInAndStatusNotAndDeleted(List<String> snList,Integer status,Long deleted){
        return new ArrayList<>();
    }
    /**
     * 根据容器编号以及完成状态获取 维修分析历史
     * @param containerCodes 容器编号列表
     * @param status 完成状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDTO> findByContainerDetailContainerCodeInAndStatusNotAndDeleted(List<String> containerCodes,Integer status,Long deleted){
        return new ArrayList<>();
    }

    /**
     * 通过sn工作详情主键id 以及 维修状态不为完成状态 获取维修历史信息
     * @param snWorkStatusId sn工作详情主键id
     * @param status  完成状态 2
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2022/10/14
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDTO> findBySnWorkStatusIdAndStatusNotAndDeleted(Long snWorkStatusId,Integer status,Long deleted){
        return Optional.empty();
    }


}
