package net.airuima.rbase.proxy.maintain;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.maintain.MaintainMaterialExchangeDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaintainMaterialExchangProxy {

    @BeanDefine(value = "maintainMaterialExchangeService", funcKey = "RepaireAnalysis")
    public List<MaintainMaterialExchangeDTO> batchSaveInstance(@ObjectField(value = "net.airuima.maintain.domain.procedure.MaintainMaterialExchange",isArray = true) List<MaintainMaterialExchangeDTO> maintainMaterialExchanges){
        return new ArrayList<>();
    }
}
