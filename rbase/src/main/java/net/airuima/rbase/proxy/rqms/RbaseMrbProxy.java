package net.airuima.rbase.proxy.rqms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.quality.MrbApplicantRequestDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMrbProxy {


    /**
     * 发起MRB评审
     *
     * @param mrbApplicantRequestDTO MRB评审请求参数DTO
     */
    @BeanDefine(value = "materialReviewBoardService", funcKey = "MRB")
    public void custom(@ObjectField("net.airuima.qms.domain.procedure.MaterialReviewBoard") MrbApplicantRequestDTO mrbApplicantRequestDTO) {

    }

    /**
     * 通过质检单据号列表获取 指定状态下的MRB单据个数
     *
     * @param inspectSerialNumbers 质检单据号列表
     * @return 待检数量
     */
    @BeanDefine(value = "materialReviewBoardService", funcKey = "MRB")
    public Long countTodoMrbTask(List<String> inspectSerialNumbers) {
        return Constants.LONG_ZERO;
    }


}
