package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseOrganizationProxy {


    @BeanDefine("organizationRepository")
    public OrganizationDTO findByIdAndDeleted(Long id, Long deleted){
        return null;
    }


    @BeanDefine("organizationRepository")
    public Optional<OrganizationDTO> findByCodeAndDeleted(String code, Long deleted){
        return Optional.empty();
    }

    @BeanDefine("organizationRepository")
    public List<OrganizationDTO> findByCodeInAndDeleted(List<String> orgCodes, Long deleted){
        return null;
    }
}
