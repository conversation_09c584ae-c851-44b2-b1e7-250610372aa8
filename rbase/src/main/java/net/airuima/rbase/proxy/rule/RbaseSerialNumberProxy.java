package net.airuima.rbase.proxy.rule;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.rlms.ModuleConfigDTO;
import net.airuima.rbase.dto.rule.SerialNumberConfigDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rule.SerialNumberHistoryDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseSerialNumberProxy {

    @BeanDefine(value = "serialNumberService")
    public String generate(@ObjectField("net.airuima.sys.dto.serial.SerialNumberGenerateDTO") SerialNumberDTO serialNumberDTO){
        return Constants.EMPTY;
    }

    @BeanDefine(value = "serialNumberHistoryService")
    public void  createInstance(@ObjectField("net.airuima.sys.dto.serial.SerialNumberHistoryDTO") SerialNumberHistoryDTO serialNumberHistoryDto){

    }

    @BeanDefine(value = "serialNumberConfigService")
    public ModuleConfigDTO findModuleConfig(String serialCode) {
        return null;
    }

    @BeanDefine(value = "serialNumberConfigRepository")
    public Optional<SerialNumberConfigDTO> findBySerialCodeAndDeleted(String serialCode,Long deleted){
        return Optional.empty();
    }
}
