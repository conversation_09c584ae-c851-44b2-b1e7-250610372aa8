package net.airuima.rbase.proxy.calibrate;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.calibrate.CalibrateStatusDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseCalibrateStatusProxy {

    @BeanDefine(value = "calibrateStatusRepository",funcKey = "FCalibration")
    public CalibrateStatusDTO findByWorkCellIdAndFacilityIdAndProcessAndDeleted(Long workCellId, Long facilityId, Integer process, Long deleted){
        return null;
    }

    @BeanDefine(value = "calibrateStatusRepository",funcKey = "FCalibration")
    public List<CalibrateStatusDTO> findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(Long workCellId, Integer process, Long deleted){
        return new ArrayList<>();
    }
}
