package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.organization.TeamDTO;
import org.springframework.stereotype.Component;

/**
 * 班组
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Component
public class RbaseTeamProxy {
    @BeanDefine("teamRepository")
    public TeamDTO findByIdAndDeleted(Long id, Long deleted) {
        return null;
    }
}
