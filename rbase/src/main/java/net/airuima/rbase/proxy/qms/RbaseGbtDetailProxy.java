package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.GbtDetailDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class RbaseGbtDetailProxy {

    @BeanDefine(value = "gbtDetailRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Optional<GbtDetailDTO> findByGbtIdAndInsolationLevelAndAqlAndDeleted(Long gbtId, String insolationLevel,
                                                                                Double aql, Integer number,
                                                                                Long deleted) {
        return Optional.empty();
    }

}
