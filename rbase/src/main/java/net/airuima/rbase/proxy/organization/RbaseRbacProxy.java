package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.dto.UserDTO;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseRbacProxy {

    @BeanDefine("userService")
    public UserDTO getUserByLoginName(String loginName) {
        return null;
    }

    @BeanDefine("userService")
    public UserDTO findByStaffId(Long staffId) {
        return null;
    }
}
