package net.airuima.rbase.proxy.optical;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.client.AgeingConfigInfoDTO;
import net.airuima.rbase.dto.client.BakeConfigInfoDTO;
import net.airuima.rbase.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO;
import net.airuima.rbase.dto.client.CycleBakeConfigInfoDTO;
import net.airuima.rbase.dto.ocmes.*;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseBakeCycleBakeAgeingProxy {

    /**
     *
     * 获取烘烤配置
     */
    @BeanDefine(value = "net.airuima.bake.service.base.api.IBakeConfigService", interfaceBean = true,funcKey = "Bake")
    public BakeConfigInfoDTO queryBakeConfigInfo(@ObjectField("net.airuima.bake.web.rest.base.dto.BakeConfigGetDTO") BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto) {
        return null;
    }

    /**
     * 获取温循配置
     */
    @BeanDefine(value = "net.airuima.cyclebake.service.base.api.ICycleBakeConfigService", interfaceBean = true,funcKey = "CycleBake")
    public CycleBakeConfigInfoDTO queryCycleBakeConfigInfo(@ObjectField("net.airuima.cyclebake.web.rest.base.dto.CycleBakeConfigGetDTO") BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto) {
        return null;
    }

    /**
     * 获取老化配置
     */
    @BeanDefine(value = "net.airuima.ageing.service.base.api.IAgeingConfigService", interfaceBean = true,funcKey = "Ageing")
    public AgeingConfigInfoDTO queryAgeingConfigInfo(@ObjectField("net.airuima.ageing.web.rest.base.dto.AgeingConfigGetDTO") BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto) {
        return null;
    }

    /**
     * 替换烘烤历史对应的sn
     */
    @BeanDefine(value = "net.airuima.bake.service.client.api.IBakeHistoryService", interfaceBean = true,funcKey = "Bake")
    public void  replaceBakeHistorySn(@ObjectField(value = "net.airuima.bake.dto.rworker.BakeSnReplaceDTO",isArray = true) List<SnReplaceDTO> snReplaceDtoList) {

    }

    /**
     * 替换老化历史对应的sn
     */
    @BeanDefine(value = "net.airuima.ageing.service.client.api.IAgeingHistoryService", interfaceBean = true,funcKey = "Ageing")
    public void replaceAgeingHistorySn(@ObjectField(value = "net.airuima.ageing.dto.rworker.AgeingSnReplaceDTO",isArray = true) List<SnReplaceDTO> snReplaceDtoList){

    }

    /**
     * 替换温循历史对应的sn
     */
    @BeanDefine(value = "net.airuima.cyclebake.service.client.api.ICycleBakeHistoryService", interfaceBean = true,funcKey = "CycleBake")
    public  void replaceCycleBakeHistorySn(@ObjectField(value = "net.airuima.cyclebake.dto.rworker.CycleBakeSnReplaceDTO",isArray = true) List<SnReplaceDTO> snReplaceDtoList) {

    }


    /**
     * 获取烘烤历史放入时间参数
     */
    @BeanDefine(value = "clientRworkerBakeService",funcKey = "Bake")
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryBakeHistoryPutInDate(@ObjectField(value = "net.airuima.bake.dto.rworker.BakeHistoryGetDTO") BakeCycleBakeAgeingHistoryGetDTO  bakeCycleBakeAgeingHistoryGetDTO){
        return null;
    }

    /**
     * 下交接口验证烘烤温循等参数
     *
     * @param bakeCycleBakeAgeingSaveDTO
     * @return BaseClientDto
     */
    @BeanDefine(value = "clientRworkerBakeService",funcKey = "Bake")
    public BaseClientDTO validateBakeStepInfo(@ObjectField(value = " net.airuima.bake.dto.rworker.BakeSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDTO) {
        return new BaseClientDTO(Constants.OK);
    }

    @BeanDefine(value = "clientRworkerBakeService",funcKey = "Bake")
    public BaseClientDTO saveBakeHistoryInfo(@ObjectField(value = "net.airuima.bake.dto.rworker.BakeSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeSaveInfoDTO) {
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 下交接口验证温循等参数
     * @param bakeCycleBakeAgeingSaveDTO
     * @return BaseClientDTO
     */
    @BeanDefine(value = "clientRworkerCycleBakeService",funcKey = "CycleBake")
    public BaseClientDTO validateCycleBakeStepInfo(@ObjectField(value = " net.airuima.cyclebake.dto.rworker.CycleBakeSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDTO){
        return new BaseClientDTO(Constants.OK);
    }

    @BeanDefine(value = "clientRworkerBakeService",funcKey = "CycleBake")
    public BaseClientDTO saveCycleBakeHistoryInfo(@ObjectField(value = "net.airuima.cyclebake.dto.rworker.CycleBakeSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeSaveInfoDTO) {
        return new BaseClientDTO(Constants.OK);
    }



    /**
     * 下交接口验证老化等参数
     * @param bakeCycleBakeAgeingSaveDTO
     * @return BaseClientDTO
     */
    @BeanDefine(value = "clientRworkerAgeingService",funcKey = "Ageing")
    public BaseClientDTO validateAgingStepInfo(@ObjectField(value = " net.airuima.ageing.dto.rworker.AgeingSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDTO){
        return new BaseClientDTO(Constants.OK);
    }

    @BeanDefine(value = "clientRworkerAgeingService",funcKey = "Ageing")
    public BaseClientDTO saveAgeingHistoryInfo(@ObjectField(value = "net.airuima.ageing.dto.rworker.AgeingSaveInfoDTO") BakeCycleBakeAgeingSaveDTO bakeSaveInfoDTO) {
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 获取温循历史放入时间参数
     */
    @BeanDefine(value = "clientRworkerCycleBakeService",funcKey = "CycleBake")
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryCycleBakeHistoryPutInDate(@ObjectField(value = "net.airuima.cyclebake.dto.rworker.CycleBakeHistoryGetDTO")  BakeCycleBakeAgeingHistoryGetDTO bakeCycleBakeAgeingHistoryGetDTO){
        return null;
    }

    /**
     * 获取老化历史放入时间参数
     */
    @BeanDefine(value = "clientRworkerAgeingService",funcKey = "Ageing")
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryAgeingHistoryPutInDate(@ObjectField(value = "net.airuima.ageing.dto.rworker.AgeingHistoryGetDTO") BakeCycleBakeAgeingHistoryGetDTO bakeCycleBakeAgeingHistoryGetDTO) {
        return null;
    }


    /**
     * 验证烘烤工序
     */
    @BeanDefine(value = "bakeHistoryService",funcKey = "Bake")
    public BaseDTO validBakeStepHistory(@ObjectField(value = "net.airuima.bake.dto.rworker.BakeValidDTO") BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDto){
        return new BaseDTO(Constants.OK);
    }

    /**
     * 验证温循工序
     */
    @BeanDefine(value = "cycleBakeHistoryService",funcKey = "CycleBake")
    public BaseDTO validCycleBakeStepHistory( @ObjectField(value = "net.airuima.cyclebake.dto.rworker.CycleBakeValidDTO") BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDto){
        return new BaseDTO(Constants.OK);
    }

    /**
     * 验证老化工序
     */
    @BeanDefine(value = "ageingHistoryService",funcKey = "Ageing")
    public BaseDTO validAgeingStepHistory(@ObjectField(value = "net.airuima.ageing.dto.rworker.AgeingValidDTO") BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDto) {
        return new BaseDTO(Constants.OK);
    }


    /**
     *
     * 烘烤放入取出保存
     */
    @BeanDefine(value = "bakeHistoryService",funcKey = "Bake")
    public void saveBakeStepHistory(@ObjectField(value = "net.airuima.bake.dto.rworker.BakeSaveRequestDTO") BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDto) {

    }

    /**
     *
     * 温循放入取出保存
     */
    @BeanDefine(value = "cycleBakeHistoryService",funcKey = "CycleBake")
    public void saveCycleBakeStepHistory(@ObjectField(value = "net.airuima.cyclebake.dto.rworker.CycleBakeSaveRequestDTO") BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDTO) {

    }

    /**
     *
     * 老化放入取出保存
     */
    @BeanDefine(value = "ageingHistoryService",funcKey = "Ageing")
    public void saveAgeingStepHistory(@ObjectField(value = "net.airuima.ageing.dto.rworker.AgeingSaveRequestDTO") BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDTO){

    }

    /**
     *
     * 回退对应的烘烤温循老化数据
     */
    @BeanDefine(value = "bakeHistoryService",funcKey = "Bake")
    public void logicDeletedBakeHistory(@ObjectField(value = "net.airuima.bake.web.rest.procedure.dto.RollBackBakeHistoryDTO") RollBackBakeCycleBakeAgeingHistoryDTO rollBackBakeCycleBakeAgeingHistoryDTO){

    }

    /**
     *
     * 回退对应的烘烤温循老化数据
     */
    @BeanDefine(value = "cycleBakeHistoryService",funcKey = "CycleBake")
    public void logicDeletedCycleBakeHistory(@ObjectField(value = "net.airuima.cyclebake.web.rest.procedure.dto.RollBackCycleBakeHistoryDTO") RollBackBakeCycleBakeAgeingHistoryDTO rollBackCycleBakeHistoryDTO){

    }

    /**
     *
     * 回退对应的老化数据
     */
    @BeanDefine(value = "ageingHistoryService",funcKey = "Ageing")
    public void logicDeletedAgeingHistory(@ObjectField(value = "net.airuima.ageing.web.rest.procedure.dto.RollBackAgeingHistoryDTO") RollBackBakeCycleBakeAgeingHistoryDTO rollBackBakeCycleBakeAgeingHistoryDTO){

    }

    /**
     *
     *替换已容器下交的烘烤温循老化记录的容器编号
     */
    @BeanDefine(value = "bakeHistoryService",funcKey = "Bake")
    public void replaceContainerBakeHistory(@ObjectField(value = "net.airuima.bake.web.rest.procedure.dto.ReplaceBakeContainerDTO") OcReplaceContainerDTO ocReplaceContainerDTO){

    }

    /**
     *
     *替换已容器下交的烘烤温循老化记录的容器编号
     */
    @BeanDefine(value = "cycleBakeHistoryService",funcKey = "CycleBake")
    public void replaceContainerCycleBakeHistory(@ObjectField(value = "net.airuima.cyclebake.web.rest.procedure.dto.ReplaceCycleBakeContainerDTO") OcReplaceContainerDTO replaceContainerDto){

    }


    /**
     *
     *替换已容器下交的烘烤温循老化记录的容器编号
     */
    @BeanDefine(value = "ageingHistoryService",funcKey = "Ageing")
    public void replaceContainerAgeingHistory(@ObjectField(value = "net.airuima.ageing.web.rest.procedure.dto.ReplaceAgeingContainerDTO") OcReplaceContainerDTO replaceContainerDto){

    }

}
