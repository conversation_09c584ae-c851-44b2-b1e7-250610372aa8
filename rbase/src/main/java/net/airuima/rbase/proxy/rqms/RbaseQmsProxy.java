package net.airuima.rbase.proxy.rqms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.qms.TestDataSaveDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseQmsProxy {

    @BeanDefine(value = "testDataService", funcKey = "QDataAnalysis")
    public BaseDTO saveInstance(@ObjectField("net.airuima.qms.web.rest.procedure.dto.TestDataSaveDTO") TestDataSaveDTO testDataSaveDto){
        return new BaseDTO(Constants.OK);
    }

    @BeanDefine(value = "testDataService", funcKey = "QDataAnalysis")
    public BaseDTO batchSaveInstance(@ObjectField(value = "net.airuima.qms.web.rest.procedure.dto.TestDataSaveDTO",isArray = true) List<TestDataSaveDTO> testDataSaveDtoList) {
        return new BaseDTO(Constants.OK);
    }

    @BeanDefine(value = "testDataRepository",funcKey = "QDataAnalysis")
    public void replaceSn(String originSn,String targetSn){

    }
}
