package net.airuima.rbase.proxy.bom;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseBomProxy {

    @BeanDefine("bomService")
    public List<BomDTO> findByBomInfoId(Long bomInfoId){
        return new ArrayList<>();
    }

    @BeanDefine("bomService")
    public  List<BomDTO> findByMainMaterialId(List<Long> mainMaterialIds){
        return new ArrayList<>();
    }

    @BeanDefine("bomInfoService")
    public List<BomInfoDTO> findByMaterialId(Long materialId) {
        return null;
    }

    @BeanDefine("bomInfoService")
    public BomInfoDTO get(Long id){
        return null;
    }

    @BeanDefine("bomInfoService")
    public BomInfoDTO findByCode(String code, Boolean isEnable) {
        return null;
    }

    @BeanDefine("bomInfoService")
    public void clone(Long originMaterialId, Long targetMaterialId) {

    }

    @BeanDefine("materialAttributeService")
    public MaterialAttributeDTO findByCode(String code) {
        return null;
    }

    @BeanDefine("materialService")
    public MaterialDTO saveInstance(@ObjectField("net.airuima.bom.domain.Material") MaterialDTO material) {
        return null;
    }
}
