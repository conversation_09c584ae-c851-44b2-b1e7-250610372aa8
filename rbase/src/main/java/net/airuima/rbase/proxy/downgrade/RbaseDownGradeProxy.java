package net.airuima.rbase.proxy.downgrade;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Component
public class RbaseDownGradeProxy {


    /**
     * 工单完成时更新降级数量
     * @param workSheet 工单
     */
    @BeanDefine(value = "pedigreeDownGradeDetailService",funcKey = "Downgrade")
    public void updateDownGradeNumberWhenWorkSheetComplete(WorkSheet workSheet){

    }


    /**
     * 创建返修单时创建降级申请记录
     * @param workSheet 返修单
     * <AUTHOR>
     * @since 1.8.1
     */
    @BeanDefine(value = "pedigreeDownGradeDetailService",funcKey = "Downgrade")
    public void createDownGradeDetailWhenCreateReworkWorkSheet(WorkSheet workSheet){

    }
}
