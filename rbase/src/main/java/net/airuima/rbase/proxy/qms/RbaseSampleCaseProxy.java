package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RbaseSampleCaseProxy {

    @BeanDefine(value = "varietyRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public SampleCaseDTO findByCodeAndIsEnableAndDeleted(String code, Boolean isEnable, Long deleted) {
        return null;
    }

    @BeanDefine(value = "sampleCaseRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public List<SampleCaseDTO> findByCategoryAndNumberAndAcAndDeleted(Integer category, Integer number, Integer ac,
                                                                      Long deleted) {
        return null;
    }

    @BeanDefine(value = "sampleCaseRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public List<SampleCaseDTO> findByCategoryAndRateAndQualifiedRateAndDeleted(Integer category, double rate,
                                                                               double qualifiedRate, Long deleted) {
        return null;
    }

    @BeanDefine(value  = "sampleCaseService",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public SampleCaseDTO save(SampleCaseDTO sampleCaseDTO) {
        return null;
    }

}
