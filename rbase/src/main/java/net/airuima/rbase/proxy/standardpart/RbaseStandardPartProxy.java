package net.airuima.rbase.proxy.standardpart;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientStandardPartCheckResultSaveDTO;
import net.airuima.rbase.dto.standardpart.StandardPartDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseStandardPartProxy {

    @BeanDefine(value = "standardPartCheckResultDetailService",funcKey = "StandardPart")
    public net.airuima.rbase.dto.client.base.BaseClientDTO saveCheckResultDetail(@ObjectField(value = "net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultSaveDTO",isArray = true) List<ClientStandardPartCheckResultSaveDTO> standardPartCheckResultSaveDTOS){
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 通过标准件SN获取标准件
     *
     * @param sn      标准件SN
     * @param deleted 逻辑删除
     */
    @BeanDefine(value = "standardPartCheckResultDetailService",funcKey = "StandardPart")
    public Optional<StandardPartDTO> findBySnAndDeleted(String sn, Long deleted){
        return Optional.empty();
    }

    /**
     * 通过标准件SN集合获取标准件
     *
     * @param sn      标准件SN集合
     * @param deleted 逻辑删除
     */
    @BeanDefine(value = "standardPartRepository",funcKey = "StandardPart")
    public List<StandardPartDTO> findBySnInAndDeleted(List<String> sn, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 通过标准件ID获取标准件
     *
     * @param standardPartId 标准件ID
     * @param deleted        逻辑删除
     */
    @BeanDefine(value = "standardPartRepository",funcKey = "StandardPart")
    public Optional<StandardPartDTO> findByIdAndDeleted(Long standardPartId, Long deleted){
        return Optional.empty();
    }
}
