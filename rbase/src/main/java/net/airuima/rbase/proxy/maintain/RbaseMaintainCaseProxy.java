package net.airuima.rbase.proxy.maintain;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.maintain.MaintainCaseDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaintainCaseProxy {

    /**
     * 根据维修方案编码获取 维修分析方案
     * @param code 维修方案编码
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.Optional<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案
     */
    @BeanDefine(value = "maintainCaseRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainCaseDTO> findByCodeAndDeleted(String code, Long deleted){
        return Optional.empty();
    }

    /**
     * 根据 维修方案主键id列表 获取维修方案列表
     * @param ids 维修方案主键id列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @return java.util.List<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案列表
     */
    @BeanDefine(value = "maintainCaseRepository", funcKey = "RepaireAnalysis")
    public List<MaintainCaseDTO> findByIdInAndDeleted(List<Long> ids, Long deleted){
        return new ArrayList<>();
    }
}
