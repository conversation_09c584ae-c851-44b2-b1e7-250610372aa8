package net.airuima.rbase.proxy.dynamicdata;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.dynamic.StepDynamicDataDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseStepDynamicDataProxy {


    /**
     * 克隆动态数据表单
     *
     * @param tapedigree            目标产品谱系
     * @param clonePedigreeId 源产品谱系
     */
    @BeanDefine(value = "stepDynamicDataService",funcKey = "StepDynamicData")
    public void cloneStepDynamicData(long clonePedigreeId, Pedigree tapedigree){

    }

    @BeanDefine(value = "stepDynamicDataRepository",funcKey = "StepDynamicData")
    public List<StepDynamicDataDTO> findByPedigreeIdInAndWorkFlowIdAndStepIdAndDeleted(List<Long> pedigreeIdList, Long workFlowId, Long stepId, Boolean enable, Long deleted){
        return new ArrayList<>();
    }
}
