package net.airuima.rbase.proxy.dynamicdata;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseStepDynamicDataColumnProxy {

    /**
     * 通过动态数据id，获取对应的 动态元数据
     *
     * @param stepDynamicDataId 动态数据id
     * @param deleted           逻辑删除
     */
    @BeanDefine(value = "stepDynamicDataColumnRepository",funcKey = "StepDynamicData")
    public List<StepDynamicDataColumnDTO> findByStepDynamicDataIdAndDeleted(Long stepDynamicDataId, Long deleted){
        return new ArrayList<>();
    }
}
