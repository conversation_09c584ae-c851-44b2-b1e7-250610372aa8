package net.airuima.rbase.proxy.rfms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.rfms.FacilityLedgerDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * classNote
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Component
public class RbaseFacilityLedgerProxy {

    /**
     * 通过设备id获取保养历史信息
     *
     * @param facilityId 设备id
     * @param deleted 逻辑删除
     */
    @BeanDefine(value = "facilityLedgerRepository",funcKey = "FBase")
    public List<FacilityLedgerDTO> findByFacilityIdAndDeleted(Long facilityId, Long deleted) {
        return null;
    }
}
