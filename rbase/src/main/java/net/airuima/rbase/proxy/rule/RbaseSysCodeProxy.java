package net.airuima.rbase.proxy.rule;

import net.airuima.config.bean.BeanDefine;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseSysCodeProxy {

    /**
     * 根据系统配置编码获取系统配置
     * @param code 系统配置编码
     * @return 配置结果
     */
    @BeanDefine(value = "systemCodeService")
    public String findByCode(String code) {
        return null;
    }
}
