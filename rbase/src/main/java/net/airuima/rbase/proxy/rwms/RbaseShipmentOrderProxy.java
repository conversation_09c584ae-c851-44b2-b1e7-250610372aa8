package net.airuima.rbase.proxy.rwms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.rwms.SyncShipmentOrderDTO;
import org.springframework.stereotype.Component;

/**
 * 出库单代理类
 */
@Component
public class RbaseShipmentOrderProxy {

    /**
     * 同步出库单数据
     *
     * @param syncShipmentOrderDTO 同步出库单DTO
     */
    @BeanDefine(value = "shipmentOrderService", funcKey = "RwmsService")
    public void syncShipmentOrder(@ObjectField("net.airuima.rwms.dto.rwms.SyncShipmentOrderDTO") SyncShipmentOrderDTO syncShipmentOrderDTO) {

    }
}
