package net.airuima.rbase.proxy.bom;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaterialUnitProxy {

    @BeanDefine("meteringUnitRepository")
    public MeteringUnitDTO findByCodeAndDeleted(String code, Long deleted){
        return null;
    }

    @BeanDefine("meteringUnitRepository")
    public MeteringUnitDTO findByIdAndDeleted(Long id, Long deleted){
        return null;
    }
}
