package net.airuima.rbase.proxy.maintain;

import net.airuima.config.annotation.DataFilter;
import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.maintain.MaintainCaseDTO;
import net.airuima.rbase.dto.maintain.PedigreeMaintainCaseDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbasePedigreeMaintainCaseProxy {

    /**
     * 根据产品谱系的优先级获取维修方案
     *
     * @param pedigree
     * @return List<PedigreeMaintainCase>
     * <AUTHOR>
     */
    @BeanDefine(value = "pedigreeMaintainCaseService", funcKey = "RepaireAnalysis")
    public List<PedigreeMaintainCaseDTO> findPedigreeMaintainCase(Pedigree pedigree) {
        return new ArrayList<>();
    }

    /**
     * 通过客户主键id，产品谱系主键id获取对应的维修分析方案列表
     *
     * @param clientId   客户主键id
     * @param pedigreeId 产品谱系id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.maintiancase.PedigreeMaintainCase> 产品谱系维修方案列表
     * <AUTHOR>
     */
    @BeanDefine(value = "pedigreeMaintainCaseRepository", funcKey = "RepaireAnalysis")
    public List<PedigreeMaintainCaseDTO> findByClientIdAndPedigreeIdAndDeleted(Long clientId, Long pedigreeId, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 通过客户主键id，产品谱系主键id 维修分析方案主键id产品谱系维修方案列表
     *
     * @param clientId       客户主键id
     * @param pedigreeId     产品谱系主键id
     * @param maintainCaseId 维修分析方案主键id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.maintiancase.PedigreeMaintainCase> 产品谱系维修方案列表
     * <AUTHOR>
     */
    @BeanDefine(value = "pedigreeMaintainCaseRepository", funcKey = "RepaireAnalysis")
    public List<PedigreeMaintainCaseDTO> findByClientIdAndPedigreeIdAndMaintainCaseIdAndDeleted(Long clientId, Long pedigreeId, Long maintainCaseId, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 按层级的获取维修分析的工艺路线
     *
     * @param pedigree     产品谱系
     * @param maintainCase 维修分析方案
     * @return List<PedigreeMaintainCase>
     * <AUTHOR>
     */
    @BeanDefine(value = "pedigreeMaintainCaseService", funcKey = "RepaireAnalysis")
    public List<PedigreeMaintainCaseDTO> findPedigreeMaintainCaseWorkFlow(Pedigree pedigree, @ObjectField("net.airuima.maintain.domain.base.MaintainCase") MaintainCaseDTO maintainCase){
        return new ArrayList<>();
    }

    /**
     * 按层级的获取维修分析的工艺路线
     *
     * @param pedigree
     * @return java.util.List<net.airuima.rbase.domain.base.maintiancase.PedigreeMaintainCase>
     * <AUTHOR>
     */
    @BeanDefine(value = "pedigreeMaintainCaseService", funcKey = "RepaireAnalysis")
    public List<PedigreeMaintainCaseDTO> findPedigreeMaintainCaseWorkFlow(Pedigree pedigree, Long clientId) {
        return new ArrayList<>();
    }
}
