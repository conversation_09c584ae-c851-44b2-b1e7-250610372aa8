package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RbaseCheckItemProxy {

    @BeanDefine(value = "checkItemRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public List<CheckItemDTO> findByCodeInAndDeleted(List<String> codes, Long deleted) {
        return null;
    }

    @BeanDefine(value = "checkItemRepository",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public CheckItemDTO findByIdAndDeleted(Long id, Long deleted) {
        return null;
    }

}
