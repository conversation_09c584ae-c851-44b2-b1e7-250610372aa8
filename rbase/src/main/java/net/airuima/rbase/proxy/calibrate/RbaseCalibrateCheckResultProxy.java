package net.airuima.rbase.proxy.calibrate;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.calibrate.CalibrateCheckResultDTO;
import net.airuima.rbase.dto.client.ClientCalibrateCheckResultSaveDTO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseCalibrateCheckResultProxy {

    /**
     * 通过台位编码及最新状态获取最新记录
     *
     * @param workCellCode 台位编码
     * @param isLatest     是否为最新记录
     * @param deleted      逻辑删除
     */
    @BeanDefine(value = "calibrateCheckResultRepository",funcKey = "FCalibration")
    public CalibrateCheckResultDTO findByWorkCellCodeAndIsLatestAndAndDeleted(String workCellCode, Boolean isLatest, Long deleted){
        return null;
    }

    /**
     * 通过台位编码 ，标准件SN,测试开始时间及测试结束时间获取列表
     *
     * @param workCellCode 台位编码
     * @param standPartId          标准件ID
     * @param startTime    测试开始时间
     * @param endTime      测试结束时间
     * @param deleted      逻辑删除
     */
    @BeanDefine(value = "calibrateCheckResultRepository",funcKey = "FCalibration")
    public List<CalibrateCheckResultDTO> findByWorkCellCodeAndStandardPartIdAndTestTimeGreaterThanEqualAndTestTimeLessThanEqualAndDeleted(String workCellCode, Long standPartId, LocalDateTime startTime, LocalDateTime endTime, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 保存台位校准数据
     *
     * @param clientCalibrateCheckResultSaveDtoList 台位校准数据参数DTO列表
     * @return BaseClientDTO
     */
    @BeanDefine(value = "calibrateCheckResultService",funcKey = "FCalibration")
    public net.airuima.rbase.dto.client.base.BaseClientDTO saveCalibrateCheckResult(@ObjectField(value = "net.airuima.calibrate.web.rest.procedure.dto.CalibrateCheckResultImportSaveDTO",isArray = true) List<ClientCalibrateCheckResultSaveDTO> clientCalibrateCheckResultSaveDtoList){
        return new BaseClientDTO(Constants.OK);
    }
}
