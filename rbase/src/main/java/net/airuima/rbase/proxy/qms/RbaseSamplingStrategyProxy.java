package net.airuima.rbase.proxy.qms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.springframework.stereotype.Component;

@Component
public class RbaseSamplingStrategyProxy {

    @BeanDefine(value = "samplingStrategyServiceImpl",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Integer fullInspection(@ObjectField("net.airuima.qms.domain.base.SampleCase") SampleCaseDTO sampleCase,
                                  Integer number) {
        return null;
    }

    @BeanDefine(value = "samplingStrategyServiceImpl",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Integer fixedSizeSampling(@ObjectField("net.airuima.qms.domain.base.SampleCase") SampleCaseDTO sampleCase,
                                     Integer number) {
        return null;
    }

    @BeanDefine(value = "samplingStrategyServiceImpl",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Integer percentageSampling(@ObjectField("net.airuima.qms.domain.base.SampleCase") SampleCaseDTO sampleCase,
                                      Integer number) {
        return null;
    }

}
