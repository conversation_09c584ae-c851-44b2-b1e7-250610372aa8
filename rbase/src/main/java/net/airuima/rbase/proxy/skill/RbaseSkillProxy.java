package net.airuima.rbase.proxy.skill;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.skill.SkillDTO;
import net.airuima.rbase.dto.skill.StaffSkillDTO;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseSkillProxy {

    /**
     * 获取产品谱系对应的工序技能
     *
     * @param pedigree 产品谱系
     * @param step     工序
     * @param workFlow 工艺路线
     * @return List<SkillDTO>
     **/
    @BeanDefine(value = "pedigreeStepSkillService",funcKey = "StaffSkillControl")
    public List<SkillDTO> findPedigreeStepSkills(Pedigree pedigree, WorkFlow workFlow, Step step) {
        return new ArrayList<>();
    }

    /**
     * 根据员工主键ID等条件获取合规的技能列表
     * @param staffId 员工主键ID
     * @param compareDate 比较日期
     * @param deleted        逻辑删除
     * @return  技能列表
     */
    @BeanDefine(value = "staffSkillRepository",funcKey = "StaffSkillControl")
    public List<SkillDTO> findLegalSkillByStaffIdAndDeleted(long staffId, LocalDate compareDate, long deleted){
        return new ArrayList<>();
    }

    /**
     * 根据员工主键ID等条件获取合规的技能列表
     *
     * @param staffId 员工主键ID
     * @param deleted        逻辑删除
     * @return  技能列表
     */
    @BeanDefine(value = "staffSkillRepository",funcKey = "StaffSkillControl")
    public List<StaffSkillDTO> findAllSkillByStaffIdAndDeleted(long staffId, long deleted){
        return new ArrayList<>();
    }
}
