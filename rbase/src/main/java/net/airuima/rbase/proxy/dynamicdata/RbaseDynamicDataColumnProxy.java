package net.airuima.rbase.proxy.dynamicdata;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.dynamic.DynamicDataColumnDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseDynamicDataColumnProxy {


    @BeanDefine(value = "dynamicDataColumnRepository",funcKey = "StepDynamicData")
    public List<DynamicDataColumnDTO> findByParentIdInAndDeleted(List<Long> parentId, Long deleted){
        return new ArrayList<>();
    }
}
