package net.airuima.rbase.client.feign.flowable;

import net.airuima.rbase.dto.flowable.ProcessInstanceStartDTO;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;
import org.springframework.http.ResponseEntity;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/15
 */
public class ProcessInstanceFeignFallBack implements ProcessInstanceFeign{

    /**
     * 启动流程实例
     * @param processInstanceStartDto 启动流程实例参数
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO> 流程节点完成返回信息
     */
    @Override
    public ResponseEntity<ProcessRunTimeEventDTO> startProcess(ProcessInstanceStartDTO processInstanceStartDto) {
        return null;
    }
}
