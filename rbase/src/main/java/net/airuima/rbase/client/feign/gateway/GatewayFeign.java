package net.airuima.rbase.client.feign.gateway;

import net.airuima.client.AuthorizedFeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
@AuthorizedFeignClient(name = "gateway", fallback = GatewayFeignFallBack.class, url = "${debug.feign.client.config.gateway.url:}")
public interface GatewayFeign {

    /**
     * rworker登录
     *
     * @param userName 用户名
     * @param password 密码
     * @return org.springframework.http.ResponseEntity<java.lang.String> 结果
     */
    @RequestMapping(value = "/auth/rworker/login", method = RequestMethod.POST)
    ResponseEntity<String> loginFromRworker(@RequestParam("userName") String userName, @RequestParam("password") String password);

    /**
     * 通过授权码执行sso登录
     */
    @RequestMapping(value = "/sso/login", method = RequestMethod.GET)
    ResponseEntity<String> ssoLogin(@RequestParam("code") String code, @RequestParam("redirectUri") String redirectUri);
}
