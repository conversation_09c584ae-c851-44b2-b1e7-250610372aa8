package net.airuima.rbase.client.feign.digiwin;

import net.airuima.rbase.dto.sync.SyncReworkSheetDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
public class SyncReworkSheetFeignClientFallback implements SyncReworkSheetFeignClient {

    /**
     * 同步返工单
     * @param syncReworkSheetDtos 返工单信息
     * <AUTHOR>
     * @date  2023/3/20
     * @return void
     */
    @Override
    public void syncReworkSheet(List<SyncReworkSheetDTO> syncReworkSheetDtos) {
        return;
    }
}
