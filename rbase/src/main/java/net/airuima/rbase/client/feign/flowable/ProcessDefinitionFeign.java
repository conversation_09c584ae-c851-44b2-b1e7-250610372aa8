package net.airuima.rbase.client.feign.flowable;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.flowable.FlowProcessDefinitionDTO;
import net.airuima.rbase.dto.flowable.ProcessDefinitionDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/15
 */
@AuthorizedFeignClient(name = "flowable", fallback = ProcessDefinitionFeignFallBack.class, url = "${debug.feign.client.config.flowable.url:}")
public interface ProcessDefinitionFeign {

    /**
     * 通过流程定义key和版本获取最新流程
     *
     * @param processKey 流程定义Key
     * @param version    版本
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.flowable.ProcessDefinitionDTO>> 流程定义信息
     */
    @RequestMapping(value = "/processdefinition/api/v1/{processKey}/{version}", method = RequestMethod.GET)
    ResponseEntity<ProcessDefinitionDTO> byProcessKeyAndVersion(@PathVariable("processKey") String processKey, @PathVariable("version") Integer version);

    /**
     * 通过流程定义id数组和是否挂起条件查询流程定义列表
     *
     * @param suspend 1:已激活;2:已挂起
     * @param ids     流程定义id数组
     * @return org.springframework.http.ResponseEntity<java.util.List<net.airuima.rbase.dto.flowable.ProcessDefinitionDTO>> 流程定义信息列表
     */
    @RequestMapping(value = "/processdefinition/api/v1/{suspend}", method = RequestMethod.GET)
    ResponseEntity<List<ProcessDefinitionDTO>> listBySuspendAndIds(@PathVariable("suspend") Integer suspend, @RequestParam("ids") Set<String> ids);

    /**
     * 通过流程定义KEY获取最新版本的的流程定义
     *
     * @param processDefinitionKey 流程定义KEY
     * @return net.airuima.rbase.dto.flowable.FlowProcessDefinitionDTO 流程定义信息
     */
    @RequestMapping(value = "/processdefinition/api/v1/lastest/{processDefinitionKey}", method = RequestMethod.GET)
    FlowProcessDefinitionDTO findLatestActiveProcessByProcessKey(@PathVariable("processDefinitionKey") String processDefinitionKey);
}
