package net.airuima.rbase.client.feign.flowable;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.flowable.FlowableResultDTO;
import net.airuima.rbase.dto.flowable.FlowableSingleToDoTaskDTO;
import net.airuima.rbase.dto.flowable.FlowableTaskCompleteDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/15
 */
@AuthorizedFeignClient(name = "flowable", fallback = ProcessTaskFeignFallBack.class,url = "${debug.feign.client.config.flowable.url:}")
public interface ProcessTaskFeign {

    /**
     * 通过业务记录ID及登录名获取业务当前待办任务及已办历史列表
     * @param loginName 登录名
     * @param businessKey 业务ID
     * @return FlowableAssignTaskDTO 当前业务个人待办任务信息
     */
    @RequestMapping(value = "/task/api/v1/single/todo/{loginName}/{businessKey}", method = RequestMethod.GET)
    FlowableSingleToDoTaskDTO toDoTaskByBusinessKeyAndLoginName(@PathVariable("loginName") String loginName, @PathVariable("businessKey") String businessKey);


    /**
     * 完成任务
     * @param flowableTaskCompleteDto 任务处理参数
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.flowable.FlowableResultDTO> 结果信息
     */
    @RequestMapping(value = "/task/api/v1/completeTask", method = RequestMethod.POST)
    ResponseEntity<FlowableResultDTO> completeTask(@RequestBody FlowableTaskCompleteDTO flowableTaskCompleteDto);


    /**
     * 任务退回，任务重新触发、删除已有任务
     * 通过业务ID、流程实例id、及任务定义key删除其他分支任务
     *
     * @param processInstanceId 流程实例
     * @param businessKey 业务ID
     * @param taskDefinitionKey 任务定义key
     */
    @DeleteMapping("/task/api/v1/delete/{businessKey}/{processInstanceId}/{taskDefinitionKey}")
    void taskDelete(@PathVariable("businessKey") String businessKey, @PathVariable("processInstanceId") String processInstanceId, @PathVariable("taskDefinitionKey") String taskDefinitionKey);
}
