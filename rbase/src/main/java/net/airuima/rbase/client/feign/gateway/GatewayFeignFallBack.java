package net.airuima.rbase.client.feign.gateway;

import org.springframework.http.ResponseEntity;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
public class GatewayFeignFallBack implements GatewayFeign {

    /**
     * rworker登录
     * @param userName 用户名
     * @param password 密码
     * @return org.springframework.http.ResponseEntity<java.lang.String> 结果
     */
    @Override
    public ResponseEntity<String> loginFromRworker(String userName, String password) {
        return null;
    }

    /**
     * 通过授权码执行sso登录
     */
    @Override
    public ResponseEntity<String> ssoLogin(String code, String redirectUri) {
        return null;
    }
}
