package net.airuima.rbase.client.feign.flowable;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.flowable.ProcessInstanceStartDTO;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/15
 */
@AuthorizedFeignClient(name = "flowable", fallback = ProcessInstanceFeignFallBack.class,url = "${debug.feign.client.config.flowable.url:}")
public interface ProcessInstanceFeign {

    /**
     * 启动流程实例
     * @param processInstanceStartDto 启动流程实例参数
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO> 流程节点完成返回信息
     */
    @RequestMapping(value = "/instance/api/v1/startProcess",method = RequestMethod.POST)
    ResponseEntity<ProcessRunTimeEventDTO> startProcess(@RequestBody ProcessInstanceStartDTO processInstanceStartDto);
}
