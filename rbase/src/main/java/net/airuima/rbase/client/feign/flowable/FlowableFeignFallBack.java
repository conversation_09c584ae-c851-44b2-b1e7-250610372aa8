package net.airuima.rbase.client.feign.flowable;

import net.airuima.rbase.dto.flowable.FlowableResultDTO;
import net.airuima.rbase.dto.flowable.ProcessDefinitionDTO;
import net.airuima.rbase.dto.flowable.TaskDTO;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
public class FlowableFeignFallBack implements FlowableFeign {

    /**
     * 根据用户主键ID获取该用户能够启动的流程列表
     *
     * @param userId 用户主键ID
     * @return java.util.List<net.airuima.rbase.dto.flowable.ProcessDefinitionDTO> 流程列表
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @Override
    public List<ProcessDefinitionDTO> findProcessByUserId(String userId) {
        return Collections.emptyList();
    }

    /**
     * 根据用户主键ID获取待办任务列表
     *
     * @param userId 用户ID
     * @return java.util.List<net.airuima.rbase.dto.flowable.TaskDTO> 待办任务列表
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @Override
    public List<TaskDTO> findPersonOrGroupTask(String userId) {
        return Collections.emptyList();
    }

    /**
     * 完成任务
     * @param taskDto    任务参数
     * @return net.airuima.rbase.dto.flowable.FlowableResultDTO  结果信息
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @Override
    public FlowableResultDTO completeTask(TaskDTO taskDto) {
        return null;
    }

    /**
     * 启动业务流程
     * <AUTHOR>
     * @param businessKey 业务主键ID
     * @param processDefinitionId 流程定义主键ID
     * @param userId 当前登录账户主键ID
     * @return String 结果信息
     * @date 2021-03-03
     **/
    @Override
    public String startProcess(String businessKey, String processDefinitionId, Long userId) {
        return null;
    }

    /**
     * 根据流程定义的KEY模糊查询获取最新版本的流程定义数据
     * <AUTHOR>
     * @param processDefintionKey     流程定义KEY
     * @return net.airuima.rbase.dto.flowable.ProcessDefinitionDTO 流程定义信息
     * @date 2021-07-26
     **/
    @Override
    public ProcessDefinitionDTO findLatestProcessDefinition(String processDefintionKey) {
        return null;
    }
}
