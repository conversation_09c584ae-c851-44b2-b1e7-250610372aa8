package net.airuima.rbase.client.feign.digiwin;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.sync.SyncReworkSheetDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
@AuthorizedFeignClient(name = "mom", fallback = SyncReworkSheetFeignClientFallback.class, url = "${debug.feign.client.config.mom.url:}")
public interface SyncReworkSheetFeignClient {

    /**
     * 同步返工单
     * @param syncReworkSheetDtos 返工单信息
     * <AUTHOR>
     * @date  2023/3/20
     * @return void
     */
    @RequestMapping(value = "/api/repair-work-sheet/syncReworkSheet",method = RequestMethod.POST)
    void syncReworkSheet(@RequestBody List<SyncReworkSheetDTO> syncReworkSheetDtos);
}
