package net.airuima.rbase.client.feign.flowable;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.flowable.FlowableResultDTO;
import net.airuima.rbase.dto.flowable.ProcessDefinitionDTO;
import net.airuima.rbase.dto.flowable.TaskDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
@AuthorizedFeignClient(name = "flowable", fallback = FlowableFeignFallBack.class,url = "${debug.feign.client.config.flowable.url:}")
public interface FlowableFeign {

    /**
     * 根据用户主键ID获取该用户能够启动的流程列表
     *
     * @param userId 用户主键ID
     * @return java.util.List<net.airuima.rbase.dto.flowable.ProcessDefinitionDTO> 流程列表
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @RequestMapping(value = "/execution/api/v1/getStartProcess/{userId}", method = RequestMethod.GET)
    List<ProcessDefinitionDTO> findProcessByUserId(@PathVariable("userId") String userId);

    /**
     * 根据用户主键ID获取待办任务列表
     *
     * @param userId 用户ID
     * @return java.util.List<net.airuima.rbase.dto.flowable.TaskDTO> 待办任务列表
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @RequestMapping(value = "/task/api/v1/getPersonOrGroupTask/{userId}", method = RequestMethod.GET)
    List<TaskDTO> findPersonOrGroupTask(@PathVariable("userId") String userId);

    /**
     * 完成任务
     * @param taskDto    任务参数
     * @return net.airuima.rbase.dto.flowable.FlowableResultDTO  结果信息
     * <AUTHOR>
     * @date 2021-03-03
     **/
    @RequestMapping(value = "/task/api/v1/completeTask", method = RequestMethod.POST)
    FlowableResultDTO completeTask(@RequestBody TaskDTO taskDto);

    /**
     * 启动业务流程
     * <AUTHOR>
     * @param businessKey 业务主键ID
     * @param processDefinitionId 流程定义主键ID
     * @param userId 当前登录账户主键ID
     * @return String 结果信息
     * @date 2021-03-03
     **/
    @RequestMapping(value = "/execution/api/v1/startProcess", method = RequestMethod.POST)
    String startProcess(@RequestParam("businessKey") String businessKey, @RequestParam("processDefinitionId") String processDefinitionId, @RequestParam("userId") Long userId);

    /**
     * 根据流程定义的KEY模糊查询获取最新版本的流程定义数据
     * <AUTHOR>
     * @param processDefintionKey     流程定义KEY
     * @return net.airuima.rbase.dto.flowable.ProcessDefinitionDTO 流程定义信息
     * @date 2021-07-26
     **/
    @RequestMapping(value = "/processdefinition/api/v1/latestProcessDefinition",method = RequestMethod.GET)
    ProcessDefinitionDTO findLatestProcessDefinition(@RequestParam(value = "processDefintionKey",required = true) String processDefintionKey);
}
