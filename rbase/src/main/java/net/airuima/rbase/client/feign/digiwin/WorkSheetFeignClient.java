package net.airuima.rbase.client.feign.digiwin;

import io.swagger.v3.oas.annotations.Operation;
import net.airuima.client.AuthorizedFeignClient;
import net.airuima.rbase.dto.digiwin.DigiwinSubWorkSheetCompleteDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@AuthorizedFeignClient(name = "mom", fallback = WorkSheetFeignClientFallback.class, url = "${debug.feign.client.config.mom.url:}")
public interface WorkSheetFeignClient {

    /**
     * 总工单的完工信息收集并保存
     * @param digiwinSubWorkSheetCompleteDTO 总工单信息dto
     * <AUTHOR>
     * @date  2021/8/12
     * @return java.lang.String 结果信息
     */
    @Operation(summary = "总工单的完工信息收集并保存")
    @RequestMapping(value = "/api/work-sheet-completes/syncWorkSheetComplete",method = RequestMethod.POST)
    String syncWorkSheetComplete(@RequestBody DigiwinSubWorkSheetCompleteDTO digiwinSubWorkSheetCompleteDTO);

}
