package net.airuima.rbase.integrate.message;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.control.EventConfigDTO;
import net.airuima.rbase.dto.message.TaskFeignDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@FuncInterceptor("Message")
public interface ITaskMessageService {

    /**
     * 发送任务消息
     *
     * @param taskDTO 任务消息参数
     */
    default void taskSystem(TaskFeignDTO taskDTO) {

    }

    /**
     * 调用消息服务发送系统消息
     * @param serialNumber 消息主体序列号(如工单号、设备编码等)
     * @param eventConfigDTO 事件配置DTO
     * @param starter 事件发起人
     * @param recordDate 发起时间
     */
    default void sendMessage(String serialNumber, EventConfigDTO eventConfigDTO, String starter, LocalDateTime recordDate){

    }

}
