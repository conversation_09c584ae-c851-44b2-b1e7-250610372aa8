package net.airuima.rbase.integrate.rmps;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface IWorkSheetSyncMpsService {

    /**
     * 同步工单到MPS
     *
     * @param workSheetId 工单主键ID
     */
    @FuncInterceptor(value = "RmpsService")
    default void syncMpsWorkSheet(Long workSheetId) {

    }
}
