package net.airuima.rbase.integrate.rmps;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.rmps.RbaseRmpsProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.procedure.aps.plugin.impl.WorkSheetServiceImpl;
import net.airuima.rbase.web.rest.procedure.aps.dto.WorkSheetSyncMpsDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 同步工单信息到MPS Service 实现
 *
 * <AUTHOR>
 * @date 2023/05/25
 */
@Order(0)
@Service
public class WorkSheetSyncMpsService implements IWorkSheetSyncMpsService{

    private final Logger log = LoggerFactory.getLogger(WorkSheetServiceImpl.class);


    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private RbaseRmpsProxy rbaseRmpsProxy;

    /**
     * 同步工单到MPS
     *
     * @param workSheetId 工单主键ID
     */
    @Override
    public void syncMpsWorkSheet(Long workSheetId) {
        //查询工单
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return;
        }
        WorkSheet workSheet = workSheetOptional.get();
        //设置工单同步参数
        WorkSheetSyncMpsDTO workSheetSyncMpsDto = new WorkSheetSyncMpsDTO();
        try {
            //获取客户编码
            String clientCode = Optional.ofNullable(workSheet.getClientDTO()).map(ClientDTO::getCode).orElse(null);
            // 获取物料编码
            String materialCode = Optional.ofNullable(workSheet.getPedigree()).map(Pedigree::getMaterialDto).map(MaterialDTO::getCode).orElse(null);
            if (StringUtils.isEmpty(materialCode)) {
                return;
            }
            // 设置MPS工单同步参数
            workSheetSyncMpsDto.setSerialNumber(workSheet.getSerialNumber()).setNumber(workSheet.getNumber())
                    .setClientCode(clientCode)
                    .setMaterialCode(materialCode)
                    .setPo(Optional.ofNullable(workSheet.getSaleOrder()).map(SaleOrder::getSerialNumber).orElse(""));
            // 同步工单到MPS
            rbaseRmpsProxy.sync(workSheetSyncMpsDto);
        } catch (Exception e) {
            log.error("工单同步MPS失败", e);
        }

    }
}
