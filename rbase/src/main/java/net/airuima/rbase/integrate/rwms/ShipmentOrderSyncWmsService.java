package net.airuima.rbase.integrate.rwms;

import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.rwms.SyncShipmentOrderDTO;
import net.airuima.rbase.proxy.rwms.RbaseShipmentOrderProxy;
import net.airuima.rbase.service.procedure.batch.WsMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步工单到WMS
 */
@Order(0)
@Service
public class ShipmentOrderSyncWmsService implements IShipmentOrderSyncWmsService {

    @Autowired
    private RbaseShipmentOrderProxy rbaseShipmentOrderProxy;

    @Autowired
    private WsMaterialService wsMaterialService;

    /**
     * 保存同步出库单及明细数据至RWMS
     *
     * @param workSheet 工单
     */
    @Override
    public void syncShipmentOrder(WorkSheet workSheet) {
        List<WsMaterial> wsMaterialList = wsMaterialService.findByWorkSheetId(workSheet.getId());
        if (wsMaterialList.isEmpty()) {
            return;
        }
        SyncShipmentOrderDTO syncShipmentOrderDTO = new SyncShipmentOrderDTO();
        syncShipmentOrderDTO.setOriginSerialNumber(workSheet.getSerialNumber()).setClientId(workSheet.getClientId()).setEstimateTime(workSheet.getPlanStartDate());
        List<SyncShipmentOrderDTO.Detail> details = new ArrayList<>();
        // TODO 后续考虑手动同步及齐套同步情况
        wsMaterialList.forEach(wsMaterial -> details.add(new SyncShipmentOrderDTO.Detail().setMaterialId(wsMaterial.getMaterialId()).setNumber(BigDecimal.valueOf(wsMaterial.getNumber()))));
        syncShipmentOrderDTO.setDetails(details);
        rbaseShipmentOrderProxy.syncShipmentOrder(syncShipmentOrderDTO);
    }

}
