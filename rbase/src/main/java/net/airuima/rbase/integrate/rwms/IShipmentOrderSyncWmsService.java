package net.airuima.rbase.integrate.rwms;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncInterceptor("RwmsService")
public interface IShipmentOrderSyncWmsService {

    /**
     * 保存同步出库单及明细数据至RWMS
     *
     * @param workSheet 工单
     */
    @FuncInterceptor(value = "MesWmsIntegration")
    default void syncShipmentOrder(WorkSheet workSheet) {
    }
}
