package net.airuima.rbase.integrate.rwms;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;

import java.util.List;

/**
 * 同步入库信息到WMS
 */
@FuncInterceptor("RwmsService")
public interface IReceiveOrderSyncWmsService {

    /**
     * 保存退料入库到RWMS
     */
    @FuncInterceptor(value = "MesWmsIntegration")
    default void syncReceiveOrder(List<WsMaterialReturn> wsMaterialReturnList) {
    }

    /**
     * 保存工单生产完成后的成品半成品入库到RWMS
     */
    @FuncInterceptor(value = "MesWmsIntegration")
    default void syncReceiveOrder(WorkSheet workSheet) {
    }

}
