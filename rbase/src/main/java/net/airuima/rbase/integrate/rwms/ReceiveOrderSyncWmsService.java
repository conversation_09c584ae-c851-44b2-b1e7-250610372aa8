package net.airuima.rbase.integrate.rwms;

import net.airuima.constant.Constants;
import net.airuima.rbase.constant.StoreCategoryEnum;
import net.airuima.rbase.constant.WorkSheetCategoryEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.sync.SyncWmsReceiveOrderDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.proxy.rwms.RbaseReceiveOrderProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步入库信息到WMS
 */
@Order(0)
@Service
public class ReceiveOrderSyncWmsService implements IReceiveOrderSyncWmsService {

    @Autowired
    private RbaseReceiveOrderProxy rbaseReceiveOrderProxy;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private WorkSheetRepository workSheetRepository;

    /**
     * 保存退料入库到RWMS
     */
    @Override
    public void syncReceiveOrder(List<WsMaterialReturn> wsMaterialReturnList) {
        SyncWmsReceiveOrderDTO syncWmsReceiveOrderDTO = new SyncWmsReceiveOrderDTO().setOriginSerialNumber(wsMaterialReturnList.get(Constants.INT_ZERO).getWorkSheet().getSerialNumber()).setCategory(StoreCategoryEnum.WAREHOUSING_TYPE_PRODUCTION_MATERIAL_RETURN.getCategory());
        List<SyncWmsReceiveOrderDTO.ReceiveOrderDetailDTO> dtoList = new ArrayList<>();
        wsMaterialReturnList.forEach(wsMaterialReturn -> dtoList.add(new SyncWmsReceiveOrderDTO.ReceiveOrderDetailDTO().setMaterialId(wsMaterialReturn.getMaterialId()).setLot(wsMaterialReturn.getBatch()).setNumber(BigDecimal.valueOf(wsMaterialReturn.getNumber()))));
        syncWmsReceiveOrderDTO.setReceiveOrderDetailDTOList(dtoList);
        rbaseReceiveOrderProxy.saveCustom(syncWmsReceiveOrderDTO);
    }

    /**
     * 保存工单生产完成后的成品半成品入库到RWMS
     */
    @Override
    @Klock(keys = {"#workSheet.id"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void syncReceiveOrder(WorkSheet workSheet) {
        //正常单返修单更新入库数量
        if (workSheet.getCategory() == WorkSheetCategoryEnum.NORMAL_CATEGORY.getCategory() || workSheet.getCategory() == WorkSheetCategoryEnum.OFFLINE_CATEGORY.getCategory()) {
            if (workSheet.getQualifiedNumber() > workSheet.getInventoryNumber()) {
                SerialNumberDTO serialNumberDTO = new SerialNumberDTO(net.airuima.rbase.constant.Constants.KEY_MATERIAL_LOT_SERIAL_NUMBER, workSheet.getSerialNumber());
                SyncWmsReceiveOrderDTO syncWmsReceiveOrderDTO = new SyncWmsReceiveOrderDTO().setOriginSerialNumber(workSheet.getSerialNumber()).setCategory(StoreCategoryEnum.WAREHOUSING_TYPE_FINISHED_PRODUCT.getCategory());
                List<SyncWmsReceiveOrderDTO.ReceiveOrderDetailDTO> dtoList = new ArrayList<>();
                dtoList.add(new SyncWmsReceiveOrderDTO.ReceiveOrderDetailDTO().setMaterialId(workSheet.getPedigree().getMaterialId()).setLot(rbaseSerialNumberProxy.generate(serialNumberDTO)).setNumber(BigDecimal.valueOf(workSheet.getQualifiedNumber() - workSheet.getInventoryNumber())));
                syncWmsReceiveOrderDTO.setReceiveOrderDetailDTOList(dtoList);
                rbaseReceiveOrderProxy.saveCustom(syncWmsReceiveOrderDTO);
                workSheet.setInventoryNumber(workSheet.getQualifiedNumber());
                workSheetRepository.save(workSheet);
            }
        }
    }
}
