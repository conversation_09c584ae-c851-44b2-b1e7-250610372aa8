package net.airuima.rbase.integrate.message;

import net.airuima.rbase.dto.control.EventConfigDTO;
import net.airuima.rbase.dto.message.SendMessageDTO;
import net.airuima.rbase.dto.message.TaskFeignDTO;
import net.airuima.rbase.proxy.message.RbaseTaskMessageProxy;
import net.airuima.util.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Order(0)
@Service
public class TaskMessageService implements ITaskMessageService{

    private static final String BEGIN_LI = "<li>";
    private static final String END_LI = "</li>";

    @Autowired
    private RbaseTaskMessageProxy rbaseTaskMessageProxy;

    /**
     * 发送任务消息
     *
     * @param taskDTO 任务消息参数
     */
    @Override
    public void taskSystem(TaskFeignDTO taskDTO) {
        rbaseTaskMessageProxy.addTaskFromSystem(taskDTO);
    }

    /**
     * 调用消息服务发送系统消息
     *
     * @param serialNumber   消息主体序列号(如工单号、设备编码等)
     * @param eventConfigDTO 事件配置DTO
     * @param starter        事件发起人
     * @param recordDate     发起时间
     */
    @Override
    public void sendMessage(String serialNumber, EventConfigDTO eventConfigDTO, String starter, LocalDateTime recordDate) {
        if (null == eventConfigDTO.getMessageGroupConfigDto()) {
            return;
        }
        SendMessageDTO sendMessageDTO = new SendMessageDTO();
        sendMessageDTO.setConfigGroupCode(eventConfigDTO.getMessageGroupConfigDto().getConfigGroupCode()).setSubject(eventConfigDTO.getName());
        // 模板信息
        String htmlBuilder = "<h1>" + eventConfigDTO.getName() + "</h1>" +
                "<p>事件内容：</p><ul>" +
                BEGIN_LI + "事件单号：" + serialNumber + END_LI +
                BEGIN_LI + "事件类型：" + eventConfigDTO.getName() + END_LI +
                BEGIN_LI + "发起人：" + starter + END_LI +
                BEGIN_LI + "发起时间：" + DateTimeUtil.localDateTime2String(recordDate) + END_LI;

        sendMessageDTO.setMessageData(htmlBuilder);
        rbaseTaskMessageProxy.groupMessage(sendMessageDTO);
    }
}
