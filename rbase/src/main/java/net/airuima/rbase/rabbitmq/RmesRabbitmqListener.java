package net.airuima.rbase.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;
import net.airuima.rbase.service.procedure.flowable.IFlowableCallBackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/16
 */
@Component
public class RmesRabbitmqListener {
    private final Logger log = LoggerFactory.getLogger(RmesRabbitmqListener.class);
    @Autowired
    private IFlowableCallBackService[] flowableCallBackServices;

    /**
     * 监听工作流连线事件
     *
     * @param message 基础消息
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(), exchange = @Exchange(value = RmesRabbitMqConfig.DELAYED_QUEUE_NAME, type = ExchangeTypes.FANOUT)))
    public void flowableSequenceListener(@Payload String message) {
        try {
            ProcessRunTimeEventDTO processRunTimeEventDto = JSON.parseObject(message, new TypeReference<ProcessRunTimeEventDTO>() {
            });
            flowableCallBackServices[0].flowableSequenceListener(processRunTimeEventDto);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

}
