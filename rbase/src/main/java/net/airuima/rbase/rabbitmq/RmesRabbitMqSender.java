package net.airuima.rbase.rabbitmq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * RabbitMQ生产者
 *
 * <AUTHOR>
 * @date 2022/7/18
 **/
@Component
public class RmesRabbitMqSender {
    private final Logger log = LoggerFactory.getLogger(RmesRabbitMqSender.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送延时消息到死信交换机
     *
     * @param routingKey 路由Key
     * @param msg        消息
     * @param delayTime  延时时间
     * <AUTHOR>
     * @date 2022/7/15
     **/
    public void send( String msg, int delayTime) {
        this.rabbitTemplate.convertAndSend(RmesRabbitMqConfig.DELAYED_EXCHANGE_NAME,  RmesRabbitMqConfig.DELAYED_ROUTING_KEY, msg, message -> {
            message.getMessageProperties().setDelay(delayTime);
            return message;
        });

    }
}
