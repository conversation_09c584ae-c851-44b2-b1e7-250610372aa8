package net.airuima.rbase.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RmesRabbitMqConfig {

    //队列名
    public static final  String DELAYED_QUEUE_NAME="delayed.queue";
    //交换机名
    public static final  String DELAYED_EXCHANGE_NAME="delayed.exchange";
    //routingKey
    public static final  String DELAYED_ROUTING_KEY="delayed.routingKey";


    /**
     * 创建一个立即消费队列
     *
     * @return : Queue
     * <AUTHOR>
     * @date 2022/7/15
     **/
    @Bean
    public Queue delayedQueue() {
        return new Queue(DELAYED_QUEUE_NAME, true);
    }


    /**
     * 创建延时的exchange
     *
     * @return : CustomExchange
     * <AUTHOR>
     * @date 2022/7/15
     **/
    @Bean
    public CustomExchange delayedExchange() {
        Map<String, Object> args = new HashMap<String, Object>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(DELAYED_EXCHANGE_NAME, "x-delayed-message", true, false, args);
    }


    /**
     * 把立即消费的队列和延时消费的exchange绑定在一起
     *
     * @return : Binding
     * <AUTHOR>
     * @date 2022/7/15
     **/
    @Bean
    public Binding delayedBinding(@Qualifier("delayedQueue") Queue delayedQueue,
                                    @Qualifier("delayedExchange") CustomExchange delayedExchange) {
        return BindingBuilder.bind(delayedQueue).to(delayedExchange).with(DELAYED_ROUTING_KEY).noargs();
    }
}
