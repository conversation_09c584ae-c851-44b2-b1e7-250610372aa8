package net.airuima.rbase.config;

import com.alibaba.fastjson.JSON;
import com.opencsv.CSVReader;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.util.ReflectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Component;

import java.beans.PropertyDescriptor;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 初始化数据统一注册管理
 */
@Component
@Order(999)
public class RbaseInitDataConfiguration implements ApplicationRunner {

    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;

    @Override
    public void run(ApplicationArguments args) {
        try {
            load("priority_element_config.csv", priorityElementConfigRepository, priorityElementConfigRepository, List.of("target", "combination"), PriorityElementConfig::new);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private <T> void load(String csvName, JpaSpecificationExecutor<T> executor, JpaRepository<T, ?> repository, List<String> uniqueColumnList, Function<Map<String, String>, T> entityCreator) {
        List<String[]> csvDataList = readCsv("config/rbase-data/" + csvName);
        String[] csvDataColumn = csvDataList.remove(0);
        List<Map<String, String>> dataList = parseCsvData(csvDataList, csvDataColumn);
        List<String> uniques = executor.findAll((Specification<T>) (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO))).stream().map(entity -> this.getUnique(entity, uniqueColumnList)).collect(Collectors.toList());
        List<T> list = createNewEntities(dataList, uniques, uniqueColumnList, entityCreator);
        saveEntities(list, repository);
    }

    private <T> String getUnique(T entity, List<String> uniqueColumnList) {
        LinkedList<String> uniqueCharts = new LinkedList<>();
        uniqueColumnList.forEach(unique -> {
            Field field = ReflectionUtils.getAccessibleField(entity, unique);
            PropertyDescriptor propertyDescriptor = org.springframework.beans.BeanUtils.getPropertyDescriptor(entity.getClass(), field.getName());
            if (Objects.nonNull(propertyDescriptor)) {
                if (unique.equals("combination")) {
                    List<Integer> combinationList = (List<Integer>) org.springframework.util.ReflectionUtils.invokeMethod(propertyDescriptor.getReadMethod(), entity);
                    assert combinationList != null;
                    uniqueCharts.add(combinationList.stream().sorted().map(Object::toString).collect(Collectors.joining()));
                } else {
                    uniqueCharts.add(String.valueOf(org.springframework.util.ReflectionUtils.invokeMethod(propertyDescriptor.getReadMethod(), entity)));
                }

            }
        });
        return String.join(Constants.UNDERLINE, uniqueCharts);
    }


    private List<Map<String, String>> parseCsvData(List<String[]> csvData, String[] columns) {
        List<Map<String, String>> dataList = new ArrayList<>();
        csvData.forEach(data -> {
            Map<String, String> map = new HashMap<>();
            for (int i = 0; i < data.length; i++) {
                map.put(columns[i], data[i]);
            }
            dataList.add(map);
        });
        return dataList;
    }

    private <T> List<T> createNewEntities(List<Map<String, String>> dataList, List<String> uniqueList, List<String> uniqueColumnList, Function<Map<String, String>, T> entityCreator) {
        List<T> entities = new ArrayList<>();
        for (Map<String, String> map : dataList) {
            LinkedList<String> uniqueCharts = new LinkedList<>();
            uniqueColumnList.forEach(unique -> {
                if (unique.equals("combination")) {
                    List<Integer> combinationList = JSON.parseArray(map.get(unique), Integer.class);
                    uniqueCharts.add(combinationList.stream().sorted().map(Object::toString).collect(Collectors.joining()));
                    return;
                }
                uniqueCharts.add(map.get(unique));
            });
            if (uniqueList.stream().noneMatch(origin->origin.equals(String.join(Constants.UNDERLINE, uniqueCharts)))) {
                entities.add(entityCreator.apply(map));
            }
        }
        return entities;
    }

    @SuppressWarnings("unchecked")
    private <T> void saveEntities(List<T> entities, JpaRepository<T, ?> repository) {
        if (!entities.isEmpty()) {
            repository.saveAll(entities);
        }
    }

    private List<String[]> readCsv(String path) {
        List<String[]> list = new ArrayList<>();
        try {
            CSVReader csvReader = new CSVReader(new InputStreamReader(Objects.requireNonNull(this.getClass().getClassLoader().getResourceAsStream(path))));
            list = csvReader.readAll();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }
}
