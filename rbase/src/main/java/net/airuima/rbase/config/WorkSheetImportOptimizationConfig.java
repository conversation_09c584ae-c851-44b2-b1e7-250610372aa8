package net.airuima.rbase.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 工单导入性能优化配置
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Configuration
@EnableAsync
public class WorkSheetImportOptimizationConfig {

    /**
     * 工单导入专用线程池
     */
    @Bean("workSheetImportExecutor")
    public Executor workSheetImportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数：CPU核心数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());

        // 最大线程数：CPU核心数 * 2
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);

        // 队列容量
        executor.setQueueCapacity(100);

        // 线程名前缀
        executor.setThreadNamePrefix("WorkSheetImport-");

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 线程空闲时间
        executor.setKeepAliveSeconds(60);

        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);

        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();
        return executor;
    }

    /**
     * 子工单生成专用线程池（串行处理避免数据库锁竞争）
     */
    @Bean("subWorkSheetGenerationExecutor")
    public Executor subWorkSheetGenerationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数：1（串行处理避免数据库锁竞争）
        executor.setCorePoolSize(1);

        // 最大线程数：1（串行处理避免数据库锁竞争）
        executor.setMaxPoolSize(1);

        // 队列容量：较大，允许更多任务排队
        executor.setQueueCapacity(1000);

        // 线程名前缀
        executor.setThreadNamePrefix("SubWorkSheetGen-");

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 线程空闲时间
        executor.setKeepAliveSeconds(300);

        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);

        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(120);

        executor.initialize();
        return executor;
    }

    /**
     * 默认异步执行器（用于@Async注解）
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        return subWorkSheetGenerationExecutor();
    }
}
