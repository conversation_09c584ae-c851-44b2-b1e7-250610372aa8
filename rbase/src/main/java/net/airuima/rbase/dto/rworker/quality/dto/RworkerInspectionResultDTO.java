package net.airuima.rbase.dto.rworker.quality.dto;

import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.dto.document.DocumentRelationDTO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/27
 */
@Schema(description = "保存首检巡检抽检终检检测结果")
public class RworkerInspectionResultDTO implements Serializable {

    /**
     * 待检任务id
     */
    @Schema(description = "待检任务id")
    private Long inspectTaskId;

    /**
     * 类型(0,首检;1 巡检，3 终检，4，抽检)
     */
    @Schema(description = "类型(0,首检;1 巡检，3 终检，4，抽检)")
    @NotNull(message = "下交质检类型不能为空")
    private Integer category;

    /**
     * 被检测工位ID
     */
    @Schema(description = "被检测工位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 被检测工序ID
     */
    @Schema(description = "被检测工序ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "被检测工序不能为空")
    private Long stepId;

    /**
     * 检测方案Id
     */
    @Schema(description = "检测方案Id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "检测方案不能为空")
    private Long checkRuleId;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 投产工单ID
     */
    @Schema(description = "投产工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "检测投产工单不能为空")
    private Long productWorkSheetId;
    /**
     * 项目类型id
     */
    @Schema(description = "项目类型id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long varietyId;
    /**
     * 报检数量
     */
    @Schema(description = "报检数量")
    @NotNull(message = "报检数量不能为空")
    private Integer inspectNumber;
    /**
     * 检查数量
     */
    @Schema(description = "检查数量")
    @NotNull(message = "检查数量不能为空")
    private Integer checkNumber;
    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    @NotNull(message = "合格数量不能为空")
    private Integer qualifiedNumber;
    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    @NotNull(message = "不合格数量不能为空")
    private Integer unqualifiedNumber;
    /**
     * 检查结果
     */
    @Schema(description = "检查结果")
    @NotNull(message = "检查结果不能为空")
    private Boolean result;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "操作人不能为空")
    private Long operatorId;

    /**
     * sn检测项目列表
     */
    @Schema(description = "sn检测项目列表")
    private List<SnCheckItemDTO> snCheckItemDtoList;

    public Long getInspectTaskId() {
        return inspectTaskId;
    }

    public RworkerInspectionResultDTO setInspectTaskId(Long inspectTaskId) {
        this.inspectTaskId = inspectTaskId;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public RworkerInspectionResultDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerInspectionResultDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerInspectionResultDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerInspectionResultDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getCheckRuleId() {
        return checkRuleId;
    }

    public RworkerInspectionResultDTO setCheckRuleId(Long checkRuleId) {
        this.checkRuleId = checkRuleId;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public RworkerInspectionResultDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerInspectionResultDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public RworkerInspectionResultDTO setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public Integer getCheckNumber() {
        return checkNumber;
    }

    public RworkerInspectionResultDTO setCheckNumber(Integer checkNumber) {
        this.checkNumber = checkNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerInspectionResultDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerInspectionResultDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public RworkerInspectionResultDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public List<SnCheckItemDTO> getSnCheckItemDtoList() {
        return snCheckItemDtoList;
    }

    public RworkerInspectionResultDTO setSnCheckItemDtoList(List<SnCheckItemDTO> snCheckItemDtoList) {
        this.snCheckItemDtoList = snCheckItemDtoList;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public RworkerInspectionResultDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Integer getInspectNumber() {
        return inspectNumber;
    }

    public RworkerInspectionResultDTO setInspectNumber(Integer inspectNumber) {
        this.inspectNumber = inspectNumber;
        return this;
    }

    @Schema(description = "sn检测项目")
    public static class SnCheckItemDTO implements Serializable{

        @Schema(description = "检测SN")
        private String sn;

        @Schema(description = "不良项目")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        @Schema(description = "检测项目结果列表")
        private List<CheckItemResultDTO> checkItemResultDtoList;

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public SnCheckItemDTO setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public String getSn() {
            return sn;
        }

        public SnCheckItemDTO setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public List<CheckItemResultDTO> getCheckItemResultDtoList() {
            return checkItemResultDtoList;
        }

        public SnCheckItemDTO setCheckItemResultDtoList(List<CheckItemResultDTO> checkItemResultDtoList) {
            this.checkItemResultDtoList = checkItemResultDtoList;
            return this;
        }

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;
            SnCheckItemDTO that = (SnCheckItemDTO) o;
            return Objects.equals(sn, that.sn);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(sn);
        }
    }

    @Schema(description = "检测项目结果")
    public static class CheckItemResultDTO implements Serializable{

        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 检测项名称
         */
        @Schema(description = "检测项名称")
        private String name;
        /**
         * 检测项编码
         */
        @Schema(description = "检测项编码")
        private String code;

        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        /**
         * 计量单位id
         */
        @Schema(description = "计量单位id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unitId;

        /**
         * 项目类型Id
         */
        @Schema(description = "项目类型Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long varietyId;


        /**
         * 检验方法:目测0/检测仪器1
         */
        @Schema(description = "检验方法:目测0/检测仪器1")
        private Integer inspectWay;

        /**
         * 分析方法:定性0/定量1
         */
        @Schema(description = "分析方法:定性0/定量1")
        private Integer analyseWay;

        /**
         * 检测仪器
         */
        @Schema(description = "检测仪器")
        private String facility;


        /**
         * 缺陷原因Id
         */
        @Schema(description = "缺陷原因Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long defectId;

        /**
         * 检测数据值
         */
        @Schema(description = "检测数据值")
        private String checkData;

        /**
         * 检测结果
         */
        @Schema(description = "检测结果")
        private Boolean result;

        /**
         * 是否为虚拟SN
         */
        @Schema(description = "是否为虚拟SN")
        private Boolean virtual;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String note;

        /**
         * 文件信息
         */
        @Schema(description = "文件信息")
        private List<DocumentRelationDTO.Document> documentList;

        public String getNote() {
            return note;
        }

        public CheckItemResultDTO setNote(String note) {
            this.note = note;
            return this;
        }

        public List<DocumentRelationDTO.Document> getDocumentList() {
            return documentList;
        }

        public CheckItemResultDTO setDocumentList(List<DocumentRelationDTO.Document> documentList) {
            this.documentList = documentList;
            return this;
        }

        public Long getId() {
            return id;
        }

        public CheckItemResultDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public CheckItemResultDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public CheckItemResultDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public CheckItemResultDTO setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
            return this;
        }

        public Long getUnitId() {
            return unitId;
        }

        public CheckItemResultDTO setUnitId(Long unitId) {
            this.unitId = unitId;
            return this;
        }

        public Long getVarietyId() {
            return varietyId;
        }

        public CheckItemResultDTO setVarietyId(Long varietyId) {
            this.varietyId = varietyId;
            return this;
        }

        public Integer getInspectWay() {
            return inspectWay;
        }

        public CheckItemResultDTO setInspectWay(Integer inspectWay) {
            this.inspectWay = inspectWay;
            return this;
        }

        public Integer getAnalyseWay() {
            return analyseWay;
        }

        public CheckItemResultDTO setAnalyseWay(Integer analyseWay) {
            this.analyseWay = analyseWay;
            return this;
        }

        public String getFacility() {
            return facility;
        }

        public CheckItemResultDTO setFacility(String facility) {
            this.facility = facility;
            return this;
        }

        public Long getDefectId() {
            return defectId;
        }

        public CheckItemResultDTO setDefectId(Long defectId) {
            this.defectId = defectId;
            return this;
        }

        public String getCheckData() {
            return checkData;
        }

        public CheckItemResultDTO setCheckData(String checkData) {
            this.checkData = checkData;
            return this;
        }

        public Boolean getResult() {
            return result;
        }

        public CheckItemResultDTO setResult(Boolean result) {
            this.result = result;
            return this;
        }

        public Boolean getVirtual() {
            return virtual;
        }

        public CheckItemResultDTO setVirtual(Boolean virtual) {
            this.virtual = virtual;
            return this;
        }
    }
}
