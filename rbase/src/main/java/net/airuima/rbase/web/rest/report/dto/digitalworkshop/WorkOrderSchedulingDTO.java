package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "数字车间驾驶舱-工单详情")
public class WorkOrderSchedulingDTO {

    /**
     * 工单编码
     */
    @Schema(description = "工单编码")
    private String serialNumber;
    /**
     * 谱系编码
     */
    @Schema(description = "谱系编码")
    private String pedigreeCode;
    /**
     * 谱系名称
     */
    @Schema(description = "谱系名称")
    private String pedigreeName;
    /**
     * 工单进度
     */
    @Schema(description = "工单进度")
    private BigDecimal progress;
    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;
    /**
     * 工单状态
     */
    @Schema(description = "工单状态")
    private Integer status;
    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期", example = "2022-05-23 23:45:33")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planStartDate;

    /**
     * 计划完工日期
     */
    @Schema(description = "计划完工日期", example = "2022-05-23 23:45:33")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planEndDate;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer number;
    /**
     * 直通率
     */
    @Schema(description = "直通率")
    private BigDecimal fpy;
    /**
     * 合格率
     */
    @Schema(description = "合格率")
    private BigDecimal fty;
    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Integer qualifiedNumber;
    /**
     * 是否逾期
     */
    @Schema(description = "是否逾期")
    private Boolean isOverdue;

    public WorkOrderSchedulingDTO() {
    }

    public WorkOrderSchedulingDTO(SubWorkSheet subWorkSheet) {
        this.serialNumber = subWorkSheet.getSerialNumber();
        this.pedigreeCode = subWorkSheet.getWorkSheet().getPedigree().getCode();
        this.pedigreeName = subWorkSheet.getWorkSheet().getPedigree().getName();
        this.progress = subWorkSheet.getProgress();
        this.priority = subWorkSheet.getPriority();
        this.status = subWorkSheet.getStatus();
        this.specification = subWorkSheet.getWorkSheet().getPedigree().getSpecification();
        this.planStartDate = subWorkSheet.getPlanStartDate();
        this.planEndDate = subWorkSheet.getPlanEndDate();
        this.number = subWorkSheet.getNumber();
        this.isOverdue = Boolean.FALSE;
        if(null != subWorkSheet.getActualEndDate() && null!= subWorkSheet.getPlanEndDate() && subWorkSheet.getActualEndDate().isAfter(subWorkSheet.getPlanEndDate())){
            this.isOverdue = Boolean.TRUE;
        }
        if(subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()){
            this.isOverdue = Boolean.FALSE;
        }
        if(null == subWorkSheet.getActualEndDate() && null!= subWorkSheet.getPlanEndDate() && subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())){
            this.isOverdue = Boolean.TRUE;
        }
    }

    public WorkOrderSchedulingDTO(WorkSheet workSheet) {
        this.serialNumber = workSheet.getSerialNumber();
        this.pedigreeCode = workSheet.getPedigree().getCode();
        this.pedigreeName = workSheet.getPedigree().getName();
        this.progress = workSheet.getProgress();
        this.priority = workSheet.getPriority();
        this.status = workSheet.getStatus();
        this.specification = workSheet.getPedigree().getSpecification();
        this.planStartDate = workSheet.getPlanStartDate();
        this.planEndDate = workSheet.getPlanEndDate();
        this.number = workSheet.getNumber();
        this.isOverdue = workSheet.getIsOverdue();
        this.fpy = workSheet.getFpy();
        this.fty = workSheet.getFty();
        this.qualifiedNumber = workSheet.getQualifiedNumber();
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkOrderSchedulingDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public BigDecimal getFpy() {
        return fpy;
    }

    public WorkOrderSchedulingDTO setFpy(BigDecimal fpy) {
        this.fpy = fpy;
        return this;
    }

    public BigDecimal getFty() {
        return fty;
    }

    public WorkOrderSchedulingDTO setFty(BigDecimal fty) {
        this.fty = fty;
        return this;
    }

    public Boolean getOverdue() {
        return isOverdue;
    }

    public WorkOrderSchedulingDTO setOverdue(Boolean overdue) {
        isOverdue = overdue;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkOrderSchedulingDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public BigDecimal getProgress() {
        return progress;
    }

    public WorkOrderSchedulingDTO setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    public Integer getPriority() {
        return priority;
    }

    public WorkOrderSchedulingDTO setPriority(Integer priority) {
        this.priority = priority;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public WorkOrderSchedulingDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public WorkOrderSchedulingDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkOrderSchedulingDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkOrderSchedulingDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkOrderSchedulingDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public WorkOrderSchedulingDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public WorkOrderSchedulingDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }
}
