package net.airuima.rbase.web.rest.procedure.single;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.single.SnWorkDetailService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支工序生产详情Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "单支工序生产详情Resource")
@RestController
@RequestMapping("/api/sn-work-details")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Single")
@AuthSkip("ICU")
public class SnWorkDetailResource extends ProtectBaseResource<SnWorkDetail> {

    private final SnWorkDetailService snWorkDetailService;
    @Autowired
    private IRollbackStepService[] rollbackStepServices;

    public SnWorkDetailResource(SnWorkDetailService snWorkDetailService) {
        this.snWorkDetailService = snWorkDetailService;
        this.mapUri = "/api/sn-work-details";
    }

    /**
     * sn单支回退
     * @param id        sn详情id
     * @param note      备注
     * @param staffId   操作员工id
     * <AUTHOR>
     * @date  2022/11/11
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Parameters({
            @Parameter(name = "note", description = "备注", required = false),
            @Parameter(name = "staffId", description = "操作员工id", required = true)
    })
    @DeleteMapping({"/rollBakeSn/{id}"})
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<Void>> rollBakeSn(@PathVariable Long id, @RequestParam(value = "note",required = false) String note, @RequestParam("staffId") Long staffId) {
        try {
            rollbackStepServices[0].deleteSnWorkDetailById(id,note,staffId);
            return ResponseData.ok("success","工序回退成功");
        }catch (ResponseException e) {
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览单支生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建单支生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改单支生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "回退单支生产详情";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出单支生产详情";
        }
        return "";
    }

}
