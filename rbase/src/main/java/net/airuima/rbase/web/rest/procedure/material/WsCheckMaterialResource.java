package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterial;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWsCheckMaterialDTO;
import net.airuima.rbase.service.procedure.material.IWsCheckMaterialService;
import net.airuima.rbase.service.procedure.material.WsCheckMaterialService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "工单核料表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-check-materials")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch")
public class WsCheckMaterialResource extends ProtectBaseResource<WsCheckMaterial> {

    @Autowired
    private IWsCheckMaterialService[] wsCheckMaterialServices;
    private final WsCheckMaterialService wsCheckMaterialService;

    public WsCheckMaterialResource(WsCheckMaterialService wsCheckMaterialService) {
        this.wsCheckMaterialService = wsCheckMaterialService;
        this.mapUri = "/api/ws-check-materials";
    }

    /**
     * 删除核料凭证记录
     * <AUTHOR>
     * @param id   核料记录ID
     * @return ResponseEntity<ResponseData<Void>>
     * @date 2021-05-23
     **/
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        BaseDTO baseDto = wsCheckMaterialServices[0].delete(id);
        if (Constants.OK.equals(baseDto.getStatus())){
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(WsCheckMaterialDetail.class.getSimpleName()), id.toString())).build();
        }else {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsCheckMaterialDetail.class.getSimpleName()), "deletedFailed", baseDto.getMessage())).build();
        }
    }


    /**
     * 导入正常单核料表
     *
     * @param file 核料信息表
     */
    @Operation(summary= "导入正常单核料表")
    @PostMapping("/importCheckMaterials")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseContent<Void>> importCheckMaterials(@RequestParam("file") MultipartFile file) {
        try {
            wsCheckMaterialServices[0].importMaterials(file);
            return ResponseContent.ok().isOkBuild();
        } catch (ResponseException e){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(e.getErrorKey(), e.getMessage())).build();
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().isBadRequestBuild(HeaderUtil.createFailureAlert(WsCheckMaterial.class.getSimpleName(), "import","导入失败"));
        }
    }


    /**
     * 工单核料单同步
     * @param syncWsCheckMaterialDtoList 核料单同步数据
     * <AUTHOR>
     * @date  2022/7/14
     * @return
     */
    @Operation(summary = "工单核料单同步")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/sync-ws-check-materials")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncWsCheckMaterial(@RequestBody List<SyncWsCheckMaterialDTO> syncWsCheckMaterialDtoList){
        try {
            return ResponseData.ok(wsCheckMaterialService.syncWsCheckMaterial(syncWsCheckMaterialDtoList));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据核料凭证判断是否存在 核料
     * @param code 核料凭证
     * <AUTHOR>
     * @date  2022/7/14
     * @return java.lang.Boolean
     */
    @Operation(summary = "核料凭证判断是否存在")
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/exist")
    public ResponseEntity<ResponseData<Boolean>> validateWsCheckMaterialExist(@RequestParam(value = "code") String code){
        return ResponseData.ok(wsCheckMaterialService.validateWsCheckMaterialExist(code));
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出工单核料";
        }
        return "";
    }

}
