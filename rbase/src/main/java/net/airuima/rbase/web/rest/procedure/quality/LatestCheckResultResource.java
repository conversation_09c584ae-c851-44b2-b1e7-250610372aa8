package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.query.QueryCondition;
import net.airuima.query.SearchFilter;
import net.airuima.rbase.domain.procedure.quality.LatestCheckResult;
import net.airuima.rbase.service.procedure.quality.LatestCheckResultService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测结果最新状态Resource
 * <AUTHOR>
 * @date 2021-03-22
 */
@Tag(name = "检测结果最新状态Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/latest-check-results")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC")
@AuthSkip("ICD")
public class LatestCheckResultResource extends ProtectBaseResource<LatestCheckResult> {

    private static final String MODULE = "最新质检结果";
    private final LatestCheckResultService latestCheckResultService;

    public LatestCheckResultResource(LatestCheckResultService latestCheckResultService) {
        this.latestCheckResultService = latestCheckResultService;
        this.mapUri = "/api/latest-check-results";
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<List<LatestCheckResult>> getAll(Pageable pageable, Long token, HttpServletRequest request) {
        List<SearchFilter> lists = new ArrayList<>();
        lists.add(new SearchFilter("IEQB_display", "true"));
        return this.dataTables(filters -> lists.toArray(new SearchFilter[0]), pageable, token, null, request);
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<List<LatestCheckResult>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        return super.searchQuery(pageable, qcs, request);
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
