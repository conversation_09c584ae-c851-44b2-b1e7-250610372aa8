package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;

@FetchEntity
public class ProcessStepMaterialBatchDTO {

    /**
     * 批次详情id；可能是工序批次，容器批次，单支批次
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "批次详情id；可能是工序批次，容器批次，单支批次")
    private Long id;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 员工DTO
     */
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;
    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    private String workCellName;


    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    private Long materialId;


    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 批次
     */
    @Schema(description = "批次")
    private String batch;


    /**
     * 批次数量
     */
    @Schema(description = "批次数量")
    private Double number;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Integer qualifiedNumber;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId", tableName = "supplier")
    private SupplierDTO supplierDTO = new SupplierDTO();


    public ProcessStepMaterialBatchDTO() {
    }

    public ProcessStepMaterialBatchDTO(Long id, String sn, Long staffId, String workCellCode, String workCellName, Long materialId, String batch, Double number, Integer qualifiedNumber, Long supplierId) {
        this.id = id;
        this.sn = sn;
        this.staffId = staffId;
        this.workCellCode = workCellCode;
        this.workCellName = workCellName;
        this.materialId = materialId;
        this.batch = batch;
        this.number = number;
        this.inputNumber = Constants.INT_ONE;
        this.qualifiedNumber = qualifiedNumber;
        this.supplierId = supplierId;
    }

    public ProcessStepMaterialBatchDTO(Long id, String containerCode, String sn, Long staffId, String workCellCode, String workCellName, Long materialId, String batch, Double number, Integer qualifiedNumber, Long supplierId) {
        this.id = id;
        this.containerCode = containerCode;
        this.sn = sn;
        this.staffId = staffId;
        this.workCellCode = workCellCode;
        this.workCellName = workCellName;
        this.materialId = materialId;
        this.batch = batch;
        this.number = number;
        this.inputNumber = Constants.INT_ONE;
        this.qualifiedNumber = qualifiedNumber;
        this.supplierId = supplierId;
    }

    public ProcessStepMaterialBatchDTO(Long id, String containerCode, Long staffId, String workCellCode, String workCellName, Long materialId, String batch,Integer inputNumber,Double number, Integer qualifiedNumber, Long supplierId) {
        this.id = id;
        this.containerCode = containerCode;
        this.staffId = staffId;
        this.workCellCode = workCellCode;
        this.workCellName = workCellName;
        this.inputNumber = inputNumber;
        this.qualifiedNumber = qualifiedNumber;
        this.batch = batch;
        this.number = number;
        this.materialId = materialId;
        this.supplierId = supplierId;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public ProcessStepMaterialBatchDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ProcessStepMaterialBatchDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public ProcessStepMaterialBatchDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public ProcessStepMaterialBatchDTO setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public ProcessStepMaterialBatchDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public ProcessStepMaterialBatchDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public ProcessStepMaterialBatchDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public ProcessStepMaterialBatchDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public ProcessStepMaterialBatchDTO setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ProcessStepMaterialBatchDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public ProcessStepMaterialBatchDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public ProcessStepMaterialBatchDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public ProcessStepMaterialBatchDTO setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDTO() {
        return supplierDTO;
    }

    public ProcessStepMaterialBatchDTO setSupplierDTO(SupplierDTO supplierDTO) {
        this.supplierDTO = supplierDTO;
        return this;
    }

    public Long getId() {
        return id;
    }

    public ProcessStepMaterialBatchDTO setId(Long id) {
        this.id = id;
        return this;
    }
}
