package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.WsStepWorkCell;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.batch.WsStepWorkCellService;
import net.airuima.rbase.service.procedure.batch.dto.WsStepWorkCellGetDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.WsStepWorkCellSaveDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
@Tag(name = "(子)工单定制工序指定工位Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-step-work-cells")
@AuthorityRegion("生产工单")
@FuncInterceptor("SpecificStepCell")
@AuthSkip("ICU")
public class WsStepWorkCellResource extends ProtectBaseResource<WsStepWorkCell> {

    private final WsStepWorkCellService wsStepWorkCellService;

    public WsStepWorkCellResource(WsStepWorkCellService wsStepWorkCellService) {
        this.wsStepWorkCellService = wsStepWorkCellService;
        this.mapUri = "/api/ws-step-work-cells";
    }

    /**
     * 根据（子）工单id 获取指定的定制工序指定工位信息
     * @param id （子）工单id
     * <AUTHOR>
     * @date  2022/8/10
     * @return List<WsStepWorkCellGetDTO>
     */
    @GetMapping("/sub-ws/{id}")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary= "根据（子）工单id 获取定制工序指定工位信息")
    public ResponseEntity<ResponseData<List<WsStepWorkCellGetDTO>>> findWsStepWorkCell(@PathVariable(value = "id") Long id){
         return ResponseData.ok(wsStepWorkCellService.findWsStepWorkCell(id));
    }

    /**
     * 保存（子）工单id 定制工序指定工位信息
     * @param wsStepWorkCellSaveDto 子）工单工序指定工位DTO
     * <AUTHOR>
     * @date  2022/8/11
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/assignee")
    @Operation(summary= "保存（子）工单id 定制工序指定工位信息")
    public ResponseEntity<ResponseData<Void>> saveWsStepWorkCell(@RequestBody WsStepWorkCellSaveDTO wsStepWorkCellSaveDto){
       try{
           BaseDTO baseDto = wsStepWorkCellService.saveWsStepWorkCell(wsStepWorkCellSaveDto);
           if (Constants.OK.equals(baseDto.getStatus())){
               return ResponseData.save();
           }else {
               return ResponseData.error("exception", baseDto.getMessage());
           }
       }catch (Exception e){
           e.printStackTrace();
           return ResponseData.error(e);
       }
    }

    /**
     *  通过（子）工单id 以及工序id获取指定工位信息
     * @param id （子）工单
     * @param stepId 工序id
     * <AUTHOR>
     * @date  2022/8/18
     * @return List<WsStepWorkCellGetDTO.WorkCellInfo>
     */
    @GetMapping("/sub-ws-step")
    @Operation(summary= "通过（子）工单id 以及工序id获取指定工位信息")
    public ResponseEntity<ResponseData<List<WsStepWorkCellGetDTO.WorkCellInfo>>> findByWsAndStepId(@RequestParam(value = "id") Long id,@RequestParam(value = "stepId") Long stepId){
        return ResponseData.ok(wsStepWorkCellService.findByWsAndStepId(id, stepId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览(子)工单工序指定工位信息";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建(子)工单工序指定工位信息";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改(子)工单工序指定工位信息";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除(子)工单工序指定工位信息";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入(子)工单工序指定工位信息";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出(子)工单工序指定工位信息";
        }
        return "";
    }

}
