package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单合格率统计结果图形部分DTO
 *
 * <AUTHOR>
 * @date 2023/06/23
 */
@Schema(description = "工单合格率统计结果图形部分DTO")
public class WorkSheetQualifiedRateReportChartResultDTO {

    /**
     * 工单总数
     */
    @Schema(description = "工单总数")
    private Long workSheetCount;


    /**
     * 总投产数
     */
    @Schema(description = "总投产数")
    private Long number;


    /**
     * 总合格数
     */
    @Schema(description = "总合格数")
    private Long qualifiedNumber;

    /**
     * 直通率
     */
    @Schema(description = "直通率")
    private Double firstQualifiedRate;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    private Double qualifiedRate;

    /**
     * 直通率图形数据
     */
    @Schema(description = "直通率图形数据")
    private List<FirstQualifiedChartDataDTO> firstQualifiedChartDataList;

    /**
     * 合格率图形数据
     */
    @Schema(description = "合格率图形数据")
    private List<QualifiedChartDataDTO> qualifiedChartDataList;


    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public Double getFirstQualifiedRate() {
        return firstQualifiedRate;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setFirstQualifiedRate(Double firstQualifiedRate) {
        this.firstQualifiedRate = firstQualifiedRate;
        return this;
    }

    public Long getWorkSheetCount() {
        return workSheetCount;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setWorkSheetCount(Long workSheetCount) {
        this.workSheetCount = workSheetCount;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public List<FirstQualifiedChartDataDTO> getFirstQualifiedChartDataList() {
        return firstQualifiedChartDataList;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setFirstQualifiedChartDataList(List<FirstQualifiedChartDataDTO> firstQualifiedChartDataList) {
        this.firstQualifiedChartDataList = firstQualifiedChartDataList;
        return this;
    }

    public List<QualifiedChartDataDTO> getQualifiedChartDataList() {
        return qualifiedChartDataList;
    }

    public WorkSheetQualifiedRateReportChartResultDTO setQualifiedChartDataList(List<QualifiedChartDataDTO> qualifiedChartDataList) {
        this.qualifiedChartDataList = qualifiedChartDataList;
        return this;
    }
}
