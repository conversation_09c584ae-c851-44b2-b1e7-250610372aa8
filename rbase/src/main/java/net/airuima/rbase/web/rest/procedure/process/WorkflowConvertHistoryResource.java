package net.airuima.rbase.web.rest.procedure.process;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.process.WorkflowConvertHistory;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.service.procedure.process.WorkflowConvertHistoryService;
import net.airuima.rbase.web.rest.procedure.process.dto.WorkflowConvertDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线历史
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Tag(name = "转工艺路线历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/workflow-convert-histories")
@FuncInterceptor("ConversionProcess")
@AuthorityRegion("生产过程数据")
@AuthSkip("IUD")
public class WorkflowConvertHistoryResource extends ProtectBaseResource<WorkflowConvertHistory> {

    private final Logger log = LoggerFactory.getLogger(WorkflowConvertHistoryResource.class);

    private final WorkflowConvertHistoryService workflowConvertHistoryService;

    public WorkflowConvertHistoryResource(WorkflowConvertHistoryService workflowConvertHistoryService) {
        this.workflowConvertHistoryService = workflowConvertHistoryService;
        this.mapUri = "/api/workflow-convert-histories";
    }

    /**
     * 转工艺路线, 保存转工艺历史记录
     *
     * @param workflowConvertDTO 转工艺路线参数
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.procedure.process.WorkflowConvertHistory> 转工艺历史
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/convertWorkflow")
    public ResponseEntity<ResponseData<WorkflowConvertHistory>> convertWorkflow(@RequestBody WorkflowConvertDTO workflowConvertDTO) throws URISyntaxException {
        BaseResultDTO<WorkflowConvertHistory> baseResultDto = workflowConvertHistoryService.convertWorkflow(workflowConvertDTO);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseData.error(baseResultDto.getKey(), baseResultDto.getMessage());
        } else {
            return ResponseData.ok(baseResultDto.getData());
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览转工艺路线历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建转工艺路线历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改转工艺路线历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除转工艺路线历史";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入转工艺路线历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出转工艺路线历史";
        }
        return "";
    }
}
