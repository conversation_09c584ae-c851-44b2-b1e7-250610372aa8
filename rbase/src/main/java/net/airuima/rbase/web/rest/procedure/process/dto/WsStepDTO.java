package net.airuima.rbase.web.rest.procedure.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 接收配置工艺路线的工序配置DTO
 *
 * <AUTHOR>
 * @create 2021/11/25
 */
public class WsStepDTO {


    @Schema(description = "请求模式")
    private Integer requestMode;

    @Schema(description = "管控模式(0:批量;1:单支)")
    private Integer controlMode;

    @Schema(description = "是否管控物料(0:不管控;1:管控)")
    private Boolean isControlMaterial;

    @Schema(description = "是否绑定容器(0:否;1:是)")
    private Boolean isBindContainer;

    public WsStepDTO() {
    }

    public WsStepDTO(Integer requestMode, Integer controlMode, Boolean isControlMaterial, Boolean isBindContainer) {
        this.requestMode = requestMode;
        this.controlMode = controlMode;
        this.isControlMaterial = isControlMaterial;
        this.isBindContainer = isBindContainer;
    }

    public Integer getRequestMode() {
        return requestMode;
    }

    public WsStepDTO setRequestMode(Integer requestMode) {
        this.requestMode = requestMode;
        return this;
    }

    public Integer getControlMode() {
        return controlMode;
    }

    public WsStepDTO setControlMode(Integer controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public Boolean getControlMaterial() {
        return isControlMaterial;
    }

    public WsStepDTO setControlMaterial(Boolean controlMaterial) {
        isControlMaterial = controlMaterial;
        return this;
    }

    public Boolean getBindContainer() {
        return isBindContainer;
    }

    public WsStepDTO setBindContainer(Boolean bindContainer) {
        isBindContainer = bindContainer;
        return this;
    }

}
