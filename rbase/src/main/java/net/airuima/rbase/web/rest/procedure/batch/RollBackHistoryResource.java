package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.batch.RollBackHistory;
import net.airuima.rbase.service.procedure.batch.RollBackHistoryService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Tag(name = "回退历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/roll-back-histories")
@AuthorityRegion("生产过程数据")
@AuthSkip("ICUD")
public class RollBackHistoryResource extends ProtectBaseResource<RollBackHistory> {
    private static final String MODULE = "回退历史";
    private final RollBackHistoryService rollBackHistoryService;

    public RollBackHistoryResource(RollBackHistoryService rollBackHistoryService) {
        this.rollBackHistoryService = rollBackHistoryService;
        this.mapUri = "/api/roll-back-histories";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
