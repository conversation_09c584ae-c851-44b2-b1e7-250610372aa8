package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合格率数值数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "合格率数值数据DTO")
public class QualifiedRateNumberQueryDTO {

    /**
     * 工单总数
     */
    @Schema(description = "工单总数")
    private Long workSheetCount;


    /**
     * 总投产数
     */
    @Schema(description = "总投产数")
    private Long workSheetPlanNumber;


    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Long workSheetQualifiedNumber;


    /**
     * 不合格数
     */
    @Schema(description = "不格数")
    private Long workSheetUnQualifiedNumber;


    /**
     * 在线返修合格数
     */
    @Schema(description = "在线返修合格数")
    private Long workSheetReworkQualifiedNumber;

    public Long getWorkSheetCount() {
        return workSheetCount;
    }

    public QualifiedRateNumberQueryDTO setWorkSheetCount(Long workSheetCount) {
        this.workSheetCount = workSheetCount;
        return this;
    }

    public Long getWorkSheetPlanNumber() {
        return workSheetPlanNumber;
    }

    public QualifiedRateNumberQueryDTO setWorkSheetPlanNumber(Long workSheetPlanNumber) {
        this.workSheetPlanNumber = workSheetPlanNumber;
        return this;
    }

    public Long getWorkSheetQualifiedNumber() {
        return workSheetQualifiedNumber;
    }

    public QualifiedRateNumberQueryDTO setWorkSheetQualifiedNumber(Long workSheetQualifiedNumber) {
        this.workSheetQualifiedNumber = workSheetQualifiedNumber;
        return this;
    }

    public Long getWorkSheetUnQualifiedNumber() {
        return workSheetUnQualifiedNumber;
    }

    public QualifiedRateNumberQueryDTO setWorkSheetUnQualifiedNumber(Long workSheetUnQualifiedNumber) {
        this.workSheetUnQualifiedNumber = workSheetUnQualifiedNumber;
        return this;
    }

    public Long getWorkSheetReworkQualifiedNumber() {
        return workSheetReworkQualifiedNumber;
    }

    public QualifiedRateNumberQueryDTO setWorkSheetReworkQualifiedNumber(Long workSheetReworkQualifiedNumber) {
        this.workSheetReworkQualifiedNumber = workSheetReworkQualifiedNumber;
        return this;
    }

    public QualifiedRateNumberQueryDTO() {

    }

    public QualifiedRateNumberQueryDTO(Long workSheetCount, Long workSheetPlanNumber, Long workSheetQualifiedNumber, Long workSheetUnQualifiedNumber, Long workSheetReworkQualifiedNumber) {
        this.workSheetCount = workSheetCount;
        this.workSheetPlanNumber = workSheetPlanNumber;
        this.workSheetQualifiedNumber = workSheetQualifiedNumber;
        this.workSheetUnQualifiedNumber = workSheetUnQualifiedNumber;
        this.workSheetReworkQualifiedNumber = workSheetReworkQualifiedNumber;
    }
}
