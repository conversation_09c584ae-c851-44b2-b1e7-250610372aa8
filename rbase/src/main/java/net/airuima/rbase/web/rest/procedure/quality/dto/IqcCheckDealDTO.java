package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检处理DTO
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Schema(name = "来料检处理DTO", description = "来料检处理")
public class IqcCheckDealDTO {

    /**
     * 来料检验ID
     */
    @Schema(description = "来料检验ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 处理方式
     */
    @Schema(description = "处理方式")
    private Integer dealWay;

    /**
     * 处理人id
     */
    @Schema(description = "处理人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealerId;

    /**
     * MRB申请原因
     */
    @Schema(description = "MRB申请原因")
    private String reason;


    public Long getId() {
        return id;
    }

    public IqcCheckDealDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public IqcCheckDealDTO setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public Long getDealerId() {
        return dealerId;
    }

    public IqcCheckDealDTO setDealerId(Long dealerId) {
        this.dealerId = dealerId;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public IqcCheckDealDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }
}
