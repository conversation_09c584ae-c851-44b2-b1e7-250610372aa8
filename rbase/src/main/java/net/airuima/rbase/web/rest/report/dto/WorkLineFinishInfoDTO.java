package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线完成情况DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "产线完成情况DTO")
public class WorkLineFinishInfoDTO {

    /**
     * 部门名字
     */
    @Schema(description = "产线名字")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;

    /**
     * 完成类型国际化key 完成类型 计划完成 PlanFinish 实际完成 ActualFinish
     */
    @Schema(description = "完成类型 计划完成 PlanFinish 实际完成 ActualFinish")
    private String type;


    public String getName() {
        return name;
    }

    public WorkLineFinishInfoDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkLineFinishInfoDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public String getType() {
        return type;
    }

    public WorkLineFinishInfoDTO setType(String type) {
        this.type = type;
        return this;
    }

    public WorkLineFinishInfoDTO() {
    }

    public WorkLineFinishInfoDTO(String name, Long number, String type) {
        this.name = name;
        this.number = number;
        this.type = type;
    }
}
