package net.airuima.rbase.web.rest.procedure.wearingpart;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;
import net.airuima.rbase.service.base.wearingpart.WearingPartService;
import net.airuima.rbase.service.procedure.wearingpart.WearingPartResetHistoryService;
import net.airuima.rbase.web.rest.procedure.wearingpart.dto.WearingPartResetHistoryDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司 易损件重置历史Resource
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Tag(name = "易损件重置历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wearing-part-reset-histories")
@AuthorityRegion("易损件管理")
@FuncInterceptor("WearingPart")
@AuthSkip("IEUD")
public class WearingPartResetHistoryResource extends ProtectBaseResource<WearingPartResetHistory> {
    private static final String MODULE = "易损件重置历史";
    private final WearingPartResetHistoryService wearingPartResetHistoryService;

    @Autowired
    private WearingPartService wearingPartService;

    public WearingPartResetHistoryResource(WearingPartResetHistoryService wearingPartResetHistoryService) {
        this.wearingPartResetHistoryService = wearingPartResetHistoryService;
        this.mapUri = "/api/wearing-part-reset-histories";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

    /**
     * 重置易损件可用状态
     *
     * @param wearingPartResetHistoryDTO 易损件重置dto
     * @return ResponseEntity<ResponseContent < BaseClientDTO>>
     */
    @Operation(summary = "重置易损件可用状态")
    @PostMapping("/resetWearingPartStatus")
    public ResponseEntity<ResponseData<Void>> resetWearingPartStatus(
            @RequestBody WearingPartResetHistoryDTO wearingPartResetHistoryDTO) {
        try {
            wearingPartService.reset(wearingPartResetHistoryDTO.getWearingPartId());
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }
}
