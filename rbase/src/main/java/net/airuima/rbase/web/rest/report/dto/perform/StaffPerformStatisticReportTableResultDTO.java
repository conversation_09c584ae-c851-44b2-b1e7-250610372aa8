package net.airuima.rbase.web.rest.report.dto.perform;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计报表表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
@Schema(description = "报工统计报表表格数据DTO")
public class StaffPerformStatisticReportTableResultDTO extends PageDTO {

    /**
     * 报工统计报表表格数据明细集合
     */
    @Schema(description = "报工统计报表表格数据明细集合")
    private List<StaffPerformStatisticReportTableItemDTO> staffPerformStatisticReportTableItemList;

    public List<StaffPerformStatisticReportTableItemDTO> getStaffPerformStatisticReportTableItemList() {
        return staffPerformStatisticReportTableItemList;
    }

    public StaffPerformStatisticReportTableResultDTO setStaffPerformStatisticReportTableItemList(List<StaffPerformStatisticReportTableItemDTO> staffPerformStatisticReportTableItemList) {
        this.staffPerformStatisticReportTableItemList = staffPerformStatisticReportTableItemList;
        return this;
    }
}
