package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组达成情况DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序组达成情况DTO")
public class StepGroupCompletionInfoDTO {

    /**
     * 工序组名称
     */
    @Schema(description = "工序组名称")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;

    /**
     * 完成类型国际化key 产出类型 计划产出 Plan 实际产出 Actual
     */
    @Schema(description = "产出类型 计划产出 Plan 实际产出 Actual")
    private String type;

    public String getName() {
        return name;
    }

    public StepGroupCompletionInfoDTO setName(String name) {
        this.name = name;
        return this;
    }


    public Long getNumber() {
        return number;
    }

    public StepGroupCompletionInfoDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public String getType() {
        return type;
    }

    public StepGroupCompletionInfoDTO setType(String type) {
        this.type = type;
        return this;
    }

    public StepGroupCompletionInfoDTO() {
    }

    public StepGroupCompletionInfoDTO(String name, Long number, String type) {
        this.name = name;
        this.number = number;
        this.type = type;
    }
}
