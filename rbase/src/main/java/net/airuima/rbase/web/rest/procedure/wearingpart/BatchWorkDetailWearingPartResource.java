package net.airuima.rbase.web.rest.procedure.wearingpart;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.service.procedure.wearingpart.BatchWorkDetailWearingPartService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *批量生产详情易损件Resource
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Tag(name = "批量生产详情易损件Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/batch-work-detail-wearing-parts")
@AuthorityRegion("易损件管理")
@FuncInterceptor("WearingPart")
@AuthSkip("ICUD")
public class BatchWorkDetailWearingPartResource extends ProtectBaseResource<BatchWorkDetailWearingPart> {
    private static final String MODULE = "批量生产详情易损件历史";
    private final BatchWorkDetailWearingPartService batchWorkDetailWearingPartService;

    public BatchWorkDetailWearingPartResource(BatchWorkDetailWearingPartService batchWorkDetailWearingPartService){
        this.batchWorkDetailWearingPartService = batchWorkDetailWearingPartService;
        this.mapUri = "/api/batch-work-detail-wearing-parts";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
