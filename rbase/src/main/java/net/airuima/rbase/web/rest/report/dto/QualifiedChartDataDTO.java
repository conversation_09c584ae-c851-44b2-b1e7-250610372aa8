package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合格率图形数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "合格率图形数据DTO")
public class QualifiedChartDataDTO {

    /**
     * 产品型号名称
     */
    @Schema(description = "产品型号名称")
    private String name;

    /**
     * y轴合格率值
     */
    @Schema(description = "y轴合格率值")
    private Double value;


    /**
     * x轴时间
     */
    @Schema(description = "x轴时间")
    private String time;

    public String getName() {
        return name;
    }

    public QualifiedChartDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Double getValue() {
        return value;
    }

    public QualifiedChartDataDTO setValue(Double value) {
        this.value = value;
        return this;
    }

    public String getTime() {
        return time;
    }

    public QualifiedChartDataDTO setTime(String time) {
        this.time = time;
        return this;
    }
}
