package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.service.report.StaffPerformUnqualifiedItemService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Tag(name = "员工不良明细resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/staff-perform-unqualified-items")
@AuthorityRegion("报表看板")
public class StaffPerformUnqualifiedItemResource extends BaseResource<StaffPerformUnqualifiedItem> {
    private final StaffPerformUnqualifiedItemService staffPerformUnqualifiedItemService;

    public StaffPerformUnqualifiedItemResource(StaffPerformUnqualifiedItemService staffPerformUnqualifiedItemService) {
        this.staffPerformUnqualifiedItemService = staffPerformUnqualifiedItemService;
        this.mapUri = "/api/staff-perform-unqualified-items";
    }
}
