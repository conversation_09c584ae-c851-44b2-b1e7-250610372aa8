package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.dto.quality.MrbProcessResultDTO;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.web.rest.procedure.quality.dto.CheckHistoryDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectionResultDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史Resource
 * <AUTHOR>
 * @date 2021-03-22
 */
@Tag(name = "检测历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/check-histories")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC || PQC || FQC || LQC")
@AuthSkip("ID")
public class CheckHistoryResource extends ProtectBaseResource<CheckHistory> {

    private static final String MODULE = "质检历史";
    private final CheckHistoryService checkHistoryService;

    public CheckHistoryResource(CheckHistoryService checkHistoryService) {
        this.checkHistoryService = checkHistoryService;
        this.mapUri = "/api/check-histories";
    }

    /**
     * 根据检测类型、项目类型、工位查询检验过程数据
     * @param category          检测类型(首检0/巡检1)
     * @param varietyId         项目类型id
     * @param workCellId        工位id
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.procedure.quality.dto.CheckHistoryDTO>返回通用对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    @GetMapping("/processDetail")
    @Operation(summary = "根据检测类型、项目类型、工位查询检验过程数据")
    @Parameters({
           @Parameter(name = "category", description = "检测类型(首检0/巡检1)", required = true),
           @Parameter(name = "varietyId", description = "项目类型id", required = false),
           @Parameter(name = "workCellId", description = "工位id", required = true)
    })
    public ResponseEntity<ResponseData<CheckHistoryDTO>> processDetail(@RequestParam(value = "category") Integer category, @RequestParam(value = "varietyId",required = false) Long varietyId, @RequestParam(value = "workCellId") Long workCellId) {
        return ResponseData.ok(checkHistoryService.processDetail(category, varietyId, workCellId));
    }

    /**
     * 根据历史记录id查询检测过程数据
     * @param historyId          历史记录id
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.procedure.quality.dto.CheckHistoryDTO>返回通用对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    @GetMapping("/detail/{historyId}")
    @Operation(summary = "根据历史记录id查询检测过程数据")
    public ResponseEntity<ResponseData<CheckHistoryDTO>> detailByHistoryId(@PathVariable("historyId") Long historyId) {
        return ResponseData.ok(checkHistoryService.detailByHistoryId(historyId));
    }

    /**
     * 首检 巡检 更新检测处理结果
     * @param inspectionResultDto 下交Dto
     * <AUTHOR>
     * @date  2023/5/6
     * @return void
     */
    @PostMapping("/first-inspection-results")
    @PreventRepeatSubmit
    @Operation(summary = "首检 巡检 更新检测处理结果")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> firstInspectionResult(@RequestBody InspectionResultDTO inspectionResultDto){
        try {
            checkHistoryService.firstInspectionResult(inspectionResultDto);
            return ResponseData.save();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 抽检 终检 更新检测处理结果
     * @param inspectionResultDto 下交Dto
     * <AUTHOR>
     * @date  2023/5/6
     * @return void
     */
    @PostMapping("/inspection-results")
    @PreventRepeatSubmit
    @Operation(summary = "抽检 终检 更新检测处理结果")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> inspectionResult(@RequestBody InspectionResultDTO inspectionResultDto){
        try {
            checkHistoryService.inspectionResult(inspectionResultDto);
            return ResponseData.save();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据MRB结果处理不良
     * @param historyId 历史ID
     * @param processResultDTOList 处理结果
     */
    @Operation(summary = "根据MRB结果处理不良")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    @PutMapping("/process/unqualified/{id}")
    public ResponseEntity<ResponseData<Void>> processUnqualified(@PathVariable("id") Long historyId,
                                                                 @RequestBody List<MrbProcessResultDTO> processResultDTOList) {
        try {
            checkHistoryService.processUnqualified(historyId, processResultDTOList);
            return ResponseData.ok();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 根据工序检验历史ID获取与检验历史条件一致的指定抽检方案的质检方案列表
     * @param category 抽检方案类型类型 全检0/固定数量1/按百分比抽样2/按国标抽样3
     * @param id        检验历史ID
     * @return List<PedigreeStepCheckRule>
     */
    @Operation(description = "根据工序检验历史ID获取与检验历史条件一致的指定抽检方案的质检方案列表",parameters = {
            @Parameter(name = "id",description = "检验历史ID",required = true,in = ParameterIn.PATH),
            @Parameter(name = "category",description = "抽样方案类型(检0/固定数量1/按百分比抽样2/按国标抽样3)",required = true,in = ParameterIn.PATH)
    })
    @PreAuthorize("@ sc.checkSecurity()")
    @GetMapping("/step/full-check-rule/id/{id}/category/{category}")
    public ResponseEntity<ResponseData<List<PedigreeStepCheckRule>>> fullCheckRule(@PathVariable("id") Long id ,@PathVariable("category") int category){
        return ResponseData.ok(checkHistoryService.findStepSpecifiedCheckRule(id,category));
    }


    /**
     *
     * @param modelMap
     * @param exportDTO
     * @param request
     * @param response
     * @throws Exception
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public void exportExcel(ModelMap modelMap, @Parameter(schema = @Schema(implementation = ExportDTO.class),required = true,description = "导出数据参数DTO") @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 获得过滤后的数据
        Specification<CheckHistory> spec = QueryConditionParser.buildSpecificationWithClassName(CheckHistory.class.getName(), exportDTO.getQcs(), this.filters, filterReformer);
        List<CheckHistory> checkHistoryList;
        if (exportDTO.getExportTemplate() != null && exportDTO.getExportTemplate() && Objects.nonNull(exportDTO.getExportParams())) {
            checkHistoryList = Collections.emptyList();
        }else {
            checkHistoryList = checkHistoryService.find(spec);
        }
        try {
            checkHistoryService.exporExcel(checkHistoryList,exportDTO, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 检验历史绑定文件
     * @param id 记录id
     * @param documentId 文件id
     */
    @Operation(summary = "检验历史绑定文件")
    @PostMapping("/bind-check-document/{id}/{documentId}")
    public ResponseEntity<ResponseData<Void>> bindCheckDocument(@PathVariable(name = "id") Long id, @PathVariable(name = "documentId") Long documentId) {
        try {
            checkHistoryService.bindCheckDocument(id, documentId);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
