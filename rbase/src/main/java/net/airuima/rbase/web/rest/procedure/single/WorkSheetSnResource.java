package net.airuima.rbase.web.rest.procedure.single;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;
import net.airuima.rbase.service.procedure.single.WorkSheetSnService;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Tag(name = "工单SN关联Resource")
@RestController
@RequestMapping("/api/work-sheet-sns")
@AuthorityRegion("生产工单")
@FuncInterceptor("Single && WorkSheetBindSn")
@AuthSkip("D")
public class WorkSheetSnResource extends ProtectBaseResource<WorkSheetSn> {

    private static final String MODULE = "工单单支预绑定";

    private final WorkSheetSnService workSheetSnService;

    public WorkSheetSnResource(WorkSheetSnService workSheetSnService) {
        this.workSheetSnService = workSheetSnService;
        this.mapUri = "/api/work-sheet-sns";
    }

    /**
     * 导入工单关联SN
     * @param file 导入文件
     * @param data 数据
     * @param suffix 前缀
     * @param metaColumn 元数据
     * @param response HttpResponse
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data,
                                                 @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn,
                                                 HttpServletResponse response) throws Exception {
        try{
            workSheetSnService.importWorkSheetSn(file);
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (Exception e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "importFailed", e.toString())).build();
        }
    }

    /**
     *  一键同步RMPS工单的SN绑定到（子）工单上
     * @param workSheetId 工单ID
     */
    @FuncInterceptor("RmpsService")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/sync/{workSheetId}")
    public ResponseEntity<ResponseData<Void>> sync(@PathVariable("workSheetId") Long workSheetId){
        try {
            workSheetSnService.sync(workSheetId);
            return ResponseData.ok("sync.success","单支预绑定成功");
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }


    /**
     * 修改关联的SN
     * @param entity 实体对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping()
    @Override
    public ResponseEntity<WorkSheetSn> update(@Valid @RequestBody WorkSheetSn entity) throws URISyntaxException {
        try {
            entity = workSheetSnService.updateInstance(entity.getId(),entity.getSn());
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString())).body(entity);
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 导出工单关联SN
     * @param modelMap modelMap
     * @param exportDTO exportDTO
     * @param request request
     * @param response  response
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or  hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setHeader("message", "export!");
        List<WorkSheetSn> workSheetSnList = Lists.newArrayList();
        if (!(exportDTO.getExportTemplate() != null && exportDTO.getExportTemplate())) {
            //获得过滤后的数据
            Specification<WorkSheetSn> spec = QueryConditionParser.buildSpecificationWithClassName(WorkSheetSn.class.getName(), exportDTO.getQcs(), this.filters, filterReformer);
            workSheetSnService.exportExcel(workSheetSnList,  exportDTO, response);
        }else {
            ExcelUtil.createTitleWorkbook(exportDTO.getExportParams(), response, exportDTO.getExcelTitle());
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
