package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计表格查询结果 DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量统计表格查询结果DTO")
public class ProductionCapacityReportTableResultDTO extends PageDTO {

    /**
     * 产量统计表格明细集合
     */
    @Schema(description = "产量统计表格明细集合")
    private List<ProductionCapacityReportTableItemDTO> productionCapacityReportTableItemList;

    public List<ProductionCapacityReportTableItemDTO> getProductionCapacityReportTableItemList() {
        return productionCapacityReportTableItemList;
    }

    public ProductionCapacityReportTableResultDTO setProductionCapacityReportTableItemList(List<ProductionCapacityReportTableItemDTO> productionCapacityReportTableItemList) {
        this.productionCapacityReportTableItemList = productionCapacityReportTableItemList;
        return this;
    }
}
