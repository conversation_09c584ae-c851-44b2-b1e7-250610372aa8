package net.airuima.rbase.web.rest.procedure.reinspect;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspectResult;
import net.airuima.rbase.service.procedure.reinspect.StepReinspectResultService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Tag(name = "工序不良复检处理结果Resource")
@RestController
@RequestMapping("/api/step-reinspect-results")
public class StepReinspectResultResource extends BaseResource<StepReinspectResult> {

    private final StepReinspectResultService stepReinspectResultService;

    public StepReinspectResultResource(StepReinspectResultService stepReinspectResultService) {
        this.stepReinspectResultService = stepReinspectResultService;
        this.mapUri = "/api/step-reinspect-results";
    }
}
