package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 质量报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Schema(description = "生产过程正向追溯")
public class ForwardTraceResultDTO implements Serializable {
    /**
     * ID
     **/
    @Schema(description = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 单据类型，0:销售订单，1:正常单，2:子工单
     **/
    @Schema(description = "单据类型，0:销售订单，1:正常单，2:子工单, 3:返工单, 4:返修单")
    @Excel(name = "单据类型", orderNum = "1", replace = {"销售订单_0", "正常单_1", "子工单_2", "返工单_3", "返修单_4"})
    private Integer category;

    /**
     * 单据号
     **/
    @Schema(description = "单据号")
    @Excel(name = "单据号", orderNum = "2")
    private String serialNumber;

    /**
     * 单据进度
     **/
    @Schema(description = "单据进度")
    @Excel(name = "单据进度", orderNum = "3", numFormat = "#%")
    private BigDecimal serialProgress;

    /**
     * 产品谱系
     **/
    @Schema(description = "产品谱系，可能有多个,号分隔")
    @Excel(name = "产品谱系", orderNum = "4")
    private String pedigreeNames;

    /**
     * 订单数量
     */
    @Schema(description = "订单数量")
    @Excel(name = "订单数量", orderNum = "5")
    private int number;

    /**
     * 投产数量
     */
    @Schema(description = "投产数量")
    @Excel(name = "投产数量", orderNum = "6")
    private int inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "7")
    private int qualifiedNumber;

    /**
     * 工序不合格数
     */
    @Schema(description = "工序不合格数")
    @Excel(name = "工序不合格数", orderNum = "8")
    private int unqualifiedNumber;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    @Excel(name = "完成数量", orderNum = "9")
    private int finishNumber;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    @Excel(name = "合格率", orderNum = "10")
    private String passRate;

    /**
     * 直通率
     */
    @Schema(description = "直通率")
    @Excel(name = "直通率", orderNum = "11")
    private String firstPassRate;

    /**
     * 交付日期
     */
    @Schema(description = "交付日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交付日期", orderNum = "12", format = "yyyy-MM-dd")
    private LocalDate deliveryDate;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "计划开工日期", orderNum = "13", format = "yyyy-MM-dd")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划完工日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "计划结单日期", orderNum = "14", format = "yyyy-MM-dd")
    private LocalDateTime planEndDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", orderNum = "15", format = "yyyy-MM-dd HH:mm:ss")
    private Instant createdDate;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Excel(name = "创建人", orderNum = "16")
    private String createdBy;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Excel(name = "客户", orderNum = "17")
    private String clientName;

    @Schema(description = "预留字段1")
    private String custom1;

    @Schema(description = "预留字段2")
    private String custom2;


    @Schema(description = "预留字段3")
    private String custom3;


    @Schema(description = "预留字段4")
    private String custom4;


    @Schema(description = "预留字段5")
    private String custom5;

    /**
     * 展示工序详情
     */
    @Schema(description = "展示工序详情")
    private Boolean showStepDetail;

    /**
     * 不良项目列表
     */
    @ExcelCollection(name = "不良项目列表")
    private List<UnqualifiedItemDetail> unqualifiedItemDetailList;

    /**
     * 子数据
     */
    @Schema(description = "子数据")
    private List<ForwardTraceResultDTO> subForwardTraceResultDTOList;


    @Schema(description = "不良项目列表")
    public static class UnqualifiedItemDetail{

        @Schema(description = "不良项目名称")
        @Excel(name = "不良项目名称", orderNum = "1")
        private String name;

        @Schema(description = "不良项目编码")
        @Excel(name = "不良项目编码", orderNum = "2")
        private String code;

        @Schema(description = "不良数量")
        @Excel(name = "不良数量", orderNum = "3")
        private Long number;



        public UnqualifiedItemDetail() {
        }

        public UnqualifiedItemDetail(String name, String code, Long number) {
            this.name = name;
            this.code = code;
            this.number = number;
        }

        public UnqualifiedItemDetail(UnqualifiedItem unqualifiedItem) {
            this.name = unqualifiedItem.getName();
            this.code = unqualifiedItem.getCode();
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemDetail setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemDetail setCode(String code) {
            this.code = code;
            return this;
        }

        public Long getNumber() {
            return number;
        }

        public UnqualifiedItemDetail setNumber(Long number) {
            this.number = number;
            return this;
        }
    }

    public List<UnqualifiedItemDetail> getUnqualifiedItemDetailList() {
        return unqualifiedItemDetailList;
    }

    public ForwardTraceResultDTO setUnqualifiedItemDetailList(List<UnqualifiedItemDetail> unqualifiedItemDetailList) {
        this.unqualifiedItemDetailList = unqualifiedItemDetailList;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ForwardTraceResultDTO setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public ForwardTraceResultDTO setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ForwardTraceResultDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public ForwardTraceResultDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public BigDecimal getSerialProgress() {
        return serialProgress;
    }

    public ForwardTraceResultDTO setSerialProgress(BigDecimal serialProgress) {
        this.serialProgress = serialProgress;
        return this;
    }

    public String getPedigreeNames() {
        return pedigreeNames;
    }

    public ForwardTraceResultDTO setPedigreeNames(String pedigreeNames) {
        this.pedigreeNames = pedigreeNames;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public ForwardTraceResultDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getInputNumber() {
        return inputNumber;
    }

    public ForwardTraceResultDTO setInputNumber(int inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public ForwardTraceResultDTO setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public ForwardTraceResultDTO setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public ForwardTraceResultDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public ForwardTraceResultDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public List<ForwardTraceResultDTO> getSubForwardTraceResultDTOList() {
        return subForwardTraceResultDTOList;
    }

    public ForwardTraceResultDTO setSubForwardTraceResultDTOList(List<ForwardTraceResultDTO> subForwardTraceResultDTOList) {
        this.subForwardTraceResultDTOList = subForwardTraceResultDTOList;
        return this;
    }

    public Boolean getShowStepDetail() {
        return showStepDetail;
    }

    public ForwardTraceResultDTO setShowStepDetail(Boolean showStepDetail) {
        this.showStepDetail = showStepDetail;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public ForwardTraceResultDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getPassRate() {
        return passRate;
    }

    public ForwardTraceResultDTO setPassRate(String passRate) {
        this.passRate = passRate;
        return this;
    }

    public String getFirstPassRate() {
        return firstPassRate;
    }

    public ForwardTraceResultDTO setFirstPassRate(String firstPassRate) {
        this.firstPassRate = firstPassRate;
        return this;
    }

    public String getCustom1() {
        return custom1;
    }

    public ForwardTraceResultDTO setCustom1(String custom1) {
        this.custom1 = custom1;
        return this;
    }

    public String getCustom2() {
        return custom2;
    }

    public ForwardTraceResultDTO setCustom2(String custom2) {
        this.custom2 = custom2;
        return this;
    }

    public Long getId() {
        return id;
    }

    public ForwardTraceResultDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public ForwardTraceResultDTO setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
        return this;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public ForwardTraceResultDTO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    public String getCustom3() {
        return custom3;
    }

    public ForwardTraceResultDTO setCustom3(String custom3) {
        this.custom3 = custom3;
        return this;
    }

    public String getCustom4() {
        return custom4;
    }

    public ForwardTraceResultDTO setCustom4(String custom4) {
        this.custom4 = custom4;
        return this;
    }

    public String getCustom5() {
        return custom5;
    }

    public ForwardTraceResultDTO setCustom5(String custom5) {
        this.custom5 = custom5;
        return this;
    }
}
