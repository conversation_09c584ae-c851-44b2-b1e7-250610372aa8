package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.service.procedure.material.WsWorkCellMaterialBatchService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工位上料表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "工单工位上料表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-work-cell-material-batches")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber && WsWorkCellMaterialBatchNumber")
@AuthSkip("ICD")
public class WsWorkCellMaterialBatchResource extends ProtectBaseResource<WsWorkCellMaterialBatch> {
    private static final String MODULE = "工单工位物料库存";
    private final WsWorkCellMaterialBatchService wsWorkCellMaterialBatchService;

    public WsWorkCellMaterialBatchResource(WsWorkCellMaterialBatchService wsWorkCellMaterialBatchService) {
        this.wsWorkCellMaterialBatchService = wsWorkCellMaterialBatchService;
        this.mapUri = "/api/ws-work-cell-material-batches";
    }

    /**
     * 通用CRUD修改工单工位物料
     * <AUTHOR>
     * @param entity
     * @return ResponseEntity<ResponseData<WsWorkCellMaterialBatch>>
     * @date 2021-11-17
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<WsWorkCellMaterialBatch> update(@Valid @RequestBody WsWorkCellMaterialBatch entity) throws URISyntaxException {
        return wsWorkCellMaterialBatchService.updateEntity(entity);
    }

    /**
     * 获取工位信息
     * @param wsId
     * <AUTHOR>
     * @date  2022/3/25
     * @return List<WorkCell>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/wsId")
    public ResponseEntity<ResponseData<List<WorkCell>>> findByWsId(@RequestParam Long wsId){
        return ResponseData.ok(wsWorkCellMaterialBatchService.findByWsId(wsId));
    }

    /**
     * 获取工单工位上料信息
     * @param wsId 工单id
     * @param workCellId 工位id
     * <AUTHOR>
     * @date  2022/3/25
     * @return List<WsWorkCellMaterialBatch>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/wsId-and-workCellId")
    public ResponseEntity<ResponseData<List<WsWorkCellMaterialBatch>>> findByWsIdAndWorkCellId(@RequestParam Long wsId,@RequestParam Long workCellId){
        return ResponseData.ok(wsWorkCellMaterialBatchService.findByWsIdAndWorkCellId(wsId,workCellId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
