package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表看板图形数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良报表看板图形数据DTO")
public class UnqualifiedReportChartResultDTO {

    /**
     * 不良项目总数
     */
    @Schema(description = "不良项目总数")
    private Long number;

    /**
     * 工序不良图形数据
     */
    @Schema(description = "工序不良图形数据")
    private List<StepUnqualifiedDTO> stepUnqualifiedList;

    /**
     * 不良比率图形数据
     */
    @Schema(description = "不良比率图形数据")
    private List<UnqualifiedRateDTO> unqualifiedRateList;

    /**
     * 不良推移图数据
     */
    @Schema(description = "不良推移图数据")
    private List<UnqualifiedItemRunChartDTO> unqualifiedItemRunChartDtoList;


    public Long getNumber() {
        return number;
    }

    public UnqualifiedReportChartResultDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public List<StepUnqualifiedDTO> getStepUnqualifiedList() {
        return stepUnqualifiedList;
    }

    public UnqualifiedReportChartResultDTO setStepUnqualifiedList(List<StepUnqualifiedDTO> stepUnqualifiedList) {
        this.stepUnqualifiedList = stepUnqualifiedList;
        return this;
    }

    public List<UnqualifiedRateDTO> getUnqualifiedRateList() {
        return unqualifiedRateList;
    }

    public UnqualifiedReportChartResultDTO setUnqualifiedRateList(List<UnqualifiedRateDTO> unqualifiedRateList) {
        this.unqualifiedRateList = unqualifiedRateList;
        return this;
    }

    public List<UnqualifiedItemRunChartDTO> getUnqualifiedItemRunChartDtoList() {
        return unqualifiedItemRunChartDtoList;
    }

    public UnqualifiedReportChartResultDTO setUnqualifiedItemRunChartDtoList(List<UnqualifiedItemRunChartDTO> unqualifiedItemRunChartDtoList) {
        this.unqualifiedItemRunChartDtoList = unqualifiedItemRunChartDtoList;
        return this;
    }

    public static class UnqualifiedItemRunChartDTO{
        private LocalDate recordDate;
        private List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfoList;

        public LocalDate getRecordDate() {
            return recordDate;
        }

        public UnqualifiedItemRunChartDTO setRecordDate(LocalDate recordDate) {
            this.recordDate = recordDate;
            return this;
        }

        public List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> getUnqualifiedItemNumberInfoList() {
            return unqualifiedItemNumberInfoList;
        }

        public UnqualifiedItemRunChartDTO setUnqualifiedItemNumberInfoList(List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfoList) {
            this.unqualifiedItemNumberInfoList = unqualifiedItemNumberInfoList;
            return this;
        }
    }
}
