package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线达成情况DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线达成情况DTO")
public class WorkLineCompletionInfoDTO {

    /**
     * 产线名称
     */
    @Schema(description = "产线名称")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;

    /**
     * 完成类型国际化key 产出类型 计划产出 Plan 实际产出 Actual
     */
    @Schema(description = "产出类型 计划产出 Plan 实际产出 Actual")
    private String type;

    public String getName() {
        return name;
    }

    public WorkLineCompletionInfoDTO setName(String name) {
        this.name = name;
        return this;
    }


    public Long getNumber() {
        return number;
    }

    public WorkLineCompletionInfoDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public String getType() {
        return type;
    }

    public WorkLineCompletionInfoDTO setType(String type) {
        this.type = type;
        return this;
    }

    public WorkLineCompletionInfoDTO() {
    }

    public WorkLineCompletionInfoDTO(String name, Long number, String type) {
        this.name = name;
        this.number = number;
        this.type = type;
    }
}
