package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/20
 */
public class InProcessWsScheduleReportRequestDTO extends PageDTO {

    /**
     * 生产线Id
     */
    @Schema(description = "生产线Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    public Long getWorkLineId() {
        return workLineId;
    }

    public InProcessWsScheduleReportRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }
}
