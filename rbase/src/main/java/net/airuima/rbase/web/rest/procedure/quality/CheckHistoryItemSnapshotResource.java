package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryItemSnapshot;
import net.airuima.rbase.service.procedure.quality.CheckHistoryItemSnapshotService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 检测历史条件详情记录Resource
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Tag(name = "检测历史条件详情记录Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/check-history-item-snapshots")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC || PQC || FQC || LQC")
public class CheckHistoryItemSnapshotResource extends BaseResource<CheckHistoryItemSnapshot> {


    private final CheckHistoryItemSnapshotService checkHistoryItemSnapshotService;

    public CheckHistoryItemSnapshotResource(CheckHistoryItemSnapshotService checkHistoryItemSnapshotService) {
        this.checkHistoryItemSnapshotService = checkHistoryItemSnapshotService;
        this.mapUri = "/api/check-history-item-snapshots";
    }

}
