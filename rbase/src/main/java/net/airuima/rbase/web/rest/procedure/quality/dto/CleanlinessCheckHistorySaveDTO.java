package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测历史表Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "洁净度检测历史DTO", description = "洁净度检测历史DTO")
public class CleanlinessCheckHistorySaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 检验人ID
     */
    @NotNull
    @Schema(description = "检验人ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * 区域ID
     */
    @NotNull
    @Schema(description = "区域ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long areaId;

    /**
     * 检测记录，JSON数组(多个检测记录则用逗号,分割)
     */
    @NotNull
    @Schema(description = "检测记录，JSON数组(多个检测记录则用逗号,分割)", required = true)
    private String record;

    public Long getOperatorId() {
        return operatorId;
    }

    public CleanlinessCheckHistorySaveDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Long getAreaId() {
        return areaId;
    }

    public CleanlinessCheckHistorySaveDTO setAreaId(Long areaId) {
        this.areaId = areaId;
        return this;
    }

    public String getRecord() {
        return record;
    }

    public CleanlinessCheckHistorySaveDTO setRecord(String checkRecord) {
        this.record = checkRecord;
        return this;
    }
}
