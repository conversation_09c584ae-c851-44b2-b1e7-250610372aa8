package net.airuima.rbase.web.rest.procedure.single;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.single.SnWorkStatusService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产状态Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "单支生产状态Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-work-statuses")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Single")
@AuthSkip("ICUD")
public class SnWorkStatusResource extends ProtectBaseResource<SnWorkStatus> {

    private final SnWorkStatusService snWorkStatusService;

    public SnWorkStatusResource(SnWorkStatusService snWorkStatusService) {
        this.snWorkStatusService = snWorkStatusService;
        this.mapUri = "/api/sn-work-statuses";
    }

    /**
     * 验证包装sn状态
     *
     * @param sn           sn
     * @param serialNumber 工单号
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2023/5/19
     */
    @Operation(summary= "验证包装sn状态")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PostMapping("/valid-package")
    public ResponseEntity<ResponseData<BaseDTO>> validatePackageSn(@RequestParam("sn") String sn, @RequestParam("serialNumber") String serialNumber) {
        try {
            return ResponseData.ok(snWorkStatusService.validatePackageSn(sn,serialNumber));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 验证SN是否可以报废
     * @param sn 待报废SN
     * @return BaseDTO
     */
    @Operation(summary= "验证SN是否可以报废")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/validate-discard/{sn}")
    public ResponseEntity<ResponseData<BaseDTO>> validateDiscardSn(@PathVariable("sn") String sn){
        return ResponseData.ok(snWorkStatusService.validateDiscardSn(sn));
    }

    /**
     * 替换SN
     * @param originSn 原始SN
     * @param targetSn 目标SN
     * @return BaseDTO
     */
    @Operation(summary= "替换SN")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PostMapping("/replace-sn")
    public ResponseEntity<ResponseData<BaseDTO>> replaceSn(@RequestParam("originSn") String originSn,@RequestParam("targetSn") String targetSn){
        try{
            return ResponseData.ok(snWorkStatusService.replaceSn(originSn,targetSn));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.ok(new BaseDTO(Constants.KO));
        }

    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览单支生产状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建单支生产状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改单支生产状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除单支生产状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入单支生产状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出单支生产状态";
        }
        return "";
    }

}
