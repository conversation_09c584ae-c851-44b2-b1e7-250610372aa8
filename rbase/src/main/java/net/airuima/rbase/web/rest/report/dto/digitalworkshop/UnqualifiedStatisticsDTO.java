package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;

import java.io.Serializable;
import java.util.List;

@Schema(description = "数字车间驾驶舱-不良项目")
public class UnqualifiedStatisticsDTO implements Serializable {


    /**
     * 不良项目对应不良数量列表
     */
    @Schema(description = "不良项目对应不良数量列表")
    List<UnqualifiedItemNumberInfo> unqualifiedItemNumberInfoList;

    /**
     * 工序不良项目对应不良数量列表
     */
    @Schema(description = "工序不良项目对应不良数量列表")
    List<StepUnqualifiedItemNumberInfo> stepUnqualifiedItemNumberInfoList;

    public List<UnqualifiedItemNumberInfo> getUnqualifiedItemNumberInfoList() {
        return unqualifiedItemNumberInfoList;
    }

    public UnqualifiedStatisticsDTO setUnqualifiedItemNumberInfoList(List<UnqualifiedItemNumberInfo> unqualifiedItemNumberInfoList) {
        this.unqualifiedItemNumberInfoList = unqualifiedItemNumberInfoList;
        return this;
    }

    public List<StepUnqualifiedItemNumberInfo> getStepUnqualifiedItemNumberInfoList() {
        return stepUnqualifiedItemNumberInfoList;
    }

    public UnqualifiedStatisticsDTO setStepUnqualifiedItemNumberInfoList(List<StepUnqualifiedItemNumberInfo> stepUnqualifiedItemNumberInfoList) {
        this.stepUnqualifiedItemNumberInfoList = stepUnqualifiedItemNumberInfoList;
        return this;
    }

    public static class UnqualifiedItemInfo {

        private Long id;

        private String code;

        private String name;

        public UnqualifiedItemInfo() {
        }

        public UnqualifiedItemInfo(UnqualifiedItem unqualifiedItem) {
            this.id = unqualifiedItem.getId();
            this.code = unqualifiedItem.getCode();
            this.name = unqualifiedItem.getName();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedItemInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemInfo setName(String name) {
            this.name = name;
            return this;
        }
    }

    public static class StepInfo {

        private Long id;

        private String code;

        private String name;

        public StepInfo() {
        }

        public StepInfo(Step step) {
            this.id = step.getId();
            this.code = step.getCode();
            this.name = step.getName();
        }

        public Long getId() {
            return id;
        }

        public StepInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepInfo setName(String name) {
            this.name = name;
            return this;
        }
    }


    @Schema(description = "不良项目对应不良数量")
    public static class UnqualifiedItemNumberInfo {

        /**
         * 不良项目
         */
        @Schema(description = "不良项目")
        private UnqualifiedItemInfo unqualifiedItemInfo;

        /**
         * 不良数量
         */
        @Schema(description = "不良数量")
        private Long number;

        public UnqualifiedItemNumberInfo() {
        }

        public UnqualifiedItemNumberInfo(UnqualifiedItem unqualifiedItem, Long number) {
            this.unqualifiedItemInfo = new UnqualifiedItemInfo(unqualifiedItem);
            this.number = number;
        }

        public UnqualifiedItemInfo getUnqualifiedItemInfo() {
            return unqualifiedItemInfo;
        }

        public UnqualifiedItemNumberInfo setUnqualifiedItemInfo(UnqualifiedItemInfo unqualifiedItemInfo) {
            this.unqualifiedItemInfo = unqualifiedItemInfo;
            return this;
        }

        public Long getNumber() {
            return number;
        }

        public UnqualifiedItemNumberInfo setNumber(Long number) {
            this.number = number;
            return this;
        }
    }


    /**
     * 工序不良项目对应不良数量列表
     */
    @Schema(description = "工序不良项目对应不良数量列表")
    public static class StepUnqualifiedItemNumberInfo {

        /**
         * 工序
         */
        @Schema(description = "工序")
        private StepInfo stepInfo;
        /**
         * 不良数量
         */
        @Schema(description = "不良数量")
        private Long number;


        public StepUnqualifiedItemNumberInfo() {
        }

        public StepUnqualifiedItemNumberInfo(Step step, Long number) {
            this.stepInfo = new StepInfo(step);
            this.number = number;
        }

        public StepInfo getStepInfo() {
            return stepInfo;
        }

        public StepUnqualifiedItemNumberInfo setStepInfo(StepInfo stepInfo) {
            this.stepInfo = stepInfo;
            return this;
        }

        public Long getNumber() {
            return number;
        }

        public StepUnqualifiedItemNumberInfo setNumber(Long number) {
            this.number = number;
            return this;
        }
    }


}
