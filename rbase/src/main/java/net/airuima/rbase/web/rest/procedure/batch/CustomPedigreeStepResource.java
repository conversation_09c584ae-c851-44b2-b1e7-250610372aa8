package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep;
import net.airuima.rbase.service.procedure.batch.CustomPedigreeStepService;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系最新工艺路线快照Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系最新工艺路线快照Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/custom-pedigree-steps")
@AuthorityRegion("生产工单")
public class CustomPedigreeStepResource extends BaseResource<CustomPedigreeStep> {

    private final CustomPedigreeStepService customPedigreeStepService;

    public CustomPedigreeStepResource(CustomPedigreeStepService customPedigreeStepService) {
        this.customPedigreeStepService = customPedigreeStepService;
        this.mapUri = "/api/custom-pedigree-steps";
    }

    /**
     * 根据产品谱系ID获取最新正常工单定制工序
     *
     * @param pedigreeId 产品谱系ID
     * @return
     */
    @Operation(summary = "通过产品谱系ID获取最新正常工单的定制工序")
    @GetMapping("/byPedigreeId/{pedigreeId}")
    public ResponseEntity<ResponseData<List<CustomPedigreeStep>>> findByPedigreeId(@PathVariable("pedigreeId") Long pedigreeId) {
        return ResponseData.ok(customPedigreeStepService.findByPedigreeId(pedigreeId));
    }
}
