package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResult;
import net.airuima.rbase.dto.quality.FqcDealDTO;
import net.airuima.rbase.service.procedure.quality.FqcCheckResultService;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "FQC检测结果表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/fqc-check-results")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FQC")
@AuthSkip("ICD")
public class FqcCheckResultResource extends ProtectBaseResource<FqcCheckResult> {

    private final FqcCheckResultService fqcCheckResultService;

    public FqcCheckResultResource(FqcCheckResultService fqcCheckResultService) {
        this.fqcCheckResultService = fqcCheckResultService;
        this.mapUri = "/api/fqc-check-results";
    }

    /**
     * 保存fqc处理结果
     *
     * @param fqcDealDto
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/fqcDeal")
    @Operation(summary = "保存fqc处理结果")
    public ResponseEntity<ResponseData<Void>> fqcDeal(@RequestBody FqcDealDTO fqcDealDto) {
        fqcCheckResultService.fqcDeal(fqcDealDto);
        return ResponseData.save();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览FQC检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建FQC检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改FQC检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除FQC检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入FQC检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出FQC检测结果";
        }
        return "";
    }

}
