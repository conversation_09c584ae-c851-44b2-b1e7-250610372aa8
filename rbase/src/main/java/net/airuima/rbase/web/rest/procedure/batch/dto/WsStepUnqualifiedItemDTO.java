package net.airuima.rbase.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Schema(name = "工单工序生产过程不良项目信息")
public class WsStepUnqualifiedItemDTO {

    /**
     * 不良项目信息
     */
    @Schema(description = "不良项目信息",implementation = UnqualifiedItemDTO.class)
    private UnqualifiedItemDTO unqualifiedItem;

    /**
     * 不良项目数量
     */
    @Schema(description = "不良项目数量", type = "integer",format = "int32")
    private int number;

    public UnqualifiedItemDTO getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public WsStepUnqualifiedItemDTO setUnqualifiedItem(UnqualifiedItemDTO unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public WsStepUnqualifiedItemDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    @Schema(name = "不良项目基础信息")
    public static class UnqualifiedItemDTO{

        /**
         * 不良项目主键ID
         */
        @Schema(description = "不良项目主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 不良项目编码
         */
        @Schema(description = "不良项目编码",type = "string")
        private String code;

        /**
         * 不良项目名称
         */
        @Schema(description = "不良项目名称",type = "string")
        private String name;

        public Long getId() {
            return id;
        }

        public UnqualifiedItemDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemDTO setName(String name) {
            this.name = name;
            return this;
        }
    }
}
