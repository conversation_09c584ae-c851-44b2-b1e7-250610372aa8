package net.airuima.rbase.web.rest.procedure.aps.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 级联下单产品谱系参数DTO
 * <AUTHOR>
 */
public class CreateCascadeWsGetDTO {

    /**
     * 产品谱系
     */
    @Schema(description = "产品谱系")
    private Pedigree pedigree;


    /**
     * 投产比例
     */
    @Schema(description = "投产比例")
    private int proportion;

    /**
     * 工艺路线列表
     */
    @Schema(description = "工艺路线列表")
    private List<WorkFlowDTO> workFlowDTOList;

    /**
     * 物料清单列表
     */
    @Schema(description = "物料清单列表")
    private List<BomInfoDTO> bomInfoDTOList;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public CreateCascadeWsGetDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public int getProportion() {
        return proportion;
    }

    public CreateCascadeWsGetDTO setProportion(int proportion) {
        this.proportion = proportion;
        return this;
    }

    public List<WorkFlowDTO> getWorkFlowDTOList() {
        return workFlowDTOList;
    }

    public CreateCascadeWsGetDTO setWorkFlowDTOList(List<WorkFlowDTO> workFlowDTOList) {
        this.workFlowDTOList = workFlowDTOList;
        return this;
    }

    public List<BomInfoDTO> getBomInfoDTOList() {
        return bomInfoDTOList;
    }

    public CreateCascadeWsGetDTO setBomInfoDTOList(List<BomInfoDTO> bomInfoDTOList) {
        this.bomInfoDTOList = bomInfoDTOList;
        return this;
    }
}
