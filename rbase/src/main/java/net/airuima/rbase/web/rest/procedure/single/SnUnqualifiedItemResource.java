package net.airuima.rbase.web.rest.procedure.single;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.service.procedure.single.SnUnqualifiedItemService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产过程产生不良记录Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "单支生产过程产生不良记录Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-unqualified-items")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("Single")
@AuthSkip("ICUD")
public class SnUnqualifiedItemResource extends ProtectBaseResource<SnUnqualifiedItem> {
    private static final String MODULE = "单支生产不良明细";
    private final SnUnqualifiedItemService snUnqualifiedItemService;

    public SnUnqualifiedItemResource(SnUnqualifiedItemService snUnqualifiedItemService) {
        this.snUnqualifiedItemService = snUnqualifiedItemService;
        this.mapUri = "/api/sn-unqualified-items";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
