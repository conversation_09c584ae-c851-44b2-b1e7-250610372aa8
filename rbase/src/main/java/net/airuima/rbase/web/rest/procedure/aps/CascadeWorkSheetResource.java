package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.CascadeWorkSheet;
import net.airuima.rbase.service.procedure.aps.CascadeWorkSheetService;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsGetDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsSaveDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "工单级联关系Resource")
@RestController
@RequestMapping("/api/cascade-work-sheets")
@FuncInterceptor("CascadePlanOrder")
public class CascadeWorkSheetResource extends BaseResource<CascadeWorkSheet> {

    private final CascadeWorkSheetService cascadeWorkSheetService;

    public CascadeWorkSheetResource(CascadeWorkSheetService cascadeWorkSheetService) {
        this.mapUri = "/api/cascade-work-sheets";
        this.cascadeWorkSheetService = cascadeWorkSheetService;
    }

    /**
     * 通过物料清单ID获取不同场景下需要级联下单的参数信息
     * @param category 0:工单管理增加级联下单；1:销售订单下发工单级联下单
     * @param bomInfoId 物料清单ID
     * @return List<CreateCascadeWsGetDTO> 级联下单前端所需参数列表
     */
    @Operation(summary = "通过物料清单ID获取不同场景下需要级联下单的参数信息",parameters = {
            @Parameter(name = "category",description = "级联下单场景(0:工单管理增加级联下单；1:销售订单下发工单级联下单)",required = true,schema = @Schema(type = "integer",format = "int"),in = ParameterIn.PATH),
            @Parameter(name = "bomInfoId",description = "物料清单ID",required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH)
    })
    @GetMapping("/bom-info/{category}/{bomInfoId}")
    public ResponseEntity<ResponseData<List<CreateCascadeWsGetDTO>>> byBomInfoId(@PathVariable("category") Integer category, @PathVariable("bomInfoId") Long bomInfoId){
        try{
            return ResponseData.ok(cascadeWorkSheetService.findCascadeWorkSheetPlanToOrderInfoByBomInfoId(category,bomInfoId));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 保存级联工单信息
     * @param createCascadeWsSaveDTO 待保存级联下单参数
     */
    @PreAuthorize("hasAnyAuthority('WORKSHEET_CREATE') or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "工单管理模块保存级联工单信息")
    @PostMapping("/create/custom")
    public ResponseEntity<ResponseData<Void>> create(@RequestBody CreateCascadeWsSaveDTO createCascadeWsSaveDTO){
        try{
            cascadeWorkSheetService.createInstance(createCascadeWsSaveDTO);
            return ResponseData.ok();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            return ResponseData.error(e);
        }
    }
}
