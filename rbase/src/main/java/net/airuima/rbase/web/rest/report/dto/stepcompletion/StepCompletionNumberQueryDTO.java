package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组产出值统计DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序组产出值统计DTO")
public class StepCompletionNumberQueryDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    public Long getPlanNumber() {
        return planNumber;
    }

    public StepCompletionNumberQueryDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public StepCompletionNumberQueryDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public StepCompletionNumberQueryDTO() {
    }

    public StepCompletionNumberQueryDTO(Long planNumber, Long actualNumber) {
        this.planNumber = planNumber;
        this.actualNumber = actualNumber;
    }

}
