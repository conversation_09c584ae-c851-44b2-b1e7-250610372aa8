package net.airuima.rbase.web.rest.procedure.aps.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单提交参数DTO
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
@Schema(description = "工单提交参数DTO")
public class WorkSheetSyncMpsDTO {

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @NotBlank
    private String serialNumber;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @NotNull
    private Integer number;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    private String clientCode;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    @NotBlank
    private String materialCode;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String po;


    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheetSyncMpsDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkSheetSyncMpsDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public WorkSheetSyncMpsDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public WorkSheetSyncMpsDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getPo() {
        return po;
    }

    public WorkSheetSyncMpsDTO setPo(String po) {
        this.po = po;
        return this;
    }
}
