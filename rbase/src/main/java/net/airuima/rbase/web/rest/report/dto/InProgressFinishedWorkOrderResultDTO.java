package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/26
 */
@Schema(description = "今日已完成工单信息")
@FetchEntity
public class InProgressFinishedWorkOrderResultDTO extends PageDTO {

    /**
     * 工单信息列表
     */
    @Schema(description = "工单信息列表")
    List<WorkOrderDTO> workOrderDtoList;

    public List<WorkOrderDTO> getWorkOrderDtoList() {
        return workOrderDtoList;
    }

    public InProgressFinishedWorkOrderResultDTO setWorkOrderDtoList(List<WorkOrderDTO> workOrderDtoList) {
        this.workOrderDtoList = workOrderDtoList;
        return this;
    }



}
