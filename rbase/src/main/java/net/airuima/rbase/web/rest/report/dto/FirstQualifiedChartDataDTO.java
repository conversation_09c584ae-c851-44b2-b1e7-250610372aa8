package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 直通率图形数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "直通率图形数据DTO")
public class FirstQualifiedChartDataDTO {

    /**
     * 产品型号名称
     */
    @Schema(description = "产品型号名称")
    private String name;

    /**
     * y轴直通率值
     */
    @Schema(description = "y轴直通率值")
    private Double value;


    /**
     * x轴时间
     */
    @Schema(description = "x轴时间")
    private String time;

    public String getName() {
        return name;
    }

    public FirstQualifiedChartDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Double getValue() {
        return value;
    }

    public FirstQualifiedChartDataDTO setValue(Double value) {
        this.value = value;
        return this;
    }

    public String getTime() {
        return time;
    }

    public FirstQualifiedChartDataDTO setTime(String time) {
        this.time = time;
        return this;
    }
}
