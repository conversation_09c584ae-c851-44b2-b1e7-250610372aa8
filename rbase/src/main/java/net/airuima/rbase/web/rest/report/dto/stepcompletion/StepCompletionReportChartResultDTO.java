package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序达成图表数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序达成图表数据DTO")
public class StepCompletionReportChartResultDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    /**
     * 达成率
     */
    @Schema(description = "达成率")
    private Double completionRate;

    /**
     * 工序组达成情况条形图数据
     */
    @Schema(description = "工序组达成情况条形图数据")
    private List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionInfoDTO> stepGroupCompletionInfoList;

    /**
     * 工序组达成率折线图数据
     */
    @Schema(description = "工序组达成率折线图数据")
    private List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionRateInfoDTO> stepGroupCompletionRateList;

    public List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionInfoDTO> getStepGroupCompletionInfoList() {
        return stepGroupCompletionInfoList;
    }

    public StepCompletionReportChartResultDTO setStepGroupCompletionInfoList(List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionInfoDTO> stepGroupCompletionInfoList) {
        this.stepGroupCompletionInfoList = stepGroupCompletionInfoList;
        return this;
    }

    public List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionRateInfoDTO> getStepGroupCompletionRateList() {
        return stepGroupCompletionRateList;
    }

    public StepCompletionReportChartResultDTO setStepGroupCompletionRateList(List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepGroupCompletionRateInfoDTO> stepGroupCompletionRateList) {
        this.stepGroupCompletionRateList = stepGroupCompletionRateList;
        return this;
    }

    public Long getPlanNumber() {
        return planNumber;
    }

    public StepCompletionReportChartResultDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public StepCompletionReportChartResultDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public Double getCompletionRate() {
        return completionRate;
    }

    public StepCompletionReportChartResultDTO setCompletionRate(Double completionRate) {
        this.completionRate = completionRate;
        return this;
    }
}
