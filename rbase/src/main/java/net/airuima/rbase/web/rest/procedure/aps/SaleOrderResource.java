package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.dto.aps.SaleOrderIssuedDTO;
import net.airuima.rbase.dto.aps.WorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.exception.BadRequestException;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncSaleOrderDTO;
import net.airuima.rbase.service.procedure.aps.SaleOrderService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderCreateDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderGetDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Resource
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Tag(name = "销售订单Resource")
@RestController
@RequestMapping("/api/sale-orders")
@AuthorityRegion("生产工单")
@FuncInterceptor("SaleOrder")
public class SaleOrderResource extends ProtectBaseResource<SaleOrder> {
    private static final String EXCEPTION = "exception";
    private final SaleOrderService saleOrderService;
    private final WorkSheetService workSheetService;

    public SaleOrderResource(SaleOrderService saleOrderService, WorkSheetService workSheetService) {
        this.saleOrderService = saleOrderService;
        this.workSheetService = workSheetService;
        this.mapUri = "/api/sale-orders";
    }

    /**
     * 订单下发
     *
     * @date 2022-12-21
     * <AUTHOR>
     * @param saleOrderIssuedDTO 订单下发
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "订单下发")
    @PreventRepeatSubmit
    @PostMapping("/orderIssued")
    public ResponseEntity<ResponseData<Void>> orderIssued(@RequestBody SaleOrderIssuedDTO saleOrderIssuedDTO){
        try {
            BaseDTO responseBaseDto = saleOrderService.orderIssued(saleOrderIssuedDTO);
            if (Constants.KO.equals(responseBaseDto.getStatus())){
                return ResponseData.error("exception", responseBaseDto.getMessage());
            }
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }



    /**
     * 新增销售订单
     *
     * @param saleOrderCreateDto 销售订单
     * @return ResponseEntity<ResponseData<SaleOrder>>
     * <AUTHOR>
     * @date 2023-01-09
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @Operation(summary = "新增销售订单")
    @PostMapping("/create")
    public ResponseEntity<ResponseData<SaleOrder>> create(@RequestBody SaleOrderCreateDTO saleOrderCreateDto){
        try{
            return ResponseData.ok(saleOrderService.saveInstance(saleOrderCreateDto));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 更新销售订单
     *
     * @param saleOrderCreateDto 销售订单
     * @return ResponseEntity<ResponseData<SaleOrder>>
     * <AUTHOR>
     * @date 2023-01-09
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @Operation(summary = "更新销售订单")
    @PostMapping("/update")
    public ResponseEntity<ResponseData<SaleOrder>> update(@RequestBody SaleOrderCreateDTO saleOrderCreateDto){
        try{
            return ResponseData.ok(saleOrderService.saveInstance(saleOrderCreateDto));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 根据销售订单id获取销售订单详情
     *
     * @param saleOrderId  销售订单id
     * @return   SaleOrderGetDTO
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据销售订单id获取销售订单详情")
    @GetMapping("/saleOrderId/{saleOrderId}")
    public ResponseEntity<ResponseData<SaleOrderGetDTO>> getSaleOrderDetail(@PathVariable Long saleOrderId){
        try{
            return ResponseData.ok(saleOrderService.getSaleOrderDetail(saleOrderId));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }



    /**
     * 同步订单
     * @param syncSaleOrderList
     * <AUTHOR>
     * @date  2023/3/17
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "同步订单信息")
    @PostMapping("/syncSaleOrder")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncSaleOrder(@RequestBody List<SyncSaleOrderDTO> syncSaleOrderList){
        try {
            return ResponseData.ok(saleOrderService.syncSaleOrder(syncSaleOrderList));
        }catch (Exception e){
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 通过销售订单主键ID获取已下发的工单列表
     * @param id        过销售订单主键ID
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData<java.util.List<net.airuima.dto.aps.WorkSheetSimpleGetDTO>>> 工单信息列表
     * <AUTHOR>
     * @date 2024/1/29
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过销售订单主键ID获取已下发的工单列表",parameters = {
            @Parameter(name = "id",description = "销售订单主键ID",schema = @Schema(type = "integer",format = "int64"),required = true,in = ParameterIn.PATH)
    })
    @GetMapping("/work-sheets/{id}")
    public ResponseEntity<ResponseData<SaleOrderProcessDTO>> worksheet(@PathVariable("id") Long id){
        return ResponseData.ok(workSheetService.findBySaleOrderId(id));
    }

    /**
     * 根据条件分页查询销售订单
     *
     * @param pageable 分页
     * @param type     类型:0按优先级排序desc，1 按交付时间排序 desc
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.rbase.domain.procedure.aps.SaleOrder>>> 销售订单列表
     */
    @GetMapping("/findByPageSort")
    public ResponseEntity<ResponseData<List<SaleOrder>>> findByPageSort(@PageableDefault(size = 1) @ParameterObject Pageable pageable,
                                                                        @RequestParam("type") Integer type) {
        return ResponseData.ok(saleOrderService.findByPageSort(pageable, type));
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority,"销售订单");
    }

}
