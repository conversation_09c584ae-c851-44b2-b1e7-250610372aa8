package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线产出值分组统计DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线产出值分组统计DTO")
public class WorkLineCompletionChartQueryDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    /**
     * 产线名字
     */
    @Schema(description = "产线名字")
    private String name;

    public Long getPlanNumber() {
        return planNumber;
    }

    public WorkLineCompletionChartQueryDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public WorkLineCompletionChartQueryDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public String getName() {
        return name;
    }

    public WorkLineCompletionChartQueryDTO setName(String name) {
        this.name = name;
        return this;
    }

    public WorkLineCompletionChartQueryDTO() {
    }

    public WorkLineCompletionChartQueryDTO(String name, Long planNumber, Long actualNumber) {
        this.name = name;
        this.planNumber = planNumber;
        this.actualNumber = actualNumber;
    }
}
