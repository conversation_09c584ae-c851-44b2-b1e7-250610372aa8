package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.aps.WsSaleOrderDetail;
import net.airuima.rbase.service.procedure.aps.WsSaleOrderDetailService;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工单关联销售订单详情
 *
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "工单关联销售订单详情")
@RestController
@RequestMapping("/api/ws-sale-order-detail")
@AuthorityRegion("销售管理")
public class WsSaleOrderDetailResource extends ProtectBaseResource<WsSaleOrderDetail> {

    private final WsSaleOrderDetailService wsSaleOrderDetailService;

    public WsSaleOrderDetailResource(WsSaleOrderDetailService wsSaleOrderDetailService) {
        this.wsSaleOrderDetailService = wsSaleOrderDetailService;
        this.mapUri = "/api/ws-sale-order-detail";
    }

    /**
     * 根据销售订单详情ID列表查询工作表信息
     *
     * @param ids 过销售订单主键ID
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.dto.aps.WorkSheetSimpleGetDTO>>> 工单信息列表
     * <AUTHOR>
     * @date 2024/1/29
     */
    @Operation(summary = "根据销售订单详情ID列表查询工作表信息")
    @PostMapping("/work-sheets")
    public ResponseEntity<ResponseData<List<SaleOrderProcessDTO>>> findWorkSheetByIds(@RequestBody List<Long> ids) {
        return ResponseData.ok(wsSaleOrderDetailService.findWorkSheetByIds(ids));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工单关联销售订单详情");
    }
}