package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序不良图形数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "工序不良图形数据DTO")
public class StepUnqualifiedDTO {

    /**
     * 工序名字
     */
    @Schema(description = "工序名字")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;

    public String getName() {
        return name;
    }

    public StepUnqualifiedDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StepUnqualifiedDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public StepUnqualifiedDTO() {
    }

    public StepUnqualifiedDTO(String name, Long number) {
        this.name = name;
        this.number = number;
    }
}
