package net.airuima.rbase.web.rest.procedure.reinspect;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspectResult;
import net.airuima.rbase.service.procedure.reinspect.StepReinspectService;
import net.airuima.rbase.web.rest.procedure.reinspect.dto.StepReinspectProcessDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Tag(name = "工序不良复检Resource")
@RestController
@RequestMapping("/api/step-reinspects")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("QReinspection")
@AuthSkip("ICD")
public class StepReinspectResource extends ProtectBaseResource<StepReinspect> {

    private static final String MODULE = "工序不良复检";

    private final StepReinspectService stepReinspectService;


    public StepReinspectResource(StepReinspectService stepReinspectService) {
        this.stepReinspectService = stepReinspectService;
        this.mapUri = "/api/step-reinspects";
    }

    /**
     * 模糊查询符合处理方式的不良项目列表
     * @param id 不良复检记录ID
     * @param result 复检结果
     * @param text 不良模糊查询名称或编码
     * @param size 返回条数
     * @return List<UnqualifiedItem>
     */
    @Operation(summary = "模糊查询符合处理方式的不良项目列表",parameters = {
            @Parameter(name = "id",description = "不良复检记录ID"),
            @Parameter(name = "result",description = "复检处理方式(0:放行;1:返工;2:报废)")
    })
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/id/{id}/result/{result}")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> likeUnqualifiedItem(@PathVariable("id") Long id,@PathVariable("result") Integer result,
                                                                                   @RequestParam(value = "text") String text, @RequestParam(value = "size") Integer size){
        return ResponseData.ok(stepReinspectService.likeUnqualifiedItem(id,result,text, size));
    }

    /**
     * 根据不良复检记录ID获取处理结果明细数据
     * @param id 不良复检记录ID
     * @return List<StepReinspectResult>
     */
    @Operation(summary = "根据不良复检记录ID获取处理结果明细数据",parameters = {
            @Parameter(name = "id",description = "不良复检记录ID")
    })
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/result/{id}")
    public ResponseEntity<ResponseData<List<StepReinspectResult>>> detail(@PathVariable("id") Long id){
        return ResponseData.ok(stepReinspectService.findResultById(id));
    }

    /**
     * 保存工序复检的结果数据
     * @param stepReinspectProcessDTOList 工序复检的结果数据列表
     */
    @Operation(summary = "保存工序复检的结果数据")
    @PreAuthorize("hasAnyAuthority('STEPREINSPECT_UPDATE') or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/process")
    public ResponseEntity<ResponseData<Void>> process(@RequestBody List<StepReinspectProcessDTO> stepReinspectProcessDTOList){
        try{
            stepReinspectService.process(stepReinspectProcessDTOList);
            return ResponseData.ok();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
