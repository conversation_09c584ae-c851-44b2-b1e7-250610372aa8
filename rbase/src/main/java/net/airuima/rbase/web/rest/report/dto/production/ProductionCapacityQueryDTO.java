package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "产量统计查询结果DTO")
public class ProductionCapacityQueryDTO {

    /**
     * 总工单数
     */
    @Schema(description = "总工单数")
    private Long countNumber;

    /**
     * 总投产数
     */
    @Schema(description = "总投产数")
    private Long number;

    /**
     * 总产量
     */
    @Schema(description = "总产量")
    private Long actualFinishNumber;

    /**
     * 总产出(总合格数)
     */
    @Schema(description = "总产出")
    private Long qualifiedNumber;


    public Long getCountNumber() {
        return countNumber;
    }

    public ProductionCapacityQueryDTO setCountNumber(Long countNumber) {
        this.countNumber = countNumber;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityQueryDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getActualFinishNumber() {
        return actualFinishNumber;
    }

    public ProductionCapacityQueryDTO setActualFinishNumber(Long actualFinishNumber) {
        this.actualFinishNumber = actualFinishNumber;
        return this;
    }

    public ProductionCapacityQueryDTO() {
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ProductionCapacityQueryDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public ProductionCapacityQueryDTO(Long countNumber, Long number, Long actualFinishNumber, Long qualifiedNumber) {
        this.countNumber = countNumber;
        this.number = number;
        this.actualFinishNumber = actualFinishNumber;
        this.qualifiedNumber = qualifiedNumber;
    }


}
