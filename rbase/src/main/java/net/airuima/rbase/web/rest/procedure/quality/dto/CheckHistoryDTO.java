package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryItemSnapshot;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 检验过程数据DTO
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Schema(name = "检验过程数据DTO", description = "检验过程数据DTO")
public class CheckHistoryDTO implements Serializable {

    /**
     * 检测历史
     */
    @Schema(description = "检测历史")
    private CheckHistory checkHistory;
    /**
     * 检测历史明细列表
     */
    @Schema(description = "检测历史明细列表")
    private List<CheckHistoryDetailSnInfo> checkHistoryDetailList;

    /**
     * 检测历史条件详情记录列表
     */
    @Schema(description = "检测历史条件详情记录列表")
    private List<CheckHistoryItemSnapshot> checkHistoryItemSnapshotList;

    /**
     * 报检SN列表(仅在不良处理时使用)
     */
    @Schema(description = "报检SN列表(仅在不良处理时使用)")
    private List<String> fullInspectSnList;

    public CheckHistory getCheckHistory() {
        return checkHistory;
    }

    public CheckHistoryDTO setCheckHistory(CheckHistory checkHistory) {
        this.checkHistory = checkHistory;
        return this;
    }

    public List<CheckHistoryDetailSnInfo> getCheckHistoryDetailList() {
        return checkHistoryDetailList;
    }

    public CheckHistoryDTO setCheckHistoryDetailList(List<CheckHistoryDetailSnInfo> checkHistoryDetailList) {
        this.checkHistoryDetailList = checkHistoryDetailList;
        return this;
    }

    public List<CheckHistoryItemSnapshot> getCheckHistoryItemSnapshotList() {
        return checkHistoryItemSnapshotList;
    }

    public List<String> getFullInspectSnList() {
        return fullInspectSnList;
    }

    public CheckHistoryDTO setFullInspectSnList(List<String> fullInspectSnList) {
        this.fullInspectSnList = fullInspectSnList;
        return this;
    }

    public CheckHistoryDTO setCheckHistoryItemSnapshotList(List<CheckHistoryItemSnapshot> checkHistoryItemSnapshotList) {
        this.checkHistoryItemSnapshotList = checkHistoryItemSnapshotList;
        return this;
    }

    @Schema(name = "根据sn分组的明细信息", description = "根据sn分组的明细信息")
    public static class CheckHistoryDetailSnInfo implements Serializable{
        /**
         * sn
         */
        @Schema(description = "sn")
        private String sn;

        /**
         * 检测项目列表
         */
        @Schema(description = "检测项目列表")
        private List<CheckItemInfo> checkItemList;

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public List<CheckItemInfo> getCheckItemList() {
            return checkItemList;
        }

        public void setCheckItemList(List<CheckItemInfo> checkItemList) {
            this.checkItemList = checkItemList;
        }

        public CheckHistoryDetailSnInfo() {
        }

        public CheckHistoryDetailSnInfo(String sn, List<CheckItemInfo> checkItemList) {
            this.sn = sn;
            this.checkItemList = checkItemList;
        }
    }

    @FetchEntity
    @Schema(name = "检测项目VO", description = "检测项目VO")
    public static class CheckItemInfo implements Serializable{
        /**
         * 检测项名称
         */
        @Schema(description = "检测项名称")
        private String name;
        /**
         * 检测项编码
         */
        @Schema(description = "检测项编码")
        private String code;
        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        /**
         * 禁用启用(0:禁用;1:启用)
         */
        @Schema(description = "禁用启用(0:禁用;1:启用)")
        private boolean isEnable;

        /**
         * 是否管控其检查结果(0:不管控；1：管控)
         */
        @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
        private boolean control;

        /**
         * 计量单位id
         */
        @Schema(description = "计量单位id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unitId;

        @Transient
        @Schema(description = "计量单位")
        @FetchField(mapUri = "/api/metering-units", serviceId = "mom", paramKey = "unitId")
        private MeteringUnitDTO unitDTO = new MeteringUnitDTO();

        /**
         * 项目类型
         */
        @Schema(description = "项目类型")
        private VarietyDTO varietyObj;

        /**
         * 检验方法:目测0/检测仪器1
         */
        @Schema(description = "检验方法:目测0/检测仪器1")
        private int inspectWay;

        /**
         * 分析方法:定性0/定量1
         */
        @Schema(description = "分析方法:定性0/定量1")
        private int analyseWay;

        /**
         * 检测仪器id
         */
        @Schema(description = "检测仪器id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long facilityId;

        @Transient
        @Schema(description = "检测仪器")
        @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
        private FacilityDTO facilityDTO = new FacilityDTO();

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String note;

        private CheckHistoryDetailInfo checkHistoryDetail;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public void setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
        }

        public boolean getIsEnable() {
            return isEnable;
        }

        public void setIsEnable(boolean enable) {
            isEnable = enable;
        }

        public boolean getControl() {
            return control;
        }

        public void setControl(boolean control) {
            this.control = control;
        }

        public Long getUnitId() {
            return unitId;
        }

        public void setUnitId(Long unitId) {
            this.unitId = unitId;
        }

        public MeteringUnitDTO getUnitDTO() {
            return unitDTO;
        }

        public void setUnitDTO(MeteringUnitDTO unitDTO) {
            this.unitDTO = unitDTO;
        }

        public VarietyDTO getVarietyObj() {
            return varietyObj;
        }

        public void setVarietyObj(VarietyDTO varietyObj) {
            this.varietyObj = varietyObj;
        }

        public int getInspectWay() {
            return inspectWay;
        }

        public void setInspectWay(int inspectWay) {
            this.inspectWay = inspectWay;
        }

        public int getAnalyseWay() {
            return analyseWay;
        }

        public void setAnalyseWay(int analyseWay) {
            this.analyseWay = analyseWay;
        }

        public Long getFacilityId() {
            return facilityId;
        }

        public void setFacilityId(Long facilityId) {
            this.facilityId = facilityId;
        }

        public FacilityDTO getFacilityDTO() {
            return facilityDTO;
        }

        public void setFacilityDTO(FacilityDTO facilityDTO) {
            this.facilityDTO = facilityDTO;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public CheckHistoryDetailInfo getCheckHistoryDetail() {
            return checkHistoryDetail;
        }

        public void setCheckHistoryDetail(CheckHistoryDetailInfo checkHistoryDetail) {
            this.checkHistoryDetail = checkHistoryDetail;
        }


    }

    @Schema(name = "检测明细", description = "检测明细")
    public static class CheckHistoryDetailInfo implements Serializable{

        /**
         * 检测结果(0:不合格;1:合格)
         */
        @Schema(description = "检测结果(0:不合格;1:合格)")
        private boolean result;

        /**
         * 检测历史
         */
        @Schema(description = "检测历史")
        private CheckHistory checkHistory;

        /**
         * 检测数据值
         */
        @Schema(description = "检测数据值")
        private String checkData;

        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        /**
         * 缺陷原因
         */
        @Schema(description = "缺陷原因")
        private DefectDTO defect;

        /**
         * 文件列表
         */
        @Schema(description = "文件列表")
        private List<DocumentDTO> documentDTOList;

        /**
         *
         */
        @Schema(description = "备注")
        private String note;

        public List<DocumentDTO> getDocumentDTOList() {
            return documentDTOList;
        }

        public CheckHistoryDetailInfo setDocumentDTOList(List<DocumentDTO> documentDTOList) {
            this.documentDTOList = documentDTOList;
            return this;
        }

        public boolean getResult() {
            return result;
        }

        public void setResult(boolean result) {
            this.result = result;
        }

        public CheckHistory getCheckHistory() {
            return checkHistory;
        }

        public void setCheckHistory(CheckHistory checkHistory) {
            this.checkHistory = checkHistory;
        }

        public String getCheckData() {
            return checkData;
        }

        public void setCheckData(String checkData) {
            this.checkData = checkData;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public void setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
        }

        public DefectDTO getDefect() {
            return defect;
        }

        public void setDefect(DefectDTO defect) {
            this.defect = defect;
        }

        public String getNote() {
            return note;
        }

        public CheckHistoryDetailInfo setNote(String note) {
            this.note = note;
            return this;
        }
    }
}
