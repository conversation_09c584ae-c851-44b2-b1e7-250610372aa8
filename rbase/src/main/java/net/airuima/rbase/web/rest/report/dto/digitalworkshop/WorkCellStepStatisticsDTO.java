package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.util.NumberUtils;

import java.util.List;

@Schema(description = "数字车间驾驶舱-工位工序")
public class WorkCellStepStatisticsDTO {

    /**
     * 工位信息
     */
    @Schema(description = "工位信息")
    private WorkCellInfo workCellInfo;

    /**
     * 工位工序 投产详情数据列表
     */
    @Schema(description = "工位工序 投产详情数据列表")
    private List<WorkCellStepProductionInfo> workCellStepProductionInfos;


    public WorkCellInfo getWorkCellInfo() {
        return workCellInfo;
    }

    public WorkCellStepStatisticsDTO setWorkCellInfo(WorkCellInfo workCellInfo) {
        this.workCellInfo = workCellInfo;
        return this;
    }

    public List<WorkCellStepProductionInfo> getWorkCellStepProductionInfos() {
        return workCellStepProductionInfos;
    }

    public WorkCellStepStatisticsDTO setWorkCellStepProductionInfos(List<WorkCellStepProductionInfo> workCellStepProductionInfos) {
        this.workCellStepProductionInfos = workCellStepProductionInfos;
        return this;
    }

    public static class WorkCellInfo {

        private Long id;

        private String code;

        private String name;

        public WorkCellInfo() {
        }

        public WorkCellInfo(WorkCell workCell) {
            this.id = workCell.getId();
            this.code = workCell.getCode();
            this.name = workCell.getName();
        }

        public Long getId() {
            return id;
        }

        public WorkCellInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkCellInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkCellInfo setName(String name) {
            this.name = name;
            return this;
        }
    }

    public static class StepInfo {
        private Long id;

        private String code;

        private String name;

        public StepInfo() {
        }


        public StepInfo(Step step) {
            this.id = step.getId();
            this.code = step.getCode();
            this.name = step.getName();
        }

        public Long getId() {
            return id;
        }

        public StepInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepInfo setName(String name) {
            this.name = name;
            return this;
        }
    }


    /**
     * 工位工序 投产详情数据
     */
    @Schema(description = "工位工序 投产详情数据")
    public static class WorkCellStepProductionInfo {

        /**
         * 工位
         */
        @Schema(description = "工位")
        private WorkCellInfo workCellInfo;
        /**
         * 工序
         */
        @Schema(description = "工序")
        private StepInfo stepInfo;
        /**
         * 工单数量
         */
        @Schema(description = "工单数量")
        private Long countProductionOrderNumber;
        /**
         * 工单投产数量
         */
        @Schema(description = "工单投产数量")
        private Long overallProductionNumber;
        /**
         * 合格数
         */
        @Schema(description = "合格数")
        private Long qualifiedNumber;
        /**
         * 不合格数
         */
        @Schema(description = "不合格数")
        private Long unqualifiedNumber;

        /**
         * 完成数量
         */
        @Schema(description = "完成数量")
        private Long finishNumber;
        /**
         * 直通率
         */
        @Schema(description = "直通率")
        private Double fpy;
        /**
         * 合格率
         */
        @Schema(description = "合格率")
        private Double fty;
        /**
         * 工序工位不良项目对应不合格数量
         */
        @Schema(description = "工序工位不良项目对应不合格数量")
        private List<WorkCellStepInfoUnqualifiedItem> workCellStepInfoUnqualifiedItems;

        public WorkCellStepProductionInfo(WorkCell workCell, Step step ,Long countProductionOrderNumber, Long overallProductionNumber, Long qualifiedNumber, Long unqualifiedNumber) {
            this.workCellInfo = new WorkCellInfo(workCell);
            this.stepInfo = new StepInfo(step);
            this.countProductionOrderNumber = countProductionOrderNumber;
            this.overallProductionNumber = overallProductionNumber;
            this.qualifiedNumber = qualifiedNumber;
            this.unqualifiedNumber = unqualifiedNumber;
            this.finishNumber = qualifiedNumber + unqualifiedNumber;
            this.fty = net.airuima.rbase.constant.Constants.DOUBLE_ZERRO;
            this.fpy = net.airuima.rbase.constant.Constants.DOUBLE_ZERRO;
            if (countProductionOrderNumber != null) {
                this.fpy = NumberUtils.divide(qualifiedNumber, overallProductionNumber, net.airuima.constant.Constants.INT_FOUR).doubleValue();
            }
            if (finishNumber != null){
                this.fty = NumberUtils.divide(qualifiedNumber, finishNumber, Constants.INT_FOUR).doubleValue();
            }
        }

        public Long getFinishNumber() {
            return finishNumber;
        }

        public WorkCellStepProductionInfo setFinishNumber(Long finishNumber) {
            this.finishNumber = finishNumber;
            return this;
        }

        public Double getFpy() {
            return fpy;
        }

        public WorkCellStepProductionInfo setFpy(Double fpy) {
            this.fpy = fpy;
            return this;
        }

        public List<WorkCellStepInfoUnqualifiedItem> getWorkCellStepInfoUnqualifiedItems() {
            return workCellStepInfoUnqualifiedItems;
        }

        public WorkCellStepProductionInfo setWorkCellStepInfoUnqualifiedItems(List<WorkCellStepInfoUnqualifiedItem> workCellStepInfoUnqualifiedItems) {
            this.workCellStepInfoUnqualifiedItems = workCellStepInfoUnqualifiedItems;
            return this;
        }

        public WorkCellInfo getWorkCellInfo() {
            return workCellInfo;
        }

        public WorkCellStepProductionInfo setWorkCellInfo(WorkCellInfo workCellInfo) {
            this.workCellInfo = workCellInfo;
            return this;
        }

        public StepInfo getStepInfo() {
            return stepInfo;
        }

        public WorkCellStepProductionInfo setStepInfo(StepInfo stepInfo) {
            this.stepInfo = stepInfo;
            return this;
        }

        public Long getCountProductionOrderNumber() {
            return countProductionOrderNumber;
        }

        public WorkCellStepProductionInfo setCountProductionOrderNumber(Long countProductionOrderNumber) {
            this.countProductionOrderNumber = countProductionOrderNumber;
            return this;
        }

        public Long getOverallProductionNumber() {
            return overallProductionNumber;
        }

        public WorkCellStepProductionInfo setOverallProductionNumber(Long overallProductionNumber) {
            this.overallProductionNumber = overallProductionNumber;
            return this;
        }

        public Long getQualifiedNumber() {
            return qualifiedNumber;
        }

        public WorkCellStepProductionInfo setQualifiedNumber(Long qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Long getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public WorkCellStepProductionInfo setUnqualifiedNumber(Long unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public Double getFty() {
            return fty;
        }

        public WorkCellStepProductionInfo setFty(Double fty) {
            this.fty = fty;
            return this;
        }
    }


    public static class WorkCellStepInfoUnqualifiedItem {
        /**
         * 工位
         */
        @Schema(description = "工位")
        private WorkCellInfo workCellInfo;
        /**
         * 工序
         */
        @Schema(description = "工序")
        private StepInfo stepInfo;
        /**
         * 不良项目
         */
        @Schema(description = "不良项目")
        private UnqualifiedStatisticsDTO.UnqualifiedItemInfo unqualifiedItemInfo;
        /**
         * 不合格数量
         */
        @Schema(description = "不合格数量")
        private Long unqualifiedNumber;

        public WorkCellStepInfoUnqualifiedItem() {
        }

        public WorkCellStepInfoUnqualifiedItem(WorkCell workCell, Step step, UnqualifiedItem unqualifiedItem, Long unqualifiedNumber) {
            this.workCellInfo = new WorkCellInfo(workCell);
            this.stepInfo = new StepInfo(step);
            this.unqualifiedItemInfo = new UnqualifiedStatisticsDTO.UnqualifiedItemInfo(unqualifiedItem);
            this.unqualifiedNumber = unqualifiedNumber;
        }

        public WorkCellInfo getWorkCellInfo() {
            return workCellInfo;
        }

        public WorkCellStepInfoUnqualifiedItem setWorkCellInfo(WorkCellInfo workCellInfo) {
            this.workCellInfo = workCellInfo;
            return this;
        }

        public StepInfo getStepInfo() {
            return stepInfo;
        }

        public WorkCellStepInfoUnqualifiedItem setStepInfo(StepInfo stepInfo) {
            this.stepInfo = stepInfo;
            return this;
        }

        public UnqualifiedStatisticsDTO.UnqualifiedItemInfo getUnqualifiedItemInfo() {
            return unqualifiedItemInfo;
        }

        public WorkCellStepInfoUnqualifiedItem setUnqualifiedItemInfo(UnqualifiedStatisticsDTO.UnqualifiedItemInfo unqualifiedItemInfo) {
            this.unqualifiedItemInfo = unqualifiedItemInfo;
            return this;
        }

        public Long getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public WorkCellStepInfoUnqualifiedItem setUnqualifiedNumber(Long unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }
    }

}
