package net.airuima.rbase.web.rest.report.dto.digitalworkshop;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkLine;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

@Schema(description = "数字车间-产线统计")
public class WorkLineStatisticsDTO implements Serializable {

    /**
     * 生产线id
     */
    @Schema(description = "生产线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 组织架构ID
     */
    @JsonIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String code;

    /**
     * 生产线名称
     */
    @Schema(description = "生产线名称")
    private String name;

    /**
     * 今日工单数
     */
    @Schema(description = "今日工单数")
    private Long number;

    /**
     * 今日计划完成数
     */
    @Schema(description = "今日计划完成数")
    private Long planFinishNumber;


    /**
     * 在线返修数
     */
    @Schema(description = "在线返修数")
    private Long reworkQualifiedNumber;

    /**
     * 今日已完成数
     */
    @Schema(description = "今日已完成数")
    private Long finishNumber;

    /**
     * 今日合格数
     */
    @Schema(description = "今日合格数")
    private Long qualifiedNumber;

    /**
     * 今日不合格数
     */
    @Schema(description = "今日不合格数")
    private Long unqualifiedNumber;

    /**
     * 工单状态 对应比例列表
     */
    @Schema(description = "工单状态 对应比例列表")
    private List<RateInfo> rateInfoList;


    public WorkLineStatisticsDTO() {
    }


    public WorkLineStatisticsDTO(Long organizationId,WorkLine workLine, Long number, Long planFinishNumber) {
        this.organizationId = organizationId;
        this.id = workLine.getId();
        this.code = workLine.getCode();
        this.name = workLine.getName();
        this.number = number;
        this.planFinishNumber = planFinishNumber;
        this.finishNumber = Constants.LONG_ZERO;
        this.qualifiedNumber = Constants.LONG_ZERO;
        this.reworkQualifiedNumber = Constants.LONG_ZERO;
        this.unqualifiedNumber = net.airuima.constant.Constants.LONG_ZERO;
    }


    public WorkLineStatisticsDTO(Long organizationId,WorkLine workLine, Long finishNumber, Long qualifiedNumber, Long unqualifiedNumber,Long reworkQualifiedNumber) {
        this.organizationId = organizationId;
        this.id = workLine.getId();
        this.code = workLine.getCode();
        this.name = workLine.getName();
        this.number = net.airuima.constant.Constants.LONG_ZERO;
        this.planFinishNumber = net.airuima.constant.Constants.LONG_ZERO;
        this.finishNumber = finishNumber;
        this.qualifiedNumber = qualifiedNumber;
        this.unqualifiedNumber = unqualifiedNumber;
        this.reworkQualifiedNumber = Optional.ofNullable(reworkQualifiedNumber).orElse(net.airuima.constant.Constants.LONG_ZERO);
    }

    public Long getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkLineStatisticsDTO setUnqualifiedNumber(Long unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkLineStatisticsDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getId() {
        return id;
    }

    public WorkLineStatisticsDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkLineStatisticsDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public WorkLineStatisticsDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkLineStatisticsDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getPlanFinishNumber() {
        return planFinishNumber;
    }

    public WorkLineStatisticsDTO setPlanFinishNumber(Long planFinishNumber) {
        this.planFinishNumber = planFinishNumber;
        return this;
    }

    public Long getFinishNumber() {
        return finishNumber;
    }

    public WorkLineStatisticsDTO setFinishNumber(Long finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkLineStatisticsDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public WorkLineStatisticsDTO setReworkQualifiedNumber(Long reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        return this;
    }

    public List<RateInfo> getRateInfoList() {
        return rateInfoList;
    }

    public WorkLineStatisticsDTO setRateInfoList(List<RateInfo> rateInfoList) {
        this.rateInfoList = rateInfoList;
        return this;
    }

    public static class RateInfo {

        private String description;

        private Double rate;

        public RateInfo() {
        }

        public RateInfo(String description, Double rate) {
            this.description = description;
            this.rate = rate;
        }

        public String getDescription() {
            return description;
        }

        public RateInfo setDescription(String description) {
            this.description = description;
            return this;
        }

        public Double getRate() {
            return rate;
        }

        public RateInfo setRate(Double rate) {
            this.rate = rate;
            return this;
        }
    }
}
