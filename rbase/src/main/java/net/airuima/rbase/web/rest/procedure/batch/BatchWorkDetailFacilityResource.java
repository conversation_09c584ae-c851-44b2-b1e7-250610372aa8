package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailFacilityService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情设备Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "批量生产详情设备Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/batch-work-detail-facilities")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("FBase")
@AuthSkip("ICUD")
public class BatchWorkDetailFacilityResource extends ProtectBaseResource<BatchWorkDetailFacility> {

    private final BatchWorkDetailFacilityService batchWorkDetailFacilityService;

    public BatchWorkDetailFacilityResource(BatchWorkDetailFacilityService batchWorkDetailFacilityService) {
        this.batchWorkDetailFacilityService = batchWorkDetailFacilityService;
        this.mapUri = "/api/batch-work-detail-equipments";
    }

    /**
     * @throws
     * @description 根据批量工作详情ID获取设备信息
     * <AUTHOR>
     * @param: batchWorkDetailId 工单工序生产详情ID
     * @updateTime 2020/12/22 19:33
     * @return: java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailEquipment>
     **/
    @Operation(summary = "根据批量工作详情ID获取设备信息")
    @GetMapping("/byBatchWorkDetailId/{batchWorkDetailId}")
    public ResponseEntity<ResponseData<List<BatchWorkDetailFacility>>> findByBatchWorkDetailId(@PathVariable(value = "batchWorkDetailId") Long batchWorkDetailId) {

        return ResponseData.ok(Collections.emptyList());
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "批量生产设备详情");
    }

}
