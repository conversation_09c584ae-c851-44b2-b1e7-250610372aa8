package net.airuima.rbase.web.rest.report.dto.onlinestatistics;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产品生产在制看板DTO
 * <AUTHOR>
 * @date 2023/12/19
 */
@Schema(description = "产品生产在制看板结果数据DTO")
public class OnlineProductStatisticsResultDTO {

    /**
     * 生产在制工单/子工单总数
     */
    @Schema(description = "生产在制工单/子工单总数")
    private Integer onlineWorkSheetNumber;

    /**
     * 生产在制工单/子工单逾期总数
     */
    @Schema(description = "生产在制工单/子工单逾期总数")
    private Integer onlineExpiredWorkSheetNumber;

    /**
     * 生产在制工序/工序组总明细列表
     */
    @Schema(description = "生产在制工序/工序组总明细列表")
    private List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList;

    /**
     * 产品谱系工序/工序组生产在制明细列表
     */
    @Schema(description = "产品谱系工序/工序组生产在制明细列表")
    private List<PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDtoList;

    public Integer getOnlineWorkSheetNumber() {
        return onlineWorkSheetNumber;
    }

    public OnlineProductStatisticsResultDTO setOnlineWorkSheetNumber(Integer onlineWorkSheetNumber) {
        this.onlineWorkSheetNumber = onlineWorkSheetNumber;
        return this;
    }

    public Integer getOnlineExpiredWorkSheetNumber() {
        return onlineExpiredWorkSheetNumber;
    }

    public OnlineProductStatisticsResultDTO setOnlineExpiredWorkSheetNumber(Integer onlineExpiredWorkSheetNumber) {
        this.onlineExpiredWorkSheetNumber = onlineExpiredWorkSheetNumber;
        return this;
    }

    public List<StepOnlineStatisticsDTO> getStepOnlineStatisticsDtoList() {
        return stepOnlineStatisticsDtoList;
    }

    public OnlineProductStatisticsResultDTO setStepOnlineStatisticsDtoList(List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList) {
        this.stepOnlineStatisticsDtoList = stepOnlineStatisticsDtoList;
        return this;
    }

    public List<PedigreeStepOnlineStatisticsDTO> getPedigreeStepOnlineStatisticsDtoList() {
        return pedigreeStepOnlineStatisticsDtoList;
    }

    public OnlineProductStatisticsResultDTO setPedigreeStepOnlineStatisticsDtoList(List<PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDtoList) {
        this.pedigreeStepOnlineStatisticsDtoList = pedigreeStepOnlineStatisticsDtoList;
        return this;
    }

    /**
     * 生产在制工序/工序组明细DTO
     */
    @Schema(description = "生产在制工序/工序组明细DTO")
    public static class StepOnlineStatisticsDTO{

        /**
         * 工序/工序组编码
         */
        @Schema(description = "工序/工序组编码")
        private String code;

        /**
         * 工序/工序组名称
         */
        @Schema(description = "工序/工序组名称")
        private String name;


        /**
         * 目标合格率
         */
        @Schema(description = "目标合格率")
        private Double targetPassRate = Constants.DOUBLE_ONE;

        /**
         * 计划产出
         */
        @Schema(description = "计划产出")
        private Integer targetPlanNumber;


        /**
         * 工序投产数
         */
        @Schema(description = "工序投产数")
        private Integer inputNumber;

        /**
         * 工序合格数量
         */
        @Schema(description = "工序合格数量")
        private Integer qualifiedNumber;

        /**
         * 工序不合格数量
         */
        @Schema(description = "工序不合格数量")
        private Integer unqualifiedNumber;

        /**
         * 下交数量
         */
        @Schema(description = "下交数量")
        private Integer transferNumber;

        /**
         * 在制数量
         */
        @Schema(description = "在制数量")
        private Integer onlineNumber;

        public String getCode() {
            return code;
        }

        public StepOnlineStatisticsDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepOnlineStatisticsDTO setName(String name) {
            this.name = name;
            return this;
        }

        public Double getTargetPassRate() {
            return targetPassRate;
        }

        public StepOnlineStatisticsDTO setTargetPassRate(Double targetPassRate) {
            this.targetPassRate = targetPassRate;
            return this;
        }

        public Integer getTargetPlanNumber() {
            return targetPlanNumber;
        }

        public StepOnlineStatisticsDTO setTargetPlanNumber(Integer targetPlanNumber) {
            this.targetPlanNumber = targetPlanNumber;
            return this;
        }

        public Integer getInputNumber() {
            return inputNumber;
        }

        public StepOnlineStatisticsDTO setInputNumber(Integer inputNumber) {
            this.inputNumber = inputNumber;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public StepOnlineStatisticsDTO setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public StepOnlineStatisticsDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public Integer getTransferNumber() {
            return transferNumber;
        }

        public StepOnlineStatisticsDTO setTransferNumber(Integer transferNumber) {
            this.transferNumber = transferNumber;
            return this;
        }

        public Integer getOnlineNumber() {
            return onlineNumber;
        }

        public StepOnlineStatisticsDTO setOnlineNumber(Integer onlineNumber) {
            this.onlineNumber = onlineNumber;
            return this;
        }
    }

    /**
     * 产品谱系工序/工序组生产在制明细DTO
     */
    @Schema(description = "产品谱系工序/工序组生产在制明细DTO")
    public static class PedigreeStepOnlineStatisticsDTO{

        /**
         * 产品谱系编码
         */
        @Schema(description = "产品谱系编码")
        private String code;

        /**
         * 产品谱系名称
         */
        @Schema(description = "产品谱系名称")
        private String name;

        /**
         * 产品谱系规格型号
         */
        @Schema(description = "产品谱系规格型号")
        private String specification;

        /**
         * 产品谱系在制工单总数
         */
        @Schema(description = "产品谱系在制工单总数")
        private Integer onlineWorkSheetNumber;

        /**
         * 产品谱系在制工单逾期总数
         */
        @Schema(description = "产品谱系在制工单逾期总数")
        private Integer onlineExpiredWorkSheetNumber;

        /**
         * 产品谱系在制工序列表
         */
        @Schema(description = "产品谱系在制工序/工序组列表")
        private List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList;

        /**
         * 产品谱系工单在制工序列表
         */
        @Schema(description = "产品谱系工单在制工序/工序组列表")
        private List<WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDtoList;

        public String getCode() {
            return code;
        }

        public PedigreeStepOnlineStatisticsDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public PedigreeStepOnlineStatisticsDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getSpecification() {
            return specification;
        }

        public PedigreeStepOnlineStatisticsDTO setSpecification(String specification) {
            this.specification = specification;
            return this;
        }

        public Integer getOnlineWorkSheetNumber() {
            return onlineWorkSheetNumber;
        }

        public PedigreeStepOnlineStatisticsDTO setOnlineWorkSheetNumber(Integer onlineWorkSheetNumber) {
            this.onlineWorkSheetNumber = onlineWorkSheetNumber;
            return this;
        }

        public Integer getOnlineExpiredWorkSheetNumber() {
            return onlineExpiredWorkSheetNumber;
        }

        public PedigreeStepOnlineStatisticsDTO setOnlineExpiredWorkSheetNumber(Integer onlineExpiredWorkSheetNumber) {
            this.onlineExpiredWorkSheetNumber = onlineExpiredWorkSheetNumber;
            return this;
        }

        public List<StepOnlineStatisticsDTO> getStepOnlineStatisticsDtoList() {
            return stepOnlineStatisticsDtoList;
        }

        public PedigreeStepOnlineStatisticsDTO setStepOnlineStatisticsDtoList(List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList) {
            this.stepOnlineStatisticsDtoList = stepOnlineStatisticsDtoList;
            return this;
        }

        public List<WorksheetStepOnlineStatisticsDTO> getWorksheetStepOnlineStatisticsDtoList() {
            return worksheetStepOnlineStatisticsDtoList;
        }

        public PedigreeStepOnlineStatisticsDTO setWorksheetStepOnlineStatisticsDtoList(List<WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDtoList) {
            this.worksheetStepOnlineStatisticsDtoList = worksheetStepOnlineStatisticsDtoList;
            return this;
        }

        /**
         * 工单工序生产在制DTO
         */
        @Schema(description = "工单工序生产在制DTO")
        public static class WorksheetStepOnlineStatisticsDTO{

            /**
             * 工单号
             */
            @Schema(description = "工单号")
            private String serialNumber;

            /**
             * 工单在制工序列表
             */
            @Schema(description = "工单在制工序列表")
            private List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList;

            /**
             * 子工单在制工序列表
             */
            @Schema(description = "子工单在制工序列表")
            private List<SubWorksheetStepOnlineStatisticsDTO> subWorksheetStepOnlineStatisticsDtoList;

            public String getSerialNumber() {
                return serialNumber;
            }

            public WorksheetStepOnlineStatisticsDTO setSerialNumber(String serialNumber) {
                this.serialNumber = serialNumber;
                return this;
            }

            public List<StepOnlineStatisticsDTO> getStepOnlineStatisticsDtoList() {
                return stepOnlineStatisticsDtoList;
            }

            public WorksheetStepOnlineStatisticsDTO setStepOnlineStatisticsDtoList(List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList) {
                this.stepOnlineStatisticsDtoList = stepOnlineStatisticsDtoList;
                return this;
            }

            public List<SubWorksheetStepOnlineStatisticsDTO> getSubWorksheetStepOnlineStatisticsDtoList() {
                return subWorksheetStepOnlineStatisticsDtoList;
            }

            public WorksheetStepOnlineStatisticsDTO setSubWorksheetStepOnlineStatisticsDtoList(List<SubWorksheetStepOnlineStatisticsDTO> subWorksheetStepOnlineStatisticsDtoList) {
                this.subWorksheetStepOnlineStatisticsDtoList = subWorksheetStepOnlineStatisticsDtoList;
                return this;
            }
        }

        /**
         * 子工单工序生产在制DTO
         */
        @Schema(description = "子工单工序生产在制DTO")
        public static class SubWorksheetStepOnlineStatisticsDTO{

            /**
             * 子工单号
             */
            @Schema(description = "子工单号")
            private String serialNumber;

            /**
             * 子工单在制工序列表
             */
            @Schema(description = "子工单在制工序列表")
            private List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList;

            public String getSerialNumber() {
                return serialNumber;
            }

            public SubWorksheetStepOnlineStatisticsDTO setSerialNumber(String serialNumber) {
                this.serialNumber = serialNumber;
                return this;
            }

            public List<StepOnlineStatisticsDTO> getStepOnlineStatisticsDtoList() {
                return stepOnlineStatisticsDtoList;
            }

            public SubWorksheetStepOnlineStatisticsDTO setStepOnlineStatisticsDtoList(List<StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList) {
                this.stepOnlineStatisticsDtoList = stepOnlineStatisticsDtoList;
                return this;
            }
        }
    }
}
