package net.airuima.rbase.web.rest.procedure.wearingpart.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 重置操作dto
 *
 * <AUTHOR>
 * @date 2021/7/22
 */
@Schema(description = "重置操作dto")
public class WearingPartResetHistoryDTO {

    /**
     * 易损件id
     */
    @Schema(description = "易损件id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wearingPartId;

    /**
     * 重置员工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "重置员工id")
    private Long staffId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    public WearingPartResetHistoryDTO() {
    }

    public WearingPartResetHistoryDTO(Long wearingPartId, Long staffId, String note) {
        this.wearingPartId = wearingPartId;
        this.staffId = staffId;
        this.note = note;
    }

    public Long getWearingPartId() {
        return wearingPartId;
    }

    public WearingPartResetHistoryDTO setWearingPartId(Long wearingPartId) {
        this.wearingPartId = wearingPartId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public WearingPartResetHistoryDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WearingPartResetHistoryDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
