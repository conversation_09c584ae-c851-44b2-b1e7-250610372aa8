package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualified;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良返回数据DTO
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Schema(name = "不良返回数据DTO", description = "不良返回数据DTO")
public class InspectUnqualifiedDTO implements Serializable {

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;
    /**
     * 检测项目列表
     */
    @Schema(description = "检测项目列表")
    private List<CheckItemInfo> checkItemList;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public List<CheckItemInfo> getCheckItemList() {
        return checkItemList;
    }

    public void setCheckItemList(List<CheckItemInfo> checkItemList) {
        this.checkItemList = checkItemList;
    }

    public InspectUnqualifiedDTO() {
    }

    public InspectUnqualifiedDTO(String sn, List<CheckItemInfo> checkItemList) {
        this.sn = sn;
        this.checkItemList = checkItemList;
    }

    @FetchEntity
    @Schema(name = "检测项目", description = "检测项目")
    public static class CheckItemInfo implements Serializable{
        /**
         * 检测项名称
         */
        @Schema(description = "检测项名称")
        private String name;
        /**
         * 检测项编码
         */
        @Schema(description = "检测项编码")
        private String code;
        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        /**
         * 禁用启用(0:禁用;1:启用)
         */
        @Schema(description = "禁用启用(0:禁用;1:启用)")
        private boolean isEnable;

        /**
         * 是否管控其检查结果(0:不管控；1：管控)
         */
        @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
        private boolean control;

        /**
         * 计量单位id
         */
        @Schema(description = "计量单位id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unitId;

        @Transient
        @Schema(description = "计量单位")
        @FetchField(mapUri = "/api/metering-units", serviceId = "mom", paramKey = "unitId")
        private MeteringUnitDTO unitDTO = new MeteringUnitDTO();

        /**
         * 项目类型
         */
        @Schema(description = "项目类型")
        private VarietyDTO varietyObj;

        /**
         * 检验方法:目测0/检测仪器1
         */
        @Schema(description = "检验方法:目测0/检测仪器1")
        private int inspectWay;

        /**
         * 分析方法:定性0/定量1
         */
        @Schema(description = "分析方法:定性0/定量1")
        private int analyseWay;

        /**
         * 检测仪器id
         */
        @Schema(description = "检测仪器id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long facilityId;

        @Transient
        @Schema(description = "检测仪器")
        @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
        private FacilityDTO facilityDTO = new FacilityDTO();

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String note;

        /**
         * 不良明细
         */
        @Schema(description = "不良明细")
        private InspectUnqualifiedDetailInfo inspectUnqualifiedDetail;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public void setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
        }

        public boolean getIsEnable() {
            return isEnable;
        }

        public void setIsEnable(boolean enable) {
            isEnable = enable;
        }

        public boolean getControl() {
            return control;
        }

        public void setControl(boolean control) {
            this.control = control;
        }

        public Long getUnitId() {
            return unitId;
        }

        public void setUnitId(Long unitId) {
            this.unitId = unitId;
        }

        public MeteringUnitDTO getUnitDTO() {
            return unitDTO;
        }

        public void setUnitDTO(MeteringUnitDTO unitDTO) {
            this.unitDTO = unitDTO;
        }

        public VarietyDTO getVarietyObj() {
            return varietyObj;
        }

        public void setVarietyObj(VarietyDTO varietyObj) {
            this.varietyObj = varietyObj;
        }

        public int getInspectWay() {
            return inspectWay;
        }

        public void setInspectWay(int inspectWay) {
            this.inspectWay = inspectWay;
        }

        public int getAnalyseWay() {
            return analyseWay;
        }

        public void setAnalyseWay(int analyseWay) {
            this.analyseWay = analyseWay;
        }

        public Long getFacilityId() {
            return facilityId;
        }

        public void setFacilityId(Long facilityId) {
            this.facilityId = facilityId;
        }

        public FacilityDTO getFacilityDTO() {
            return facilityDTO;
        }

        public void setFacilityDTO(FacilityDTO facilityDTO) {
            this.facilityDTO = facilityDTO;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public InspectUnqualifiedDetailInfo getInspectUnqualifiedDetail() {
            return inspectUnqualifiedDetail;
        }

        public void setInspectUnqualifiedDetail(InspectUnqualifiedDetailInfo inspectUnqualifiedDetail) {
            this.inspectUnqualifiedDetail = inspectUnqualifiedDetail;
        }
    }

    @Schema(name = "不良明细", description = "不良明细")
    public static class InspectUnqualifiedDetailInfo implements Serializable{
        /**
         * 不良品管理记录id
         */
        @Schema(description = "不良品管理记录id", required = true)
        private InspectUnqualified inspectUnqualified;

        /**
         * sn
         */
        @Schema(description = "sn")
        private String sn;

        /**
         * 是否为虚拟SN(0:否;1:是)
         */
        @Schema(description = "是否为虚拟SN(0:否;1:是)")
        private boolean virtual;

        /**
         * 不良项目id
         */
        @Schema(description = "不良项目id")
        private UnqualifiedItem unqualifiedItem;

        /**
         * 缺陷原因id
         */
        @Schema(description = "缺陷原因id")
        private DefectDTO defect;

        /**
         * 检验数据
         */
        @Schema(description = "检验数据")
        private String checkData;

        /**
         * 处理结果:0不合格，1合格
         */
        @Schema(description = "处理结果:0不合格，1合格", required = true)
        private boolean checkResult;

        public InspectUnqualified getInspectUnqualified() {
            return inspectUnqualified;
        }

        public void setInspectUnqualified(InspectUnqualified inspectUnqualified) {
            this.inspectUnqualified = inspectUnqualified;
        }

        public DefectDTO getDefect() {
            return defect;
        }

        public void setDefect(DefectDTO defect) {
            this.defect = defect;
        }

        public String getCheckData() {
            return checkData;
        }

        public void setCheckData(String checkData) {
            this.checkData = checkData;
        }

        public boolean getCheckResult() {
            return checkResult;
        }

        public void setCheckResult(boolean checkResult) {
            this.checkResult = checkResult;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public boolean getVirtual() {
            return virtual;
        }

        public void setVirtual(boolean virtual) {
            this.virtual = virtual;
        }

        public UnqualifiedItem getUnqualifiedItem() {
            return unqualifiedItem;
        }

        public void setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
            this.unqualifiedItem = unqualifiedItem;
        }
    }
}
