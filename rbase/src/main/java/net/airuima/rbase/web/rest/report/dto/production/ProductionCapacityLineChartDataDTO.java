package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量按产品型号统计查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量按产品型号统计查询结果DTO")
public class ProductionCapacityLineChartDataDTO {

    /**
     * 产品型号名称
     */
    @Schema(description = "产品型号名称")
    private String name;

    /**
     * y轴产量
     */
    @Schema(description = "y轴产量")
    private Long value;

    /**
     * x轴时间
     */
    @Schema(description = "x轴时间")
    private String time;

    public String getName() {
        return name;
    }

    public ProductionCapacityLineChartDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getValue() {
        return value;
    }

    public ProductionCapacityLineChartDataDTO setValue(Long value) {
        this.value = value;
        return this;
    }

    public String getTime() {
        return time;
    }

    public ProductionCapacityLineChartDataDTO setTime(String time) {
        this.time = time;
        return this;
    }

    public ProductionCapacityLineChartDataDTO() {
    }

    public ProductionCapacityLineChartDataDTO(String name, Long value, Object time) {
        this.name = name;
        this.value = value;
        this.time = Objects.isNull(time)? StringUtils.EMPTY:String.valueOf(time);
    }

}
