package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量按部门统计查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量按部门统计查询结果DTO")
public class ProductionCapacityOrganizationDTO {

    /**
     * 部门名字
     */
    @Schema(description = "部门名字")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;


    public String getName() {
        return name;
    }

    public ProductionCapacityOrganizationDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityOrganizationDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public ProductionCapacityOrganizationDTO() {
    }

    public ProductionCapacityOrganizationDTO(String name, Long number) {
        this.name = name;
        this.number = number;
    }

}
