package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.rbase.service.procedure.quality.CheckHistoryDetailService;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史明细Resource
 * <AUTHOR>
 * @date 2021-03-22
 */
@Tag(name = "检测历史明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/check-history-details")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC || PQC || FQC || LQC")
public class CheckHistoryDetailResource extends BaseResource<CheckHistoryDetail> {

    private static final String MODULE = "检测历史明细";
    private final CheckHistoryDetailService checkHistoryDetailService;

    public CheckHistoryDetailResource(CheckHistoryDetailService checkHistoryDetailService) {
        this.checkHistoryDetailService = checkHistoryDetailService;
        this.mapUri = "/api/check-history-details";
    }

    /**
     * 通过检测历史ID获取明细数据
     * <AUTHOR>
     * @param historyId  检测历史ID
     * @return List<CheckHistoryDetail>
     * @date 2021-03-23
     **/
    @Operation(summary = "通过检测历史ID获取明细数据列表")
    @GetMapping("/byHistoryId/{historyId}")
    public ResponseEntity<ResponseData<List<CheckHistoryDetail>>> byHistoryId(@PathVariable("historyId") Long historyId){
        return ResponseData.ok(checkHistoryDetailService.findByHistoryId(historyId));
    }
}
