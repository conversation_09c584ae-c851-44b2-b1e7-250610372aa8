package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单统计报表表格明细DTO
 *
 * <AUTHOR>
 * @date 2023/06/30
 */
@Schema(description = "工单统计报表统计明细DTO")
public class WorkSheetStatisticReportTableItemDTO {

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "1")
    private String workSheetSerialNumber;


    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "2")
    private String subWorkSheetSerialNumber;

    /**
     * 工单状态 3生产完成 5 异常结单
     */
    @Schema(description = "工单状态(-2:已取消;-1:审批中;0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)")
    @Excel(name = "工单状态", orderNum = "3", replace = {"已取消_-2", "审批中_-1", "已下单_0", "投产中_1", "已暂停_2", "已完成_3", "正常结单_4", "异常结单_5"})
    private Integer status;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "4")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "5")
    private String pedigreeName;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Excel(name = "规格型号", orderNum = "6")
    private String specification;

    /**
     * 生产部门
     */
    @Schema(description = "生产部门")
    @Excel(name = "生产部门", orderNum = "7")
    private String organizationName;

    /**
     * 生产线名字
     */
    @Schema(description = "生产线名字")
    @Excel(name = "生产线名字", orderNum = "8")
    private String workLineName;


    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数", orderNum = "9")
    private Long number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "10")
    private Long qualifiedNumber;


    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Excel(name = "不合格数", orderNum = "11")
    private Long unqualifiedNumber;


    /**
     * 计划开工时间
     */
    @Schema(description = "计划开工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划开工时间", orderNum = "12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartDate;

    /**
     * 计划完工时间
     */
    @Schema(description = "计划完工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划完工时间", orderNum = "13", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndDate;


    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public WorkSheetStatisticReportTableItemDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public WorkSheetStatisticReportTableItemDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public WorkSheetStatisticReportTableItemDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public WorkSheetStatisticReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public WorkSheetStatisticReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public WorkSheetStatisticReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public WorkSheetStatisticReportTableItemDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkSheetStatisticReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkSheetStatisticReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetStatisticReportTableItemDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public WorkSheetStatisticReportTableItemDTO setUnqualifiedNumber(Long unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkSheetStatisticReportTableItemDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheetStatisticReportTableItemDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }
}
