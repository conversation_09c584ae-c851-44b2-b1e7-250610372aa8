package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "数字驾驶仓内部DTO")
public class DigitalWorkshopBaseDTO {

    /**
     * 组织架构id
     */
    @Schema(description = "组织架构id")
    private Long organizationId;

    /**
     * 生产线id
     */
    @Schema(description = "生产线id")
    private Long workLineId;

    public DigitalWorkshopBaseDTO() {
    }


    public Long getOrganizationId() {
        return organizationId;
    }

    public DigitalWorkshopBaseDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public DigitalWorkshopBaseDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }
}
