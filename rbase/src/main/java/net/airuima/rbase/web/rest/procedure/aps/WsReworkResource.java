package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.dto.aps.WsReworkDetailDTO;
import net.airuima.rbase.dto.batch.ReworkSaveSubWsUnqualifiedGroupDTO;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WsReworkService;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO;
import net.airuima.rbase.service.procedure.aps.plugin.IWsReworkService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修单关联正常单Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "返工单关联关系Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-reworks")
@AuthorityRegion("生产工单")
public class WsReworkResource extends ProtectBaseResource<WsRework> {

    private final WsReworkService wsReworkService;
    private final CommonService commonService;
    private final SubWorkSheetService subWorkSheetService;
    @Autowired
    private IWsReworkService[] wsReworkServices;

    public WsReworkResource(WsReworkService wsReworkService, CommonService commonService, SubWorkSheetService subWorkSheetService) {
        this.wsReworkService = wsReworkService;
        this.commonService = commonService;
        this.subWorkSheetService = subWorkSheetService;
        this.mapUri = "/api/ws-reworks";
    }

    /**
     * 通过总工单以及不良组别相关信息生成在线返修单
     * <AUTHOR>
     * @param reworkSaveSubWsUnqualifiedGroupDto     请求参数
     * @return ResponseEntity<ResponseData<List<WorkSheet>>>
     * @date 2021-05-07
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过总工单以及不良组别相关信息生成在线返修单")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<List<WorkSheet>>> createCustom(@RequestBody ReworkSaveSubWsUnqualifiedGroupDTO reworkSaveSubWsUnqualifiedGroupDto){
        try{
            WorkSheetResDTO workSheetResDto = wsReworkService.saveInstance(reworkSaveSubWsUnqualifiedGroupDto);
            if (Constants.KO.equals(workSheetResDto.getStatus())){
                return ResponseData.error("exception",workSheetResDto.getMessage());
            }
            List<WorkSheet> workSheetList = workSheetResDto.getWorkSheetList();
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheetList.get(Constants.INT_ZERO).getPedigree());
            workSheetList.forEach(workSheet -> subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(),workSheet.getPlanEndDate(),
                    null != pedigreeConfig && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO ? pedigreeConfig.getSplitNumber() : Constants.INT_TWO_HUNDRED,Constants.TRUE));
            //推送返工单信息erp
            wsReworkServices[0].syncReworkSheetToErp(workSheetResDto.getWsReworkList());
            return ResponseData.ok(workSheetList);
        }catch (ResponseException e){
            e.printStackTrace();
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过返工单主键ID查询生成该返工单的不良组别明细
     *
     * @param reworkWorkSheetId 返工单ID
     * @return : ResponseEntity<ResponseData<WsReworkDetailDTO>>
     * <AUTHOR>
     * @date 2023/1/12
     **/
    @Operation(summary = "通过返工单主键ID查询生成该返工单的不良组别明细",parameters = {
            @Parameter(name = "reworkWorkSheetId",description = "返工单主键ID",required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/unqualified-item-groups/{reworkWorkSheetId}")
    public ResponseEntity<ResponseData<WsReworkDetailDTO>> queryByReworkWorkSheetId(@PathVariable("reworkWorkSheetId") Long reworkWorkSheetId) {
        try {
            return ResponseData.ok(wsReworkService.queryByReworkWorkSheetId(reworkWorkSheetId));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority,"返工单");
    }

}
