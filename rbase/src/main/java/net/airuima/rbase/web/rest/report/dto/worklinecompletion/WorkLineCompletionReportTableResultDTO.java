package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线达成表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Schema(description = "产线达成表格数据DTO")
public class WorkLineCompletionReportTableResultDTO extends PageDTO {

    /**
     * 产线达成表格明细集合
     */
    @Schema(description = "产线达成表格明细集合")
    private List<WorkLineCompletionReportTableItemDTO> workLineCompletionReportTableItemList;


    public List<WorkLineCompletionReportTableItemDTO> getWorkLineCompletionReportTableItemList() {
        return workLineCompletionReportTableItemList;
    }

    public WorkLineCompletionReportTableResultDTO setWorkLineCompletionReportTableItemList(List<WorkLineCompletionReportTableItemDTO> workLineCompletionReportTableItemList) {
        this.workLineCompletionReportTableItemList = workLineCompletionReportTableItemList;
        return this;
    }
}
