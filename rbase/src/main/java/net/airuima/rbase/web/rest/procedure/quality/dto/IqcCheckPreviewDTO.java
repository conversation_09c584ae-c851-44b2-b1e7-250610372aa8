package net.airuima.rbase.web.rest.procedure.quality.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验预览DTO
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Schema(name = "IqcCheckPreviewDTO", description = "来料检验预览DTO")
public class IqcCheckPreviewDTO {

    /**
     * 来料检验
     */
    @Schema(description = "来料检验")
    private IqcCheckHistory iqcCheckHistory;

    /**
     * 来料检验详情
     */
    @Schema(description = "来料检验详情")
    private List<IqcCheckHistoryDetail> detailList;

    /**
     * 质检方案列表
     */
    @Schema(description = "质检方案列表")
    private List<PedigreeStepCheckRule> pedigreeStepCheckRuleList;


    public List<PedigreeStepCheckRule> getPedigreeStepCheckRuleList() {
        return pedigreeStepCheckRuleList;
    }

    public IqcCheckPreviewDTO setPedigreeStepCheckRuleList(List<PedigreeStepCheckRule> pedigreeStepCheckRuleList) {
        this.pedigreeStepCheckRuleList = pedigreeStepCheckRuleList;
        return this;
    }

    public IqcCheckHistory getIqcCheckHistory() {
        return iqcCheckHistory;
    }

    public IqcCheckPreviewDTO setIqcCheckHistory(IqcCheckHistory iqcCheckHistory) {
        this.iqcCheckHistory = iqcCheckHistory;
        return this;
    }

    public List<IqcCheckHistoryDetail> getDetailList() {
        return detailList;
    }

    public IqcCheckPreviewDTO setDetailList(List<IqcCheckHistoryDetail> detailList) {
        this.detailList = detailList;
        return this;
    }
}
