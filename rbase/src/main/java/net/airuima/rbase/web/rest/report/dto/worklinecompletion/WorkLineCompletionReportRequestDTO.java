package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.LocalDate;

@Schema(description = "产线达成请求参数DTO")
public class WorkLineCompletionReportRequestDTO extends PageDTO {
    /**
     * 计划时间类型
     */
    @Schema(description = "计划时间类型 0今天 1本周 2本月")
    private Integer planFinishTimeCategory;

    /**
     * 产品谱系id(最小层级)
     */
    @Schema(description = "产品谱系id(最小层级)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 产线id
     */
    @Schema(description = "产线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;


    /**
     * 开始时间
     */
    @Schema(description = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @Schema(description = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 是否导出
     */
    @Schema(description = "是否导出 true导出 false不导出")
    private Boolean exportStatus;

    /**
     * 报表类型
     */
    @Schema(description = "报表类型 0 工单 1子工单")
    private Integer reportType;


    public Integer getPlanFinishTimeCategory() {
        return planFinishTimeCategory;
    }

    public WorkLineCompletionReportRequestDTO setPlanFinishTimeCategory(Integer planFinishTimeCategory) {
        this.planFinishTimeCategory = planFinishTimeCategory;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public WorkLineCompletionReportRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public WorkLineCompletionReportRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public WorkLineCompletionReportRequestDTO setStartDate(LocalDate startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public WorkLineCompletionReportRequestDTO setEndDate(LocalDate endDate) {
        this.endDate = endDate;
        return this;
    }

    public Boolean getExportStatus() {
        return exportStatus;
    }

    public WorkLineCompletionReportRequestDTO setExportStatus(Boolean exportStatus) {
        this.exportStatus = exportStatus;
        return this;
    }

    public Integer getReportType() {
        return reportType;
    }

    public WorkLineCompletionReportRequestDTO setReportType(Integer reportType) {
        this.reportType = reportType;
        return this;
    }
}
