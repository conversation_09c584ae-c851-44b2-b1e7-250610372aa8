package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线达成图表数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线达成图表数据DTO")
public class WorkLineCompletionReportChartResultDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    /**
     * 达成率
     */
    @Schema(description = "达成率")
    private Double completionRate;

    /**
     * 产线达成情况条形图数据
     */
    @Schema(description = "产线达成情况条形图数据")
    private List<WorkLineCompletionInfoDTO> workLineCompletionInfoList;

    /**
     * 产线达成率折线图数据
     */
    @Schema(description = "产线达成率折线图数据")
    private List<WorkLineCompletionRateInfoDTO> workLineCompletionRateList;

    public List<WorkLineCompletionInfoDTO> getWorkLineCompletionInfoList() {
        return workLineCompletionInfoList;
    }

    public WorkLineCompletionReportChartResultDTO setWorkLineCompletionInfoList(List<WorkLineCompletionInfoDTO> workLineCompletionInfoList) {
        this.workLineCompletionInfoList = workLineCompletionInfoList;
        return this;
    }

    public List<WorkLineCompletionRateInfoDTO> getWorkLineCompletionRateList() {
        return workLineCompletionRateList;
    }

    public WorkLineCompletionReportChartResultDTO setWorkLineCompletionRateList(List<WorkLineCompletionRateInfoDTO> workLineCompletionRateList) {
        this.workLineCompletionRateList = workLineCompletionRateList;
        return this;
    }

    public Long getPlanNumber() {
        return planNumber;
    }

    public WorkLineCompletionReportChartResultDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public WorkLineCompletionReportChartResultDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public Double getCompletionRate() {
        return completionRate;
    }

    public WorkLineCompletionReportChartResultDTO setCompletionRate(Double completionRate) {
        this.completionRate = completionRate;
        return this;
    }
}
