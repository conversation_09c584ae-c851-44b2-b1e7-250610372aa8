package net.airuima.rbase.web.rest.procedure.aps.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划提交参数DTO
 *
 * <AUTHOR>
 * @date 2023/07/04
 */
@Schema(description = "生产计划提交参数DTO")
public class ProductionPlanDTO {

    /**
     * 生产计划
     */
    @Schema(description = "生产计划 新增时不传 修改时传")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 工序组别
     */

    @Schema(description = "工序组别")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepGroupId;

    /**
     * 产品谱系
     */
    @Schema(description = "产品谱系")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 计划粒度(0:工序组,1:生产线)
     */
    @NotNull
    @Schema(description = "计划粒度(0:工序组,1:生产线)")
    private Integer category;


    /**
     * 计划状态(0:待确认;1:已确认)
     */
    @Schema(description = "计划状态(0:待确认;1:已确认)")
    private Boolean status;

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Integer planNumber;


    /**
     * 计划日期
     */
    @Schema(description = "计划日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate planDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    public Long getId() {
        return id;
    }

    public ProductionPlanDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public ProductionPlanDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public Long getStepGroupId() {
        return stepGroupId;
    }

    public ProductionPlanDTO setStepGroupId(Long stepGroupId) {
        this.stepGroupId = stepGroupId;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public ProductionPlanDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ProductionPlanDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Boolean getStatus() {
        return status;
    }

    public ProductionPlanDTO setStatus(Boolean status) {
        this.status = status;
        return this;
    }

    public Integer getPlanNumber() {
        return planNumber;
    }

    public ProductionPlanDTO setPlanNumber(Integer planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public LocalDate getPlanDate() {
        return planDate;
    }

    public ProductionPlanDTO setPlanDate(LocalDate planDate) {
        this.planDate = planDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ProductionPlanDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
