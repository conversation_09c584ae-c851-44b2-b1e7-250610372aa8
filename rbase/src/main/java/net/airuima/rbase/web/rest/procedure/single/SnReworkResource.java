package net.airuima.rbase.web.rest.procedure.single;

import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.service.procedure.single.SnReworkService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 容器在线返修单SN关联Resource
 *
 * <AUTHOR>
 * @date 2021-01-11
 */

@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-reworks")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("Container && Single")
public class SnReworkResource extends BaseResource<SnRework> {

    private final SnReworkService snReworkService;

    public SnReworkResource(SnReworkService snReworkService) {
        this.snReworkService = snReworkService;
        this.mapUri = "/api/sn-reworks";
    }
}
