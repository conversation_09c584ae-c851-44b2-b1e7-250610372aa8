package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import java.util.List;

/**
 * 订单列表DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class OrderListDTO {

    /**
     * 业务单号
     */
    private String code;

    /**
     * 0, "销售订单"
     * 1, "工单"
     * 2, "子工单"
     * 3, "容器"
     * 4, "sn"
     * 5, "物料"
     * 6, "设备"
     * 7, "易损件"
     * 8, "人员"
     * 9, "返工单"
     */
    private Integer type;

    /**
     * 单号下层级
     */
    private List<OrderListDTO> orderListDTOList;

    public String getCode() {
        return code;
    }

    public OrderListDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public OrderListDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public List<OrderListDTO> getOrderListDTOList() {
        return orderListDTOList;
    }

    public OrderListDTO setOrderListDTOList(List<OrderListDTO> orderListDTOList) {
        this.orderListDTOList = orderListDTOList;
        return this;
    }
}
