package net.airuima.rbase.web.rest.report.dto.onlinestatistics;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Schema(description = "产品生产在制看板查询参数DT")
public class OnlineProductStatisticsRequestDTO {

    /**
     * 组织架构ID
     */
    @Schema(description = "组织架构ID")
    private Long organizationId;

    /**
     * 产品谱系ID
     */
    @Schema(description = "产品谱系ID")
    private Long pedigreeId;

    /**
     * 生产线ID
     */
    @Schema(description = "生产线ID")
    private Long workLineId;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long workSheetId;

    /**
     * 查询粒度(0:工序;1:工序组)
     */
    @Schema(description = "查询粒度(0:工序;1:工序组)")
    private Integer category;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public OnlineProductStatisticsRequestDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public OnlineProductStatisticsRequestDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public OnlineProductStatisticsRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public OnlineProductStatisticsRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public OnlineProductStatisticsRequestDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }
}
