package net.airuima.rbase.web.rest.procedure.single;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility;
import net.airuima.rbase.service.procedure.single.SnWorkDetailFacilityService;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情设备Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "单支生产详情设备Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-work-detail-facilities")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Single && FBase")
@AuthSkip("ICUD")
public class SnWorkDetailFacilityResource extends ProtectBaseResource<SnWorkDetailFacility> {

    private final SnWorkDetailFacilityService snWorkDetailFacilityService;

    public SnWorkDetailFacilityResource(SnWorkDetailFacilityService snWorkDetailFacilityService) {
        this.snWorkDetailFacilityService = snWorkDetailFacilityService;
        this.mapUri = "/api/sn-work-detail-equipments";
    }

    /**
     * @throws
     * @description 根据SN工作详情ID获取详情设备信息
     * <AUTHOR>
     * @param: snWorkDetailId SN工作详情ID
     * @updateTime 2020/12/22 19:08
     * @return: java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailEquipment>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "根据SN工作详情ID获取详情设备信息")
    @GetMapping("/bySnWorkDetailId/{snWorkDetailId}")
    public ResponseEntity<ResponseData<List<SnWorkDetailFacility>>> findBySnWorkDetailId(@PathVariable(value = "snWorkDetailId") Long snWorkDetailId) {

        return ResponseData.ok(Collections.emptyList());
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览单支生产设备详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建单支生产设备详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改单支生产设备详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除单支生产设备详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出单支生产设备详情";
        }
        return "";
    }

}
