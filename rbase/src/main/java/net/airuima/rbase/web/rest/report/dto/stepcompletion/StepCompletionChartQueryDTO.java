package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组产出值分组统计DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序组产出值分组统计DTO")
public class StepCompletionChartQueryDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    /**
     * 工序组名字
     */
    @Schema(description = "工序组名字")
    private String name;

    public Long getPlanNumber() {
        return planNumber;
    }

    public StepCompletionChartQueryDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public StepCompletionChartQueryDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public String getName() {
        return name;
    }

    public StepCompletionChartQueryDTO setName(String name) {
        this.name = name;
        return this;
    }

    public StepCompletionChartQueryDTO() {
    }

    public StepCompletionChartQueryDTO(String name, Long planNumber, Long actualNumber) {
        this.name = name;
        this.planNumber = planNumber;
        this.actualNumber = actualNumber;

    }
}
