package net.airuima.rbase.web.rest.report.dto.perform;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计报表表格明细数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
@Schema(description = "报工统计报表表格明细数据DTO")
public class StaffPerformStatisticReportTableItemDTO {


    /**
     * 报工时间
     */
    @Schema(description = "报工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报工时间", orderNum = "1", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordDateTime;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "2")
    private String workSheetSerialNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "3")
    private String subWorkSheetSerialNumber;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "4")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "5")
    private String pedigreeName;


    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Excel(name = "规格型号", orderNum = "6")
    private String specification;

    /**
     * 生产线名字
     */
    @Schema(description = "生产线名字")
    @Excel(name = "生产线名字", orderNum = "7")
    private String workLineName;

    /**
     * 员工名字
     */
    @Schema(description = "员工名字")
    @Excel(name = "员工名字", orderNum = "8")
    private String staffName;


    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    @Excel(name = "员工编码", orderNum = "9")
    private String staffCode;


    /**
     * 工位名字
     */
    @Schema(description = "工位名字")
    @Excel(name = "工位名字", orderNum = "10")
    private String workCellName;


    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    @Excel(name = "工位编码", orderNum = "11")
    private String workCellCode;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工位编码", orderNum = "12")
    private String stepCode;

    /**
     * 工序名字
     */
    @Schema(description = "工序名字")
    @Excel(name = "工位名字", orderNum = "13")
    private String stepName;


    /**
     * 完成数
     */
    @Schema(description = "完成数")
    @Excel(name = "完成数", orderNum = "14")
    private Long number;


    public LocalDateTime getRecordDateTime() {
        return recordDateTime;
    }

    public StaffPerformStatisticReportTableItemDTO setRecordDateTime(LocalDateTime recordDateTime) {
        this.recordDateTime = recordDateTime;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public StaffPerformStatisticReportTableItemDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public StaffPerformStatisticReportTableItemDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public StaffPerformStatisticReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public StaffPerformStatisticReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public StaffPerformStatisticReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public StaffPerformStatisticReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public StaffPerformStatisticReportTableItemDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public StaffPerformStatisticReportTableItemDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public StaffPerformStatisticReportTableItemDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public StaffPerformStatisticReportTableItemDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public StaffPerformStatisticReportTableItemDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public StaffPerformStatisticReportTableItemDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StaffPerformStatisticReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }
}
