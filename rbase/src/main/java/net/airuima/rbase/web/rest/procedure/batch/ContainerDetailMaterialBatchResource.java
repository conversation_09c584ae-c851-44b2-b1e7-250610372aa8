package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailMaterialBatch;
import net.airuima.rbase.service.procedure.batch.ContainerDetailMaterialBatchService;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情物料批次表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器工作详情物料批次表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/container-detail-material-batches")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Container && WorksheetMaterial")
@AuthSkip("ICUD")
public class ContainerDetailMaterialBatchResource extends ProtectBaseResource<ContainerDetailMaterialBatch> {

    private final ContainerDetailMaterialBatchService containerDetailMaterialBatchService;

    public ContainerDetailMaterialBatchResource(ContainerDetailMaterialBatchService containerDetailMaterialBatchService) {
        this.containerDetailMaterialBatchService = containerDetailMaterialBatchService;
        this.mapUri = "/api/container-detail-material-batches";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览容器生产物料批次";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建容器生产物料批次";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改容器生产物料批次";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除容器生产物料批次";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出容器生产物料批次";
        }
        return "";
    }

}
