package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistoryDetail;
import net.airuima.rbase.service.procedure.quality.PedigreeStepInspectionHistoryDetailService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工序检查配置历史详情Resource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "工序检查配置历史详情Resource")
@AppKey("RmesService")
@FuncInterceptor("StepPreCheck")
@AuthorityRegion("生产质量数据")
@RestController
@RequestMapping("/api/pedigree-step-inspection-history-details")
@AuthSkip("CUID")
public class PedigreeStepInspectionHistoryDetailResource extends ProtectBaseResource<PedigreeStepInspectionHistoryDetail> {

    private final PedigreeStepInspectionHistoryDetailService pedigreeStepInspectionHistoryDetailService;

    public PedigreeStepInspectionHistoryDetailResource(PedigreeStepInspectionHistoryDetailService pedigreeStepInspectionHistoryDetailService) {
        this.pedigreeStepInspectionHistoryDetailService = pedigreeStepInspectionHistoryDetailService;
        this.mapUri = "/api/pedigree-step-inspection-history-details";
    }

    /**
     * 通过工序检查配置历史id获取检查详情
     *
     * @param id 检查历史id'
     * @return List<PedigreeStepInspectionHistoryDetail>
     * <AUTHOR>
     * @since 1.8.1
     */
    @GetMapping("/historyId")
    public ResponseEntity<ResponseData<List<PedigreeStepInspectionHistoryDetail>>> getHistoriesDetailId(@RequestParam(name = "id") Long id) {
        try{
            return ResponseData.ok(pedigreeStepInspectionHistoryDetailService.getHistoriesDetailId(id));
        }catch (ResponseException r){
            return ResponseData.error(r);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "工序检查历史记录详情");
    }
}
