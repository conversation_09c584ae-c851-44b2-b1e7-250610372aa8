package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 易损件履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class WearingPartHistoryDTO {

    /**
     * 易损件
     */
    private WearingPart wearingPart;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,创建
         * 1,使用
         * 2,重置
         * 3,报废
         */
        private Integer type;

        /**
         * 使用
         */
        private BatchWorkDetailWearingPart batchWorkDetailWearingPart;

        /**
         * 重置
         */
        private WearingPartResetHistory wearingPartResetHistory;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public BatchWorkDetailWearingPart getBatchWorkDetailWearingPart() {
            return batchWorkDetailWearingPart;
        }

        public HistoryDTO setBatchWorkDetailWearingPart(BatchWorkDetailWearingPart batchWorkDetailWearingPart) {
            this.batchWorkDetailWearingPart = batchWorkDetailWearingPart;
            return this;
        }

        public WearingPartResetHistory getWearingPartResetHistory() {
            return wearingPartResetHistory;
        }

        public HistoryDTO setWearingPartResetHistory(WearingPartResetHistory wearingPartResetHistory) {
            this.wearingPartResetHistory = wearingPartResetHistory;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }
    }

    public WearingPart getWearingPart() {
        return wearingPart;
    }

    public WearingPartHistoryDTO setWearingPart(WearingPart wearingPart) {
        this.wearingPart = wearingPart;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public WearingPartHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
