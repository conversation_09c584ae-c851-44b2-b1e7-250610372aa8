package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司 质量报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Schema(name = "生产过程正向追溯数据明细DTO")
public class ForwardTraceStepDetailDTO  implements Serializable {
    /**
     * 工序名称
     */
    @Schema(description = "工序名称",type = "string")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码",type = "string")
    private String stepCode;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序生产开始时间",type = "datetime",format = "date-time",example = "2024-01-30 08:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startDate;

    /**
     * 工序结束时间
     */
    @Schema(description = "工序生产完成时间",type = "datetime",format = "date-time",example = "2024-01-30 08:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endDate;

    /**
     * 投产数
     */
    @Schema(description = "工序投产数",type = "integer",format = "int32")
    private Integer inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "工序合格数",type = "integer",format = "int32")
    private Integer qualifiedNumber;

    /**
     * 工序不合格数
     */
    @Schema(description = "工序不合格数",type = "integer")
    private Integer unqualifiedNumber;

    /**
     * 合格率
     */
    @Schema(description = "工序合格率",type = "number",format = "double")
    private BigDecimal qualifiedRate;

    /**
     * 工序耗时
     */
    @Schema(description = "工序生产耗时",type = "number",format = "double")
    private BigDecimal workHour;


    @Schema(description = "预留字段1")
    private String custom1;

    @Schema(description = "预留字段2")
    private String custom2;


    @Schema(description = "预留字段3")
    private String custom3;


    @Schema(description = "预留字段4")
    private String custom4;


    @Schema(description = "预留字段5")
    private String custom5;

    /**
     * 数据单元
     **/
    @Schema(description = "数据单元（批量：properties/容器：name、code、properties/容器SN：name、code、snProperties/SN：sn、properties）")
    private List<DateUnit> dataUnitList;

    public String getStepName() {
        return stepName;
    }

    public ForwardTraceStepDetailDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public ForwardTraceStepDetailDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public ForwardTraceStepDetailDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public ForwardTraceStepDetailDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public ForwardTraceStepDetailDTO setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ForwardTraceStepDetailDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public BigDecimal getQualifiedRate() {
        return qualifiedRate;
    }

    public ForwardTraceStepDetailDTO setQualifiedRate(BigDecimal qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public ForwardTraceStepDetailDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public BigDecimal getWorkHour() {
        return workHour;
    }

    public ForwardTraceStepDetailDTO setWorkHour(BigDecimal workHour) {
        this.workHour = workHour;
        return this;
    }

    public String getCustom1() {
        return custom1;
    }

    public ForwardTraceStepDetailDTO setCustom1(String custom1) {
        this.custom1 = custom1;
        return this;
    }

    public String getCustom2() {
        return custom2;
    }

    public ForwardTraceStepDetailDTO setCustom2(String custom2) {
        this.custom2 = custom2;
        return this;
    }

    public String getCustom3() {
        return custom3;
    }

    public ForwardTraceStepDetailDTO setCustom3(String custom3) {
        this.custom3 = custom3;
        return this;
    }

    public String getCustom4() {
        return custom4;
    }

    public ForwardTraceStepDetailDTO setCustom4(String custom4) {
        this.custom4 = custom4;
        return this;
    }

    public String getCustom5() {
        return custom5;
    }

    public ForwardTraceStepDetailDTO setCustom5(String custom5) {
        this.custom5 = custom5;
        return this;
    }

    public List<DateUnit> getDataUnitList() {
        return dataUnitList;
    }

    public ForwardTraceStepDetailDTO setDataUnitList(List<DateUnit> dataUnitList) {
        this.dataUnitList = dataUnitList;
        return this;
    }

    /**
     * 数据单元【批量：properties/容器：name、code、properties/容器SN：name、code、snProperties/SN：sn、properties】
     **/
    @Schema(description = "工序生产数据明细信息")
    public static class DateUnit implements Serializable {
        /**
         * 数据类型，0:批量，1:容器，2:容器SN，3:SN
         */
        @Schema(description = "数据类型",type = "integer",format = "int32",example = "0:批量，1:容器，2:容器SN，3:SN")
        private Integer category;

        /**
         * 容器名称
         */
        @Schema(description = "容器名称",type = "string")
        private String name;

        /**
         * 容器编码
         */
        @Schema(description = "容器编码",type = "string")
        private String code;

        /**
         * SN
         */
        @Schema(description = "SN",type = "string")
        private String sn;

        /**
         * 工序生产人机料汇总信息
         */
        @Schema(description = "工序生产人机料汇总信息",implementation = Properties.class)
        private Properties properties;

        /**
         * SN工序生产人机料信息列表
         */
        @ArraySchema(schema = @Schema(description = "SN工序生产人机料信息列表",implementation = SnProperties.class))
        private List<SnProperties> snProperties;

        public Integer getCategory() {
            return category;
        }

        public DateUnit setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public String getName() {
            return name;
        }

        public DateUnit setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public DateUnit setCode(String code) {
            this.code = code;
            return this;
        }

        public String getSn() {
            return sn;
        }

        public DateUnit setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public Properties getProperties() {
            return properties;
        }

        public DateUnit setProperties(Properties properties) {
            this.properties = properties;
            return this;
        }

        public List<SnProperties> getSnProperties() {
            return snProperties;
        }

        public DateUnit setSnProperties(List<SnProperties> snProperties) {
            this.snProperties = snProperties;
            return this;
        }
    }

    @Schema(name = "SN工序生产人机料信息")
    public static class SnProperties implements Serializable {
        /**
         * SN
         */
        @Schema(description = "SN")
        private String sn;

        /**
         * 属性集合
         */
        @Schema(description = "属性集合")
        private Properties properties;

        public String getSn() {
            return sn;
        }

        public SnProperties setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public Properties getProperties() {
            return properties;
        }

        public SnProperties setProperties(Properties properties) {
            this.properties = properties;
            return this;
        }
    }

    @Schema(name = "工序生产人机料汇总信息")
    public static class Properties implements Serializable {
        /**
         * 人
         **/
        @Schema(description = "生产员工信息",implementation = StaffDetailDTO.class)
        private StaffDetailDTO staffDTO;
        /**
         * 机
         **/
        @Schema(description = "生产工位及设备信息",implementation = MachineDTO.class)
        private MachineDTO machineDTO;
        /**
         * 料
         **/
        @ArraySchema(schema = @Schema(description = "生产物料信息列表",implementation = MaterialDTO.class ))
        private List<MaterialDTO> materialDTO;
        /**
         * 易
         **/
        @ArraySchema(schema = @Schema(description = "生产易损件信息列表",implementation = WearingPartDTO.class))
        private List<WearingPartDTO> wearingPartDTO;
        /**
         * 质
         **/
        @Schema(description = "生产质量信息",implementation = QualifiedDTO.class)
        private QualifiedDTO qualifiedDTO;

        public StaffDetailDTO getStaffDTO() {
            return staffDTO;
        }

        public Properties setStaffDTO(StaffDetailDTO staffDTO) {
            this.staffDTO = staffDTO;
            return this;
        }

        public MachineDTO getMachineDTO() {
            return machineDTO;
        }

        public Properties setMachineDTO(MachineDTO machineDTO) {
            this.machineDTO = machineDTO;
            return this;
        }

        public List<MaterialDTO> getMaterialDTO() {
            return materialDTO;
        }

        public Properties setMaterialDTO(List<MaterialDTO> materialDTO) {
            this.materialDTO = materialDTO;
            return this;
        }

        public List<WearingPartDTO> getWearingPartDTO() {
            return wearingPartDTO;
        }

        public Properties setWearingPartDTO(List<WearingPartDTO> wearingPartDTO) {
            this.wearingPartDTO = wearingPartDTO;
            return this;
        }

        public QualifiedDTO getQualifiedDTO() {
            return qualifiedDTO;
        }

        public Properties setQualifiedDTO(QualifiedDTO qualifiedDTO) {
            this.qualifiedDTO = qualifiedDTO;
            return this;
        }
    }

    @Schema(name = "生产员工信息")
    public static class StaffDetailDTO implements Serializable {
        /**
         * 员工名称
         */
        @Schema(description = "员工姓名",type = "string")
        private String name;

        /**
         * 员工编码
         */
        @Schema(description = "员工编码",type = "string")
        private String code;

        public String getName() {
            return name;
        }

        public StaffDetailDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StaffDetailDTO setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(name = "工位及设备信息")
    public static class MachineDTO implements Serializable {
        /**
         * 工位名称
         */
        @Schema(description = "工位名称",type = "string")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码",type = "string")
        private String workCellCode;

        /**
         * 设备集合
         */
        @ArraySchema(schema = @Schema(description = "设备列表",implementation = FacilityDetailDTO.class))
        private List<FacilityDetailDTO> facilityDTOList;

        public String getWorkCellName() {
            return workCellName;
        }

        public MachineDTO setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public MachineDTO setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public List<FacilityDetailDTO> getFacilityDTOList() {
            return facilityDTOList;
        }

        public MachineDTO setFacilityDTOList(List<FacilityDetailDTO> facilityDTOList) {
            this.facilityDTOList = facilityDTOList;
            return this;
        }

        /**
         * 设备
         **/
        @Schema(name = "设备信息")
        public static class FacilityDetailDTO implements Serializable {
            /**
             * 设备编码
             */
            @Schema(description = "设备编码",type = "string")
            private String code;

            /**
             * 设备名称
             */
            @Schema(description = "设备名称",type = "string")
            private String name;

            public String getCode() {
                return code;
            }

            public FacilityDetailDTO setCode(String code) {
                this.code = code;
                return this;
            }

            public String getName() {
                return name;
            }

            public FacilityDetailDTO setName(String name) {
                this.name = name;
                return this;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (o == null || getClass() != o.getClass()) return false;
                FacilityDetailDTO that = (FacilityDetailDTO) o;
                return Objects.equals(code, that.code) && Objects.equals(name, that.name);
            }

            @Override
            public int hashCode() {
                return Objects.hash(code, name);
            }
        }
    }

    public static class MaterialDTO implements Serializable {
        /**
         * 物料编码
         */
        @Schema(description = "物料编码",type = "string")
        private String code;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称",type = "string")
        private String name;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次",type = "string")
        private String batch;

        /**
         * 物料数量
         */
        @Schema(description = "物料数量",type = "number",format = "double")
        private BigDecimal number;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称",type = "string")
        private String supplierName;

        public String getCode() {
            return code;
        }

        public MaterialDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public MaterialDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getBatch() {
            return batch;
        }

        public MaterialDTO setBatch(String batch) {
            this.batch = batch;
            return this;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public MaterialDTO setSupplierName(String supplierName) {
            this.supplierName = supplierName;
            return this;
        }

        public BigDecimal getNumber() {
            return number;
        }

        public MaterialDTO setNumber(BigDecimal number) {
            this.number = number;
            return this;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MaterialDTO that = (MaterialDTO) o;
            return Objects.equals(code, that.code) && Objects.equals(batch, that.batch);
        }

        @Override
        public int hashCode() {
            return Objects.hash(code, batch);
        }
    }

    public static class WearingPartDTO implements Serializable {
        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称",type = "string")
        private String name;

        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码",type = "string")
        private String code;

        /**
         * 使用次数
         */
        @Schema(description = "使用次数",type = "integer",format = "int32")
        private Integer useNumber;

        /**
         * 使用时长(秒为单位)
         */
        @Schema(description = "使用时长(秒)",type = "integer",format = "int32")
        private Integer useTime;

        public String getName() {
            return name;
        }

        public WearingPartDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WearingPartDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public Integer getUseNumber() {
            return useNumber;
        }

        public WearingPartDTO setUseNumber(Integer useNumber) {
            this.useNumber = useNumber;
            return this;
        }

        public Integer getUseTime() {
            return useTime;
        }

        public WearingPartDTO setUseTime(Integer useTime) {
            this.useTime = useTime;
            return this;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            WearingPartDTO that = (WearingPartDTO) o;
            return Objects.equals(code, that.code);
        }

        @Override
        public int hashCode() {
            return Objects.hash(code);
        }
    }

    @Schema(name = "生产质量信息")
    public static class QualifiedDTO implements Serializable {
        /**
         * 投产数
         */
        @Schema(description = "投产数",type = "integer",format = "int32")
        private Integer inputNumber;

        /**
         * 合格数
         */
        @Schema(description = "合格数",type = "integer",format = "int32")
        private Integer qualifiedNumber;

        /**
         * 不合格数
         */
        @Schema(description = "不合格数",type = "integer")
        private Integer unqualifiedNumber;

        /**
         * 不良详情信息列表
         */
        @Schema(description = "不良详情信息列表")
        private List<UnqualifiedDetail> unqualifiedDetails;

        public Integer getInputNumber() {
            return inputNumber;
        }

        public QualifiedDTO setInputNumber(Integer inputNumber) {
            this.inputNumber = inputNumber;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public QualifiedDTO setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public QualifiedDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public List<UnqualifiedDetail> getUnqualifiedDetails() {
            return unqualifiedDetails;
        }

        public QualifiedDTO setUnqualifiedDetails(List<UnqualifiedDetail> unqualifiedDetails) {
            this.unqualifiedDetails = unqualifiedDetails;
            return this;
        }

        @Schema(description = "不良详情信息")
        public static class UnqualifiedDetail{
            /**
             * 不良项目名称
             */
            @Schema(description = "不良项目名称")
            private String unqualifiedItemName;
            /**
             * 不良项目编码
             */
            @Schema(description = "不良项目编码")
            private String unqualifiedItemCode;


            /**
             * 不良数量
             */
            @Schema(description = "不良数量")
            private Integer number;

            public UnqualifiedDetail() {
            }

            public UnqualifiedDetail(UnqualifiedItem unqualifiedItem) {
                this.unqualifiedItemCode = unqualifiedItem.getCode();
                this.unqualifiedItemName = unqualifiedItem.getName();
            }

            public String getUnqualifiedItemName() {
                return unqualifiedItemName;
            }

            public UnqualifiedDetail setUnqualifiedItemName(String unqualifiedItemName) {
                this.unqualifiedItemName = unqualifiedItemName;
                return this;
            }

            public String getUnqualifiedItemCode() {
                return unqualifiedItemCode;
            }

            public UnqualifiedDetail setUnqualifiedItemCode(String unqualifiedItemCode) {
                this.unqualifiedItemCode = unqualifiedItemCode;
                return this;
            }

            public Integer getNumber() {
                return number;
            }

            public UnqualifiedDetail setNumber(Integer number) {
                this.number = number;
                return this;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (o == null || getClass() != o.getClass()) return false;
                UnqualifiedDetail that = (UnqualifiedDetail) o;
                return Objects.equals(unqualifiedItemCode, that.unqualifiedItemCode);
            }

            @Override
            public int hashCode() {
                return Objects.hash(unqualifiedItemCode);
            }
        }
    }
}
