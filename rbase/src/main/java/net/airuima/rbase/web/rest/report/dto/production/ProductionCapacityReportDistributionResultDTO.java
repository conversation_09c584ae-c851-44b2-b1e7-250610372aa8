package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计分布图形查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量统计分布图形查询结果DTO")
public class ProductionCapacityReportDistributionResultDTO {

    /**
     * 产量按部门统计图形数据
     */
    @Schema(description = "产量按部门统计图形数据")
    private List<ProductionCapacityOrganizationDTO> productionCapacityOrganizationDataList;

    /**
     * 产量按产线统计图形数据
     */
    @Schema(description = "产量按产线统计图形数据")
    private List<net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO> productionCapacityWorkLineDataList;

    public List<ProductionCapacityOrganizationDTO> getProductionCapacityOrganizationDataList() {
        return productionCapacityOrganizationDataList;
    }

    public ProductionCapacityReportDistributionResultDTO setProductionCapacityOrganizationDataList(List<ProductionCapacityOrganizationDTO> productionCapacityOrganizationDataList) {
        this.productionCapacityOrganizationDataList = productionCapacityOrganizationDataList;
        return this;
    }

    public List<net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO> getProductionCapacityWorkLineDataList() {
        return productionCapacityWorkLineDataList;
    }

    public ProductionCapacityReportDistributionResultDTO setProductionCapacityWorkLineDataList(List<net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO> productionCapacityWorkLineDataList) {
        this.productionCapacityWorkLineDataList = productionCapacityWorkLineDataList;
        return this;
    }
}
