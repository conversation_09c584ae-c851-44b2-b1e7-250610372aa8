package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线达成率情况DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线达成率情况DTO")
public class WorkLineCompletionRateInfoDTO {

    /**
     * 产线名称
     */
    @Schema(description = "产线名称")
    private String name;

    /**
     * 达成率
     */
    @Schema(description = "达成率")
    private Double rate;

    public String getName() {
        return name;
    }

    public WorkLineCompletionRateInfoDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Double getRate() {
        return rate;
    }

    public WorkLineCompletionRateInfoDTO setRate(Double rate) {
        this.rate = rate;
        return this;
    }

    public WorkLineCompletionRateInfoDTO() {
    }

    public WorkLineCompletionRateInfoDTO(String name, Double rate) {
        this.name = name;
        this.rate = rate;
    }
}
