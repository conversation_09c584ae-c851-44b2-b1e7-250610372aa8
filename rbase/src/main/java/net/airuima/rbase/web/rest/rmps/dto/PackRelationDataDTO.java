package net.airuima.rbase.web.rest.rmps.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;

import java.io.Serializable;

@FetchEntity
public class PackRelationDataDTO implements Serializable {
    /**
     * 工单编码
     */
    @Schema(description = "工单编码")
    private String wsSerialNumber;


    /**
     * 子工单编码
     */
    @Schema(description = "子工单编码")
    private String subWsSerialNumber;


    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;


    /**
     * 物料编码id
     */
    @Schema(description = "物料编码id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String materialBatch;

    public PackRelationDataDTO() {
    }

    public PackRelationDataDTO(String wsSerialNumber, Long materialId, String materialBatch) {
        this.wsSerialNumber = wsSerialNumber;
        this.materialId = materialId;
        this.materialBatch = materialBatch;
    }

    public PackRelationDataDTO(String wsSerialNumber, String subWsSerialNumber, Long materialId, String materialBatch) {
        this.wsSerialNumber = wsSerialNumber;
        this.subWsSerialNumber = subWsSerialNumber;
        this.materialId = materialId;
        this.materialBatch = materialBatch;
    }

    public PackRelationDataDTO(String wsSerialNumber, Long materialId, String materialBatch,String containerCode) {
        this.wsSerialNumber = wsSerialNumber;
        this.containerCode = containerCode;
        this.materialId = materialId;
        this.materialBatch = materialBatch;
    }

    public PackRelationDataDTO(String wsSerialNumber, String subWsSerialNumber, Long materialId, String materialBatch,String containerCode) {
        this.wsSerialNumber = wsSerialNumber;
        this.subWsSerialNumber = subWsSerialNumber;
        this.containerCode = containerCode;
        this.materialId = materialId;
        this.materialBatch = materialBatch;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public PackRelationDataDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public PackRelationDataDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getWsSerialNumber() {
        return wsSerialNumber;
    }

    public PackRelationDataDTO setWsSerialNumber(String wsSerialNumber) {
        this.wsSerialNumber = wsSerialNumber;
        return this;
    }

    public String getSubWsSerialNumber() {
        return subWsSerialNumber;
    }

    public PackRelationDataDTO setSubWsSerialNumber(String subWsSerialNumber) {
        this.subWsSerialNumber = subWsSerialNumber;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public PackRelationDataDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public PackRelationDataDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getMaterialName() {
        return materialName;
    }

    public PackRelationDataDTO setMaterialName(String materialName) {
        this.materialName = materialName;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public PackRelationDataDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }
}
