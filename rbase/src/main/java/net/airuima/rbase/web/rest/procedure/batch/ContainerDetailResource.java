package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.service.procedure.batch.ContainerDetailService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.ContainerDetailDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器生产详情Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器生产详情Resource")
@RestController
@RequestMapping("/api/container-details")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Container")
@AuthSkip("IC")
public class ContainerDetailResource extends ProtectBaseResource<ContainerDetail> {

    private final ContainerDetailService containerDetailService;

    @Autowired
    private IRollbackStepService[] rollbackStepServices;

    public ContainerDetailResource(ContainerDetailService containerDetailService) {
        this.containerDetailService = containerDetailService;
        this.mapUri = "/api/container-details";
    }

    /**
     * 重写容器想起通用删除接口
     * <AUTHOR>
     * @param rollBackDto    回退参数DTO
     * @return ResponseEntity<ResponseData<Void>>
     * @date 2021-08-27
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过容器工序详情ID删除记录")
    @PostMapping ("/rollback-container")
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<List<ContainerDetail>>> rollbackContainer(@RequestBody RollBackDTO rollBackDto) {
        try{
            ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].deleteContainerDetailById(rollBackDto);
            if (Constants.KO.equals(containerDetailReplaceDto.getStatus())){
                return ResponseData.error("Rollback", containerDetailReplaceDto.getMessage());
            }else {
                return ResponseData.ok(containerDetailReplaceDto.getContainerDetailList(),"success","工序回退成功");
            }
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 更新容器工序详情对应的容器编码
     *
     * @param containerDetailId 容器工序详情ID
     * @param containerCode     容器编码
     * <AUTHOR>
     * @date 2022/8/1
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "更新容器工序详情对应的容器编码")
    @PutMapping("/containerDetailId/{containerDetailId}/containerCode/{containerCode}")
    public ResponseEntity<ResponseData<Void>> updateContainerCode(@PathVariable(value = "containerDetailId") Long containerDetailId, @PathVariable(value = "containerCode") String containerCode) {
        try {
            containerDetailService.updateContainerCode(containerDetailId, containerCode);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 获取工序容器详情
     *
     * @param serialNumber 子工单号
     * @param containerCode 容器编码
     * @return ResponseEntity
     */
    @Operation(summary = "获取工序容器详情")
    @GetMapping("/get-step-container-detail/{serialNumber}/{containerCode}")
    public ResponseEntity<ResponseData<List<ContainerDetailDTO>>> getStepContainerDetail(@PathVariable("serialNumber") String serialNumber,
                                                                                         @PathVariable("containerCode") String containerCode){
        try {
            return ResponseData.ok(containerDetailService.getStepContainerDetail(serialNumber, containerCode));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览容器生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建容器生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改容器生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "回退容器生产详情";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出容器生产详情";
        }
        return "";
    }

}
