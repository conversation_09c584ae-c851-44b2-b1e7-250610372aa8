package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Sn履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class SnHistoryDTO {

    /**
     * SN状态
     */
    private SnWorkStatus snWorkStatus;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,开始生产
         * 1,返工
         * 2,维修分析
         * 3,生产完成
         */
        private Integer type;

        /**
         * 子工单
         */
        private SubWorkSheet subWorkSheet;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 返工单
         */
        private WorkSheet workSheet;

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public SubWorkSheet getSubWorkSheet() {
            return subWorkSheet;
        }

        public HistoryDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
            this.subWorkSheet = subWorkSheet;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public WorkSheet getWorkSheet() {
            return workSheet;
        }

        public HistoryDTO setWorkSheet(WorkSheet workSheet) {
            this.workSheet = workSheet;
            return this;
        }
    }

    public SnWorkStatus getSnWorkStatus() {
        return snWorkStatus;
    }

    public SnHistoryDTO setSnWorkStatus(SnWorkStatus snWorkStatus) {
        this.snWorkStatus = snWorkStatus;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public SnHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
