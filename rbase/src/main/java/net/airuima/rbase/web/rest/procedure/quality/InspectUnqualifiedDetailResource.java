package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualifiedDetail;
import net.airuima.rbase.service.procedure.quality.InspectUnqualifiedDetailService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良管理记录详情Resource
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Tag(name = "不良管理记录详情Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/inspect-unqualified-details")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("PQC || FQC || LQC")
public class InspectUnqualifiedDetailResource extends BaseResource<InspectUnqualifiedDetail> {


    private final InspectUnqualifiedDetailService inspectUnqualifiedDetailService;

    public InspectUnqualifiedDetailResource(InspectUnqualifiedDetailService inspectUnqualifiedDetailService) {
        this.inspectUnqualifiedDetailService = inspectUnqualifiedDetailService;
        this.mapUri = "/api/inspect-unqualified-details";
    }


}
