package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.BatchWorkDetailGetDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量工序生产详情Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "批量工序生产详情Resource")
@RestController
@RequestMapping("/api/batch-work-details")
@AuthorityRegion("生产过程数据")
@AuthSkip("ICU")
public class BatchWorkDetailResource extends ProtectBaseResource<BatchWorkDetail> {


    private final BatchWorkDetailService batchWorkDetailService;
    @Autowired
    private IRollbackStepService[] rollbackStepServices;

    public BatchWorkDetailResource(BatchWorkDetailService batchWorkDetailService) {
        this.batchWorkDetailService = batchWorkDetailService;
        this.mapUri = "/api/batch-work-details";
    }

    /**
     *  子工单投产粒度时通过子工单主键ID获取工序生产过程数据
     * <AUTHOR>
     * @param subWsId 子工单ID
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工序生产详情集合
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "子工单投产粒度时通过子工单主键ID获取工序生产过程数据")
    @GetMapping("/sub-work-sheets/{subWsId}")
    public ResponseEntity<ResponseData<List<BatchWorkDetailGetDTO.SubWorkSheetBatchWorkDetailDTO>>> findBySubWorkSheetId(@PathVariable(value = "subWsId") Long subWsId) {
        return ResponseData.ok(batchWorkDetailService.findProcessStepBySubWsId(subWsId));
    }


    /**
     * @description 工单投产粒度时通过工单主键ID获取工序生产过程数据
     * @param wsId 工单ID
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工序生产详情集合
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "工单投产粒度时通过工单主键ID获取工序生产过程数据")
    @GetMapping("/work-sheets/{wsId}")
    public ResponseEntity<ResponseData<List<BatchWorkDetailGetDTO.WorkSheetBatchWorkDetailDTO>>> findByWorkSheetId(@PathVariable(value = "wsId") Long wsId) {
        return ResponseData.ok(batchWorkDetailService.findProcessStepByWsId(wsId));
    }

    /**
     * 通过批量详情ID删除记录
     *
     * @param rollBackDto 批量详情ID
     * @return ResponseEntity<ResponseData<Void>>
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过批量工序详情ID删除记录")
    @PostMapping("/rollback-step")
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<List<ContainerDetail>>> rollbackStep(@RequestBody RollBackDTO rollBackDto) {
        try {
            ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].deleteBatchWorkDetailById(rollBackDto);
            if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
                return ResponseData.error("Rollback", containerDetailReplaceDto.getMessage());
            } else {
                return ResponseData.ok(containerDetailReplaceDto.getContainerDetailList(),"success","工序回退成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 验证当前工序（容器）的上一道工序的容器是否被占用
     *
     * @param id 容器详情id或者工序详情id
     * @return List<ContainerDetail>
     * <AUTHOR>
     * @date 2022/2/22
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "验证容器占用")
    @GetMapping("/check-container-occupation/{id}")
    public ResponseEntity<ResponseData<List<ContainerDetail>>> checkContainerOccupation(@PathVariable(value = "id") Long id) {
        try {
            ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].checkContainerOccupation(id);
            if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
                return ResponseData.error("exception",containerDetailReplaceDto.getMessage());
            } else {
                return ResponseData.ok(containerDetailReplaceDto.getContainerDetailList());
            }
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览批量生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建批量生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改批量生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "回退批量生产详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出批量生产详情";
        }
        return "";
    }

}
