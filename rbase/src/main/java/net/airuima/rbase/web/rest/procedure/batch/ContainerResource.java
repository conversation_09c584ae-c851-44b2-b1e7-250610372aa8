package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.service.procedure.batch.ContainerService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/containers")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Container")
public class ContainerResource extends BaseResource<Container> {

    private final ContainerService containerService;

    public ContainerResource(ContainerService containerService) {
        this.containerService = containerService;
        this.mapUri = "/api/containers";
    }
}
