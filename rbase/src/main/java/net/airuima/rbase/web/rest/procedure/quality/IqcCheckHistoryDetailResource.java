package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail;
import net.airuima.rbase.service.procedure.quality.IqcCheckHistoryDetailService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验Resource
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Tag(name = "来料检验详情Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/iqc-check-history-details")
@AuthorityRegion("来料检验详情")
@FuncInterceptor("IQC")
public class IqcCheckHistoryDetailResource extends BaseResource<IqcCheckHistoryDetail> {


    private final IqcCheckHistoryDetailService iqcCheckHistoryDetailService;


    public IqcCheckHistoryDetailResource(IqcCheckHistoryDetailService iqcCheckHistoryDetailService) {
        this.iqcCheckHistoryDetailService = iqcCheckHistoryDetailService;
        this.mapUri = "/api/iqc-check-history-details";
    }


}
