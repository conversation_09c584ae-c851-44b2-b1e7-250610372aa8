package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线产出值统计DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线产出值统计DTO")
public class WorkLineCompletionNumberQueryDTO {

    /**
     * 计划产出
     */
    @Schema(description = "计划产出")
    private Long planNumber;

    /**
     * 实际产出
     */
    @Schema(description = "实际产出")
    private Long actualNumber;

    public Long getPlanNumber() {
        return planNumber;
    }

    public WorkLineCompletionNumberQueryDTO setPlanNumber(Long planNumber) {
        this.planNumber = planNumber;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public WorkLineCompletionNumberQueryDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public WorkLineCompletionNumberQueryDTO() {
    }

    public WorkLineCompletionNumberQueryDTO(Long planNumber, Long actualNumber) {
        this.planNumber = planNumber;
        this.actualNumber = actualNumber;
    }
}
