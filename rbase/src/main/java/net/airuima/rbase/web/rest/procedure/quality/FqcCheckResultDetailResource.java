package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResultDetail;
import net.airuima.rbase.service.procedure.quality.FqcCheckResultDetailService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果明细表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "FQC检测结果明细表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/fqc-check-result-details")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FQC")
public class FqcCheckResultDetailResource extends BaseResource<FqcCheckResultDetail> {

    private final FqcCheckResultDetailService fqcCheckResultDetailService;

    public FqcCheckResultDetailResource(FqcCheckResultDetailService fqcCheckResultDetailService) {
        this.fqcCheckResultDetailService = fqcCheckResultDetailService;
        this.mapUri = "/api/fqc-check-result-details";
    }

}
