package net.airuima.rbase.web.rest.report.dto.worksheetstatus;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单状态汇总数据DTO
 * <AUTHOR>
 * @date 2023/10/24
 */
public class WsStatusNumberDTO {
    private Integer status;
    private Long number;
    private String recordDate;

    public WsStatusNumberDTO(Integer status, Long number) {
        this.status = status;
        this.number = number;
    }

    public WsStatusNumberDTO(Object recordDate,Long number){
        this.recordDate = Objects.isNull(recordDate)? StringUtils.EMPTY:String.valueOf(recordDate);
        this.number = number;
    }

    public Integer getStatus() {
        return status;
    }

    public WsStatusNumberDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WsStatusNumberDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public WsStatusNumberDTO setRecordDate(String recordDate) {
        this.recordDate = recordDate;
        return this;
    }
}
