package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合格率图形查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "合格率图形查询结果DTO")
public class QualifiedChartQueryDataDTO {


    /**
     * 实际完成数
     */
    @Schema(description = "实际完成数")
    private Long actualNumber;


    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Long qualifiedNumber;


    /**
     * 在线返修合格数
     */
    @Schema(description = "在线返修合格数")
    private Long reworkQualifiedNumber;


    /**
     * 产品型号
     */
    @Schema(description = "产品型号")
    private String name;

    /**
     * x轴时间
     */
    @Schema(description = "x轴时间")
    private String time;


    public String getName() {
        return name;
    }

    public QualifiedChartQueryDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getTime() {
        return time;
    }

    public QualifiedChartQueryDataDTO setTime(String time) {
        this.time = time;
        return this;
    }

    public Long getActualNumber() {
        return actualNumber;
    }

    public QualifiedChartQueryDataDTO setActualNumber(Long actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public QualifiedChartQueryDataDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public QualifiedChartQueryDataDTO setReworkQualifiedNumber(Long reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        return this;
    }

    public QualifiedChartQueryDataDTO() {

    }

    public QualifiedChartQueryDataDTO(Long actualNumber, Long qualifiedNumber, Long reworkQualifiedNumber, String name, Object time) {
        this.actualNumber = actualNumber;
        this.qualifiedNumber = qualifiedNumber;
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        this.name = name;
        this.time = Objects.isNull(time)? StringUtils.EMPTY:String.valueOf(time);
    }

}
