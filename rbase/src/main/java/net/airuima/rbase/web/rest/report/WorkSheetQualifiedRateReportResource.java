package net.airuima.rbase.web.rest.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.service.report.WorkSheetQualifiedRateReportService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.web.rest.report.dto.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * <p>
 * 工单合格率报表Resource
 *
 * <AUTHOR>
 * @date 2021-3-19
 */
@Tag(name = "工单合格率报表Resource")
@RestController
@RequestMapping("/api/report/work-sheet-qualified-rate-report")
@AuthorityRegion("报表看板")
public class WorkSheetQualifiedRateReportResource {


    private final WorkSheetQualifiedRateReportService workSheetQualifiedRateReportService;

    public WorkSheetQualifiedRateReportResource(WorkSheetQualifiedRateReportService workSheetQualifiedRateReportService) {
        this.workSheetQualifiedRateReportService = workSheetQualifiedRateReportService;
    }

    /**
     * 工单合格率图形数据
     *
     * @param workSheetQualifiedRateRequestDto 工单报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.WorkSheetQualifiedRateReportChartResultDTO> 工单报表统计图表返回结果
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_QUALIFIED_RATE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("PassrateStatistics")
    @PostMapping("/chart")
    public ResponseEntity<ResponseData<WorkSheetQualifiedRateReportChartResultDTO>> workSheetQualifiedReportChart(
            @RequestBody WorkSheetQualifiedRateReportRequestDTO workSheetQualifiedRateRequestDto) {
        try {
            WorkSheetQualifiedRateReportChartResultDTO workSheetQualifiedRateReportChartResultDto = workSheetQualifiedRateReportService.getWorkSheetQualifiedReportChart(workSheetQualifiedRateRequestDto);
            return ResponseData.ok(workSheetQualifiedRateReportChartResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工单合格率表格数据
     *
     * @param workSheetQualifiedRateRequestDto 工单报表请求参数
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.report.dto.WorkSheetQualifiedRateReportTableResultDTO> 工单报表表格数据返回结果
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_QUALIFIED_RATE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("PassrateStatistics")
    @PostMapping("/table")
    public ResponseEntity<ResponseData<WorkSheetQualifiedRateReportTableResultDTO>> workSheetQualifiedReportTable(
            @RequestBody WorkSheetQualifiedRateReportRequestDTO workSheetQualifiedRateRequestDto) {
        try {
            WorkSheetQualifiedRateReportTableResultDTO workSheetQualifiedRateReportTableResultDto = workSheetQualifiedRateReportService.getWorkSheetQualifiedReportTable(workSheetQualifiedRateRequestDto);
            return ResponseData.ok(workSheetQualifiedRateReportTableResultDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工单合格率表格导出数据
     * @param file 图形区域文件
     * @param requestParam 请求参数
     */
    @PreAuthorize("hasAnyAuthority('WORK_SHEET_QUALIFIED_RATE_REPORT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @FuncInterceptor("PassrateStatistics")
    @PostMapping("/export")
    public void workSheetQualifiedReportTableExport(@RequestParam("file") MultipartFile file,
                                                    @RequestParam("requestParam") String requestParam,  @RequestParam(value = "excelType",required = false) String excelType,HttpServletResponse response) throws IOException {

        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
        WorkSheetQualifiedRateReportRequestDTO workSheetQualifiedRateRequestDto = JSON.parseObject(requestParam, new TypeReference<>() {
        });
        WorkSheetQualifiedRateReportTableResultDTO workSheetQualifiedRateReportTableResultDto = workSheetQualifiedRateReportService.getWorkSheetQualifiedReportTable(workSheetQualifiedRateRequestDto);
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(ExcelUtils.createSheetParams("合格率明细",  WorkSheetQualifiedRateReportTableItemDTO.class, workSheetQualifiedRateReportTableResultDto.getQualifiedRateReportTableItemList()));
        sheetsList.add(ExcelUtils.createSheetParams("统计图表",  ExportGraphDTO.class, new ArrayList<>(List.of(new ExportGraphDTO(file.getBytes())))));
        String fileName = "工单合格率报表" + System.currentTimeMillis() + prefix;
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?ExcelType.HSSF:ExcelType.XSSF);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        workbook.write(response.getOutputStream());
    }

    public String getAuthorityDescription(String authority) {
        if (StringUtils.isEmpty(authority)) {
            return "";
        } else if ("WORK_SHEET_QUALIFIED_RATE_REPORT_READ".equals(authority)) {
            return "浏览工单合格率报表";
        }
        return "";
    }

}
