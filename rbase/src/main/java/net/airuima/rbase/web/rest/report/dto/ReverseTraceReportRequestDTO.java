package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/7/4
 */
@Schema(name = "生产过程反向追溯查询参数DTO")
public class ReverseTraceReportRequestDTO extends PageDTO {

    /**
     * 反向追溯类型 0：物料，1：设备，2：易损件
     */
    @Schema(description = "反向追溯类型",type = "integer",format = "int32",example = "0：物料，1：设备，2：易损件")
    private Integer category;

    /**
     * 物料主键ID
     */
    @Schema(description = "物料主键ID(按照物料反向追溯时需要提供此参数)",type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次(按照物料反向追溯时需要提供此参数)",type = "string")
    private String materialBatch;

    /**
     * 设备主键ID
     */
    @Schema(description = "设备主键ID(按照设备反向追溯类时需要提供此参数)",type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    /**
     * 易损件主键ID
     */
    @Schema(description = "易损件主键ID(按照易损件反向追溯类时需要提供此参数)",type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wearingPartId;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * excel导出格式（xls,xlsx）
     */
    @Schema(description = "excel导出格式（xls,xlsx）")
    private String excelType;

    public Integer getCategory() {
        return category;
    }

    public ReverseTraceReportRequestDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public ReverseTraceReportRequestDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public ReverseTraceReportRequestDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public ReverseTraceReportRequestDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public Long getWearingPartId() {
        return wearingPartId;
    }

    public ReverseTraceReportRequestDTO setWearingPartId(Long wearingPartId) {
        this.wearingPartId = wearingPartId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public ReverseTraceReportRequestDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public ReverseTraceReportRequestDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public String getExcelType() {
        return excelType;
    }

    public ReverseTraceReportRequestDTO setExcelType(String excelType) {
        this.excelType = excelType;
        return this;
    }
}
