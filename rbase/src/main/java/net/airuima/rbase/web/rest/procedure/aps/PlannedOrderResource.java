package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.aps.PlannedOrder;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.service.procedure.aps.PlannedOrderService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计划下单记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "计划下单记录")
@RestController
@RequestMapping("/api/planned-orders")
@AuthorityRegion("生产工单")
public class PlannedOrderResource extends ProtectBaseResource<PlannedOrder> {

    @Autowired
    private PlannedOrderService plannedOrderService;

    public PlannedOrderResource(PlannedOrderService plannedOrderService) {
        this.plannedOrderService = plannedOrderService;
        this.mapUri = "/api/planned-orders";
    }

    /**
     * 计划下单
     *
     * @param saleOrderDetailIds 订单详情ids
     * <AUTHOR>
     * @since 1.8.1
     */
    @Operation(description = "计划下单")
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> plannedOrders(@RequestBody List<Long> saleOrderDetailIds) {
        try {
            plannedOrderService.plannedOrders(saleOrderDetailIds);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 取消计划下单
     *
     * @param ids 计划下单记录ids
     * @return ResponseEntity<ResponseData < Void>>
     * <AUTHOR>
     * @since 1.8.1
     */
    @Operation(description = "取消计划下单")
    @PutMapping("/cancel")
    public ResponseEntity<ResponseData<Void>> cancelOrder(@RequestBody List<Long> ids) {
        try {
            plannedOrderService.cancelOrder(ids);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 将销售订单详情分组合并为销售订单展示
     *
     * @param saleOrderDetailIds 销售订单详情ids
     * @return List<SaleOrder>
     * <AUTHOR>
     * @since 1.8.1
     */
    @Operation(description = "将销售订单详情分组合并为销售订单展示")
    @PostMapping("/groupBySaleOrderId")
    public ResponseEntity<ResponseData<List<SaleOrder>>> saleOrderDetailIdsGroupBySaleOrderId(@RequestBody List<Long> saleOrderDetailIds) {
        try {
            return ResponseData.ok(plannedOrderService.saleOrderDetailIdsGroupBySaleOrderId(saleOrderDetailIds));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "计划下单记录");
    }
}
