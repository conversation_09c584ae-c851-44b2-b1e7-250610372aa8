package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.StepIntervalEvent;
import net.airuima.rbase.service.procedure.quality.StepIntervalEventService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序间隔异常Resource
 * <AUTHOR>
 */
@Tag(name = "工序间隔异常Resource")
@RestController
@RequestMapping("/api/step-interval-events")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("StepInterval")
@AuthSkip("ICD")
public class StepIntervalEventResource extends ProtectBaseResource<StepIntervalEvent> {
    private static final String MODULE = "工序间隔异常";
    private final StepIntervalEventService stepIntervalEventService;

    public StepIntervalEventResource(StepIntervalEventService stepIntervalEventService) {
        this.stepIntervalEventService = stepIntervalEventService;
        this.mapUri = "/api/step-interval-events";
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
