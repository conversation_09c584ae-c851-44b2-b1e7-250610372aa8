package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.aps.ProductionPlanService;
import net.airuima.rbase.web.rest.procedure.aps.dto.ProductionPlanDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划Resource
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Tag(name = "生产计划Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/production-plans")
@FuncInterceptor("ProductionPlan")
@AuthorityRegion("生产计划")
public class ProductionPlanResource extends ProtectBaseResource<ProductionPlan> {

    private final ProductionPlanService productionPlanService;

    public ProductionPlanResource(ProductionPlanService productionPlanService) {
        this.productionPlanService = productionPlanService;
        this.mapUri = "/api/production-plans";
    }

    /**
     * 提交生产计划
     *
     * @param productionPlanDto 生产计划提交参数
     * @return org.springframework.http.ResponseEntity 返回通用对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "提交生产计划")
    @PostMapping("/custom")
    @PreventRepeatSubmit(expireTime = 5)
    public ResponseEntity<ResponseData<Void>> saveProductionPlan(@RequestBody @Validated ProductionPlanDTO productionPlanDto) {
        try {
            BaseDTO baseDto = productionPlanService.saveProductionPlan(productionPlanDto);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }


    /**
     * 更新生产计划
     *
     * @param productionPlanDto 生产计划提交参数
     * @return org.springframework.http.ResponseEntity 返回通用对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "更新生产计划")
    @PutMapping("/custom")
    @PreventRepeatSubmit(expireTime = 5)
    public ResponseEntity<ResponseData<Void>> updateProductionPlan(@RequestBody @Validated ProductionPlanDTO productionPlanDto) {
        try {
            BaseDTO baseDto = productionPlanService.updateProductionPlan(productionPlanDto);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 生产计划导入
     *
     * @param file excel文件
     * @return org.springframework.http.ResponseEntity 返回通用对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/excel/import"})
    public ResponseEntity<ResponseData<Void>> importProductionPlanExcel(@RequestParam("file") MultipartFile file) {
        try {
            productionPlanService.importProductionPlanExcel(file);
            return ResponseData.importExcel();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 生产计划确认
     *
     * @param id 生产计划id
     * @return org.springframework.http.ResponseEntity 返回通用对象
     */
    @Parameters({
            @Parameter(name = "id", description = "生产计划id")
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/confirm/{id}"})
    @Operation(summary = "生产计划确认")
    public ResponseEntity<ResponseData<Void>> confirm(@PathVariable("id") Long id) {
        try {
            productionPlanService.confirm(id);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "生产计划");
    }

}
