package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.dto.material.CheckMaterialDetailDTO;
import net.airuima.rbase.service.procedure.material.IWsCheckMaterialDetailService;
import net.airuima.rbase.service.procedure.material.WsCheckMaterialDetailService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料明细表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "工单核料明细表Resource")
@RestController
@RequestMapping("/api/ws-check-material-details")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch")
public class WsCheckMaterialDetailResource extends ProtectBaseResource<WsCheckMaterialDetail> {

    private final WsCheckMaterialDetailService wsCheckMaterialDetailService;

    @Autowired
    private IWsCheckMaterialDetailService[] iWsCheckMaterialDetailServices;

    public WsCheckMaterialDetailResource(WsCheckMaterialDetailService wsCheckMaterialDetailService) {
        this.wsCheckMaterialDetailService = wsCheckMaterialDetailService;
        this.mapUri = "/api/ws-check-material-details";
    }

    /**
     * 通过凭证号获取待核料明细信息
     *
     * @param checkMaterialCode 凭证号
     * @return 核料明细
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "通过凭证号获取待核料明细信息")
    @GetMapping("/byCheckMaterialCode")
    public ResponseEntity<ResponseData<List<WsCheckMaterialDetail>>> findByDetailByCode(@RequestParam("checkMaterialCode") String checkMaterialCode) {
        List<WsCheckMaterialDetail> detailList = wsCheckMaterialDetailService.findByDetailByCode(checkMaterialCode);
        return ResponseData.ok(detailList);
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "通过核料凭证记录ID获核料明细信息")
    @GetMapping("/byWsCheckMaterialId/{wsCheckMaterialId}")
    public ResponseEntity<ResponseData<List<WsCheckMaterialDetail>>> findByDetailByCode(@PathVariable(value = "wsCheckMaterialId") Long wsCheckMaterialId) {
        List<WsCheckMaterialDetail> detailList = wsCheckMaterialDetailService.findByWsCheckMaterialId(wsCheckMaterialId);
        return ResponseData.ok(detailList);
    }



    /**
     * 保存工单核料数量信息
     *
     * @param checkMaterialDetails 核料数量
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit(expireTime = 30)
    @Operation(summary= "保存工单核料数量信息")
    @PostMapping("/updateCustom")
    public ResponseEntity<ResponseContent<Void>> saveCheckMaterialDetail(@RequestBody List<CheckMaterialDetailDTO> checkMaterialDetails) {
        try {
            iWsCheckMaterialDetailServices[0].saveCheckMaterialDetail(checkMaterialDetails);
        } catch (ResponseException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsCheckMaterialDetail.class.getSimpleName()), e.getErrorKey(), e.getErrorMessage())).build();
        }
        return ResponseContent.ok().isOkBuild();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览工单核料明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建工单核料明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改工单核料明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除工单核料明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入工单核料明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出工单核料明细";
        }
        return "";
    }

}
