package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.service.procedure.quality.InspectTaskService;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 待检任务Resource
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Tag(name = "待检任务Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/inspect-tasks")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("PQC || FQC || FAI || IPQC || LQC")
public class InspectTaskResource extends BaseResource<InspectTask> {

    private static final String MODULE = "待检任务";

    private final InspectTaskService inspectTaskService;

    public InspectTaskResource(InspectTaskService inspectTaskService) {
        this.inspectTaskService = inspectTaskService;
        this.mapUri = "/api/inspect-tasks";
    }

    /**
     * 获取所有待检任务
     * <AUTHOR>
     * @date  2023/5/5
     */
    @GetMapping("/all")
    public ResponseEntity<ResponseData<List<InspectTask>>> getAll() {
        return ResponseData.ok(inspectTaskService.getAll());
    }

}
