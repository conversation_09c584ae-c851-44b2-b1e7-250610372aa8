package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent;
import net.airuima.rbase.service.procedure.quality.UnqualifiedEventService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 预警停线事件表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "预警停线事件表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/unqualified-events")
@AuthorityRegion("质量预警配置")
@FuncInterceptor("QPrewarning")
@AuthSkip("ICD")
public class UnqualifiedEventResource extends ProtectBaseResource<UnqualifiedEvent> {

    private final UnqualifiedEventService unqualifiedEventService;

    public UnqualifiedEventResource(UnqualifiedEventService unqualifiedEventService) {
        this.unqualifiedEventService = unqualifiedEventService;
        this.mapUri = "/api/unqualified-events";
    }

    /**
     * 预警停线事件处理
     * <AUTHOR>
     * @param entity 预警停线实体
     * @return ResponseEntity<ResponseData<UnqualifiedEvent>>
     * @date 2021-03-19
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<UnqualifiedEvent> update(@Valid @RequestBody UnqualifiedEvent entity) throws URISyntaxException {
        if (null == entity.getId()) {
            throw new ResponseException("error.NullId", "Invalid id");
        }
        entity = unqualifiedEventService.updateInstance(entity);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(UnqualifiedEvent.class.getSimpleName()), entity.getId().toString())).body(entity);
    }

    /**
     *
     * 流程审批恢复预警停线
     * @param serialNumber 预警停线事件序列号
     */
    @Operation(summary = "流程审批恢复预警停线")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity() ")
    @PostMapping("/process/recover")
    public ResponseEntity<ResponseData<Void>> recover(@RequestParam("businessKey") String serialNumber){
        unqualifiedEventService.processRecover(serialNumber);
        return ResponseData.save();
    }

    /**
     *
     * 通过流水号获取预警停线事件记录
     * @param serialNumber 预警停线事件序列号
     * @return net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent 预警停线事件记录
     */
    @Operation(summary = "通过流水号获取预警停线事件记录")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity() ")
    @GetMapping("/serialNumber/{serialNumber}")
    public ResponseEntity<ResponseData<UnqualifiedEvent>> serialNumber(@PathVariable("serialNumber") String serialNumber){
        return ResponseData.ok(unqualifiedEventService.findBySerialNumber(serialNumber));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览预警停线事件";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建预警停线事件";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改预警停线事件";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除预警停线事件";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入预警停线事件";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出预警停线事件";
        }
        return "";
    }

}
