package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistory;
import net.airuima.rbase.service.procedure.quality.PedigreeStepInspectionHistoryService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ValidateUtils;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工序检查历史记录Resource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "工序检查历史记录Resource")
@AppKey("RmesService")
@FuncInterceptor("StepPreCheck")
@AuthorityRegion("生产质量数据")
@RestController
@RequestMapping("/api/pedigree-step-inspection-histories")
@AuthSkip("CUID")
public class PedigreeStepInspectionHistoryResource extends ProtectBaseResource<PedigreeStepInspectionHistory> {

    private final PedigreeStepInspectionHistoryService pedigreeStepInspectionHistoryService;

    public PedigreeStepInspectionHistoryResource(PedigreeStepInspectionHistoryService pedigreeStepInspectionHistoryService) {
        this.pedigreeStepInspectionHistoryService = pedigreeStepInspectionHistoryService;
        this.mapUri = "/api/pedigree-step-inspection-histories";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "工序检查历史记录");
    }
}
