package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.SaleOrderDetail;
import net.airuima.rbase.service.procedure.aps.SaleOrderDetailService;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * SaleOrderDetailReSource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "销售订单详情Resource")
@RestController
@RequestMapping("/api/sale-order-details")
@AuthorityRegion("生产工单")
@FuncInterceptor("SaleOrder")
public class SaleOrderDetailResource extends ProtectBaseResource<SaleOrderDetail> {

    private final SaleOrderDetailService saleOrderDetailService;

    public SaleOrderDetailResource(SaleOrderDetailService saleOrderDetailService) {
        this.saleOrderDetailService = saleOrderDetailService;
        this.mapUri = "/api/sale-order-details";
    }

    /**
     * 根据订单号、谱系ID模糊查询
     *
     * @param text       根据订单号、谱系ID模糊查询
     * @param size       返回数据个数
     * @param pedigreeId 谱系ID
     * @return List<SaleOrderDetail>
     * <AUTHOR>
     * @date 2022/12/29
     */
    @Parameters({
            @Parameter(name = "text", description = "订单号", required = true),
            @Parameter(name = "pedigreeId", description = "谱系ID", required = true),
            @Parameter(name = "size", description = "数量", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据订单号、谱系ID模糊查询")
    @GetMapping("/bySerialNumberAndPedigreeId")
    public ResponseEntity<ResponseData<List<SaleOrderDetail>>> bySerialNumberAndSpecification(@RequestParam(value = "text") String text, @RequestParam(value = "pedigreeId") Long pedigreeId, @RequestParam(value = "size") int size) {
        return ResponseData.ok(saleOrderDetailService.bySerialNumberAndPedigreeId(text, pedigreeId, size));
    }

    /**
     * 根据谱系ID和客户ID查询销售订单详情
     *
     * @param pedigreeId 产品谱系id
     * @param clientId 客户id
     * @return List<SaleOrderDetail>
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据谱系ID和客户ID查询销售订单详情")
    @GetMapping("/findByPedigreeIdAndClientId")
    public ResponseEntity<ResponseData<List<SaleOrderDetail>>> findByPedigreeIdAndClientId(@RequestParam(value = "pedigreeId") Long pedigreeId, @RequestParam(value = "clientId", required = false) Long clientId,@ParameterObject Pageable pageable) {
        return ResponseData.ok(saleOrderDetailService.findByPedigreeIdAndClientId(pedigreeId, clientId,pageable));
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "销售订单详情");
    }
}
