package net.airuima.rbase.web.rest.report.dto.forwardtracereport;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Objects;

@Schema(name = "生产过程SN正向追溯数据明细DTO")
@FetchEntity
public class SnForwardTraceStepDetailExportDTO {
    /**
     * 单据号
     */
    @Schema(description = "单据号")
    private String serialNumber;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startDate;

    /**
     * 工序结束时间
     */
    @Schema(description = "工序结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endDate;

    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    private Integer stepInputNumber;

    /**
     * 工序合格数
     */
    @Schema(description = "工序合格数")
    private Integer stepQualifiedNumber;


    /**
     * 工序不合格数
     */
    @Schema(description = "工序不合格数")
    private Integer stepUnqualifiedNumber;


    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    private String sn;

    /**
     * 员工Id
     */
    @Schema(description = "员工Id")
    private Long staffId;

    /**
     * 员工DTO
     **/
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    private String staffCode;

    /**
     * 员工名称
     */
    @Schema(description = "员工姓名")
    private String staffName;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;

    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    private String workCellName;

    /**
     * 物料ID
     */
    @Schema(description = "物料ID")
    private Long materialId;

    /**
     * 物料DTO
     **/
    @Schema(description = "物料DTO")
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String materialBatch;

    /**
     * 物料数量
     */
    @Schema(description = "物料数量")
    private Double materialNumber;

    /**
     * 易损件编码
     */
    @Schema(description = "易损件编码")
    private String wearingPartCode;

    /**
     * 易损件名称
     */
    @Schema(description = "易损件名称")
    private String wearingPartName;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private Long facilityId;
    /**
     * 设备DTO
     */
    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Integer qualifiedNumber;

    /**
     * 不良项目名称
     */
    @Schema(description = "不良项目名称")
    private String unqualifiedItemCode;
    /**
     * 不良项目编码
     */
    @Schema(description = "不良项目编码")
    private String unqualifiedItemName;
    /**
     * 不良项目数量
     */
    @Schema(description = "不良项目数量")
    private Integer unqualifiedItemNumber;

    @Schema(description = "工序耗时")
    private Double workHour;

    @Schema(description = "预留字段1")
    private String custom1;

    @Schema(description = "预留字段2")
    private String custom2;


    @Schema(description = "预留字段3")
    private String custom3;


    @Schema(description = "预留字段4")
    private String custom4;


    @Schema(description = "预留字段5")
    private String custom5;


    public SnForwardTraceStepDetailExportDTO() {
    }


    public SnForwardTraceStepDetailExportDTO(String serialNumber,String stepCode, String stepName, LocalDateTime startDate, LocalDateTime endDate,
                                             Integer stepInputNumber, Integer stepQualifiedNumber, Integer stepUnqualifiedNumber,String containerCode, String sn,
                                             Long staffId, String workCellCode, String workCellName, Long materialId, String materialBatch, Double materialNumber,
                                             String wearingPartCode, String wearingPartName, Integer inputNumber, Integer qualifiedNumber,Long facilityId,
                                             String unqualifiedItemCode, String unqualifiedItemName,Double workHour,String custom1,String custom2) {
        this.serialNumber = serialNumber;
        this.stepCode = stepCode;
        this.stepName = stepName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.stepInputNumber = stepInputNumber;
        this.stepQualifiedNumber = stepQualifiedNumber;
        this.containerCode = containerCode;
        this.sn = sn;
        this.staffId = staffId;
        this.workCellCode = workCellCode;
        this.workCellName = workCellName;
        this.materialId = materialId;
        this.materialBatch = materialBatch;
        this.materialNumber = materialNumber;
        this.wearingPartCode = wearingPartCode;
        this.wearingPartName = wearingPartName;
        this.inputNumber = inputNumber;
        this.qualifiedNumber = qualifiedNumber;
        this.facilityId = facilityId;
        this.unqualifiedItemCode = unqualifiedItemCode;
        this.unqualifiedItemName = unqualifiedItemName;
        this.unqualifiedItemNumber = Objects.nonNull(unqualifiedItemName)?Constants.INT_ONE:null;
        this.stepUnqualifiedNumber = stepUnqualifiedNumber;
        this.workHour = workHour;
        this.custom1 = custom1;
        this.custom2 = custom2;

    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SnForwardTraceStepDetailExportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public SnForwardTraceStepDetailExportDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public SnForwardTraceStepDetailExportDTO setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public String getUnqualifiedItemCode() {
        return unqualifiedItemCode;
    }

    public SnForwardTraceStepDetailExportDTO setUnqualifiedItemCode(String unqualifiedItemCode) {
        this.unqualifiedItemCode = unqualifiedItemCode;
        return this;
    }

    public String getUnqualifiedItemName() {
        return unqualifiedItemName;
    }

    public SnForwardTraceStepDetailExportDTO setUnqualifiedItemName(String unqualifiedItemName) {
        this.unqualifiedItemName = unqualifiedItemName;
        return this;
    }

    public Integer getUnqualifiedItemNumber() {
        return unqualifiedItemNumber;
    }

    public SnForwardTraceStepDetailExportDTO setUnqualifiedItemNumber(Integer unqualifiedItemNumber) {
        this.unqualifiedItemNumber = unqualifiedItemNumber;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public SnForwardTraceStepDetailExportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public SnForwardTraceStepDetailExportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public SnForwardTraceStepDetailExportDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public SnForwardTraceStepDetailExportDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public Integer getStepInputNumber() {
        return stepInputNumber;
    }

    public SnForwardTraceStepDetailExportDTO setStepInputNumber(Integer stepInputNumber) {
        this.stepInputNumber = stepInputNumber;
        return this;
    }

    public Integer getStepQualifiedNumber() {
        return stepQualifiedNumber;
    }

    public SnForwardTraceStepDetailExportDTO setStepQualifiedNumber(Integer stepQualifiedNumber) {
        this.stepQualifiedNumber = stepQualifiedNumber;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public SnForwardTraceStepDetailExportDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public SnForwardTraceStepDetailExportDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public SnForwardTraceStepDetailExportDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public SnForwardTraceStepDetailExportDTO setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public SnForwardTraceStepDetailExportDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public SnForwardTraceStepDetailExportDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public SnForwardTraceStepDetailExportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public SnForwardTraceStepDetailExportDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getMaterialName() {
        return materialName;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialName(String materialName) {
        this.materialName = materialName;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public Double getMaterialNumber() {
        return materialNumber;
    }

    public SnForwardTraceStepDetailExportDTO setMaterialNumber(Double materialNumber) {
        this.materialNumber = materialNumber;
        return this;
    }

    public String getWearingPartCode() {
        return wearingPartCode;
    }

    public SnForwardTraceStepDetailExportDTO setWearingPartCode(String wearingPartCode) {
        this.wearingPartCode = wearingPartCode;
        return this;
    }

    public String getWearingPartName() {
        return wearingPartName;
    }

    public SnForwardTraceStepDetailExportDTO setWearingPartName(String wearingPartName) {
        this.wearingPartName = wearingPartName;
        return this;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public SnForwardTraceStepDetailExportDTO setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public SnForwardTraceStepDetailExportDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getStepUnqualifiedNumber() {
        return stepUnqualifiedNumber;
    }

    public SnForwardTraceStepDetailExportDTO setStepUnqualifiedNumber(Integer stepUnqualifiedNumber) {
        this.stepUnqualifiedNumber = stepUnqualifiedNumber;
        return this;
    }

    public Double getWorkHour() {
        return workHour;
    }

    public SnForwardTraceStepDetailExportDTO setWorkHour(Double workHour) {
        this.workHour = workHour;
        return this;
    }

    public String getCustom1() {
        return custom1;
    }

    public SnForwardTraceStepDetailExportDTO setCustom1(String custom1) {
        this.custom1 = custom1;
        return this;
    }

    public String getCustom2() {
        return custom2;
    }

    public SnForwardTraceStepDetailExportDTO setCustom2(String custom2) {
        this.custom2 = custom2;
        return this;
    }

    public String getCustom3() {
        return custom3;
    }

    public SnForwardTraceStepDetailExportDTO setCustom3(String custom3) {
        this.custom3 = custom3;
        return this;
    }

    public String getCustom4() {
        return custom4;
    }

    public SnForwardTraceStepDetailExportDTO setCustom4(String custom4) {
        this.custom4 = custom4;
        return this;
    }

    public String getCustom5() {
        return custom5;
    }

    public SnForwardTraceStepDetailExportDTO setCustom5(String custom5) {
        this.custom5 = custom5;
        return this;
    }
}
