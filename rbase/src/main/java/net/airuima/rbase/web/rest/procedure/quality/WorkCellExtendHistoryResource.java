package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendHistory;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendStepDetail;
import net.airuima.rbase.service.procedure.quality.WorkCellExtendHistoryService;
import net.airuima.rbase.service.procedure.quality.WorkCellExtendStepDetailService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工位宽放历史Resource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "工位宽放历史Resource")
@RestController
@RequestMapping("/api/work-cell-extend-histories")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC ")
@AuthSkip("ID")
public class WorkCellExtendHistoryResource extends ProtectBaseResource<WorkCellExtendHistory> {

    private static final String MODULE = "工位宽放历史";
    private final WorkCellExtendHistoryService WorkCellExtendStepDetailResource;

    public WorkCellExtendHistoryResource(WorkCellExtendHistoryService WorkCellExtendStepDetailResource) {
        this.WorkCellExtendStepDetailResource = WorkCellExtendStepDetailResource;
        this.mapUri = "/api/work-cell-extend-histories";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
