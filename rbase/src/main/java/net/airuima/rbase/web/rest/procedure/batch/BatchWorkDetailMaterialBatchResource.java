package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailMaterialBatchService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情物料批次Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "批量生产详情物料批次Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/batch-work-detail-material-batches")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("WorksheetMaterial")
@AuthSkip("ICUD")
public class BatchWorkDetailMaterialBatchResource extends ProtectBaseResource<BatchWorkDetailMaterialBatch> {

    private final BatchWorkDetailMaterialBatchService batchWorkDetailMaterialBatchService;

    public BatchWorkDetailMaterialBatchResource(BatchWorkDetailMaterialBatchService batchWorkDetailMaterialBatchService) {
        this.batchWorkDetailMaterialBatchService = batchWorkDetailMaterialBatchService;
        this.mapUri = "/api/batch-work-detail-material-batches";
    }

    /**
     * @description 通过工单工序生产过程数据主键ID获取工序用料物料信息
     * <AUTHOR>
     * @param batchWorkDetailId 工单工序生产详情ID
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过工单工序生产过程数据主键ID获取工序用料物料信息",parameters = {
            @Parameter(name = "batchWorkDetailId",description = "工单工序生产过程数据主键ID",required = true,schema = @Schema(type = "integer",types = "int64"),in= ParameterIn.PATH)
    })
    @GetMapping("/batch-work-details/{batchWorkDetailId}/material-batches")
    public ResponseEntity<ResponseData<List<BatchWorkDetailMaterialBatch.MaterialBatchBaseRecordInfo>>> findByBatchWorkDetailId(@PathVariable(value = "batchWorkDetailId") Long batchWorkDetailId) {
        return ResponseData.ok(batchWorkDetailMaterialBatchService.findMaterialBatchByBatchWorkDetailId(batchWorkDetailId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "批量生产物料批次");
    }

}
