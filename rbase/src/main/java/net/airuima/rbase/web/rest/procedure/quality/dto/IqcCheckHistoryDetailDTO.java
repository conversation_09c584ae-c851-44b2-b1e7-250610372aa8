package net.airuima.rbase.web.rest.procedure.quality.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验项目参数
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Schema(description = "来料检验项目参数")
public class IqcCheckHistoryDetailDTO {

    /**
     * 检验项目id
     */
    @Schema(description = "检验项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkItemId;


    /**
     * 序列号
     */
    @Schema(description = "序列号")
    private String sn;

    /**
     * 检测结果值(数字或OK、NG)
     */
    @Schema(description = "检测结果值(数字或OK、NG)")
    private String checkData;

    /**
     * 判定标准(开闭区间或者OK)
     */
    @Schema(description = "判定标准(开闭区间或者OK)")
    private String qualifiedRange;

    /**
     * 结果(0:不合格;1:合格)
     */
    @Schema(description = "结果(0:不合格;1:合格)")
    private Boolean result;

    /**
     * 缺陷原因
     */
    @Schema(description = "缺陷原因")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defectId;

    public Long getCheckItemId() {
        return checkItemId;
    }

    public IqcCheckHistoryDetailDTO setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public IqcCheckHistoryDetailDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getCheckData() {
        return checkData;
    }

    public IqcCheckHistoryDetailDTO setCheckData(String checkData) {
        this.checkData = checkData;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public IqcCheckHistoryDetailDTO setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public IqcCheckHistoryDetailDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Long getDefectId() {
        return defectId;
    }

    public IqcCheckHistoryDetailDTO setDefectId(Long defectId) {
        this.defectId = defectId;
        return this;
    }
}
