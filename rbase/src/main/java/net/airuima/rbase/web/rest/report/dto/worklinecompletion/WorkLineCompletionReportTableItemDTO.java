package net.airuima.rbase.web.rest.report.dto.worklinecompletion;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线完成报表表格明细数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "产线完成报表表格明细数据DTO")
public class WorkLineCompletionReportTableItemDTO {

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "1")
    private String workOrderNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "2")
    private String subWorkOrderNumber;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "3")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "4")
    private String pedigreeName;

    /**
     * 规格季号
     */
    @Schema(description = "规格季号")
    @Excel(name = "规格季号", orderNum = "5")
    private String specification;


    /**
     * 生产部门
     */
    @Schema(description = "生产部门")
    @Excel(name = "生产部门", orderNum = "6")
    private String organizationName;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    @Excel(name = "生产线", orderNum = "7")
    private String workLineName;


    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数", orderNum = "8")
    private Long number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "9")
    private Long qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Excel(name = "不合格数", orderNum = "10")
    private Long unQualifiedNumber;


    public String getWorkOrderNumber() {
        return workOrderNumber;
    }

    public WorkLineCompletionReportTableItemDTO setWorkOrderNumber(String workOrderNumber) {
        this.workOrderNumber = workOrderNumber;
        return this;
    }

    public String getSubWorkOrderNumber() {
        return subWorkOrderNumber;
    }

    public WorkLineCompletionReportTableItemDTO setSubWorkOrderNumber(String subWorkOrderNumber) {
        this.subWorkOrderNumber = subWorkOrderNumber;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public WorkLineCompletionReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public WorkLineCompletionReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public WorkLineCompletionReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public WorkLineCompletionReportTableItemDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkLineCompletionReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkLineCompletionReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkLineCompletionReportTableItemDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getUnQualifiedNumber() {
        return unQualifiedNumber;
    }

    public WorkLineCompletionReportTableItemDTO setUnQualifiedNumber(Long unQualifiedNumber) {
        this.unQualifiedNumber = unQualifiedNumber;
        return this;
    }
}
