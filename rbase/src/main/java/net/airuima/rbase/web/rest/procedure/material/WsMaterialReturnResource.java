package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.exception.BadRequestException;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWsCheckMaterialDTO;
import net.airuima.rbase.service.procedure.material.IWsMaterialReturnService;
import net.airuima.rbase.service.procedure.material.WsMaterialReturnService;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Tag(name = "工单退料表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-material-returns")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber")
@AuthSkip("IEUD")
public class WsMaterialReturnResource extends ProtectBaseResource<WsMaterialReturn> {

    private final WsMaterialReturnService wsMaterialReturnService;

    @Autowired
    private IWsMaterialReturnService[] wsMaterialReturnServices;

    public WsMaterialReturnResource(WsMaterialReturnService wsMaterialReturnService) {
        this.wsMaterialReturnService = wsMaterialReturnService;
        this.mapUri = "/api/ws-material-returns";
    }

    /**
     * 保存工单退料记录
     * @param rollBackMaterialDto 工单退料信息
     * <AUTHOR>
     * @date  2022/3/28
     * @return
     */
    @Operation(summary= "工单退料保存接口")
    @PostMapping("/ws-material-return")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseData<Void>> createWsMaterialReturn(@RequestBody RollBackMaterialDTO rollBackMaterialDto){
        try {
            BaseDTO baseDto = wsMaterialReturnServices[0].saveWsMaterialReturn(rollBackMaterialDto);
            if (Constants.OK.equals(baseDto.getStatus())){
                return ResponseData.save();
            }else {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工单退料单同步
     *
     * @param syncWsCheckMaterialDtoList 退料单同步数据
     * @return List
     */
    @Operation(summary = "工单退料单同步")
    @PostMapping("/sync-ws-material-return")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncWsMaterialReturn(@RequestBody List<SyncWsCheckMaterialDTO> syncWsCheckMaterialDtoList){
        try {
            return ResponseData.ok(wsMaterialReturnService.syncWsMaterialReturn(syncWsCheckMaterialDtoList));
        }catch (Exception e){
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览工单退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建工单退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改工单退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除工单退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入工单退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出工单退料";
        }
        return "";
    }
}
