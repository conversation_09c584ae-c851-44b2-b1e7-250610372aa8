package net.airuima.rbase.web.rest.procedure.batch.dto;

import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;

/**
 * 容器详情DTO
 */
public class ContainerDetailDTO {

    /**
     * 工序
     */
    private Step step;

    /**
     * 容器详情
     */
    private ContainerDetail containerDetail;

    public Step getStep() {
        return step;
    }

    public ContainerDetailDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public ContainerDetailDTO setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }
}
