package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表看板请求参数DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良报表看板请求参数DTO")
public class UnqualifiedReportRequestDTO extends PageDTO {

    /**
     * 计划完工时间类型
     */
    @Schema(description = "计划完工时间类型 0今天 1本周 2本月")
    private Integer planFinishTimeCategory;

    /**
     * 产品谱系id(最小层级)
     */
    @Schema(description = "产品谱系id(最小层级)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;


    /**
     * 生产线id
     */
    @Schema(description = "产线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 下单日期开始范围
     */
    @Schema(description = "下单日期开始范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 下单日期结束范围
     */
    @Schema(description = "下单日期结束范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 下单日期结束范围
     */
    @Schema(description = "是否导出 true导出 false不导出")
    private Boolean exportStatus;

    /**
     * 报表类型
     */
    @Schema(description = "报表类型 0 工单 1子工单")
    private Integer reportType;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String serialNumber;

    public Integer getPlanFinishTimeCategory() {
        return planFinishTimeCategory;
    }

    public UnqualifiedReportRequestDTO setPlanFinishTimeCategory(Integer planFinishTimeCategory) {
        this.planFinishTimeCategory = planFinishTimeCategory;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public UnqualifiedReportRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public UnqualifiedReportRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public UnqualifiedReportRequestDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public UnqualifiedReportRequestDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public Boolean getExportStatus() {
        return exportStatus;
    }

    public UnqualifiedReportRequestDTO setExportStatus(Boolean exportStatus) {
        this.exportStatus = exportStatus;
        return this;
    }

    public Integer getReportType() {
        return reportType;
    }

    public UnqualifiedReportRequestDTO setReportType(Integer reportType) {
        this.reportType = reportType;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public UnqualifiedReportRequestDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }
}
