package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线完成情况查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "产线完成情况查询结果DTO")
public class WorkLineFinishInfoChartResultDTO {

    /**
     * 部门名字
     */
    @Schema(description = "产线名字")
    private String name;


    /**
     * 实际完成数量
     */
    @Schema(description = "实际完成数量", required = true)
    private Long actualFinishNumber;

    /**
     * 实际计划数量
     */
    @Schema(description = "计划完成数量", required = true)
    private Long planFinishNumber;


    public String getName() {
        return name;
    }

    public WorkLineFinishInfoChartResultDTO setName(String name) {
        this.name = name;
        return this;
    }


    public Long getActualFinishNumber() {
        return actualFinishNumber;
    }

    public WorkLineFinishInfoChartResultDTO setActualFinishNumber(Long actualFinishNumber) {
        this.actualFinishNumber = actualFinishNumber;
        return this;
    }

    public Long getPlanFinishNumber() {
        return planFinishNumber;
    }

    public WorkLineFinishInfoChartResultDTO setPlanFinishNumber(Long planFinishNumber) {
        this.planFinishNumber = planFinishNumber;
        return this;
    }

    public WorkLineFinishInfoChartResultDTO() {
    }

    public WorkLineFinishInfoChartResultDTO(String name, Long actualFinishNumber, Long planFinishNumber) {
        this.name = name;
        this.actualFinishNumber = actualFinishNumber;
        this.planFinishNumber = planFinishNumber;
    }

}
