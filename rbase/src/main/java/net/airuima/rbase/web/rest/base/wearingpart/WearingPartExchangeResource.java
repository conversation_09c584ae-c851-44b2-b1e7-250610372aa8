package net.airuima.rbase.web.rest.base.wearingpart;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.rbase.service.base.wearingpart.WearingPartExchangeService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *易损件种类替代Resource
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Tag(name = "易损件种类替代Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wearing-part-exchanges")
@AuthorityRegion("易损件管理")
@FuncInterceptor("WearingPart")
@AuthSkip("D")
public class WearingPartExchangeResource extends ProtectBaseResource<WearingPartExchange> {

    private static final String MODULE = "易损件种类替代";
    private final WearingPartExchangeService wearingPartExchangeService;

    public WearingPartExchangeResource(WearingPartExchangeService wearingPartExchangeService){
        this.wearingPartExchangeService = wearingPartExchangeService;
        this.mapUri = "/api/wearing-part-exchanges";
    }

    /**
     * 根据替换易损件类型查询替代关系
     *
     * @param exchangeGroupId 替换易损件类型id
     * <AUTHOR>
     * @date 2022/10/28
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartExchange>
     */
    @Parameters({
           @Parameter(name = "exchangeGroupId", description = "替换易损件类型id", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据替换易损件类型查询替代关系")
    @GetMapping("/byExchangeGroupId")
    public ResponseEntity<ResponseData<List<WearingPartExchange>>> findByExchangeGroupId(@RequestParam(value = "exchangeGroupId") Long exchangeGroupId) {
        return ResponseData.ok(wearingPartExchangeService.findByExchangeGroupId(exchangeGroupId));
    }

    /**
     * 查询易损件替代关联关系
     *
     * @param originGroupId 易损件类型id
     * @param exchangeGroupId 替换易损件类型id
     * <AUTHOR>
     * @date 2022/10/28
     * @return WearingPartExchange
     */
    @Parameters({
           @Parameter(name = "originGroupId", description = "易损件类型id", required = true),
           @Parameter(name = "exchangeGroupId", description = "替换易损件类型id", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "查询易损件替代关联关系")
    @GetMapping("/byWearingPartGroupIdAndExchangeGroupId")
    public ResponseEntity<ResponseData<WearingPartExchange>> findByOriginWearingPartGroupIdAndExchangeWearingPartGroupId(@RequestParam(value = "originGroupId") Long originGroupId,@RequestParam(value = "exchangeGroupId") Long exchangeGroupId) {
        return ResponseData.ok(wearingPartExchangeService.findByOriginWearingPartGroupIdAndExchangeWearingPartGroupId(originGroupId,exchangeGroupId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
