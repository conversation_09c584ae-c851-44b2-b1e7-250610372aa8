package net.airuima.rbase.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.aps.SubWorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.aps.WorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Schema(name = "工单生产详情信息")
public class BatchWorkDetailGetDTO extends AbstractDto {

    @Schema(name = "子工单-工序生产过程数据")
    public static class  SubWorkSheetBatchWorkDetailDTO{

        /**
         * 工序生产数据主键ID
         */
        @Schema(description = "工序生产数据主键ID",type = "integer",format = "int64")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 子工单信息
         */
        @Schema(description = "子工单信息",implementation = SubWorkSheetSimpleGetDTO.class)
        private SubWorkSheetSimpleGetDTO subWorkSheet;

        /**
         * 生产工序信息
         */
        @Schema(description = "生产工序信息",implementation = BatchWorkDetailStepDTO.class)
        private BatchWorkDetailStepDTO step;

        /**
         * 生产工位信息
         */
        @Schema(description = "生产工位信息",implementation = BatchWorkDetailStepDTO.class)
        private BatchWorkDetailWorkCellDTO workCell;

        /**
         * 生产员工信息
         */
        @Schema(description = "生产员工信息",implementation = BatchWorkDetailStaffDTO.class)
        private BatchWorkDetailStaffDTO operatorDto;

        /**
         * 工序投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32",defaultValue = "0")
        private int inputNumber;

        /**
         * 工序实际完成数量
         */
        @Schema(description = "工序实际完成数量", type = "integer",format = "int32",defaultValue = "0")
        private int finishNumber;

        /**
         * 工序合格数量
         */
        @Schema(description = "工序合格数量",type = "integer",format = "int32",defaultValue = "0")
        private int qualifiedNumber;

        /**
         * 工序不合格数量
         */
        @Schema(description = "工序不合格数量",type = "integer",format = "int32",defaultValue = "0")
        private int unqualifiedNumber;

        /**
         * 待流转数量
         */
        @Schema(description = "待流转数量",type = "integer",format = "int32",defaultValue = "0")
        private int transferNumber;

        /**
         * 有效合格数
         */
        @Schema(description = "有效合格数",deprecated = true,type = "number",format = "double",defaultValue = "0")
        private double effectNumber;

        /**
         * 工序完成耗时
         */
        @Schema(description = "工序完成耗时",type = "number",format = "double",defaultValue = "0")
        private double workHour;

        /**
         * 工序生产开始时间
         */
        @Schema(description = "工序生产开始时间",type = "datetime",format = "date-time")
        private LocalDateTime startDate;

        /**
         * 完成日期
         */
        @Schema(description = "工序生产完成时间",type = "datetime",format = "date-time")
        private LocalDateTime endDate;

        /**
         * 是否完成(1:完成;0:未完成)
         */
        @Schema(description = "是否完成",type = "integer",format = "int32",example = "1:完成;0:未完成")
        private int finish;

        /**
         * 动态数据DTO
         */
        @Schema(description = "动态数据信息",implementation = StepDynamicDataColumnGetDTO.class)
        private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO;

        /**
         * 生产工序来源工艺路线主键ID
         */
        @Schema(description = "生产工序来源工艺路线主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workFlowId;

        private String custom1;

        private String custom2;

        public SubWorkSheetSimpleGetDTO getSubWorkSheet() {
            return subWorkSheet;
        }

        public SubWorkSheetBatchWorkDetailDTO setSubWorkSheet(SubWorkSheetSimpleGetDTO subWorkSheet) {
            this.subWorkSheet = subWorkSheet;
            return this;
        }

        public BatchWorkDetailStepDTO getStep() {
            return step;
        }

        public SubWorkSheetBatchWorkDetailDTO setStep(BatchWorkDetailStepDTO step) {
            this.step = step;
            return this;
        }

        public BatchWorkDetailWorkCellDTO getWorkCell() {
            return workCell;
        }

        public SubWorkSheetBatchWorkDetailDTO setWorkCell(BatchWorkDetailWorkCellDTO workCell) {
            this.workCell = workCell;
            return this;
        }

        public BatchWorkDetailStaffDTO getOperatorDto() {
            return operatorDto;
        }

        public SubWorkSheetBatchWorkDetailDTO setOperatorDto(BatchWorkDetailStaffDTO operatorDto) {
            this.operatorDto = operatorDto;
            return this;
        }

        public int getInputNumber() {
            return inputNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setInputNumber(int inputNumber) {
            this.inputNumber = inputNumber;
            return this;
        }

        public int getFinishNumber() {
            return finishNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setFinishNumber(int finishNumber) {
            this.finishNumber = finishNumber;
            return this;
        }

        public int getQualifiedNumber() {
            return qualifiedNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setQualifiedNumber(int qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public int getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setUnqualifiedNumber(int unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public int getTransferNumber() {
            return transferNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setTransferNumber(int transferNumber) {
            this.transferNumber = transferNumber;
            return this;
        }

        public double getEffectNumber() {
            return effectNumber;
        }

        public SubWorkSheetBatchWorkDetailDTO setEffectNumber(double effectNumber) {
            this.effectNumber = effectNumber;
            return this;
        }

        public double getWorkHour() {
            return workHour;
        }

        public SubWorkSheetBatchWorkDetailDTO setWorkHour(double workHour) {
            this.workHour = workHour;
            return this;
        }

        public LocalDateTime getStartDate() {
            return startDate;
        }

        public SubWorkSheetBatchWorkDetailDTO setStartDate(LocalDateTime startDate) {
            this.startDate = startDate;
            return this;
        }

        public LocalDateTime getEndDate() {
            return endDate;
        }

        public SubWorkSheetBatchWorkDetailDTO setEndDate(LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public int getFinish() {
            return finish;
        }

        public SubWorkSheetBatchWorkDetailDTO setFinish(int finish) {
            this.finish = finish;
            return this;
        }

        public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDTO() {
            return stepDynamicDataColumnGetDTO;
        }

        public SubWorkSheetBatchWorkDetailDTO setStepDynamicDataColumnGetDTO(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO) {
            this.stepDynamicDataColumnGetDTO = stepDynamicDataColumnGetDTO;
            return this;
        }

        public Long getWorkFlowId() {
            return workFlowId;
        }

        public SubWorkSheetBatchWorkDetailDTO setWorkFlowId(Long workFlowId) {
            this.workFlowId = workFlowId;
            return this;
        }

        public Long getId() {
            return id;
        }

        public SubWorkSheetBatchWorkDetailDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCustom1() {
            return custom1;
        }

        public SubWorkSheetBatchWorkDetailDTO setCustom1(String custom1) {
            this.custom1 = custom1;
            return this;
        }

        public String getCustom2() {
            return custom2;
        }

        public SubWorkSheetBatchWorkDetailDTO setCustom2(String custom2) {
            this.custom2 = custom2;
            return this;
        }
    }

    @Schema(name = "工单-工序生产过程数据")
    public static class WorkSheetBatchWorkDetailDTO{

        /**
         * 工序生产数据主键ID
         */
        @Schema(description = "工序生产数据主键ID",type = "integer",format = "int64")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工单信息
         */
        @Schema(description = "工单信息",implementation = WorkSheetSimpleGetDTO.class)
        private WorkSheetSimpleGetDTO workSheet;

        /**
         * 生产工序信息
         */
        @Schema(description = "生产工序信息",implementation = BatchWorkDetailStepDTO.class)
        private BatchWorkDetailStepDTO step;

        /**
         * 生产工位信息
         */
        @Schema(description = "生产工位信息",implementation = BatchWorkDetailStepDTO.class)
        private BatchWorkDetailWorkCellDTO workCell;

        /**
         * 生产员工信息
         */
        @Schema(description = "生产员工信息",implementation = BatchWorkDetailStaffDTO.class)
        private BatchWorkDetailStaffDTO operatorDto;

        /**
         * 工序投产数
         */
        @Schema(description = "工序投产数",type = "integer",format = "int32",defaultValue = "0")
        private int inputNumber;

        /**
         * 工序实际完成数量
         */
        @Schema(description = "工序实际完成数量", type = "integer",format = "int32",defaultValue = "0")
        private int finishNumber;

        /**
         * 工序合格数量
         */
        @Schema(description = "工序合格数量",type = "integer",format = "int32",defaultValue = "0")
        private int qualifiedNumber;

        /**
         * 工序不合格数量
         */
        @Schema(description = "工序不合格数量",type = "integer",format = "int32",defaultValue = "0")
        private int unqualifiedNumber;

        /**
         * 待流转数量
         */
        @Schema(description = "待流转数量",type = "integer",format = "int32",defaultValue = "0")
        private int transferNumber;

        /**
         * 有效合格数
         */
        @Schema(description = "有效合格数",deprecated = true,type = "number",format = "double",defaultValue = "0")
        private double effectNumber;

        /**
         * 工序完成耗时
         */
        @Schema(description = "工序完成耗时",type = "number",format = "double",defaultValue = "0")
        private double workHour;

        /**
         * 工序生产开始时间
         */
        @Schema(description = "工序生产开始时间",type = "datetime",format = "date-time")
        private LocalDateTime startDate;

        /**
         * 完成日期
         */
        @Schema(description = "工序生产完成时间",type = "datetime",format = "date-time")
        private LocalDateTime endDate;

        /**
         * 是否完成(1:完成;0:未完成)
         */
        @Schema(description = "是否完成",type = "integer",format = "int32",example = "1:完成;0:未完成")
        private int finish;

        /**
         * 动态数据DTO
         */
        @Schema(description = "动态数据信息",implementation = StepDynamicDataColumnGetDTO.class)
        private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO;

        /**
         * 生产工序来源工艺路线主键ID
         */
        @Schema(description = "生产工序来源工艺路线主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workFlowId;

        private String custom1;

        private String custom2;

        public WorkSheetSimpleGetDTO getWorkSheet() {
            return workSheet;
        }

        public WorkSheetBatchWorkDetailDTO setWorkSheet(WorkSheetSimpleGetDTO workSheet) {
            this.workSheet = workSheet;
            return this;
        }

        public BatchWorkDetailStepDTO getStep() {
            return step;
        }

        public WorkSheetBatchWorkDetailDTO setStep(BatchWorkDetailStepDTO step) {
            this.step = step;
            return this;
        }

        public BatchWorkDetailWorkCellDTO getWorkCell() {
            return workCell;
        }

        public WorkSheetBatchWorkDetailDTO setWorkCell(BatchWorkDetailWorkCellDTO workCell) {
            this.workCell = workCell;
            return this;
        }

        public BatchWorkDetailStaffDTO getOperatorDto() {
            return operatorDto;
        }

        public WorkSheetBatchWorkDetailDTO setOperatorDto(BatchWorkDetailStaffDTO operatorDto) {
            this.operatorDto = operatorDto;
            return this;
        }

        public int getInputNumber() {
            return inputNumber;
        }

        public WorkSheetBatchWorkDetailDTO setInputNumber(int inputNumber) {
            this.inputNumber = inputNumber;
            return this;
        }

        public int getFinishNumber() {
            return finishNumber;
        }

        public WorkSheetBatchWorkDetailDTO setFinishNumber(int finishNumber) {
            this.finishNumber = finishNumber;
            return this;
        }

        public int getQualifiedNumber() {
            return qualifiedNumber;
        }

        public WorkSheetBatchWorkDetailDTO setQualifiedNumber(int qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public int getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public WorkSheetBatchWorkDetailDTO setUnqualifiedNumber(int unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public int getTransferNumber() {
            return transferNumber;
        }

        public WorkSheetBatchWorkDetailDTO setTransferNumber(int transferNumber) {
            this.transferNumber = transferNumber;
            return this;
        }

        public double getEffectNumber() {
            return effectNumber;
        }

        public WorkSheetBatchWorkDetailDTO setEffectNumber(double effectNumber) {
            this.effectNumber = effectNumber;
            return this;
        }

        public double getWorkHour() {
            return workHour;
        }

        public WorkSheetBatchWorkDetailDTO setWorkHour(double workHour) {
            this.workHour = workHour;
            return this;
        }

        public LocalDateTime getStartDate() {
            return startDate;
        }

        public WorkSheetBatchWorkDetailDTO setStartDate(LocalDateTime startDate) {
            this.startDate = startDate;
            return this;
        }

        public LocalDateTime getEndDate() {
            return endDate;
        }

        public WorkSheetBatchWorkDetailDTO setEndDate(LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public int getFinish() {
            return finish;
        }

        public WorkSheetBatchWorkDetailDTO setFinish(int finish) {
            this.finish = finish;
            return this;
        }

        public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDTO() {
            return stepDynamicDataColumnGetDTO;
        }

        public WorkSheetBatchWorkDetailDTO setStepDynamicDataColumnGetDTO(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO) {
            this.stepDynamicDataColumnGetDTO = stepDynamicDataColumnGetDTO;
            return this;
        }

        public Long getWorkFlowId() {
            return workFlowId;
        }

        public WorkSheetBatchWorkDetailDTO setWorkFlowId(Long workFlowId) {
            this.workFlowId = workFlowId;
            return this;
        }

        public Long getId() {
            return id;
        }

        public WorkSheetBatchWorkDetailDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCustom1() {
            return custom1;
        }

        public WorkSheetBatchWorkDetailDTO setCustom1(String custom1) {
            this.custom1 = custom1;
            return this;
        }

        public String getCustom2() {
            return custom2;
        }

        public WorkSheetBatchWorkDetailDTO setCustom2(String custom2) {
            this.custom2 = custom2;
            return this;
        }
    }

    @Schema(description = "员工基础信息")
    public static class BatchWorkDetailStaffDTO{

        /**
         * 员工主键ID
         */
        @Schema(description = "员工主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 员工姓名
         */
        @Schema(description = "员工姓名",type = "string")
        private String name;

        /**
         * 员工编码
         */
        @Schema(description = "员工编码",type = "string")
        private String code;

        public Long getId() {
            return id;
        }

        public BatchWorkDetailStaffDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public BatchWorkDetailStaffDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public BatchWorkDetailStaffDTO setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(description = "工序基础信息")
    public static class BatchWorkDetailStepDTO{

        /**
         * 工序主键ID
         */
        @Schema(description = "工序主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码",type = "string")
        private String code;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称",type = "string")
        private String name;

        /**
         * 扩展数据
         */
        @Schema(description = "扩展数据",type = "string")
        private String custom1;


        public Long getId() {
            return id;
        }

        public BatchWorkDetailStepDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public BatchWorkDetailStepDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public BatchWorkDetailStepDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCustom1() {
            return custom1;
        }

        public BatchWorkDetailStepDTO setCustom1(String custom1) {
            this.custom1 = custom1;
            return this;
        }
    }

    @Schema(description = "工位基础信息")
    public static class BatchWorkDetailWorkCellDTO{
        /**
         * 工位主键ID
         */
        @Schema(description = "工位主键ID",type = "integer",format = "int64",maxLength = 20)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码",type = "string")
        private String code;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称",type = "string")
        private String name;

        public Long getId() {
            return id;
        }

        public BatchWorkDetailWorkCellDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public BatchWorkDetailWorkCellDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public BatchWorkDetailWorkCellDTO setName(String name) {
            this.name = name;
            return this;
        }
    }
}
