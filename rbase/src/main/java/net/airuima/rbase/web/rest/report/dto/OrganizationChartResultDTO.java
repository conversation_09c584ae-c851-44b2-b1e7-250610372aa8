package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.OrganizationDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单统计按部门分组合并查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/20
 */
@FetchEntity
@Schema(description = "工单统计按部门分组合并查询结果DTO")
public class OrganizationChartResultDTO {

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    @Transient
    @Schema(description = "部门DTO")
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 实际完成数量
     */
    @Schema(description = "实际完成数量", required = true)
    private Long actualFinishNumber;

    /**
     * 实际计划数量
     */
    @Schema(description = "计划完成数量", required = true)
    private Long planFinishNumber;


    public Long getOrganizationId() {
        return organizationId;
    }

    public OrganizationChartResultDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public OrganizationChartResultDTO setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Long getActualFinishNumber() {
        return actualFinishNumber;
    }

    public OrganizationChartResultDTO setActualFinishNumber(Long actualFinishNumber) {
        this.actualFinishNumber = actualFinishNumber;
        return this;
    }

    public Long getPlanFinishNumber() {
        return planFinishNumber;
    }

    public OrganizationChartResultDTO setPlanFinishNumber(Long planFinishNumber) {
        this.planFinishNumber = planFinishNumber;
        return this;
    }


    public OrganizationChartResultDTO() {
    }

    public OrganizationChartResultDTO(Long organizationId, Long actualFinishNumber, Long planFinishNumber) {
        this.organizationId = organizationId;
        this.actualFinishNumber = actualFinishNumber;
        this.planFinishNumber = planFinishNumber;
    }

}
