package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售订单履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class SaleOrderHistoryDTO {

    /**
     * 销售订单信息
     */
    private SaleOrder saleOrder;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,创建
         * 1,下发
         * 2,发货
         */
        private Integer type;

        /**
         * 备注
         */
        private String note;

        /**
         * 工单信息
         */
        private WorkSheet workSheet;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 操作人
         */
        private StaffDTO staffDTO;

        public StaffDTO getStaffDTO() {
            return staffDTO;
        }

        public HistoryDTO setStaffDTO(StaffDTO staffDTO) {
            this.staffDTO = staffDTO;
            return this;
        }

        public HistoryDTO() {
        }

        public HistoryDTO(Integer type, LocalDateTime localDateTime, String note, WorkSheet workSheet, StaffDTO staffDTO) {
            this.type = type;
            this.localDateTime = localDateTime;
            this.note = note;
            this.workSheet = workSheet;
            this.staffDTO = staffDTO;
        }

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public String getNote() {
            return note;
        }

        public HistoryDTO setNote(String note) {
            this.note = note;
            return this;
        }

        public WorkSheet getWorkSheet() {
            return workSheet;
        }

        public HistoryDTO setWorkSheet(WorkSheet workSheet) {
            this.workSheet = workSheet;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }
    }

    public SaleOrder getSaleOrder() {
        return saleOrder;
    }

    public SaleOrderHistoryDTO setSaleOrder(SaleOrder saleOrder) {
        this.saleOrder = saleOrder;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public SaleOrderHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
