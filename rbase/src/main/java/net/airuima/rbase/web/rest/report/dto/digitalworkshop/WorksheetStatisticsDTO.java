package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "数字车间-工单总览")
public class WorksheetStatisticsDTO implements Serializable {

    /**
     * 工单总数
     */
    @Schema(description = "工单总数")
    private Long countNumber;

    /**
     * 工单各状态总数 分布列表 （已逾期，今日计划完成，待完成）
     */
    @Schema(description = "工单各状态总数 分布列表 （已逾期，今日计划完成，待完成）")
    private List<WorkSheetData> workSheetDataList;


    public Long getCountNumber() {
        return countNumber;
    }

    public WorksheetStatisticsDTO setCountNumber(Long countNumber) {
        this.countNumber = countNumber;
        return this;
    }

    public List<WorkSheetData> getWorkSheetDataList() {
        return workSheetDataList;
    }

    public WorksheetStatisticsDTO setWorkSheetDataList(List<WorkSheetData> workSheetDataList) {
        this.workSheetDataList = workSheetDataList;
        return this;
    }

    @Schema(description = "工单状态-总数")
    public static class WorkSheetData {

        /**
         * 工单状态
         */
        @Schema(description = "工单状态")
        private String status;

        /**
         * 工单状态对应-总数
         */
        @Schema(description = "工单状态对应-总数")
        private Long number;

        public WorkSheetData() {
        }

        public WorkSheetData(String status, Long number) {
            this.status = status;
            this.number = number;
        }

        public String getStatus() {
            return status;
        }

        public WorkSheetData setStatus(String status) {
            this.status = status;
            return this;
        }

        public Long getNumber() {
            return number;
        }

        public WorkSheetData setNumber(Long number) {
            this.number = number;
            return this;
        }
    }
}
