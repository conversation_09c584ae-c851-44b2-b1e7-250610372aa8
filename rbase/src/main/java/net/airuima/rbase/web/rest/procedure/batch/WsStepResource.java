package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.query.QueryCondition;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.batch.WsStepDTO;
import net.airuima.rbase.service.procedure.batch.WsStepService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.PaginationUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工单工艺路线快照Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "生产工单工艺路线快照Resource")
@RestController
@RequestMapping("/api/ws-steps")
@AuthorityRegion("生产工单")
@AuthSkip("ICD")
public class WsStepResource extends ProtectBaseResource<WsStep> {

    private final WsStepService wsStepService;

    public WsStepResource(WsStepService wsStepService) {
        this.wsStepService = wsStepService;
        this.mapUri = "/api/ws-steps";
    }

    /**
     * 根据总工单ID获取定制的生产工序信息
     *
     * @param workSheetId 总工单ID
     * @updateTime 2020/12/22 19:13
     * @return: java.util.List<net.airuima.domain.procedure.batch.WsStep>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据总工单ID获取定制的生产工序信息")
    @GetMapping("/byWorkSheetId/{workSheetId}")
    public ResponseEntity<ResponseData<List<WsStep>>> findByWorkSheetId(@PathVariable(value = "workSheetId") Long workSheetId) {
        return ResponseData.ok(wsStepService.findByWorkSheetId(workSheetId));
    }

    /**
     * 根据子工单ID获取定制的生产工序信息
     *
     * @param subWorkSheetId 子工单ID
     * @return List<WsStep> 工序快照列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据子工单ID获取定制的生产工序信息")
    @GetMapping("/bySubWorkSheetId/{subWorkSheetId}")
    public ResponseEntity<ResponseData<List<WsStep>>> findBySubWorkSheetId(@PathVariable(value = "subWorkSheetId") Long subWorkSheetId) {
        return ResponseData.ok(wsStepService.findBySubWorkSheetId(subWorkSheetId));
    }

    /**
     * 修改工单定制工序
     *
     * @param wsStepDto
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "修改工单定制工序")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<String>> updateCustom(@RequestBody WsStepDTO wsStepDto) {
        return ResponseData.<String>ok(wsStepService.updateInstance(wsStepDto,Objects.nonNull(wsStepDto.getSubWorkSheet())?wsStepDto.getSubWorkSheet().getId():wsStepDto.getWorkSheet().getId()).getBody());
    }

    @Override
    public ResponseEntity<WsStep> update(@Valid @RequestBody WsStep entity) throws URISyntaxException {
        return wsStepService.updateEntity(entity, Objects.nonNull(entity.getSubWorkSheet())?entity.getSubWorkSheet().getId():entity.getWorkSheet().getId());
    }


    /**
     * 通过传入的数据，如果存在工单号或者子工单则按工艺路线顺序排序分页
     * @param pageable 分页参数
     * @param qcs 过滤条件
     * @param request
     * <AUTHOR>
     * @date  2021/11/19
     * @return ResponseEntity<ResponseData<List<WsStep>>>
     */
    @Override
    public ResponseEntity<List<WsStep>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        Specification<WsStep> spec = QueryConditionParser.buildSpecificationWithClassName(WsStep.class.getName(), qcs, this.filters, null);
        Page<WsStep> page = wsStepService.findByPage(spec,qcs,pageable,request);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, request.getRequestURI().endsWith("search") ? request.getRequestURI() : request.getRequestURI() + "/search");
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "生产工单工艺路线快照");
    }

}
