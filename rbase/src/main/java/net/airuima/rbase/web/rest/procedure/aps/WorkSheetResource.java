package net.airuima.rbase.web.rest.procedure.aps;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.afterturn.easypoi.view.PoiBaseView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.domain.MetaTable;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.ExportParamDTO;
import net.airuima.dto.UserHabitDTO;
import net.airuima.query.QueryCondition;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.aps.WorkSheetDTO;
import net.airuima.rbase.dto.aps.WorkSheetFullGetDTO;
import net.airuima.rbase.dto.aps.WorkSheetProgressDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.digiwin.WorkSheetMaterialDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.flowable.FlowableTaskCompleteDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetDTO;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO;
import net.airuima.rbase.service.procedure.aps.plugin.ISubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetService;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetSyncService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsGetDTO;
import net.airuima.service.MetaColumnService;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产总工单Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "生产工单Resource")
@RestController
@RequestMapping("/api/work-sheets")
@AuthorityRegion("生产工单")
public class WorkSheetResource extends ProtectBaseResource<WorkSheet> {
    private static final String EXCEPTION = "exception";

    private final WorkSheetService workSheetService;
    private final SubWorkSheetService subWorkSheetService;

    @Autowired
    private IWorkSheetService[] workSheetServices;
    @Autowired
    private IWorkSheetSyncService[] workSheetSyncServices;
    @Autowired
    private ISubWorkSheetService[] subWorkSheetServices;

    public WorkSheetResource(WorkSheetService workSheetService, SubWorkSheetService subWorkSheetService) {
        this.workSheetService = workSheetService;
        this.subWorkSheetService = subWorkSheetService;
        this.mapUri = "/api/work-sheets";
    }

    /**
     * 获取工单对应bom的Sop图片
     * @param id 工单id
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.document.DocumentDTO>
     * <AUTHOR>
     * @since 1.8.1
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "获取工单对应bom的Sop图片")
    @GetMapping("/bom-sop/{id}")
    public ResponseEntity<ResponseData<DocumentDTO>> getWsBomSop(@PathVariable("id") Long id) {
        try{
            return ResponseData.ok(workSheetService.getWsBomSop(id));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    public ResponseEntity<List<WorkSheet>> getAll(Pageable pageable, Long token, HttpServletRequest request) {
        try {
            Map<String, Object> searchParams = QueryConditionParser.getParametersStartingWith(request, "search_");
            List<QueryCondition> qcs = this.prepareQueryCondition(request, searchParams, this.isQuerySuperInclude);
            Method prepareHabit = getClass().getSuperclass().getDeclaredMethod("prepareHabit", Integer.class);
            prepareHabit.setAccessible(Boolean.TRUE);
            Optional<UserHabitDTO> userViewHabit = (Optional<UserHabitDTO>) prepareHabit.invoke(this, VIEW_HABIT);
            Optional<UserHabitDTO> userQueryHabit = (Optional<UserHabitDTO>) prepareHabit.invoke(this, QUERY_HABIT);
            qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals("isOverdue")).findFirst().ifPresent(qcs::remove);
            Specification<WorkSheet> spec = QueryConditionParser.buildSpecificationWithClassName(WorkSheet.class.getName(), qcs, this.filters, null);
            Page<WorkSheet> workSheetPage = workSheetService.find(spec, pageable);
            MetaTable metaTableWithColumn = BeanUtil.getBean(MetaColumnService.class).findMetaTableWithColumn(this.tableName, token);
            HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(workSheetPage, this.mapUri);
            HeaderUtil.add(headers, "qcs", JSON.toJSONString(qcs));
            HeaderUtil.add(headers, "fieldState", JSON.toJSONString(metaTableWithColumn));
            HeaderUtil.add(headers, "viewHabit", userViewHabit.map(JSON::toJSONString).orElse(""));
            HeaderUtil.add(headers, "queryHabit", userQueryHabit.map(JSON::toJSONString).orElse(""));
            return ResponseEntity.ok().headers(headers).body(workSheetPage.getContent());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "getAll", e.getMessage())).build();
        }
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    public ResponseEntity<List<WorkSheet>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        AtomicReference<Boolean> delay = workSheetService.getDelay(qcs);
        if (delay.get() == null) {
            return super.searchQuery(pageable, qcs, request);
        }
        Specification<WorkSheet> spec = QueryConditionParser.buildSpecificationWithClassName(WorkSheet.class.getName(), qcs, this.filters, null);
        Specification<WorkSheet> newSpec = workSheetService.addSpecification(delay);
        spec = spec.and(newSpec);
        Page<WorkSheet> workSheetPage = workSheetService.find(spec, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(workSheetPage, request.getRequestURI().endsWith("search") ? request.getRequestURI() : request.getRequestURI() + "/search");
        return ResponseEntity.ok().headers(headers).body(workSheetPage.getContent());
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or  hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PostMapping({"/v2/exportExcel"})
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {

        if (exportDTO.getExportTemplate() != null && exportDTO.getExportTemplate()) {
            ExcelUtil.createTitleWorkbook(exportDTO.getExportParams(), response, exportDTO.getExcelTitle());
        } else {
            this.exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
            this.exportParams.setFreezeCol(2);
            this.exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
            if (StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")) {
                exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
                exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
            }
            List<QueryCondition> qcs = exportDTO.getQcs();
            AtomicReference<Boolean> delay = workSheetService.getDelay(qcs);
            Specification<WorkSheet> spec = QueryConditionParser.buildSpecificationWithClassName(WorkSheet.class.getName(), exportDTO.getQcs(), this.filters, this.filterReformer);
            if (delay.get() != null) {
                Specification<WorkSheet> newSpec = workSheetService.addSpecification(delay);
                spec = spec.and(newSpec);
            }
            long count = this.getService().count(spec);
            List<ExportParamDTO> exportParamDTOList = exportDTO.getExportParams();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map((s) -> {
                return StringUtils.substringBefore(s.getLabel(), "[[");
            }).map((label) -> {
                return new ExcelExportEntity(label, label);
            }).collect(Collectors.toList());
            if (count <= 100000L) {
                List<WorkSheet> dataList = this.getService().find(spec);
                dataList.forEach(workSheet -> workSheet.setIsOverdue(workSheet.getIsOverdue()).setOverdueTime(workSheet.getOverdueTime()).setProgress(workSheet.getProgress()));

                List<Map<String, String>> result = new ArrayList<>();
                if (!exportParamDTOList.isEmpty()) {
                    ExcelUtil.dealExcelFilterData(dataList, exportParamDTOList, result);
                }
                result.forEach(map -> {
                    if (null != map.get("工单进度")) {
                        map.put("工单进度", NumberUtils.divide(NumberUtils.multiply(Double.valueOf(map.get("工单进度")), Constants.INT_ONE_HUNDRED).doubleValue(), Constants.INT_ONE, Constants.INT_FOUR).doubleValue() + "%");
                    }
                });
                modelMap.put("mapList", result);
                modelMap.put("EntityList", excelExportEntityList);
                modelMap.put("params", this.exportParams);
                modelMap.put("fileName", URLEncoder.encode(exportDTO.getExcelTitle(), "utf-8"));
                response.setHeader("message", "export!");
                PoiBaseView.render(modelMap, request, response, "easypoiMapExcelView");
            } else {
                if (RUNNING_EXPORT.get()) {
                    response.setHeader("message", "running!");
                    return;
                }

                String uuid = UUID.randomUUID().toString().replace("-", "");
                Specification<WorkSheet> finalSpec = spec;
                CompletableFuture<Void> async = CompletableFuture.runAsync(() -> {
                    RUNNING_EXPORT.set(true);
                    RabbitTemplate rabbitTemplate = BeanUtil.getBean(RabbitTemplate.class);
                    Workbook workbook = ExcelExportUtil.exportBigExcel(this.exportParams, excelExportEntityList, new IExcelExportServer() {
                        private int pageNumber = 0;

                        public List<Object> selectListForExcelExport(Object queryParams, int page) {
                            Pageable pageable = PageRequest.of(this.pageNumber / 100000, 100000);
                            List<WorkSheet> dataList = workSheetService.find(finalSpec, pageable).getContent();
                            this.pageNumber += 100000;
                            if (dataList.isEmpty()) {
                                return null;
                            } else {
                                dataList.forEach(workSheet -> workSheet.setIsOverdue(workSheet.getIsOverdue()).setOverdueTime(workSheet.getOverdueTime()).setProgress(workSheet.getProgress()));

                                List<Map<String, String>> result = new ArrayList<>();
                                if (!exportParamDTOList.isEmpty()) {
                                    ExcelUtil.dealExcelFilterData(dataList, exportParamDTOList, result);
                                }
                                result.forEach(map -> {
                                    if (null != map.get("工单进度")) {
                                        map.put("工单进度", NumberUtils.divide(NumberUtils.multiply(Double.parseDouble(map.get("工单进度")), Constants.INT_ONE_HUNDRED).doubleValue(), Constants.INT_ONE, Constants.INT_FOUR).doubleValue() + "%");
                                    }
                                });
                                return new ArrayList<>(result);
                            }
                        }
                    }, null);

                    try {
                        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls") ? ".xls" : ".xlsx";
                        String fileName = exportDTO.getExcelTitle() + "-" + uuid + prefix;
                        FileOutputStream fileOutputStream = new FileOutputStream(FileUtil.TMPDIR + fileName);
                        workbook.write(fileOutputStream);
                        fileOutputStream.flush();
                        workbook.close();
                        fileOutputStream.close();
                        rabbitTemplate.convertAndSend("amq.topic", uuid, fileName);
                    } catch (Exception var10) {
                        var10.printStackTrace();
                    }

                });
                async.whenCompleteAsync((unused, throwable) -> {
                    RUNNING_EXPORT.set(false);
                });
                response.setHeader("message", "wait!");
                response.setHeader("id", uuid);
            }
        }
    }

    /**
     * 新增总工单
     *
     * @param workSheetDto 总工单DTO
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "新增生产工单")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<List<CreateCascadeWsGetDTO>>> createCustom(@Valid @RequestBody WorkSheetDTO workSheetDto) throws URISyntaxException {
        try {
            if (null != workSheetDto.getId()) {
                throw new ResponseException("error.IdExists", "A new entity cannot already have an ID");
            }
            WorkSheetResDTO workSheetResDto = workSheetServices[0].saveInstance(workSheetDto);
            if (Constants.KO.equals(workSheetResDto.getStatus())) {
                return ResponseData.error("exception", workSheetResDto.getMessage());
            }
            WorkSheet workSheet = workSheetResDto.getWorkSheet();
            workSheetDto.setId(workSheet.getId());
            workSheetServices[0].afterUpdateInstance(workSheetDto);
            return ResponseData.ok(workSheetResDto.getCascadeWsGetDTOList());
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 修改总工单
     *
     * @param workSheetDto 总工单DTO
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改生产总工单")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<WorkSheet>> updateCustom(@Valid @RequestBody WorkSheetDTO workSheetDto) throws URISyntaxException {
        if (null == workSheetDto.getId()) {
            throw new ResponseException("error.NullId", "Invalid id");
        }
        try {
            WorkSheetResDTO workSheetResDto = workSheetServices[0].updateInstance(workSheetDto);
            if (Constants.KO.equals(workSheetResDto.getStatus())) {
                return ResponseData.error("exception", workSheetResDto.getMessage());
            }
            workSheetServices[0].afterUpdateInstance(workSheetDto);
            return ResponseData.ok(workSheetResDto.getWorkSheet());

        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过总工单号模糊查询总工单列表
     *
     * @param text 总工单号
     * @param size 行数
     * @return List<WorkSheet>
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过总工单号模糊查询总工单列表")
    @GetMapping("/bySerialNumber")
    public ResponseEntity<ResponseData<List<WorkSheet>>> bySerialNumber(@RequestParam(value = "text", required = false) String text,
                                                                        @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(workSheetService.findPageBySerialNumber(text, size));
    }

    /**
     * 通过工单ID获取工单、投料单及工序快照信息
     *
     * @param id 总工单ID
     * @return WorkSheetResDTO
     * <AUTHOR>
     * @date 2021-03-31
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工单主键ID获取工单、物料清单及工序快照信息", parameters = {
            @Parameter(name = "id", description = "工单主键ID", required = true, schema = @Schema(type = "integer", format = "int64"), in = ParameterIn.PATH)
    })
    @GetMapping("/{id}/materials/steps")
    public ResponseEntity<ResponseData<WorkSheetFullGetDTO>> findByWorkSheetId(@PathVariable(value = "id") Long id) {
        return ResponseData.ok(workSheetService.findBaseInfoByWorkSheetId(id));
    }

    /**
     * 通过工单编码获取工单，投料单基础信息
     *
     * @param serialNumber 工单号
     * @return net.airuima.dto.digiwin.WorkSheetMaterialDTO
     * <AUTHOR>
     * @date 2021/8/9
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Deprecated
    @Operation(summary = "工单审批时通过工单号获取工单、投料单", hidden = true)
    @GetMapping("/get-worksheet-material")
    public ResponseEntity<ResponseData<WorkSheetMaterialDTO>> getWorksheetMaterial(@RequestParam(value = "serialNumber") String serialNumber) {
        return ResponseData.ok(workSheetService.getWorksheetMaterial(serialNumber));
    }

    /**
     * 生产工单同步
     *
     * @param syncWorkSheetDtoList 上传的需要同步的工单列表
     * @return List<SyncResultDTO>
     * <AUTHOR>
     * @date 2021-06-03
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "同步工单信息")
    @PostMapping("/syncWorkSheet")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncWorkSheet(@RequestBody List<SyncWorkSheetDTO> syncWorkSheetDtoList) {
        try {
            return ResponseData.ok(workSheetSyncServices[0].syncWorkSheet(syncWorkSheetDtoList));
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 修改工单状态
     * f
     *
     * @param id     总工单ID
     * @param status 0:暂停;1:恢复
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * <AUTHOR>
     * @date 2021-07-22
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改工单状态(0:暂停;1:恢复)")
    @PutMapping("/updateStatus")
    public ResponseEntity<ResponseData<WorkSheet>> updateStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        try {
            WorkSheet workSheet = workSheetService.updateStatus(id, status);
            return ResponseData.ok(workSheet);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 修改工单状态(flowable方法回调)
     *
     * @param serialNumber 工单号
     * @param status       0:暂停;1:恢复
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * @date 2023-3-11
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改工单状态(0:暂停;1:恢复)")
    @PostMapping("/status")
    public ResponseEntity<ResponseData<WorkSheet>> updateStatusBySerialNumber(@RequestParam("serialNumber") String serialNumber, @RequestParam("status") Integer status) {
        try {
            WorkSheet workSheet = workSheetService.updateStatusBySerialNumber(serialNumber, status);
            return ResponseData.ok(workSheet);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 暂停工单api(事件回调)
     *
     * @param serialNumber 工单号
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * @date 2023-3-11
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "暂停工单状态")
    @PostMapping("/stopStatus")
    public ResponseEntity<ResponseData<WorkSheet>> stopStatus(@RequestParam("serialNumber") String serialNumber) {
        try {
            WorkSheet workSheet = workSheetService.updateStatusBySerialNumber(serialNumber, ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
            return ResponseData.ok(workSheet);
        } catch (ResponseException e) {
            e.printStackTrace();
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 恢复工单api(事件回调)
     *
     * @param serialNumber 工单号
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * @date 2023-3-11
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "恢复工单状态")
    @PostMapping("/recoverStatus")
    public ResponseEntity<ResponseData<WorkSheet>> recoverStatus(@RequestParam("serialNumber") String serialNumber) {
        try {
            WorkSheet workSheet = workSheetService.updateStatusBySerialNumber(serialNumber, ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
            return ResponseData.ok(workSheet);
        } catch (ResponseException e) {
            e.printStackTrace();
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 修改工单状态为取消
     *
     * @param id 总工单ID
     * @return ResponseEntity<ResponseData < WorkSheet>>
     * <AUTHOR>
     * @date 2022-12-28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改工单状态为取消")
    @PutMapping("/status/cancel/{id}")
    public ResponseEntity<ResponseData<Void>> cancel(@PathVariable("id") Long id) {
        try {
            workSheetService.cancel(id);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 重写总工单删除按钮，校验工单状态是否是已下单，同时工单未投产
     *
     * @param id 总工单id
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            workSheetService.deleteWorkSheet(id);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
        } catch (ResponseException badRequestAlertException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(badRequestAlertException.getErrorKey(), badRequestAlertException.getMessage())).build();
        }
    }

    /**
     * 修改总工单的备注
     *
     * @param id   工单id
     * @param note 备注
     * @return
     * <AUTHOR>
     * @date 2022/1/18
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PutMapping({"/worksheet-note"})
    public ResponseEntity<ResponseData<Void>> updateWsNote(@RequestParam("id") Long id, @RequestParam("note") String note) {
        BaseDTO response = workSheetService.updateWsNote(id, note);
        if (Constants.KO.equals(response.getStatus())) {
            return ResponseData.error("exception", response.getMessage());
        } else {
            return ResponseData.save();
        }
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "中途结单")
    @PostMapping("/halfwayFinish")
    public ResponseEntity<ResponseData<Void>> halfwayFinish(@RequestBody SubWorkSheetDTO subWorkSheetDto) {
        try {
            BaseDTO baseDto = subWorkSheetServices[0].forceFinishWorkSheet(subWorkSheetDto);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (Exception e) {
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 中途结单(flowable方法回调)
     *
     * @param serialNumber
     * @param statementReason
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "中途结单")
    @PostMapping("/processScrap")
    public ResponseEntity<ResponseData<Void>> processScrap(@RequestParam("serialNumber") String serialNumber, @RequestParam("statementReason") String statementReason) {
        BaseDTO baseDto = subWorkSheetService.processScrap(serialNumber, statementReason);
        if (Constants.KO.equals(baseDto.getStatus())) {
            return ResponseData.error("exception", baseDto.getMessage());
        }
        return ResponseData.save();
    }

    /**
     * BOM审批
     *
     * @param id                      工单ID
     * @param flowableTaskCompleteDTO 工作流任务完成DTO
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/27
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "BOM审批")
    @Parameters({
            @Parameter(name = "id", description = "工单ID", required = true),
            @Parameter(name = "flowableTaskCompleteDTO", description = "工作流任务DTO", required = true)
    })
    @PostMapping("/task/bomApproval/{id}")
    public ResponseEntity<ResponseData<Void>> bomApproval(@PathVariable("id") Long id, @RequestBody FlowableTaskCompleteDTO flowableTaskCompleteDTO) {
        try {
            workSheetService.bomApproval(id, flowableTaskCompleteDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 定制工艺路线
     *
     * @param workSheetDTO 工单DTO
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "定制工艺路线")
    @Parameters({
            @Parameter(name = "workSheetDTO", description = "工单DTO", required = true)
    })
    @PostMapping("/task/remakeProcess")
    public ResponseEntity<ResponseData<Void>> remakeProcess(@RequestBody WorkSheetDTO workSheetDTO) {
        try {
            workSheetService.remakeProcess(workSheetDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工艺路线审批
     *
     * @param id                      工单ID
     * @param flowableTaskCompleteDTO 工作流任务完成DTO
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "工艺路线审批")
    @Parameters({
            @Parameter(name = "id", description = "工单ID", required = true),
            @Parameter(name = "flowableTaskCompleteDTO", description = "工作流任务完成DTO", required = true)
    })
    @PostMapping("/task/processApproval/{id}")
    public ResponseEntity<ResponseData<Void>> processApproval(@PathVariable("id") Long id, @RequestBody FlowableTaskCompleteDTO flowableTaskCompleteDTO) {
        try {
            workSheetService.processApproval(id, flowableTaskCompleteDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过工单ID获取子工单及返工单工单进度
     *
     * @param workSheetId 工单ID
     * @return List<WsSubProgressDTO>
     * <AUTHOR>
     * @date 2022-12-29
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工单主键ID获取子工单及返工单生产进度", parameters = {
            @Parameter(name = "workSheetId", required = true, description = "工单主键ID", schema = @Schema(type = "integer", format = "int64"), in = ParameterIn.PATH)
    })
    @GetMapping("/progress/{workSheetId}")
    public ResponseEntity<ResponseData<List<WorkSheetProgressDTO>>> queryProgressByWorkSheetId(@PathVariable(value = "workSheetId") Long workSheetId) {
        return ResponseData.ok(workSheetService.findProgressByWorkSheetIdAndDeleted(workSheetId));
    }

    /**
     * 工单增量导入
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PostMapping({"/importTableExcel"})
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        try {
            BaseDTO baseDto = workSheetService.importTableExcel(file, response);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
            } else {
                return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
            }
        } catch (ResponseException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "import", e.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "import", e.getMessage())).build();
        }
    }


    /**
     * 工单步骤卡打印
     *
     * @param id           工单ID
     * @param templateCode 模板编码
     * @param response     响应
     * @throws Exception 异常
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/step-card/{id}/print/{templateCode}")
    public void exportWsStepCard(@PathVariable Long id, @PathVariable String templateCode, HttpServletResponse response) throws Exception {
        workSheetServices[0].printWorkSheetStepCard(id, null, templateCode, response);
    }

    /**
     * 同步WMS入库
     *
     * @param id 工单id
     */
    @Operation(summary = "同步WMS入库")
    @PostMapping("/sync-wms/{id}")
    public ResponseEntity<ResponseData<Void>> syncWms(@PathVariable("id") Long id) {
        try {
            workSheetServices[0].syncWms(id);
            return ResponseData.ok("syncWms.success","同步WMS入库成功");
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 查询未完成的工单
     */
    @Operation(summary = "")
    @GetMapping("/unfinish")
    public ResponseEntity<ResponseData<List<WorkSheet>>> findNoFinishWorkSheet() {
        return ResponseData.ok(workSheetService.findNoFinishWorkSheet());
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工单");
    }

}
