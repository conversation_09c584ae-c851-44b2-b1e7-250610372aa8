package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.SnStepFppHistory;
import net.airuima.rbase.service.procedure.quality.SnStepFppHistoryService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "单支FPP历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-step-fpp-histories")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("StepFpp")
@AuthSkip("CUI")
public class SnStepFppHistoryResource extends ProtectBaseResource<SnStepFppHistory> {
    private static final String MODULE = "单支FPP历史";
    private final SnStepFppHistoryService snStepFppHistoryService;

    public SnStepFppHistoryResource(SnStepFppHistoryService snStepFppHistoryService) {
        this.snStepFppHistoryService = snStepFppHistoryService;
        this.mapUri = "/api/sn-step-fpp-histories";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
