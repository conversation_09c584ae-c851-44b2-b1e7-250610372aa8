package net.airuima.rbase.web.rest.report.dto.worksheethistorydata.enums;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @version 1.8.0
 * @since 1.8.0
 */
public enum OrderTypeEnum {
    SALES_ORDER(0, "销售订单"),
    WORK_SHEET(1, "工单"),
    SUB_WORK_SHEET(2, "子工单"),
    CONTAINER(3, "容器"),
    SN(4, "sn"),
    MATERIAL(5, "物料"),
    EQUIPMENT(6, "设备"),
    CONSUMABLE(7, "易损件"),
    STAFF(8, "人员"),
    WS_REWORK(9, "返工单");

    private final int code;
    private final String description;

    OrderTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OrderTypeEnum fo(int code) {
        for (OrderTypeEnum type : OrderTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的单号类型代码: " + code);
    }
}