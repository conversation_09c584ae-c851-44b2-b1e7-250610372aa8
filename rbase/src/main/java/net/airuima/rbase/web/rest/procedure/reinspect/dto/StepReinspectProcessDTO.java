package net.airuima.rbase.web.rest.procedure.reinspect.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检处理结果DTO
 * <AUTHOR>
 */
@Schema(description = "工序不良复检处理结果DTO")
public class StepReinspectProcessDTO {

    /**
     * 复检历史Id
     */
    @Schema(description = "复检历史Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 复检后不良项目Id(如果放行可以没有)
     */
    @Schema(description = "复检后不良项目Id(如果放行可以没有)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetUnqualifiedItemId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;


    /**
     * 处理结果:0:放行;1:返工;2:报废
     */
    @Schema(description = "处理结果:0:放行;1:返工;2:报废")
    private Integer result;


    public Long getId() {
        return id;
    }

    public StepReinspectProcessDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getTargetUnqualifiedItemId() {
        return targetUnqualifiedItemId;
    }

    public StepReinspectProcessDTO setTargetUnqualifiedItemId(Long targetUnqualifiedItemId) {
        this.targetUnqualifiedItemId = targetUnqualifiedItemId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public StepReinspectProcessDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getResult() {
        return result;
    }

    public StepReinspectProcessDTO setResult(Integer result) {
        this.result = result;
        return this;
    }
}
