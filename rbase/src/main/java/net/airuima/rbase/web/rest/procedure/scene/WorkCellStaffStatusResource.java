package net.airuima.rbase.web.rest.procedure.scene;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.scene.WorkCellStaffStatus;
import net.airuima.rbase.service.procedure.scene.WorkCellStaffStatusService;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/15
 */
@Tag(name = "员工工位状态Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-cell-staff-statuses")
@AuthorityRegion("生产现场")
@FuncInterceptor("SceneModel")
public class WorkCellStaffStatusResource extends ProtectBaseResource<WorkCellStaffStatus> {
    private final WorkCellStaffStatusService workCellStaffStatusService;

    public WorkCellStaffStatusResource(WorkCellStaffStatusService workCellStaffStatusService) {
        this.workCellStaffStatusService = workCellStaffStatusService;
        this.mapUri = "/api/work-cell-staff-statuses";
    }

    /**
     * 通过员工工位状态ID重置最新登陆时间
     * @param id 工工位状态ID
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过员工工位状态ID重置最新登陆时间")
    @PutMapping("/latestLoginTime/{id}")
    public ResponseEntity<ResponseData<WorkCellStaffStatus>> resetLatestLoginTime(@PathVariable("id")Long id){
        try{
            WorkCellStaffStatus workCellStaffStatus = workCellStaffStatusService.resetLatestLoginTime(id);
            return ResponseData.ok(workCellStaffStatus);
        }catch (Exception e){
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览员工工位状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建员工工位状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改员工工位状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除员工工位状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入员工工位状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出员工工位状态";
        }
        return "";
    }
}
