package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量按产线统计查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量按产线统计查询结果DTO")
public class ProductionCapacityWorkLineDTO {

    /**
     * 产线名字
     */
    @Schema(description = "产线名字")
    private String name;


    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;

    public String getName() {
        return name;
    }

    public ProductionCapacityWorkLineDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityWorkLineDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public ProductionCapacityWorkLineDTO() {
    }

    public ProductionCapacityWorkLineDTO(String name, Long number) {
        this.name = name;
        this.number = number;
    }
}
