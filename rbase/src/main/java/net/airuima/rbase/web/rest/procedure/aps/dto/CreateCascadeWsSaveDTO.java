package net.airuima.rbase.web.rest.procedure.aps.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 级联下单待保存参数信息DTO
 * <AUTHOR>
 */
@Schema(description = "级联下单待保存参数信息DTO")
public class CreateCascadeWsSaveDTO {

    /**
     * 父级工单号
     */
    @Schema(description = "父级工单号")
    private String serialNumber;

    /**
     * 子级工单待保存参数列表
     */
    @Schema(description = "子级工单待保存参数列表")
    private List<SubordinateWorkSheetInfo> subordinateWorkSheetInfoList;

    public String getSerialNumber() {
        return serialNumber;
    }

    public CreateCascadeWsSaveDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public List<SubordinateWorkSheetInfo> getSubordinateWorkSheetInfoList() {
        return subordinateWorkSheetInfoList;
    }

    public CreateCascadeWsSaveDTO setSubordinateWorkSheetInfoList(List<SubordinateWorkSheetInfo> subordinateWorkSheetInfoList) {
        this.subordinateWorkSheetInfoList = subordinateWorkSheetInfoList;
        return this;
    }

    /**
     * 子级工单待保存参数
     */
    @Schema(description = "子级工单待保存参数")
    public static class SubordinateWorkSheetInfo{
        /**
         * 工单号
         */
        @Schema(description = "工单号")
        private String serialNumber;

        /**
         * 产品谱系ID
         */
        @Schema(description = "产品谱系ID")
        private Long pedigreeId;

        /**
         * 工艺路线id
         */
        @Schema(description = "工艺路线id")
        private Long workFlowId;

        /**
         * 物料清单id
         */
        @Schema(description = "物料清单id")
        private Long bomInfoId;

        /**
         * 组织架构ID
         */
        @NotNull
        @Schema(description = "组织架构ID")
        private Long organizationId;

        /**
         * 生产线ID
         */
        @Schema(description = "生产线ID")
        private Long workLineId;

        /**
         * 客户ID
         */
        @Schema(description = "客户ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long clientId;

        /**
         * 投产数
         */
        @Schema(description = "投产数")
        private int number;

        /**
         * 计划开工日期
         */
        @Schema(description = "计划开工日期")
        private LocalDate planStartDate;

        /**
         * 计划结单日期
         */
        @Schema(description = "计划完工日期")
        private LocalDate planEndDate;

        /**
         * 优先级
         */
        @Schema(description = "优先级", required = true)
        private int priority;

        /**
         * 交付日期
         */
        @Schema(description = "交付日期", required = true)
        private LocalDate deliveryDate;

        /**
         * 是否自动生成子工单
         */
        @Schema(description = "是否自动生成子工单")
        private Boolean isAutoGenerateSubWs = false;

        public String getSerialNumber() {
            return serialNumber;
        }

        public SubordinateWorkSheetInfo setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public Long getPedigreeId() {
            return pedigreeId;
        }

        public SubordinateWorkSheetInfo setPedigreeId(Long pedigreeId) {
            this.pedigreeId = pedigreeId;
            return this;
        }

        public Long getWorkFlowId() {
            return workFlowId;
        }

        public SubordinateWorkSheetInfo setWorkFlowId(Long workFlowId) {
            this.workFlowId = workFlowId;
            return this;
        }

        public Long getBomInfoId() {
            return bomInfoId;
        }

        public SubordinateWorkSheetInfo setBomInfoId(Long bomInfoId) {
            this.bomInfoId = bomInfoId;
            return this;
        }

        public Long getOrganizationId() {
            return organizationId;
        }

        public SubordinateWorkSheetInfo setOrganizationId(Long organizationId) {
            this.organizationId = organizationId;
            return this;
        }

        public Long getWorkLineId() {
            return workLineId;
        }

        public SubordinateWorkSheetInfo setWorkLineId(Long workLineId) {
            this.workLineId = workLineId;
            return this;
        }

        public Long getClientId() {
            return clientId;
        }

        public SubordinateWorkSheetInfo setClientId(Long clientId) {
            this.clientId = clientId;
            return this;
        }

        public int getNumber() {
            return number;
        }

        public SubordinateWorkSheetInfo setNumber(int number) {
            this.number = number;
            return this;
        }

        public LocalDate getPlanStartDate() {
            return planStartDate;
        }

        public SubordinateWorkSheetInfo setPlanStartDate(LocalDate planStartDate) {
            this.planStartDate = planStartDate;
            return this;
        }

        public LocalDate getPlanEndDate() {
            return planEndDate;
        }

        public SubordinateWorkSheetInfo setPlanEndDate(LocalDate planEndDate) {
            this.planEndDate = planEndDate;
            return this;
        }

        public int getPriority() {
            return priority;
        }

        public SubordinateWorkSheetInfo setPriority(int priority) {
            this.priority = priority;
            return this;
        }

        public LocalDate getDeliveryDate() {
            return deliveryDate;
        }

        public SubordinateWorkSheetInfo setDeliveryDate(LocalDate deliveryDate) {
            this.deliveryDate = deliveryDate;
            return this;
        }

        public Boolean getIsAutoGenerateSubWs() {
            return isAutoGenerateSubWs;
        }

        public SubordinateWorkSheetInfo setIsAutoGenerateSubWs(Boolean autoGenerateSubWs) {
            isAutoGenerateSubWs = autoGenerateSubWs;
            return this;
        }
    }
}
