package net.airuima.rbase.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
@Schema(description = "（子）工单工序指定工位DTO")
public class WsStepWorkCellSaveDTO {

    /**
     * （子）工单ID
     */
    @Schema(description = "（子）工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工序指定工位信息
     */
    @Schema(description = "工序指定工位信息")
    private List<WsStepWorkCellInfo> wsStepWorkCellInfos;

    public Long getId() {
        return id;
    }

    public WsStepWorkCellSaveDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public List<WsStepWorkCellInfo> getWsStepWorkCellInfos() {
        return wsStepWorkCellInfos;
    }

    public WsStepWorkCellSaveDTO setWsStepWorkCellInfos(List<WsStepWorkCellInfo> wsStepWorkCellInfos) {
        this.wsStepWorkCellInfos = wsStepWorkCellInfos;
        return this;
    }

    public static class WsStepWorkCellInfo{

        /**
         * 工序ID
         */
        @Schema(description = "工序ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long stepId;

        /**
         * 工位id列表
         */
        @Schema(description = "工位id列表")
        private List<Long> workCellIds;

        public Long getStepId() {
            return stepId;
        }

        public WsStepWorkCellInfo setStepId(Long stepId) {
            this.stepId = stepId;
            return this;
        }

        public List<Long> getWorkCellIds() {
            return workCellIds;
        }

        public WsStepWorkCellInfo setWorkCellIds(List<Long> workCellIds) {
            this.workCellIds = workCellIds;
            return this;
        }
    }
}
