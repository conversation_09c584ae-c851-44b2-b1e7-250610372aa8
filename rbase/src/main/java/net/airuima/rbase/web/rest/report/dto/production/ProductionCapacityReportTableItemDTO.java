package net.airuima.rbase.web.rest.report.dto.production;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计表格明细查询结果 DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量统计表格明细查询结果DTO")
public class ProductionCapacityReportTableItemDTO {

    /**
     * 完工时间
     */
    @Schema(description = "完工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完工时间", orderNum = "1", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;


    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "2")
    private String workSheetSerialNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "3")
    private String subWorkSheetSerialNumber;

    /**
     * 工单状态 3生产完成 5 异常结单
     */
    @Schema(description = "工单状态 3生产完成 5 异常结单")
    @Excel(name = "工单状态", orderNum = "4" , replace = {"生产完成_3", "异常结单_5"})
    private Integer status;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "5")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "6")
    private String pedigreeName;


    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Excel(name = "规格型号", orderNum = "7")
    private String specification;

    /**
     * 生产部门
     */
    @Schema(description = "生产部门")
    @Excel(name = "生产部门", orderNum = "8")
    private String organizationName;

    /**
     * 生产线名字
     */
    @Schema(description = "生产线名字")
    @Excel(name = "生产线名字", orderNum = "9")
    private String workLineName;


    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数", orderNum = "10")
    private Long number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "11")
    private Long qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Excel(name = "不合格数", orderNum = "12")
    private Long unqualifiedNumber;


    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public ProductionCapacityReportTableItemDTO setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public ProductionCapacityReportTableItemDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public ProductionCapacityReportTableItemDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public ProductionCapacityReportTableItemDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public ProductionCapacityReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public ProductionCapacityReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public ProductionCapacityReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public ProductionCapacityReportTableItemDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public ProductionCapacityReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ProductionCapacityReportTableItemDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public ProductionCapacityReportTableItemDTO setUnqualifiedNumber(Long unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }
}
