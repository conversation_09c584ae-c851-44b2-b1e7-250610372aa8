package net.airuima.rbase.web.rest.base.wearingpart;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.service.base.wearingpart.PedigreeStepWearingPartGroupService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司 产品谱系工序易损件规则Resource
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Tag(name = "产品谱系工序易损件规则Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-step-wearing-part-groups")
@AuthorityRegion("易损件管理")
@FuncInterceptor("WearingPart")
public class PedigreeStepWearingPartGroupResource extends ProtectBaseResource<PedigreeStepWearingPartGroup> {
    private static final String MODULE = "易损件使用规则配置";
    private final PedigreeStepWearingPartGroupService pedigreeStepWearingPartGroupService;

    public PedigreeStepWearingPartGroupResource(PedigreeStepWearingPartGroupService pedigreeStepWearingPartGroupService) {
        this.pedigreeStepWearingPartGroupService = pedigreeStepWearingPartGroupService;
        this.mapUri = "/api/pedigree-step-wearing-part-groups";
    }

    @Operation(summary = "新增易损件使用规则")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeStepWearingPartGroup> create(@Valid @RequestBody PedigreeStepWearingPartGroup entity) throws URISyntaxException {
        try{
            entity = pedigreeStepWearingPartGroupService.saveInstance(entity);
            return ResponseEntity.created(new URI(mapUri + "/" + entity.getId())).headers(HeaderUtil.createdAlert(entityName, entity.getId().toString())).body(entity);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }


    @Operation(summary = "修改易损件使用规则")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeStepWearingPartGroup> update(@Valid @RequestBody PedigreeStepWearingPartGroup entity) throws URISyntaxException {
        try{
            entity = pedigreeStepWearingPartGroupService.saveInstance(entity);
            return ResponseEntity.ok()
                    .headers(HeaderUtil.updatedAlert(entityName, entity.getId().toString()))
                    .body(entity);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
