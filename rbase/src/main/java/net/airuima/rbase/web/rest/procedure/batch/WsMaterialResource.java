package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.batch.WsMaterialDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialExchangeDTO;
import net.airuima.rbase.service.procedure.batch.WsMaterialService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单投料单Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工单投料单Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-materials")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial")
@AuthSkip("IE")
public class WsMaterialResource extends ProtectBaseResource<WsMaterial> {
    private static final String MODULE = "工单投料单";
    private static final String EXCEPTION = "exception";
    private final WsMaterialService wsMaterialService;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;

    public WsMaterialResource(WsMaterialService wsMaterialService) {
        this.wsMaterialService = wsMaterialService;
        this.mapUri = "/api/ws-materials";
    }

    /**
     * @throws
     * @description 根据总工单ID获取投料单信息
     * <AUTHOR>
     * @param: workSheetId 总工单ID
     * @updateTime 2020/12/22 19:17
     * @return: java.util.List<net.airuima.rbase.domain.procedure.batch.WsMaterial>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据总工单ID获取投料单信息")
    @GetMapping("/byWorkSheetId/{workSheetId}")
    public ResponseEntity<ResponseData<List<WsMaterial>>> findByWorkSheetId(@PathVariable(value = "workSheetId") Long workSheetId) {
        return ResponseData.ok(wsMaterialService.findByWorkSheetId(workSheetId));
    }

    /**
     * @throws
     * @description 根据工单ID获取投料单原始物料信息
     * <AUTHOR>
     * @param: workSheetId 工单id
     * @updateTime 2020/12/22 19:17
     * @return: java.util.List<net.airuima.rbase.domain.procedure.batch.WsMaterial>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据工单ID获取投料单原始物料信息")
    @GetMapping("/distinctByWorkSheetId/{workSheetId}")
    public ResponseEntity<ResponseData<List<WsMaterial>>> distinctByWorkSheetId(@PathVariable(value = "workSheetId") Long workSheetId) {
        return ResponseData.ok(wsMaterialService.distinctByWorkSheetId(workSheetId));
    }

    /**
     * 通过工单和原始物料ID获取投料单信息
     * @param workSheetId 工单id
     * @param originMaterialId 原始物料id
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsMaterial>
     * <AUTHOR>
     * @date 2023/3/29
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据工单ID、原始物料获取替换料信息")
    @GetMapping("/byWorkSheetId/{workSheetId}/originMaterialId/{originMaterialId}")
    public ResponseEntity<ResponseData<List<WsMaterial>>> findByWorkSheetIdAndOriginMaterialId(@PathVariable(value = "workSheetId") Long workSheetId, @PathVariable(value = "originMaterialId") Long originMaterialId) {
        return ResponseData.ok(wsMaterialService.findByWorkSheetIdAndOriginMaterialId(workSheetId, originMaterialId));
    }

    /**
     * 同步工单换料数据 (目前仅sap使用)
     * <AUTHOR>
     * @param workSheetMaterialExchangeDtoList  上传的工单换料信息
     * @return List<SapBaseDTO>
     * @date 2021-06-04
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "同步工单换料数据")
    @PostMapping("/workSheetMaterialExchangeSync")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> workSheetMaterialExchangeSync(@RequestBody List<SyncWorkSheetMaterialExchangeDTO> workSheetMaterialExchangeDtoList){
        try {
            return ResponseData.ok(wsMaterialServices[0].workSheetMaterialExchangeSync(workSheetMaterialExchangeDtoList));
        }catch (Exception e){
            return ResponseData.error(e);
        }
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增工单投料单")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<WsMaterial>> create(@RequestBody WsMaterialDTO wsMaterialDto){
        try {
            return ResponseData.ok(wsMaterialService.saveInstance(wsMaterialDto).getBody());
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "修改工单投料单")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<WsMaterial>> update(@RequestBody WsMaterialDTO wsMaterialDto){
        try{
            return ResponseData.ok(wsMaterialService.updateInstance(wsMaterialDto).getBody());
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "删除工单投料单")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try{
            return wsMaterialService.deleteInstance(id);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()),EXCEPTION,e.toString())).build();

        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
