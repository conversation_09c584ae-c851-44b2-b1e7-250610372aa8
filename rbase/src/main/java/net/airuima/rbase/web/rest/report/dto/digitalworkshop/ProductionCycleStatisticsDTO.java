package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "数字车间驾驶舱-产品谱系平均时长")
public class ProductionCycleStatisticsDTO implements Serializable {


    /**
     * 产品谱系平均使用时长列表
     */
    @Schema(description = "产品谱系平均使用时长列表")
    private List<PedigreeAverageTimeConsumptionInfo> pedigreeAverageTimeConsumptionInfos;

    /**
     * 产品谱系工序平均使用时长列表
     */
    @Schema(description = "产品谱系工序平均使用时长列表")
    private List<PedigreeStepAverageTimeConsumptionInfo> pedigreeStepAverageTimeConsumptionInfos;


    public List<PedigreeAverageTimeConsumptionInfo> getPedigreeAverageTimeConsumptionInfos() {
        return pedigreeAverageTimeConsumptionInfos;
    }

    public ProductionCycleStatisticsDTO setPedigreeAverageTimeConsumptionInfos(List<PedigreeAverageTimeConsumptionInfo> pedigreeAverageTimeConsumptionInfos) {
        this.pedigreeAverageTimeConsumptionInfos = pedigreeAverageTimeConsumptionInfos;
        return this;
    }

    public List<PedigreeStepAverageTimeConsumptionInfo> getPedigreeStepAverageTimeConsumptionInfos() {
        return pedigreeStepAverageTimeConsumptionInfos;
    }

    public ProductionCycleStatisticsDTO setPedigreeStepAverageTimeConsumptionInfos(List<PedigreeStepAverageTimeConsumptionInfo> pedigreeStepAverageTimeConsumptionInfos) {
        this.pedigreeStepAverageTimeConsumptionInfos = pedigreeStepAverageTimeConsumptionInfos;
        return this;
    }


    /**
     * 产品谱系工序平均使用时长
     */
    @Schema(description = "产品谱系工序平均使用时长")
    public static class PedigreeStepAverageTimeConsumptionInfo {

        /**
         * 谱系id
         */
        @Schema(description = "谱系id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long pedigreeId;
        /**
         * 谱系编码
         */
        @Schema(description = "谱系编码")
        private String pedigreeCode;
        /**
         * 谱系名称
         */
        @Schema(description = "谱系名称")
        private String pedigreeName;


        private List<StepAverageTimeConsumptionInfo> stepAverageTimeConsumptionInfoList;

        public PedigreeStepAverageTimeConsumptionInfo() {
        }

        public PedigreeStepAverageTimeConsumptionInfo(StepAverageTimeConsumptionInfo stepAverageTimeConsumptionInfo, List<StepAverageTimeConsumptionInfo> stepAverageTimeConsumptionInfoList) {
            this.pedigreeId = stepAverageTimeConsumptionInfo.getPedigreeId();
            this.pedigreeCode = stepAverageTimeConsumptionInfo.getPedigreeCode();
            this.pedigreeName = stepAverageTimeConsumptionInfo.getPedigreeName();
            this.stepAverageTimeConsumptionInfoList = stepAverageTimeConsumptionInfoList;
        }

        public Long getPedigreeId() {
            return pedigreeId;
        }

        public PedigreeStepAverageTimeConsumptionInfo setPedigreeId(Long pedigreeId) {
            this.pedigreeId = pedigreeId;
            return this;
        }

        public String getPedigreeCode() {
            return pedigreeCode;
        }

        public PedigreeStepAverageTimeConsumptionInfo setPedigreeCode(String pedigreeCode) {
            this.pedigreeCode = pedigreeCode;
            return this;
        }

        public String getPedigreeName() {
            return pedigreeName;
        }

        public PedigreeStepAverageTimeConsumptionInfo setPedigreeName(String pedigreeName) {
            this.pedigreeName = pedigreeName;
            return this;
        }

        public List<StepAverageTimeConsumptionInfo> getStepAverageTimeConsumptionInfoList() {
            return stepAverageTimeConsumptionInfoList;
        }

        public PedigreeStepAverageTimeConsumptionInfo setStepAverageTimeConsumptionInfoList(List<StepAverageTimeConsumptionInfo> stepAverageTimeConsumptionInfoList) {
            this.stepAverageTimeConsumptionInfoList = stepAverageTimeConsumptionInfoList;
            return this;
        }
    }


    /**
     * 谱系工序平均使用时长
     */
    @Schema(description = "谱系工序平均使用时长")
    public interface StepAverageTimeConsumptionInfo {
        /**
         * 产品谱系id
         */
        Long getPedigreeId();

        /**
         * 产品谱系编码
         */
        String getPedigreeCode();

        /**
         * 产品谱系名称
         */
        String getPedigreeName();

        /**
         * 工序id
         */
        Long getStepId();

        /**
         * 工序编码
         */
        String getStepCode();

        /**
         * 工序名称
         */
        String getStepName();

        /**
         * 工序平均使用时长
         */
        Double getAverageUsageDuration();


    }

    /**
     * 产品谱系平均使用时长
     */
    @Schema(description = "产品谱系平均使用时长")
    public interface PedigreeAverageTimeConsumptionInfo {
        /**
         * 产品谱系id
         */
        Long getPedigreeId();

        /**
         * 产品谱系编码
         */
        String getPedigreeCode();

        /**
         * 产品谱系名称
         */
        String getPedigreeName();

        /**
         * 谱系平均使用时长
         */
        Double getAverageUsageDuration();

    }

}
