package net.airuima.rbase.web.rest.procedure.aps;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.afterturn.easypoi.view.PoiBaseView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.ExportParamDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.aps.WsSubWsDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.plugin.ISubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.io.FileOutputStream;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产子工单Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "生产子工单Resource")
@RestController
@RequestMapping("/api/sub-work-sheets")
@AuthorityRegion("生产工单")
@FuncInterceptor("SubWorkSheet")
@AuthSkip("I")
public class SubWorkSheetResource extends ProtectBaseResource<SubWorkSheet> {

    private final Logger log = LoggerFactory.getLogger(SubWorkSheetResource.class);
    private static final String PAUSE_SUB_WORK_SHEET = "pauseSubWorkSheet";

    private final SubWorkSheetService subWorkSheetService;

    @Autowired
    private ISubWorkSheetService[] subWorkSheetServices;
    @Autowired
    private IWorkSheetService[] workSheetServices;

    public SubWorkSheetResource(SubWorkSheetService subWorkSheetService) {
        this.subWorkSheetService = subWorkSheetService;
        this.mapUri = "/api/sub-work-sheets";
    }

    /**
     * 根据总工单ID获取所有子工单
     *
     * <AUTHOR>
     * @param: workSheetId 总工单ID
     * @updateTime 2020/12/22 19:53
     * @return: java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过工单主键ID获取工单信息及所有已分单子工单信息",parameters = {
            @Parameter(name = "workSheetId",description = "工单主键ID",schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH)
    })
    @GetMapping("/work-sheets/{workSheetId}")
    public ResponseEntity<ResponseData<WsSubWsDTO>> findByWorkSheetId(@PathVariable(value = "workSheetId") Long workSheetId) {
        return ResponseData.ok(subWorkSheetService.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO));
    }

    /**
     * 根据编码获取子工单
     *
     * @param serialNumber 子工单
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据编码获取子工单")
    @GetMapping("/serialNumber")
    public ResponseEntity<ResponseData<SubWorkSheet>> findBySerialNumber(@RequestParam(value = "serialNumber") String serialNumber) {
        return ResponseData.ok(subWorkSheetService.findBySerialNumber(serialNumber));
    }

    /**
     * @throws
     * @description 新增子工单
     * <AUTHOR>
     * @param: entity
     * @updateTime 2020/12/24 15:36
     * @return: org.springframework.http.ResponseEntity<net.airuima.domain.procedure.aps.SubWorkSheet>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "批量新增或修改生产子工单")
    @PostMapping("/createCustom")
    @PreventRepeatSubmit(expireTime = 5)
    public ResponseEntity<ResponseData<Void>> createCustom(@Valid @RequestBody List<SubWorkSheetDTO> subWorkSheetDTOList) {
        try{
            BaseDTO responseBaseDto = subWorkSheetService.saveInstance(subWorkSheetDTOList);
            if (Constants.KO.equals(responseBaseDto.getStatus())) {
                return ResponseData.error("exception", responseBaseDto.getMessage());
            }
        }catch (ResponseException e){
            return ResponseData.error(e);
        }

        return ResponseData.save();
    }

    /**
     * @param workSheetId   总工单ID
     * @param workLineId    生产线ID
     * @param singleNumber  单个子工单数量
     * @param planStartDate 计划开工日期
     * @param planEndDate   计划完工日期
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "手动进行自动分单")
    @PostMapping("/autoGenerateSubWorkSheet")
    @PreventRepeatSubmit(expireTime = 5)
    public ResponseEntity<ResponseData<Void>> autoGenerateSubWorkSheet(@RequestParam(value = "workSheetId") Long workSheetId,
                                                                       @RequestParam(value = "workLineId", required = false) Long workLineId,
                                                                       @RequestParam(value = "singleNumber") Integer singleNumber,
                                                                       @RequestParam(value = "planStartDate") LocalDateTime planStartDate,
                                                                       @RequestParam(value = "planEndDate", required = false) LocalDateTime planEndDate) {
        try {
            SubWorkSheet subWorkSheet = subWorkSheetService.autoGenerateSubWorkSheet(workSheetId, workLineId, planStartDate, planEndDate, singleNumber, true);
            if (subWorkSheet != null) {
                return ResponseData.save();
            } else {
                return ResponseData.error("error", "自动分单失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过子工单号模糊查询子工单列表
     *
     * @param text 子工单号
     * @param size 行数
     * @return List<WorkSheet>
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过子工单号模糊查询子工单列表")
    @GetMapping("/bySerialNumber")
    public ResponseEntity<ResponseData<List<SubWorkSheet>>> bySerialNumber(@RequestParam(value = "text") String text,
                                                                           @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(subWorkSheetService.findPageBySerialNumber(text, size));
    }

    /**
     * 上传总工单完工信息修改子工单上传状态
     *
     * @param subWorkSheetIdList 子工单id列
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2021/8/10
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "上传工单完工信息及修改子工单上传状态")
    @PostMapping("/syncWorkSheetComplete")
    public ResponseEntity<Void> subWorkSheetComplete(@RequestBody List<Long> subWorkSheetIdList) {
        return subWorkSheetService.subWorkSheetComplete(subWorkSheetIdList);
    }

    /**
     * 通过子工单的id修改子工单的状态
     *
     * @param status         绑定状态(0:解绑;1:绑定)
     * @param subWorkSheetId 子工单的id
     * @return ResponseEntity
     * @date 2022-04-15
     * <AUTHOR>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过子工单的id修改子工单的状态")
    @PutMapping("/update-status/{subWorkSheetId}/{status}")
    public ResponseEntity<ResponseData<Void>> syncStatus(@PathVariable("subWorkSheetId") Long subWorkSheetId, @PathVariable("status") Integer status) {
        try {
            subWorkSheetService.syncStatus(subWorkSheetId, status);
        } catch (Exception e) {
            log.error("通过子工单的id【{}】修改子工单的状态失败,原因是:{}", subWorkSheetId, e.getMessage());
            return ResponseData.error(e);
        }
        return ResponseData.ok();
    }

    /**
     * 修改子工单状态 (事件机制、flowable 回调)
     *
     * @param serialNumber 子工单号
     * @param status       1: 投产中；2: 暂停
     * <AUTHOR>
     * @date 2023/4/3
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改子工单状态 (1: 投产中；2: 暂停)")
    @PostMapping("/status")
    public ResponseEntity<ResponseData<Void>> updateStatusBySerialNumber(@RequestParam("serialNumber") String serialNumber, @RequestParam("status") Integer status) {
        try {
            subWorkSheetService.updateStatusBySerialNumber(serialNumber, status);
        } catch (Exception e) {
            log.error("通过子工单号【{}】修改子工单的状态失败，原因是:{}", serialNumber, e.getMessage());
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 暂停子工单 (事件机制、flowable 回调)
     *
     * @param serialNumber 子工单号
     * <AUTHOR>
     * @date 2023/4/3
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "暂停子工单状态")
    @PostMapping("/stopStatus")
    public ResponseEntity<ResponseData<Void>> stopStatus(@RequestParam("serialNumber") String serialNumber) {
        try {
            subWorkSheetService.updateStatusBySerialNumber(serialNumber, ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
        } catch (Exception e) {
            log.error("通过子工单号【{}】暂停子工单的状态失败，原因是:{}", serialNumber, e.getMessage());
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 恢复子工单(事件机制、flowable 回调)
     *
     * @param serialNumber 子工单号
     * <AUTHOR>
     * @date 2023/4/3
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "恢复子工单状态")
    @PostMapping("/recoverStatus")
    public ResponseEntity<ResponseData<Void>> recoverStatus(@RequestParam("serialNumber") String serialNumber) {
        try {
            subWorkSheetService.updateStatusBySerialNumber(serialNumber, ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
        } catch (Exception e) {
            log.error("通过子工单号【{}】恢复子工单的状态失败，原因是:{}", serialNumber, e.getMessage());
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 子工单中途结单
     *
     * @param subWorkSheetDto 子工单DTO
     * @return ResponseEntity
     * @date 2022-04-15
     * <AUTHOR>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "子工单中途结单")
    @PostMapping("/force-finish")
    public ResponseEntity<ResponseData<Void>> forceFinishSubWorkSheet(@RequestBody SubWorkSheetDTO subWorkSheetDto) {
        try {
            BaseDTO baseDto = subWorkSheetServices[0].forceFinishSubWorkSheet(subWorkSheetDto);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (Exception e) {
            log.error("子工单id【{}】中途结单失败,原因是:{}", subWorkSheetDto.getSerialNumber(), e.getMessage());
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 通用CRUD修改子工单
     *
     * @param entity
     * @return ResponseEntity<ResponseData<SubWorkSheet>>
     * <AUTHOR>
     * @date 2021-11-17
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<SubWorkSheet> update(@Valid @RequestBody SubWorkSheet entity) throws URISyntaxException {
        return subWorkSheetService.updateEntity(entity);
    }

    /**
     * 通过产线ID、完成状态获取子工单
     *
     * @param workLineId 生产线ID
     * @param status     状态
     * @return List<SubWorkSheet>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/byWorkLineOrStatus")
    public ResponseEntity<ResponseData<List<SubWorkSheet>>> subWorkSheetByWorkLineAndStatus(@RequestParam("workLineId") Long workLineId, @RequestParam("status") Integer status) {
        return ResponseData.ok(subWorkSheetService.findByWorkLineIdAndStatusLessThanAndDeleted(workLineId, status));
    }

    /**
     * 修改子工单状态为取消
     *
     * @param id 子工单ID
     * @return ResponseEntity<ResponseData<Void>>
     * <AUTHOR>
     * @date 2022-12-28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "修改工单状态为取消")
    @PutMapping("/status/cancel/{id}")
    public ResponseEntity<ResponseData<Void>> cancel(@PathVariable("id") Long id) {
        try {
            subWorkSheetService.cancel(id);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/v2/exportExcel"})
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {

        if (exportDTO.getExportTemplate() != null && exportDTO.getExportTemplate()) {
            ExcelUtil.createTitleWorkbook(exportDTO.getExportParams(), response, exportDTO.getExcelTitle());
        } else {
            this.exportParams = new ExportParams( null, exportDTO.getExcelTitle(), ExcelType.XSSF);
            this.exportParams.setFreezeCol(2);
            this.exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
            if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
                exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
                exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
            }
            Specification<SubWorkSheet> spec = QueryConditionParser.buildSpecificationWithClassName(SubWorkSheet.class.getName(), exportDTO.getQcs(), this.filters, this.filterReformer);
            long count = this.getService().count(spec);
            List<ExportParamDTO> exportParamDTOList = exportDTO.getExportParams();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map((s) -> {
                return StringUtils.substringBefore(s.getLabel(), "[[");
            }).map((label) -> {
                return new ExcelExportEntity(label, label);
            }).collect(Collectors.toList());
            if (count <= 100000L) {
                List<SubWorkSheet> dataList = this.getService().find(spec);
                dataList.forEach(subWorkSheet -> subWorkSheet.setProgress(subWorkSheet.getProgress()));

                List<Map<String, String>> result = new ArrayList<>();
                if (!exportParamDTOList.isEmpty()) {
                    ExcelUtil.dealExcelFilterData(dataList, exportParamDTOList, result);
                }
                result.forEach(map -> {
                    if (null != map.get("工单进度")) {
                        map.put("工单进度", NumberUtils.divide(NumberUtils.multiply(Double.valueOf(map.get("工单进度")), Constants.INT_ONE_HUNDRED).doubleValue(), Constants.INT_ONE, Constants.INT_FOUR).doubleValue() + "%");
                    }
                });
                modelMap.put("mapList", result);
                modelMap.put("EntityList", excelExportEntityList);
                modelMap.put("params", this.exportParams);
                modelMap.put("fileName", URLEncoder.encode(exportDTO.getExcelTitle(), "utf-8"));
                response.setHeader("message", "export!");
                PoiBaseView.render(modelMap, request, response, "easypoiMapExcelView");
            } else {
                if (RUNNING_EXPORT.get()) {
                    response.setHeader("message", "running!");
                    return;
                }

                String uuid = UUID.randomUUID().toString().replace("-", "");
                CompletableFuture<Void> async = CompletableFuture.runAsync(() -> {
                    RUNNING_EXPORT.set(true);
                    RabbitTemplate rabbitTemplate = BeanUtil.getBean(RabbitTemplate.class);
                    Workbook workbook = ExcelExportUtil.exportBigExcel(this.exportParams, excelExportEntityList, new IExcelExportServer() {
                        private int pageNumber = 0;

                        public List<Object> selectListForExcelExport(Object queryParams, int page) {
                            Pageable pageable = PageRequest.of(this.pageNumber / 100000, 100000);
                            List<SubWorkSheet> dataList = subWorkSheetService.find(spec, pageable).getContent();
                            this.pageNumber += 100000;
                            if (dataList.isEmpty()) {
                                return null;
                            } else {
                                dataList.forEach(subWorkSheet -> subWorkSheet.setProgress(subWorkSheet.getProgress()));

                                List<Map<String, String>> result = new ArrayList<>();
                                if (!exportParamDTOList.isEmpty()) {
                                    ExcelUtil.dealExcelFilterData(dataList, exportParamDTOList, result);
                                }
                                result.forEach(map -> {
                                    if (null != map.get("工单进度")) {
                                        map.put("工单进度", NumberUtils.divide(NumberUtils.multiply(Double.valueOf(map.get("工单进度")), Constants.INT_ONE_HUNDRED).doubleValue(), Constants.INT_ONE, Constants.INT_FOUR).doubleValue() + "%");
                                    }
                                });
                                return result.stream().map((t) -> {
                                    return t;
                                }).collect(Collectors.toList());
                            }
                        }
                    }, (Object) null);

                    try {
                        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
                        String fileName = exportDTO.getExcelTitle() + "-" + uuid + prefix;
                        FileOutputStream fileOutputStream = new FileOutputStream(FileUtil.TMPDIR + fileName);
                        workbook.write(fileOutputStream);
                        fileOutputStream.flush();
                        workbook.close();
                        fileOutputStream.close();
                        rabbitTemplate.convertAndSend("amq.topic", uuid, fileName);
                    } catch (Exception var10) {
                        var10.printStackTrace();
                    }

                });
                async.whenCompleteAsync((unused, throwable) -> {
                    RUNNING_EXPORT.set(false);
                });
                response.setHeader("message", "wait!");
                response.setHeader("id", uuid);
            }
        }
    }

    /**
     * 通过主键ID逻辑删除数据通用接口
     * @param id
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(
            summary = "通过主键ID逻辑删除数据通用接口",
            parameters = {@Parameter(
                    name = "id",
                    description = "主键ID",
                    schema = @Schema(
                            type = "integer",
                            format = "int64"
                    ),
                    required = true,
                    in = ParameterIn.PATH
            )}
    )
    @DeleteMapping({"/{id}"})
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try{
            subWorkSheetService.logicDeleteById(id);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
    }

    /**
     * 子工单流程卡打印
     *
     * @param id           工单ID
     * @param templateCode 模板编码
     * @param response     响应
     * @throws Exception 异常
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/step-card/{id}/print/{templateCode}")
    public void exportWsStepCard(@PathVariable Long id, @PathVariable String templateCode, HttpServletResponse response) throws Exception {
        workSheetServices[0].printWorkSheetStepCard(null,id,templateCode,response);
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览子工单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建子工单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改子工单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除子工单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入子工单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出子工单";
        }
        return "";
    }

}
