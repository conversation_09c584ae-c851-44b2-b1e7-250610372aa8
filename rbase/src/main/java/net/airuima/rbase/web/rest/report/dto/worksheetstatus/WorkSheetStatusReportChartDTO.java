package net.airuima.rbase.web.rest.report.dto.worksheetstatus;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/10/24
 */
@Schema(description = "工单状态报表图形返回数据")
public class WorkSheetStatusReportChartDTO {

    /**
     * 总工单数
     */
    @Schema(description = "总工单数")
    private long number;

    /**
     * 已完成状态数量
     */
    @Schema(description = "已完成状态数量")
    private long finishNumber;

    /**
     * 投产中状态数量
     */
    @Schema(description = "投产中状态数量")
    private long productionNumber;

    /**
     * 已下单状态数量
     */
    @Schema(description = "已下单状态数量")
    private long preparedNumber;

    /**
     * 工单状态饼图数据列表
     */
    @Schema(description = "工单状态饼图数据列表")
    private List<PieChartDTO> pieChartDTOList;

    /**
     * 柱状图数据列表
     */
    @Schema(description = "柱状图数据列表")
    private List<BarChartDTO> barChartDTOList;

    public Long getNumber() {
        return number;
    }

    public WorkSheetStatusReportChartDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public long getFinishNumber() {
        return finishNumber;
    }

    public WorkSheetStatusReportChartDTO setFinishNumber(long finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public long getProductionNumber() {
        return productionNumber;
    }

    public WorkSheetStatusReportChartDTO setProductionNumber(long productionNumber) {
        this.productionNumber = productionNumber;
        return this;
    }

    public long getPreparedNumber() {
        return preparedNumber;
    }

    public WorkSheetStatusReportChartDTO setPreparedNumber(long preparedNumber) {
        this.preparedNumber = preparedNumber;
        return this;
    }

    public List<PieChartDTO> getPieChartDTOList() {
        return pieChartDTOList;
    }

    public WorkSheetStatusReportChartDTO setPieChartDTOList(List<PieChartDTO> pieChartDTOList) {
        this.pieChartDTOList = pieChartDTOList;
        return this;
    }

    public List<BarChartDTO> getBarChartDTOList() {
        return barChartDTOList;
    }

    public WorkSheetStatusReportChartDTO setBarChartDTOList(List<BarChartDTO> barChartDTOList) {
        this.barChartDTOList = barChartDTOList;
        return this;
    }

    @Schema(description = "工单状态饼图数据DTO")
    public static class PieChartDTO{
        /**
         * 工单状态
         */
        @Schema(description = "工单状态")
        private String status;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private long number;


        public String getStatus() {
            return status;
        }

        public PieChartDTO setStatus(String status) {
            this.status = status;
            return this;
        }

        public long getNumber() {
            return number;
        }

        public PieChartDTO setNumber(long number) {
            this.number = number;
            return this;
        }
    }

    @Schema(description = "柱状图数据DTO")
    public static class BarChartDTO{
        /**
         * 工单状态
         */
        @Schema(description = "工单状态")
        private String status;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private long number;

        /**
         * 日期
         */
        @Schema(description = "日期")
        private String date;

        public BarChartDTO(String status, long number, String date) {
            this.status = status;
            this.number = number;
            this.date = date;
        }

        public BarChartDTO() {
        }

        public String getStatus() {
            return status;
        }

        public BarChartDTO setStatus(String status) {
            this.status = status;
            return this;
        }

        public long getNumber() {
            return number;
        }

        public BarChartDTO setNumber(long number) {
            this.number = number;
            return this;
        }

        public String getDate() {
            return date;
        }

        public BarChartDTO setDate(String date) {
            this.date = date;
            return this;
        }
    }
}
