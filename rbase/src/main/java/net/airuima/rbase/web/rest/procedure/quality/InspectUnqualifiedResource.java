package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.config.annotation.HideAuthority;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualified;
import net.airuima.rbase.service.procedure.quality.InspectUnqualifiedService;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectUnqualifiedDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectUnqualifiedResultDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良品管理记录Resource
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Tag(name = "不良品管理记录Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/inspect-unqualifieds")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("PQC || FQC || LQC")
@HideAuthority
public class InspectUnqualifiedResource extends ProtectBaseResource<InspectUnqualified> {

    private static final String MODULE = "不良品";

    private final InspectUnqualifiedService inspectUnqualifiedService;

    public InspectUnqualifiedResource(InspectUnqualifiedService inspectUnqualifiedService) {
        this.inspectUnqualifiedService = inspectUnqualifiedService;
        this.mapUri = "/api/inspect-unqualifieds";
    }

    /**
     * 根据id获取明细信息
     * @param id    不良管理记录id
     * @return org.springframework.http.ResponseEntity<java.util.List<net.airuima.web.rest.procedure.quality.dto.InspectUnqualifiedDTO>>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping({"/detail/{id}"})
    @Operation(summary = "根据id获取明细信息")
    public ResponseEntity<ResponseData<List<InspectUnqualifiedDTO>>> detail(@PathVariable(value = "id") Long id) {
        return ResponseData.ok(inspectUnqualifiedService.findDetailById(id));
    }

    /**
     * 抽检终检 不良处理
     * @param inspectUnqualifiedResultDto
     * <AUTHOR>
     * @date  2023/5/9
     * @return void
     */
    @PostMapping("/inspection-results")
    @PreventRepeatSubmit
    @Operation(summary = "抽检 终检 不良处理结果")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')  or @ sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> inspectionUnqualifiedResult(@RequestBody InspectUnqualifiedResultDTO inspectUnqualifiedResultDto){
        try {
            inspectUnqualifiedService.inspectionUnqualifiedResult(inspectUnqualifiedResultDto);
            return ResponseData.save();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
