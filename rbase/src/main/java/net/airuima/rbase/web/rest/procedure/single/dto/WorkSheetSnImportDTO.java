package net.airuima.rbase.web.rest.procedure.single.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单关联SN导入导出DTO
 * <AUTHOR>
 * @date 2024/2/29
 */
public class WorkSheetSnImportDTO implements Serializable {

    /**
     * 工单号
     */
    @Excel(name = "工单号" ,width = 20)
    private String wsSerialNumber;

    /**
     * 子工单号
     */
    @Excel(name = "子工单号",funcKey = "SubWorkSheet", width = 20)
    private String subWsSerialNumber;

    /**
     * 投产SN
     */
    @Excel(name = "SN", width = 30)
    private String sn;


    public WorkSheetSnImportDTO() {

    }

    public WorkSheetSnImportDTO(WorkSheetSn workSheetSn) {
        this.wsSerialNumber = workSheetSn.getWorkSheet().getSerialNumber();
        this.subWsSerialNumber = Objects.isNull(workSheetSn.getSubWorkSheet()) ? null:workSheetSn.getSubWorkSheet().getSerialNumber();
        this.sn = workSheetSn.getSn();
    }

    public String getWsSerialNumber() {
        return wsSerialNumber;
    }

    public WorkSheetSnImportDTO setWsSerialNumber(String wsSerialNumber) {
        this.wsSerialNumber = wsSerialNumber;
        return this;
    }

    public String getSubWsSerialNumber() {
        return subWsSerialNumber;
    }

    public WorkSheetSnImportDTO setSubWsSerialNumber(String subWsSerialNumber) {
        this.subWsSerialNumber = subWsSerialNumber;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public WorkSheetSnImportDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
