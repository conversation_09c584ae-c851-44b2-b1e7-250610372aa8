package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.SnStepFppStatus;
import net.airuima.rbase.service.procedure.quality.SnStepFppStatusService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 单支FPP状态Resource
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "单支FPP状态Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/sn-step-fpp-statuses")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("StepFpp")
@AuthSkip("CUI")
public class SnStepFppStatusResource extends ProtectBaseResource<SnStepFppStatus> {

    private static final String MODULE = "SN工序FPP状态";
    private final SnStepFppStatusService snStepFppStatusService;

    public SnStepFppStatusResource(SnStepFppStatusService snStepFppStatusService) {
        this.snStepFppStatusService = snStepFppStatusService;
        this.mapUri = "/api/sn-step-fpp-statuses";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
