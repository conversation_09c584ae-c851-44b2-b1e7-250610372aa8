package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组达成率情况DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序组达成率情况DTO")
public class StepGroupCompletionRateInfoDTO {

    /**
     * 工序组名称
     */
    @Schema(description = "工序组名称")
    private String name;

    /**
     * 达成率
     */
    @Schema(description = "达成率")
    private Double rate;

    public String getName() {
        return name;
    }

    public StepGroupCompletionRateInfoDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Double getRate() {
        return rate;
    }

    public StepGroupCompletionRateInfoDTO setRate(Double rate) {
        this.rate = rate;
        return this;
    }

    public StepGroupCompletionRateInfoDTO() {
    }

    public StepGroupCompletionRateInfoDTO(String name, Double rate) {
        this.name = name;
        this.rate = rate;
    }
}
