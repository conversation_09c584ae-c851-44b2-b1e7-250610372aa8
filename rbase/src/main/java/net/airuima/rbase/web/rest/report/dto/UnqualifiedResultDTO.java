package net.airuima.rbase.web.rest.report.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良项目数量数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良项目数量数据DTO")
public class UnqualifiedResultDTO {

    /**
     * 项目ID
     */
    @Schema(description = "不良项目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 项目编码
     */
    @Schema(description = "不良项目编码")
    private String code;

    /**
     * 项目名字
     */
    @Schema(description = "不良项目名字")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "不良项目数量")
    private Long number;

    /**
     * 不良项目产生日期
     */
    @Schema(description = "不良项目产生日期")
    private LocalDate recordDate;

    public UnqualifiedResultDTO() {
    }

    public UnqualifiedResultDTO(Long id, String code, String name, Long number, LocalDate recordDate) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.number = number;
        this.recordDate = recordDate;
    }

    public String getName() {
        return name;
    }

    public UnqualifiedResultDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public UnqualifiedResultDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getId() {
        return id;
    }

    public UnqualifiedResultDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedResultDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public UnqualifiedResultDTO setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

}
