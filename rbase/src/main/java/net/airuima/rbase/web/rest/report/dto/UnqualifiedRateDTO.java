package net.airuima.rbase.web.rest.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良比例数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良比例数据DTO")
public class UnqualifiedRateDTO {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private Long id;

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String code;

    /**
     * 项目名字
     */
    @Schema(description = "项目名字")
    private String name;

    /**
     * number
     */
    @Schema(description = "不良数量")
    private Long number;

    /**
     * 比率
     */
    @Schema(description = "比率")
    private Double rate;

    public Long getId() {
        return id;
    }

    public UnqualifiedRateDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedRateDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public UnqualifiedRateDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public UnqualifiedRateDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Double getRate() {
        return rate;
    }

    public UnqualifiedRateDTO setRate(Double rate) {
        this.rate = rate;
        return this;
    }



}
