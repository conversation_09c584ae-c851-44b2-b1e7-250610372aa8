package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialReturn;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;
import net.airuima.rbase.service.procedure.material.WsWorkCellMaterialReturnService;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Tag(name = "工单工位退料表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-work-cell-material-returns")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber && WsWorkCellMaterialBatchNumber")
@AuthSkip("IEUD")
public class WsWorkCellMaterialReturnResource extends ProtectBaseResource<WsWorkCellMaterialReturn> {

    private final WsWorkCellMaterialReturnService wsWorkCellMaterialReturnService;

    public WsWorkCellMaterialReturnResource(WsWorkCellMaterialReturnService wsWorkCellMaterialReturnService) {
        this.wsWorkCellMaterialReturnService = wsWorkCellMaterialReturnService;
        this.mapUri = "/api/ws-work-cell-material-returns";
    }

    /**
     * 保存工单工位退料信息
     * @param rollBackMaterialDto
     * <AUTHOR>
     * @date  2022/3/28
     * @return
     */
    @Operation(summary= "工单工位退料保存接口")
    @PostMapping("/ws-work-cell-material-return")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseData<Void>> createWsMaterialReturn(@RequestBody RollBackMaterialDTO rollBackMaterialDto){
        try {
            BaseDTO baseDto = wsWorkCellMaterialReturnService.saveWsWorkCellMaterialReturn(rollBackMaterialDto);
            if (Constants.OK.equals(baseDto.getStatus())){
                return ResponseData.save();
            }else {
                return ResponseData.error("exception", baseDto.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览工单工位退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建工单工位退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改工单工位退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除工单工位退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入工单工位退料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出工单工位退料";
        }
        return "";
    }
}
