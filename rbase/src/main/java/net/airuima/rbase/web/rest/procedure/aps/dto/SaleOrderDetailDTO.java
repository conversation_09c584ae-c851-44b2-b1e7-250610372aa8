package net.airuima.rbase.web.rest.procedure.aps.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 销售订单详情DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "销售订单详情DTO", description = "销售订单详情DTO")
public class SaleOrderDetailDTO implements Serializable {

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 产品谱系Id
     */
    @Schema(description = "产品谱系Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 订单数量
     */
    @Schema(description = "订单数量")
    private Integer number;


    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划完工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate planEndDate;

    public String getContractNo() {
        return contractNo;
    }

    public SaleOrderDetailDTO setContractNo(String contractNo) {
        this.contractNo = contractNo;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public SaleOrderDetailDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public SaleOrderDetailDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public LocalDate getPlanStartDate() {
        return planStartDate;
    }

    public SaleOrderDetailDTO setPlanStartDate(LocalDate planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDate getPlanEndDate() {
        return planEndDate;
    }

    public SaleOrderDetailDTO setPlanEndDate(LocalDate planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        SaleOrderDetailDTO that = (SaleOrderDetailDTO) o;
        return Objects.equals(pedigreeId, that.pedigreeId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pedigreeId);
    }
}
