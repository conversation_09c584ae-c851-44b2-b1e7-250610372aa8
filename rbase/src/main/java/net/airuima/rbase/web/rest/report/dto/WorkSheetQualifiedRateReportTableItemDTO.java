package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合格率看板表格数据明细DTO
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
@Schema(description = "合格率看板表格数据明细DTO")
public class WorkSheetQualifiedRateReportTableItemDTO {

    /**
     * 实际完成日期
     */
    @Schema(description = "实际完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际完成日期", orderNum = "1", format = "yyyy-MM-dd HH:mm:ss",needMerge = true)
    private LocalDateTime actualEndDate;


    /**
     * 工单号
     */
    @Schema(description = "工单号", required = true)
    @Excel(name = "工单号", orderNum = "2",needMerge = true)
    private String workSheetSerialNumber;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "3",needMerge = true)
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "4",needMerge = true)
    private String pedigreeName;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Excel(name = "规格型号", orderNum = "5",needMerge = true)
    private String specification;

    /**
     * 生产线名字
     */
    @Schema(description = "生产线名字")
    @Excel(name = "生产线名字", orderNum = "6",needMerge = true)
    private String workLineName;

    /**
     * 投产数量
     */
    @Schema(description = "投产数量")
    @Excel(name = "投产数", orderNum = "7",needMerge = true)
    private Long number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "8",needMerge = true)
    private Long qualifiedNumber;


    /**
     * 一次合格数
     */
    @Schema(description = "一次合格数")
    @Excel(name = "一次合格数", orderNum = "9",needMerge = true)
    private Long firstQualifiedNumber;

    /**
     * 直通率
     */
    @Schema(description = "直通率")
    @Excel(name = "直通率", orderNum = "10",needMerge = true)
    private Double firstQualifiedRate;


    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Excel(name = "不合格数", orderNum = "11",needMerge = true)
    private Long unQualifiedNumber;

    /**
     * 合格率
     */
    @Schema(description = "合格率")
    @Excel(name = "合格率", orderNum = "12",needMerge = true)
    private Double qualifiedRate;

    /**
     * 不良项目明细列表
     */
    @ExcelCollection(name = "不良项目明细",orderNum = "13")
    public List<UnqualifiedItemInfo> unqualifiedItemInfoList;


    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getFirstQualifiedNumber() {
        return firstQualifiedNumber;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setFirstQualifiedNumber(Long firstQualifiedNumber) {
        this.firstQualifiedNumber = firstQualifiedNumber;
        return this;
    }

    public Double getFirstQualifiedRate() {
        return firstQualifiedRate;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setFirstQualifiedRate(Double firstQualifiedRate) {
        this.firstQualifiedRate = firstQualifiedRate;
        return this;
    }

    public Long getUnQualifiedNumber() {
        return unQualifiedNumber;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setUnQualifiedNumber(Long unQualifiedNumber) {
        this.unQualifiedNumber = unQualifiedNumber;
        return this;
    }

    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public List<UnqualifiedItemInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public WorkSheetQualifiedRateReportTableItemDTO setUnqualifiedItemInfoList(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    @Schema(description = "不良项目信息")
    public static class UnqualifiedItemInfo{

        /**
         * 工单号（统计数据时使用）
         */
        @JsonIgnore
        private String serialNumber;

        /**
         * 不良项目名称
         */
        @Schema(description = "不良项目名称")
        @Excel(name = "名称",orderNum = "13")
        private String name;

        /**
         * 不良项目编码
         */
        @Schema(description = "不良项目编码")
        @Excel(name = "编码",orderNum = "14")
        private String code;

        /**
         * 不良项目数量
         */
        @Schema(description = "不良项目数量")
        @Excel(name = "数量",orderNum ="15")
        private Long number;


        /**
         * 不良项目占比
         */
        @Schema(description = "不良项目占比")
        @Excel(name = "比例",orderNum = "16")
        private String percent;

        public UnqualifiedItemInfo(){

        }

        public UnqualifiedItemInfo(String serialNumber, String name, String code, Long number){
            this.serialNumber = serialNumber;
            this.name = name;
            this.code = code;
            this.number = number;
        }


        public String getSerialNumber() {
            return serialNumber;
        }

        public UnqualifiedItemInfo setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public Long getNumber() {
            return number;
        }

        public UnqualifiedItemInfo setNumber(Long number) {
            this.number = number;
            return this;
        }

        public String getPercent() {
            return percent;
        }

        public UnqualifiedItemInfo setPercent(String percent) {
            this.percent = percent;
            return this;
        }
    }



}
