package net.airuima.rbase.web.rest.report.dto.worksheethistorydata.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产履历导出Excel
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class WorkSheetExcel {

    /**
     * 工单号
     */
    @Excel(name = "工单号")
    private String serialNumber;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单，6：收货中)
     */
    @Excel(name = "工单状态", replace = {"已取消_-2", "审批中_-1", "已下单_0", "投产中_1", "已暂停_2", "已完成_3", "正常结单_4", "异常结单_5"})
    private int status;

    /**
     * 工单进度
     */
    @Excel(name = "工单进度")
    private String progress;

    /**
     * 产品谱系
     */
    @Excel(name = "产品谱系")
    private String pedigreeNameCode;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String specification;

    /**
     * 产线
     */
    @Excel(name = "产线")
    private String workLineNameCode;

    /**
     * 工艺路线
     */
    @Excel(name = "工艺路线")
    private String workFlowNameCode;


    /**
     * 物料清单
     */
    @Excel(name = "物料清单")
    private String bomInfoNameCode;

    /**
     * 计划开工日期
     */
    @Excel(name = "计划开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Excel(name = "计划结单日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Excel(name = "实际开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Excel(name = "实际完成日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    /**
     * 投产数
     */
    @Excel(name = "投产数")
    private Integer number;

    /**
     * 合格数
     */
    @Excel(name = "合格数")
    private Integer qualifiedNumber;

    /**
     * 履历信息
     */
    @ExcelCollection(name = "履历信息")
    private List<HistoryExcel> historyExcelList;

    public static class HistoryExcel {

        /**
         * 0,创建
         * 1,分单
         * 2,暂停
         * 3,恢复
         * 4,完成
         */
        @Excel(name = "工单状态", replace = {"创建_0", "分单_1", "暂停_2", "恢复_3", "完成_4"})
        private Integer type;

        /**
         * 记录时间
         */
        @Excel(name = "时间", format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime localDateTime;

        /**
         * 人员信息
         */
        @Excel(name = "人员信息")
        private String staffNameCode;

        /**
         * 备注
         */
        @Excel(name = "备注")
        private String note;

        public Integer getType() {
            return type;
        }

        public HistoryExcel setType(Integer type) {
            this.type = type;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryExcel setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public String getStaffNameCode() {
            return staffNameCode;
        }

        public HistoryExcel setStaffNameCode(String staffNameCode) {
            this.staffNameCode = staffNameCode;
            return this;
        }

        public String getNote() {
            return note;
        }

        public HistoryExcel setNote(String note) {
            this.note = note;
            return this;
        }
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheetExcel setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public WorkSheetExcel setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getProgress() {
        return progress;
    }

    public WorkSheetExcel setProgress(String progress) {
        this.progress = progress;
        return this;
    }

    public String getPedigreeNameCode() {
        return pedigreeNameCode;
    }

    public WorkSheetExcel setPedigreeNameCode(String pedigreeNameCode) {
        this.pedigreeNameCode = pedigreeNameCode;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public WorkSheetExcel setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkLineNameCode() {
        return workLineNameCode;
    }

    public WorkSheetExcel setWorkLineNameCode(String workLineNameCode) {
        this.workLineNameCode = workLineNameCode;
        return this;
    }

    public String getWorkFlowNameCode() {
        return workFlowNameCode;
    }

    public WorkSheetExcel setWorkFlowNameCode(String workFlowNameCode) {
        this.workFlowNameCode = workFlowNameCode;
        return this;
    }

    public String getBomInfoNameCode() {
        return bomInfoNameCode;
    }

    public WorkSheetExcel setBomInfoNameCode(String bomInfoNameCode) {
        this.bomInfoNameCode = bomInfoNameCode;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkSheetExcel setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheetExcel setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public WorkSheetExcel setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
        return this;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public WorkSheetExcel setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkSheetExcel setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetExcel setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public List<HistoryExcel> getHistoryExcelList() {
        return historyExcelList;
    }

    public WorkSheetExcel setHistoryExcelList(List<HistoryExcel> historyExcelList) {
        this.historyExcelList = historyExcelList;
        return this;
    }
}
