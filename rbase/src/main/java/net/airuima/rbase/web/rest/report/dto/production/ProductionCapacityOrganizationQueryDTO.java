package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.OrganizationDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量按部门统计查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量按部门统计查询结果DTO")
@FetchEntity
public class ProductionCapacityOrganizationQueryDTO {

    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Long organizationId;

    @Transient
    @Schema(description = "部门DTO")
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long number;


    public Long getOrganizationId() {
        return organizationId;
    }

    public ProductionCapacityOrganizationQueryDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public ProductionCapacityOrganizationQueryDTO setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityOrganizationQueryDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public ProductionCapacityOrganizationQueryDTO() {
    }

    public ProductionCapacityOrganizationQueryDTO(Long organizationId, Long number) {
        this.organizationId = organizationId;
        this.number = number;
    }

}
