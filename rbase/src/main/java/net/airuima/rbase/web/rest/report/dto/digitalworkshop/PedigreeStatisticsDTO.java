package net.airuima.rbase.web.rest.report.dto.digitalworkshop;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Schema(description = "数字车间驾驶舱-产品谱系生产直通率产品趋势DTO")
public class PedigreeStatisticsDTO {

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String pedigreeName;

    /**
     * x 轴坐标时间 14：30
     */
    @Schema(description = "x 轴坐标时间 14：30")
    private String time;

    /**
     * y2-完成数
     */
    @Schema(description = "y2-完成数")
    private Long finishNumber;
    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long qualifiedNumber;
    /**
     * 返回合格数
     */
    @Schema(description = "返回合格数")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long reworkQualifiedNumber;

    /**
     * y1-直通率
     */
    @Schema(description = "y1-直通率")
    private Double fty;

    public PedigreeStatisticsDTO() {
    }

    public PedigreeStatisticsDTO(String pedigreeName, Object time, Long finishNumber, Long qualifiedNumber, Long reworkQualifiedNumber) {
        this.pedigreeName = pedigreeName;
        this.time = Objects.isNull(time)? StringUtils.EMPTY:String.valueOf(time);
        this.fty = finishNumber != Constants.LONG_ZERO ? NumberUtils.divide(qualifiedNumber - reworkQualifiedNumber, finishNumber, Constants.INT_FOUR).doubleValue() : Constants.LONG_ZERO;
        this.qualifiedNumber = qualifiedNumber;
        this.finishNumber = finishNumber;
        this.reworkQualifiedNumber = reworkQualifiedNumber;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public PedigreeStatisticsDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getTime() {
        return time;
    }

    public PedigreeStatisticsDTO setTime(String time) {
        this.time = time;
        return this;
    }

    public Double getFty() {
        return fty;
    }

    public PedigreeStatisticsDTO setFty(Double fty) {
        this.fty = fty;
        return this;
    }

    public Long getFinishNumber() {
        return finishNumber;
    }

    public PedigreeStatisticsDTO setFinishNumber(Long finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public Long getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public PedigreeStatisticsDTO setReworkQualifiedNumber(Long reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public PedigreeStatisticsDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }
}
