package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 后端导出图片DTO
 * <AUTHOR>
 * @date 2023/10/26
 */
public class ExportGraphDTO {
    /**
     * 统计报表图片
     */
    @Schema(description = "统计报表图片")
    @Excel(name = "统计图表", type = 2 ,width = 200 , height = 200,imageType = 2)
    private byte[] graph;

    public ExportGraphDTO(byte[] graph) {
        this.graph = graph;
    }


    public byte[] getGraph() {
        return graph;
    }

    public ExportGraphDTO setGraph(byte[] graph) {
        this.graph = graph;
        return this;
    }
}
