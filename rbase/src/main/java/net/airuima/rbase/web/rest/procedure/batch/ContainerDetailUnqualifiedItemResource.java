package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem;
import net.airuima.rbase.service.procedure.batch.ContainerDetailUnqualifiedItemService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情不良明细表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器工作详情不良明细表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/container-detail-unqualified-items")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("Container")
@AuthSkip("ICUD")
public class ContainerDetailUnqualifiedItemResource extends ProtectBaseResource<ContainerDetailUnqualifiedItem> {
    private static final String MODULE = "容器生产不良明细";
    private final ContainerDetailUnqualifiedItemService containerDetailUnqualifiedItemService;

    public ContainerDetailUnqualifiedItemResource(ContainerDetailUnqualifiedItemService containerDetailUnqualifiedItemService) {
        this.containerDetailUnqualifiedItemService = containerDetailUnqualifiedItemService;
        this.mapUri = "/api/container-detail-unqualified-items";
    }

    /**
     *
     * <AUTHOR>
     * @param containerDetailId     根据容器详情获取不良项目信息
     * @return List<ContainerDetailUnqualifiedItem>
     * @date 2021-04-15
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "根据容器详情ID获取不良项目信息")
    @GetMapping("/byContainerDetailId/{containerDetailId}")
    public ResponseEntity<ResponseData<List<ContainerDetailUnqualifiedItem>>> byContainerDetailId(@PathVariable("containerDetailId") Long containerDetailId){
        return ResponseData.ok(containerDetailUnqualifiedItemService.findByContainerDetailId(containerDetailId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
