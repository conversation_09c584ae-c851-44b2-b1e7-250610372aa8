package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 执行来料检验参数DTO
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Schema(description = "执行来料检验参数DTO")
public class IqcCheckDTO {

    /**
     * 来料检验ID
     */
    @Schema(description = "来料检验ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 质检方案id
     */
    @Schema(description = "质检方案id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkRuleId;

    /**
     * 来料检验详情
     */
    @Schema(description = "来料检验详情")
    private List<IqcCheckHistoryDetailDTO> detailList;


    /**
     * 不良数量
     */
    @Schema(description = "不良数量")
    private BigDecimal unqualifiedNumber;

    /**
     * 检验员id
     */
    @Schema(description = "检验员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;


    /**
     * 检验结果(0:不合格;1:合格)
     */
    @Schema(description = "检验结果(0:不合格;1:合格)")
    private Boolean result;

    /**
     * 处理方式
     */
    @Schema(description = "处理方式")
    private Integer dealWay;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String note;


    /**
     * MRB申请原因
     */
    @Schema(description = "MRB申请原因")
    private String reason;

    public Long getId() {
        return id;
    }

    public IqcCheckDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getCheckRuleId() {
        return checkRuleId;
    }

    public IqcCheckDTO setCheckRuleId(Long checkRuleId) {
        this.checkRuleId = checkRuleId;
        return this;
    }

    public List<IqcCheckHistoryDetailDTO> getDetailList() {
        return detailList;
    }

    public IqcCheckDTO setDetailList(List<IqcCheckHistoryDetailDTO> detailList) {
        this.detailList = detailList;
        return this;
    }

    public BigDecimal getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public IqcCheckDTO setUnqualifiedNumber(BigDecimal unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public IqcCheckDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public IqcCheckDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public IqcCheckDTO setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getNote() {
        return note;
    }

    public IqcCheckDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public IqcCheckDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }
}
