package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序完成报表表格明细数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Schema(description = "工序完成报表表格明细数据DTO")
public class StepCompletionReportTableItemDTO {

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "1")
    private String workOrderNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "2")
    private String subWorkOrderNumber;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "3")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "4")
    private String pedigreeName;

    /**
     * 规格季号
     */
    @Schema(description = "规格季号")
    @Excel(name = "规格季号", orderNum = "5")
    private String specification;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    @Excel(name = "工艺路线", orderNum = "6")
    private String workFlowName;

    /**
     * 生产部门
     */
    @Schema(description = "生产部门")
    @Excel(name = "生产部门", orderNum = "7")
    private String organizationName;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    @Excel(name = "生产线", orderNum = "8")
    private String workLineName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工序编码", orderNum = "9")
    private String stepCode;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    @Excel(name = "工序名称", orderNum = "10")
    private String stepName;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数", orderNum = "11")
    private Long number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    @Excel(name = "合格数", orderNum = "12")
    private Long qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    @Excel(name = "不合格数", orderNum = "13")
    private Long unQualifiedNumber;

    /**
     * 员工名字
     */
    @Schema(description = "员工名字")
    @Excel(name = "员工名字", orderNum = "14")
    private String staffName;

    public String getStaffName() {
        return staffName;
    }

    public StepCompletionReportTableItemDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getWorkOrderNumber() {
        return workOrderNumber;
    }

    public StepCompletionReportTableItemDTO setWorkOrderNumber(String workOrderNumber) {
        this.workOrderNumber = workOrderNumber;
        return this;
    }

    public String getSubWorkOrderNumber() {
        return subWorkOrderNumber;
    }

    public StepCompletionReportTableItemDTO setSubWorkOrderNumber(String subWorkOrderNumber) {
        this.subWorkOrderNumber = subWorkOrderNumber;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public StepCompletionReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public StepCompletionReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public StepCompletionReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkFlowName() {
        return workFlowName;
    }

    public StepCompletionReportTableItemDTO setWorkFlowName(String workFlowName) {
        this.workFlowName = workFlowName;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public StepCompletionReportTableItemDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public StepCompletionReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public StepCompletionReportTableItemDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public StepCompletionReportTableItemDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StepCompletionReportTableItemDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public StepCompletionReportTableItemDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Long getUnQualifiedNumber() {
        return unQualifiedNumber;
    }

    public StepCompletionReportTableItemDTO setUnQualifiedNumber(Long unQualifiedNumber) {
        this.unQualifiedNumber = unQualifiedNumber;
        return this;
    }
}
       
