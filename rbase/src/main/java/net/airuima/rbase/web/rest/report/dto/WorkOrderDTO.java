package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotEmpty;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/26
 */
@FetchEntity
public class WorkOrderDTO implements Serializable {

        /**
         * 工单号
         */
        @Schema(description = "工单号")
        @Excel(name = "工单号",orderNum = "1")
        private String serialNumber;

        /**
         * 工单状态
         */
        @Schema(description = "工单状态")
        @Excel(name = "工单状态",orderNum = "2")
        private Integer status;

        /**
         * 产品谱系编码
         */
        @Schema(description = "产品谱系编码")
        @Excel(name = "产品谱系编码",orderNum = "3")
        private String pedigreeCode;

        /**
         * 产品谱系名称
         */
        @Schema(description = "产品谱系名称")
        @Excel(name = "产品谱系名称",orderNum = "4")
        private String pedigreeName;

        /**
         * 产品型号
         */
        @Schema(description = "产品型号")
        @Excel(name = "产品型号",orderNum = "5")
        private String specification;

        /**
         * 部门ID
         */
        @Schema(description = "部门ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long organizationId;

        @Transient
        @Schema(description = "部门DTO")
        @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
        private OrganizationDTO organizationDto = new OrganizationDTO();

        /**
         * 组织架构编码
         */
        @Schema(description = "组织架构编码")
        @Excel(name = "组织架构编码",orderNum = "6")
        private String orgCode;

        /**
         * 组织架构名称
         */
        @Schema(description = "组织架构名称")
        @Excel(name = "组织架构名称",orderNum = "7")
        private String orgName;

        /**
         * 下达日期
         */
        @Schema(description = "下达日期",example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @Excel(name = "下达日期",orderNum = "8",format = "yyyy-MM-dd HH:mm:ss")
        private Instant createdDate;

        /**
         * 计划开工日期
         */
        @Schema(description = "计划开工日期", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @NotEmpty
        @Excel(name = "计划开工日期",orderNum = "9",format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime planStartDate;

        /**
         * 计划完工日期
         */
        @Schema(description = "计划完工日期", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @NotEmpty
        @Excel(name = "计划完工日期",orderNum = "10",format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime planEndDate;

        /**
         * 实际开工日期
         */
        @Schema(description = "实际开工日期", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @NotEmpty
        @Excel(name = "实际开工日期",orderNum = "11",format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime actualStartDate;

        /**
         * 实际完工日期
         */
        @Schema(description = "实际完工日期", example = "2022-05-23 23:45:33")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @NotEmpty
        @Excel(name = "实际完工日期",orderNum = "12",format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime actualEndDate;

        /**
         * 投产数
         */
        @Schema(description = "投产数")
        @Excel(name = "投产数",orderNum = "13")
        private Integer number;

        /**
         * 合格数
         */
        @Schema(description = "合格数")
        @Excel(name = "合格数",orderNum = "14")
        private Integer qualifiedNumber;

        public WorkOrderDTO() {
        }

        public WorkOrderDTO(String serialNumber, Integer status, String pedigreeCode, String pedigreeName, String specification, Long organizationId,Instant createdDate, LocalDateTime planStartDate, LocalDateTime planEndDate, LocalDateTime actualStartDate, LocalDateTime actualEndDate, Integer number, Integer qualifiedNumber) {
            this.serialNumber = serialNumber;
            this.status = status;
            this.pedigreeCode = pedigreeCode;
            this.pedigreeName = pedigreeName;
            this.specification = specification;
            this.organizationId = organizationId;
            this.createdDate = createdDate;
            this.planStartDate = planStartDate;
            this.planEndDate = planEndDate;
            this.actualStartDate = actualStartDate;
            this.actualEndDate = actualEndDate;
            this.number = number;
            this.qualifiedNumber = qualifiedNumber;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public WorkOrderDTO setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public String getStatus() {
            if (ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName() == status){
                return "异常结单";
            }
            if (ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() == status){
                return "已完成";
            }
            return "其他";
        }

        public WorkOrderDTO setStatus(Integer status) {
            this.status = status;
            return this;
        }

        public String getPedigreeCode() {
            return pedigreeCode;
        }

        public WorkOrderDTO setPedigreeCode(String pedigreeCode) {
            this.pedigreeCode = pedigreeCode;
            return this;
        }

        public String getPedigreeName() {
            return pedigreeName;
        }

        public WorkOrderDTO setPedigreeName(String pedigreeName) {
            this.pedigreeName = pedigreeName;
            return this;
        }

        public String getSpecification() {
            return specification;
        }

        public WorkOrderDTO setSpecification(String specification) {
            this.specification = specification;
            return this;
        }

        public String getOrgCode() {
            if (ObjectUtils.isEmpty(organizationDto.getCode())){
                return this.orgCode;
            }else {
                return organizationDto.getCode();
            }
        }

        public WorkOrderDTO setOrgCode(String orgCode) {
            this.orgCode = orgCode;
            return this;
        }

        public String getOrgName() {
            if (ObjectUtils.isEmpty(organizationDto.getName())){
                return this.orgName;
            }else {
                return organizationDto.getName();
            }
        }

        public WorkOrderDTO setOrgName(String orgName) {
            this.orgName = orgName;
            return this;
        }

        public LocalDateTime getCreatedDate() {
            if (ObjectUtils.isEmpty(createdDate)){
                return null;
            }
            return LocalDateTime.ofInstant(createdDate, ZoneId.systemDefault());
        }

        public WorkOrderDTO setCreatedDate(Instant createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public LocalDateTime getPlanStartDate() {
            return planStartDate;
        }

        public WorkOrderDTO setPlanStartDate(LocalDateTime planStartDate) {
            this.planStartDate = planStartDate;
            return this;
        }

        public LocalDateTime getPlanEndDate() {
            return planEndDate;
        }

        public WorkOrderDTO setPlanEndDate(LocalDateTime planEndDate) {
            this.planEndDate = planEndDate;
            return this;
        }

        public LocalDateTime getActualStartDate() {
            return actualStartDate;
        }

        public WorkOrderDTO setActualStartDate(LocalDateTime actualStartDate) {
            this.actualStartDate = actualStartDate;
            return this;
        }

        public LocalDateTime getActualEndDate() {
            return actualEndDate;
        }

        public WorkOrderDTO setActualEndDate(LocalDateTime actualEndDate) {
            this.actualEndDate = actualEndDate;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public WorkOrderDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public WorkOrderDTO setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Long getOrganizationId() {
            return organizationId;
        }

        public WorkOrderDTO setOrganizationId(Long organizationId) {
            this.organizationId = organizationId;
            return this;
        }

        public OrganizationDTO getOrganizationDto() {
            return organizationDto;
        }

        public WorkOrderDTO setOrganizationDto(OrganizationDTO organizationDto) {
            this.organizationDto = organizationDto;
            return this;
        }

}
