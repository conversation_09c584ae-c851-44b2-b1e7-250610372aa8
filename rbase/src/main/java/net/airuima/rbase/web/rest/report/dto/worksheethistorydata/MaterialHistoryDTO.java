package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.dto.bom.MaterialDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class MaterialHistoryDTO {

    /**
     * 物料信息
     */
    private MaterialDTO materialDTO;

    /**
     * 批次
     */
    private String lot;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,来料检验
         * 1,上架入库
         * 2,生产领料
         * 3,生产退料
         */
        private Integer type;

        /**
         * 来料检验单
         */
        private IqcCheckHistory iqcCheckHistory;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 领料详情
         */
        private WsCheckMaterialDetail wsCheckMaterialDetail;

        /**
         * 退料详情
         */
        private WsMaterialReturn wsMaterialReturn;

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public IqcCheckHistory getIqcCheckHistory() {
            return iqcCheckHistory;
        }

        public HistoryDTO setIqcCheckHistory(IqcCheckHistory iqcCheckHistory) {
            this.iqcCheckHistory = iqcCheckHistory;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public WsCheckMaterialDetail getWsCheckMaterialDetail() {
            return wsCheckMaterialDetail;
        }

        public HistoryDTO setWsCheckMaterialDetail(WsCheckMaterialDetail wsCheckMaterialDetail) {
            this.wsCheckMaterialDetail = wsCheckMaterialDetail;
            return this;
        }

        public WsMaterialReturn getWsMaterialReturn() {
            return wsMaterialReturn;
        }

        public HistoryDTO setWsMaterialReturn(WsMaterialReturn wsMaterialReturn) {
            this.wsMaterialReturn = wsMaterialReturn;
            return this;
        }
    }

    public MaterialDTO getMaterialDTO() {
        return materialDTO;
    }

    public MaterialHistoryDTO setMaterialDTO(MaterialDTO materialDTO) {
        this.materialDTO = materialDTO;
        return this;
    }

    public String getLot() {
        return lot;
    }

    public MaterialHistoryDTO setLot(String lot) {
        this.lot = lot;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public MaterialHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
