package net.airuima.rbase.web.rest.report.dto.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计报表看板请求DTO
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Schema(description = "产量统计报表看板请求DTO")
public class ProductionCapacityReportRequestDTO extends PageDTO {

    /**
     * 计划完工时间类型
     */
    @Schema(description = "计划完工时间类型 0今天 1本周 2本月")
    private Integer planFinishTimeCategory;


    /**
     * 产品谱系id(最小层级)
     */
    @Schema(description = "产品谱系id(最小层级)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    /**
     * 生产线id
     */
    @Schema(description = "产线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @JsonSerialize(using = ToStringSerializer.class)
    private String serialNumber;

    /**
     * 下单日期开始范围
     */
    @Schema(description = "下单日期开始范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 下单日期结束范围
     */
    @Schema(description = "下单日期结束范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;


    /**
     * 产量分布类型
     */
    @Schema(description = "产量分布类型 0 部门分布 1 产线分布")
    private Integer distributionCategory;


    /**
     * 下单日期结束范围
     */
    @Schema(description = "是否导出 true导出 false不导出")
    private Boolean exportStatus;

    /**
     * 报表类型
     */
    @Schema(description = "报表类型 0 工单 1子工单")
    private Integer reportType;

    /**
     * 统计报表图片Base64
     */
    @Schema(description = "统计报表图片Base64")
    private String graph;

    /**
     * 小时序号 1-24正整数
     */
    @Schema(description = "小时序号 1-24正整数")
    private Integer hour;

    /**
     * 工序产出推移图分类 1 工序  2 工序组
     */
    @Schema(description = "工序产出推移图分类 1 工序  2 工序组")
    private Integer stepCategory;

    /**
     * 日期
     */
    @Schema(description = "日期")
    private String day;



    /**
     * 工艺路线id
     */
    @Schema(description = "工艺路线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workFlowId;

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public ProductionCapacityReportRequestDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public String getDay() {
        return day;
    }

    public ProductionCapacityReportRequestDTO setDay(String day) {
        this.day = day;
        return this;
    }

    public Integer getStepCategory() {
        return stepCategory;
    }

    public ProductionCapacityReportRequestDTO setStepCategory(Integer stepCategory) {
        this.stepCategory = stepCategory;
        return this;
    }

    public Integer getHour() {
        return hour;
    }

    public ProductionCapacityReportRequestDTO setHour(Integer hour) {
        this.hour = hour;
        return this;
    }

    public Integer getPlanFinishTimeCategory() {
        return planFinishTimeCategory;
    }

    public ProductionCapacityReportRequestDTO setPlanFinishTimeCategory(Integer planFinishTimeCategory) {
        this.planFinishTimeCategory = planFinishTimeCategory;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public ProductionCapacityReportRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public ProductionCapacityReportRequestDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public ProductionCapacityReportRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public ProductionCapacityReportRequestDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public ProductionCapacityReportRequestDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public ProductionCapacityReportRequestDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Boolean getExportStatus() {
        return exportStatus;
    }

    public ProductionCapacityReportRequestDTO setExportStatus(Boolean exportStatus) {
        this.exportStatus = exportStatus;
        return this;
    }

    public Integer getReportType() {
        return reportType;
    }

    public ProductionCapacityReportRequestDTO setReportType(Integer reportType) {
        this.reportType = reportType;
        return this;
    }

    public Integer getDistributionCategory() {
        return distributionCategory;
    }

    public ProductionCapacityReportRequestDTO setDistributionCategory(Integer distributionCategory) {
        this.distributionCategory = distributionCategory;
        return this;
    }

    public String getGraph() {
        return graph;
    }

    public ProductionCapacityReportRequestDTO setGraph(String graph) {
        this.graph = graph;
        return this;
    }
}
