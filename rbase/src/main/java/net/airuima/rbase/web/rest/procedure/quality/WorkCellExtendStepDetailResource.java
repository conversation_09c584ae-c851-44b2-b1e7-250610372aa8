package net.airuima.rbase.web.rest.procedure.quality;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendStepDetail;
import net.airuima.rbase.service.procedure.quality.WorkCellExtendStepDetailService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工位宽放过站记录详情Resource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "工位宽放过站记录详情Resource")
@RestController
@RequestMapping("/api/work-cell-extend-step-details")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("FAI || IPQC ")
@AuthSkip("ID")
public class WorkCellExtendStepDetailResource extends ProtectBaseResource<WorkCellExtendStepDetail> {

    private static final String MODULE = "工位宽放过站记录详情";
    private final WorkCellExtendStepDetailService workCellExtendStepDetailService;

    public WorkCellExtendStepDetailResource(WorkCellExtendStepDetailService workCellExtendStepDetailService) {
        this.workCellExtendStepDetailService = workCellExtendStepDetailService;
        this.mapUri = "/api/work-cell-extend-step-details";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
