package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序达成表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Schema(description = "工序达成表格数据DTO")
public class StepCompletionReportTableResultDTO extends PageDTO {

    /**
     * 工序达成表格明细集合
     */
    @Schema(description = "工序达成表格明细集合")
    private List<StepCompletionReportTableItemDTO> stepCompletionReportTableItemList;


    public List<StepCompletionReportTableItemDTO> getStepCompletionReportTableItemList() {
        return stepCompletionReportTableItemList;
    }

    public StepCompletionReportTableResultDTO setStepCompletionReportTableItemList(List<StepCompletionReportTableItemDTO> stepCompletionReportTableItemList) {
        this.stepCompletionReportTableItemList = stepCompletionReportTableItemList;
        return this;
    }
}
