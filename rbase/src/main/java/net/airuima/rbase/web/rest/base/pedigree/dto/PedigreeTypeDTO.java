package net.airuima.rbase.web.rest.base.pedigree.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系类型配置DTO
 *
 * <AUTHOR>
 * @date 2023/07/13
 */
@Schema(description = "产品谱系类型配置DTO")
public class PedigreeTypeDTO {


    /**
     * key值
     */
    @Schema(description = "key值")
    private int key;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    private String value;


    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }



}
