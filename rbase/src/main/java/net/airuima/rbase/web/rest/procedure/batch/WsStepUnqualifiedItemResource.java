package net.airuima.rbase.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.dto.batch.ReworkGetSubWsDTO;
import net.airuima.rbase.service.procedure.batch.WsStepUnqualifiedItemService;
import net.airuima.rbase.web.rest.procedure.batch.dto.WsStepUnqualifiedItemDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工序不良项目统计Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工单工序不良项目统计Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-step-unqualified-items")
@AuthorityRegion("生产质量数据")
@AuthSkip("ICUD")
public class WsStepUnqualifiedItemResource extends ProtectBaseResource<WsStepUnqualifiedItem> {
    private static final String MODULE = "批量生产不良明细";
    private final WsStepUnqualifiedItemService wsStepUnqualifiedItemService;

    public WsStepUnqualifiedItemResource(WsStepUnqualifiedItemService wsStepUnqualifiedItemService) {
        this.wsStepUnqualifiedItemService = wsStepUnqualifiedItemService;
        this.mapUri = "/api/ws-step-unqualified-items";
    }

    /**
     * 根据投产工单id及工序id获取不良信息
     * <AUTHOR>
     * @param productWorkSheetId 投产工单ID
     * @param stepId 工序ID
     * @return List<WsStepUnqualifiedItem>
     * @date 2021-04-15
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过投产粒度工单主键ID及生产工序主键ID获取工序生产不良信息",parameters = {
            @Parameter(name = "productWorkSheetId",description = "投产粒度工单ID",example = "子工单粒度投产时则为子工单主键ID,工单粒度投产时则为工单主键ID",required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH),
            @Parameter(name = "stepId",description = "生产工序主键ID",required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH)
    })
    @GetMapping("/work-sheets/{productWorkSheetId}/steps/{stepId}/unqualified-items")
    public ResponseEntity<ResponseData<List<WsStepUnqualifiedItemDTO>>> findByProductWorkSheetIdAndStepId(@PathVariable("productWorkSheetId") Long productWorkSheetId, @PathVariable("stepId") Long stepId){
        return ResponseData.ok(wsStepUnqualifiedItemService.findByProductWorkSheetIdAndStepId(productWorkSheetId,stepId));
    }

    /**
     * 通过总工单ID获取可以生成返工单的所有不良相关信息
     * <AUTHOR>
     * @param workSheetId  总工单ID
     * @return ResponseEntity<ResponseContent<ReworkGetSubWsUnqualifiedGroupDTO>>
     * @date 2021-04-29
     **/
    @Operation(summary = "通过总工单ID获取可以生成返工单的所有不良相关信息")
    @GetMapping("/subWorkSheetUnqualifiedInfo/{workSheetId}")
    public ResponseEntity<ResponseData<ReworkGetSubWsDTO>> findSubWorkSheetUnqualifiedInfo(@PathVariable("workSheetId") Long workSheetId, @RequestParam("stepOrWsLevel") Integer stepOrWsLevel){
        try{
            return ResponseData.ok(wsStepUnqualifiedItemService.findSubWorkSheetUnqualifiedInfo(workSheetId, stepOrWsLevel));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
