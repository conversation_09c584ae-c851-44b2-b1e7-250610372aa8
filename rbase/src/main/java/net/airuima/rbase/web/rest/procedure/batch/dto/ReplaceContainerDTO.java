package net.airuima.rbase.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 替换容器dto
 *
 * <AUTHOR>
 * @date 2022/1/26
 */
public class ReplaceContainerDTO {

    /**
     * 容器详情id与容器替换之间的对应关系实体DTO列表
     */
    private List<ContainerDetailInfoDTO> containerDetailInfoDtoList;

    public List<ContainerDetailInfoDTO> getContainerDetailInfoDtoList() {
        return containerDetailInfoDtoList;
    }

    public ReplaceContainerDTO setContainerDetailInfoDtoList(List<ContainerDetailInfoDTO> containerDetailInfoDtoList) {
        this.containerDetailInfoDtoList = containerDetailInfoDtoList;
        return this;
    }

    /**
     * 容器详情id与容器替换之间的对应关系实体DTO
     */
    public static class ContainerDetailInfoDTO{
        /**
         * 容器详情id
         */
        @Schema(description = "容器详情id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long containerDetailId;

        /**
         * 容器id
         */
        @Schema(description = "容器id")
        private String containerCode;

        public Long getContainerDetailId() {
            return containerDetailId;
        }

        public ContainerDetailInfoDTO setContainerDetailId(Long containerDetailId) {
            this.containerDetailId = containerDetailId;
            return this;
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ContainerDetailInfoDTO setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }
    }
}
