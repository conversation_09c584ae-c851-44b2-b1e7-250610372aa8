package net.airuima.rbase.web.rest.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表看板表格数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
@Schema(description = "不良报表看板表格数据DTO")
public class UnqualifiedReportTableItemDTO {

    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "记录时间", orderNum = "1", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordTime;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号", orderNum = "2")
    private String workSheetSerialNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    @Excel(name = "子工单号", orderNum = "3")
    private String subWorkSheetSerialNumber;


    /**
     * 不合格项目代码
     */
    @Schema(description = "不合格项目代码")
    @Excel(name = "不合格项目代码", orderNum = "4")
    private String unqualifiedItemCode;

    /**
     * 不合格项目名称
     */
    @Schema(description = "不合格项目名称")
    @Excel(name = "不合格项目名称", orderNum = "5")
    private String unqualifiedItemName;


    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码", orderNum = "6")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称", orderNum = "7")
    private String pedigreeName;


    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    @Excel(name = "规格型号", orderNum = "8")
    private String specification;

    /**
     * 生产线名字
     */
    @Schema(description = "生产线名字")
    @Excel(name = "生产线名字", orderNum = "9")
    private String workLineName;


    /**
     * 工序名字
     */
    @Schema(description = "工序名字")
    @Excel(name = "工序名字", orderNum = "10")
    private String stepName;


    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工序编码", orderNum = "11")
    private String stepCode;

    /**
     * 容器名称
     */
    @Schema(description = "容器名称")
    @Excel(name = "容器名称", orderNum = "12")
    private String containerName;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    @Excel(name = "容器编码", orderNum = "13")
    private String containerCode;

    /**
     * sn
     */
    @Schema(description = "sn")
    @Excel(name = "sn", orderNum = "14")
    private String sn;


    /**
     * 不良数量
     */
    @Schema(description = "不良数量")
    @Excel(name = "不良数量", orderNum = "15")
    private Integer number;


    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public UnqualifiedReportTableItemDTO setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public UnqualifiedReportTableItemDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public UnqualifiedReportTableItemDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public String getUnqualifiedItemCode() {
        return unqualifiedItemCode;
    }

    public UnqualifiedReportTableItemDTO setUnqualifiedItemCode(String unqualifiedItemCode) {
        this.unqualifiedItemCode = unqualifiedItemCode;
        return this;
    }

    public String getUnqualifiedItemName() {
        return unqualifiedItemName;
    }

    public UnqualifiedReportTableItemDTO setUnqualifiedItemName(String unqualifiedItemName) {
        this.unqualifiedItemName = unqualifiedItemName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public UnqualifiedReportTableItemDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public UnqualifiedReportTableItemDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public UnqualifiedReportTableItemDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public UnqualifiedReportTableItemDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public UnqualifiedReportTableItemDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public UnqualifiedReportTableItemDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getContainerName() {
        return containerName;
    }

    public UnqualifiedReportTableItemDTO setContainerName(String containerName) {
        this.containerName = containerName;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public UnqualifiedReportTableItemDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public UnqualifiedReportTableItemDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public UnqualifiedReportTableItemDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }
}
