package net.airuima.rbase.web.rest.report.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.OrganizationDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单统计按部门分组查询结果DTO
 *
 * <AUTHOR>
 * @date 2023/06/20
 */
@FetchEntity
@Schema(description = "工单统计按部门分组查询结果DTO")
public class WorkSheetOrganizationPlanChartDTO {

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    @Transient
    @Schema(description = "部门DTO")
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 数量
     */
    @Schema(description = "数量", required = true)
    private Long number;

    /**
     * 完成类型 计划完成 实际完成
     */
    @Schema(description = "完成类型 计划完成 实际完成")
    private String type;


    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkSheetOrganizationPlanChartDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public WorkSheetOrganizationPlanChartDTO setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkSheetOrganizationPlanChartDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public String getType() {
        return type;
    }

    public WorkSheetOrganizationPlanChartDTO setType(String type) {
        this.type = type;
        return this;
    }

    public WorkSheetOrganizationPlanChartDTO() {
    }

    public WorkSheetOrganizationPlanChartDTO(Long organizationId, Long number, String type) {
        this.organizationId = organizationId;
        this.number = number;
        this.type = type;
    }
}
