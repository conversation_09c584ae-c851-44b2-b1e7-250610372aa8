package net.airuima.rbase.web.rest.report.dto.perform;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计报表排行榜数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
@Schema(description = "报工统计报表排行榜数据DTO")
public class StaffPerformStatisticReportRankResultDTO {

    /**
     * 员工报工排名图形数据
     */
    @Schema(description = "员工报工排名图形数据")
    private List<net.airuima.rbase.web.rest.report.dto.perform.StaffRankDataDTO> staffRankDataList;


    /**
     * 工位排名图形数据
     */
    @Schema(description = "工位排名图形数据")
    private List<net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO> workCellRankDataList;


    /**
     * 工序排名图形数据
     */
    @Schema(description = "工序排名图形数据")
    private List<net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO> stepRankDataList;


    public List<net.airuima.rbase.web.rest.report.dto.perform.StaffRankDataDTO> getStaffRankDataList() {
        return staffRankDataList;
    }

    public StaffPerformStatisticReportRankResultDTO setStaffRankDataList(List<net.airuima.rbase.web.rest.report.dto.perform.StaffRankDataDTO> staffRankDataList) {
        this.staffRankDataList = staffRankDataList;
        return this;
    }

    public List<net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO> getWorkCellRankDataList() {
        return workCellRankDataList;
    }

    public StaffPerformStatisticReportRankResultDTO setWorkCellRankDataList(List<net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO> workCellRankDataList) {
        this.workCellRankDataList = workCellRankDataList;
        return this;
    }

    public List<net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO> getStepRankDataList() {
        return stepRankDataList;
    }

    public StaffPerformStatisticReportRankResultDTO setStepRankDataList(List<net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO> stepRankDataList) {
        this.stepRankDataList = stepRankDataList;
        return this;
    }
}
