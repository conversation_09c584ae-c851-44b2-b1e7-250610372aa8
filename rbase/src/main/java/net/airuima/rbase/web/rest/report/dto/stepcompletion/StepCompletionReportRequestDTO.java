package net.airuima.rbase.web.rest.report.dto.stepcompletion;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序达成请求参数DTO
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Schema(description = "工序达成请求参数DTO")
public class StepCompletionReportRequestDTO extends PageDTO {

    /**
     * 计划时间类型
     */
    @Schema(description = "计划时间类型 0今天 1本周 2本月")
    private Integer planFinishTimeCategory;

    /**
     * 产品谱系id(最小层级)
     */
    @Schema(description = "产品谱系id(最小层级)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 工序组id
     */
    @Schema(description = "工序组id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepGroupId;


    /**
     * 查询开始时间
     */
    @Schema(description = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 查询结束时间
     */
    @Schema(description = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 是否导出
     */
    @Schema(description = "是否导出 true导出 false不导出")
    private Boolean exportStatus;

    /**
     * 报表类型
     */
    @Schema(description = "报表类型 0 工单 1子工单")
    private Integer reportType;

    public Integer getPlanFinishTimeCategory() {
        return planFinishTimeCategory;
    }

    public StepCompletionReportRequestDTO setPlanFinishTimeCategory(Integer planFinishTimeCategory) {
        this.planFinishTimeCategory = planFinishTimeCategory;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public StepCompletionReportRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getStepGroupId() {
        return stepGroupId;
    }

    public StepCompletionReportRequestDTO setStepGroupId(Long stepGroupId) {
        this.stepGroupId = stepGroupId;
        return this;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public StepCompletionReportRequestDTO setStartDate(LocalDate startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public StepCompletionReportRequestDTO setEndDate(LocalDate endDate) {
        this.endDate = endDate;
        return this;
    }

    public Boolean getExportStatus() {
        return exportStatus;
    }

    public StepCompletionReportRequestDTO setExportStatus(Boolean exportStatus) {
        this.exportStatus = exportStatus;
        return this;
    }

    public Integer getReportType() {
        return reportType;
    }

    public StepCompletionReportRequestDTO setReportType(Integer reportType) {
        this.reportType = reportType;
        return this;
    }
}
