package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/9
 */
@Schema(description = "不良记录处理结果DTO")
public class InspectUnqualifiedResultDTO implements Serializable {

    /**
     * 不良品记录id
     */
    @Schema(description = "不良品记录id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inspectUnqualifiedId;

    /**
     * 结果:0待处理，1维修分析
     */
    @Schema(description = "结果:0待处理，1维修分析")
    private Integer dealResult;

    /**
     * sn对应不良项目列表
     */
    @Schema(description = "sn对应不良项目列表")
    private List<SnUnqualifiedItem> snUnqualifiedItemList;

    @Schema(description = "sn对应不良项目")
    public static class SnUnqualifiedItem implements Serializable{
        /**
         * sn
         */
        @Schema(description = "sn")
        private String sn;

        /**
         * 不良项目id
         */
        @Schema(description = "不良项目id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        public String getSn() {
            return sn;
        }

        public SnUnqualifiedItem setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public SnUnqualifiedItem setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }
    }

    public Long getInspectUnqualifiedId() {
        return inspectUnqualifiedId;
    }

    public InspectUnqualifiedResultDTO setInspectUnqualifiedId(Long inspectUnqualifiedId) {
        this.inspectUnqualifiedId = inspectUnqualifiedId;
        return this;
    }

    public Integer getDealResult() {
        return dealResult;
    }

    public InspectUnqualifiedResultDTO setDealResult(Integer dealResult) {
        this.dealResult = dealResult;
        return this;
    }

    public List<SnUnqualifiedItem> getSnUnqualifiedItemList() {
        return snUnqualifiedItemList;
    }

    public InspectUnqualifiedResultDTO setSnUnqualifiedItemList(List<SnUnqualifiedItem> snUnqualifiedItemList) {
        this.snUnqualifiedItemList = snUnqualifiedItemList;
        return this;
    }
}
