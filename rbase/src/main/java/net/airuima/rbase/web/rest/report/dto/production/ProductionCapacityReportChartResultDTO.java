package net.airuima.rbase.web.rest.report.dto.production;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计图形查询结果 DTO
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Schema(description = "产量统计图形查询结果DTO")
public class ProductionCapacityReportChartResultDTO {

    /**
     * 总工单数
     */
    @Schema(description = "总工单数")
    private Long countNumber;

    /**
     * 总投产数
     */
    @Schema(description = "总投产数")
    private Long number;

    /**
     * 总产量
     */
    @Schema(description = "总产量")
    private Long actualFinishNumber;

    /**
     * 总产出(总合格数)
     */
    @Schema(description = "总产出")
    private Long qualifiedNumber;



    /**
     * 产量折线图统计图形数据
     */
    @Schema(description = "产量折线图统计图形数据")
    private List<ProductionCapacityLineChartDataDTO> productionCapacityLineChartDataList;


    public List<ProductionCapacityLineChartDataDTO> getProductionCapacityLineChartDataList() {
        return productionCapacityLineChartDataList;
    }

    public ProductionCapacityReportChartResultDTO setProductionCapacityLineChartDataList(List<ProductionCapacityLineChartDataDTO> productionCapacityLineChartDataList) {
        this.productionCapacityLineChartDataList = productionCapacityLineChartDataList;
        return this;
    }

    public Long getCountNumber() {
        return countNumber;
    }

    public ProductionCapacityReportChartResultDTO setCountNumber(Long countNumber) {
        this.countNumber = countNumber;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public ProductionCapacityReportChartResultDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Long getActualFinishNumber() {
        return actualFinishNumber;
    }

    public ProductionCapacityReportChartResultDTO setActualFinishNumber(Long actualFinishNumber) {
        this.actualFinishNumber = actualFinishNumber;
        return this;
    }

    public Long getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ProductionCapacityReportChartResultDTO setQualifiedNumber(Long qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }
}
