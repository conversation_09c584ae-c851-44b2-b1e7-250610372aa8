package net.airuima.rbase.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *  工序回退实体dto
 *
 * <AUTHOR>
 * @date 2022/2/7
 */
@Schema(description = "工序回退DTO")
public class RollBackDTO {

    /**
     * 当前工序详情id
     */
    @Schema(description = "当前工序详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long batchWorkDetailId;

    /**
     * 当前工序详情id
     */
    @Schema(description = "当前容器详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long containerDetailId;

    /**
     * 容器详情id与容器替换之间的对应关系实体DTO
     */
    @Schema(description = "容器详情id与容器替换之间的对应关系实体DTO")
    private List<ContainerDetailInfoDTO> containerDetailInfoDtoList;

    /**
     * 操作员工id
     */
    @Schema(description = "操作员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 回退原因
     */
    private String note;

    /**
     * 容器详情id与容器替换之间的对应关系实体DTO
     */
    public static class ContainerDetailInfoDTO{
        /**
         * 容器详情id
         */
        @Schema(description = "容器详情id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long containerDetailId;

        /**
         * 容器id
         */
        @Schema(description = "容器id")
        private String containerCode;

        public Long getContainerDetailId() {
            return containerDetailId;
        }

        public ContainerDetailInfoDTO setContainerDetailId(Long containerDetailId) {
            this.containerDetailId = containerDetailId;
            return this;
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ContainerDetailInfoDTO setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }
    }


    public Long getContainerDetailId() {
        return containerDetailId;
    }

    public RollBackDTO setContainerDetailId(Long containerDetailId) {
        this.containerDetailId = containerDetailId;
        return this;
    }

    public Long getBatchWorkDetailId() {
        return batchWorkDetailId;
    }

    public RollBackDTO setBatchWorkDetailId(Long batchWorkDetailId) {
        this.batchWorkDetailId = batchWorkDetailId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RollBackDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public String getNote() {
        return note;
    }

    public RollBackDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public List<ContainerDetailInfoDTO> getContainerDetailInfoDtoList() {
        return containerDetailInfoDtoList;
    }

    public RollBackDTO setContainerDetailInfoDtoList(List<ContainerDetailInfoDTO> containerDetailInfoDtoList) {
        this.containerDetailInfoDtoList = containerDetailInfoDtoList;
        return this;
    }
}
