package net.airuima.rbase.web.rest.procedure.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线DTO
 *
 * <AUTHOR>
 * @date 2023/09/21
 */
@Schema(description = "转工艺路线DTO")
public class WorkflowConvertDTO {


    /**
     * 子工单id
     */
    @Schema(description = "子工单id 子工单投产时传")
    private Long subWorkSheetId;


    /**
     * 工单id
     */
    @Schema(description = "工单id  工单投产时传")
    private Long workSheetId;


    /**
     * 当前工序id
     */
    @Schema(description = "当前工序id")
    private Long stepId;


    /**
     * 转工艺流程id
     */
    @Schema(description = "转工艺流程id")
    private Long workflowId;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public WorkflowConvertDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public WorkflowConvertDTO setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public WorkflowConvertDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getWorkflowId() {
        return workflowId;
    }

    public WorkflowConvertDTO setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
        return this;
    }
}
