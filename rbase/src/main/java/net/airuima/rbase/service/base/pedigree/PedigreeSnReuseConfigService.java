package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig;
import net.airuima.rbase.repository.base.pedigree.PedigreeSnReuseConfigRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * SN复用配置Service
 * <AUTHOR>
 * @date 2021-02-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeSnReuseConfigService extends CommonJpaService<PedigreeSnReuseConfig> {

    private static final String PEDIGREE_SN_REUSE_CONFIG_ENTITY_GRAPH = "pedigreeSnReuseConfigEntityGraph";

    private final PedigreeSnReuseConfigRepository pedigreeSnReuseConfigRepository;

    public PedigreeSnReuseConfigService(PedigreeSnReuseConfigRepository pedigreeSnReuseConfigRepository) {
        this.pedigreeSnReuseConfigRepository = pedigreeSnReuseConfigRepository;
    }

    @Override
    public Page<PedigreeSnReuseConfig> find(Specification<PedigreeSnReuseConfig> spec, Pageable pageable) {
        return pedigreeSnReuseConfigRepository.findAll(spec,pageable,new NamedEntityGraph(PEDIGREE_SN_REUSE_CONFIG_ENTITY_GRAPH));
    }

    @Override
    public List<PedigreeSnReuseConfig> find(Specification<PedigreeSnReuseConfig> spec) {
        return pedigreeSnReuseConfigRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_SN_REUSE_CONFIG_ENTITY_GRAPH));
    }

    @Override
    public Page<PedigreeSnReuseConfig> findAll(Pageable pageable) {
        return pedigreeSnReuseConfigRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_SN_REUSE_CONFIG_ENTITY_GRAPH));
    }
}
