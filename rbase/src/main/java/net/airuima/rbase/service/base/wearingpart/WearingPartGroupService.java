package net.airuima.rbase.service.base.wearingpart;

import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.wearingpart.WearingPartGroup;
import net.airuima.rbase.repository.base.wearingpart.WearingPartGroupRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 易损件类型service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WearingPartGroupService extends CommonJpaService<WearingPartGroup> {

    @Autowired
    private WearingPartGroupRepository wearingPartGroupRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPartGroup> find(Specification<WearingPartGroup> spec, Pageable pageable) {
        return wearingPartGroupRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WearingPartGroup> find(Specification<WearingPartGroup> spec) {
        return wearingPartGroupRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPartGroup> findAll(Pageable pageable) {
        return wearingPartGroupRepository.findAll(pageable);
    }

    /**
     * 根据易损件类型名称或者编码获取易损件类型列表
     *
     * @param text 易损件类型名称或者编码
     * @param size 返回个数
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartGroup>
     * <AUTHOR>
     * @date 2021/6/25
     */
    @Transactional(readOnly = true)
    public List<WearingPartGroup> findByCodeOrName(String text, int size) {
        Page<WearingPartGroup> wearingPartGroupsPage = wearingPartGroupRepository.findByCodeOrName(text, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(wearingPartGroupsPage).map(Slice::getContent).orElse(null);
    }

    /**
     * 模糊查询获取和原始易损件属性相同的易损件种类列表
     *
     * @param originWearingPartId 原始易损件种类ID
     * @param text                易损件类型名称或者编码
     * @param size                返回个数
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartGroup> 易损件种类列表
     */
    @Transactional(readOnly = true)
    public List<WearingPartGroup> findSameAttributeByLikeKeyWord(Long originWearingPartId, String text, int size) {
        Page<WearingPartGroup> wearingPartGroupsPage = wearingPartGroupRepository.findByCodeOrName(text, PageRequest.of(Constants.INT_ZERO, size));
        List<WearingPartGroup> wearingPartGroupList = Optional.ofNullable(wearingPartGroupsPage).map(Slice::getContent).orElse(null);
        if (CollectionUtils.isEmpty(wearingPartGroupList)) {
            return new ArrayList<>();
        }
        WearingPartGroup originWearingPartGroup = wearingPartGroupRepository.getReferenceById(originWearingPartId);
        return wearingPartGroupList.stream().filter(wearingPartGroup -> !wearingPartGroup.getId().equals(originWearingPartGroup.getId())
                && wearingPartGroup.getAutoWorkRecord() == originWearingPartGroup.getAutoWorkRecord()
                && wearingPartGroup.getGranularity() == originWearingPartGroup.getGranularity()).toList();
    }
}
