package net.airuima.rbase.service.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.constant.WorkCellStartCheckEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.rqms.RbaseMrbProxy;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 待检任务表Service
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectTaskService extends CommonJpaService<InspectTask> {

    private final String INSPECT_TASK_ENTITY_GRAPH = "inspectTaskEntityGraph";
    private final InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private RbaseMrbProxy rbaseMrbProxy;

    public InspectTaskService(InspectTaskRepository inspectTaskRepository) {
        this.inspectTaskRepository = inspectTaskRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<InspectTask> find(Specification<InspectTask> spec, Pageable pageable) {
        return inspectTaskRepository.findAll(spec, pageable, new NamedEntityGraph(INSPECT_TASK_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<InspectTask> find(Specification<InspectTask> spec) {
        return inspectTaskRepository.findAll(spec, new NamedEntityGraph(INSPECT_TASK_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<InspectTask> findAll(Pageable pageable) {
        return inspectTaskRepository.findAll(pageable, new NamedEntityGraph(INSPECT_TASK_ENTITY_GRAPH));
    }

    /**
     * 获取所有待检任务
     *
     * <AUTHOR>
     * @date 2023/5/5
     */
    public List<InspectTask> getAll() {
        return inspectTaskRepository.findByStatusAndDeleted(Boolean.FALSE, Constants.LONG_ZERO);
    }


    /**
     * 验证 投产工单 在工序 中 所有的待检任务
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param step         工序
     * @return BaseDTO
     */
    public BaseDTO getTodoStepTask(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step) {

        //判断是否存在待质检(抽检、终检、末检)分析的任务
        Long todoTaskCount = Objects.nonNull(subWorkSheet) ?
                inspectTaskRepository.countBySubWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), step.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO) :
                inspectTaskRepository.countByWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), step.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);

        if (Objects.nonNull(todoTaskCount) && todoTaskCount > Constants.INT_ZERO) {
            return new BaseDTO(Constants.KO, "存在待质检(抽检、终检、末检)分析的任务");
        }
        //判断是否存在待质检(抽检、终检、末检)历史未处理
        todoTaskCount = Objects.nonNull(subWorkSheet) ? checkHistoryRepository.countBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(subWorkSheet.getId(), step.getId(), Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_ZERO, Constants.LONG_ZERO) :
                checkHistoryRepository.countByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(workSheet.getId(), step.getId(), Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_ZERO, Constants.LONG_ZERO);
        if (todoTaskCount > Constants.INT_ZERO) {
            return new BaseDTO(Constants.KO, "存在待质检(抽检、终检、末检)历史未处理");
        }
        //判断 是否存在 抽检、终检、末检的处理任务是MRB处理，如果存在 则需要查看是否MRB处理结束
        List<CheckHistory> checkHistoryList = Objects.nonNull(subWorkSheet) ? checkHistoryRepository.findBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(subWorkSheet.getId(), step.getId(),
                Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_FIVE, Constants.LONG_ZERO) :
                checkHistoryRepository.findByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(workSheet.getId(), step.getId(),
                        Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_FIVE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(checkHistoryList)) {
            todoTaskCount = rbaseMrbProxy.countTodoMrbTask(checkHistoryList.stream().map(CheckHistory::getSerialNumber).distinct().toList());
            if (todoTaskCount > Constants.LONG_ZERO) {
                return new BaseDTO(Constants.KO, "存在MRB任务未处理");
            }
        }
        //判断是否存在待维修分析的任务
        todoTaskCount = Objects.nonNull(subWorkSheet) ? rbaseMaintainHistoryProxy.countBySubWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(subWorkSheet.getId(), step.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO)
                : rbaseMaintainHistoryProxy.countByWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(workSheet.getId(), step.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return new BaseDTO(Constants.KO, "存在待维修分析的任务未处理");
        }
        //判断是否存在不良复检未处理
        todoTaskCount = Objects.nonNull(subWorkSheet) ? stepReinspectRepository.countBySubWorkSheetIdAndStepIdAndStatusAndDeleted(subWorkSheet.getId(), step.getId(), Boolean.FALSE, Constants.LONG_ZERO)
                : stepReinspectRepository.countByWorkSheetIdAndStepIdAndStatusAndDeleted(workSheet.getId(), step.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return new BaseDTO(Constants.KO, "存在不良复检未处理");
        }
        return new BaseDTO(Constants.OK);
    }

}
