package net.airuima.rbase.service.base.scene;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 部门区域表Service
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OrganizationAreaService extends CommonJpaService<OrganizationArea> {

    private final OrganizationAreaRepository organizationAreaRepository;

    public OrganizationAreaService(OrganizationAreaRepository organizationAreaRepository) {
        this.organizationAreaRepository = organizationAreaRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<OrganizationArea> find(Specification<OrganizationArea> spec, Pageable pageable) {
        return organizationAreaRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<OrganizationArea> find(Specification<OrganizationArea> spec) {
        return organizationAreaRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<OrganizationArea> findAll(Pageable pageable) {
        return organizationAreaRepository.findAll(pageable);
    }

    /**
     * 根据部门ID查询部门区域列表接口
     *
     * @param organizationId 部门ID
     * @return List<OrganizationArea>
     */
    @FetchMethod
    @Transactional(readOnly = true)
    public List<OrganizationArea> findByOrganizationId(Long organizationId) {
        List<OrganizationArea> organizationAreaList = organizationAreaRepository.findByOrganizationIdAndDeleted(organizationId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(organizationAreaList)) {
            throw new ResponseException("error.OrganizationAreaNotExist", "当前部门未配置区域列表");
        }
        return organizationAreaList;
    }

    /**
     * 根据区域编码或者名称获取部门区域
     *
     * @param text 框图编码或者名称
     * @param size 最大返回数据条数
     * <AUTHOR>
     **/
    public List<OrganizationArea> findByCodeOrName(String text, Integer size) {
        Page<OrganizationArea> organizationAreaPage = organizationAreaRepository.findByCodeOrName(text, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(organizationAreaPage).map(Slice::getContent).orElse(null);
    }
}
