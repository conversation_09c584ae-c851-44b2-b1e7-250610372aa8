package net.airuima.rbase.service.base.wearingpart;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.rbase.repository.base.wearingpart.WearingPartExchangeRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *易损件种类替代Service
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WearingPartExchangeService extends CommonJpaService<WearingPartExchange> {

    @Autowired
    private WearingPartExchangeRepository wearingPartExchangeRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPartExchange> find(Specification<WearingPartExchange> spec, Pageable pageable) {
        return wearingPartExchangeRepository.findAll(spec,pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WearingPartExchange> find(Specification<WearingPartExchange> spec) {
        return wearingPartExchangeRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPartExchange> findAll(Pageable pageable) {
        return wearingPartExchangeRepository.findAll(pageable);
    }

    /**
     * 根据替换易损件类型查询替代关系
     *
     * @param exchangeGroupId 替换易损件类型id
     * <AUTHOR>
     * @date 2022/10/28
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartExchange>
     */
    @Transactional(readOnly = true)
    public List<WearingPartExchange> findByExchangeGroupId(Long exchangeGroupId) {
        return wearingPartExchangeRepository.findByExchangeWearingPartGroupIdAndDeleted(exchangeGroupId, Constants.LONG_ZERO);
    }

    /**
     * 根据易损件类型、替换易损件类型查询关联关系
     *
     * @param originGroupId 易损件类型id
     * @param exchangeGroupId 替换易损件类型id
     * <AUTHOR>
     * @date 2022/10/28
     * @return WearingPartExchange
     */
    public WearingPartExchange findByOriginWearingPartGroupIdAndExchangeWearingPartGroupId(Long originGroupId, Long exchangeGroupId) {
        return wearingPartExchangeRepository.findByOriginWearingPartGroupIdAndExchangeWearingPartGroupIdAndDeleted(originGroupId,exchangeGroupId,Constants.LONG_ZERO);
    }
}
