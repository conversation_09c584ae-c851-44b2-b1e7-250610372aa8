package net.airuima.rbase.service.base.pedigree;

import net.airuima.config.annotation.FuncDefault;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Copyright(C), 2017-2023,武汉睿码智能科技有限公司
 * 产品谱系工序不良项目 api
 *
 * @author: rain
 * @date: 2024/4/15 18:58
 */
@FuncDefault
public interface IPedigreeStepUnqualifiedItemService {

    /**
     * 工序不良配置导入
     *
     * @param file 工序不良配置导入
     * @return List
     */
    default List<Map<String, Object>> importPedigreeStepUnqualifiedItemExcel(MultipartFile file) {
        return Lists.newArrayList();
    }
}
