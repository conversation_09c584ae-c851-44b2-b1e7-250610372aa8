package net.airuima.rbase.service.report;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Service
@Transactional(rollbackFor = {Exception.class})
public class StaffPerformService extends CommonJpaService<StaffPerform> {

    private static final String STAFF_PERFORM_ENTITY_GRAPH = "staffPerformEntityGraph";
    private final StaffPerformRepository staffPerformRepository;

    public StaffPerformService(StaffPerformRepository staffPerformRepository) {
        this.staffPerformRepository = staffPerformRepository;
    }

    @Override
    @FetchMethod
    public Page<StaffPerform> find(Specification<StaffPerform> spec, Pageable pageable) {
        return staffPerformRepository.findAll(spec,pageable,new NamedEntityGraph(STAFF_PERFORM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public List<StaffPerform> find(Specification<StaffPerform> spec) {
        return staffPerformRepository.findAll(spec,new NamedEntityGraph(STAFF_PERFORM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<StaffPerform> findAll(Pageable pageable) {
        return staffPerformRepository.findAll(pageable,new NamedEntityGraph(STAFF_PERFORM_ENTITY_GRAPH));
    }

}
