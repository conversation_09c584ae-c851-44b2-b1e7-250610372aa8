package net.airuima.rbase.service.procedure.material.impl;

import net.airuima.constant.Constants;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialExchangeDTO;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.service.procedure.batch.WsMaterialService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.util.BeanUtil;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 投料单相关场景实现
 * <AUTHOR>
 * @date 2023/1/17
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsMaterialServiceImpl implements IWsMaterialService {

    @Autowired
    private WsMaterialRepository wsMaterialRepository;

    /**
     * 保存投料单
     * @param workSheet 工单
     * @param bomDtoList BOM信息列表
     */
    @Override
    @CachePut(cacheNames = {"wsMaterialQueryCache"},key = "#workSheet.id")
    @FetchMethod
    public List<WsMaterial> saveWsMaterial(WorkSheet workSheet, List<BomDTO> bomDtoList) {
        List<WsMaterial> wsMaterials = Lists.newArrayList();
        if (net.airuima.util.ValidateUtils.isValid(bomDtoList)) {
            //保存生产投料单信息
            bomDtoList.forEach(bomDto -> {
                WsMaterial wsMaterial = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(),bomDto.getChildMaterial().getId(),Constants.LONG_ZERO).orElse(new WsMaterial());
                //原始物料用料数量=工单数量*用量分子/用量分母
                double inputNumber = bomDto.getCategory() == net.airuima.constant.Constants.INT_ONE?NumberUtils.divide(bomDto.getProportion(),bomDto.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue():NumberUtils.divide(NumberUtils.multiply(workSheet.getNumber(),bomDto.getProportion()).doubleValue(),bomDto.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue();
                wsMaterial.setMaterialId(bomDto.getChildMaterial().getId())
                        .setNumber(inputNumber + inputNumber*bomDto.getWastage())
                        .setOriginMaterialId(bomDto.getChildMaterial().getId())
                        .setWorkSheet(workSheet).setBackFlush(bomDto.getBackFlush());
                wsMaterials.add(wsMaterial);
                if (ValidateUtils.isValid(bomDto.getBomMaterialReplaceDtoList())) {
                    bomDto.getBomMaterialReplaceDtoList().forEach(bomMaterialReplaceDTO -> {
                        WsMaterial replaceWsMaterial = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(),bomMaterialReplaceDTO.getReplaceMaterial().getId(),Constants.LONG_ZERO).orElse(new WsMaterial());
                        //替换物料用料数量=工单数量*用量分子/用量分母
                        double replaceInputNumber =  bomMaterialReplaceDTO.getCategory() == net.airuima.constant.Constants.INT_ONE?NumberUtils.divide(bomMaterialReplaceDTO.getProportion(),bomMaterialReplaceDTO.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue():NumberUtils.divide(NumberUtils.multiply(workSheet.getNumber(),bomMaterialReplaceDTO.getProportion()).doubleValue(),bomMaterialReplaceDTO.getBase(), Constants.INT_FIVE).doubleValue();
                        replaceWsMaterial.setMaterialId(bomMaterialReplaceDTO.getReplaceMaterial().getId())
                                .setNumber(replaceInputNumber + replaceInputNumber * bomMaterialReplaceDTO.getWastage())
                                .setOriginMaterialId(bomDto.getChildMaterial().getId())
                                .setWorkSheet(workSheet).setBackFlush(bomMaterialReplaceDTO.getBackFlush());
                        wsMaterials.add(replaceWsMaterial);
                    });
                }
            });
            return wsMaterialRepository.saveAll(wsMaterials);
        }
        return new ArrayList<>();
    }

    /**
     * 同步工单换料数据
     * <AUTHOR>
     * @param workSheetMaterialExchangeDtoList  上传的工单换料信息
     * @return List<SapBaseDTO>
     **/
    @Override
    public List<SyncResultDTO> workSheetMaterialExchangeSync(List<SyncWorkSheetMaterialExchangeDTO> workSheetMaterialExchangeDtoList) {
        return BeanUtil.getBean(WsMaterialService.class).workSheetMaterialExchangeSync(workSheetMaterialExchangeDtoList);
    }
}
