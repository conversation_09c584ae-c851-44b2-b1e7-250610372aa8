package net.airuima.rbase.service.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResultDetail;
import net.airuima.rbase.repository.procedure.quality.FqcCheckResultDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果明细表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FqcCheckResultDetailService extends CommonJpaService<FqcCheckResultDetail> {
    private static final String FQC_CHECK_RESULT_DETAIL_ENTITY_GRAPH = "fqcCheckResultDetailEntityGraph";
    private final FqcCheckResultDetailRepository fqcCheckResultDetailRepository;

    public FqcCheckResultDetailService(FqcCheckResultDetailRepository fqcCheckResultDetailRepository) {
        this.fqcCheckResultDetailRepository = fqcCheckResultDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<FqcCheckResultDetail> find(Specification<FqcCheckResultDetail> spec, Pageable pageable) {
        return fqcCheckResultDetailRepository.findAll(spec, pageable,new NamedEntityGraph(FQC_CHECK_RESULT_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<FqcCheckResultDetail> find(Specification<FqcCheckResultDetail> spec) {
        return fqcCheckResultDetailRepository.findAll(spec,new NamedEntityGraph(FQC_CHECK_RESULT_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<FqcCheckResultDetail> findAll(Pageable pageable) {
        return fqcCheckResultDetailRepository.findAll(pageable,new NamedEntityGraph(FQC_CHECK_RESULT_DETAIL_ENTITY_GRAPH));
    }

}
