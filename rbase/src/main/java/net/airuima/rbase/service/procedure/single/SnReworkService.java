package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.repository.procedure.single.SnReworkRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 容器在线返修单SN关联Service
 *
 * <AUTHOR>
 * @date 2021-01-11
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnReworkService extends CommonJpaService<SnRework> {
    private static final String SN_REWORK_ENTITY_GRAPH = "snReworkEntityGraph";
    private final SnReworkRepository snReworkRepository;

    public SnReworkService(SnReworkRepository snReworkRepository) {
        this.snReworkRepository = snReworkRepository;
    }


    @Override
    @Transactional(readOnly = true)
    public Page<SnRework> find(Specification<SnRework> spec, Pageable pageable) {
        return snReworkRepository.findAll(spec, pageable,new NamedEntityGraph(SN_REWORK_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<SnRework> find(Specification<SnRework> spec) {
        return snReworkRepository.findAll(spec,new NamedEntityGraph(SN_REWORK_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SnRework> findAll(Pageable pageable) {
        return snReworkRepository.findAll(pageable,new NamedEntityGraph(SN_REWORK_ENTITY_GRAPH));
    }
}
