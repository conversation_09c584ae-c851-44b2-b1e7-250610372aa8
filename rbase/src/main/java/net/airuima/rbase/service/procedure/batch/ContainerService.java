package net.airuima.rbase.service.procedure.batch;

import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.repository.procedure.batch.ContainerRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerService extends CommonJpaService<Container> {

    private final ContainerRepository containerRepository;

    public ContainerService(ContainerRepository containerRepository) {
        this.containerRepository = containerRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Container> find(Specification<Container> spec, Pageable pageable) {
        return containerRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Container> find(Specification<Container> spec) {
        return containerRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Container> findAll(Pageable pageable) {
        return containerRepository.findAll(pageable);
    }

}
