package net.airuima.rbase.service.base.pedigree;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.pedigree.SnRuleDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepMaterialRuleRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序上料规则Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepMaterialRuleService extends CommonJpaService<PedigreeStepMaterialRule> {
    private static final String PEDIGREE_STEP_MATERIAL_RULE_ENTITY_GRAPH = "pedigreeStepMaterialRuleEntityGraph";
    private final Logger log = LoggerFactory.getLogger(PedigreeStepMaterialRuleService.class);

    private final PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository;
    private final PriorityElementConfigRepository priorityElementConfigRepository;


    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;

    public PedigreeStepMaterialRuleService(PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository, PriorityElementConfigRepository priorityElementConfigRepository) {
        this.pedigreeStepMaterialRuleRepository = pedigreeStepMaterialRuleRepository;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<PedigreeStepMaterialRule> find(Specification<PedigreeStepMaterialRule> spec, Pageable pageable) {
        return pedigreeStepMaterialRuleRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_STEP_MATERIAL_RULE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<PedigreeStepMaterialRule> find(Specification<PedigreeStepMaterialRule> spec) {
        return pedigreeStepMaterialRuleRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_MATERIAL_RULE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<PedigreeStepMaterialRule> findAll(Pageable pageable) {
        return pedigreeStepMaterialRuleRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_MATERIAL_RULE_ENTITY_GRAPH));
    }


    /**
     * 新增产品谱系工序上料规则
     *
     * @param pedigreeStepMaterialRule 产品谱系工序上料规则
     */
    public PedigreeStepMaterialRule createEntity(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
        List<List<SnRuleDTO>> serialNumberRule = pedigreeStepMaterialRule.getSerialNumberRule();
        if (!CollectionUtils.isEmpty(serialNumberRule)) {
            for (List<SnRuleDTO> snRuleDtoList : serialNumberRule) {
                for (SnRuleDTO snRuleDTO : snRuleDtoList) {
                    fillRegularForSnRule(snRuleDTO);
                }
            }
        }
        if(Objects.isNull(pedigreeStepMaterialRule.getPriorityElementConfig())){
            PriorityElementConfig priorityElementConfig = getPedigreeStepMaterialRulePriority(Objects.nonNull(pedigreeStepMaterialRule.getPedigree())?pedigreeStepMaterialRule.getPedigree().getId():null,
                    Objects.nonNull(pedigreeStepMaterialRule.getWorkFlow())?pedigreeStepMaterialRule.getWorkFlow().getId():null,
                    Objects.nonNull(pedigreeStepMaterialRule.getClientId())?pedigreeStepMaterialRule.getClientId():null,
                    Objects.nonNull(pedigreeStepMaterialRule.getStep())?pedigreeStepMaterialRule.getStep().getId():null);
            if(Objects.isNull(priorityElementConfig)){
                throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
            }
            pedigreeStepMaterialRule.setPriorityElementConfig(priorityElementConfig);
        }
        this.validatePedigreeStepMaterialRule(pedigreeStepMaterialRule);
        if (Objects.isNull(pedigreeStepMaterialRule.getPriorityElementConfig())) {
            throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(pedigreeStepMaterialRule.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (priorityElementConfigOptional.isEmpty()) {
            throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
        }
        int maxLevel = commonService.getPedigreeMaxLevel();
        MaterialDTO materialDto = rbaseMaterialProxy.findByIdAndDeleted(pedigreeStepMaterialRule.getMaterialId(),Constants.LONG_ZERO);
        if(Objects.nonNull(pedigreeStepMaterialRule.getPedigree()) && materialDto.getMaterialCategory() == ConstantsEnum.MATERIAL_MAIN_CATEGORY.getCategoryName()){
            Pedigree pedigree = pedigreeRepository.getReferenceById(pedigreeStepMaterialRule.getPedigree().getId());
            if(pedigree.getType() == maxLevel && validateMaterialMatchedBom(materialDto,pedigree).equals(Constants.KO)){
                throw new ResponseException("error.NoBomMatchMaterial", "规则中的物料没有匹配的产品谱系的BOM");
            }
        }
        //验证传入的
        return this.save(pedigreeStepMaterialRule);
    }

    /**
     *
     * 若谱系为型号则验证物料是否属于产品型号的BOM里的物料
     * @param materialDto 物料DTO
     * @param pedigree 产品谱系
     * @return 验证结果
     */
    private String validateMaterialMatchedBom(MaterialDTO materialDto,Pedigree pedigree) {
        List<BomDTO> bomDTOList = rbaseBomProxy.findByMainMaterialId(Stream.of(pedigree).map(Pedigree::getMaterialId).toList());
        if(CollectionUtils.isEmpty(bomDTOList) || bomDTOList.stream().noneMatch(bomDTO -> bomDTO.getChildMaterial().getId().equals(materialDto.getId()))){
            return Constants.KO;
        }
        return Constants.OK;
    }


    /**
     * 验证保存参数
     *
     * @param pedigreeStepMaterialRule
     * @return
     */
    private void validatePedigreeStepMaterialRule(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
        // 更新后的条件参数
        Long stepId = null != pedigreeStepMaterialRule.getStep() ? pedigreeStepMaterialRule.getStep().getId() : null;
        Long workFlowId = null != pedigreeStepMaterialRule.getWorkFlow() ? pedigreeStepMaterialRule.getWorkFlow().getId() : null;
        Long pedigreeId = null != pedigreeStepMaterialRule.getPedigree() ? pedigreeStepMaterialRule.getPedigree().getId() : null;
        Long clientId = null != pedigreeStepMaterialRule.getClientId() ? pedigreeStepMaterialRule.getClientId() : null;
        Long materialId = null != pedigreeStepMaterialRule.getMaterialId() ? pedigreeStepMaterialRule.getMaterialId() : null;
        PedigreeStepMaterialRule queryPedigreeStepMaterialRule =  pedigreeStepMaterialRuleRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndMaterialIdAndDeleted(clientId, pedigreeId, workFlowId, stepId, materialId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeStepMaterialRule.getId()) && null != queryPedigreeStepMaterialRule) {
            throw new ResponseException("error.pedigreeStepMaterialRuleExist", "上料规则已存在");
        }
        if (Objects.nonNull(pedigreeStepMaterialRule.getId()) && null != queryPedigreeStepMaterialRule && !queryPedigreeStepMaterialRule.getId().equals(pedigreeStepMaterialRule.getId())) {
            throw new ResponseException("error.pedigreeStepMaterialRuleExist", "上料规则已存在");
        }
    }


    /**
     * 根据分类和数据，拼接填充规则
     *
     * @param snRuleDTO 序列号规则DTO
     */
    private void fillRegularForSnRule(SnRuleDTO snRuleDTO) {
        String category = snRuleDTO.getCategory();
        if (SnRuleEnum.MANUALLY_ENTER_REGULAR.getCategory().equals(category)) {
            return;
        }
        String initialRegular = SnRuleEnum.findRegularByCategory(category);
        String inputRegular = snRuleDTO.getRegular();
        if (SnRuleEnum.FROM_WHICH_MATCH_CHARACTERS.getCategory().equals(category)) {
            //从第{n}位开始匹配输入字符{n2}
            String[] inputArray = StringUtils.split(inputRegular, Constants.STR_COMMA);
            if (!ValidateUtils.isValid(inputArray) || inputArray.length < Constants.INT_TWO) {
                log.warn("fill regular fail,category:{},inputRegular{}", category, initialRegular);
                return;
            }
            String regular = initialRegular.replace("{n}", inputArray[0]).replace("{n2}", inputArray[1]);
            snRuleDTO.setRegular(regular);
        } else {
            String regular = initialRegular.replace("{n}", inputRegular);
            snRuleDTO.setRegular(regular);
        }
    }


    /**
     * 更新产品谱系工序上料规则
     *
     * @param pedigreeStepMaterialRule 产品谱系工序上料规则
     */
    public PedigreeStepMaterialRule updateEntity(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
        return createEntity(pedigreeStepMaterialRule);
    }

    /**
     * 批量更新产品谱系工序上料规则
     * @param pedigreeStepMaterialRules
     */
    public void batchUpdate(List<PedigreeStepMaterialRule> pedigreeStepMaterialRules){
        if (CollectionUtils.isEmpty(pedigreeStepMaterialRules)){
            return;
        }
        pedigreeStepMaterialRules.forEach(this::createEntity);
    }

    /**
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId 客户ID
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序ID
     * @return List<PedigreeStepMaterialRule>
     */
    public List<PedigreeStepMaterialRule> findByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(Long pedigreeId, Long clientId, Long workFlowId,Long stepId) {
        return pedigreeStepMaterialRuleRepository.findAllByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId,workFlowId,stepId,clientId, net.airuima.constant.Constants.LONG_ZERO);
    }

    /**
     * 产品谱系工序上料配置导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < String, Object>> 行数据
     */
    public List<Map<String, Object>> importPedigreeStepMaterialRuleExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();

        //谱系
        List<String> pedigreeCodes = rowList.stream().map(row -> String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.PEDIGREE_CODE)))
                .filter(ValidateUtils::isValid).distinct().toList();
        Map<String,List<Pedigree>> pedigreeMap = new LinkedHashMap<>();
        if (ValidateUtils.isValid(pedigreeCodes)){
            List<Pedigree> pedigreeList = pedigreeRepository.findByCodeInAndDeleted(pedigreeCodes, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(pedigreeList)){
                pedigreeMap = pedigreeList.stream().collect(Collectors.groupingBy(Pedigree::getCode));
            }
        }

        //工艺路线
        List<String> workFlowCodes = rowList.stream().map(row -> String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.WORK_FLOW_CODE)))
                .filter(ValidateUtils::isValid).distinct().toList();
        Map<String,List<WorkFlow>> workFlowMap = new LinkedHashMap<>();
        if (ValidateUtils.isValid(workFlowCodes)){
            List<WorkFlow> workFlows = workFlowRepository.findByCodeInAndDeleted(workFlowCodes, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(workFlows)){
                workFlowMap = workFlows.stream().collect(Collectors.groupingBy(WorkFlow::getCode));
            }
        }

        //工序
        List<String> stepCodes = rowList.stream().map(row -> String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.STEP_CODE)))
                .filter(ValidateUtils::isValid).distinct().toList();
        Map<String,List<Step>> stepMap = new LinkedHashMap<>();
        if (ValidateUtils.isValid(stepCodes)){
            List<Step> steps = stepRepository.findByCodeInAndDeleted(stepCodes, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(steps)){
                stepMap = steps.stream().collect(Collectors.groupingBy(Step::getCode));
            }
        }

        //客户
        List<String> clientCodes = rowList.stream().map(row -> String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.CLIENT_CODE)))
                .filter(ValidateUtils::isValid).distinct().toList();
        Map<String,List<ClientDTO>> clientMap = new LinkedHashMap<>();
        if (ValidateUtils.isValid(clientCodes)){
            List<ClientDTO> clientDTOList = rbaseClientProxy.findByCodeIn(clientCodes);
            if (ValidateUtils.isValid(clientDTOList)){
                clientMap = clientDTOList.stream().collect(Collectors.groupingBy(ClientDTO::getCode));
            }
        }


        //物料
        List<String> materialCodes = rowList.stream().map(row -> String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.MATERIAL_CODE)))
                .filter(ValidateUtils::isValid).distinct().toList();
        Map<String,List<MaterialDTO>> materialMap = new LinkedHashMap<>();
        if (ValidateUtils.isValid(materialCodes)){
            List<MaterialDTO> materialDtoList = rbaseMaterialProxy.findByCodeInAndDeleted(materialCodes, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(materialDtoList)){
                materialMap = materialDtoList.stream().collect(Collectors.groupingBy(MaterialDTO::getCode));
            }
        }

        // 对每一行数据进行处理
        Map<String, List<Pedigree>> finalPedigreeMap = pedigreeMap;
        Map<String, List<WorkFlow>> finalWorkFlowMap = workFlowMap;
        Map<String, List<Step>> finalStepMap = stepMap;
        Map<String, List<ClientDTO>> finalClientMap = clientMap;
        Map<String, List<MaterialDTO>> finalMaterialMap = materialMap;

        List<PedigreeStepMaterialRule> pedigreeStepMaterialRules = Lists.newArrayList();
        int maxLevel = commonService.getPedigreeMaxLevel();
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.PEDIGREE_CODE));
            // 产品谱系
            Pedigree pedigree = null;
            if (!StringUtils.isEmpty(pedigreeCode) && !"null".equals(pedigreeCode)) {
                if (ValidateUtils.isValid(finalPedigreeMap) && ValidateUtils.isValid(finalPedigreeMap.get(pedigreeCode))){
                    pedigree = finalPedigreeMap.get(pedigreeCode).get(Constants.INT_ZERO);
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工艺路线编码并转换为对象
            String workFlowCode = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.WORK_FLOW_CODE));
            WorkFlow workFlow = null;
            if (!StringUtils.isEmpty(workFlowCode) && !"null".equals(workFlowCode)) {
                if (ValidateUtils.isValid(finalWorkFlowMap) && ValidateUtils.isValid(finalWorkFlowMap.get(workFlowCode))) {
                    workFlow = finalWorkFlowMap.get(workFlowCode).get(Constants.INT_ZERO);
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工序编码并转换为对象
            String stepCode = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.STEP_CODE));
            Step step = null;
            if (StringUtils.isEmpty(stepCode) || "null".equals(stepCode)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因工序不存在");
                illegalDataList.add(row);
                return;
            } else {
                if (ValidateUtils.isValid(finalStepMap) && ValidateUtils.isValid(finalStepMap.get(stepCode))) {
                    step = finalStepMap.get(stepCode).get(Constants.INT_ZERO);
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工序不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            //获取客户代码并转为对象
            String clientCode = String.valueOf(row.get((PedigreeStepMaterialRuleExcelConstants.CLIENT_CODE)));
            ClientDTO clientDto = StringUtils.isEmpty(clientCode) || "null".equals(clientCode) ? null : (ValidateUtils.isValid(finalClientMap) && ValidateUtils.isValid(finalClientMap.get(clientCode)))?finalClientMap.get(clientCode).get(Constants.INT_ZERO):null;
            if ((!StringUtils.isEmpty(clientCode) && !"null".equals(clientCode)) && (clientDto == null || clientDto.getId() == null)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因客户编码不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取物料代码并转为对象
            String materialCode = String.valueOf(row.get((PedigreeStepMaterialRuleExcelConstants.MATERIAL_CODE)));
            MaterialDTO materialDto = StringUtils.isEmpty(materialCode) || "null".equals(materialCode) ? null : (ValidateUtils.isValid(finalMaterialMap) && ValidateUtils.isValid(finalMaterialMap.get(materialCode)))?finalMaterialMap.get(materialCode).get(Constants.INT_ZERO):null;
            if (materialDto == null || materialDto.getId() == null) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因物料编码不存在");
                illegalDataList.add(row);
                return;
            }
            if(Objects.nonNull(pedigree) &&  materialDto.getMaterialCategory() == ConstantsEnum.MATERIAL_MAIN_CATEGORY.getCategoryName() && pedigree.getType() == maxLevel && validateMaterialMatchedBom(materialDto,pedigree).equals(Constants.KO)){
                row.put("错误信息", "导入Excel失败,数据有误, 原因物物料没有匹配的产品谱系的BOM");
                illegalDataList.add(row);
                return;
            }
            // 获取是否启用
            Boolean isEnable = "否".equals(String.valueOf(row.get(PedigreeReworkWorkFlowExcelConstants.IS_ENABLE))) ? Boolean.FALSE : Boolean.TRUE;
            // 工序id
            Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
            // 客户id
            Long clientId = Optional.ofNullable(clientDto).map(ClientDTO::getId).orElse(null);
            // 扣料比例
            Double proportion = Objects.nonNull(row.get(PedigreeStepMaterialRuleExcelConstants.PROPORTION)) &&
                    !"null".equals(row.get(PedigreeStepMaterialRuleExcelConstants.PROPORTION)) ? Double.parseDouble(row.get(PedigreeStepMaterialRuleExcelConstants.PROPORTION).toString()) : 1d;
            // 是否扣数
            Boolean isDeduct = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.IS_DEDUCT)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 是否核物料
            Boolean isCheckMaterial = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.IS_CHECK_MATERIAL)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 是否核物料批次
            Boolean isCheckMaterialBatch = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.IS_CHECK_MATERIAL_BATCH)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 物料管控粒度
            Integer controlMaterialGranularity = Objects.nonNull(row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_MATERIAL_GRANULARITY)) &&
                    !"null".equals(row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_MATERIAL_GRANULARITY)) && "单只序列号".equals(row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_MATERIAL_GRANULARITY)) ? Constants.INT_ZERO : Constants.INT_ONE;
            // 管控序列号次数
            Integer controlSnCount = Objects.nonNull(row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_SN_COUNT)) &&
                    !"null".equals(row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_SN_COUNT)) ? Integer.parseInt( row.get(PedigreeStepMaterialRuleExcelConstants.CONTROL_SN_COUNT).toString()) : Constants.INT_ONE;
            //规则
            String serialNumberRule = String.valueOf(row.get(PedigreeStepMaterialRuleExcelConstants.SERIAL_NUMBER_RULE));
            Long materialId = Optional.ofNullable(materialDto).map(MaterialDTO::getId).orElse(null);


            // 查询产品谱系工序上料配置是否已经存在
            PedigreeStepMaterialRule queryPedigreeStepMaterialRule = pedigreeStepMaterialRuleRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndMaterialIdAndDeleted(clientId,pedigreeId,workFlowId,stepId,materialId, Constants.LONG_ZERO).orElse(null);
            // 获取优先级配置
            PriorityElementConfig pedigreeStepUnqualifiedItemPriority = getPedigreeStepMaterialRulePriority(pedigreeId, workFlowId, clientId, stepId);
            if (pedigreeStepUnqualifiedItemPriority == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 该组合条件优先级未配置");
                illegalDataList.add(row);
                return;
            }
            //规则
            List<List<SnRuleDTO>> serialNumberRuleList = Objects.nonNull(serialNumberRule) && !"null".equals(serialNumberRule) ? JSON.parseObject(serialNumberRule,
                    new TypeReference<>() {
                    }) : null;
            // 保存产品谱系工序上料配置
            pedigreeStepMaterialRules.add(
            addPedigreeStepMaterialRule(step, pedigree, workFlow, clientId, materialId,
                    queryPedigreeStepMaterialRule, proportion, isDeduct, isCheckMaterial,
                    isCheckMaterialBatch, controlMaterialGranularity, controlSnCount, serialNumberRuleList, pedigreeStepUnqualifiedItemPriority, isEnable));
        });

        if (ValidateUtils.isValid(pedigreeStepMaterialRules)){
            savePedigreeStepMaterialRule(pedigreeStepMaterialRules);
        }
        return illegalDataList;
    }

    /**
     * 添加上料规则配置
     * @return 上料规则配置
     */
    private PedigreeStepMaterialRule addPedigreeStepMaterialRule(Step step, Pedigree pedigree, WorkFlow workFlow, Long clientId, Long materialId,
                                              PedigreeStepMaterialRule queryPedigreeStepMaterialRule, Double proportion,
                                              Boolean isDeduct, Boolean isCheckMaterial, Boolean isCheckMaterialBatch,
                                              Integer controlMaterialGranularity, Integer controlSnCount,
                                              List<List<SnRuleDTO>> serialNumberRule, PriorityElementConfig pedigreeStepUnqualifiedItemPriority, Boolean isEnable) {
        if (Objects.nonNull(queryPedigreeStepMaterialRule)) {
            return queryPedigreeStepMaterialRule
                    .setProportion(proportion)
                    .setIsDeduct(isDeduct)
                    .setIsCheckMaterial(isCheckMaterial)
                    .setIsCheckMaterialBatch(isCheckMaterialBatch)
                    .setControlMaterialGranularity(controlMaterialGranularity)
                    .setSerialNumberRules(serialNumberRule)
                    .setEnable(isEnable)
                    .setControlSnCount(controlSnCount);
        } else {
            PedigreeStepMaterialRule pedigreeStepMaterialRule = new PedigreeStepMaterialRule();
             pedigreeStepMaterialRule
                    .setClientId(clientId)
                    .setMaterialId(materialId)
                    .setPedigree(pedigree)
                    .setStep(step)
                    .setWorkFlow(workFlow)
                    .setProportion(proportion)
                    .setIsDeduct(isDeduct)
                    .setIsCheckMaterial(isCheckMaterial)
                    .setIsCheckMaterialBatch(isCheckMaterialBatch)
                    .setControlMaterialGranularity(controlMaterialGranularity)
                    .setSerialNumberRules(serialNumberRule)
                    .setPriorityElementConfig(pedigreeStepUnqualifiedItemPriority)
                    .setEnable(isEnable)
                    .setControlSnCount(controlSnCount);
            pedigreeStepMaterialRule.setDeleted(Constants.LONG_ZERO);
            return pedigreeStepMaterialRule;
        }

    }

    /**
     * 获取上料配置优先级配置
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param clientId   客户id
     * @param stepId     工序id
     * @return net.airuima.domain.base.priority.PriorityElementConfig 优先级配置
     */
    private PriorityElementConfig getPedigreeStepMaterialRulePriority(Long pedigreeId, Long workFlowId, Long clientId, Long stepId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(workFlowId)) {
            combination.add(Constants.WORKFLOW_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        if (Objects.nonNull(stepId)) {
            combination.add(Constants.STEP_ELEMENT);
        }
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_FIVE,combination);
    }

    /**
     * 保存上料规则
     * @param pedigreeStepMaterialRules
     */
    private void savePedigreeStepMaterialRule(List<PedigreeStepMaterialRule> pedigreeStepMaterialRules){

        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = Lists.newArrayList();

        pedigreeStepMaterialRules.stream().collect(Collectors.groupingBy(pedigreeStepMaterialRule -> {
            StringBuilder unique = new StringBuilder();
            if (Objects.nonNull(pedigreeStepMaterialRule.getClientId())){
                unique.append(pedigreeStepMaterialRule.getClientId());
            }
            if (Objects.nonNull(pedigreeStepMaterialRule.getPedigree())){
                unique.append(pedigreeStepMaterialRule.getPedigree().getId());
            }
            if (Objects.nonNull(pedigreeStepMaterialRule.getWorkFlow())){
                unique.append(pedigreeStepMaterialRule.getWorkFlow().getId());
            }
            if (Objects.nonNull(pedigreeStepMaterialRule.getStep())){
                unique.append(pedigreeStepMaterialRule.getStep().getId());
            }
            if (Objects.nonNull(pedigreeStepMaterialRule.getMaterialId())){
                unique.append(pedigreeStepMaterialRule.getMaterialId());
            }
            return unique.toString();
        })).forEach((uniqueCode,newPedigreeStepMaterialRuleList) -> {
            pedigreeStepMaterialRuleList.add(newPedigreeStepMaterialRuleList.get(Constants.INT_ZERO));
        });
        pedigreeStepMaterialRuleRepository.saveAll(pedigreeStepMaterialRuleList);
    }


    /**
     * 根据记录ID删除记录
     * @param entityId 记录ID
     */
    @Override
    public void deleteById(Long entityId) {
        pedigreeStepMaterialRuleRepository.deleteById(entityId);
    }
}
