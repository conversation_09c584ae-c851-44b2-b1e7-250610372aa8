package net.airuima.rbase.service.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualifiedDetail;
import net.airuima.rbase.repository.procedure.quality.InspectUnqualifiedDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良管理记录详情表Service
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectUnqualifiedDetailService extends CommonJpaService<InspectUnqualifiedDetail> {

    private static final String INSPECT_UNQUALIFIED_DETAIL_ENTITY_GRAPH = "inspectUnqualifiedDetailEntityGraph";

    private final InspectUnqualifiedDetailRepository inspectUnqualifiedDetailRepository;

    public InspectUnqualifiedDetailService(InspectUnqualifiedDetailRepository inspectUnqualifiedDetailRepository) {
        this.inspectUnqualifiedDetailRepository = inspectUnqualifiedDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<InspectUnqualifiedDetail> find(Specification<InspectUnqualifiedDetail> spec, Pageable pageable) {
        return inspectUnqualifiedDetailRepository.findAll(spec, pageable, new NamedEntityGraph(INSPECT_UNQUALIFIED_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<InspectUnqualifiedDetail> find(Specification<InspectUnqualifiedDetail> spec) {
        return inspectUnqualifiedDetailRepository.findAll(spec, new NamedEntityGraph(INSPECT_UNQUALIFIED_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<InspectUnqualifiedDetail> findAll(Pageable pageable) {
        return inspectUnqualifiedDetailRepository.findAll(pageable, new NamedEntityGraph(INSPECT_UNQUALIFIED_DETAIL_ENTITY_GRAPH));
    }

}
