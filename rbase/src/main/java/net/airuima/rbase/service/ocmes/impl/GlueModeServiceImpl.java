package net.airuima.rbase.service.ocmes.impl;

import com.google.common.collect.Lists;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;
import net.airuima.rbase.proxy.optical.RbaseGlueMaterialProxy;
import net.airuima.rbase.service.ocmes.GlueModeService;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class GlueModeServiceImpl implements GlueModeService {

    @Autowired
    private RbaseGlueMaterialProxy rbaseGlueMaterialProxy;

    /**
     * 添加上料规则，标记胶水标签
     * @param materialRuleInfoList 上料规则
     * @param pedigreeStepMaterialRuleList 上料规则配置
     * @param putInNumber  投产数
     * <AUTHOR>
     * @date  2022/12/22
     * @return java.util.List<net.airuima.dto.client.ClientGetStepInfoDTO.MaterialRuleInfo> 当前工序的上料规则
     */
    @Override
    public List<ClientGetStepInfoDTO.MaterialRuleInfo> isGlueMaterial(List<ClientGetStepInfoDTO.MaterialRuleInfo> materialRuleInfoList, List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList, Integer putInNumber) {

         materialRuleInfoList = ValidateUtils.isValid(materialRuleInfoList) ? materialRuleInfoList : Lists.newArrayList();

         if (ValidateUtils.isValid(pedigreeStepMaterialRuleList)){
             List<PedigreeStepMaterialRule> gluePedigreeStepMaterialRuleList = Lists.newArrayList();
            if (ValidateUtils.isValid(materialRuleInfoList)){
                //剔除工序上料规则中已添加的上料信息
                List<ClientGetStepInfoDTO.MaterialRuleInfo> finalMaterialRuleInfoList = materialRuleInfoList;
                gluePedigreeStepMaterialRuleList.addAll(pedigreeStepMaterialRuleList.stream().filter(
                        pedigreeStepMaterialRule -> !finalMaterialRuleInfoList.stream().map(ClientGetStepInfoDTO.MaterialRuleInfo::getMaterialId).toList().contains(pedigreeStepMaterialRule.getMaterialId())
                ).collect(Collectors.toList()));
            }else {
                gluePedigreeStepMaterialRuleList.addAll(pedigreeStepMaterialRuleList);
            }
            //获取存在胶水配置的物料信息
             List<Long> glueMaterialList = rbaseGlueMaterialProxy.isGlueMaterial(pedigreeStepMaterialRuleList.stream().map(PedigreeStepMaterialRule::getMaterialId).collect(Collectors.toList()));
             //获取不在投料单中，但属于胶水的物料，添加到上料信息中
             List<ClientGetStepInfoDTO.MaterialRuleInfo> glueMaterialRuleInfoList = Lists.newArrayList();
            if (ValidateUtils.isValid(glueMaterialList)){
                gluePedigreeStepMaterialRuleList.forEach(pedigreeStepMaterialRule -> {
                    if (glueMaterialList.stream().anyMatch(glueMaterial -> glueMaterial.equals(pedigreeStepMaterialRule.getMaterialId()))){
                        ClientGetStepInfoDTO.MaterialRuleInfo materialRuleInfo = new ClientGetStepInfoDTO.MaterialRuleInfo();
                        materialRuleInfo.setMaterialId(pedigreeStepMaterialRule.getMaterialId())
                                .setMaterialName(pedigreeStepMaterialRule.getMaterialDto().getName())
                                .setMaterialCode(pedigreeStepMaterialRule.getMaterialDto().getCode())
                                .setProportion(pedigreeStepMaterialRule.getProportion())
                                //如果需要验证库存且上料规则配置的需要扣数则计算上料总数,后面会根据库存扣减
                                .setNumber(putInNumber * pedigreeStepMaterialRule.getProportion())
                                .setLeftNumber(Constants.DOUBLE_ZERRO)
                                .setCheckMaterial(pedigreeStepMaterialRule.getIsCheckMaterial())
                                .setCheckMaterialBatch(pedigreeStepMaterialRule.getIsCheckMaterialBatch())
                                .setDeduct(pedigreeStepMaterialRule.getIsDeduct())
                                .setControlMaterialGranularity(pedigreeStepMaterialRule.getControlMaterialGranularity())
                                .setSerialNumberRule(pedigreeStepMaterialRule.getSerialNumberRule())
                                .setControlSnCount(pedigreeStepMaterialRule.getControlSnCount())
                                .setIsGlue(Boolean.TRUE);
                        glueMaterialRuleInfoList.add(materialRuleInfo);
                    }
                });
            }
            //将投料单与上料规则中是胶水的物料打上标记
            if (ValidateUtils.isValid(glueMaterialList)){
                  materialRuleInfoList.forEach(materialRuleInfo -> {
                      materialRuleInfo.setIsGlue(glueMaterialList.stream().anyMatch(glueMaterial -> materialRuleInfo.getMaterialId().equals(glueMaterial)));
                });
                materialRuleInfoList.addAll(glueMaterialRuleInfoList);
            }
         }
        return materialRuleInfoList;
    }
}
