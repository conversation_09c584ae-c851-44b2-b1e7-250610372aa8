package net.airuima.rbase.service.procedure.flowable;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@FuncDefault
public interface IFlowableCallBackService {

    /**
     * 工作流连线事件
     *
     * @param processRunTimeEventDto 流程节点完成返回信息DTO
     */
    default void flowableSequenceListener(ProcessRunTimeEventDTO processRunTimeEventDto){

    }

}
