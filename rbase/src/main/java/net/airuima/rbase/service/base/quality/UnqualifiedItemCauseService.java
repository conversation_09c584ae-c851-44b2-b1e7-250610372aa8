package net.airuima.rbase.service.base.quality;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedCause;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemCause;
import net.airuima.rbase.dto.quality.UnqualifiedDTO;
import net.airuima.rbase.dto.quality.UnqualifiedItemCauseDTO;
import net.airuima.rbase.repository.base.quality.UnqualifiedCauseRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedGroupRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemCauseRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良现象原因关系Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedItemCauseService extends CommonJpaService<UnqualifiedItemCause> {

    private final UnqualifiedItemCauseRepository unqualifiedItemCauseRepository;

    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private UnqualifiedGroupRepository unqualifiedGroupRepository;
    @Autowired
    private UnqualifiedCauseRepository unqualifiedCauseRepository;

    public UnqualifiedItemCauseService(UnqualifiedItemCauseRepository unqualifiedItemCauseRepository) {
        this.unqualifiedItemCauseRepository = unqualifiedItemCauseRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedItemCause> find(Specification<UnqualifiedItemCause> spec, Pageable pageable) {
        return unqualifiedItemCauseRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedItemCause> find(Specification<UnqualifiedItemCause> spec) {
        return unqualifiedItemCauseRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedItemCause> findAll(Pageable pageable) {
        return unqualifiedItemCauseRepository.findAll(pageable);
    }

    /**
     * 不良现象批量或单个新增/修改不良原因
     *
     * @param unqualifiedItemCauseDto 不良现象原因DTO(一对多)
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindCauses(UnqualifiedItemCauseDTO unqualifiedItemCauseDto) {
        unqualifiedItemCauseRepository.deleteByUnqualifiedItemId(unqualifiedItemCauseDto.getUnqualifiedItemId());
        List<UnqualifiedItemCause> unqualifiedItemCauses = unqualifiedItemCauseDto.getUnqualifiedCauseIds().stream().map(s -> {
            UnqualifiedItem unqualifiedItem = new UnqualifiedItem();
            unqualifiedItem.setId(unqualifiedItemCauseDto.getUnqualifiedItemId());
            UnqualifiedCause unqualifiedCause = new UnqualifiedCause();
            unqualifiedCause.setId(s);
            return new UnqualifiedItemCause().setUnqualifiedItem(unqualifiedItem).setUnqualifiedCause(unqualifiedCause);
        }).collect(Collectors.toList());
        unqualifiedItemCauseRepository.saveAll(unqualifiedItemCauses);
    }

    /**
     * 获得树形结构的不良数据
     *
     * @return
     */
    @Transactional(readOnly = true)
    public ResponseEntity<ResponseContent<List<UnqualifiedDTO.UnqualifiedGroupDTO>>> unqualifiedTreeData() {
        try {
            List<UnqualifiedDTO.UnqualifiedGroupDTO> unqualifiedGroupDtoList = new ArrayList<>();
            //查询所有不良组别
            List<UnqualifiedGroup> unqualifiedGroupList = unqualifiedGroupRepository.findByDeleted(Constants.LONG_ZERO);
            if (ValidateUtils.isValid(unqualifiedGroupList)) {
                List<Long> unqualifiedGroupIdList = unqualifiedGroupList.stream().map(UnqualifiedGroup::getId).collect(Collectors.toList());
                //查找不良组别对应的所有不良项
                List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByUnqualifiedGroupIdInAndDeleted(unqualifiedGroupIdList, Constants.LONG_ZERO);
                Map<Long, List<UnqualifiedItem>> unqualifiedItemMap = unqualifiedItemList.stream().collect(Collectors.groupingBy(unqualifiedItem -> unqualifiedItem.getUnqualifiedGroup().getId()));
                Map<Long, List<UnqualifiedItemCause>> unqualifiedItemCauseMap = null;
                if (ValidateUtils.isValid(unqualifiedItemList)) {
                    List<Long> unqualifiedItemIdList = unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList());
                    //查找不良项对应的所有不良原因
                    List<UnqualifiedItemCause> unqualifiedItemCauseList = unqualifiedItemCauseRepository.findByUnqualifiedItemIdInAndDeleted(unqualifiedItemIdList, Constants.LONG_ZERO);
                    unqualifiedItemCauseMap = unqualifiedItemCauseList.stream().collect(
                            Collectors.groupingBy(unqualifiedItemCause -> unqualifiedItemCause.getUnqualifiedItem().getId()));
                }
                Map<Long, List<UnqualifiedItemCause>> finalUnqualifiedItemCauseMap = unqualifiedItemCauseMap;
                unqualifiedGroupList.forEach(unqualifiedGroup -> {
                    UnqualifiedDTO.UnqualifiedGroupDTO unqualifiedGroupDto = Optional.ofNullable(unqualifiedGroup).map(UnqualifiedDTO.UnqualifiedGroupDTO::new).get();
                    List<UnqualifiedItem> currUnqualifiedItemList = unqualifiedItemMap.get(unqualifiedGroup.getId());
                    if (ValidateUtils.isValid(currUnqualifiedItemList)) {
                        List<UnqualifiedDTO.UnqualifiedItemDTO> unqualifiedItemDtoList = currUnqualifiedItemList.stream().map(UnqualifiedDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
                        unqualifiedItemDtoList.forEach(unqualifiedItemDto -> {
                            if (finalUnqualifiedItemCauseMap != null && ValidateUtils.isValid(finalUnqualifiedItemCauseMap.get(unqualifiedItemDto.getId()))) {
                                unqualifiedItemDto.setUnqualifiedCauseList(finalUnqualifiedItemCauseMap.get(
                                        unqualifiedItemDto.getId()).stream().map(UnqualifiedItemCause::getUnqualifiedCause)
                                        .map(UnqualifiedDTO.UnqualifiedCauseDTO::new).collect(Collectors.toList()));
                            }
                        });
                        unqualifiedGroupDto.setUnqualifiedItemList(unqualifiedItemDtoList);
                    }
                    unqualifiedGroupDtoList.add(unqualifiedGroupDto);
                });
            }
            return ResponseContent.isOk(unqualifiedGroupDtoList);
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 新增不良原因同时绑定不良项目
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<ResponseContent<Void>> unqualifiedItemBindCause(UnqualifiedItemCause unqualifiedItemCause) {
        try {
            if (unqualifiedItemCause.getUnqualifiedItem() == null
                    || unqualifiedItemCause.getUnqualifiedItem().getId() == null) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(UnqualifiedItemCause.class.getSimpleName()),"unqualifiedItemIsNull","不良项目为空")).build();
            }
            UnqualifiedCause unqualifiedCause = null;
            if (unqualifiedItemCause.getUnqualifiedCause().getId() != null) {
                unqualifiedCause = unqualifiedCauseRepository.findByIdAndDeleted(
                        unqualifiedItemCause.getUnqualifiedCause().getId(), Constants.LONG_ZERO).orElse(null);
            } else {
                unqualifiedCause = unqualifiedCauseRepository.save(unqualifiedItemCause.getUnqualifiedCause());
            }
            unqualifiedItemCause.setUnqualifiedCause(unqualifiedCause);
            unqualifiedItemCauseRepository.save(unqualifiedItemCause);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(StringUtils.uncapitalize(UnqualifiedItemCause.class.getSimpleName()), unqualifiedItemCause.getId().toString())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 删除不良原因绑定不良项目关系
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<ResponseContent<Void>> deleteUnqualifiedItemBindCause(UnqualifiedItemCause unqualifiedItemCause) {
        try {
            if (unqualifiedItemCause.getUnqualifiedItem() == null
                    || unqualifiedItemCause.getUnqualifiedCause() == null
                    || unqualifiedItemCause.getUnqualifiedItem().getId() == null
                    || unqualifiedItemCause.getUnqualifiedCause().getId()== null) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(UnqualifiedItemCause.class.getSimpleName()),"isIsNull","id为空")).build();
            }
            //删除不良原因对应绑定不良项目关系
            Optional<UnqualifiedItemCause> unqualifiedItemCauseOptional = unqualifiedItemCauseRepository.findByUnqualifiedItemIdAndUnqualifiedCauseIdAndDeleted(
                    unqualifiedItemCause.getUnqualifiedItem().getId(), unqualifiedItemCause.getUnqualifiedCause().getId(), Constants.LONG_ZERO);
            unqualifiedItemCauseOptional.ifPresent(currUnqualifiedItemCause -> {
                currUnqualifiedItemCause.setDeleted(currUnqualifiedItemCause.getId());
                unqualifiedItemCauseRepository.save(currUnqualifiedItemCause);
            });
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(UnqualifiedItemCause.class.getSimpleName()), "")).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 删除不良原因的同时删除绑定的关系
     *
     * @param unqualifiedItemCause
     * @return ResponseEntity<ResponseContent < Void>>
     * <AUTHOR>
     * @date 2021-04-13
     **/
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<ResponseContent<Void>> deleteUnqualifiedItemCause(UnqualifiedItemCause unqualifiedItemCause) {
        try {
            Optional<UnqualifiedCause> unqualifiedCauseOptional = unqualifiedCauseRepository.findByIdAndDeleted(
                    unqualifiedItemCause.getUnqualifiedCause().getId(), Constants.LONG_ZERO);
            //删除不良原因的同时删除绑定的关系
            unqualifiedCauseOptional.ifPresent(unqualifiedCause -> {
                unqualifiedCause.setDeleted(unqualifiedCause.getId());
                unqualifiedCauseRepository.save(unqualifiedCause);
                unqualifiedItemCauseRepository.deleteByUnqualifiedCauseIdAndDeleted(unqualifiedCause.getId(), Constants.LONG_ZERO);
            });
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(UnqualifiedItemCause.class.getSimpleName()),"")).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }

    }

}
