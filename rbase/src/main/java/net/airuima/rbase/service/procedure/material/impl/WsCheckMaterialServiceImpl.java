package net.airuima.rbase.service.procedure.material.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import net.airuima.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.FuncKeyConstants;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterial;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.material.CheckMaterialDTO;
import net.airuima.rbase.dto.material.CheckMaterialDetailDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.organization.RbaseSupplierProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.rbase.service.procedure.material.IWsCheckMaterialDetailService;
import net.airuima.rbase.service.procedure.material.IWsCheckMaterialService;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单核料相关默认实现Service
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsCheckMaterialServiceImpl implements IWsCheckMaterialService {
    @Autowired
    private WsCheckMaterialRepository wsCheckMaterialRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialService;
    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private IWsCheckMaterialDetailService[] wsCheckMaterialDetailServices;
    @Autowired
    private RbaseSupplierProxy rbaseSupplierProxy;

    /**
     * 导入正常单核料表
     *
     * @param file 核料信息表文件
     */
    @Override
    public void importMaterials(MultipartFile file) throws Exception {
        List<Map<String, Object>> mapList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, new ImportParams());
        List<CheckMaterialDTO> checkMaterialDtoList = mapList.stream().map(CheckMaterialDTO::new).toList();
        boolean existNullValue = checkMaterialDtoList.stream().anyMatch(checkMaterialDTO -> checkMaterialDTO.getWorkSheetSerialNumber() == null || checkMaterialDTO.getCheckMaterialCode() == null || checkMaterialDTO.getMaterialCode() == null || checkMaterialDTO.getMaterialNumber() == null);
        //判断必要数据是否存在空的值
        if (existNullValue) {
            throw new ResponseException("error.dataIsEmpty", "部分数据为空");
        }
        //统一查询需要导入的数据
        List<WsCheckMaterial> wsCheckMaterialList = wsCheckMaterialRepository.findByCodeList(checkMaterialDtoList.stream().map(CheckMaterialDTO::getCheckMaterialCode).distinct().collect(Collectors.toList()));
        if (net.airuima.util.ValidateUtils.isValid(wsCheckMaterialList)) {
            throw new ResponseException("error.existSerialNumber", "核料凭证已存在");
        }
        List<String> serialNumbers = checkMaterialDtoList.stream().map(CheckMaterialDTO::getWorkSheetSerialNumber).distinct().collect(Collectors.toList());
        //获得工单相关数据
        List<WorkSheet> workSheetList = workSheetRepository.findBySerialNumberList(serialNumbers);
        if (CollectionUtils.isEmpty(workSheetList) || workSheetList.size() != serialNumbers.size()) {
            throw new ResponseException("error.serialNumberNotExist", "工单不存在");
        }
        Map<String, List<WorkSheet>> workSheetMap = workSheetList.stream().collect(Collectors.groupingBy(WorkSheet::getSerialNumber));
        //获得投料单相关数据
        List<WsMaterial> wsMaterialList = net.airuima.util.ValidateUtils.isValid(workSheetList) ? wsMaterialRepository.findByWorkSheetIdInAndDeleted(workSheetList.stream().map(WorkSheet::getId).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO) : new ArrayList<>();
        //判断当前导入物料是否在投料单中存在
        Map<String, List<WsMaterial>> wsMaterialMap = wsMaterialList.stream().collect(Collectors.groupingBy(wsMaterial -> wsMaterial.getWorkSheet().getSerialNumber()));
        Map<String, List<CheckMaterialDTO>> collect = checkMaterialDtoList.stream().collect(Collectors.groupingBy(CheckMaterialDTO::getWorkSheetSerialNumber));
        collect.forEach((serialNumber, checkMaterialDTos) -> {
            List<WsMaterial> wsMaterials = wsMaterialMap.get(serialNumber);
            Map<String, List<WsMaterial>> wsMaterialTempMap = wsMaterials.stream().collect(Collectors.groupingBy(wsMaterial -> wsMaterial.getMaterialDto().getCode()));
            if (checkMaterialDTos.stream().anyMatch(materialDTO -> !wsMaterialTempMap.containsKey(materialDTO.getMaterialCode()))) {
                throw new ResponseException("error.notExistMaterial", "物料在投料单中不存在");
            }
        });
        //获取是否自动核料配置，若是自动核料则自动更新库存及核料记录
        String result = rbaseSysCodeProxy.findByCode(net.airuima.rbase.constant.Constants.KEY_AUTO_CHECK_MATERIAL);
        boolean autoCheckMaterial = StringUtils.isNotBlank(result) && Boolean.parseBoolean(result);
        List<CheckMaterialDetailDTO> checkMaterialDetailDTOList = new ArrayList<>();
        //获得物料相关数据
        List<MaterialDTO> materialDtoList = rbaseMaterialService.findByCodeInAndDeleted(checkMaterialDtoList.stream().map(CheckMaterialDTO::getMaterialCode).distinct().collect(Collectors.toList()),Constants.LONG_ZERO);
        Map<String, List<MaterialDTO>> materialMap = materialDtoList.stream().collect(Collectors.groupingBy(MaterialDTO::getCode));
        //获得供应商相关数据
        List<String> supplierCodes = checkMaterialDtoList.stream().filter(checkMaterialDTO -> net.airuima.util.ValidateUtils.isValid(checkMaterialDTO.getSupplierCode())).map(CheckMaterialDTO::getSupplierCode).distinct().collect(Collectors.toList());
        List<SupplierDTO> supplierDtoList = FuncKeyUtil.checkApi(FuncKeyConstants.SUPPLIER) && !CollectionUtils.isEmpty(supplierCodes) ? rbaseSupplierProxy.findByCodeInAndDeleted(supplierCodes,Constants.LONG_ZERO) : null;
        Map<String, List<SupplierDTO>> supplierDtoMap = net.airuima.util.ValidateUtils.isValid(supplierDtoList) ? supplierDtoList.stream().collect(Collectors.groupingBy(SupplierDTO::getCode)) : null;
        checkMaterialDtoList.stream().collect(Collectors.collectingAndThen(Collectors.groupingBy(CheckMaterialDTO::new), Map::entrySet)).forEach(entry -> {
            CheckMaterialDTO key = entry.getKey();
            List<CheckMaterialDTO> value = entry.getValue();
            WorkSheet workSheet = workSheetMap.get(value.get(net.airuima.constant.Constants.INT_ZERO).getWorkSheetSerialNumber()).get(net.airuima.constant.Constants.INT_ZERO);
            //保存核料信息
            WsCheckMaterial wsCheckMaterial = new WsCheckMaterial().setCode(key.getCheckMaterialCode()).setWorkSheet(workSheet);
            wsCheckMaterial = wsCheckMaterialRepository.save(wsCheckMaterial);
            //保存核料详情
            WsCheckMaterial finalWsCheckMaterial = wsCheckMaterial;
            value.forEach(checkMaterialDTO -> {
                //去掉批次号的首尾空格
                String batch = checkMaterialDTO.getMaterialBatch().trim();
                WsCheckMaterialDetail wsCheckMaterialDetail = wsCheckMaterialDetailRepository.findByWsCheckMaterialIdAndMaterialIdAndBatchAndSupplierIdAndDeleted(finalWsCheckMaterial.getId(), materialMap.get(checkMaterialDTO.getMaterialCode()).get(net.airuima.constant.Constants.INT_ZERO).getId(), batch, Objects.nonNull(supplierDtoMap) ? supplierDtoMap.get(checkMaterialDTO.getSupplierCode()).get(net.airuima.constant.Constants.INT_ZERO).getId() : null, net.airuima.constant.Constants.LONG_ZERO).orElse(new WsCheckMaterialDetail());
                wsCheckMaterialDetail.setWsCheckMaterial(finalWsCheckMaterial).setWorkSheet(workSheet).setMaterialId(materialMap.get(checkMaterialDTO.getMaterialCode()).get(net.airuima.constant.Constants.INT_ZERO).getId()).setBatch(batch).setUncheckNumber(wsCheckMaterialDetail.getUncheckNumber() + checkMaterialDTO.getMaterialNumber());
                if (ValidateUtils.isValid(supplierDtoMap)) {
                    wsCheckMaterialDetail.setSupplierId(supplierDtoMap.get(checkMaterialDTO.getSupplierCode()).get(net.airuima.constant.Constants.INT_ZERO).getId());
                }
                wsCheckMaterialDetail = wsCheckMaterialDetailRepository.save(wsCheckMaterialDetail);
                if (autoCheckMaterial) {
                    checkMaterialDetailDTOList.add(new CheckMaterialDetailDTO().setId(wsCheckMaterialDetail.getId()).setNum(wsCheckMaterialDetail.getUncheckNumber()));
                }
            });
        });
        //如果是自动核料则直接自动进行核料和更新库存
        if (autoCheckMaterial && !CollectionUtils.isEmpty(checkMaterialDetailDTOList)) {
            wsCheckMaterialDetailServices[0].saveCheckMaterialDetail(checkMaterialDetailDTOList);
        }
    }

    /**
     * 删除导入的核料凭证
     *
     * @param id 核料记录ID
     * @return ResponseEntity<Void>
     * <AUTHOR>
     * @date 2021-05-23
     **/
    @Override
    public BaseDTO delete(Long id) {
        WsCheckMaterial wsCheckMaterial = wsCheckMaterialRepository.getReferenceById(id);
        /**
         * 工单开工并不一定进行了核料动作，可能当前工单设定的为不核料
         * 判断是否核料的标准就是查看当前核料记录的核料状态
         */
        if (wsCheckMaterial.getStatus() != ConstantsEnum.NOT_CHECK_MATERIAL.getCategoryName()) {
            return new BaseDTO(net.airuima.constant.Constants.KO, "工单已上料,禁止删除");
        }
        wsCheckMaterialDetailRepository.batchDeleteWsCheckMaterialDetailByWsCheckMaterialId(id);
        wsCheckMaterialRepository.logicDelete(id);
        return new BaseDTO(Constants.OK);
    }
}
