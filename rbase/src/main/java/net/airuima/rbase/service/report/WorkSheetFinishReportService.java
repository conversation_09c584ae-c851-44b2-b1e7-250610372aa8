package net.airuima.rbase.service.report;

import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.report.WorkLineReportDTO;
import net.airuima.rbase.dto.report.WorkSheetFinishReportDTO;
import net.airuima.rbase.dto.report.WorkSheetProgressReportDTO;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.ResponseContent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单完成率报表Service
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetFinishReportService {
    private static final String CREATED_DATE = "createdDate";
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private WorkLineRepository workLineRepository;

    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private BatchWorkDetailService batchWorkDetailService;

    @Autowired
    private CommonService commonService;
    /**
     * 工单完成率报表
     *
     * @param requestInfo 请求参数
     * @return
     */
    @Transactional(readOnly = true)
    public ResponseEntity<ResponseContent<WorkSheetFinishReportDTO.ResponseInfo>> workSheetFinishReport(WorkSheetFinishReportDTO.RequestInfo requestInfo) {

        Specification<WorkSheet> specification = (Specification<WorkSheet>) (root, query, criteriaBuilder) -> {

            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);

            //根据工单号筛选
            if (ValidateUtils.isValid(requestInfo.getWsSerialNumber())) {
                Predicate subWsPredicate = criteriaBuilder.equal(root.get("serialNumber"), requestInfo.getWsSerialNumber());
                predicateList.add(subWsPredicate);
            }

            //开始时间和结束时间筛选
            if (requestInfo.getEndDate() != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                        LocalDateTime.of(requestInfo.getEndDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant()));
            }
            if (requestInfo.getStartDate() != null ) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                        LocalDateTime.of(requestInfo.getStartDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant()));
            }


            return query.where(predicateList.toArray(new Predicate[predicateList.size()])).getRestriction();
        };

        Page<WorkSheet> workSheetPage = workSheetRepository.findAll(specification, PageRequest.of(requestInfo.getCurrentPage(), requestInfo.getPageSize()));
        List<WorkSheet> workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(null);
        WorkSheetFinishReportDTO.ResponseInfo responseInfo = new WorkSheetFinishReportDTO.ResponseInfo();
        List<WorkSheetFinishReportDTO.WsFinishDTO> wsFinishDtoList = new ArrayList<>();
        //根据工单获取对应的子工单
        if (ValidateUtils.isValid(workSheetList)) {
            workSheetList.stream().map(workSheet -> workSheet.getId()).collect(Collectors.toList()).forEach(workSheetId -> {
                WorkSheetFinishReportDTO.WsFinishDTO wsFinishDto = new WorkSheetFinishReportDTO.WsFinishDTO();
                List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
                if (ValidateUtils.isValid(subWorkSheetList)) {
                    wsFinishDto.setSerialNumber(subWorkSheetList.get(Constants.INT_ZERO).getWorkSheet().getSerialNumber());
                    wsFinishDto.setSubWsNumber(subWorkSheetList.size());
                    wsFinishDto.setFinishSubWsNumber((int) subWorkSheetList.stream().filter(subWorkSheet -> subWorkSheet.getStatus() >= Constants.FINISH).count());
                } else {
                    wsFinishDto.setSerialNumber(workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO).get().getSerialNumber());
                    wsFinishDto.setSubWsNumber(Constants.INT_ZERO);
                    wsFinishDto.setFinishSubWsNumber(Constants.INT_ZERO);
                    wsFinishDto.setFinishRate(Constants.DOUBLE_ZERRO);
                }
                wsFinishDtoList.add(wsFinishDto);
            });
            responseInfo.setCountSize(workSheetPage.getTotalElements());
            return ResponseContent.isOk(responseInfo.setWsFinishDtoList(wsFinishDtoList));
        }
        return ResponseContent.ok().message("查无数据!").isOkBuild();
    }


    /**
     * 通过名称或编码+是否开启+删除标识 查询产线
     *
     * @param nameOrCode 名称或编码
     * @return : java.util.List<net.airuima.rbase.domain.base.scene.WorkLine>
     * <AUTHOR>
     * @date 2022/12/30
     **/
    public List<WorkLineReportDTO> findWorkLineByNameOrCode(String nameOrCode) {
        List<WorkLine> workLineList = workLineRepository.findByNameOrCodeAndIsEnableAndDeleted(nameOrCode, Boolean.TRUE, Constants.LONG_ZERO);
        List<WorkLineReportDTO> workLineReportDTOList = workLineList.stream().map(i -> new WorkLineReportDTO().setId(i.getId()).setWorkLineName(i.getName()).setOrganizationName(i.getOrganizationDto().getName())).collect(Collectors.toList());
        return workLineReportDTOList;
    }

    /**
     * 工单进度看板
     *
     * @param workLineId 名称或编码
     * @return 生产线列表
     */
    public Page<WorkSheetProgressReportDTO> workSheetProgress(Long workLineId, Pageable pageable) {
        //获取投产粒度(true:子工单投产,false:工单投产)
        String productionModeStr = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean productionMode = StringUtils.isBlank(productionModeStr) ? Boolean.TRUE : Boolean.parseBoolean(productionModeStr);

        Page<WorkSheetProgressReportDTO> workSheetProgressReportDTOPage = null;
        if (productionMode) {
            //根据生产线获取工单进度集合(子工单投产)
            workSheetProgressReportDTOPage = subWorkSheetRepository.workSheetProgressBySubWorkSheet(workLineId, pageable);
        } else {
            //根据生产线获取工单进度集合(工单投产)
            workSheetProgressReportDTOPage = subWorkSheetRepository.workSheetProgressByWorkSheet(workLineId, pageable);
        }
        return workSheetProgressReportDTOPage;
    }
}
