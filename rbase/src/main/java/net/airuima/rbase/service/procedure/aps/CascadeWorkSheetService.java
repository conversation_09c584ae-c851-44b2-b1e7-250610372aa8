package net.airuima.rbase.service.procedure.aps;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.CascadeWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.pedigree.PedigreeDetailDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.integrate.rmps.IWorkSheetSyncMpsService;
import net.airuima.rbase.integrate.rwms.IShipmentOrderSyncWmsService;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.CascadeWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.base.pedigree.PedigreeService;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.plugin.ICascadeWorkSheetService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsGetDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsSaveDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单级联关系Service
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class CascadeWorkSheetService extends CommonJpaService<CascadeWorkSheet> implements ICascadeWorkSheetService {

    private final CascadeWorkSheetRepository cascadeWorkSheetRepository;

    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private PedigreeService pedigreeService;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;
    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private IWorkSheetSyncMpsService[] workSheetSyncMpsFeignClients;
    @Autowired
    private IShipmentOrderSyncWmsService[] shipmentOrderSyncWmsServices;

    public CascadeWorkSheetService(CascadeWorkSheetRepository cascadeWorkSheetRepository) {
        this.cascadeWorkSheetRepository = cascadeWorkSheetRepository;
    }

    /**
     * 通过物料清单ID获取不同场景下需要级联下单的参数信息
     * @param category 0:工单管理增加级联下单；1:销售订单下发工单级联下单
     * @param bomInfoId 物料清单ID
     * @return List<CreateCascadeWsGetDTO> 级联下单前端所需参数列表
     */
    @Transactional(readOnly = true)
    public List<CreateCascadeWsGetDTO> findCascadeWorkSheetPlanToOrderInfoByBomInfoId(Integer category,Long bomInfoId) {
        List<CreateCascadeWsGetDTO> createCascadeWsGetDTOS = findCascadeWorkSheetPlanToOrderInfo(rbaseBomProxy.findByBomInfoId(bomInfoId));
        if(category == Constants.INT_ZERO && CollectionUtils.isEmpty(createCascadeWsGetDTOS)){
            throw new ResponseException("error.cascadeWorkSheetInfoEmpty","当前产品不可级联下单，请检查BOM子件是否存在对应产品谱系以及工艺路线等关联信息");
        }
        return createCascadeWsGetDTOS;
    }

    /**
     * 根据bom信息获取级联下单产品谱系参数列表
     *
     * @param bomDtoList bom列表
     * @return 级联下单产品谱系参数列表
     */
    @Override
    public List<CreateCascadeWsGetDTO> findCascadeWorkSheetPlanToOrderInfo(List<BomDTO> bomDtoList) {
        List<CreateCascadeWsGetDTO> cascadeWsGetDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bomDtoList)) {
            return cascadeWsGetDTOList;
        }
        List<Long> childMaterialIdList = bomDtoList.stream().map(bomDTO -> bomDTO.getChildMaterial().getId()).toList();
        if (CollectionUtils.isEmpty(childMaterialIdList)) {
            return cascadeWsGetDTOList;
        }
        List<Pedigree> pedigreeList = pedigreeRepository.findByMaterialIdInAndDeleted(childMaterialIdList, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeList)) {
            return cascadeWsGetDTOList;
        }
        Map<Long, List<BomDTO>> groupBom = bomDtoList.stream().collect(Collectors.groupingBy(bomDTO -> bomDTO.getChildMaterial().getId()));
        pedigreeList.forEach(pedigree -> {
            //获取子级产品的工艺路线信息
            PedigreeDetailDTO pedigreeDetailDTO = pedigreeService.findByPedigree(pedigree.getId(), null, Constants.INT_ZERO);
            if (Objects.isNull(pedigreeDetailDTO) || CollectionUtils.isEmpty(pedigreeDetailDTO.getWorkFlowDTOList())) {
                return;
            }
            //获取子级产品的物料清单信息
            List<BomInfoDTO> bomInfoDTOList = rbaseBomProxy.findByMaterialId(pedigree.getMaterialId());
            if (CollectionUtils.isEmpty(bomInfoDTOList)) {
                return;
            }
            BomDTO bomDTO = groupBom.get(pedigree.getMaterialId()).get(Constants.INT_ZERO);
            CreateCascadeWsGetDTO createCascadeWsGetDTO = new CreateCascadeWsGetDTO();
            createCascadeWsGetDTO.setPedigree(pedigree)
                    .setProportion((int) Math.ceil(NumberUtils.divide(bomDTO.getProportion(), bomDTO.getBase(), Constants.INT_ONE).doubleValue()))
                    .setBomInfoDTOList(bomInfoDTOList).setWorkFlowDTOList(pedigreeDetailDTO.getWorkFlowDTOList());
            cascadeWsGetDTOList.add(createCascadeWsGetDTO);
        });
        return cascadeWsGetDTOList;
    }

    /**
     *  保存级联工单信息
     * @param createCascadeWsSaveDTO 待保存级联下单参数
     */
    public void createInstance(CreateCascadeWsSaveDTO createCascadeWsSaveDTO){
        WorkSheet superiorWorkSheet = workSheetRepository.findBySerialNumberAndDeleted(createCascadeWsSaveDTO.getSerialNumber(),Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.workSheetNotExist","工单不存在"));
        Optional<DictionaryDTO> dictionaryDTOOptional = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_PRODUCTION_MODE,Constants.LONG_ZERO);
        boolean subWsProductionMode = dictionaryDTOOptional.isEmpty() || Boolean.parseBoolean(dictionaryDTOOptional.get().getData());
        List<WorkSheet> subordinateWorkSheet = new ArrayList<>();
        createCascadeWsSaveDTO.getSubordinateWorkSheetInfoList().forEach(cascadeWorkSheetInfo -> {
            if (LocalDate.now().isAfter(cascadeWorkSheetInfo.getPlanStartDate()) || LocalDate.now().isAfter(cascadeWorkSheetInfo.getPlanEndDate())){
                throw new ResponseException("error.planEndDateOrStartDateLessThanCurrentTime", "计划时间不能小于当前时间");
            }
            String serialNumber = cascadeWorkSheetInfo.getSerialNumber();
            WorkLine workLine = workLineRepository.getReferenceById(cascadeWorkSheetInfo.getWorkLineId());
            if (!ValidateUtils.isValid(serialNumber)) {
                SerialNumberDTO serialNumberDTO = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET, null, workLine.getOrganizationId());
                serialNumber = rbaseSerialNumberProxy.generate(serialNumberDTO);
            }
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
            if (workSheetOptional.isPresent()){
                throw new ResponseException("error.serialNumberIsExist", "工单号已存在");
            }
            Pedigree pedigree = pedigreeRepository.getReferenceById(cascadeWorkSheetInfo.getPedigreeId());
            WorkFlow workFlow = workFlowRepository.getReferenceById(cascadeWorkSheetInfo.getWorkFlowId());
            WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
            if(CollectionUtils.isEmpty(workFlowDTO.getStepDtoList())){
                throw new ResponseException("error.workFlowStepNotExist", "工艺路线【"+workFlow.getName()+"未绑定工序");
            }
            for (StepDTO stepDto : workFlowDTO.getStepDtoList()) {
                Step step = stepRepository.getReferenceById(stepDto.getId());
                StepDTO stepConfigDto = commonService.findPedigreeStepConfig(null, pedigree, workFlow, step);
                if (null == stepConfigDto) {
                    throw new ResponseException("error.stepConfigNotExist", step.getCode() +"工序配置不存在");
                }
            }
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigree);
            WorkSheet workSheet = new WorkSheet();
            workSheet.setSerialNumber(serialNumber);
            workSheet.setNumber(cascadeWorkSheetInfo.getNumber());
            workSheet.setClientId(cascadeWorkSheetInfo.getClientId());
            workSheet.setPlanStartDate(cascadeWorkSheetInfo.getPlanStartDate().atStartOfDay());
            workSheet.setPlanEndDate(cascadeWorkSheetInfo.getPlanEndDate().atStartOfDay());
            workSheet.setCategory(superiorWorkSheet.getCategory());
            workSheet.setStatus(Constants.INT_ZERO);
            workSheet.setOrganizationId(workLine.getOrganizationId());
            workSheet.setBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            workSheet.setPedigree(pedigree);
            workSheet.setPriority(cascadeWorkSheetInfo.getPriority());
            workSheet.setWorkFlow(workFlow);
            workSheet.setWorkLine(workLine);
            workSheet.setGenerateSubWsStatus(Constants.INT_ZERO);
            workSheet.setDownGradeNumber(Constants.INT_ZERO);
            workSheet.setDeleted(Constants.LONG_ZERO);
            workSheet.setDeliveryDate(cascadeWorkSheetInfo.getDeliveryDate());
            WorkSheet fianlWorkSheet = workSheetRepository.save(workSheet);
            workSheetService.saveWsSteps(subWsProductionMode,workSheet,workFlow,workSheet.getPedigree(),workFlowDTO.getStepDtoList());
            List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            wsMaterialServices[0].saveWsMaterial(fianlWorkSheet,bomDtoList);
            //如果分单配置存在且子工单投产则自动分单
            if (null != pedigreeConfig && pedigreeConfig.getIsEnable() && subWsProductionMode && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) {
                subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                        pedigreeConfig.getSplitNumber(), Boolean.TRUE);
            }
            CascadeWorkSheet cascadeWorkSheet = new CascadeWorkSheet(superiorWorkSheet,workSheet);
            cascadeWorkSheet.setDeleted(Constants.LONG_ZERO);
            cascadeWorkSheetRepository.save(cascadeWorkSheet);
            subordinateWorkSheet.add(fianlWorkSheet);
        });
        if (ValidateUtils.isValid(subordinateWorkSheet)){
            for (WorkSheet workSheet : subordinateWorkSheet) {
                // 同步工单信息到MPS
                workSheetSyncMpsFeignClients[0].syncMpsWorkSheet(workSheet.getId());
                // 同步工单信息到WMS
                shipmentOrderSyncWmsServices[0].syncShipmentOrder(workSheet);
            }
        }
    }
}
