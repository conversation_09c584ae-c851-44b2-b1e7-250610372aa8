package net.airuima.rbase.service.report.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.rbase.service.report.api.IProductionPlanReportService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ProductionPlanReportServiceImpl implements IProductionPlanReportService {

    @Autowired
    private ProductionPlanRepository productionPlanRepository;

    /**
     * 获取数字车间-产线统计信息
     * @param workLineStatisticsDTO 数字车间-产线统计参数
     * @return net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO 获取数字车间-产线统计信息
     */
    @Override
    public WorkLineStatisticsDTO getOrgWorkLineIdProductionPlanAchieveRate(WorkLineStatisticsDTO workLineStatisticsDTO) {

        //获取指定计划时间内的 计划产量总数
        Long planFinishNumber = productionPlanRepository.sumByPlanDateAndCategoryAndWorkLineId(workLineStatisticsDTO.getId(), Constants.INT_ONE, Boolean.TRUE, LocalDate.now());

        List<WorkLineStatisticsDTO.RateInfo> rateInfoList = workLineStatisticsDTO.getRateInfoList();

        if (planFinishNumber == null){
            rateInfoList.add(new WorkLineStatisticsDTO.RateInfo("达成率",Constants.DOUBLE_ZERRO));
            workLineStatisticsDTO.setPlanFinishNumber(Constants.LONG_ZERO).setRateInfoList(rateInfoList);
        }else {
            rateInfoList.add(new WorkLineStatisticsDTO.RateInfo("达成率",NumberUtils.divide(workLineStatisticsDTO.getQualifiedNumber(), planFinishNumber, Constants.INT_FOUR).doubleValue()));
            workLineStatisticsDTO.setPlanFinishNumber(planFinishNumber).setRateInfoList(rateInfoList);
        }

        return workLineStatisticsDTO;
    }
}
