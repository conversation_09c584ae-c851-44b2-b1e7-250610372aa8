package net.airuima.rbase.service.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.dto.ExportDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.BusinessRuleCombinationEnum;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.IqcCheckItemDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.organization.RbaseSupplierProxy;
import net.airuima.rbase.proxy.qms.RbaseCheckItemProxy;
import net.airuima.rbase.proxy.qms.RbaseSampleCaseProxy;
import net.airuima.rbase.proxy.qms.RbaseVarietyProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepCheckRuleImportDTO;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepCheckRuleSaveDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测标准Service
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepCheckRuleService extends CommonJpaService<PedigreeStepCheckRule> {
    private final String PEDIGREE_STEP_CHECK_RULE_GRAPH= "pedigreeStepCheckRuleEntityGraph";
    private static final String VARIETY = "variety";
    private static final String ERROR = "error";
    private static final String CATEGORY = "category";
    private static final String VALUE = "value";
    private static final String WORK_SHEET_CATEGORY = "workSheetCategory";
    private static final String ERR_MSG_SUFFIX = "】不存在，请检查！！！";
    private static final String ERR_MSG_DISABLED = "】已禁用！！！";

    private final PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    private final PriorityElementConfigRepository priorityElementConfigRepository;
    private final PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;

    @Autowired
    private RbaseCheckItemProxy checkItemRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private WorkCellRepository workCellRepository;

    @Autowired
    private StepGroupRepository stepGroupRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    @Autowired
    private RbaseSupplierProxy rbaseSupplierProxy;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private RbaseVarietyProxy varietyRepository;

    @Autowired
    private RbaseSampleCaseProxy sampleCaseRepository;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;

    @Autowired
    private RbaseClientProxy rbaseClientProxy;

    public PedigreeStepCheckRuleService(PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository,
                                        PriorityElementConfigRepository priorityElementConfigRepository, PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository) {
        this.pedigreeStepCheckRuleRepository = pedigreeStepCheckRuleRepository;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.pedigreeStepCheckItemRepository = pedigreeStepCheckItemRepository;
    }


    @Override
    @FetchMethod
    public Page<PedigreeStepCheckRule> find(Specification<PedigreeStepCheckRule> spec, Pageable pageable) {
        return pedigreeStepCheckRuleRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_STEP_CHECK_RULE_GRAPH));
    }

    @Override
    @FetchMethod
    public List<PedigreeStepCheckRule> find(Specification<PedigreeStepCheckRule> spec) {
        return pedigreeStepCheckRuleRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_CHECK_RULE_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<PedigreeStepCheckRule> findAll(Pageable pageable) {
        return pedigreeStepCheckRuleRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_CHECK_RULE_GRAPH));
    }

    /**
     * 新增检测规则
     *
     * @param pedigreeStepCheckRuleSaveDTO 检测项目DTO
     * <AUTHOR>
     * @date 2022/11/2 21:39
     */
    public void saveInstance(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) {
        //校验编码
        if (StringUtils.isBlank(pedigreeStepCheckRuleSaveDTO.getCode())) {
            throw new ResponseException("error.CodeNotNull", "编码不能为空");
        }
        Optional<PedigreeStepCheckRule> standardRepositoryByCodeAndDeleted = pedigreeStepCheckRuleRepository.findByCodeAndDeleted(pedigreeStepCheckRuleSaveDTO.getCode(), Constants.LONG_ZERO);
        if (standardRepositoryByCodeAndDeleted.isPresent()) {
            throw new ResponseException("error.SameCode", "已有相同编码");
        }
        //校验检测规则
        if(pedigreeStepCheckRuleSaveDTO.getCategory() != Constants.INT_FIVE){
            Optional<PedigreeStepCheckRule> checkRule = checkPedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
            if (checkRule.isPresent()) {
                throw new ResponseException("error.OtherRulesAlreadyExist", "已有相同检测规则");
            }
        }
        if(pedigreeStepCheckRuleSaveDTO.getCategory() == Constants.INT_FIVE){
            Optional<PedigreeStepCheckRule> checkRule = checkIqcPedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
            if (checkRule.isPresent()) {
                throw new ResponseException("error.OtherRulesAlreadyExist", "已有相同检测规则");
            }
        }
        //将DTO保存到entity内
        PedigreeStepCheckRule pedigreeStepCheckRule = savePedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
        //保存检测规则
        PedigreeStepCheckRule savePedigreeStepCheckRule = pedigreeStepCheckRuleRepository.save(pedigreeStepCheckRule);
        //保存检测项目
        savePedigreeStepCheckItem(pedigreeStepCheckRuleSaveDTO, savePedigreeStepCheckRule);
    }

    /**
     * 将DTO保存到entity内
     *
     * @param pedigreeStepCheckRuleSaveDTO 检测规则DTO
     * <AUTHOR>
     * @date 2022/11/4 9:51
     */
    private PedigreeStepCheckRule savePedigreeStepCheckRule(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) {
        PedigreeStepCheckRule pedigreeStepCheckRule = MapperUtils.map(pedigreeStepCheckRuleSaveDTO, PedigreeStepCheckRule.class);
        pedigreeStepCheckRule.setPedigree(null != pedigreeStepCheckRuleSaveDTO.getPedigreeId() ? new Pedigree(pedigreeStepCheckRuleSaveDTO.getPedigreeId()) : null)
                .setStep(null != pedigreeStepCheckRuleSaveDTO.getStepId() ? new Step(pedigreeStepCheckRuleSaveDTO.getStepId()) : null)
                .setWorkFlow(null != pedigreeStepCheckRuleSaveDTO.getWorkFlowId() ? new WorkFlow(pedigreeStepCheckRuleSaveDTO.getWorkFlowId()) : null)
                .setStepGroup(null != pedigreeStepCheckRuleSaveDTO.getStepGroupId() ? new StepGroup(pedigreeStepCheckRuleSaveDTO.getStepGroupId()) : null)
                .setWorkSheet(null != pedigreeStepCheckRuleSaveDTO.getWorkSheetId() ? new WorkSheet(pedigreeStepCheckRuleSaveDTO.getWorkSheetId()) : null)
                .setVarietyId(null != pedigreeStepCheckRuleSaveDTO.getVarietyId() ? pedigreeStepCheckRuleSaveDTO.getVarietyId() : null)
                .setClientId(pedigreeStepCheckRuleSaveDTO.getClientId())
                .setWorkCell(null != pedigreeStepCheckRuleSaveDTO.getWorkCellId() ? new WorkCell(pedigreeStepCheckRuleSaveDTO.getWorkCellId()) : null)
                .setPriorityElementConfig(null != pedigreeStepCheckRuleSaveDTO.getPriorityElementConfigId() ? new PriorityElementConfig(pedigreeStepCheckRuleSaveDTO.getPriorityElementConfigId()) : null)
                .setSampleCaseId(null != pedigreeStepCheckRuleSaveDTO.getSampleCaseId() ? pedigreeStepCheckRuleSaveDTO.getSampleCaseId() : null)
                .setAttributeId(pedigreeStepCheckRuleSaveDTO.getAttributeId())
                .setMaterialId(pedigreeStepCheckRuleSaveDTO.getMaterialId())
                .setSupplierId(pedigreeStepCheckRuleSaveDTO.getSupplierId())
                .setDeleted(Constants.LONG_ZERO);
        return pedigreeStepCheckRule;
    }

    /**
     * 校验检测规则
     *
     * @param pedigreeStepCheckRuleSaveDTO 检测规则DTO
     * @return PedigreeStepCheckRule
     * <AUTHOR>
     * @date 2022/11/4 9:39
     */
    private Optional<PedigreeStepCheckRule> checkPedigreeStepCheckRule(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) {
        //根据组合条件和规则类型查询检测规则
        Optional<PedigreeStepCheckRule> checkRule = pedigreeStepCheckRuleRepository.findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndCategoryAndWorkCellIdAndVarietyObjIdAndDeleted(
                pedigreeStepCheckRuleSaveDTO.getPedigreeId(),
                pedigreeStepCheckRuleSaveDTO.getStepId(),
                pedigreeStepCheckRuleSaveDTO.getWorkFlowId(),
                pedigreeStepCheckRuleSaveDTO.getStepGroupId(),
                pedigreeStepCheckRuleSaveDTO.getWorkSheetId(),
                pedigreeStepCheckRuleSaveDTO.getClientId(),
                pedigreeStepCheckRuleSaveDTO.getWorkSheetCategory(), pedigreeStepCheckRuleSaveDTO.getCategory(),
                pedigreeStepCheckRuleSaveDTO.getWorkCellId(), pedigreeStepCheckRuleSaveDTO.getVarietyId(), Constants.LONG_ZERO);

        //校验检测项目是否重复
        if (ValidateUtils.isValid(pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList())) {
            List<PedigreeStepCheckRuleSaveDTO.checkItemInfo> checkItemInfoList = pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList().stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PedigreeStepCheckRuleSaveDTO.checkItemInfo::getId))), ArrayList::new));
            if (checkItemInfoList.size() < pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList().size()) {
                throw new ResponseException("error.ItemRepetition", "检测项目重复");
            }
        }
        return checkRule;
    }

    /**
     * 检查来料检质检方案
     * @param pedigreeStepCheckRuleSaveDTO 质检方案参数DTO
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule>  质检方案
     */
    private Optional<PedigreeStepCheckRule> checkIqcPedigreeStepCheckRule(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) {
        //根据组合条件和规则类型查询检测规则
        Optional<PedigreeStepCheckRule> checkRule = pedigreeStepCheckRuleRepository.findByPriorityElementConfigTargetAndCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(
                Constants.INT_FIFTEEN,
                pedigreeStepCheckRuleSaveDTO.getCategory(),
                pedigreeStepCheckRuleSaveDTO.getAttributeId(),
                pedigreeStepCheckRuleSaveDTO.getMaterialId(),
                pedigreeStepCheckRuleSaveDTO.getSupplierId(),
                pedigreeStepCheckRuleSaveDTO.getClientId(),
                Constants.LONG_ZERO);
        //校验检测项目是否重复
        if (ValidateUtils.isValid(pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList())) {
            List<PedigreeStepCheckRuleSaveDTO.checkItemInfo> checkItemInfoList = pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList().stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PedigreeStepCheckRuleSaveDTO.checkItemInfo::getId))), ArrayList::new));
            if (checkItemInfoList.size() < pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList().size()) {
                throw new ResponseException("error.ItemRepetition", "检测项目重复");
            }
        }
        return checkRule;
    }

    /**
     * 修改检测项目
     *
     * @param pedigreeStepCheckRuleSaveDTO 检测项目DTO
     * <AUTHOR>
     * @date 2022/11/2 21:39
     */
    public void updateInstance(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) {
        if (null == pedigreeStepCheckRuleSaveDTO.getId()) {
            throw new ResponseException("error.NoObjectSelected", "未选择需要修改的对象");
        }
        //校验检测规则
        if(pedigreeStepCheckRuleSaveDTO.getCategory() != Constants.INT_FIVE){
            Optional<PedigreeStepCheckRule> checkRule = checkPedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
            if (checkRule.isPresent() && !pedigreeStepCheckRuleSaveDTO.getId().equals(checkRule.get().getId())) {
                throw new ResponseException("error.OtherRulesAlreadyExist", "已有相同检测规则");
            }
        }
        if(pedigreeStepCheckRuleSaveDTO.getCategory() == Constants.INT_FIVE){
            Optional<PedigreeStepCheckRule> checkRule = checkIqcPedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
            if (checkRule.isPresent() && !pedigreeStepCheckRuleSaveDTO.getId().equals(checkRule.get().getId())) {
                throw new ResponseException("error.OtherRulesAlreadyExist", "已有相同检测规则");
            }
        }
        //校验优先级配置是否修改
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findById(pedigreeStepCheckRuleSaveDTO.getId()).orElseThrow(() -> new ResponseException("error.pedigreeStepCheckRuleNotExist", "产品谱系工序检测规则不存在"));
        if (!pedigreeStepCheckRule.getPriorityElementConfig().getId().equals(pedigreeStepCheckRuleSaveDTO.getPriorityElementConfigId())) {
            throw new ResponseException("error.PriorityDoNotUpdate", "优先级配置不可修改");
        }
        pedigreeStepCheckRule = savePedigreeStepCheckRule(pedigreeStepCheckRuleSaveDTO);
        //删除所有检测项目
        pedigreeStepCheckItemRepository.deleteByPedigreeStepCheckRuleId(pedigreeStepCheckRuleSaveDTO.getId());
        //保存检测规则信息
        pedigreeStepCheckRuleRepository.save(pedigreeStepCheckRule);
        //保存检测项目
        savePedigreeStepCheckItem(pedigreeStepCheckRuleSaveDTO, pedigreeStepCheckRule);
    }

    /**
     * 保存检测项目
     *
     * @param pedigreeStepCheckRuleSaveDTO 检测规则DTO
     * @param pedigreeStepCheckRule        检测规则entity
     * <AUTHOR>
     * @date 2022/11/4 9:37
     */
    private void savePedigreeStepCheckItem(PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO, PedigreeStepCheckRule pedigreeStepCheckRule) {
        //校验检测项目是否重复,只能修改检测项目和检测标准
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = Lists.newArrayList();
        if (ValidateUtils.isValid(pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList())) {
            for (PedigreeStepCheckRuleSaveDTO.checkItemInfo checkItemInfo : pedigreeStepCheckRuleSaveDTO.getCheckItemInfoList()) {
                PedigreeStepCheckItem pedigreeStepCheckItem = new PedigreeStepCheckItem();
                pedigreeStepCheckItem.setPedigreeStepCheckRule(pedigreeStepCheckRule).setCheckItemId(checkItemInfo.getId()).setInspectNumberCase(checkItemInfo.getInspectNumberCase()).setCustomizeInspectNumber(checkItemInfo.getCustomizeInspectNumber()).setQualifiedRange(checkItemInfo.getQualifiedRange()).setControl(checkItemInfo.getControl()).setDeleted(Constants.LONG_ZERO);
                if(Objects.nonNull(checkItemInfo.getSampleCaseId())){
                    pedigreeStepCheckItem.setSampleCaseId(checkItemInfo.getSampleCaseId());
                }
                pedigreeStepCheckItemList.add(pedigreeStepCheckItem);
            }
            pedigreeStepCheckItemRepository.saveAll(pedigreeStepCheckItemList);
        }
    }

    /**
     * 逻辑删除检测规则及关联的检测项目
     *
     * @param id 检测规则id
     * <AUTHOR>
     * @date 2022/11/3 13:32
     */
    public void deletedById(Long id) {
        pedigreeStepCheckItemRepository.deleteByPedigreeStepCheckRuleId(id);
        pedigreeStepCheckRuleRepository.deleteByIdAndDeleted(id, Constants.LONG_ZERO);
    }

    /**
     * 产品谱系工序检测判定标准数据导入
     *
     * @param file   产品谱系工序检测判定标准数据
     * @param target 条件对象
     * <AUTHOR>
     * @date 2023/03/10 18:00
     */
    public void multipleImport(MultipartFile file, Integer target) throws Exception {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(2);
        ExcelImportResult<PedigreeStepCheckRuleImportDTO> excelImportResult = ExcelImportUtil.importExcelMore(file.getInputStream(), PedigreeStepCheckRuleImportDTO.class, params);
        // 得到导入文件转换成的列表数据
        List<PedigreeStepCheckRuleImportDTO> PedigreeStepCheckRuleImportList = excelImportResult.getList();
        // 记录excel行数【计数器】
        AtomicInteger count = new AtomicInteger(Constants.INT_ONE);
        // 获取质量检测类型
        String qualityCheckCategory = commonService.getDictionaryData(Constants.KEY_QUALITY_CHECK_CATEGORY);
        // 获取工单的类型
        String workSheetCategoryConfig = commonService.getDictionaryData(Constants.WORK_SHEET_CATEGORY);
        // 通过条件对象获取条件优先级配置
        List<PriorityElementConfig> priorityElementConfigList = priorityElementConfigRepository.findAllByTargetAndDeletedOrderByPriorityDesc(target, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(priorityElementConfigList)) {
            throw new ResponseException(ERROR, "取条件优先级配置不存在");
        }
        if (Constants.INT_FIFTEEN != target) {
            //处理质检方案导入
            handleCheckRuleData(PedigreeStepCheckRuleImportList, priorityElementConfigList, qualityCheckCategory, workSheetCategoryConfig, count);
        }
        if (Constants.INT_FIFTEEN == target) {
            // 处理来料检数据类型质检方案导入
            handleIqcCheckRuleData(PedigreeStepCheckRuleImportList, priorityElementConfigList, qualityCheckCategory, workSheetCategoryConfig, count);
        }

    }

    /**
     * 处理检测规则数据
     * @param PedigreeStepCheckRuleImportList 检测规则数据
     * @param priorityElementConfigList 优先级配置
     * @param qualityCheckCategory 质量检测类型
     * @param workSheetCategoryConfig 工单类型
     * @param count 计数器
     */
    private void handleIqcCheckRuleData(List<PedigreeStepCheckRuleImportDTO> PedigreeStepCheckRuleImportList, List<PriorityElementConfig> priorityElementConfigList, String qualityCheckCategory, String workSheetCategoryConfig, AtomicInteger count) {
        // 处理行数据
        PedigreeStepCheckRuleImportList.forEach(pedigreeStepCheckRuleImportDTO -> {
            // 验证excel行数据
            Map<String, Object> map = verifyIqcImportExcel(pedigreeStepCheckRuleImportDTO, priorityElementConfigList, qualityCheckCategory, workSheetCategoryConfig, count);
            // 编码
            String code = (String) map.get("code");
            // 名称
            String name = (String) map.get("name");
            // 客户信息
            ClientDTO clientDTO = (ClientDTO) map.get("clientDTO");
            // 物料信息
            MaterialDTO materialDTO = (MaterialDTO) map.get("materialDTO");
            // 物料属性信息
            MaterialAttributeDTO materialAttributeDTO = (MaterialAttributeDTO) map.get("materialAttributeDTO");
            // 供应商信息
            SupplierDTO supplierDTO = (SupplierDTO) map.get("supplierDTO");
            // 检测项目列表
            List<IqcCheckItemDTO> checkItemList = (List<IqcCheckItemDTO>) map.get("checkItemList");
            //  target 15 就是来料检
            int category = Constants.INT_FIVE;
            // 检测优先级
            PriorityElementConfig priorityElementConfig = (PriorityElementConfig) map.get("priorityElementConfig");
            // 抽样方案
            SampleCaseDTO sampleCase = (SampleCaseDTO) map.get("sampleCase");
            // 项目类型
            VarietyDTO variety = (VarietyDTO) map.get(VARIETY);
            //根据组合条件和规则类型查询检测规则
            Long attributeId = Optional.ofNullable(materialAttributeDTO).map(MaterialAttributeDTO::getId).orElse(null);
            Long materialId = Optional.ofNullable(materialDTO).map(MaterialDTO::getId).orElse(null);
            Long supplierId = Optional.ofNullable(supplierDTO).map(SupplierDTO::getId).orElse(null);
            Long clientId = Optional.ofNullable(clientDTO).map(ClientDTO::getId).orElse(null);
            Optional<PedigreeStepCheckRule> queryCheckRule = pedigreeStepCheckRuleRepository.findByPriorityElementConfigTargetAndCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(
                    Constants.INT_FIFTEEN,
                    category,
                    attributeId,
                    materialId,
                    supplierId,
                    clientId,
                    Constants.LONG_ZERO);
            PedigreeStepCheckRule savePedigreeStepCheckRule = null;
            if (queryCheckRule.isPresent()) {
                savePedigreeStepCheckRule = queryCheckRule.get();
                savePedigreeStepCheckRule.setVarietyObj(variety).setCode(code).setName(name).setSampleCase(sampleCase).setDeleted(Constants.LONG_ZERO);
                // 保存
                pedigreeStepCheckRuleRepository.save(savePedigreeStepCheckRule);
                //删除所有检测项目
                pedigreeStepCheckItemRepository.deleteByPedigreeStepCheckRuleId(savePedigreeStepCheckRule.getId());
                // 处理项目
                if (ValidateUtils.isValid(checkItemList)) {
                    saveIqcCheckRuleItem(savePedigreeStepCheckRule, checkItemList);
                }
            } else {
                savePedigreeStepCheckRule = new PedigreeStepCheckRule();
                savePedigreeStepCheckRule.setMaterialId(materialId)
                        .setAttributeId(attributeId)
                        .setSupplierId(supplierId)
                        .setCategory(category)
                        .setPriorityElementConfig(priorityElementConfig)
                        .setIsEnable(Boolean.TRUE)
                        .setClientId(clientId).setVarietyObj(variety).setCode(code).setName(name).setSampleCase(sampleCase).setDeleted(Constants.LONG_ZERO);
                // 保存
                pedigreeStepCheckRuleRepository.save(savePedigreeStepCheckRule);
                // 处理项目
                if (ValidateUtils.isValid(checkItemList)) {
                    saveIqcCheckRuleItem(savePedigreeStepCheckRule, checkItemList);
                }
            }
            count.getAndIncrement();
        });
    }

    /**
     * 保存来料检质检方案详情
     * @param savePedigreeStepCheckRule  质检方案
     * @param checkItemList 检测项目列表
     */
    private void saveIqcCheckRuleItem(PedigreeStepCheckRule savePedigreeStepCheckRule, List<IqcCheckItemDTO> checkItemList) {
        checkItemList.forEach(checkItemDto -> {
            PedigreeStepCheckItem pedigreeStepCheckItem = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndCheckItemIdAndDeleted(savePedigreeStepCheckRule.getId(), checkItemDto.getCheckItem().getId(), Constants.LONG_ZERO).orElse(new PedigreeStepCheckItem());
            pedigreeStepCheckItem.setCheckItemId(checkItemDto.getCheckItem().getId())
                    .setPedigreeStepCheckRule(savePedigreeStepCheckRule)
                    .setQualifiedRange(checkItemDto.getCheckItem().getQualifiedRange())
                    .setInspectNumberCase(checkItemDto.getInspectNumberCase())
                    .setControl(checkItemDto.getControl());
            pedigreeStepCheckItem.setDeleted(Constants.LONG_ZERO);
            pedigreeStepCheckItemRepository.save(pedigreeStepCheckItem);
        });
    }


    /**
     * 验证excel行数据 来料检验证
     *
     * @param pedigreeStepCheckRuleImportDTO 产品谱系工序检测判定标准数据导入DTO
     * @param priorityElementConfigList      条件优先级配置列表
     * @param qualityCheckCategory           质量检测类型
     * @param count                          记录excel行数【计数器】
     * @return java.util.Map<java.lang.String, java.lang.String> 返回验证结果
     */
    private Map<String, Object> verifyIqcImportExcel(PedigreeStepCheckRuleImportDTO pedigreeStepCheckRuleImportDTO,
                                                  List<PriorityElementConfig> priorityElementConfigList,
                                                  String qualityCheckCategory, String workSheetCategoryConfig,
                                                  AtomicInteger count) {
        Map<String, Object> map = new HashMap<>();
        // 校验编码
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getCode())) {
            map.put("code", pedigreeStepCheckRuleImportDTO.getCode());
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，编码为空");
        }

        // 获取名称
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getName())) {
            map.put("name", pedigreeStepCheckRuleImportDTO.getName());
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，名称为空");
        }


        // 获取条件组合
        List<Integer> combinationList = getCombinationList(pedigreeStepCheckRuleImportDTO);
        PriorityElementConfig priorityElementConfig = priorityElementConfigList.stream().filter(config ->
                        combinationList.size() == config.getCombination().size() && combinationList.containsAll(config.getCombination()))
                .findFirst().orElse(null);
        if (priorityElementConfig == null) {
            throw new ResponseException(ERROR, "没有匹配的条件优先级配置");
        }
        map.put("priorityElementConfig", priorityElementConfig);
        // 获取客户信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getClientCode())) {
            ClientDTO clientDTO = rbaseClientProxy.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getClientCode(),Constants.LONG_ZERO);;
            map.put("clientDTO", clientDTO);
        }
        // 获取物料信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getMaterialCode())) {
            MaterialDTO materialDTO = rbaseMaterialProxy.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
            map.put("materialDTO", materialDTO);
        }
        // 获取物料属性信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getAttributeCode())) {
            MaterialAttributeDTO attributeDTO = rbaseBomProxy.findByCode(pedigreeStepCheckRuleImportDTO.getAttributeCode());
            map.put("materialAttributeDTO", attributeDTO);
        }
        // 获取供应商信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSupplierCode())) {
            SupplierDTO supplierDTO = rbaseSupplierProxy.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getClientCode(),Constants.LONG_ZERO);
            map.put("supplierDTO ", supplierDTO );
        }
        // 项目类型
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getVarietyCode())) {
            VarietyDTO variety =
                    varietyRepository.findByCodeAndIsEnableAndDeleted(pedigreeStepCheckRuleImportDTO.getVarietyCode(), Boolean.TRUE, Constants.LONG_ZERO);
            map.put(VARIETY, variety);
        }
        // 抽样方案
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSampleCaseCode())) {
            SampleCaseDTO sampleCase =
                    sampleCaseRepository.findByCodeAndIsEnableAndDeleted(pedigreeStepCheckRuleImportDTO.getSampleCaseCode(), Boolean.TRUE, Constants.LONG_ZERO);
            if (sampleCase == null) {
                throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，抽检方案编码为【" + pedigreeStepCheckRuleImportDTO.getSampleCaseCode()
                        + ERR_MSG_SUFFIX);
            }
            if (!sampleCase.isEnable()) {
                throw new ResponseException("error.sampleCaseDisabled", "第【" + count.get() + "】行出现问题，抽检方案编码为【" + pedigreeStepCheckRuleImportDTO.getSampleCaseCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("sampleCase", sampleCase);
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，抽样方案编码为空");
        }
        // 过期时间校验
        if (null == pedigreeStepCheckRuleImportDTO.getExpiryDate()) {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，有效期为空");
        }
        // 导入进来的list不存在的时候列表里面存在元素，并且为null
        List<PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO> pedigreeStepCheckItemCodeList = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList();
        pedigreeStepCheckItemCodeList = pedigreeStepCheckItemCodeList.stream().filter(pedigreeStepCheckItemDTO ->
                StringUtils.isNotBlank(pedigreeStepCheckItemDTO.getCheckItemCode())).collect(Collectors.toList());
        pedigreeStepCheckRuleImportDTO.setPedigreeStepCheckItemCodeList(pedigreeStepCheckItemCodeList);
        // 获取检测项目库列表信息
        if (ValidateUtils.isValid(pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList())) {
            List<String> codes = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList().stream()
                    .map(PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO::getCheckItemCode).collect(Collectors.toList());
            List<CheckItemDTO> checkItemList = checkItemRepository.findByCodeInAndDeleted(codes, Constants.LONG_ZERO);
            List<IqcCheckItemDTO> iqcCheckItemDTOList = Lists.newArrayList();
            codes.forEach(code -> {
                Optional<PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO> pedigreeStepCheckItemOptional = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList().stream().filter(pedigreeStepCheckItemDto -> pedigreeStepCheckItemDto.getCheckItemCode().equals(code) && !ObjectUtils.isEmpty(pedigreeStepCheckItemDto.getControl())).findFirst();
                if (pedigreeStepCheckItemOptional.isEmpty()) {
                    throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + "】未配置管控模式，请检查！！！");
                }
                List<String> checkItemCodeList = checkItemList.stream().map(CheckItemDTO::getCode).toList();
                if (!checkItemCodeList.contains(code)) {
                    throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + ERR_MSG_SUFFIX);
                }
                if (checkItemList.stream().anyMatch(checkItem -> checkItem.getCode().equals(code) && !checkItem.getIsEnable())) {
                    throw new ResponseException("error.checkItemDisabled", "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + ERR_MSG_DISABLED);
                }
                pedigreeStepCheckItemOptional.ifPresent(pedigreeStepCheckItemDto -> {
                    map.put(pedigreeStepCheckItemDto.getCheckItemCode(), pedigreeStepCheckItemDto.getControl());
                    checkItemList.stream().filter(checkItem -> checkItem.getCode().equals(code)).findFirst().ifPresent(checkItem -> {
                        IqcCheckItemDTO iqcCheckItemDTO = new IqcCheckItemDTO();
                        iqcCheckItemDTO.setCheckItem(checkItem)
                                .setInspectNumberCase(pedigreeStepCheckItemDto.getInspectNumberCase())
                                .setControl(pedigreeStepCheckItemDto.getControl());
                        iqcCheckItemDTOList.add(iqcCheckItemDTO);
                    });
                });

            });
            map.put("checkItemList",  iqcCheckItemDTOList);
        }
        return map;
    }

    /**
     * 出质检方案导入数据
     * @param PedigreeStepCheckRuleImportList 产品谱系工序检测判定标准数据导入DTO
     * @param priorityElementConfigList 条件优先级配置列表
     * @param qualityCheckCategory 质量检测类型
     * @param workSheetCategoryConfig 工单类型
     * @param count 记录excel行数【计数器】
     */
    private void handleCheckRuleData(List<PedigreeStepCheckRuleImportDTO> PedigreeStepCheckRuleImportList, List<PriorityElementConfig> priorityElementConfigList, String qualityCheckCategory, String workSheetCategoryConfig, AtomicInteger count) {
        // 处理行数据
        PedigreeStepCheckRuleImportList.forEach(pedigreeStepCheckRuleImportDTO -> {
            // 验证excel行数据
            Map<String, Object> map = verifyImportExcel(pedigreeStepCheckRuleImportDTO, priorityElementConfigList, qualityCheckCategory, workSheetCategoryConfig, count);
            // 编码
            String code = (String) map.get("code");
            // 名称
            String name = (String) map.get("name");
            // 客户信息
            ClientDTO clientDTO = (ClientDTO) map.get("clientDTO");
            if (clientDTO == null) {
                clientDTO = new ClientDTO();
            }
            // 检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)
            int category = map.get(CATEGORY) == null ? 0 : Integer.parseInt(map.get(CATEGORY).toString());
            // 检测优先级
            PriorityElementConfig priorityElementConfig = (PriorityElementConfig) map.get("priorityElementConfig");
            //工单类型
            Integer workSheetCategory = map.get(WORK_SHEET_CATEGORY) == null ? null : Integer.parseInt(map.get(WORK_SHEET_CATEGORY).toString());
            // 项目类型
            VarietyDTO variety = (VarietyDTO) map.get(VARIETY);
            // 抽样方案
            SampleCaseDTO sampleCase = (SampleCaseDTO) map.get("sampleCase");
            Pedigree pedigree = (Pedigree) map.get("pedigree");
            Step step = (Step) map.get("step");
            WorkSheet workSheet = (WorkSheet) map.get("workSheet");
            WorkCell workCell = (WorkCell) map.get("workCell");
            StepGroup stepGroup = (StepGroup) map.get("stepGroup");
            WorkFlow workFlow = (WorkFlow) map.get("workFlow");

            PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findUnique(
                    pedigree == null ? null : pedigree.getId(), workSheet == null ? null : workSheet.getId(), workSheetCategory,
                    stepGroup == null ? null : stepGroup.getId(), step == null ? null : step.getId(),
                    workFlow == null ? null : workFlow.getId(), clientDTO.getId()).orElse(new PedigreeStepCheckRule());

            // 创建对象
            pedigreeStepCheckRule.setCode(code).setName(name).setClientId(clientDTO.getId()).setCategory(category).setPriorityElementConfig(priorityElementConfig)
                    .setVarietyObj(variety).setSampleCase(sampleCase).setIsEnable(Boolean.TRUE).setPedigree(pedigree)
                    .setStep(step).setWorkSheet(workSheet).setWorkCell(workCell)
                    .setStepGroup(stepGroup).setExpiryDate(pedigreeStepCheckRuleImportDTO.getExpiryDate());
            // 保存
            pedigreeStepCheckRuleRepository.save(pedigreeStepCheckRule);
            // 检测项目列表
            List<CheckItemDTO> checkItemList = (List<CheckItemDTO>) map.get("checkItemList");
            // 处理项目
            if (ValidateUtils.isValid(checkItemList)) {
                checkItemList.forEach(checkItem -> {
                    PedigreeStepCheckItem pedigreeStepCheckItem = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndCheckItemIdAndDeleted(pedigreeStepCheckRule.getId(), checkItem.getId(), Constants.LONG_ZERO).orElse(new PedigreeStepCheckItem());
                    pedigreeStepCheckItem.setCheckItemId(checkItem.getId())
                            .setPedigreeStepCheckRule(pedigreeStepCheckRule)
                            .setQualifiedRange(checkItem.getQualifiedRange())
                            .setControl((boolean) map.get(checkItem.getCode()));
                    pedigreeStepCheckItemRepository.save(pedigreeStepCheckItem);
                });
            }
            count.getAndIncrement();
        });
    }

    /**
     * 验证excel行数据
     *
     * @param pedigreeStepCheckRuleImportDTO 产品谱系工序检测判定标准数据导入DTO
     * @param priorityElementConfigList      条件优先级配置列表
     * @param qualityCheckCategory           质量检测类型
     * @param count                          记录excel行数【计数器】
     * @return Map
     */
    private Map<String, Object> verifyImportExcel(PedigreeStepCheckRuleImportDTO pedigreeStepCheckRuleImportDTO,
                                                  List<PriorityElementConfig> priorityElementConfigList,
                                                  String qualityCheckCategory, String workSheetCategoryConfig,
                                                  AtomicInteger count) {
        Map<String, Object> map = new HashMap<>();
        // 校验编码
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getCode())) {
            map.put("code", pedigreeStepCheckRuleImportDTO.getCode());
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，编码为空");
        }

        // 获取名称
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getName())) {
            map.put("name", pedigreeStepCheckRuleImportDTO.getName());
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，名称为空");
        }

        // 获取检测类型
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getCategory())) {
            // 处理并往map中设置检测类型
            verifyImportExcelCategoryConfig(map, qualityCheckCategory, pedigreeStepCheckRuleImportDTO.getCategory(), count);
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，检测类型为空");
        }
        // 获取工单类型
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkSheetCategory())) {
            verifyImportExcelWorkSheetCategoryConfig(map, workSheetCategoryConfig, pedigreeStepCheckRuleImportDTO.getWorkSheetCategory(), count);
        }
        // 获取条件组合
        List<Integer> combinationList = getCombinationList(pedigreeStepCheckRuleImportDTO);
        PriorityElementConfig priorityElementConfig = priorityElementConfigList.stream().filter(config ->
                        combinationList.size() == config.getCombination().size() && combinationList.containsAll(config.getCombination()))
                .findFirst().orElse(null);
        if (priorityElementConfig == null) {
            throw new ResponseException(ERROR, "没有匹配的条件优先级配置");
        }
        map.put("priorityElementConfig", priorityElementConfig);
        // 获取产品谱系信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getPedigreeCode())) {
            Pedigree pedigree = pedigreeRepository.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getPedigreeCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，产品谱系编码为【" + pedigreeStepCheckRuleImportDTO.getPedigreeCode()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            if (null != pedigree && !pedigree.getIsEnable()) {
                throw new ResponseException("error.pedigreeDisabled", "第【" + count.get() + "】行出现问题，产品谱系编码为【" + pedigreeStepCheckRuleImportDTO.getPedigreeCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("pedigree", pedigree);
        }
        // 获取工序信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getStepCode())) {
            Step step = stepRepository.findByCode(pedigreeStepCheckRuleImportDTO.getStepCode())
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，工序编码为【" + pedigreeStepCheckRuleImportDTO.getStepCode()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            if (null != step && !step.getIsEnable()) {
                throw new ResponseException("error.stepDisabled", "第【" + count.get() + "】行出现问题，工序编码为【" + pedigreeStepCheckRuleImportDTO.getStepCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("step", step);
        }
        // 获取工单信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSerialNumber())) {
            WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(pedigreeStepCheckRuleImportDTO.getSerialNumber(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，工单号为【" + pedigreeStepCheckRuleImportDTO.getSerialNumber()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            map.put("workSheet", workSheet);
        }
        // 获取工位信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkCellCode())) {
            WorkCell workCell = workCellRepository.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getWorkCellCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，工位编码为【" + pedigreeStepCheckRuleImportDTO.getWorkCellCode()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            if (null != workCell && !workCell.getIsEnable()) {
                throw new ResponseException("error.workCellDisabled", "第【" + count.get() + "】行出现问题，工位编码为【" + pedigreeStepCheckRuleImportDTO.getWorkCellCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("workCell", workCell);
        }
        // 获取工序组
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getStepGroupCode())) {
            StepGroup stepGroup = stepGroupRepository.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getStepGroupCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，工序组编码为【" + pedigreeStepCheckRuleImportDTO.getStepGroupCode()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            if (null != stepGroup && !stepGroup.getIsEnable()) {
                throw new ResponseException("error.stepGroupDisabled", "第【" + count.get() + "】行出现问题，工序组编码为【" + pedigreeStepCheckRuleImportDTO.getStepGroupCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("stepGroup", stepGroup);
        }
        // 获取客户信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getClientCode())) {
            ClientDTO clientDTO = rbaseClientProxy.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getClientCode(),Constants.LONG_ZERO);
            if (clientDTO == null) {
                throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，客户编码为【" + pedigreeStepCheckRuleImportDTO.getClientCode()
                        + ERR_MSG_SUFFIX);
            }
            if (!clientDTO.getEnable()) {
                throw new ResponseException("error.clientDisabled", "第【" + count.get() + "】行出现问题，客户编码为【" + pedigreeStepCheckRuleImportDTO.getClientCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("clientDTO", clientDTO);
        }

        // 项目类型
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getVarietyCode())) {
            VarietyDTO variety =
                    varietyRepository.findByCodeAndIsEnableAndDeleted(pedigreeStepCheckRuleImportDTO.getVarietyCode(), Boolean.TRUE, Constants.LONG_ZERO);
            if (variety == null) {
                throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，项目类型编码为【" + pedigreeStepCheckRuleImportDTO.getVarietyCode()
                        + ERR_MSG_SUFFIX);
            }
            if (!variety.getIsEnable()) {
                throw new ResponseException("error.varietyDisabled", "第【" + count.get() + "】行出现问题，项目类型编码为【" + pedigreeStepCheckRuleImportDTO.getVarietyCode()
                        + ERR_MSG_DISABLED);
            }
            map.put(VARIETY, variety);
        }

        // 获取工艺路线信息
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkFlowCode())) {
            WorkFlow workFlow = workFlowRepository.findByCodeAndDeleted(pedigreeStepCheckRuleImportDTO.getWorkFlowCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new BadRequestAlertException("第【" + count.get() + "】行出现问题，工艺路线编码为【" + pedigreeStepCheckRuleImportDTO.getWorkFlowCode()
                            + ERR_MSG_SUFFIX, PedigreeStepCheckRule.class.getSimpleName(), ERROR));
            if (!workFlow.getIsEnable()) {
                throw new ResponseException("error.workFlowDisabled", "第【" + count.get() + "】行出现问题，工艺路线编码为【" + pedigreeStepCheckRuleImportDTO.getWorkFlowCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("workFlow", workFlow);
        }

        // 抽样方案
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSampleCaseCode())) {
            SampleCaseDTO sampleCase =
                    sampleCaseRepository.findByCodeAndIsEnableAndDeleted(pedigreeStepCheckRuleImportDTO.getSampleCaseCode(), Boolean.TRUE, Constants.LONG_ZERO);
            if (sampleCase == null) {
                throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，抽检方案编码为【" + pedigreeStepCheckRuleImportDTO.getSampleCaseCode()
                        + ERR_MSG_SUFFIX);
            }
            if (!sampleCase.isEnable()) {
                throw new ResponseException("error.sampleCaseDisabled", "第【" + count.get() + "】行出现问题，抽检方案编码为【" + pedigreeStepCheckRuleImportDTO.getSampleCaseCode()
                        + ERR_MSG_DISABLED);
            }
            map.put("sampleCase", sampleCase);
        } else {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，抽样方案编码为空");
        }

        // 过期时间校验
        if (null == pedigreeStepCheckRuleImportDTO.getExpiryDate()) {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，有效期为空");
        }

        // 导入进来的list不存在的时候列表里面存在元素，并且为null
        List<PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO> pedigreeStepCheckItemCodeList = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList();
        pedigreeStepCheckItemCodeList = pedigreeStepCheckItemCodeList.stream().filter(pedigreeStepCheckItemDTO ->
                StringUtils.isNotBlank(pedigreeStepCheckItemDTO.getCheckItemCode())).collect(Collectors.toList());
        pedigreeStepCheckRuleImportDTO.setPedigreeStepCheckItemCodeList(pedigreeStepCheckItemCodeList);
        // 获取检测项目库列表信息
        if (ValidateUtils.isValid(pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList())) {
            List<String> codes = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList().stream()
                    .map(PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO::getCheckItemCode).collect(Collectors.toList());
            List<CheckItemDTO> checkItemList = checkItemRepository.findByCodeInAndDeleted(codes, Constants.LONG_ZERO);
            codes.forEach(code -> {
                Optional<PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO> pedigreeStepCheckItemOptional = pedigreeStepCheckRuleImportDTO.getPedigreeStepCheckItemCodeList().stream().filter(pedigreeStepCheckItemDto -> pedigreeStepCheckItemDto.getCheckItemCode().equals(code) && !ObjectUtils.isEmpty(pedigreeStepCheckItemDto.getControl())).findFirst();
                if (pedigreeStepCheckItemOptional.isEmpty()) {
                    throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + "】未配置管控模式，请检查！！！");
                }
                pedigreeStepCheckItemOptional.ifPresent(pedigreeStepCheckItemDto -> {
                    map.put(pedigreeStepCheckItemDto.getCheckItemCode(), pedigreeStepCheckItemDto.getControl());
                });
                List<String> checkItemCodeList = checkItemList.stream().map(CheckItemDTO::getCode).toList();
                if (!checkItemCodeList.contains(code)) {
                    throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + ERR_MSG_SUFFIX);
                }
                if (checkItemList.stream().anyMatch(checkItem -> checkItem.getCode().equals(code) && !checkItem.getIsEnable())) {
                    throw new ResponseException("error.checkItemDisabled", "第【" + count.get() + "】行出现问题，检测项目库编码为【" + code
                            + ERR_MSG_DISABLED);
                }

            });
            map.put("checkItemList", checkItemList);
        }
        return map;
    }

    /**
     * 获取条件组合
     *
     * @param pedigreeStepCheckRuleImportDTO 产品谱系工序检测判定标准数据导入DTO
     * @return List<Integer>
     */
    private List<Integer> getCombinationList(PedigreeStepCheckRuleImportDTO pedigreeStepCheckRuleImportDTO) {
        List<Integer> list = new ArrayList<>();
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getPedigreeCode())) {
            list.add(BusinessRuleCombinationEnum.PEDIGREE.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getStepCode())) {
            list.add(BusinessRuleCombinationEnum.STEP.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSerialNumber())) {
            list.add(BusinessRuleCombinationEnum.WORKSHEET.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkCellCode())) {
            list.add(BusinessRuleCombinationEnum.WORKCELL.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkSheetCategory())) {
            list.add(BusinessRuleCombinationEnum.WORKSHEETCATEGORY.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getStepGroupCode())) {
            list.add(BusinessRuleCombinationEnum.STEPGROUP.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getClientCode())) {
            list.add(BusinessRuleCombinationEnum.CLIENTDTO.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getWorkFlowCode())) {
            list.add(BusinessRuleCombinationEnum.WORKFLOW.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getMaterialCode())) {
            list.add(BusinessRuleCombinationEnum.MATERIAL.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getAttributeCode())) {
            list.add(BusinessRuleCombinationEnum.MATERIAL_ATTRIBUTE.getCategoryValue());
        }
        if (StringUtils.isNotBlank(pedigreeStepCheckRuleImportDTO.getSupplierCode())) {
            list.add(BusinessRuleCombinationEnum.SUPPLIER.getCategoryValue());
        }
        return list;
    }

    /**
     * 获取检测类型
     *
     * @param map                  带保存的信息列表
     * @param qualityCheckCategory 检测类型Json
     * @param category             excel检测类型
     * @param count                excel行数
     */
    private void verifyImportExcelCategoryConfig(Map<String, Object> map, String qualityCheckCategory, String category, AtomicInteger count) {
        if (StringUtils.isBlank(qualityCheckCategory)) {
            throw new ResponseException(ERROR, "质量检测类型配置不存在");
        }
        List<Map<String, String>> qualityCheckCategoryList = JSON.parseObject(qualityCheckCategory, new TypeReference<List<Map<String, String>>>() {
        });
        qualityCheckCategoryList.forEach(categoryMap -> {
            String value = categoryMap.get(VALUE);
            if (Objects.equals(value, category)) {
                map.put(CATEGORY, categoryMap.get("key"));
            }
        });
        if (!map.containsKey(CATEGORY)) {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，类型为【" + category
                    + "】在质量检测类型配置不存在，请检查！！！");
        }
    }


    /**
     * 获取工单类型
     *
     * @param map                     带保存的信息列表
     * @param workSheetCategoryConfig 工单类型Json
     * @param workSheetCategory       excel工单类型
     * @param count                   excel行数
     */
    private void verifyImportExcelWorkSheetCategoryConfig(Map<String, Object> map, String workSheetCategoryConfig, String workSheetCategory, AtomicInteger count) {
        if (StringUtils.isBlank(workSheetCategoryConfig)) {
            throw new ResponseException(ERROR, "工单类型配置不存在");
        }
        List<Map<String, String>> workSheetCategoryList = JSON.parseObject(workSheetCategoryConfig, new TypeReference<List<Map<String, String>>>() {
        });
        workSheetCategoryList.forEach(workSheetCategoryMap -> {
            String value = workSheetCategoryMap.get(VALUE);
            if (Objects.equals(value, workSheetCategory)) {
                map.put(WORK_SHEET_CATEGORY, workSheetCategoryMap.get("key"));
            }
        });
        if (!map.containsKey(WORK_SHEET_CATEGORY)) {
            throw new ResponseException(ERROR, "第【" + count.get() + "】行出现问题，工单类型为【" + workSheetCategory
                    + "】在工单类型配置不存在，请检查！！！");
        }
    }

    /**
     * 定时任务调用此方法，当方案到达有效期之后更新是否启用状态为禁用
     */
    public void upDateIsEnable() {
        pedigreeStepCheckRuleRepository.upDateIsEnableByExpiryDateAndIsEnableAndDeleted(Boolean.FALSE, LocalDate.now(), Boolean.TRUE, Constants.LONG_ZERO);
    }

    /**
     * 产品谱系工序检测判定标准数据导出
     */
    public void exportTableExcel(List<PedigreeStepCheckRule> pedigreeStepCheckRuleList, ExportDTO exportDTO, HttpServletResponse response) throws IOException {
        List<PedigreeStepCheckRuleImportDTO> pedigreeStepCheckRuleImportList = Lists.newArrayList();
        // 获取质量检测类型
        String qualityCheckCategory = Optional.ofNullable(commonService.getDictionaryData(Constants.KEY_QUALITY_CHECK_CATEGORY))
                .orElseThrow(() -> new ResponseException(ERROR, "质量检测类型配置不存在"));
        List<Map<String, String>> qualityCheckCategoryList = JSON.parseObject(qualityCheckCategory, new TypeReference<List<Map<String, String>>>() {
        });

        // 获取工单的类型
        String workSheetCategoryConfig = Optional.ofNullable(commonService.getDictionaryData(Constants.WORK_SHEET_CATEGORY))
                .orElseThrow(() -> new ResponseException(ERROR, "工单类型配置不存在"));
        List<Map<String, String>> workSheetCategoryList = JSON.parseObject(workSheetCategoryConfig, new TypeReference<List<Map<String, String>>>() {
        });

        pedigreeStepCheckRuleList.forEach(pedigreeStepCheckRule -> {
            PedigreeStepCheckRuleImportDTO pedigreeStepCheckRuleImportDto = new PedigreeStepCheckRuleImportDTO(pedigreeStepCheckRule);
            // 检测类型
            qualityCheckCategoryList.forEach(qualityCheckCategoryMap -> Optional.ofNullable(qualityCheckCategoryMap.get("key")).ifPresent(key -> {
                if (Integer.parseInt(key) == pedigreeStepCheckRule.getCategory()) {
                    pedigreeStepCheckRuleImportDto.setCategory(qualityCheckCategoryMap.get(VALUE));
                }
            }));
            // 工单类型
            if (pedigreeStepCheckRule.getWorkSheetCategory() != null) {
                workSheetCategoryList.forEach(workSheetCategoryMap -> Optional.ofNullable(workSheetCategoryMap.get("key")).ifPresent(key -> {
                    if (Integer.parseInt(key) == pedigreeStepCheckRule.getWorkSheetCategory()) {
                        pedigreeStepCheckRuleImportDto.setWorkSheetCategory(workSheetCategoryMap.get(VALUE));
                    }
                }));
            }

            List<PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO> pedigreeStepCheckItemDtoList = Lists.newArrayList();
            List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
            pedigreeStepCheckItemList.forEach(pedigreeStepCheckItem -> {
                PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO pedigreeStepCheckItemDto = new PedigreeStepCheckRuleImportDTO.PedigreeStepCheckItemDTO();
                pedigreeStepCheckItemDto.setCheckItemCode(pedigreeStepCheckItem.getCheckItem().getCode()).setControl(pedigreeStepCheckItem.getControl()).setInspectNumberCase(Objects.isNull(pedigreeStepCheckItem.getInspectNumberCase()) ? Constants.INT_ZERO :pedigreeStepCheckItem.getInspectNumberCase());
                pedigreeStepCheckItemDtoList.add(pedigreeStepCheckItemDto);
            });
            pedigreeStepCheckRuleImportDto.setPedigreeStepCheckItemCodeList(pedigreeStepCheckItemDtoList);
            pedigreeStepCheckRuleImportList.add(pedigreeStepCheckRuleImportDto);
        });
        // 写入数据
        ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
        }
        String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PedigreeStepCheckRuleImportDTO.class, pedigreeStepCheckRuleImportList);
        String fileName = URLEncoder.encode(exportDTO.getExcelTitle() + prefix, StandardCharsets.UTF_8);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.close();
    }

    /**
     * 初始化数据
     */
    public void init() {
        //固定数量 0
        List<PedigreeStepCheckRule> numberPedigreeStepCheckRules = pedigreeStepCheckRuleRepository.findByJudgeWayAndDeleted(0, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(numberPedigreeStepCheckRules)) {
            numberPedigreeStepCheckRules.stream().filter(pedigreeStepCheckRule -> !ObjectUtils.isEmpty(pedigreeStepCheckRule.getJudgeWay()) && !ObjectUtils.isEmpty(pedigreeStepCheckRule.getBaseNumber()) && !ObjectUtils.isEmpty(pedigreeStepCheckRule.getQualifiedNumber()))
                    .collect(Collectors.groupingBy(pedigreeStepCheckRule -> pedigreeStepCheckRule.getJudgeWay() + "" + pedigreeStepCheckRule.getBaseNumber() + "" + pedigreeStepCheckRule.getQualifiedNumber()))
                    .forEach((key, pedigreeStepCheckRules) -> {
                        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRules.get(0);
                        List<SampleCaseDTO> sampleCases = sampleCaseRepository.findByCategoryAndNumberAndAcAndDeleted(1
                                , pedigreeStepCheckRule.getBaseNumber(), pedigreeStepCheckRule.getBaseNumber() - pedigreeStepCheckRule.getQualifiedNumber(), Constants.LONG_ZERO);
                        SampleCaseDTO sampleCase;
                        if (ValidateUtils.isValid(sampleCases)) {
                            sampleCase = sampleCases.get(Constants.INT_ZERO);
                        } else {
                            sampleCase = new SampleCaseDTO();
                            sampleCase.setCategory(1).setNumber(pedigreeStepCheckRule.getBaseNumber()).setAc(pedigreeStepCheckRule.getBaseNumber() - pedigreeStepCheckRule.getQualifiedNumber()).setCode("1" + "-" + pedigreeStepCheckRule.getBaseNumber() + "-" + (pedigreeStepCheckRule.getBaseNumber() - pedigreeStepCheckRule.getQualifiedNumber()))
                                    .setName("固定数量" + "-" + pedigreeStepCheckRule.getBaseNumber() + "-" + (pedigreeStepCheckRule.getBaseNumber() - pedigreeStepCheckRule.getQualifiedNumber())).setExpiryDate(LocalDate.now().plusYears(10)).setEnable(Boolean.TRUE).setDeleted(Constants.LONG_ZERO);
                            sampleCase = sampleCaseRepository.save(sampleCase);
                        }
                        SampleCaseDTO finalSampleCase = sampleCase;
                        pedigreeStepCheckRules = pedigreeStepCheckRules.stream().map(pedigreeStepCheckRule1 -> pedigreeStepCheckRule1.setSampleCase(finalSampleCase)).collect(Collectors.toList());
                        pedigreeStepCheckRuleRepository.saveAll(pedigreeStepCheckRules);
                    });
        }
        //固定比例 1
        List<PedigreeStepCheckRule> ratePedigreeStepCheckRules = pedigreeStepCheckRuleRepository.findByJudgeWayAndDeleted(1, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(ratePedigreeStepCheckRules)) {
            ratePedigreeStepCheckRules.stream().filter(pedigreeStepCheckRule -> !ObjectUtils.isEmpty(pedigreeStepCheckRule.getJudgeWay()) && !ObjectUtils.isEmpty(pedigreeStepCheckRule.getRate()) && !ObjectUtils.isEmpty(pedigreeStepCheckRule.getQualifiedRate()))
                    .collect(Collectors.groupingBy(pedigreeStepCheckRule -> pedigreeStepCheckRule.getJudgeWay() + "" + pedigreeStepCheckRule.getRate() + "" + pedigreeStepCheckRule.getQualifiedRate()))
                    .forEach((key, pedigreeStepCheckRules) -> {
                        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRules.get(0);
                        List<SampleCaseDTO> sampleCases =
                                sampleCaseRepository.findByCategoryAndRateAndQualifiedRateAndDeleted(2, pedigreeStepCheckRule.getRate(), pedigreeStepCheckRule.getQualifiedRate(), Constants.LONG_ZERO);
                        SampleCaseDTO sampleCase;
                        if (ValidateUtils.isValid(sampleCases)) {
                            sampleCase = sampleCases.get(Constants.INT_ZERO);
                        } else {
                            sampleCase = new SampleCaseDTO();
                            sampleCase.setCategory(2).setRate(pedigreeStepCheckRule.getRate()).setQualifiedRate(pedigreeStepCheckRule.getQualifiedRate()).setCode("2" + "-" + pedigreeStepCheckRule.getRate() + "-" + pedigreeStepCheckRule.getQualifiedRate())
                                    .setName("百分比抽样" + "-" + pedigreeStepCheckRule.getRate() + "-" + pedigreeStepCheckRule.getQualifiedRate()).setExpiryDate(LocalDate.now().plusYears(10)).setEnable(Boolean.TRUE).setDeleted(Constants.LONG_ZERO);
                            sampleCase = sampleCaseRepository.save(sampleCase);
                        }
                        SampleCaseDTO finalSampleCase = sampleCase;
                        pedigreeStepCheckRules = pedigreeStepCheckRules.stream().map(pedigreeStepCheckRule1 -> pedigreeStepCheckRule1.setSampleCase(finalSampleCase)).collect(Collectors.toList());
                        pedigreeStepCheckRuleRepository.saveAll(pedigreeStepCheckRules);
                    });
        }

    }
}
