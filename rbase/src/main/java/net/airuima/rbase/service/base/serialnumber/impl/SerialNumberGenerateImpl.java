package net.airuima.rbase.service.base.serialnumber.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.rlms.ModuleConfigDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.util.ResponseException;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/17
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SerialNumberGenerateImpl implements ISerialNumberGenerate {


    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private WorkSheetRepository workSheetRepository;


    /**
     * 根据编码规则键生成编码规则请求参数
     * @param serialCode 编码规则键
     * <AUTHOR>
     * @date  2023/7/13
     * @return net.airuima.rbase.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    @Override
    public SerialNumberDTO getSerialNumber(String serialCode) {
        return ISerialNumberGenerate.super.getSerialNumber(serialCode);
    }


    /**
     * 根据工单生成编码
     * @param serialCode 编码规则键
     * @param id 工单主键ID
     * <AUTHOR>
     * @date  2022/5/17
     * @return net.airuima.rbase.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    @Override
    public SerialNumberDTO getSerialNumberByWorkSheet(String serialCode, Long id) {
        SerialNumberDTO serialNumberDto = ISerialNumberGenerate.super.getSerialNumberByWorkSheet(serialCode, id);
        ModuleConfigDTO moduleConfig = rbaseSerialNumberProxy.findModuleConfig(serialCode);
        if (null == id) {
            throw new ResponseException("error.WorkSheetNotFound", "关联工单id不存在");
        }
        //验证编码规则是否引用其他服务
        if (null != moduleConfig && (null != moduleConfig.getCategory() && ConstantsEnum.SERIALNUMBER_DYNAMIC_PREFIX.getCategoryName() == moduleConfig.getCategory()) && (ValidateUtils.isValid(moduleConfig.getModuleCode()))) {
            serialNumberDto.setId(id);
        }
        return serialNumberDto;
    }

    /**
     * 根据组织架构生成编码 （根据工单id获取组织架构id，这里可传入工单id 可方便后续插件扩展修改原逻辑，而不用在 代码引用层面修改）
     * @param serialCode 编码规则键
     * @param id 工单主键ID
     * <AUTHOR>
     * @date  2022/5/17
     * @return et.airuima.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    @Override
    public SerialNumberDTO getSerialNumberByOrganization(String serialCode, Long id) {
        SerialNumberDTO serialNumberDto = ISerialNumberGenerate.super.getSerialNumberByOrganization(serialCode, id);

        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            throw new ResponseException("error.WorkSheetNotFound", "关联工单id不存在");
        }
        Long organizationId = workSheetOptional.get().getOrganizationId();
        if (null == organizationId) {
            throw new ResponseException("error.OrganizationNotFound", "关联组织架构id不存在");

        }
        ModuleConfigDTO moduleConfig = rbaseSerialNumberProxy.findModuleConfig(serialCode);
        //验证编码规则是否引用其他服务
        if (null != moduleConfig && (null != moduleConfig.getCategory() && ConstantsEnum.SERIALNUMBER_DYNAMIC_PREFIX.getCategoryName() == moduleConfig.getCategory()) && (ValidateUtils.isValid(moduleConfig.getModuleCode()))) {
            serialNumberDto.setId(organizationId);
        }
        return serialNumberDto;
    }
}
