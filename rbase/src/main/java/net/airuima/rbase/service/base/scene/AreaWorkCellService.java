package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.AreaWorkCell;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.repository.base.scene.AreaWorkCellRepository;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.scene.dto.AreaWorkCellDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 区域工位Service
 *
 * <AUTHOR>
 * @date 2023/4/13 9:57
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class AreaWorkCellService extends CommonJpaService<AreaWorkCell> {

    private final String AREA_WORK_CELL_ENTITY_GRAPH = "areaWorkCellEntityGraph";
    private final AreaWorkCellRepository areaWorkCellRepository;

    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;

    @Autowired
    private WorkCellRepository workCellRepository;

    public AreaWorkCellService(AreaWorkCellRepository areaWorkCellRepository) {
        this.areaWorkCellRepository = areaWorkCellRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AreaWorkCell> find(Specification<AreaWorkCell> spec, Pageable pageable) {
        return areaWorkCellRepository.findAll(spec, pageable,new NamedEntityGraph(AREA_WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<AreaWorkCell> find(Specification<AreaWorkCell> spec) {
        return areaWorkCellRepository.findAll(spec,new NamedEntityGraph(AREA_WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AreaWorkCell> findAll(Pageable pageable) {
        return areaWorkCellRepository.findAll(pageable,new NamedEntityGraph(AREA_WORK_CELL_ENTITY_GRAPH));
    }

    /**
     * 区域新增工位，需要批量添加
     *
     * @param areaWorkCellDTO 区域DTO
     */
    public void bindWorkCells(AreaWorkCellDTO areaWorkCellDTO) {
        if (!ValidateUtils.isValid(areaWorkCellDTO.getWorkCellIds())) {
            throw new ResponseException("error.workCellListNotExist", "不存在待保存的工位列表");
        }
        //如果找到则当前为编辑，需继承之前开关状态
        List<AreaWorkCell> areaWorkCellList = areaWorkCellRepository.findByAreaIdAndDeleted(areaWorkCellDTO.getAreaId(), Constants.LONG_ZERO);
        boolean isEnable = CollectionUtils.isEmpty(areaWorkCellList) ? Boolean.TRUE : areaWorkCellList.get(Constants.INT_ZERO).getIsEnable();
        // 先删除
        areaWorkCellRepository.deleteByAreaId(areaWorkCellDTO.getAreaId());
        // 验证需要添加的工位已经存在
        List<AreaWorkCell> areaWorkCells = areaWorkCellRepository.findByWorkCellIdInAndDeleted(areaWorkCellDTO.getWorkCellIds(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(areaWorkCells)) {
            throw new ResponseException("error.workCellIsExist", "工位" + areaWorkCells.get(Constants.INT_ZERO).getWorkCell().getName() + "存在于"+areaWorkCells.get(Constants.INT_ZERO).getArea().getName()+"区域");
        }
        // 再添加
        organizationAreaRepository.findByIdAndDeleted(areaWorkCellDTO.getAreaId(), Constants.LONG_ZERO).ifPresent(area -> {
            List<WorkCell> workCellList = workCellRepository.findAllById(areaWorkCellDTO.getWorkCellIds());
            List<AreaWorkCell> awcList = workCellList.stream().map(workCell -> {
                AreaWorkCell areaWorkCell = new AreaWorkCell();
                areaWorkCell.setArea(area).setWorkCell(workCell).setIsEnable(isEnable);
                return areaWorkCell;
            }).collect(Collectors.toList());
            areaWorkCellRepository.saveAll(awcList);
        });
    }

    /**
     * 启用/禁用指定区域工位
     *
     * @param areaId 区域ID
     **/
    public void enableByAreaId(Long areaId) {
        List<AreaWorkCell> areaWorkCellList = areaWorkCellRepository.findByAreaIdAndDeleted(areaId, Constants.LONG_ZERO);
        areaWorkCellList.forEach(i -> i.setIsEnable(!i.getIsEnable()));
        areaWorkCellRepository.saveAll(areaWorkCellList);
    }

    /**
     * 查询该区域绑定的工位信息
     *
     * @param areaId 区域id
     * @param size   条数
     * @return 区域工位列表
     */
    public List<WorkCell> findByAreaId(Long areaId, String keyword, Integer size) {
        if (StringUtils.isNotBlank(keyword)) {
            return Optional.ofNullable(areaWorkCellRepository.findByAreaIdAndKeyword(areaId, keyword, PageRequest.of(Constants.INT_ZERO, size)))
                    .map(Slice::getContent).orElse(Lists.newArrayList());
        }
        return Optional.ofNullable(areaWorkCellRepository.findByAreaId(areaId, PageRequest.of(Constants.INT_ZERO, size)))
                .map(Slice::getContent).orElse(Lists.newArrayList());
    }

    /**
     * 根据区域去重
     *
     * @param areaWorkCellList 区域工位列表
     * @return 根据区域去重后区域工位列表(一对多)
     */
    public List<AreaWorkCell> getUniqueAreaWorkCells(List<AreaWorkCell> areaWorkCellList) {
        // 区域对应工位列表
        Map<OrganizationArea, Set<WorkCell>> areaWorkCellsMap = areaWorkCellList.stream().collect(
                Collectors.groupingBy(AreaWorkCell::getArea, Collectors.mapping(AreaWorkCell::getWorkCell, Collectors.toSet())));
        // 根据区域去重
        areaWorkCellList = areaWorkCellList.stream().filter(distinctByKey(areaWorkCell -> areaWorkCell.getArea().getId()))
                .sorted(Comparator.comparing(AreaWorkCell::getId).reversed()).collect(Collectors.toList());
        areaWorkCellList.forEach(awc -> awc.setWorkCellSet(areaWorkCellsMap.get(awc.getArea())));
        return areaWorkCellList;
    }

    /**
     * 手动分页 区域工位
     */
    public List<AreaWorkCell> getAreaWorkCellList(List<AreaWorkCell> areaWorkCellList, HttpServletRequest request) {
        int pageSize = request.getParameter("size") != null ? Integer.parseInt(request.getParameter("size")) : Constants.INT_FIVE;
        int currentPage = request.getParameter("page") != null ? Integer.parseInt(request.getParameter("page")) : Constants.INT_ZERO;
        List<AreaWorkCell> areaWorkCells = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < areaWorkCellList.size(); index++) {
            areaWorkCells.add(areaWorkCellList.get(index));
        }
        return areaWorkCells;
    }

    /**
     * 根据对象属性去重
     * <pre>{@code
     *     List<Material> materialList = materialList.stream()
     *                      .filter(CommonUtils.distinctByKey(Material::getId))
     *                      .collect(Collectors.toList());
     * }</pre>
     *
     * @param keyExtractor 对象属性
     * @param <T>          Object
     * @return Object
     */
    private <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
