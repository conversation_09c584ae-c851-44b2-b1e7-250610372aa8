package net.airuima.rbase.service.base.pedigree;

import net.airuima.config.annotation.InterfaceNote;
import net.airuima.config.annotation.MethodNote;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.pedigree.PedigreeConfigOptionDTO;
import net.airuima.rbase.dto.pedigree.PedigreeDTO;
import net.airuima.rbase.dto.pedigree.PedigreeDetailDTO;
import net.airuima.rbase.dto.sync.SyncMaterialDTO;
import net.airuima.rbase.dto.sync.SyncPedigreeDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.service.CommonJpaInterface;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeModelCloneDTO;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeTypeDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@InterfaceNote("产品谱系接口")
public interface PedigreeInterface extends CommonJpaInterface<Pedigree> {

    /**
     * 通过名称或编码和产品谱系类型和查询数量和是否启用查询产品谱系列表
     * @param text 名称或编码
     * @param type 产品谱系类型
     * @param size 查询数量
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @MethodNote(value = "通过名称或编码模糊查询产品谱系", params = {"名称或编码", "产品谱系类型", "查询数量", "是否启用"}, returnParam = "产品谱系集合")
    default List<Pedigree> findByNameOrCode(String text, Integer type, Integer size, Boolean isEnable) {
        return null;
    }

    /**
     * 通过产品谱系类型和是否启用查找产品谱系列表
     * @param type 产品谱系类型
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @MethodNote(value = "通过类型查找当前类型的产品谱系", params = {"产品谱系类型", "是否启用"}, returnParam = "产品谱系集合")
    default List<Pedigree> findByType(Integer type, Boolean isEnable) {
        return null;
    }

    /**
     * 新增产品谱系
     * @param pedigreeDto 产品谱系/产品谱系配置/物料DT
     */
    @MethodNote(value = "新增产品谱系", params = {"产品谱系/产品谱系配置/物料DTO"})
    default ResponseEntity<Void> createInstance(PedigreeDTO pedigreeDto) {
        return null;
    }

    /**
     * 修改产品谱系
     * @param pedigreeDto 产品谱系/产品谱系配置/物料DT
     */
    @MethodNote(value = "修改产品谱系", params = {"产品谱系/产品谱系配置/物料DTO"})
    default ResponseEntity<Void> updateInstance(PedigreeDTO pedigreeDto) {
        return null;
    }

    /**
     * 修改产品谱系
     * @param entity 产品谱系
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.pedigree.Pedigree>  产品谱系
     */
    @MethodNote(value = "修改产品谱系", params = {"产品谱系实体类"})
    default ResponseEntity<Pedigree> updateInstance(Pedigree entity) {
        return null;
    }

    /**
     * 获取产品谱系树形展示数据
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @MethodNote(value = "获取产品谱系树形展示数据", params = {"是否启用"}, returnParam = "产品谱系集合")
    default List<Pedigree> getTreeData(Boolean isEnable) {
        return null;
    }


    /**
     * 获取产品谱系工序所有配置项
     * @param clientId 客户主键Id
     * @param pedigreeId 产品谱系主键Id
     * @param workFlowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @return net.airuima.rbase.dto.pedigree.PedigreeConfigOptionDTO  产品谱系工序所有配置项
     */
    @MethodNote(value = "获取产品谱系工序所有配置项", params = {"客户主键Id","产品谱系主键Id", "工艺路线主键ID", "工序主键ID"}, returnParam = "产品谱系工序配置项信息集合")
    default PedigreeConfigOptionDTO findPedigreeConfigOption(Long clientId,Long pedigreeId, Long workFlowId, Long stepId) {
        return null;
    }

    /**
     * 导入产品谱系数据
     * @param list 数据
     * @param maxLevel 最高层级
     * @param productLevel 生产层级
     * @return java.util.List<java.util.Map<String, Object>>  结果数据
     */
    @MethodNote(value = "导入产品谱系数据", params = {"数据", "最高层级", "生产层级"}, returnParam = "处理后的数据")
    default List<Map<String, Object>> importPedigree(List<Map<String, Object>> list, Integer maxLevel, Integer productLevel) {
        return null;
    }

    /**
     * 同步物料数据  当物料为半成品和成品时需要更新产品谱系
     * @param syncMaterialDtoList 上传的物料参数集合
     * @return java.util.List<net.airuima.rbase.dto.sync.SyncResultDTO> 同步物料结果集合
     */
    @MethodNote(value = "同步物料数据", params = {"同步物料DTO集合"}, returnParam = "同步结果DTO集合")
    default List<SyncResultDTO> syncMaterial(List<SyncMaterialDTO> syncMaterialDtoList) {
        return null;
    }

    /**
     * 根据产品谱系ID查找产品谱系详情
     * @param pedigreeId 产品谱系主键ID
     * @param clientId 客户主键ID
     * @param category 工艺路线类型
     * @return net.airuima.rbase.dto.pedigree.PedigreeDetailDTO 产品谱系详情
     */
    @MethodNote(value = "根据产品谱系ID查找产品谱系详情", params = {"产品谱系主键ID","客户主键ID","工艺路线类型"}, returnParam = "产品谱系详情")
    default PedigreeDetailDTO findByPedigree(Long pedigreeId,Long clientId,Integer category) {
        return null;
    }

    /**
     * 同步产品谱系数据
     * @param pedigreeList 同步产品谱系信息列表
     * @return java.util.List<net.airuima.rbase.dto.sync.SyncResultDTO> 同步产品谱系结果列表
     */
    @MethodNote(value = "同步产品谱系数据", params = {"同步产品谱系DTO集合"}, returnParam = "同步结果DTO集合")
    default List<SyncResultDTO> syncPedigree(List<SyncPedigreeDTO> pedigreeList) {
        return null;
    }

    /**
     * 产品谱系导入
     *
     * @param file excel文件
     */
    default void importPedigreeExcel(MultipartFile file) {
    }

    /**
     * 解析产品谱系配置
     *
     * @param config 产品谱系配置
     * @return java.util.List<net.airuima.web.rest.base.pedigree.dto.PedigreeTypeDTO> 品谱系配置集合
     */
    default List<PedigreeTypeDTO> parsePedigreeConfig(String config) {
        return null;
    }

    /**
     * 校验产品谱系
     *
     * @param pedigree      产品谱系
     * @param rowNumber     行号
     * @param pedigreeTypes 产品谱系类型配置
     */
    default void validatePedigree(Pedigree pedigree, String rowNumber, List<PedigreeTypeDTO> pedigreeTypes) {
    }

    /**
     * 通过产品谱系编码和是否启用查询对应产品谱系
     *
     * @param code     产品谱系编码
     * @param isEnable 是否启用
     * @return net.airuima.rbase.domain.base.pedigree.Pedigree 产品谱系
     */
    @MethodNote(value = "通过编码和是否启用查询对应产品谱系", params = {"编码和是否启用"}, returnParam = "产品谱系")
    default Pedigree findByCode(String code, Boolean isEnable){
        return null;
    }


    /**
     * 克隆产品谱系模型
     * @param pedigreeModelCloneDto 产品谱系模型克隆参数
     * @return net.airuima.rbase.dto.base.BaseResultDTO<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系
     */
    @MethodNote(value = "克隆产品谱系模型")
    default BaseResultDTO<Pedigree> pedigreeModelClone(PedigreeModelCloneDTO pedigreeModelCloneDto){
        return new BaseResultDTO<>(Constants.OK,null);
    }
}
