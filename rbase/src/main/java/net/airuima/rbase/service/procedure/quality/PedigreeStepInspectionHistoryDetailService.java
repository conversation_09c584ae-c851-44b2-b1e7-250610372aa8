package net.airuima.rbase.service.procedure.quality;

import net.airuima.constant.Constants;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistory;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistoryDetail;
import net.airuima.rbase.repository.procedure.quality.PedigreeStepInspectionHistoryDetailRepository;
import net.airuima.rbase.repository.procedure.quality.PedigreeStepInspectionHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工序检查配置历史详情表
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepInspectionHistoryDetailService extends CommonJpaService<PedigreeStepInspectionHistoryDetail> {

    private final PedigreeStepInspectionHistoryDetailRepository pedigreeStepInspectionHistoryDetailRepository;

    @Autowired
    private PedigreeStepInspectionHistoryRepository pedigreeStepInspectionHistoryRepository;

    public PedigreeStepInspectionHistoryDetailService(PedigreeStepInspectionHistoryDetailRepository pedigreeStepInspectionHistoryDetailRepository) {
        this.pedigreeStepInspectionHistoryDetailRepository = pedigreeStepInspectionHistoryDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<PedigreeStepInspectionHistoryDetail> find(Specification<PedigreeStepInspectionHistoryDetail> spec, Pageable pageable) {
        return pedigreeStepInspectionHistoryDetailRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<PedigreeStepInspectionHistoryDetail> find(Specification<PedigreeStepInspectionHistoryDetail> spec) {
        return pedigreeStepInspectionHistoryDetailRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<PedigreeStepInspectionHistoryDetail> findAll(Pageable pageable) {
        return pedigreeStepInspectionHistoryDetailRepository.findAll(pageable);
    }

    /**
     * 通过工序检查配置历史id获取检查详情
     *
     * @param historyId 检查历史id'
     * @return List<PedigreeStepInspectionHistoryDetail>
     * <AUTHOR>
     * @since 1.8.1
     */
    public List<PedigreeStepInspectionHistoryDetail> getHistoriesDetailId(Long historyId) {

        return pedigreeStepInspectionHistoryDetailRepository.findByPedigreeStepInspectionHistoryIdAndDeleted(historyId, Constants.LONG_ZERO);
    }
}
