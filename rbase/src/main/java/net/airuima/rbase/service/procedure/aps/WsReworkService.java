package net.airuima.rbase.service.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.aps.WsReworkDetailDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.batch.ReworkGetSubWsDTO;
import net.airuima.rbase.dto.batch.ReworkSaveSubWsUnqualifiedGroupDTO;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rule.SerialNumberHistoryDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedGroupRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.single.SnReworkRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修单关联正常单Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsReworkService extends CommonJpaService<WsRework> {
    private static final String WS_REWORK_ENTITY_GRAPH = "wsReworkEntityGraph";
    private final WsReworkRepository wsReworkRepository;
    private final WorkSheetRepository workSheetRepository;
    private final UnqualifiedGroupRepository unqualifiedGroupRepository;
    private final WorkFlowRepository workFlowRepository;
    private final WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    private final WorkFlowStepRepository workFlowStepRepository;
    private final WsMaterialRepository wsMaterialRepository;
    private final WsStepRepository wsStepRepository;
    private final StepRepository stepRepository;
    private final CommonService commonService;

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SnReworkRepository snReworkRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private ISerialNumberGenerate[] serialNumberGenerate;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;

    public WsReworkService(WsReworkRepository wsReworkRepository, WorkSheetRepository workSheetRepository,
                           UnqualifiedGroupRepository unqualifiedGroupRepository,
                           WorkFlowRepository workFlowRepository, WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository,
                           WorkFlowStepRepository workFlowStepRepository, WsMaterialRepository wsMaterialRepository, WsStepRepository wsStepRepository, CommonService commonService, StepRepository stepRepository) {
        this.wsReworkRepository = wsReworkRepository;
        this.workSheetRepository = workSheetRepository;
        this.unqualifiedGroupRepository = unqualifiedGroupRepository;
        this.workFlowRepository = workFlowRepository;
        this.wsStepUnqualifiedItemRepository = wsStepUnqualifiedItemRepository;
        this.workFlowStepRepository = workFlowStepRepository;
        this.wsMaterialRepository = wsMaterialRepository;
        this.wsStepRepository = wsStepRepository;
        this.commonService = commonService;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsRework> find(Specification<WsRework> spec, Pageable pageable) {
        return wsReworkRepository.findAll(spec, pageable, new NamedEntityGraph(WS_REWORK_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsRework> find(Specification<WsRework> spec) {
        return wsReworkRepository.findAll(spec, new NamedEntityGraph(WS_REWORK_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsRework> findAll(Pageable pageable) {
        return wsReworkRepository.findAll(pageable, new NamedEntityGraph(WS_REWORK_ENTITY_GRAPH));
    }

    /**
     * 根据原始总工单与不良组别信息生成在线返修单
     *
     * @param reworkSaveSubWsUnqualifiedGroupDto 请求参数
     * @return ResponseEntity<List < WorkSheet>>
     * <AUTHOR>
     * @date 2021-05-07
     **/
    public WorkSheetResDTO saveInstance(ReworkSaveSubWsUnqualifiedGroupDTO reworkSaveSubWsUnqualifiedGroupDto) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(reworkSaveSubWsUnqualifiedGroupDto.getWorkSheetId(), Constants.LONG_ZERO).orElse(null);
        if (null == workSheet) {
            return new WorkSheetResDTO(new BaseDTO(Constants.KO, "WorkSheetNotExist"));
        }
        //若当前扫描的总工单为在线返修单则需要查询获取原始的正常总工单
        if (workSheet.getCategory() == Constants.NEGATIVE_ONE) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null == wsRework) {
                return new WorkSheetResDTO(new BaseDTO(Constants.KO, "WsReworkNotExist"));
            }
            workSheet = wsRework.getOriginalWorkSheet();
        }
        //验证工艺路线里的工序是否都有配置属性
        this.validateStepConfig(workSheet,reworkSaveSubWsUnqualifiedGroupDto.getUnqualifiedGroupInfoDtoList().stream().map(ReworkSaveSubWsUnqualifiedGroupDTO.UnqualifiedGroupInfoDTO::getWorkFlowId).collect(Collectors.toList()), workSheet.getPedigree());
        List<WsMaterial> wsMaterials = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
        int planFinishDay = null != pedigreeConfig ? pedigreeConfig.getPlanFinishDay() == 0 ? Constants.INT_SEVEN : pedigreeConfig.getPlanFinishDay() : Constants.INT_SEVEN;
        List<WorkSheet> reWorkSheetList = Lists.newArrayList();
        List<WsRework> reworks = Lists.newArrayList();
        WorkSheet finalWorkSheet = workSheet;
        reworkSaveSubWsUnqualifiedGroupDto.getUnqualifiedGroupInfoDtoList().forEach(unqualifiedGroupInfoDto -> {
            SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumberByOrganization(Constants.KEY_SERIAL_NUMBER_ONLINE_REWORK_WORK_SHEET, finalWorkSheet.getId());
            WorkSheet reWorkSheet = new WorkSheet();
            //生成并保存在线返修单
            reWorkSheet.setCategory(Constants.NEGATIVE_ONE)
                    .setNumber(unqualifiedGroupInfoDto.getNumber())
                    .setBomInfoId(finalWorkSheet.getBomInfoId())
                    .setOrganizationId(finalWorkSheet.getOrganizationId())
                    .setWorkFlow(workFlowRepository.getReferenceById(unqualifiedGroupInfoDto.getWorkFlowId()))
                    .setWorkLine(finalWorkSheet.getWorkLine())
                    .setClientId(finalWorkSheet.getClientId())
                    .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto))
                    .setPedigree(finalWorkSheet.getPedigree())
                    .setPlanStartDate(LocalDateTime.now())
                    .setPlanEndDate(LocalDateTime.now().plusDays(planFinishDay))
                    .setDeleted(Constants.LONG_ZERO);
            workSheetRepository.save(reWorkSheet);
            //保存流水号历史明细
            rbaseSerialNumberProxy.createInstance(new SerialNumberHistoryDTO(Constants.KEY_SERIAL_NUMBER_ONLINE_REWORK_WORK_SHEET, LocalDate.now(), reWorkSheet.getSerialNumber(), reWorkSheet.getOrganizationId().toString()));
            //保存在线返修单的投料单
            this.saveReworkSheetMaterial(reWorkSheet, finalWorkSheet, wsMaterials);
            //保存在线返修单工序快照
            this.saveReworkSheetStep(reWorkSheet, unqualifiedGroupInfoDto.getWorkFlowId());
            reWorkSheetList.add(reWorkSheet);
            //保存在线返修单与原始正常工单的关联关系
            WsRework wsRework = new WsRework();
            wsRework.setReworkWorkSheet(reWorkSheet)
                    .setOriginalWorkSheet(finalWorkSheet)
                    .setUnqualifiedGroup(null != unqualifiedGroupInfoDto.getUnqualifiedGroupId() ? unqualifiedGroupRepository.getReferenceById(unqualifiedGroupInfoDto.getUnqualifiedGroupId()) : null)
                    .setStep(null != unqualifiedGroupInfoDto.getStepId() ? stepRepository.findByIdAndDeleted(unqualifiedGroupInfoDto.getStepId(), Constants.LONG_ZERO).orElse(null) : null)
                    .setDeleted(Constants.LONG_ZERO);
            List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByIdInAndDeleted(unqualifiedGroupInfoDto.getSubWorkSheetStepDTOList().stream().map(ReworkSaveSubWsUnqualifiedGroupDTO.UnqualifiedGroupInfoDTO.subWorkSheetStepDTO::getSubWorkSheetId).collect(Collectors.toList()), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(subWorkSheets)) {
                wsRework.setOrgSubWorkSheetList(subWorkSheets.stream().map(SubWorkSheet::getSerialNumber).toList().toString());
            }
            WsRework finalWsRework = wsReworkRepository.save(wsRework);
            reworks.add(finalWsRework);
            //更新已生成在线返修单的不良项目状态，防止重复生成
            unqualifiedGroupInfoDto.getSubWorkSheetStepDTOList().forEach(subWorkSheetStepDto -> {
                subWorkSheetStepDto.getUnqualifiedItemStepDtoList().forEach(unqualifiedItemStepDto -> {
                    processUnqualifiedGroupInfo(subWorkSheetStepDto.getSubWorkSheetId(), unqualifiedItemStepDto);
                    List<SnUnqualifiedItem> snUnqualifiedItems = snUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheetStepDto.getSubWorkSheetId(), unqualifiedItemStepDto.getUnqualifiedStepId(), unqualifiedItemStepDto.getUnqualifiedItemId(), Constants.LONG_ZERO);
                    if (ValidateUtils.isValid(snUnqualifiedItems)) {
                        List<String> snList = snUnqualifiedItems.stream().map(SnUnqualifiedItem::getSn).collect(Collectors.toList());
                        List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(snList, Constants.LONG_ZERO);
                        if (ValidateUtils.isValid(snWorkStatusList)) {
                            List<SnRework> snReworkList = Lists.newArrayList();
                            snWorkStatusList = snWorkStatusList.stream().peek(snWorkStatus -> {
                                snWorkStatus.setStatus(Constants.INT_THREE);
                                SnRework snRework = new SnRework();
                                snRework.setWsRework(finalWsRework).setSnWorkStatus(snWorkStatus).setStatus(Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
                                snReworkList.add(snRework);
                            }).collect(Collectors.toList());
                            //修改sn状态
                            snWorkStatusRepository.saveAll(snWorkStatusList);
                            //添加sn与反修单的关系
                            snReworkRepository.saveAll(snReworkList);
                        }
                    }
                });
            });
        });
        return new WorkSheetResDTO(new BaseDTO(Constants.OK), reWorkSheetList, reworks);
    }

    /**
     * 返工记录返修状态
     *
     * @param subWorkSheetId         子工单
     * @param unqualifiedItemStepDto 不良详情工序信息
     */
    private void processUnqualifiedGroupInfo(Long subWorkSheetId, ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO unqualifiedItemStepDto) {
        //修改当前批不良详情记录
        wsStepUnqualifiedItemRepository.updateByFlag(subWorkSheetId, unqualifiedItemStepDto.getUnqualifiedStepId(), unqualifiedItemStepDto.getUnqualifiedItemId(), Constants.TRUE);

        List<ContainerDetail> containerDetails = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndDeleted(subWorkSheetId, unqualifiedItemStepDto.getUnqualifiedStepId(), Constants.LONG_ZERO);
        //容器 不良详
        if (ValidateUtils.isValid(containerDetails)) {
            containerDetails.forEach(containerDetail -> {
                Optional<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItemOptional = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItemStepDto.getUnqualifiedItemId(), Constants.LONG_ZERO);
                containerDetailUnqualifiedItemOptional.ifPresent(containerDetailUnqualifiedItem -> {
                    containerDetailUnqualifiedItem.setRepairCount(containerDetailUnqualifiedItem.getNumber()).setFlag(Constants.TRUE);
                    containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                });
            });
        }
    }

    /**
     * 验证工艺路线里的工序是否都有配置属性
     *
     * @param workFlowIdList 工艺路线ID列表
     * @param pedigree       产品谱系
     * @return HttpHeaders
     * <AUTHOR>
     * @date 2021-05-11
     **/
    @Transactional(readOnly = true)
    public void validateStepConfig(WorkSheet workSheet,List<Long> workFlowIdList, Pedigree pedigree) {
        for (Long workFlowId : workFlowIdList) {
            WorkFlow workFlow = workFlowRepository.findByIdAndDeleted(workFlowId, Constants.LONG_ZERO).orElse(null);
            if (null == workFlow) {
                throw new ResponseException("error.workFlowNotExist", "工艺路线不存在");
            }
            List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(workFlowIdList)) {
                throw new ResponseException("error.workFlowStepNotExist", "工艺路线工序不存在");
            }
            for (WorkFlowStep workFlowStep : workFlowStepList) {
                StepDTO stepDto = commonService.findPedigreeStepConfig(workSheet.getClientId(),pedigree, workFlow, workFlowStep.getStep());
                if (null == stepDto) {
                    throw new ResponseException("error.StepConfigNotExist", "工艺路线工序配置不存在");
                }
            }
        }
    }

    /**
     * 保存在线返修单工序快照
     *
     * @param reWorkSheet 在线返修单
     * @param workFlowId  工艺路线ID
     * <AUTHOR>
     * @date 2021-05-11
     **/
    @CachePut(cacheNames = {"wsStepQueryCache"},key = "#reWorkSheet.id")
    public List<WsStep> saveReworkSheetStep(WorkSheet reWorkSheet, Long workFlowId) {
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workFlowStepList)) {
            List<WsStep> wsSteps = Lists.newArrayList();
            workFlowStepList.forEach(workFlowStep -> {
                StepDTO stepDto = commonService.findPedigreeStepConfig(reWorkSheet.getClientId(),reWorkSheet.getPedigree(), workFlowStep.getWorkFlow(), workFlowStep.getStep());
                if (null == stepDto) {
                    throw new ResponseException("error.WorkFlowStepConfigNotExist", "工艺路线工序配置不存在");
                }
                WsStep wsStep = new WsStep();
                wsStep.setStep(workFlowStep.getStep())
                        .setAfterStepId(workFlowStep.getAfterStepId())
                        .setPreStepId(workFlowStep.getPreStepId())
                        .setCategory(workFlowStep.getStep().getCategory())
                        .setControlMode(stepDto.getControlMode())
                        .setIsBindContainer(stepDto.getIsBindContainer())
                        .setIsControlMaterial(stepDto.getIsControlMaterial())
                        .setRequestMode(stepDto.getRequestMode())
                        .setInputRate(stepDto.getInputRate())
                        .setWorkSheet(reWorkSheet)
                        .setWorkFlow(reWorkSheet.getWorkFlow());
                wsSteps.add(wsStep);
            });
            //获取系统配置的投产粒度(子工单或者工单),只有投产粒度为子工单时才会进行分单
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            //工单投产粒度时需要更新工单的工序个数
            if(!subWsProductionMode && !CollectionUtils.isEmpty(wsSteps)){
                reWorkSheet.setStepNumber(wsSteps.size());
                workSheetRepository.save(reWorkSheet);
            }
            wsStepRepository.deleteByWorkSheetId(reWorkSheet.getId());
            return wsStepRepository.saveAll(wsSteps);
        } else {
            throw new ResponseException("error.WorkFlowStepNotExist", "工艺路线工序不存在");
        }
    }

    /**
     * @param reWorkSheet     在线返修单
     * @param originWorkSheet 原始正常单
     * @param wsMaterials     原始正常单投料单
     * <AUTHOR>
     * @date 2021-05-11
     **/
    @FetchMethod
    @CachePut(cacheNames = {"wsMaterialQueryCache"},key = "#reWorkSheet.id")
    public List<WsMaterial> saveReworkSheetMaterial(WorkSheet reWorkSheet, WorkSheet originWorkSheet, List<WsMaterial> wsMaterials) {
        List<WsMaterial> reWsMaterialList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wsMaterials) || null == originWorkSheet.getBomInfoId()) {
            return reWsMaterialList;
        }
        List<BomDTO> bomDTOList = rbaseBomProxy.findByBomInfoId(originWorkSheet.getBomInfoId());
        if (CollectionUtils.isEmpty(bomDTOList)) {
            return reWsMaterialList;
        }
        wsMaterials.forEach(originWsMaterial -> {
            WsMaterial wsMaterial = new WsMaterial();
            wsMaterial.setMaterialId(originWsMaterial.getMaterialId())
                    .setOriginMaterialId(originWsMaterial.getOriginMaterialId())
                    .setNumber(reWorkSheet.getNumber() * (originWsMaterial.getNumber() / originWorkSheet.getNumber()))
                    .setWorkSheet(reWorkSheet);
            //获取BOM的用量分子、用量分母以及损耗率进行更新返工单的投料单
            bomDTOList.stream().filter(bomDto -> originWsMaterial.getOriginMaterialId().equals(bomDto.getChildMaterial().getId())).findFirst().ifPresent(bomDTO -> {
                //首先判断是否为BOM的原始物料，否则再去判断是否为BOM的替代料
                if (bomDTO.getChildMaterial().getId().equals(originWsMaterial.getMaterialId())) {
                    //原始物料用料数量=工单数量*用量分子/用量分母
                    double inputNumber = bomDTO.getCategory() == net.airuima.constant.Constants.INT_ONE?NumberUtils.divide(bomDTO.getProportion(),bomDTO.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue():NumberUtils.divide(NumberUtils.multiply(reWorkSheet.getNumber(),bomDTO.getProportion()).doubleValue(),bomDTO.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue();
                    wsMaterial.setNumber(inputNumber + inputNumber * bomDTO.getWastage());
                } else if (!CollectionUtils.isEmpty(bomDTO.getBomMaterialReplaceDtoList())) {
                    bomDTO.getBomMaterialReplaceDtoList().stream().filter(bomMaterialReplaceDTO -> bomMaterialReplaceDTO.getReplaceMaterial().getId().equals(originWsMaterial.getMaterialId())).findFirst().ifPresent(bomMaterialReplaceDTO -> {
                        //替换物料用料数量=工单数量*用量分子/用量分母
                        double replaceInputNumber =  bomMaterialReplaceDTO.getCategory() == net.airuima.constant.Constants.INT_ONE?NumberUtils.divide(bomMaterialReplaceDTO.getProportion(),bomMaterialReplaceDTO.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue():NumberUtils.divide(NumberUtils.multiply(reWorkSheet.getNumber(),bomMaterialReplaceDTO.getProportion()).doubleValue(),bomMaterialReplaceDTO.getBase(), net.airuima.constant.Constants.INT_FIVE).doubleValue();
                        wsMaterial.setNumber(replaceInputNumber + replaceInputNumber * bomMaterialReplaceDTO.getWastage());
                    });
                }
            });
            wsMaterial.setDeleted(net.airuima.constant.Constants.LONG_ZERO);
            reWsMaterialList.add(wsMaterial);
        });
        if (net.airuima.util.ValidateUtils.isValid(reWsMaterialList)) {
            return wsMaterialRepository.saveAll(reWsMaterialList);
        }
        return reWsMaterialList;
    }

    /**
     * 通过返修工单ID查询不良明细
     *
     * @param reworkWorkSheetId 返修工单ID
     * @return : WsReworkDetailDTO
     * <AUTHOR>
     * @date 2023/1/12
     **/
    public WsReworkDetailDTO queryByReworkWorkSheetId(Long reworkWorkSheetId) {
        Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(reworkWorkSheetId, Constants.LONG_ZERO);
        if (wsReworkOptional.isPresent()) {
            WsReworkDetailDTO wsReworkDetailDTO = new WsReworkDetailDTO();
            wsReworkDetailDTO.setOriginalSerialNumber(wsReworkOptional.get().getOriginalWorkSheet().getSerialNumber());
            wsReworkDetailDTO.setOrgSubWorkSheetList(wsReworkOptional.get().getOrgSubWorkSheetList());
            if (wsReworkOptional.get().getStep() != null) {
                wsReworkDetailDTO.setStepName(wsReworkOptional.get().getStep().getName());
                wsReworkDetailDTO.setStepCode(wsReworkOptional.get().getStep().getCode());
            }
            if (wsReworkOptional.get().getUnqualifiedGroup() != null) {
                wsReworkDetailDTO.setUnqualifiedGroupName(wsReworkOptional.get().getUnqualifiedGroup().getName());
                wsReworkDetailDTO.setUnqualifiedGroupCode(wsReworkOptional.get().getUnqualifiedGroup().getCode());
            }
            return wsReworkDetailDTO;
        }
        return null;
    }
}
