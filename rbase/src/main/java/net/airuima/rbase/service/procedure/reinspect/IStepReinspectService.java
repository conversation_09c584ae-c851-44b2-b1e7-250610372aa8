package net.airuima.rbase.service.procedure.reinspect;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface IStepReinspectService {

    /**
     * 开返工单验证是否存复检，存在则提示先进行复检
     *
     * @param subWorkSheetId 子工单主键ID
     * @param workSheetId 工单主键ID
     * @param stepIds        工序主键ID列表
     */
    @FuncInterceptor(value = "QReinspection")
    default void validReWorkStepReinspect(Long subWorkSheetId,  Long workSheetId,List<Long> stepIds){

    }

    /**
     * 工单批次回退删除工序复检记录
     * @param batchWorkDetail 批量工序生产详情
     */
    @FuncInterceptor(value = "QReinspection")
    default void rollBackBatch(BatchWorkDetail batchWorkDetail){
    }

    /**
     * 容器回退删除工序复检记录
     * @param containerDetail 容器生产详情
     */
    @FuncInterceptor(value = "QReinspection")
    default void rollBackContainer(ContainerDetail containerDetail){

    }


    /**
     * 单支回退删除工序复检记录
     * @param snWorkDetail sn生产详情
     */
    @FuncInterceptor(value = "QReinspection")
    default void rollBackSn(SnWorkDetail snWorkDetail){
    }
}
