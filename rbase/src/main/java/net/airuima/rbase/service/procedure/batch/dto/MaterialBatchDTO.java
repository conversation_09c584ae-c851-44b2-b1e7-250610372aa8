package net.airuima.rbase.service.procedure.batch.dto;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 保存物料批次dto
 * <AUTHOR>
 * @date 2022/4/17
 */
public class MaterialBatchDTO {

    /**
     * 工单id
     */
    private Long wsId;

    /**
     * 工位id
     */
    private Long workCellId;

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 批次
     */
    private String batch;

    /**
     * sn
     */
    private String sn;

    /**
     * 退料数量
     */
    private Double number;

    /**
     * 扣料类型（1：工单，2：工位）
     */
    private Integer type;

    public MaterialBatchDTO() {
    }

    /**
     * 容器物料批次
     */
    public MaterialBatchDTO(long wsId,long workCellId,long materialId,String batch,Integer type,Double number) {
        this.wsId = wsId;
        this.workCellId = workCellId;
        this.materialId = materialId;
        this.batch = batch;
        this.number = number;
        this.type = type;
    }

    public Long getWsId() {
        return wsId;
    }

    public MaterialBatchDTO setWsId(Long wsId) {
        this.wsId = wsId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public MaterialBatchDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public MaterialBatchDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public MaterialBatchDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public MaterialBatchDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public MaterialBatchDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public MaterialBatchDTO setNumber(Double number) {
        this.number = number;
        return this;
    }
}
