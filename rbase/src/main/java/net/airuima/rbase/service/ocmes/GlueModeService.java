package net.airuima.rbase.service.ocmes;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 胶水相关模块插件
 * <AUTHOR>
 * @date 2022/12/22
 */
@FuncInterceptor(value = "Glue")
public interface GlueModeService {

    /**
     * 添加上料规则，标记胶水标签
     * @param materialRuleInfoList 上料规则
     * @param pedigreeStepMaterialRuleList 上料规则配置
     * @param putInNumber  投产数
     * @return java.util.List<net.airuima.dto.client.ClientGetStepInfoDTO.MaterialRuleInfo> 当前工序的上料规则
     */
    default List<ClientGetStepInfoDTO.MaterialRuleInfo> isGlueMaterial(List<ClientGetStepInfoDTO.MaterialRuleInfo> materialRuleInfoList, List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList, Integer putInNumber){
        return materialRuleInfoList;
    }
}
