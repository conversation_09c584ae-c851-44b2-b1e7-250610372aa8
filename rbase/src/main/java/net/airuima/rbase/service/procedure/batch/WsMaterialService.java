package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.batch.WsMaterialDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialExchangeDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单投料单Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsMaterialService extends CommonJpaService<WsMaterial> {
    private static final String WS_MATERIAL_ENTITY_GRAPH = "wsMaterialEntityGraph";
    private final WsMaterialRepository wsMaterialRepository;
    private final WorkSheetRepository workSheetRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;

    public WsMaterialService(WsMaterialRepository wsMaterialRepository, WorkSheetRepository workSheetRepository) {
        this.wsMaterialRepository = wsMaterialRepository;
        this.workSheetRepository = workSheetRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WsMaterial> find(Specification<WsMaterial> spec, Pageable pageable) {
        return wsMaterialRepository.findAll(spec, pageable,new NamedEntityGraph(WS_MATERIAL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WsMaterial> find(Specification<WsMaterial> spec) {
        return wsMaterialRepository.findAll(spec,new NamedEntityGraph(WS_MATERIAL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WsMaterial> findAll(Pageable pageable) {
        return wsMaterialRepository.findAll(pageable,new NamedEntityGraph(WS_MATERIAL_ENTITY_GRAPH));
    }

    /**
     * 根据总工单ID获取投料单
     *
     * @param workSheetId 总工单ID
     * @return
     */
    @Transactional(readOnly = true)
    public List<WsMaterial> findByWorkSheetId(Long workSheetId) {
        return wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
    }

    /**
     * 根据工单ID、分组获取投料单原始物料信息
     * @param workSheetId 工单ID
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsMaterial>
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Transactional(readOnly = true)
    public List<WsMaterial> distinctByWorkSheetId(Long workSheetId) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
        if(!workSheetOptional.isPresent()){
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()){
                workSheetId = subWorkSheetOptional.get().getWorkSheet().getId();
            }
        }
        workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
        if(!workSheetOptional.isPresent()){
            return Lists.newArrayList();
        }
        if(workSheetOptional.get().getCategory() == WsEnum.ONLINE_RE_WS.getCategory()){
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheetId,Constants.LONG_ZERO);
            workSheetId = wsReworkOptional.isPresent()?wsReworkOptional.get().getOriginalWorkSheet().getId():workSheetId;
        }
        return wsMaterialRepository.findByWorkSheetIdAndDeletedGroupByOriginMaterialId(workSheetId);
    }

    /**
     * 通过工单和原始物料ID获取投料单信息
     * @param workSheetId 工单id
     * @param originMaterialId 原始物料id
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsMaterial>
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Transactional(readOnly = true)
    public List<WsMaterial> findByWorkSheetIdAndOriginMaterialId(Long workSheetId, Long originMaterialId) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
        if(!workSheetOptional.isPresent()){
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()){
                workSheetId = subWorkSheetOptional.get().getWorkSheet().getId();
            }
        }
        workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId,Constants.LONG_ZERO);
        if(!workSheetOptional.isPresent()){
            return Lists.newArrayList();
        }
        if(workSheetOptional.get().getCategory() == WsEnum.ONLINE_RE_WS.getCategory()){
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheetId,Constants.LONG_ZERO);
            workSheetId = wsReworkOptional.isPresent()?wsReworkOptional.get().getOriginalWorkSheet().getId():workSheetId;
        }
        return wsMaterialRepository.findByWorkSheetIdAndOriginMaterialIdAndDeleted(workSheetId, originMaterialId, Constants.LONG_ZERO);
    }

    /**
     * 同步SAP上传的投料单替换料信息
     *
     * @param workSheetMaterialExchangeDtoList SAP上传的投料单替换料信息
     * @return List<SapBaseDTO>
     * <AUTHOR>
     * @date 2021-06-04
     **/
    public List<SyncResultDTO> workSheetMaterialExchangeSync(List<SyncWorkSheetMaterialExchangeDTO> workSheetMaterialExchangeDtoList) {
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (net.airuima.util.ValidateUtils.isValid(workSheetMaterialExchangeDtoList)) {
            //按照工单号进行分组
            Map<String,List<SyncWorkSheetMaterialExchangeDTO>> group = workSheetMaterialExchangeDtoList.stream().collect(Collectors.groupingBy(SyncWorkSheetMaterialExchangeDTO::getSerialNumber));
            group.forEach((serialNumber,syncWorkSheetMaterialExchangeDtoList)->{
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, net.airuima.constant.Constants.LONG_ZERO);
                if (workSheetOptional.isEmpty()) {
                    syncWorkSheetMaterialExchangeDtoList.forEach(syncWorkSheetMaterialExchangeDTO -> {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDTO.getId(), net.airuima.constant.Constants.INT_TWO, "工单记录不存在"));
                    });
                    return;
                }
                this.saveSyncWsMaterialInfo(workSheetOptional.get(),syncResultDtoList,syncWorkSheetMaterialExchangeDtoList);
            });
        }
        return syncResultDtoList;
    }

    /**
     * 保存同步的投料单替换料信息
     *
     * @param workSheet                        工单
     * @param syncResultDtoList                同步结果
     * @param workSheetMaterialExchangeDtoList 同步投料单替换料信息
     **/
    public void saveSyncWsMaterialInfo(WorkSheet workSheet,List<SyncResultDTO> syncResultDtoList,List<SyncWorkSheetMaterialExchangeDTO> workSheetMaterialExchangeDtoList){
        List<WsMaterial> wsMaterialList = Lists.newArrayList();
        workSheetMaterialExchangeDtoList.forEach(syncWorkSheetMaterialExchangeDto -> {

            MaterialDTO originMaterialDto = rbaseMaterialProxy.findByCodeAndDeleted(syncWorkSheetMaterialExchangeDto.getOriginMaterialCode(),Constants.LONG_ZERO).orElse(null);

            MaterialDTO replaceMaterialDto = rbaseMaterialProxy.findByCodeAndDeleted(syncWorkSheetMaterialExchangeDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);

            //新增&修改
            if(StringUtils.isNotBlank(syncWorkSheetMaterialExchangeDto.getMaterialCode())){
                if (null == replaceMaterialDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "替换物料不存在"));
                    return;
                }
                //新增投料单 物料
                Optional<WsMaterial> replaceWsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(), replaceMaterialDto.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (replaceWsMaterialOptional.isPresent()){
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "替换物料在投料单里已存在"));
                    return;
                }

                //修改：原始物料存在，则先删除物料 然后 ，在 添加 新的投料单 信息
                if (StringUtils.isNotBlank(syncWorkSheetMaterialExchangeDto.getOriginMaterialCode())){
                    if (null == originMaterialDto) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "原始物料不存在"));
                        return;
                    }
                    Optional<WsMaterial> originWsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(), originMaterialDto.getId(), net.airuima.constant.Constants.LONG_ZERO);
                    if (originWsMaterialOptional.isEmpty()){
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "原始物料在投料单里不存在"));
                        return;
                    }
                    //删除
                    WsMaterial originWsMaterial = originWsMaterialOptional.get();
                    originWsMaterial.setDeleted(originWsMaterial.getId());
                    wsMaterialList.add(originWsMaterial);
                }
                WsMaterial wsMaterial = new WsMaterial();
                wsMaterial.setMaterialId(replaceMaterialDto.getId())
                        .setOriginMaterialId(replaceMaterialDto.getId())
                        .setWorkSheet(workSheet)
                        .setNumber(ObjectUtils.isEmpty(syncWorkSheetMaterialExchangeDto.getNumber())?workSheet.getNumber():syncWorkSheetMaterialExchangeDto.getNumber())
                        .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                wsMaterialList.add(wsMaterial);
            }
            //删除
            if (StringUtils.isNotBlank(syncWorkSheetMaterialExchangeDto.getOriginMaterialCode()) && StringUtils.isBlank(syncWorkSheetMaterialExchangeDto.getMaterialCode())){
                if (null == originMaterialDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "原始物料不存在"));
                    return;
                }
                Optional<WsMaterial> originWsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(), originMaterialDto.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (originWsMaterialOptional.isEmpty()){
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_TWO, "原始物料在投料单里不存在"));
                    return;
                }
                //删除
                WsMaterial originWsMaterial = originWsMaterialOptional.get();
                originWsMaterial.setDeleted(originWsMaterial.getId());
                wsMaterialList.add(originWsMaterial);
            }
            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialExchangeDto.getId(), net.airuima.constant.Constants.INT_ONE, ""));
        });
        wsMaterialRepository.saveAll(wsMaterialList.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 新增投料单主物料或者替换料
     * <AUTHOR>
     * @param wsMaterialDto  投料单参数
     * @return ResponseEntity<WsMaterial>
     * @date 2021-07-22
     **/
    public ResponseEntity<WsMaterial> saveInstance(WsMaterialDTO wsMaterialDto){
        if(null == wsMaterialDto.getWorkSheetId()){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "notExistWsMaterialWorkSheetId", "参数工单ID不能为空")).build();
        }
        if(null == wsMaterialDto.getMaterialInfo() || null == wsMaterialDto.getMaterialInfo().getId()){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "notExistWsMaterialInfo", "参数主物料ID不能为空")).build();
        }
        Optional<WsMaterial> wsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndOriginMaterialIdAndDeleted(wsMaterialDto.getWorkSheetId(),wsMaterialDto.getMaterialInfo().getId(),wsMaterialDto.getMaterialInfo().getId(),Constants.LONG_ZERO);
        if(wsMaterialOptional.isPresent() && !ValidateUtils.isValid(wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList())){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "existWsMaterialInfo", "当前物料在投料单中已存在")).build();
        }
        WorkSheet workSheet = workSheetRepository.getReferenceById(wsMaterialDto.getWorkSheetId());
        //如果主物料在投料单中不存在则直接更新主物料以及对应的替换料
        if(wsMaterialOptional.isEmpty()){
            WsMaterial wsMaterial = this.saveMaterialInfo(workSheet,wsMaterialDto.getMaterialInfo().getId(),wsMaterialDto.getMaterialInfo().getId(),wsMaterialDto.getMaterialInfo().getNumber(),wsMaterialDto.getMaterialInfo().getBackFlush());
            if(ValidateUtils.isValid(wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList())) {
                wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList().forEach(replaceMaterialInfo -> {
                    this.saveMaterialInfo(workSheet, wsMaterial.getOriginMaterialId(), replaceMaterialInfo.getId(), replaceMaterialInfo.getNumber(),wsMaterial.getBackFlush());
                });
            }
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()),"")).build();
        }
        wsMaterialOptional.ifPresent(wsMaterial -> wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList().forEach(replaceMaterialInfo -> {
            this.saveMaterialInfo(workSheet, wsMaterial.getOriginMaterialId(), replaceMaterialInfo.getId(), replaceMaterialInfo.getNumber(),wsMaterial.getBackFlush());
        }));

        return ResponseEntity.ok().headers(HeaderUtil.createdAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()),"")).build();
    }

    /**
     * 修改投料单主物料或者替换料
     * <AUTHOR>
     * @param wsMaterialDto     投料单参数
     * @return ResponseEntity<WsMaterial>
     * @date 2021-07-22
     **/
    public ResponseEntity<WsMaterial> updateInstance(WsMaterialDTO wsMaterialDto){
        if(null == wsMaterialDto.getWorkSheetId()){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "notExistWsMaterialWorkSheetId", "参数工单ID不能为空")).build();
        }
        if(null == wsMaterialDto.getMaterialInfo() || null == wsMaterialDto.getMaterialInfo().getId()){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "notExistWsMaterialInfo", "参数主物料ID不能为空")).build();
        }
        if(!ValidateUtils.isValid(wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList()) && null == wsMaterialDto.getMaterialInfo().getWsMaterialId()){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "notExistWsMaterialId", "参数主物料投料单记录ID不能为空")).build();
        }
        //更新主料信息的同时需要更新各个替换料的原始物料
        if(!ValidateUtils.isValid(wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList())){
            WsMaterial wsMaterial = wsMaterialRepository.getReferenceById(wsMaterialDto.getMaterialInfo().getWsMaterialId());
            Optional<WsMaterial> wsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndOriginMaterialIdAndDeleted(wsMaterial.getWorkSheet().getId(),wsMaterialDto.getMaterialInfo().getId(),wsMaterialDto.getMaterialInfo().getId(),Constants.LONG_ZERO);
            if(wsMaterialOptional.isPresent() && !wsMaterialOptional.get().getId().equals(wsMaterialDto.getMaterialInfo().getWsMaterialId())){
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), "existWsMaterialInfo", "当前物料在投料单中已存在")).build();
            }
            List<WsMaterial> replaceWsMaterialList = wsMaterialRepository.findByWorkSheetIdAndOriginMaterialIdAndDeleted(wsMaterial.getWorkSheet().getId(),wsMaterial.getOriginMaterialId(),Constants.LONG_ZERO);
            wsMaterial.setBackFlush(Optional.ofNullable(wsMaterialDto.getMaterialInfo().getBackFlush()).orElse(Boolean.FALSE)).setMaterialId(wsMaterialDto.getMaterialInfo().getId()).setOriginMaterialId(wsMaterialDto.getMaterialInfo().getId()).setNumber(wsMaterialDto.getMaterialInfo().getNumber()).setDeleted(Constants.LONG_ZERO);
            wsMaterialRepository.save(wsMaterial);
            if(ValidateUtils.isValid(replaceWsMaterialList)){
                replaceWsMaterialList.forEach(replaceWsMaterial-> replaceWsMaterial.setOriginMaterialId(wsMaterial.getOriginMaterialId()).setNumber(wsMaterialDto.getMaterialInfo().getNumber()).setBackFlush(wsMaterial.getBackFlush()));
            }
            wsMaterialRepository.saveAll(replaceWsMaterialList);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()),wsMaterialDto.getMaterialInfo().getWsMaterialId().toString())).build();
        }
        //更新替换料
        wsMaterialDto.getMaterialInfo().getReplaceMaterialInfoList().forEach(replaceMaterialInfo -> {
            WsMaterial replaceWsMaterial = wsMaterialRepository.getReferenceById(replaceMaterialInfo.getWsMaterialId());
            replaceWsMaterial.setMaterialId(replaceMaterialInfo.getId()).setBackFlush(replaceWsMaterial.getBackFlush());
            wsMaterialRepository.save(replaceWsMaterial);
        });
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()),"")).build();
    }

    /**
     * 删除投料单记录
     * <AUTHOR>
     * @param id   投料单记录ID
     * @return ResponseEntity<Void>
     **/
    public ResponseEntity<Void> deleteInstance(Long id){
        WsMaterial wsMaterial = wsMaterialRepository.getReferenceById(id);
        this.deleteIncludeReplaceWsMaterial(wsMaterial);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(WsMaterial.class.getSimpleName()), id.toString())).build();
    }

    public void deleteIncludeReplaceWsMaterial(WsMaterial wsMaterial){
        wsMaterial.setDeleted(wsMaterial.getId());
        wsMaterialRepository.save(wsMaterial);
        //若果删除的为主物料则需要将对应的替换料一并删除
        if(wsMaterial.getMaterialId().equals(wsMaterial.getOriginMaterialId())){
            List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndOriginMaterialIdAndDeleted(wsMaterial.getWorkSheet().getId(),wsMaterial.getOriginMaterialId(), net.airuima.constant.Constants.LONG_ZERO);
            if(net.airuima.util.ValidateUtils.isValid(wsMaterialList)) {
                wsMaterialList.forEach(replaceWsMaterial -> replaceWsMaterial.setDeleted(replaceWsMaterial.getId()));
                wsMaterialRepository.saveAll(wsMaterialList);
            }
        }
    }


    /**
     * 通用保存新投料信息
     * <AUTHOR>
     * @param workSheet 总工单
     * @param originMaterialId 原始物料ID
     * @param materialId 投料ID
     * @param number     数量
     * @return WsMaterial 工单物料
     * @date 2021-07-22
     **/
    public WsMaterial saveMaterialInfo(WorkSheet workSheet,Long originMaterialId,Long materialId,Double number,Boolean backFlush){
        WsMaterial wsMaterial = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndOriginMaterialIdAndDeleted(workSheet.getId(),materialId,originMaterialId,Constants.LONG_ZERO).orElse(new WsMaterial());
        wsMaterial.setMaterialId(materialId)
                .setOriginMaterialId(originMaterialId)
                .setWorkSheet(workSheet)
                .setNumber(number)
                .setBackFlush(Optional.ofNullable(backFlush).orElse(Boolean.FALSE))
                .setDeleted(Constants.LONG_ZERO);
        wsMaterialRepository.save(wsMaterial);
        return wsMaterial;
    }
}
