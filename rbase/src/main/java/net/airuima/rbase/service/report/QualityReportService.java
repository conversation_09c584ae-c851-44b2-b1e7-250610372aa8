package net.airuima.rbase.service.report;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.dto.report.QualityReportDTO;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.util.ResponseContent;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 质量报表Service
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class QualityReportService {
    private static final String CREATED_DATE = "createdDate";
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;

    /**
     * 工单良率报表
     *
     * @param requestInfo 请求参数
     * @return
     */
    @Transactional(readOnly = true)
    public ResponseEntity<ResponseContent<QualityReportDTO.ResponseInfo>> workSheetQualityReport(QualityReportDTO.RequestInfo requestInfo) {
        Specification<WorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //总工单或子工单筛选
            if (ValidateUtils.isValid(requestInfo.getWsSerialNumber())) {
                Predicate wsPredicate = criteriaBuilder.equal(root.get("serialNumber"), requestInfo.getWsSerialNumber());
                predicateList.add(wsPredicate);
            } else {
                //开始时间和结束时间筛选
                if (requestInfo.getStartDate() != null || requestInfo.getEndDate() != null) {
                    //只有存在时间筛选条件时才能进行产品谱系筛选
                    if (requestInfo.getPedigreeId() != null) {
                        Predicate pedigreePredicate = criteriaBuilder.equal(root.get("pedigree").get("id"), requestInfo.getPedigreeId());
                        predicateList.add(pedigreePredicate);
                    }
                    if (requestInfo.getEndDate() != null) {
                        predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                                requestInfo.getEndDate().atZone(ZoneId.systemDefault()).toInstant()));
                    }
                    if (requestInfo.getStartDate() != null ) {
                        predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                                requestInfo.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
                    }
                }
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        Page<WorkSheet> workSheetPage = workSheetRepository.findAll(specification, PageRequest.of(requestInfo.getCurrentPage(), requestInfo.getPageSize()),new NamedEntityGraph("workSheetEntityGraph"));
        List<WorkSheet> workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(null);
        QualityReportDTO.ResponseInfo responseInfo = new QualityReportDTO.ResponseInfo();
        List<QualityReportDTO.QualityInfo> qualityInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workSheetList)) {
            List<Long> idList = workSheetList.stream().map(WorkSheet::getId).collect(Collectors.toList());
            //查找正常工单产生的不良
            List<WsStepUnqualifiedItem> wsUnqualifiedItemList = wsStepUnqualifiedItemRepository
                    .findBySubWorkSheetWorkSheetIdInAndDeleted(idList, Constants.LONG_ZERO);
            Map<Long, List<WsStepUnqualifiedItem>> wsUqMap = null;
            if (ValidateUtils.isValid(wsUnqualifiedItemList)) {
                wsUqMap = wsUnqualifiedItemList.stream().collect(Collectors.groupingBy(wsStepUnqualifiedItem ->
                        wsStepUnqualifiedItem.getSubWorkSheet().getWorkSheet().getId()));
            }
            //查找返修工单产生的不良
            Map<Long, List<WsStepUnqualifiedItem>> reWsUnqualifiedItemMap = null;
            List<WsRework> wsReworkList = wsReworkRepository.findByOriginalWorkSheetIdInAndDeleted(idList, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsReworkList)) {
                List<WsStepUnqualifiedItem> reworkWsUnqualifiedItemList = wsStepUnqualifiedItemRepository
                        .findBySubWorkSheetWorkSheetIdInAndDeleted(wsReworkList.stream().map(wsRework ->
                                wsRework.getReworkWorkSheet().getId()).collect(Collectors.toList()), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(reworkWsUnqualifiedItemList)) {
                    //获得正常单出现的返修不良
                   reWsUnqualifiedItemMap = reworkWsUnqualifiedItemList.stream().collect(Collectors.groupingBy(wsStepUnqualifiedItem ->
                            wsStepUnqualifiedItem.getSubWorkSheet().getWorkSheet().getId()));
                }
            }
            Map<Long, List<WsStepUnqualifiedItem>> finalWsUqMap = wsUqMap;
            //组装返回的数据
            Map<Long, List<WsStepUnqualifiedItem>> finalReWsUnqualifiedItemMap = reWsUnqualifiedItemMap;
            workSheetList.forEach(workSheet -> {
                QualityReportDTO.QualityInfo qualityInfo = new QualityReportDTO.QualityInfo();
                qualityInfo.setWorkSheet(workSheet);
                if (finalWsUqMap != null && finalWsUqMap.get(workSheet.getId()) != null) {
                    List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = finalWsUqMap.get(workSheet.getId());
                    Map<Long, List<WsStepUnqualifiedItem>> collect = wsStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(wsStepUnqualifiedItem ->
                            wsStepUnqualifiedItem.getUnqualifiedItem().getId()));
                    List<QualityReportDTO.QualifiedInfo> qualifiedInfoList = new ArrayList<>();
                    collect.forEach((unqualifiedItemId, wsStepUnqualifiedItems) -> {
                        QualityReportDTO.QualifiedInfo qualifiedInfo = new QualityReportDTO.QualifiedInfo();
                        UnqualifiedItem unqualifiedItem = wsStepUnqualifiedItems.get(Constants.INT_ZERO).getUnqualifiedItem();
                        qualifiedInfo.setUnqualifiedItemName(unqualifiedItem.getName());
                        qualifiedInfo.setUnqualifiedItemCode(unqualifiedItem.getCode());
                        qualifiedInfo.setUnqualifiedItemNumber(wsStepUnqualifiedItems.stream().mapToInt(WsStepUnqualifiedItem::getNumber).sum());
                        qualifiedInfoList.add(qualifiedInfo);
                    });
                    qualityInfo.setFirstQualifiedInfo(qualifiedInfoList);
                }
                if (ValidateUtils.isValid(finalReWsUnqualifiedItemMap) && finalReWsUnqualifiedItemMap.get(workSheet.getId()) != null) {
                    List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = finalReWsUnqualifiedItemMap.get(workSheet.getId());
                    Map<Long, List<WsStepUnqualifiedItem>> collect = wsStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(wsStepUnqualifiedItem ->
                            wsStepUnqualifiedItem.getUnqualifiedItem().getId()));
                    List<QualityReportDTO.QualifiedInfo> reQualifiedInfoList = new ArrayList<>();
                    collect.forEach((unqualifiedItemId, wsStepUnqualifiedItems) -> {
                        QualityReportDTO.QualifiedInfo qualifiedInfo = new QualityReportDTO.QualifiedInfo();
                        UnqualifiedItem unqualifiedItem = wsStepUnqualifiedItems.get(Constants.INT_ZERO).getUnqualifiedItem();
                        qualifiedInfo.setUnqualifiedItemName(unqualifiedItem.getName());
                        qualifiedInfo.setUnqualifiedItemCode(unqualifiedItem.getCode());
                        qualifiedInfo.setUnqualifiedItemNumber(wsStepUnqualifiedItems.stream().mapToInt(WsStepUnqualifiedItem::getNumber).sum());
                        reQualifiedInfoList.add(qualifiedInfo);
                    });
                    qualityInfo.setQualifiedInfo(reQualifiedInfoList);
                }
                qualityInfoList.add(qualityInfo);
            });
            responseInfo.setCountSize(Optional.ofNullable(workSheetPage).get().getTotalElements());
            return ResponseContent.isOk(responseInfo.setQualityInfoList(qualityInfoList));
        }
        return ResponseContent.ok().message("查无数据!").isOkBuild();
    }

}
