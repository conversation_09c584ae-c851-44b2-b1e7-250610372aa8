package net.airuima.rbase.service.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 批量生产详情易损件service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BatchWorkDetailWearingPartService extends CommonJpaService<BatchWorkDetailWearingPart> {
    private static final String BATCH_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH = "batchWorkDetailWearingPartEntityGraph";
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<BatchWorkDetailWearingPart> find(Specification<BatchWorkDetailWearingPart> spec, Pageable pageable) {
        return batchWorkDetailWearingPartRepository.findAll(spec,pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<BatchWorkDetailWearingPart> find(Specification<BatchWorkDetailWearingPart> spec) {
        return batchWorkDetailWearingPartRepository.findAll(spec,new NamedEntityGraph(BATCH_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<BatchWorkDetailWearingPart> findAll(Pageable pageable) {
        return batchWorkDetailWearingPartRepository.findAll(pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }


    @Override
    public BatchWorkDetailWearingPart  save(BatchWorkDetailWearingPart entity) {
        return super.save(entity);
    }
}
