package net.airuima.rbase.service.base.serialnumber;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.rule.SerialNumberDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 编码规则生成
 *
 * <AUTHOR>
 * @date 2024/01/31
 */
@FuncDefault
public interface ISerialNumberGenerate {


    /**
     * 根据编码规则键生成编码规则请求参数
     * @param serialCode 编码规则键
     * <AUTHOR>
     * @date  2023/7/13
     * @return net.airuima.rbase.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    default SerialNumberDTO getSerialNumber(String serialCode){
        SerialNumberDTO serialNumberDto = new SerialNumberDTO();
        serialNumberDto.setCode(serialCode);
        return serialNumberDto;
    }

    /**
     * 根据工单生成编码
     * @param serialCode 编码规则键
     * @param id 工单主键ID
     * <AUTHOR>
     * @date  2022/5/17
     * @return net.airuima.rbase.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    default SerialNumberDTO getSerialNumberByWorkSheet(String serialCode,Long id){
        SerialNumberDTO serialNumberDto = new SerialNumberDTO();
        serialNumberDto.setCode(serialCode);
        return serialNumberDto;
    }

    /**
     * 根据组织架构生成编码 （根据工单id获取组织架构id，这里可传入工单id 可方便后续插件扩展修改原逻辑，而不用在 代码引用层面修改）
     * @param serialCode 编码规则键
     * @param id 工单主键ID
     * <AUTHOR>
     * @date  2022/5/17
     * @return et.airuima.dto.rule.SerialNumberDTO 编码规则请求参数
     */
    default SerialNumberDTO getSerialNumberByOrganization(String serialCode ,Long id) {
        SerialNumberDTO serialNumberDto = new SerialNumberDTO();
        serialNumberDto.setCode(serialCode);
        return serialNumberDto;
    }


}
