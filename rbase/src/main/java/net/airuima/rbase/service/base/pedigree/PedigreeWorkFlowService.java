package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.PedigreeWorkFlowExcelConstants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeReworkWorkFlowRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeWorkFlowRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系流程框图Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeWorkFlowService extends CommonJpaService<PedigreeWorkFlow> {
    private static final String PEDIGREE_WORK_FLOW_ENTITY_GRAPH = "pedigreeWorkFlowEntityGraph";
    private final PedigreeWorkFlowRepository pedigreeWorkFlowRepository;
    private final PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;
    private final PedigreeRepository pedigreeRepository;
    private final CommonService commonService;
    private final PriorityElementConfigRepository priorityElementConfigRepository;
    private final WorkFlowRepository workFlowRepository;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public PedigreeWorkFlowService(PedigreeWorkFlowRepository pedigreeWorkFlowRepository,
                                   PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository, PedigreeRepository pedigreeRepository,
                                   CommonService commonService, PriorityElementConfigRepository priorityElementConfigRepository, WorkFlowRepository workFlowRepository) {
        this.pedigreeWorkFlowRepository = pedigreeWorkFlowRepository;
        this.pedigreeReworkWorkFlowRepository = pedigreeReworkWorkFlowRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.commonService = commonService;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.workFlowRepository = workFlowRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeWorkFlow> find(Specification<PedigreeWorkFlow> spec, Pageable pageable) {
        return pedigreeWorkFlowRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_WORK_FLOW_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeWorkFlow> find(Specification<PedigreeWorkFlow> spec) {
        return pedigreeWorkFlowRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_WORK_FLOW_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeWorkFlow> findAll(Pageable pageable) {
        return pedigreeWorkFlowRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_WORK_FLOW_ENTITY_GRAPH));
    }


    /**
     * 保存产品谱系工艺路线
     *
     * @param entity 产品谱系工艺路线
     */
    public BaseResultDTO saveInstance(PedigreeWorkFlow entity) {
        if (Objects.isNull(entity.getPriorityElementConfig())) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", PedigreeWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(entity.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (priorityElementConfigOptional.isEmpty()) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", PedigreeWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        BaseResultDTO baseResultDTO = checkPedigreeWorkFlow(entity);
        if (Objects.nonNull(baseResultDTO)) {
            return baseResultDTO;
        }
        entity.setDeleted(Constants.LONG_ZERO);
        pedigreeWorkFlowRepository.save(entity);
        return new BaseResultDTO(Constants.OK, entity);
    }

    /**
     * 校验产品谱系工艺路线已经存在
     *
     * @param pedigreeWorkFlow 产品谱系工艺路线
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    private BaseResultDTO checkPedigreeWorkFlow(PedigreeWorkFlow pedigreeWorkFlow) {
        //客户代码
        Long clientId = null != pedigreeWorkFlow.getClientId() ? pedigreeWorkFlow.getClientId() : null;
        // 产品谱系
        Long pedigreeId = null != pedigreeWorkFlow.getPedigree() ? pedigreeWorkFlow.getPedigree().getId() : null;
        // 工艺路线
        Long workFlowId = null != pedigreeWorkFlow.getWorkFlow() ? pedigreeWorkFlow.getWorkFlow().getId() : null;
        PedigreeWorkFlow queryPedigreeWorkFlow = pedigreeWorkFlowRepository.findByPedigreeIdAndWorkFlowIdAndClientIdAndDeleted(pedigreeId, workFlowId, clientId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeWorkFlow.getId()) && Objects.nonNull(queryPedigreeWorkFlow)) {
            return new BaseResultDTO(Constants.KO, "产品谱系工艺路线已存在", StringUtils.uncapitalize(PedigreeWorkFlow.class.getSimpleName()), "error.pedigreeWorkFlowExists");
        }
        if (Objects.nonNull(pedigreeWorkFlow.getId()) && Objects.nonNull(queryPedigreeWorkFlow) && !queryPedigreeWorkFlow.getId().equals(pedigreeWorkFlow.getId())) {
            return new BaseResultDTO(Constants.KO, "产品谱系工艺路线已存在", StringUtils.uncapitalize(PedigreeWorkFlow.class.getSimpleName()), "error.pedigreeWorkFlowExists");
        }
        return null;
    }


    /**
     * 根据产品谱系ID和客户id获取流程框图
     *
     * @param pedigreeId 谱系ID
     * @return List<WorkFlow>
     */
    @Transactional(readOnly = true)
    public List<WorkFlow> findByPedigreeIdAndClientId(Long pedigreeId, Long clientId, Integer category) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findById(pedigreeId);
        if (pedigreeOptional.isPresent()) {
            Set<WorkFlow> allWorkFlows = new HashSet<>();
            if (Objects.isNull(category) || category == Constants.INT_ZERO || category == Constants.INT_TWO) {
                List<WorkFlow> workFlows = commonService.findPedigreeWorkFlowAndClientId(pedigreeOptional.get(), clientId, Boolean.TRUE);
                allWorkFlows.addAll(workFlows);
            }
            if (Objects.isNull(category) || category == Constants.INT_ONE) {
                List<WorkFlow> reWorkFlows = commonService.findPedigreeReworkWorkFlows(pedigreeOptional.get(), clientId, Boolean.TRUE);
                allWorkFlows.addAll(reWorkFlows);
            }
            return new ArrayList<>(allWorkFlows);
        }

        return Lists.newArrayList();
}


    /**
     * 通过产品谱系获取流程框图
     *
     * @param pedigreeId 产品谱系ID
     * @param clientId   客户ID
     * @param keyword    谱系编码或者名称
     * @param category   工艺路线类型
     * @return List<WorkFlow>
     * <AUTHOR>
     * @date 2021-05-14
     **/
    @Transactional(readOnly = true)
    public List<WorkFlow> findByPedigreeAndKeyword(Long pedigreeId, Long clientId, String keyword, Integer category, Boolean isEnable) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findById(pedigreeId);
        List<WorkFlow> workFlows = Lists.newArrayList();
        if (StringUtils.isBlank(keyword)) {
            if (pedigreeOptional.isPresent()) {
                workFlows = commonService.findPedigreeWorkFlowAndClientId(pedigreeOptional.get(), clientId, Boolean.TRUE);
            }
        } else {
            workFlows = pedigreeWorkFlowRepository.findWorkFlowByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword, clientId);
        }
        workFlows = ValidateUtils.isValid(workFlows) ? workFlows : Lists.newArrayList();
        if (StringUtils.isBlank(keyword)) {
            if (pedigreeOptional.isPresent()) {
                workFlows.addAll(commonService.findPedigreeReworkWorkFlows(pedigreeOptional.get(), clientId, Boolean.TRUE));
            }

        } else {
            List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlows = pedigreeReworkWorkFlowRepository.findWorkFlowByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword, clientId);
            if (ValidateUtils.isValid(pedigreeReworkWorkFlows)) {
                workFlows.addAll(pedigreeReworkWorkFlows.stream().map(PedigreeReworkWorkFlow::getWorkFlow).distinct().toList());
            }
        }
        if (!ValidateUtils.isValid(workFlows)) {
            return Collections.emptyList();
        }
        if (null != category) {
            workFlows = workFlows.stream().filter(workFlow -> workFlow.getCategory() == category).collect(Collectors.toList());
        }
        // 去重
        workFlows = workFlows.stream().distinct().collect(Collectors.toList());
        return null != isEnable && ValidateUtils.isValid(workFlows) ? workFlows.stream().filter(workFlow -> workFlow.getIsEnable() == isEnable).collect(Collectors.toList()) : workFlows;
    }


    /**
     * 通过谱系ID和工艺路线ID逻辑删除绑定关系
     *
     * @param pedigreeId 谱系ID
     * @param workFlowId 工艺路线ID
     * @return void
     * <AUTHOR>
     * @date 2021-05-10
     **/
    public void deleteByPedigreeIdAndWorkFlowId(long pedigreeId, Long workFlowId, Long clientId) {
        pedigreeWorkFlowRepository.deleteByPedigreeIdAndWorkFlowIdAndClientId(pedigreeId, workFlowId, clientId);
    }

    /**
     * 启用/禁用指定产品谱系正常工艺路线
     *
     * @param pedigreeWorkFlowId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByPedigreeWorkFlowId(Long pedigreeWorkFlowId) {
        PedigreeWorkFlow pedigreeWorkFlow = pedigreeWorkFlowRepository.findById(pedigreeWorkFlowId).orElseThrow(() -> new ResponseException("error.pedigreeWorkFlowNotExist", "产品谱系流程框图不存在"));
        pedigreeWorkFlow.setIsEnable(!pedigreeWorkFlow.getIsEnable());
        pedigreeWorkFlowRepository.save(pedigreeWorkFlow);
    }

    /**
     * 产品谱系工艺路线导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>> 行数据
     */
    public List<Map<String, Object>> importPedigreeWorkFlowExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(PedigreeWorkFlowExcelConstants.PEDIGREE_CODE));
            // 产品谱系
            Pedigree pedigree = null;
            if (!StringUtils.isEmpty(pedigreeCode) && !"null".equals(pedigreeCode)) {
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO);
                if (pedigreeOptional.isPresent()) {
                    pedigree = pedigreeOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工艺路线编码并转换为对象
            String workFlowCode = String.valueOf(row.get(PedigreeWorkFlowExcelConstants.WORK_FLOW_CODE));
            WorkFlow workFlow = null;
            if (!StringUtils.isEmpty(workFlowCode) && !"null".equals(workFlowCode)) {
                Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(workFlowCode, Constants.LONG_ZERO);
                if (workFlowOptional.isPresent()) {
                    workFlow = workFlowOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            //获取客户代码
            String clientCode = String.valueOf(row.get((PedigreeWorkFlowExcelConstants.CLIENT_CODE)));
            ClientDTO clientDto = StringUtils.isEmpty(clientCode) || "null".equals(clientCode) ? null : rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO);
            if ((!StringUtils.isEmpty(clientCode) && !"null".equals(clientCode)) && (clientDto == null || clientDto.getId() == null)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因客户编码不存在");
                illegalDataList.add(row);
                return;
            }
            // 客户id
            Long clientId = Optional.ofNullable(clientDto).map(ClientDTO::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
            // 获取是否启用
            Boolean isEnable = String.valueOf(row.get(PedigreeWorkFlowExcelConstants.IS_ENABLE)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 查询工艺路线是否已经存在
            PedigreeWorkFlow queryPedigreeWorkFlow = pedigreeWorkFlowRepository.findByPedigreeIdAndWorkFlowIdAndClientIdAndDeleted(pedigreeId, workFlowId, clientId, Constants.LONG_ZERO).orElse(null);
            // 获取工艺路线条件优先级配置
            PriorityElementConfig pedigreeWorkFlowPriority = getPedigreeWorkFlowPriority(pedigreeId, clientId);
            if (pedigreeWorkFlowPriority == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 该组合条件优先级未配置");
                illegalDataList.add(row);
                return;
            }
            // 保存配置
            savePedigreeWorkFlow(pedigree, workFlow, isEnable, queryPedigreeWorkFlow, pedigreeWorkFlowPriority, clientId);
        });
        return illegalDataList;
    }

    /**
     * 保存产品谱系工艺路线配置
     *
     * @param pedigree                 产品谱系
     * @param workFlow                 工艺路线
     * @param isEnable                 是否启用
     * @param queryPedigreeWorkFlow    查询的工艺路线配置
     * @param pedigreeWorkFlowPriority 条件优先级
     * @param clientId                 客户id
     */
    private void savePedigreeWorkFlow(Pedigree pedigree, WorkFlow workFlow, Boolean isEnable, PedigreeWorkFlow queryPedigreeWorkFlow,
                                      PriorityElementConfig pedigreeWorkFlowPriority, Long clientId) {
        if (Objects.nonNull(queryPedigreeWorkFlow)) {
            queryPedigreeWorkFlow.setIsEnable(isEnable);
            pedigreeWorkFlowRepository.save(queryPedigreeWorkFlow);
        } else {
            PedigreeWorkFlow pedigreeWorkFlow = new PedigreeWorkFlow();
            pedigreeWorkFlow
                    .setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setPriorityElementConfig(pedigreeWorkFlowPriority)
                    .setClientId(clientId)
                    .setIsEnable(isEnable);
            pedigreeWorkFlow.setDeleted(Constants.LONG_ZERO);
            pedigreeWorkFlowRepository.save(pedigreeWorkFlow);
        }
    }


    /**
     * 获取产品谱系工艺路线条件优先级配置
     *
     * @param pedigreeId 产品谱系id
     * @param clientId   客户id
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    public PriorityElementConfig getPedigreeWorkFlowPriority(Long pedigreeId, Long clientId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_SEVEN, combination);
    }

    /**
     * 获取只有产品谱系配置的工艺路线(正常、返工、返修)
     * @param pedigreeId 产品谱系
     * @return List<WorkFlow>
     */
    public List<WorkFlow> findByPedigreeId(Long pedigreeId) {
        Set<WorkFlow> workFlowSet = new HashSet<>();
        List<WorkFlow> workFlowList =  pedigreeWorkFlowRepository.findByPedigreeIdAndClientIdAndDeleted(pedigreeId, null, net.airuima.constant.Constants.LONG_ZERO);
        List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlows = pedigreeReworkWorkFlowRepository.findByPedigreeIdAndClientIdAndDeleted(pedigreeId,null, net.airuima.constant.Constants.LONG_ZERO);
        if(CollectionUtils.isNotEmpty(workFlowList)){
            workFlowSet.addAll(workFlowList);
        }
        if(CollectionUtils.isNotEmpty(pedigreeReworkWorkFlows)){
            workFlowSet.addAll(pedigreeReworkWorkFlows.stream().map(PedigreeReworkWorkFlow::getWorkFlow).toList());
        }
        return workFlowSet.stream().toList();
    }


}
