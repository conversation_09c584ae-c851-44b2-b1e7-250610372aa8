package net.airuima.rbase.service.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.report.WorkSheetStatusReportDTO;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportRequestDTO;
import net.airuima.rbase.web.rest.report.dto.worksheetstatus.WorkSheetStatusReportChartDTO;
import net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO;
import net.airuima.util.ResponseContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单状态报表Service
 *
 * <AUTHOR>
 * @date 2021-3-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetStatusReportService {
    private static final String WORK_SHEET = "workSheet";
    private static final String CREATED_DATE = "createdDate";
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private CommonService commonService;

    /**
     * 工单状态报表
     *
     * @param requestInfo 请求参数
     * @return
     */
    @Transactional(readOnly = true)
    public ResponseEntity<ResponseContent<WorkSheetStatusReportDTO.ResponseInfo>> workSheetFinishReport(WorkSheetStatusReportDTO.RequestInfo requestInfo) {
        Specification<SubWorkSheet> specification = (Specification<SubWorkSheet>) (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //总工单筛选
            if (ValidateUtils.isValid(requestInfo.getWsSerialNumber())) {
                Predicate subWsPredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("serialNumber"), requestInfo.getWsSerialNumber());
                predicateList.add(subWsPredicate);
            } else {
                //开始时间和结束时间筛选
                if ( requestInfo.getEndDate() != null) {
                    predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                            LocalDateTime.of(requestInfo.getEndDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant()));
                }
                if (requestInfo.getStartDate() != null) {
                    predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(CREATED_DATE).as(Instant.class),
                            LocalDateTime.of(requestInfo.getStartDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant()));
                }
                if (requestInfo.getPedigreeId() != null) {
                    Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), requestInfo.getPedigreeId());
                    predicateList.add(pedigreePredicate);
                }
                if (requestInfo.getWorkLineId() != null) {
                    Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("workLine").get("id"), requestInfo.getWorkLineId());
                    predicateList.add(workLinePredicate);
                }
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        Page<SubWorkSheet> subWorkSheetPage = subWorkSheetRepository.findAll(specification, PageRequest.of(requestInfo.getCurrentPage(), requestInfo.getPageSize()),new NamedEntityGraph("subWorkSheetEntityGraph"));
        List<SubWorkSheet> subWorkSheetList = Optional.ofNullable(subWorkSheetPage).map(Slice::getContent).orElse(null);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            WorkSheetStatusReportDTO.ResponseInfo responseInfo = new WorkSheetStatusReportDTO.ResponseInfo();
            List<WorkSheetStatusReportDTO.SubWorkSheetDTO> subWorkSheetDtoList = subWorkSheetList.stream().map(WorkSheetStatusReportDTO.SubWorkSheetDTO::new).collect(Collectors.toList());
            responseInfo.setCountSize(subWorkSheetPage.getTotalElements());
            return ResponseContent.isOk(responseInfo.setSubWorkSheetDtoList(subWorkSheetDtoList));
        }
        return ResponseContent.ok().message("查无数据!").isOkBuild();
    }

    /**
     * 获取工单状态图形统计数据
     * @param workSheetStatisticRequestDto  请求参数
     * @return net.airuima.rbase.web.rest.report.dto.worksheetstatus.WorkSheetStatusReportChartDTO
     * <AUTHOR>
     * @date 2023/10/24
     */
    @Transactional(readOnly = true)
    public WorkSheetStatusReportChartDTO workSheetStatusReportChart(WorkSheetStatisticReportRequestDTO workSheetStatisticRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(workSheetStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = workSheetStatisticRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = workSheetStatisticRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = workSheetStatisticRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = workSheetStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetStatisticRequestDto.getEndDate();
        //报表类型
        Integer reportType = workSheetStatisticRequestDto.getReportType();
        String result = commonService.getDictionaryData(Constants.KEY_WORK_SHEET_STATUS);
        List<Map<String, String>> wsStatusMapList = JSON.parseObject(result, new TypeReference<>() {
        });
        //获取子工单报表
        if (WorkSheetReportTypeEnum.SUB_WORK_SHEET.getCategory() == reportType) {
            return subWorkSheetStatusReportChart(pedigreeId, organizationId, workLineId, startDateTime, endDateTime,wsStatusMapList);
        } else {
            //获取工单报表
            return workSheetStatusReportChart(pedigreeId, organizationId, workLineId, startDateTime, endDateTime,wsStatusMapList);
        }
    }

    /**
     * 统计子工单生产状态数据
     * @param pedigreeId 产品谱系ID
     * @param organizationId 组织架构ID
     * @param workLineId 生产线ID
     * @param startDateTime  开始时间
     * @param endDateTime 结束时间
     * @param wsStatusMapList    工单状态列表
     * @return net.airuima.rbase.web.rest.report.dto.worksheetstatus.WorkSheetStatusReportChartDTO
     * <AUTHOR>
     * @date 2023/10/24
     */
    private WorkSheetStatusReportChartDTO subWorkSheetStatusReportChart(Long pedigreeId, Long organizationId, Long workLineId,
                                                                        LocalDateTime startDateTime, LocalDateTime endDateTime, List<Map<String, String>> wsStatusMapList) {
        WorkSheetStatusReportChartDTO workSheetStatusReportChartDTO = new WorkSheetStatusReportChartDTO();
        //总的子工单数量
        int subWorkSheetNumberCount = Optional.ofNullable(subWorkSheetRepository.countSubWorkSheetNumber(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        workSheetStatusReportChartDTO.setNumber((long) subWorkSheetNumberCount);
        List<WsStatusNumberDTO> wsStatusNumberDTOList = subWorkSheetRepository.countStatusNumberGroupByStatus(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        //获取饼图数据
        List<WorkSheetStatusReportChartDTO.PieChartDTO> pieChartDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wsStatusNumberDTOList)) {
            wsStatusNumberDTOList.forEach(wsStatusNumberDTO -> {
                wsStatusMapList.forEach(wsStatusMap -> {
                    if (Integer.parseInt(wsStatusMap.get("key")) == wsStatusNumberDTO.getStatus()) {
                        WorkSheetStatusReportChartDTO.PieChartDTO pieChartDTO = new WorkSheetStatusReportChartDTO.PieChartDTO();
                        pieChartDTO.setStatus(wsStatusMap.get("value")).setNumber(wsStatusNumberDTO.getNumber());
                        pieChartDTOList.add(pieChartDTO);
                    }
                });
                //已完成包括正常完成及异常结单
                if (wsStatusNumberDTO.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                    workSheetStatusReportChartDTO.setFinishNumber(workSheetStatusReportChartDTO.getFinishNumber() + wsStatusNumberDTO.getNumber());
                }
                //投产中状态数量
                if (wsStatusNumberDTO.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()) {
                    workSheetStatusReportChartDTO.setProductionNumber(wsStatusNumberDTO.getNumber());
                }
                //已下单状态数量
                if (wsStatusNumberDTO.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) {
                    workSheetStatusReportChartDTO.setPreparedNumber(wsStatusNumberDTO.getNumber());
                }
            });
            workSheetStatusReportChartDTO.setPieChartDTOList(pieChartDTOList);
        }
        //获取组装柱状图数据
        List<WsStatusNumberDTO> finishedStatusNumberList = subWorkSheetRepository.countStatusNumberGroupByPlanEndDate(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()));
        List<WsStatusNumberDTO> expireStatusNumberList = subWorkSheetRepository.countExpiredNumberGroupByPlanEndDate(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, LocalDateTime.now(), Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_APPROVING.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()));
        List<WorkSheetStatusReportChartDTO.BarChartDTO> barChartDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(finishedStatusNumberList)) {
            finishedStatusNumberList.forEach(finishedStatusNumber -> {
                barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.FINISH.getKey(), finishedStatusNumber.getNumber(), finishedStatusNumber.getRecordDate()));
                barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.EXPIRE.getKey(), Constants.LONG_ZERO, finishedStatusNumber.getRecordDate()));
            });
        }
        if (!CollectionUtils.isEmpty(expireStatusNumberList)) {
            expireStatusNumberList.forEach(expireStatusNumber -> {
                if (CollectionUtils.isEmpty(barChartDTOList) || barChartDTOList.stream().noneMatch(barChartDTO -> barChartDTO.getDate().equals(expireStatusNumber.getRecordDate()) && barChartDTO.getStatus().equals(WorkSheetReportChartTypeEnum.EXPIRE.getKey()))) {
                    barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.FINISH.getKey(), Constants.LONG_ZERO, expireStatusNumber.getRecordDate()));
                    barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.EXPIRE.getKey(), expireStatusNumber.getNumber(), expireStatusNumber.getRecordDate()));
                } else {
                    barChartDTOList.forEach(barChartDTO -> {
                        if (barChartDTO.getStatus().equals(WorkSheetReportChartTypeEnum.EXPIRE.getKey()) && barChartDTO.getDate().equals(expireStatusNumber.getRecordDate())) {
                            barChartDTO.setNumber(expireStatusNumber.getNumber());
                        }
                    });
                }
            });
        }
        workSheetStatusReportChartDTO.setBarChartDTOList(barChartDTOList);
        return workSheetStatusReportChartDTO;
    }

    /**
     * 统计工单生产状态数据
     * @param pedigreeId 产品谱系ID
     * @param organizationId 组织架构ID
     * @param workLineId 生产线ID
     * @param startDateTime  开始时间
     * @param endDateTime 结束时间
     * @param wsStatusMapList    工单状态列表
     * @return net.airuima.rbase.web.rest.report.dto.worksheetstatus.WorkSheetStatusReportChartDTO
     * <AUTHOR>
     * @date 2023/10/24
     */
    private WorkSheetStatusReportChartDTO workSheetStatusReportChart(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime,List<Map<String, String>> wsStatusMapList) {
        WorkSheetStatusReportChartDTO workSheetStatusReportChartDTO = new WorkSheetStatusReportChartDTO();
        //总的工单数量
        int workSheetNumberCount = Optional.ofNullable(workSheetRepository.countWorkSheetNumber(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        workSheetStatusReportChartDTO.setNumber((long) workSheetNumberCount);
        List<WsStatusNumberDTO> wsStatusNumberDTOList = workSheetRepository.countStatusNumberGroupByStatus(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        //获取饼图数据
        List<WorkSheetStatusReportChartDTO.PieChartDTO> pieChartDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wsStatusNumberDTOList)) {
            wsStatusNumberDTOList.forEach(wsStatusNumberDTO -> {
                wsStatusMapList.forEach(wsStatusMap -> {
                    if (Integer.parseInt(wsStatusMap.get("key")) == wsStatusNumberDTO.getStatus()) {
                        WorkSheetStatusReportChartDTO.PieChartDTO pieChartDTO = new WorkSheetStatusReportChartDTO.PieChartDTO();
                        pieChartDTO.setStatus(wsStatusMap.get("value")).setNumber(wsStatusNumberDTO.getNumber());
                        pieChartDTOList.add(pieChartDTO);
                    }
                });
                //已完成包括正常完成及异常结单
                if (wsStatusNumberDTO.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                    workSheetStatusReportChartDTO.setFinishNumber(workSheetStatusReportChartDTO.getFinishNumber() + wsStatusNumberDTO.getNumber());
                }
                //投产中状态数量
                if (wsStatusNumberDTO.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()) {
                    workSheetStatusReportChartDTO.setProductionNumber(wsStatusNumberDTO.getNumber());
                }
                //已下单状态数量
                if (wsStatusNumberDTO.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) {
                    workSheetStatusReportChartDTO.setPreparedNumber(wsStatusNumberDTO.getNumber());
                }
            });
            workSheetStatusReportChartDTO.setPieChartDTOList(pieChartDTOList);
        }
        //获取组装柱状图数据
        List<WsStatusNumberDTO> finishedStatusNumberList = workSheetRepository.countStatusNumberGroupByPlanEndDate(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()));
        List<WsStatusNumberDTO> expireStatusNumberList = workSheetRepository.countExpiredNumberGroupByPlanEndDate(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, LocalDateTime.now(), Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_APPROVING.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()));
        List<WorkSheetStatusReportChartDTO.BarChartDTO> barChartDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(finishedStatusNumberList)) {
            finishedStatusNumberList.forEach(finishedStatusNumber -> {
                barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.FINISH.getKey(), finishedStatusNumber.getNumber(), finishedStatusNumber.getRecordDate()));
                barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.EXPIRE.getKey(), Constants.LONG_ZERO, finishedStatusNumber.getRecordDate()));
            });
        }
        if (!CollectionUtils.isEmpty(expireStatusNumberList)) {
            expireStatusNumberList.forEach(expireStatusNumber -> {
                if (CollectionUtils.isEmpty(barChartDTOList) || barChartDTOList.stream().noneMatch(barChartDTO -> barChartDTO.getDate().equals(expireStatusNumber.getRecordDate()) && barChartDTO.getStatus().equals(WorkSheetReportChartTypeEnum.EXPIRE.getKey()))) {
                    barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.FINISH.getKey(), Constants.LONG_ZERO, expireStatusNumber.getRecordDate()));
                    barChartDTOList.add(new WorkSheetStatusReportChartDTO.BarChartDTO(WorkSheetReportChartTypeEnum.EXPIRE.getKey(), expireStatusNumber.getNumber(), expireStatusNumber.getRecordDate()));
                } else {
                    barChartDTOList.forEach(barChartDTO -> {
                        if (barChartDTO.getStatus().equals(WorkSheetReportChartTypeEnum.EXPIRE.getKey()) && barChartDTO.getDate().equals(expireStatusNumber.getRecordDate())) {
                            barChartDTO.setNumber(expireStatusNumber.getNumber());
                        }
                    });
                }
            });
        }
        workSheetStatusReportChartDTO.setBarChartDTOList(barChartDTOList);
        return workSheetStatusReportChartDTO;
    }

    /**
     * 计划完成时期解析
     *
     * @param workSheetStatisticRequestDto 工单报表请求参数
     */
    private void parseTimeFinishTimeCategory(WorkSheetStatisticReportRequestDTO workSheetStatisticRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = workSheetStatisticRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = workSheetStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetStatisticRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                workSheetStatisticRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                workSheetStatisticRequestDto.setStartDate(weekStart);
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                workSheetStatisticRequestDto.setStartDate(monthStart);
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX));
            }
        }
    }


}
