package net.airuima.rbase.service.procedure.aps.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsGetDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/12/25
 */
@Schema(description = "正常单与返工单下交返回数据参数")
public class WorkSheetResDTO extends BaseDTO {

    /**
     *  工单
     */
    private WorkSheet workSheet;

    /**
     * 可级联下单产品参数DTO
     */
    private List<CreateCascadeWsGetDTO> cascadeWsGetDTOList;

    /**
     * 工单列表
     */
    private List<WorkSheet> workSheetList;

    /**
     * 工单返修单对应关系
     */
    private List<WsRework> wsReworkList;

    public WorkSheetResDTO() {
    }

    public WorkSheetResDTO(BaseDTO baseDto) {
        super(baseDto.getStatus(), baseDto.getMessage());
    }

    public WorkSheetResDTO(BaseDTO baseDto, WorkSheet workSheet) {
        super(baseDto.getStatus(), baseDto.getMessage());
        this.workSheet = workSheet;
    }
    public WorkSheetResDTO(BaseDTO baseDto, WorkSheet workSheet,List<CreateCascadeWsGetDTO> cascadeWsGetDTOList) {
        super(baseDto.getStatus(), baseDto.getMessage());
        this.workSheet = workSheet;
        this.cascadeWsGetDTOList = cascadeWsGetDTOList;
    }


    public WorkSheetResDTO(BaseDTO baseDto, List<WorkSheet> workSheetList,List<WsRework> wsReworkList) {
        super(baseDto.getStatus(), baseDto.getMessage());
        this.workSheetList = workSheetList;
        this.wsReworkList = wsReworkList;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetResDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public List<WorkSheet> getWorkSheetList() {
        return workSheetList;
    }

    public WorkSheetResDTO setWorkSheetList(List<WorkSheet> workSheetList) {
        this.workSheetList = workSheetList;
        return this;
    }

    public List<WsRework> getWsReworkList() {
        return wsReworkList;
    }

    public WorkSheetResDTO setWsReworkList(List<WsRework> wsReworkList) {
        this.wsReworkList = wsReworkList;
        return this;
    }

    public List<CreateCascadeWsGetDTO> getCascadeWsGetDTOList() {
        return cascadeWsGetDTOList;
    }

    public WorkSheetResDTO setCascadeWsGetDTOList(List<CreateCascadeWsGetDTO> cascadeWsGetDTOList) {
        this.cascadeWsGetDTOList = cascadeWsGetDTOList;
        return this;
    }
}
