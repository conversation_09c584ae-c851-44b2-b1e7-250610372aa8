package net.airuima.rbase.service.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *易损件容器Service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailWearingPartService extends CommonJpaService<ContainerDetailWearingPart> {
    private static final String CONTAINER_DETAIL_WEARING_PART_ENTITY_GRAPH = "containerDetailWearingPartEntityGraph";
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailWearingPart> find(Specification<ContainerDetailWearingPart> spec, Pageable pageable) {
        return containerDetailWearingPartRepository.findAll(spec,pageable,new NamedEntityGraph(CONTAINER_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<ContainerDetailWearingPart> find(Specification<ContainerDetailWearingPart> spec) {
        return containerDetailWearingPartRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<ContainerDetailWearingPart> findAll(Pageable pageable) {
        return containerDetailWearingPartRepository.findAll(pageable,new NamedEntityGraph(CONTAINER_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    public ContainerDetailWearingPart  save(ContainerDetailWearingPart entity) {
        return super.save(entity);
    }
}
