package net.airuima.rbase.service.base.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.repository.base.wearingpart.PedigreeStepWearingPartGroupRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *产品谱系工序易损件规则service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepWearingPartGroupService extends CommonJpaService<PedigreeStepWearingPartGroup> {
    private static final String PEDIGREE_STEP_WEARING_PART_GROUP_ENTITY_GRAPH = "pedigreeStepWearingPartGroupEntityGraph";
    @Autowired
    private PedigreeStepWearingPartGroupRepository pedigreeStepWearingPartGroupRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeStepWearingPartGroup> find(Specification<PedigreeStepWearingPartGroup> spec, Pageable pageable) {
        return pedigreeStepWearingPartGroupRepository.findAll(spec,pageable,new NamedEntityGraph(PEDIGREE_STEP_WEARING_PART_GROUP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<PedigreeStepWearingPartGroup> find(Specification<PedigreeStepWearingPartGroup> spec) {
        return pedigreeStepWearingPartGroupRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_WEARING_PART_GROUP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeStepWearingPartGroup> findAll(Pageable pageable) {
        return pedigreeStepWearingPartGroupRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_WEARING_PART_GROUP_ENTITY_GRAPH));
    }

    /**
     * 新增或者修改易损件使用规则
     * @param pedigreeStepWearingPartGroup 待保存易损件使用规则
     */
    public PedigreeStepWearingPartGroup saveInstance(PedigreeStepWearingPartGroup pedigreeStepWearingPartGroup){
        PedigreeStepWearingPartGroup entity = pedigreeStepWearingPartGroupRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndWorkCellIdAndWearingPartGroupIdAndDeleted(Objects.nonNull(pedigreeStepWearingPartGroup.getPedigree())?pedigreeStepWearingPartGroup.getPedigree().getId():null,
                Objects.nonNull(pedigreeStepWearingPartGroup.getWorkFlow())?pedigreeStepWearingPartGroup.getWorkFlow().getId():null,Objects.nonNull(pedigreeStepWearingPartGroup.getStep())?pedigreeStepWearingPartGroup.getStep().getId():null,
                Objects.nonNull(pedigreeStepWearingPartGroup.getWorkCell())?pedigreeStepWearingPartGroup.getWorkCell().getId():null,pedigreeStepWearingPartGroup.getWearingPartGroup().getId(), Constants.LONG_ZERO,Boolean.TRUE);
        if(Objects.isNull(pedigreeStepWearingPartGroup.getId()) && Objects.nonNull(entity)){
            throw new ResponseException("error.pedigreeStepWearingPartGroupExist","工位易损件使用规则已存在");
        }

        if(Objects.nonNull(pedigreeStepWearingPartGroup.getId()) && Objects.nonNull(entity) && !pedigreeStepWearingPartGroup.getId().equals(entity.getId())){
            throw new ResponseException("error.pedigreeStepWearingPartGroupExist","工位易损件使用规则已存在");
        }
        return this.save(pedigreeStepWearingPartGroup);
    }

    @Transactional(readOnly = true)
    public List<PedigreeStepWearingPartGroup> findPedigreeWorkFlowStepByWearingGroupInfo(Pedigree pedigree, WorkFlow workFlow, Step step, WorkCell workCell) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<PedigreeStepWearingPartGroup> pedigreeStepWearingPartGroups = pedigreeStepWearingPartGroupRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndWorkCellIdAndDeleted(pedigreeIdList,workFlow.getId(),step.getId(),workCell.getId(),Constants.LONG_ZERO,Boolean.TRUE);
        if(CollectionUtils.isEmpty(pedigreeStepWearingPartGroups)){
            return Collections.emptyList();
        }
        //工位+产品谱系+工艺路线+工序
        List<PedigreeStepWearingPartGroup> fullMatchpedigreeStepWearingPartGroups  = pedigreeStepWearingPartGroups.stream().filter(pedigreeStepWearingPartGroup -> Objects.nonNull(pedigreeStepWearingPartGroup.getPedigree())
                && Objects.nonNull(pedigreeStepWearingPartGroup.getStep()) && Objects.nonNull(pedigreeStepWearingPartGroup.getWorkFlow()) && Objects.nonNull(pedigreeStepWearingPartGroup.getWorkCell())).toList();
        if(!CollectionUtils.isEmpty(fullMatchpedigreeStepWearingPartGroups)){
            int maxPedigreeLevel = fullMatchpedigreeStepWearingPartGroups.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            return fullMatchpedigreeStepWearingPartGroups.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList());
        }
        //工位+产品谱系+工艺路线
        fullMatchpedigreeStepWearingPartGroups = pedigreeStepWearingPartGroups.stream().filter(pedigreeStepWearingPartGroup -> Objects.nonNull(pedigreeStepWearingPartGroup.getPedigree())
                && Objects.nonNull(pedigreeStepWearingPartGroup.getWorkFlow()) && Objects.nonNull(pedigreeStepWearingPartGroup.getWorkCell())).toList();
        if(!CollectionUtils.isEmpty(fullMatchpedigreeStepWearingPartGroups)){
            int maxPedigreeLevel = fullMatchpedigreeStepWearingPartGroups.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            return fullMatchpedigreeStepWearingPartGroups.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList());
        }
        //工位+产品谱系
        fullMatchpedigreeStepWearingPartGroups = pedigreeStepWearingPartGroups.stream().filter(pedigreeStepWearingPartGroup -> Objects.nonNull(pedigreeStepWearingPartGroup.getPedigree())
                &&  Objects.nonNull(pedigreeStepWearingPartGroup.getWorkCell())).toList();
        if(!CollectionUtils.isEmpty(fullMatchpedigreeStepWearingPartGroups)){
            int maxPedigreeLevel = fullMatchpedigreeStepWearingPartGroups.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            return fullMatchpedigreeStepWearingPartGroups.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList());
        }
        //工位
        return pedigreeStepWearingPartGroups;
    }

    /**
     * 递归获取谱系的父亲节点数据
     *
     * @param pedigree        产品谱系
     * @param parentPedigrees 节点列表
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public void findParentPedigree(Pedigree pedigree, List<Pedigree> parentPedigrees) {
        if (null != pedigree.getParent()) {
            parentPedigrees.add(pedigree.getParent());
            findParentPedigree(pedigree.getParent(), parentPedigrees);
        }
    }

    @Override
    public String validateBeforeSave(PedigreeStepWearingPartGroup entity) {
        if(Objects.isNull(entity.getPedigree()) && (Objects.nonNull(entity.getWorkFlow()) || Objects.nonNull(entity.getStep()))){
            return  "工艺路线或工序存在时，产品谱系不可为空";
        }
        if(Objects.isNull(entity.getWorkFlow()) && Objects.nonNull(entity.getStep())){
            return  "工序存在时，产品谱系及工艺路线不可为空";
        }
        return StringUtils.EMPTY;
    }
}
