package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ReportTimeRangeEnum;
import net.airuima.rbase.constant.StaffPerformStatisticReportRankEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.web.rest.report.dto.perform.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工看板报表 Service
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
@Service
@Transactional(rollbackFor = {Exception.class})
public class StaffPerformStatisticReportService {

    /**
     * 报工时间
     */
    private final String RECORD_TIME = "recordTime";

    /**
     * 工单
     */
    private final String WORK_SHEET = "workSheet";

    /**
     * 子工单
     */
    private final String SUB_WORK_SHEET = "subWorkSheet";

    private final StaffPerformService staffPerformService;
    private final StaffPerformRepository staffPerformRepository;

    @Autowired
    private CommonService commonService;

    public StaffPerformStatisticReportService(StaffPerformService staffPerformService, StaffPerformRepository staffPerformRepository) {
        this.staffPerformService = staffPerformService;
        this.staffPerformRepository = staffPerformRepository;
    }


    /**
     * 报工统计看板排行榜数据
     *
     * @param staffPerformStatisticRequestDto 报工统计请求参数Dto
     * @return net.airuima.rbase.web.rest.report.dto.StaffPerformStatisticReportRankResultDTO 报工统计图形数据
     */
    @Transactional(readOnly = true)
    public StaffPerformStatisticReportRankResultDTO getStaffPerformStatisticRank(StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto,int top) {
        // 查询时间解析
        parseTimeFinishTimeCategory(staffPerformStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = staffPerformStatisticRequestDto.getPedigreeId();
        // 产线id
        Long workLineId = staffPerformStatisticRequestDto.getWorkLineId();
        // 员工id
        Long staffId = staffPerformStatisticRequestDto.getStaffId();
        // 开始查询时间
        LocalDateTime startDateTime = staffPerformStatisticRequestDto.getStartDate();
        // 结束查询时间
        LocalDateTime endDateTime = staffPerformStatisticRequestDto.getEndDate();
        // 排行榜类型
        Integer rankCategory = staffPerformStatisticRequestDto.getRankCategory();
        // 获取报工统计看板排行榜数据
        return getStaffPerformStatisticRankResultDTO(pedigreeId, staffId, workLineId, startDateTime, endDateTime, rankCategory,top);
    }

    /**
     * 获取报工统计看板排行榜数据
     *
     * @param pedigreeId    产品谱系id
     * @param staffId       员工id
     * @param workLineId    产线id
     * @param startDateTime 开始查询时间
     * @param endDateTime   结束查询时间
     * @param rankCategory  排行榜类型
     * @return net.airuima.rbase.web.rest.report.dto.StaffPerformStatisticReportRankResultDTO 报工统计看板图形数据
     */
    private StaffPerformStatisticReportRankResultDTO getStaffPerformStatisticRankResultDTO(Long pedigreeId, Long staffId, Long workLineId,
                                                                                           LocalDateTime startDateTime, LocalDateTime endDateTime,
                                                                                           Integer rankCategory,int top) {
        // 排行榜数据
        StaffPerformStatisticReportRankResultDTO reportChartResultDto = new StaffPerformStatisticReportRankResultDTO();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (!subWsProductionMode) {
            // 员工排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.STAFF.getCategory() == rankCategory) {
                List<StaffRankQueryDataDTO> staffRankQueryDataDTOList = Optional.ofNullable(staffPerformRepository.findWorkSheetStaffPerformStatisticStaffRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                List<StaffRankDataDTO> staffRankDataDTOList = convertStaffRankQueryToChartData(staffRankQueryDataDTOList);
                reportChartResultDto.setStaffRankDataList(staffRankDataDTOList);
            }
            // 工位排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.WORK_CELL.getCategory() == rankCategory) {
                List<WorkCellRankDataDTO> workCellRankDataDTOList = Optional.ofNullable(staffPerformRepository.findWorkSheetStaffPerformStatisticWorkCellRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                reportChartResultDto.setWorkCellRankDataList(workCellRankDataDTOList);
            }
            // 工序排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.STEP.getCategory() == rankCategory) {
                List<StepRankDataDTO> stepRankDataDTOList = Optional.ofNullable(staffPerformRepository.findWorkSheetStaffPerformStatisticStepRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                reportChartResultDto.setStepRankDataList(stepRankDataDTOList);
            }
        }
        if (subWsProductionMode) {
            // 员工排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.STAFF.getCategory() == rankCategory) {
                List<StaffRankQueryDataDTO> staffRankQueryDataDTOList = Optional.ofNullable(staffPerformRepository.findSubWorkSheetStaffPerformStatisticStaffRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                List<StaffRankDataDTO> staffRankDataDTOList = convertStaffRankQueryToChartData(staffRankQueryDataDTOList);
                reportChartResultDto.setStaffRankDataList(staffRankDataDTOList);
            }
            // 工位排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.WORK_CELL.getCategory() == rankCategory) {
                List<WorkCellRankDataDTO> workCellRankDataDTOList = Optional.ofNullable(staffPerformRepository.findSubWorkSheetStaffPerformStatisticWorkCellRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                reportChartResultDto.setWorkCellRankDataList(workCellRankDataDTOList);
            }
            // 工序排行榜
            if (Objects.nonNull(rankCategory) && StaffPerformStatisticReportRankEnum.STEP.getCategory() == rankCategory) {
                List<StepRankDataDTO> stepRankDataDTOList = Optional.ofNullable(staffPerformRepository.findSubWorkSheetStaffPerformStatisticStepRankChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
                reportChartResultDto.setStepRankDataList(stepRankDataDTOList);
            }
        }
        return reportChartResultDto;

    }

    /**
     * 转换报工统计员工排名查询结果为图表数据
     *
     * @param staffRankQueryDataDTOList 报工统计员工排名查询结果
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.StaffRankDataDTO> 报工统计员工排名图表数据
     */
    public List<StaffRankDataDTO> convertStaffRankQueryToChartData(List<StaffRankQueryDataDTO> staffRankQueryDataDTOList) {
        List<StaffRankDataDTO> staffRankDataDTOList = Lists.newLinkedList();
        if(CollectionUtils.isEmpty(staffRankQueryDataDTOList)){
            return new ArrayList<>();
        }
        staffRankQueryDataDTOList.forEach(s -> {
            StaffRankDataDTO staffRankDataDto = new StaffRankDataDTO();
            staffRankDataDto.setNumber(Optional.ofNullable(s).map(StaffRankQueryDataDTO::getNumber).orElse(Constants.LONG_ZERO))
                    .setWorkHour(Optional.ofNullable(s).map(StaffRankQueryDataDTO::getWorkHour).orElse(Constants.DOUBLE_ZERRO))
                    .setCode(Optional.ofNullable(s).map(StaffRankQueryDataDTO::getStaffDto).map(StaffDTO::getCode).orElse(""))
                    .setName(Optional.ofNullable(s).map(StaffRankQueryDataDTO::getStaffDto).map(StaffDTO::getName).orElse(""));
            staffRankDataDTOList.add(staffRankDataDto);
        });
        return staffRankDataDTOList;
    }

    /**
     * 报工统计看板表格数据
     *
     * @param staffPerformStatisticRequestDto 报工统计请求参数Dto
     * @return net.airuima.rbase.web.rest.report.dto.StaffPerformStatisticReportTableResultDTO 报工统计表格数据
     */
    @Transactional(readOnly = true)
    public StaffPerformStatisticReportTableResultDTO getStaffPerformStatisticTable(StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto) {
        // 查询时间解析
        parseTimeFinishTimeCategory(staffPerformStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = staffPerformStatisticRequestDto.getPedigreeId();
        // 产线id
        Long workLineId = staffPerformStatisticRequestDto.getWorkLineId();
        // 员工id
        Long staffId = staffPerformStatisticRequestDto.getStaffId();
        // 开始查询时间
        LocalDateTime startDateTime = staffPerformStatisticRequestDto.getStartDate();
        // 结束查询时间
        LocalDateTime endDateTime = staffPerformStatisticRequestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = staffPerformStatisticRequestDto.getExportStatus();
        // 报表类型
        Integer reportType = staffPerformStatisticRequestDto.getReportType();
        //当前页
        Integer currentPage = staffPerformStatisticRequestDto.getCurrentPage();
        // 分页大小
        Integer pageSize = staffPerformStatisticRequestDto.getPageSize();
        //获取报表表格
        return getStaffPerformStatisticTableResultDTO(pedigreeId, staffId, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize, reportType);
    }


    /**
     * 获取报工统计看板表格数据
     *
     * @param pedigreeId    产品谱系id
     * @param staffId       员工id
     * @param workLineId    产线id
     * @param startDateTime 开始查询时间
     * @param endDateTime   结束查询时间
     * @param exportStatus  是否导出
     * @param currentPage   当前页
     * @param pageSize      分页大小
     * @param reportType    报表类型
     * @return net.airuima.rbase.web.rest.report.dto.StaffPerformStatisticReportTableResultDTO 报工统计看板表格数据
     */
    private StaffPerformStatisticReportTableResultDTO getStaffPerformStatisticTableResultDTO(Long pedigreeId, Long staffId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize, Integer reportType) {
        // 分页查询
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        Specification<StaffPerform> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            // 查询报表类型
            if (!subWsProductionMode) {
                // 筛选工单投产
                Predicate subWorkSheetPredicate = criteriaBuilder.isNull(root.get(SUB_WORK_SHEET));
                predicateList.add(subWorkSheetPredicate);
                // 产品谱系筛选
                if (pedigreeId != null) {
                    Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                    predicateList.add(pedigreePredicate);
                }
                // 产线筛选
                if (workLineId != null) {
                    Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("workLine").get("id"), workLineId);
                    predicateList.add(workLinePredicate);
                }
            }
            if (subWsProductionMode) {
                // 筛选子工单投产
                Predicate subWorkSheetPredicate = criteriaBuilder.isNotNull(root.get(SUB_WORK_SHEET));
                predicateList.add(subWorkSheetPredicate);
                // 产品谱系筛选
                if (pedigreeId != null) {
                    Predicate pedigreePredicate = criteriaBuilder.equal(root.get(SUB_WORK_SHEET).get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                    predicateList.add(pedigreePredicate);
                }
                // 产线筛选
                if (workLineId != null) {
                    Predicate workLinePredicate = criteriaBuilder.equal(root.get(SUB_WORK_SHEET).get("workLine").get("id"), workLineId);
                    predicateList.add(workLinePredicate);
                }
            }
            //开始时间和结束时间筛选
            if (endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null ) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class), startDateTime));
            }
            // 员工筛选
            if (staffId != null) {
                Predicate staffIdPredicate = criteriaBuilder.equal(root.get("staffId"), staffId);
                predicateList.add(staffIdPredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).orderBy(criteriaBuilder.desc(root.get(RECORD_TIME))).getRestriction();
        };
        // 工单集合
        List<StaffPerform> staffPerformList;
        Page<StaffPerform> staffPerformPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            staffPerformList = staffPerformService.find(specification);
        } else {
            //分页查询
            staffPerformPage = staffPerformService.find(specification, PageRequest.of(currentPage, pageSize));
            staffPerformList = Optional.ofNullable(staffPerformPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        StaffPerformStatisticReportTableResultDTO tableResultDto = new StaffPerformStatisticReportTableResultDTO();
        // 转换员工产量为报工统计看板表格数据
        List<StaffPerformStatisticReportTableItemDTO> staffPerformStatisticReportTableItemList = covertToStaffPerformStatisticReportTableData(staffPerformList, subWsProductionMode);
        tableResultDto.setStaffPerformStatisticReportTableItemList(staffPerformStatisticReportTableItemList);
        // 设置分页数据
        tableResultDto.setCurrentPage(currentPage);
        tableResultDto.setPageSize(pageSize);
        tableResultDto.setCountSize(Boolean.TRUE.equals(exportStatus) ? Optional.ofNullable(staffPerformList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(staffPerformPage).map(Page::getTotalElements).orElse(0L));
        return tableResultDto;
    }

    /**
     * 转换员工产量为报工统计看板
     *
     * @param staffPerformList 员工产量查询结果
     * @param subWsProductionMode     投产类型
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.StaffPerformStatisticReportTableItemDTO> 报工统计表格明细集合
     */
    private List<StaffPerformStatisticReportTableItemDTO> covertToStaffPerformStatisticReportTableData(List<StaffPerform> staffPerformList, Boolean subWsProductionMode) {
        List<StaffPerformStatisticReportTableItemDTO> staffPerformStatisticReportTableItemList = Lists.newLinkedList();
        staffPerformList.forEach(s -> {
            StaffPerformStatisticReportTableItemDTO statisticReportTableItemDto = new StaffPerformStatisticReportTableItemDTO();
            WorkSheet workSheet = null;
            // 子工单
            SubWorkSheet subWorkSheet = Optional.ofNullable(s).map(StaffPerform::getSubWorkSheet).orElse(null);
            // 投产类型工单
            if (Boolean.FALSE.equals(subWsProductionMode)) {
                workSheet = Optional.ofNullable(s).map(StaffPerform::getWorkSheet).orElse(null);
            }
            // 投产类型子工单
            if (Boolean.TRUE.equals(subWsProductionMode)) {
                workSheet = Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getWorkSheet).orElse(null);
            }
            //工位
            WorkCell workCell = Optional.ofNullable(s).map(StaffPerform::getWorkCell).orElse(null);
            // 工序
            Step step = Optional.ofNullable(s).map(StaffPerform::getStep).orElse(null);
            // 合格数
            Long qualifiedNumber = Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO);
            // 不合格数
            Long unQualifiedNumber = Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO);
            //产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).orElse(null);
            //设置报工统计表格数据
            statisticReportTableItemDto.setRecordDateTime(Optional.ofNullable(s).map(StaffPerform::getRecordTime).orElse(null))
                    .setWorkSheetSerialNumber(Optional.ofNullable(workSheet).map(WorkSheet::getSerialNumber).orElse(null))
                    .setSubWorkSheetSerialNumber(Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getSerialNumber).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse(null))
                    .setWorkLineName(Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(WorkLine::getName).orElse(null))
                    .setStaffName(Optional.ofNullable(s).map(StaffPerform::getStaffDto).map(StaffDTO::getName).orElse(null))
                    .setStaffCode(Optional.ofNullable(s).map(StaffPerform::getStaffDto).map(StaffDTO::getCode).orElse(null))
                    .setWorkCellName(Optional.ofNullable(workCell).map(WorkCell::getName).orElse(null))
                    .setWorkCellCode(Optional.ofNullable(workCell).map(WorkCell::getCode).orElse(null))
                    .setStepCode(Optional.ofNullable(step).map(Step::getCode).orElse(null))
                    .setStepName(Optional.ofNullable(step).map(Step::getName).orElse(null))
                    .setNumber(qualifiedNumber + unQualifiedNumber);
            staffPerformStatisticReportTableItemList.add(statisticReportTableItemDto);
        });
        return staffPerformStatisticReportTableItemList;
    }


    /**
     * 查询时间范围解析
     *
     * @param staffPerformStatisticRequestDto 工单报表请求参数
     */
    private void parseTimeFinishTimeCategory(StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = staffPerformStatisticRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = staffPerformStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = staffPerformStatisticRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                staffPerformStatisticRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                staffPerformStatisticRequestDto.setEndDate(LocalDateTime.now());
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                staffPerformStatisticRequestDto.setStartDate(weekStart);
                staffPerformStatisticRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                staffPerformStatisticRequestDto.setStartDate(monthStart);
                staffPerformStatisticRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
        }
    }

    /**
     * 查询报工分布条形图数据
     *
     * @param staffPerformStatisticRequestDto 报工统计查询参数
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.DistributionChartDataDTO> 报工统计查询数据
     */
    @Transactional(readOnly = true)
    public List<DistributionChartDataDTO> getStaffPerformStatisticDistribution(StaffPerformStatisticRequestDTO staffPerformStatisticRequestDto) {
        //查询时间范围解析
        parseTimeFinishTimeCategory(staffPerformStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = staffPerformStatisticRequestDto.getPedigreeId();
        // 产线id
        Long workLineId = staffPerformStatisticRequestDto.getWorkLineId();
        // 员工id
        Long staffId = staffPerformStatisticRequestDto.getStaffId();
        // 开始查询时间
        LocalDateTime startDateTime = staffPerformStatisticRequestDto.getStartDate();
        // 结束查询时间
        LocalDateTime endDateTime = staffPerformStatisticRequestDto.getEndDate();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        List<DistributionChartDataDTO> distributionChartDataDTOList;
        // 查询报工统计条形图数据
        if (subWsProductionMode) {
            distributionChartDataDTOList = staffPerformRepository.findSubWorkSheetStaffPerformStatisticDistributionChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO);
        }else {
            distributionChartDataDTOList = staffPerformRepository.findWorkSheetStaffPerformStatisticDistributionChart(pedigreeId, workLineId, staffId, startDateTime, endDateTime, Constants.LONG_ZERO);
        }
        //展示的条形图数据
        List<DistributionChartDataDTO> displayChartDataDTOList = Lists.newLinkedList();
        displayChartDataDTOList.addAll(distributionChartDataDTOList);
        //日期填充
        List<String> existsDateList = displayChartDataDTOList.stream().map(DistributionChartDataDTO::getTime).collect(Collectors.toList());
        List<String> datesList = DateUtils.getDatesBetween(startDateTime, endDateTime,"yyyy-MM-dd");
        datesList.forEach(i -> {
            if (!existsDateList.contains(i)) {
                DistributionChartDataDTO distributionChartDataDto = new DistributionChartDataDTO();
                distributionChartDataDto.setTime(i).setNumber(Constants.LONG_ZERO);
                displayChartDataDTOList.add(distributionChartDataDto);
            }
        });
        displayChartDataDTOList.sort(Comparator.comparing(DistributionChartDataDTO::getTime));
        // 调整x轴时间样式
        displayChartDataDTOList.forEach(i -> i.setTime(i.getTime().length() > 5 ? i.getTime().substring(5) : ""));
        return displayChartDataDTOList;
    }


}
