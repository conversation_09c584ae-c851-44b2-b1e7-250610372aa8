package net.airuima.rbase.service.procedure.material;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialExchangeDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/17
 */
@FuncDefault
public interface IWsMaterialService {

    /**
     * 保存工单投料单
     * @param workSheet 工单
     * @param bomDtoList BOM信息列表
     */
    @FuncInterceptor("WorksheetMaterial")
    default List<WsMaterial> saveWsMaterial(WorkSheet workSheet, List<BomDTO> bomDtoList) {
        return null;
    }


    /**
     * 同步工单换料数据
     * <AUTHOR>
     * @param workSheetMaterialExchangeDtoList  上传的工单换料信息
     * @return List<SapBaseDTO>
     **/
    @FuncInterceptor("WorksheetMaterial")
    default List<SyncResultDTO> workSheetMaterialExchangeSync(List<SyncWorkSheetMaterialExchangeDTO> workSheetMaterialExchangeDtoList){
        return null;
    }
}
