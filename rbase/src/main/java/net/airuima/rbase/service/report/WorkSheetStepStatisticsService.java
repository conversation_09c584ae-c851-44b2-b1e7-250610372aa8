package net.airuima.rbase.service.report;

import net.airuima.rbase.domain.procedure.report.WorkSheetStepStatistics;
import net.airuima.rbase.repository.procedure.report.PedigreeStepStatisticsRepository;
import net.airuima.rbase.repository.procedure.report.WorkSheetStepStatisticsRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单工序生产在制统计Service
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetStepStatisticsService extends CommonJpaService<WorkSheetStepStatistics> {

    private final WorkSheetStepStatisticsRepository workSheetStepStatisticsRepository;
    private final PedigreeStepStatisticsRepository pedigreeStepStatisticsRepository;

    public WorkSheetStepStatisticsService(WorkSheetStepStatisticsRepository workSheetStepStatisticsRepository, PedigreeStepStatisticsRepository pedigreeStepStatisticsRepository) {
        this.workSheetStepStatisticsRepository = workSheetStepStatisticsRepository;
        this.pedigreeStepStatisticsRepository = pedigreeStepStatisticsRepository;
    }

    @Override
    public Page<WorkSheetStepStatistics> find(Specification<WorkSheetStepStatistics> spec, Pageable pageable) {
        return workSheetStepStatisticsRepository.findAll(spec, pageable);
    }

    @Override
    public List<WorkSheetStepStatistics> find(Specification<WorkSheetStepStatistics> spec) {
        return workSheetStepStatisticsRepository.findAll(spec);
    }

    @Override
    public Page<WorkSheetStepStatistics> findAll(Pageable pageable) {
        return workSheetStepStatisticsRepository.findAll(pageable);
    }

    /**
     * 每天0点定时清空除在线数量以外的其他数量
     */
    public void initNumber(){
        try {
            //每天零点将在线工单的投产数、合格数、不合格数、下交数清零
            workSheetStepStatisticsRepository.initWorkSheetStepStatisticsNumber();
        }catch (Exception e){

        }
        try {
            //每天零点将在线产品的投产数、合格数、不合格数、下交数清零
            pedigreeStepStatisticsRepository.initPedigreeStepStatisticsNumber();
        }catch (Exception e){

        }
        try {
            //每天零点将在线产品的投产数、合格数、不合格数、下交数清零
            pedigreeStepStatisticsRepository.initPedigreeStepStatisticsOnlineNumber();
        }catch (Exception e){

        }

    }

}
