package net.airuima.rbase.service.procedure.aps;

import net.airuima.constant.Constants;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.PlannedOrder;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.SaleOrderDetail;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.procedure.aps.PlannedOrderRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderDetailRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.SecurityUtils;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计划下单记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PlannedOrderService extends CommonJpaService<PlannedOrder> {

    private final PlannedOrderRepository plannedOrderRepository;
    private final SaleOrderDetailRepository saleOrderDetailRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;


    public PlannedOrderService(PlannedOrderRepository plannedOrderRepository, SaleOrderDetailRepository saleOrderDetailRepository) {
        this.plannedOrderRepository = plannedOrderRepository;
        this.saleOrderDetailRepository = saleOrderDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<PlannedOrder> find(Specification<PlannedOrder> spec, Pageable pageable) {
        return plannedOrderRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<PlannedOrder> find(Specification<PlannedOrder> spec) {
        return plannedOrderRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<PlannedOrder> findAll(Pageable pageable) {
        return plannedOrderRepository.findAll(pageable);
    }

    /**
     * 计划下单
     *
     * @param saleOrderDetailIds 订单详情ids
     * @since 1.8.1
     */
    public void plannedOrders(List<Long> saleOrderDetailIds) {

        if (!ValidateUtils.isValid(saleOrderDetailIds)) {
            throw new ResponseException("saleOrderDetailIdsEmpty", "计划下单信息不能为空");
        }
        saleOrderDetailIds = saleOrderDetailIds.stream().distinct().collect(Collectors.toList());

        //获取销售订单详情数据
        Map<Long, SaleOrderDetail> saleOrderDetailMap = saleOrderDetailRepository
                .findByIdInAndDeleted(saleOrderDetailIds, Constants.LONG_ZERO)
                .stream().collect(Collectors.toMap(SaleOrderDetail::getId, Function.identity()));

        //获取待计划下单的数据
        Map<Long, PlannedOrder> plannedOrderMap = plannedOrderRepository
                .findBySaleOrderDetailIdInAndStatusAndDeleted(saleOrderDetailIds, Constants.INT_ZERO, Constants.LONG_ZERO)
                .stream().collect(Collectors.toMap(plannedOrder ->
                        plannedOrder.getSaleOrderDetail().getId(), Function.identity()));


        AtomicReference<Long> planUserId = new AtomicReference<>();
        //获取当前登录用户id
        SecurityUtils.getCurrentUserLogin().ifPresent(login -> {
            UserDTO userDto = rbaseRbacProxy.getUserByLoginName(login);
            if (userDto != null && userDto.getId() != null) {
                planUserId.set(userDto.getId());
            }
        });

        List<PlannedOrder> plannedOrderList = Lists.newArrayList();

        //验证参数
        saleOrderDetailIds.forEach(saleOrderDetailId -> {

            SaleOrderDetail saleOrderDetail = saleOrderDetailMap.get(saleOrderDetailId);
            if (Objects.isNull(saleOrderDetail)) {
                throw new ResponseException("saleOrderDetailEmpty", "销售订单详情不存在");
            }
            PlannedOrder plannedOrder = plannedOrderMap.get(saleOrderDetailId);
            if (Objects.nonNull(plannedOrder)) {
                throw new ResponseException("plannedOrderExist", saleOrderDetail.getSaleOrder().getSerialNumber() + " 产品型号:"
                        + saleOrderDetail.getPedigree().getCode() + " | " + saleOrderDetail.getPedigree().getName() +
                        " 当前销售订单详情已计划下单,待分配,请勿重复下单");
            }
            PlannedOrder newPlannedOrder = new PlannedOrder();
            newPlannedOrder.setSaleOrderDetail(saleOrderDetail)
                    .setPlanUserId(planUserId.get())
                    .setStatus(Constants.INT_ZERO).setDeleted(Constants.LONG_ZERO);
            plannedOrderList.add(newPlannedOrder);
        });
        plannedOrderRepository.saveAll(plannedOrderList);
    }


    /**
     * 修改计划下单 状态，添加派单用户
     *
     * @param saleOrderDetailIds 销售订单详情ids
     * <AUTHOR>
     * @since 1.8.1
     */
    public void updatePlanOrdersStatus(List<Long> saleOrderDetailIds) {

        List<PlannedOrder> plannedOrders = plannedOrderRepository.findBySaleOrderDetailIdInAndStatusAndDeleted(
                saleOrderDetailIds, Constants.INT_ZERO, Constants.LONG_ZERO);

        //获取计划派单用户
        AtomicReference<Long> allocateUserId = new AtomicReference<>();
        //获取当前登录用户id
        SecurityUtils.getCurrentUserLogin().ifPresent(login -> {
            UserDTO userDto = rbaseRbacProxy.getUserByLoginName(login);
            if (userDto != null && userDto.getId() != null) {
                allocateUserId.set(userDto.getId());
            }
        });

        //修改计划下单列表状态
        if (ValidateUtils.isValid(plannedOrders)) {
            plannedOrders.forEach(plannedOrder -> {
                plannedOrder.setStatus(Constants.INT_ONE)
                        .setAllocateUserId(allocateUserId.get());
            });
            plannedOrderRepository.saveAll(plannedOrders);
        }
    }

    /**
     * 根据销售订单详情id列表获取销售订单列表
     *
     * @param saleOrderDetailIds 销售订单详情id列表
     * @return List<SaleOrder>
     * <AUTHOR>
     * @since 1.8.1
     */
    public List<SaleOrder> saleOrderDetailIdsGroupBySaleOrderId(List<Long> saleOrderDetailIds) {
        List<SaleOrder> saleOrders = Lists.newArrayList();
        List<SaleOrderDetail> saleOrderDetails = saleOrderDetailRepository.findByIdInAndDeleted(saleOrderDetailIds, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(saleOrderDetails)) {
            saleOrderDetails.stream().collect(Collectors.groupingBy(SaleOrderDetail::getSaleOrder)).forEach((saleOrder, details) -> {
                saleOrders.add(saleOrder.setSaleOrderDetailList(details));
            });
        }
        return saleOrders;
    }

    /**
     * 取消计划下单
     *
     * @param ids 计划下单id列表
     * <AUTHOR>
     * @since 1.8.1
     */
    public void cancelOrder(List<Long> ids) {
        plannedOrderRepository.updateStatus(Constants.INT_TWO, ids, Constants.LONG_ZERO);
    }
}
