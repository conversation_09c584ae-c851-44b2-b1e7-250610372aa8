package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.base.BaseDTO;


/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface ISubWorkSheetService {

    /**
     * 子工单中途结单
     *
     * @param subWorkSheetDto 结单信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     * <AUTHOR>
     * @date 2023/09/12
     */
    default BaseDTO forceFinishSubWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        return new BaseDTO(Constants.KO);
    }


    /**
     * 工单中途结单
     *
     * @param subWorkSheetDto 结单信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     * <AUTHOR>
     * @date 2023/09/12
     */
    default BaseDTO forceFinishWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        return new BaseDTO(Constants.KO);
    }
}
