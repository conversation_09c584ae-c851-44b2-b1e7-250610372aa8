package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailMaterialBatchRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情物料批次Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BatchWorkDetailMaterialBatchService extends CommonJpaService<BatchWorkDetailMaterialBatch> {
    private static final String BATCH_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH = "batchWorkDetailMaterialBatchEntityGraph";
    private final BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;

    public BatchWorkDetailMaterialBatchService(BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository) {
        this.batchWorkDetailMaterialBatchRepository = batchWorkDetailMaterialBatchRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<BatchWorkDetailMaterialBatch> find(Specification<BatchWorkDetailMaterialBatch> spec, Pageable pageable) {
        return batchWorkDetailMaterialBatchRepository.findAll(spec, pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<BatchWorkDetailMaterialBatch> find(Specification<BatchWorkDetailMaterialBatch> spec) {
        return batchWorkDetailMaterialBatchRepository.findAll(spec,new NamedEntityGraph(BATCH_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<BatchWorkDetailMaterialBatch> findAll(Pageable pageable) {
        return batchWorkDetailMaterialBatchRepository.findAll(pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    /**
     * 根据工单工序生产过程数据主键ID获取工序用料物料信息
     * @param batchWorkDetailId     批量详情主键ID
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch.MaterialBatchBaseRecordInfo> 工序用料信息列表
     * <AUTHOR>
     * @date 2024/1/29
     */
    @Transactional(readOnly = true)
    public List<BatchWorkDetailMaterialBatch.MaterialBatchBaseRecordInfo> findMaterialBatchByBatchWorkDetailId(long batchWorkDetailId){
        List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatches = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetailId, Constants.LONG_ZERO);
        List<BatchWorkDetailMaterialBatch.MaterialBatchBaseRecordInfo> materialBatchBaseRecordInfos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(batchWorkDetailMaterialBatches)){
            batchWorkDetailMaterialBatches.forEach(batchWorkDetailMaterialBatch -> materialBatchBaseRecordInfos.add(new BatchWorkDetailMaterialBatch.MaterialBatchBaseRecordInfo(batchWorkDetailMaterialBatch.getMaterialDto().getName()
                    ,batchWorkDetailMaterialBatch.getMaterialDto().getCode(),batchWorkDetailMaterialBatch.getMaterialBatch(),batchWorkDetailMaterialBatch.getNumber())));
        }
        return materialBatchBaseRecordInfos;
    }

}
