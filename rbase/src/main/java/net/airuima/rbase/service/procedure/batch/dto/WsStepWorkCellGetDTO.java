package net.airuima.rbase.service.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;

import java.util.List;
import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
public class WsStepWorkCellGetDTO {

    /**
     * 工序信息
     */
    @Schema(description = "工序信息")
    private StepInfo stepInfo;

    /**
     * 工位列表
     */
    @Schema(description = "工位列表")
    private List<WorkCellInfo> workCellInfoList;


    public StepInfo getStepInfo() {
        return stepInfo;
    }

    public WsStepWorkCellGetDTO setStepInfo(StepInfo stepInfo) {
        this.stepInfo = stepInfo;
        return this;
    }

    public List<WorkCellInfo> getWorkCellInfoList() {
        return workCellInfoList;
    }

    public WsStepWorkCellGetDTO setWorkCellInfoList(List<WorkCellInfo> workCellInfoList) {
        this.workCellInfoList = workCellInfoList;
        return this;
    }

    @Schema(description = "工序Info")
    public static class StepInfo{
        /**
         * 工序Id
         */
        @Schema(description = "工序Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String code;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String name;

        public Long getId() {
            return id;
        }

        public StepInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepInfo setName(String name) {
            this.name = name;
            return this;
        }

        public StepInfo() {
        }

        public StepInfo(Step step) {
            this.id = step.getId();
            this.code = step.getCode();
            this.name = step.getName();
        }
    }
    @Schema(description = "工位Info")
    public static class WorkCellInfo{
        /**
         * 工位Id
         */
        @Schema(description = "工位Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String code;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String name;

        public Long getId() {
            return id;
        }

        public WorkCellInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkCellInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkCellInfo setName(String name) {
            this.name = name;
            return this;
        }

        public WorkCellInfo() {
        }

        public WorkCellInfo(WorkCell workCell) {
            this.id = workCell.getId();
            this.code = workCell.getCode();
            this.name = workCell.getName();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            WorkCellInfo that = (WorkCellInfo) o;
            return Objects.equals(id, that.id);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }
    }
}
