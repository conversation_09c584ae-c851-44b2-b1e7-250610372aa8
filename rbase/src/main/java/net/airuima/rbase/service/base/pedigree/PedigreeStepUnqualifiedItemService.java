package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.PedigreeStepUnqualifiedExcelConstants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良现象Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Order(0)
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepUnqualifiedItemService extends CommonJpaService<PedigreeStepUnqualifiedItem> implements IPedigreeStepUnqualifiedItemService {
    private final PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository;

    private final String PEDIGREE_STEP_UNQUALIFIED_ITEM_GRAPH = "pedigreeStepUnqualifiedItemEntityGraph";
    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;


    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public PedigreeStepUnqualifiedItemService(PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository) {
        this.pedigreeStepUnqualifiedItemRepository = pedigreeStepUnqualifiedItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStepUnqualifiedItem> find(Specification<PedigreeStepUnqualifiedItem> spec, Pageable pageable) {
        return pedigreeStepUnqualifiedItemRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_STEP_UNQUALIFIED_ITEM_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeStepUnqualifiedItem> find(Specification<PedigreeStepUnqualifiedItem> spec) {
        return pedigreeStepUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_UNQUALIFIED_ITEM_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStepUnqualifiedItem> findAll(Pageable pageable) {
        return pedigreeStepUnqualifiedItemRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_UNQUALIFIED_ITEM_GRAPH));
    }


    /**
     * 根据产品谱系与工序Id查询不良现象
     *
     * @param pedigreeId 产品谱系ID
     * @param workFlowId 工艺路线ID
     * @param stepId     工序ID
     * @param clientId   客户id
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 不良项目集合
     * <AUTHOR>
     * @date 2020-12-30
     **/
    @Transactional(readOnly = true)
    public List<UnqualifiedItem> findUnqualifiedItemByPedigreeIdAndStepId(Long pedigreeId, Long workFlowId, Long stepId, Long clientId) {
        List<PedigreeStepUnqualifiedItem> validPedigreeStepUnqualifiedItem = this.getValidPedigreeStepUnqualifiedItem(pedigreeId, workFlowId, stepId, clientId);
        if (CollectionUtils.isEmpty(validPedigreeStepUnqualifiedItem)) {
            return Lists.newArrayList();
        }
        return validPedigreeStepUnqualifiedItem.stream().map(PedigreeStepUnqualifiedItem::getUnqualifiedItem).collect(Collectors.toList());
    }

    /**
     * 批量更新工序不良项目
     * @param pedigreeStepUnqualifiedItems 参数列表
     */
    public void batchUpdate(List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItems){
        if(CollectionUtils.isEmpty(pedigreeStepUnqualifiedItems)){
            return;
        }
        pedigreeStepUnqualifiedItems.forEach(this::saveInstance);
    }

    /**
     * 保存工序不良项目配置
     *
     * @param pedigreeStepUnqualifiedItem 工序不良项目配置
     */
    public BaseResultDTO saveInstance(PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem) {
        if(checkPedigreeStepUnqualifiedItem(pedigreeStepUnqualifiedItem)){
            return new BaseResultDTO(net.airuima.constant.Constants.KO, "该工序不良项目配置已存在", PedigreeStepUnqualifiedItem.class.getSimpleName(), "error.pedigreeStepUnqualifiedItemExists");
        }
        if(Objects.isNull(pedigreeStepUnqualifiedItem.getPriorityElementConfig())){
            PriorityElementConfig priorityElementConfig = getPedigreeStepUnqualifiedItemPriority(Objects.nonNull(pedigreeStepUnqualifiedItem.getPedigree())?pedigreeStepUnqualifiedItem.getPedigree().getId():null,
                    Objects.nonNull(pedigreeStepUnqualifiedItem.getWorkFlow())?pedigreeStepUnqualifiedItem.getWorkFlow().getId():null,
                    Objects.nonNull(pedigreeStepUnqualifiedItem.getClientId())?pedigreeStepUnqualifiedItem.getClientId():null);
            if(Objects.isNull(priorityElementConfig)){
                return new BaseResultDTO(net.airuima.constant.Constants.KO, "没有匹配的组合条件", StepWarningStandard.class.getSimpleName(), "error.noMatchCombination");
            }
            pedigreeStepUnqualifiedItem.setPriorityElementConfig(priorityElementConfig);
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(pedigreeStepUnqualifiedItem.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (!priorityElementConfigOptional.isPresent()) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", StepWarningStandard.class.getSimpleName(), "error.noMatchCombination");
        }
        pedigreeStepUnqualifiedItem.setPriorityElementConfig(priorityElementConfigOptional.get());
        PedigreeStepUnqualifiedItem savePedigreeStepUnqualifiedItem = this.save(pedigreeStepUnqualifiedItem);
        return new BaseResultDTO(Constants.OK, savePedigreeStepUnqualifiedItem);
    }

    /**
     * 校验工序不良项目配置是否已经存在
     *
     * @param pedigreeStepUnqualifiedItem 工序不良项目配置
     * @return java.lang.Boolean true 存在 false不存在
     */
    private Boolean checkPedigreeStepUnqualifiedItem(PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem) {
        // 工序id
        Long stepId = null != pedigreeStepUnqualifiedItem.getStep() ? pedigreeStepUnqualifiedItem.getStep().getId() : null;
        // 产品谱系id
        Long pedigreeId = null != pedigreeStepUnqualifiedItem.getPedigree() ? pedigreeStepUnqualifiedItem.getPedigree().getId() : null;
        // 工艺路线id
        Long workFlowId = null != pedigreeStepUnqualifiedItem.getWorkFlow() ? pedigreeStepUnqualifiedItem.getWorkFlow().getId() : null;
        // 不良id
        Long unqualifiedItemId = null != pedigreeStepUnqualifiedItem.getUnqualifiedItem() ? pedigreeStepUnqualifiedItem.getUnqualifiedItem().getId():null;
        // 客户id
        Long clientId = null != pedigreeStepUnqualifiedItem.getClientId() ? pedigreeStepUnqualifiedItem.getClientId() : null;
        PedigreeStepUnqualifiedItem result = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndAndClientIdAndUnqualifiedItemIdAndDeleted(pedigreeId,workFlowId,stepId,clientId,unqualifiedItemId,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeStepUnqualifiedItem.getId()) && Objects.nonNull(result)) {
            return true;
        }
        return Objects.nonNull(pedigreeStepUnqualifiedItem.getId()) && Objects.nonNull(result) && !result.getId().equals(pedigreeStepUnqualifiedItem.getId());
    }

    /**
     * 启用/禁用指定产品谱系工序不良现象
     *
     * @param pedigreeStepUnqualifiedItemId
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     **/
    public BaseResultDTO enableByPedigreeStepUnqualifiedItemId(Long pedigreeStepUnqualifiedItemId) {
        PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemRepository.findByIdAndDeleted(pedigreeStepUnqualifiedItemId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeStepUnqualifiedItem)) {
            return new BaseResultDTO(Constants.KO, "产品谱系工序不良现象不存在", PedigreeStepUnqualifiedItem.class.getSimpleName(), "error.pedigreeStepUnqualifiedItemNotExist");
        }
        pedigreeStepUnqualifiedItem.setIsEnable(!pedigreeStepUnqualifiedItem.getIsEnable());
        PedigreeStepUnqualifiedItem savePedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemRepository.save(pedigreeStepUnqualifiedItem);
        return new BaseResultDTO(Constants.OK, savePedigreeStepUnqualifiedItem);
    }


    /**
     * 根据产品谱系id和工艺路线id和工序id和客户id获取有效的工序不良项目配置
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param stepId     工序id
     * @param clientId   客户id
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 工序不良项目配置集合
     */
    public List<PedigreeStepUnqualifiedItem> getValidPedigreeStepUnqualifiedItem(Long pedigreeId, Long workFlowId, Long stepId, Long clientId) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, Constants.LONG_ZERO);
        if (!pedigreeOptional.isPresent()) {
            return Lists.newArrayList();
        }
        Pedigree pedigree = pedigreeOptional.get();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(pedigree);
        if (CollectionUtils.isEmpty(pedigreeIdList)) {
            return Lists.newArrayList();
        }
        //递归获取产品谱系所有父级ID集合
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndPriorityElementConfigTargetAndIsEnableAndDeleted
                (pedigreeIdList, workFlowId, stepId, clientId, Constants.INT_FOUR, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(pedigreeIdList)) {
            pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        } else {
            pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemList.stream().filter(i -> Objects.nonNull(i.getPedigree())).collect(Collectors.toList());
        }
        //取到优先级最高的工序不良配置列表
        Map<Integer, List<PedigreeStepUnqualifiedItem>> priorityToItems = pedigreeStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(p -> p.getPriorityElementConfig().getPriority()));
        Integer minPriority = Collections.min(priorityToItems.keySet());
        pedigreeStepUnqualifiedItemList = priorityToItems.get(minPriority);
        if (!CollectionUtils.isEmpty(pedigreeIdList)) {
            // 根据产品谱系层级分组
            Map<Integer, List<PedigreeStepUnqualifiedItem>> priorityToItemsMap = pedigreeStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(p -> p.getPedigree().getType()));
            // 取产品谱系层级最大的
            Integer maxPedigreeType = Collections.max(priorityToItemsMap.keySet());
            pedigreeStepUnqualifiedItemList = priorityToItemsMap.get(maxPedigreeType);

        }
        return CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemList) ? Lists.newArrayList() : pedigreeStepUnqualifiedItemList;
    }


    /**
     * 根据工序id查询适用于所有产品谱系和所有工艺路线的工序不良项目
     *
     * @param stepId   工序id
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 工序不良项目
     */
    @Transactional(readOnly = true)
    public List<UnqualifiedItem> findByStepId(Long stepId) {
        List<PedigreeStepUnqualifiedItem> validPedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdNullAndWorkFlowIdNullAndStepIdAndClientIdNullAndIsEnableAndDeleted(stepId, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(validPedigreeStepUnqualifiedItemList)) {
            return Lists.newArrayList();
        }
        // 不良项目集合
        return validPedigreeStepUnqualifiedItemList.stream().map(PedigreeStepUnqualifiedItem::getUnqualifiedItem).collect(Collectors.toList());
    }

    /**
     *  根据产品谱系ID、客户ID、工艺路线ID、工序ID查询工序不良配置
     * @param pedigreeId 产品谱系ID
     * @param clientId 客户ID
     * @param workFlowId 工艺路线ID
     * @param stepId 工序ID
     * @return List<PedigreeStepUnqualifiedItem>
     */
    public List<PedigreeStepUnqualifiedItem> findAllByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(Long pedigreeId, Long clientId, Long workFlowId, Long stepId){
        return pedigreeStepUnqualifiedItemRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId,workFlowId,stepId,clientId, net.airuima.constant.Constants.LONG_ZERO);
    }


    /**
     * 工序不良配置导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object> 行数据
     */
    public List<Map<String, Object>> importPedigreeStepUnqualifiedItemExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(PedigreeStepUnqualifiedExcelConstants.PEDIGREE_CODE));
            // 产品谱系
            Pedigree pedigree = null;
            if (!StringUtils.isEmpty(pedigreeCode) && !"null".equals(pedigreeCode)) {
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO);
                if (pedigreeOptional.isPresent()) {
                    pedigree = pedigreeOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工艺路线编码并转换为对象
            String workFlowCode = String.valueOf(row.get(PedigreeStepUnqualifiedExcelConstants.WORK_FLOW_CODE));
            WorkFlow workFlow = null;
            if (!StringUtils.isEmpty(workFlowCode) && !"null".equals(workFlowCode)) {
                Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(workFlowCode, Constants.LONG_ZERO);
                if (workFlowOptional.isPresent()) {
                    workFlow = workFlowOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工序编码并转换为对象
            String stepCode = String.valueOf(row.get(PedigreeStepUnqualifiedExcelConstants.STEP_CODE));
            Step step = null;
            if (StringUtils.isEmpty(stepCode) || "null".equals(stepCode)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因工序不存在");
                illegalDataList.add(row);
                return;
            } else {
                Optional<Step> stepOptional = stepRepository.findByCodeAndDeleted(stepCode, Constants.LONG_ZERO);
                if (stepOptional.isPresent()) {
                    step = stepOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工序不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取不良编码并转换为对象
            String unqualifiedItemCode = String.valueOf(row.get(PedigreeStepUnqualifiedExcelConstants.UNQUALIFIED_ITEM_CODE));
            UnqualifiedItem unqualifiedItem = null;
            if (StringUtils.isEmpty(unqualifiedItemCode) || "null".equals(unqualifiedItemCode)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因不良不存在");
                illegalDataList.add(row);
                return;
            } else {
                Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository.findByCodeAndDeleted(unqualifiedItemCode, Constants.LONG_ZERO);
                if (unqualifiedItemOptional.isPresent()) {
                    unqualifiedItem = unqualifiedItemOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因不良不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            //获取客户代码并转为对象
            String clientCode = String.valueOf(row.get((PedigreeStepUnqualifiedExcelConstants.CLIENT_CODE)));
            ClientDTO clientDto = StringUtils.isEmpty(clientCode) || "null".equals(clientCode) ? null : rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO);
            if ((!StringUtils.isEmpty(clientCode) && !"null".equals(clientCode)) && (clientDto == null || clientDto.getId() == null)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因客户编码不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取是否启用
            Boolean isEnable = String.valueOf(row.get(PedigreeStepUnqualifiedExcelConstants.IS_ENABLE)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 工序id
            Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
            // 不良id
            Long unqualifiedItemId = Optional.ofNullable(unqualifiedItem).map(UnqualifiedItem::getId).orElse(null);
            // 客户id
            Long clientId = Optional.ofNullable(clientDto).map(ClientDTO::getId).orElse(null);
            // 查询不良配置是否已经存在
            PedigreeStepUnqualifiedItem queryPedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndAndClientIdAndUnqualifiedItemIdAndDeleted(pedigreeId,workFlowId,stepId,clientId,unqualifiedItemId,Constants.LONG_ZERO).orElse(null);
            // 获取不良条件优先级配置
            PriorityElementConfig pedigreeStepUnqualifiedItemPriority = getPedigreeStepUnqualifiedItemPriority(pedigreeId, workFlowId, clientId);
            if (pedigreeStepUnqualifiedItemPriority == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 该组合条件优先级未配置");
                illegalDataList.add(row);
                return;
            }
            // 保存工序不良
            saveUnqualifiedItem(pedigree, workFlow, step, unqualifiedItem, isEnable, queryPedigreeStepUnqualifiedItem, pedigreeStepUnqualifiedItemPriority, clientId);
        });
        return illegalDataList;
    }

    /**
     * 保存工序不良配置
     *
     * @param pedigree                            产品谱系
     * @param workFlow                            工艺路线
     * @param step                                工序
     * @param unqualifiedItem                     不良项目
     * @param isEnable                            是否启用
     * @param queryPedigreeStepUnqualifiedItem    查询的不良配置
     * @param pedigreeStepUnqualifiedItemPriority 条件优先级
     * @param clientId                            客户id
     */
    private void saveUnqualifiedItem(Pedigree pedigree, WorkFlow workFlow, Step step, UnqualifiedItem unqualifiedItem, Boolean isEnable, PedigreeStepUnqualifiedItem queryPedigreeStepUnqualifiedItem, PriorityElementConfig pedigreeStepUnqualifiedItemPriority, Long clientId) {
        if (Objects.nonNull(queryPedigreeStepUnqualifiedItem)) {
            queryPedigreeStepUnqualifiedItem.setPriorityElementConfig(pedigreeStepUnqualifiedItemPriority).setClientId(clientId)
                    .setIsEnable(isEnable);
            pedigreeStepUnqualifiedItemRepository.save(queryPedigreeStepUnqualifiedItem);
        } else {
            PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = new PedigreeStepUnqualifiedItem();
            pedigreeStepUnqualifiedItem
                    .setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setStep(step)
                    .setUnqualifiedItem(unqualifiedItem)
                    .setPriorityElementConfig(pedigreeStepUnqualifiedItemPriority)
                    .setClientId(clientId)
                    .setIsEnable(isEnable);
            pedigreeStepUnqualifiedItem.setDeleted(Constants.LONG_ZERO);
            pedigreeStepUnqualifiedItemRepository.save(pedigreeStepUnqualifiedItem);
        }
    }

    /**
     * 获取工序不良条件优先级配置
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param clientId   客户id
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    private PriorityElementConfig getPedigreeStepUnqualifiedItemPriority(Long pedigreeId, Long workFlowId, Long clientId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(workFlowId)) {
            combination.add(Constants.WORKFLOW_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        combination.add(Constants.STEP_ELEMENT);
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_FOUR,combination);
    }

    /**
     * 删除工序不良配置
     *
     * @param id 工序不良配置id
     */
    public void deleteEntity(Long id) {
        pedigreeStepUnqualifiedItemRepository.deleteByPedigreeStepUnqualifiedItemIdAndDeleted(id);
    }

    /**
     * 详情重写
     *
     * @param id 工序不良配置ID
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem 工序不良配置
     */
    @Transactional(readOnly = true)
    @FetchMethod
    public PedigreeStepUnqualifiedItem get(Long id) {
        return pedigreeStepUnqualifiedItemRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElse(null);
    }
}
