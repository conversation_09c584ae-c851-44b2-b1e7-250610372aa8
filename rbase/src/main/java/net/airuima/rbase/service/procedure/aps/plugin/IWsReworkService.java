package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.aps.WsRework;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *  返工单与正常单对应关系接口
 * <AUTHOR>
 * @date 2023/3/20
 */
@FuncDefault
public interface IWsReworkService {

    /**
     * 返工单关联正常单列表
      * @param wsReworkList 返工单关联正常单列表
     * <AUTHOR>
     * @date  2023/3/21
     */
    @FuncInterceptor(value = "SyncReworkSheetErp")
    default void syncReworkSheetToErp(List<WsRework> wsReworkList){
    }
}
