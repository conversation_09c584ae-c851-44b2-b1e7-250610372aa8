package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailMaterialBatchRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情物料批次表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailMaterialBatchService extends CommonJpaService<ContainerDetailMaterialBatch> {
    private static final String CONTAINER_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH = "containerDetailMaterialBatchEntityGraph";
    private final ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;

    public ContainerDetailMaterialBatchService(ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository) {
        this.containerDetailMaterialBatchRepository = containerDetailMaterialBatchRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailMaterialBatch> find(Specification<ContainerDetailMaterialBatch> spec, Pageable pageable) {
        return containerDetailMaterialBatchRepository.findAll(spec, pageable,new NamedEntityGraph(CONTAINER_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<ContainerDetailMaterialBatch> find(Specification<ContainerDetailMaterialBatch> spec) {
        return containerDetailMaterialBatchRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailMaterialBatch> findAll(Pageable pageable) {
        return containerDetailMaterialBatchRepository.findAll(pageable,new NamedEntityGraph(CONTAINER_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

}
