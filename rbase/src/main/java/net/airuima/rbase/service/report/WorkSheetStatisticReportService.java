package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.web.rest.report.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单统计报表Service
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetStatisticReportService {

    private final SubWorkSheetRepository subWorkSheetRepository;

    private final WorkSheetRepository workSheetRepository;


    private final SubWorkSheetService subWorkSheetService;

    private final WorkSheetService workSheetService;

    private static final String WORK_SHEET = "workSheet";
    private static final String PLAN_END_DATE = "planEndDate";

    public WorkSheetStatisticReportService(SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository, SubWorkSheetService subWorkSheetService, WorkSheetService workSheetService) {
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.subWorkSheetService = subWorkSheetService;
        this.workSheetService = workSheetService;
    }

    /**
     * 工单报表统计图形
     *
     * @param workSheetStatisticRequestDto 工单报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportChartResultDTO 工单报表统计图表返回结果
     */
    @Transactional(readOnly = true)
    public WorkSheetStatisticReportChartResultDTO workSheetFinishReportChart(WorkSheetStatisticReportRequestDTO workSheetStatisticRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(workSheetStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = workSheetStatisticRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = workSheetStatisticRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = workSheetStatisticRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = workSheetStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetStatisticRequestDto.getEndDate();
        //报表类型
        Integer reportType = workSheetStatisticRequestDto.getReportType();
        WorkSheetStatisticReportChartResultDTO workSheetStatisticReportChartResultDto = null;
        //获取子工单报表
        if (WorkSheetReportTypeEnum.SUB_WORK_SHEET.getCategory() == reportType) {
            workSheetStatisticReportChartResultDto = getSubWorkSheetStatisticReportChartResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime);
        }
        //获取工单报表
        if (WorkSheetReportTypeEnum.WORK_SHEET.getCategory() == reportType) {
            workSheetStatisticReportChartResultDto = getWorkSheetStatisticReportChartResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime);
        }
        return workSheetStatisticReportChartResultDto;
    }

    /**
     * 工单报表表格数据
     *
     * @param workSheetStatisticRequestDto 工单报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportTableResultDTO 工单报表表格数据返回结果
     */
    @Transactional(readOnly = true)
    @FetchMethod
    public WorkSheetStatisticReportTableResultDTO workSheetFinishReportTable(WorkSheetStatisticReportRequestDTO workSheetStatisticRequestDto,boolean ignoreCancel) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(workSheetStatisticRequestDto);
        // 产品谱系id
        Long pedigreeId = workSheetStatisticRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = workSheetStatisticRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = workSheetStatisticRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = workSheetStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetStatisticRequestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = workSheetStatisticRequestDto.getExportStatus();
        //当前页
        Integer currentPage = workSheetStatisticRequestDto.getCurrentPage();
        //页数
        Integer pageSize = workSheetStatisticRequestDto.getPageSize();
        //报表类型
        Integer reportType = workSheetStatisticRequestDto.getReportType();
        WorkSheetStatisticReportTableResultDTO workSheetStatisticReportTableResultDto = null;
        //获取子工单报表
        if (WorkSheetReportTypeEnum.SUB_WORK_SHEET.getCategory() == reportType) {
            workSheetStatisticReportTableResultDto = getSubWorkSheetStatisticReportTableResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize,ignoreCancel);
        }
        //获取工单报表
        if (WorkSheetReportTypeEnum.WORK_SHEET.getCategory() == reportType) {
            workSheetStatisticReportTableResultDto = getWorkSheetStatisticReportTableResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize,ignoreCancel);
        }
        return workSheetStatisticReportTableResultDto;
    }


    /**
     * 获取工单报表图形数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportChartResultDTO 报表返回结果图形数据
     */
    private WorkSheetStatisticReportChartResultDTO getWorkSheetStatisticReportChartResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        WorkSheetStatisticReportChartResultDTO reportResultResultDto = new WorkSheetStatisticReportChartResultDTO();
        //总的工单数量
        Integer workSheetNumberCount = Optional.ofNullable(workSheetRepository.countWorkSheetNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        //计划完成数
        Integer workSheetPlanNumber = Optional.ofNullable(workSheetRepository.countWorkSheetPlanNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        // 实际完成数
        Integer workSheetActualFinishNumber = Optional.ofNullable(workSheetRepository.countWorkSheetActualFinishNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        double finishRate = 0d;
        //计算完成率
        if (Constants.INT_ZERO != workSheetPlanNumber) {
            finishRate = BigDecimal.valueOf((float) (workSheetActualFinishNumber * 100) / workSheetPlanNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        //设置工单报表条形图数据
        setWorkSheetChartData(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, reportResultResultDto);
        //设置工单报表数据
        reportResultResultDto.setNumber(workSheetNumberCount).setPlanFinishNumber(workSheetPlanNumber).setActualFinishNumber(workSheetActualFinishNumber).setFinishRate(finishRate);
        return reportResultResultDto;
    }

    /**
     * 设置工单条形图数据
     *
     * @param pedigreeId            设备谱系id
     * @param organizationId        部门id
     * @param workLineId            产线id
     * @param startDateTime         查询开始时间
     * @param endDateTime           查询结束时间
     * @param reportResultResultDto 报表数据
     */
    private void setWorkSheetChartData(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, WorkSheetStatisticReportChartResultDTO reportResultResultDto) {
        //查询按部门分类的完成数据
        List<OrganizationChartResultDTO> organizationResultList = workSheetRepository.findOrganizationChart(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        List<OrganizationFinishInfoDTO> organizationFinishInfoList = convertOrganizationQueryToChartData(organizationResultList);
        //查询按产线分类的完成数据
        List<WorkLineFinishInfoChartResultDTO> workLineFinishInfoChartResultList = workSheetRepository.findWorkLineChart(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        List<WorkLineFinishInfoDTO> workLineFinishInfoList = convertWorkLineFinishQueryToChartInfo(workLineFinishInfoChartResultList);
        reportResultResultDto.setOrganizationFinishInfoDTOList(organizationFinishInfoList).setWorkLineFinishInfoDTOList(workLineFinishInfoList);
    }

    /**
     * 获取子工单报表图表数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportChartResultDTO 报表返回结果图形数据
     */
    private WorkSheetStatisticReportChartResultDTO getSubWorkSheetStatisticReportChartResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        WorkSheetStatisticReportChartResultDTO reportResultResultDto = new WorkSheetStatisticReportChartResultDTO();
        //总的子工单数量
        Integer subWorkSheetNumberCount = Optional.ofNullable(subWorkSheetRepository.countSubWorkSheetNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        //计划完成数
        Integer subWorkSheetPlanNumber = Optional.ofNullable(subWorkSheetRepository.countSubWorkSheetPlanNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        // 实际完成数
        Integer subWorkSheetActualFinishNumber = Optional.ofNullable(subWorkSheetRepository.countSubWorkSheetActualFinishNumberIgnoreCancel(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO)).orElse(Constants.INT_ZERO);
        double finishRate = 0d;
        //计算完成率
        if (Constants.INT_ZERO != subWorkSheetPlanNumber) {
            finishRate = BigDecimal.valueOf((float) (subWorkSheetActualFinishNumber * 100) / subWorkSheetPlanNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        //设置子工单报表条形图数据
        setSubWorkSheetChartData(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, reportResultResultDto);
        //设置工单报表数据
        reportResultResultDto.setNumber(subWorkSheetNumberCount).setPlanFinishNumber(subWorkSheetPlanNumber).setActualFinishNumber(subWorkSheetActualFinishNumber).setFinishRate(finishRate);
        return reportResultResultDto;
    }

    /**
     * 获取子工单报表表格数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @param exportStatus   是否导出
     * @param currentPage    当前页
     * @param pageSize       页数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportTableResultDTO 报表返回结果表格数据
     */
    private WorkSheetStatisticReportTableResultDTO getSubWorkSheetStatisticReportTableResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime,
                                                                                                LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize,boolean ignoreCancel) {
        WorkSheetStatisticReportTableResultDTO reportTableResultDto = new WorkSheetStatisticReportTableResultDTO();
        // 分页查询
        Specification<SubWorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            if(ignoreCancel) {
                //除去已取消状态的
                Predicate statusPredicate = criteriaBuilder.notEqual(root.get("status"), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
                predicateList.add(statusPredicate);
            }
            Predicate categoryPredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("category"), WsEnum.NORMAL_WS.getCategory());
            predicateList.add(categoryPredicate);
            //开始时间和结束时间筛选
            if ( endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(PLAN_END_DATE).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null ) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(PLAN_END_DATE).as(LocalDateTime.class), startDateTime));
            }
            // 产品谱系筛选
            if (pedigreeId != null) {
                Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                predicateList.add(pedigreePredicate);
            }
            // 产线筛选
            if (workLineId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("workLine").get("id"), workLineId);
                predicateList.add(workLinePredicate);
            }
            //部门筛选
            if (organizationId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("organizationId"), organizationId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        List<SubWorkSheet> subWorkSheetList;
        Page<SubWorkSheet> subWorkSheetPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            subWorkSheetList = subWorkSheetService.find(specification);
        } else {
            // 分页查询
            subWorkSheetPage = subWorkSheetService.find(specification, PageRequest.of(currentPage, pageSize));
            subWorkSheetList = Optional.ofNullable(subWorkSheetPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        //设置子工单报表数据
        reportTableResultDto.setWorkSheetStatisticReportTableItemList(convertToSubWorkSheetStatisticTableData(subWorkSheetList));
        //设置子工单表格分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(exportStatus ? Optional.ofNullable(subWorkSheetList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(subWorkSheetPage).map(Page::getTotalElements).orElse(0L));
        return reportTableResultDto;
    }

    /**
     * 转换子工单查询结果为统计表格数据
     *
     * @param subWorkSheetList 子工单查询结果
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportTableItemDTO>  统计表格数据
     */
    private List<WorkSheetStatisticReportTableItemDTO> convertToSubWorkSheetStatisticTableData(List<SubWorkSheet> subWorkSheetList) {
        // 统计表格数据集合
        List<WorkSheetStatisticReportTableItemDTO> reportTableDataList = Lists.newLinkedList();
        subWorkSheetList.forEach(s -> {
            // 工单
            WorkSheet workSheet = Optional.ofNullable(s).map(i -> i.getWorkSheet()).orElse(null);
            // 产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(i -> i.getPedigree()).orElse(null);
            // 表格明细
            WorkSheetStatisticReportTableItemDTO reportTableItem = new WorkSheetStatisticReportTableItemDTO();
            reportTableItem.setWorkSheetSerialNumber(Optional.ofNullable(workSheet).map(i -> i.getSerialNumber()).orElse(null))
                    .setSubWorkSheetSerialNumber(Optional.ofNullable(s).map(i -> i.getSerialNumber()).orElse(null))
                    .setStatus(Optional.ofNullable(s).map(i -> i.getStatus()).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(i -> i.getCode()).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(i -> i.getName()).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(i -> i.getSpecification()).orElse(null))
                    .setOrganizationName(Optional.ofNullable(workSheet).map(i -> i.getOrganizationDto()).map(o -> o.getName()).orElse(null))
                    .setWorkLineName(Optional.ofNullable(s).map(i -> i.getWorkLine()).map(o -> o.getName()).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getNumber())).orElse(Constants.LONG_ZERO))
                    .setQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setUnqualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setPlanStartDate(Optional.ofNullable(s).map(i -> i.getPlanStartDate()).orElse(null))
                    .setPlanEndDate(Optional.ofNullable(s).map(i -> i.getPlanEndDate()).orElse(null));
            reportTableDataList.add(reportTableItem);
        });
        return reportTableDataList;
    }

    /**
     * 转换工单查询结果为统计表格数据
     *
     * @param workSheetList 子工单查询结果
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportTableItemDTO>  统计表格数据
     */
    private List<WorkSheetStatisticReportTableItemDTO> convertToWorkSheetStatisticTableData(List<WorkSheet> workSheetList) {
        // 统计表格数据集合
        List<WorkSheetStatisticReportTableItemDTO> reportTableDataList = Lists.newLinkedList();
        workSheetList.forEach(s -> {
            // 产品谱系
            Pedigree pedigree = Optional.ofNullable(s).map(i -> i.getPedigree()).orElse(null);
            // 表格明细
            WorkSheetStatisticReportTableItemDTO reportTableItem = new WorkSheetStatisticReportTableItemDTO();
            reportTableItem.setWorkSheetSerialNumber(Optional.ofNullable(s).map(i -> i.getSerialNumber()).orElse(null))
                    .setSubWorkSheetSerialNumber(Optional.ofNullable(s).map(i -> i.getSerialNumber()).orElse(null))
                    .setStatus(Optional.ofNullable(s).map(i -> i.getStatus()).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(i -> i.getCode()).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(i -> i.getName()).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(i -> i.getSpecification()).orElse(null))
                    .setOrganizationName(Optional.ofNullable(s).map(i -> i.getOrganizationDto()).map(o -> o.getName()).orElse(null))
                    .setWorkLineName(Optional.ofNullable(s).map(i -> i.getWorkLine()).map(o -> o.getName()).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getNumber())).orElse(Constants.LONG_ZERO))
                    .setQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setUnqualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setPlanStartDate(Optional.ofNullable(s).map(i -> i.getPlanStartDate()).orElse(null))
                    .setPlanEndDate(Optional.ofNullable(s).map(i -> i.getPlanEndDate()).orElse(null));
            reportTableDataList.add(reportTableItem);
        });
        return reportTableDataList;
    }

    /**
     * 获取工单报表表格数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @param exportStatus   是否导出
     * @param currentPage    当前页
     * @param pageSize       页数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetStatisticReportTableResultDTO 报表返回结果表格数据
     */
    private WorkSheetStatisticReportTableResultDTO getWorkSheetStatisticReportTableResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime,
                                                                                             LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize,boolean ignoreCancel) {
        WorkSheetStatisticReportTableResultDTO reportTableResultDto = new WorkSheetStatisticReportTableResultDTO();
        // 分页查询
        Specification<WorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            if(ignoreCancel) {
                //除去已取消状态的
                Predicate statusPredicate = criteriaBuilder.notEqual(root.get("status"), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
                predicateList.add(statusPredicate);
            }
            //只查询正常单
            Predicate categoryPredicate = criteriaBuilder.equal(root.get("category"), WsEnum.NORMAL_WS.getCategory());
            predicateList.add(categoryPredicate);
            //开始时间和结束时间筛选
            if ( endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(PLAN_END_DATE).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(PLAN_END_DATE).as(LocalDateTime.class), startDateTime));
            }
            // 产品谱系筛选
            if (pedigreeId != null) {
                Predicate pedigreePredicate = criteriaBuilder.equal(root.get("pedigree").get("id"), pedigreeId);
                predicateList.add(pedigreePredicate);
            }
            // 产线筛选
            if (workLineId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("workLine").get("id"), workLineId);
                predicateList.add(workLinePredicate);
            }
            //部门筛选
            if (organizationId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("organizationId"), organizationId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        List<WorkSheet> workSheetList;
        Page<WorkSheet> workSheetPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            workSheetList = workSheetService.find(specification);
        } else {
            //分页查询
            workSheetPage = workSheetService.find(specification, PageRequest.of(currentPage, pageSize));
            workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        //设置工单报表数据
        reportTableResultDto.setWorkSheetStatisticReportTableItemList(convertToWorkSheetStatisticTableData(workSheetList));
        //设置工单表格分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(exportStatus ? Optional.ofNullable(workSheetList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(workSheetPage).map(Page::getTotalElements).orElse(0L));
        return reportTableResultDto;
    }


    /**
     * 设置子工单条形图数据
     *
     * @param pedigreeId            设备谱系id
     * @param organizationId        部门id
     * @param workLineId            产线id
     * @param startDateTime         查询开始时间
     * @param endDateTime           查询结束时间
     * @param reportResultResultDto 报表数据
     */
    private void setSubWorkSheetChartData(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, WorkSheetStatisticReportChartResultDTO reportResultResultDto) {
        //查询按部门分类的完成数据
        List<OrganizationChartResultDTO> organizationResultList = subWorkSheetRepository.findOrganizationChart(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        List<OrganizationFinishInfoDTO> organizationFinishInfoList = convertOrganizationQueryToChartData(organizationResultList);
        //查询按产线分类的完成数据
        List<WorkLineFinishInfoChartResultDTO> workLineFinishInfoChartResultList = subWorkSheetRepository.findWorkLineChart(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        List<WorkLineFinishInfoDTO> workLineFinishInfoList = convertWorkLineFinishQueryToChartInfo(workLineFinishInfoChartResultList);
        reportResultResultDto.setOrganizationFinishInfoDTOList(organizationFinishInfoList).setWorkLineFinishInfoDTOList(workLineFinishInfoList);
    }

    /**
     * 转换产线分组统计数据为图表数据
     *
     * @param workLineFinishInfoChartResultList 产线分组统计数据
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.WorkLineFinishInfoDTO> 图表数据
     */
    private List<WorkLineFinishInfoDTO> convertWorkLineFinishQueryToChartInfo(List<WorkLineFinishInfoChartResultDTO> workLineFinishInfoChartResultList) {
        List<WorkLineFinishInfoDTO> workLineFinishInfoList = Lists.newLinkedList();
        workLineFinishInfoChartResultList.forEach(w -> {
            WorkLineFinishInfoDTO workLineFinishPlanInfo = new WorkLineFinishInfoDTO();
            WorkLineFinishInfoDTO workLineFinishActualInfo = new WorkLineFinishInfoDTO();
            //设置计划完成图形数据
            workLineFinishPlanInfo.setName(w.getName()).setType(WorkSheetReportChartTypeEnum.PLAN.getKey()).setNumber(w.getPlanFinishNumber());
            //设置实际完成图形数据
            workLineFinishActualInfo.setName(w.getName()).setType(WorkSheetReportChartTypeEnum.ACTUAL.getKey()).setNumber(w.getActualFinishNumber());
            workLineFinishInfoList.add(workLineFinishPlanInfo);
            workLineFinishInfoList.add(workLineFinishActualInfo);
        });
        return workLineFinishInfoList;
    }


    /**
     * 计划完成时期解析
     *
     * @param workSheetStatisticRequestDto 工单报表请求参数
     */
    private void parseTimeFinishTimeCategory(WorkSheetStatisticReportRequestDTO workSheetStatisticRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = workSheetStatisticRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = workSheetStatisticRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetStatisticRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                workSheetStatisticRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                workSheetStatisticRequestDto.setStartDate(weekStart);
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                workSheetStatisticRequestDto.setStartDate(monthStart);
                workSheetStatisticRequestDto.setEndDate(LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX));
            }
        }
    }


    /**
     * 工单按部门统计查询转换
     *
     * @param organizationChartResultList 部门统计查询结果
     * @return net.airuima.rbase.web.rest.report.dto.OrganizationFinishInfoDTO 按部门统计图形数据
     */
    private List<OrganizationFinishInfoDTO> convertOrganizationQueryToChartData(List<OrganizationChartResultDTO> organizationChartResultList) {
        List<OrganizationFinishInfoDTO> organizationFinishInfoList = Lists.newLinkedList();
        organizationChartResultList.forEach(o -> {
            String name = Optional.ofNullable(o.getOrganizationDto()).map(i -> i.getName()).orElse(null);
            if (!StringUtils.isEmpty(name)) {
                //设置计划完成数据
                OrganizationFinishInfoDTO organizationFinishPlanInfoDto = new OrganizationFinishInfoDTO();
                organizationFinishPlanInfoDto.setName(name).setType(WorkSheetReportChartTypeEnum.PLAN.getKey()).setNumber(Optional.ofNullable(o.getPlanFinishNumber()).orElse(0L));
                organizationFinishInfoList.add(organizationFinishPlanInfoDto);
                //设置实际完成数据
                OrganizationFinishInfoDTO organizationFinishActualInfoDto = new OrganizationFinishInfoDTO();
                organizationFinishActualInfoDto.setName(name).setType(WorkSheetReportChartTypeEnum.ACTUAL.getKey()).setNumber(Optional.ofNullable(o.getActualFinishNumber()).orElse(0L));
                organizationFinishInfoList.add(organizationFinishActualInfoDto);
            }
        });
        return organizationFinishInfoList;
    }

}
