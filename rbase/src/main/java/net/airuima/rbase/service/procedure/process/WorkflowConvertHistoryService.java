package net.airuima.rbase.service.procedure.process;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.process.WorkflowConvertHistory;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowConvertConfigRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.process.WorkflowConvertHistoryRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.web.rest.procedure.process.dto.WorkflowConvertDTO;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺历史Service
 *
 * <AUTHOR>
 * 2023/09/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkflowConvertHistoryService extends CommonJpaService<WorkflowConvertHistory> {

    private final String WORK_FLOW_CONVERT_HISTORY_GRAPH = "convertWorkflowHistoryEntityGraph";
    private final WorkflowConvertHistoryRepository workflowConvertHistoryRepository;
    private final WorkFlowConvertConfigRepository workFlowConvertConfigRepository;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetRepository workSheetRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepRepository stepRepository;
    private final WsStepRepository wsStepRepository;
    private final WorkFlowStepRepository workFlowStepRepository;

    private final CommonService commonService;


    private final PedigreeStepRepository pedigreeStepRepository;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;

    public WorkflowConvertHistoryService(WorkflowConvertHistoryRepository workflowConvertHistoryRepository,
                                         WorkFlowConvertConfigRepository workFlowConvertConfigRepository,
                                         BatchWorkDetailRepository batchWorkDetailRepository,
                                         SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository,
                                         WorkFlowRepository workFlowRepository, StepRepository stepRepository, WsStepRepository wsStepRepository,
                                         WorkFlowStepRepository workFlowStepRepository, CommonService commonService,
                                         PedigreeStepRepository pedigreeStepRepository) {
        this.workflowConvertHistoryRepository = workflowConvertHistoryRepository;
        this.workFlowConvertConfigRepository = workFlowConvertConfigRepository;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
        this.wsStepRepository = wsStepRepository;
        this.workFlowStepRepository = workFlowStepRepository;
        this.commonService = commonService;
        this.pedigreeStepRepository = pedigreeStepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkflowConvertHistory> find(Specification<WorkflowConvertHistory> spec, Pageable pageable) {
        return workflowConvertHistoryRepository.findAll(spec, pageable,new NamedEntityGraph(WORK_FLOW_CONVERT_HISTORY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WorkflowConvertHistory> find(Specification<WorkflowConvertHistory> spec) {
        return workflowConvertHistoryRepository.findAll(spec,new NamedEntityGraph(WORK_FLOW_CONVERT_HISTORY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkflowConvertHistory> findAll(Pageable pageable) {
        return workflowConvertHistoryRepository.findAll(pageable,new NamedEntityGraph(WORK_FLOW_CONVERT_HISTORY_GRAPH));
    }

    /**
     * 用于判断是否为子工单投产
     *
     * @return java.lang.Boolean true 是 false 部署
     */
    public Boolean isSubWorksheetProductionMode() {
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        return StringUtils.isEmpty(mode) || Boolean.parseBoolean(mode);
    }

    /**
     * 转工艺路线, 保存转工艺历史记录
     *
     * @param workflowConvertDto 转工艺路线参数
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResultDTO convertWorkflow(WorkflowConvertDTO workflowConvertDto) {
        //当前工序
        Long stepId = workflowConvertDto.getStepId();
        // 转工艺路线id
        Long targetWorkflowId = workflowConvertDto.getWorkflowId();
        // 子工单id
        Long subWorkSheetId = workflowConvertDto.getSubWorkSheetId();
        //工单id
        Long workSheetId = workflowConvertDto.getWorkSheetId();
        // 投产方式
        Boolean subWorksheetProductionMode = isSubWorksheetProductionMode();
        SubWorkSheet subWorkSheet;
        WorkSheet workSheet;
        // 子工单投产
        if (subWorksheetProductionMode) {
            // 获取子工单
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
            if (subWorkSheetOptional.isEmpty()) {
                return new BaseResultDTO(Constants.KO, "SubWorkSheetNotFound", "当前子工单不存在!");
            }
            subWorkSheet = subWorkSheetOptional.get();
            // 子工单获取工单
            workSheet = subWorkSheet.getWorkSheet();
        } else {
            subWorkSheet = null;
            // 工单投产直接获取工单
            workSheet = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO).orElse(null);
        }
        // 工单校验
        if (Objects.isNull(workSheet)) {
            return new BaseResultDTO(Constants.KO, "WorkSheetNotFound", "工单不存在!");
        }
        // 工单中的客户id
        Long clientId = workSheet.getClientId();
        // 工单中的产品谱系id
        Long pedigreeId = workSheet.getPedigree().getId();
        // 工单中的工艺路线id
        Long originWorkflowId = workSheet.getWorkFlow().getId();
        // 转工艺之前校验
        BaseResultDTO baseResultDto = checkBeforeConvertWorkFlow(stepId, targetWorkflowId, clientId, pedigreeId, originWorkflowId, workSheetId, subWorkSheetId, subWorksheetProductionMode);
        if (baseResultDto != null) {
            return baseResultDto;
        }
        //获取转工艺的目标工艺路线
        WorkFlow convertWorkflow = workFlowRepository.findByIdAndDeleted(targetWorkflowId, Constants.LONG_ZERO).orElse(null);
        // 获取当前工序
        Step currentStep = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(currentStep)) {
            return new BaseResultDTO(Constants.KO, "CurrentStepNotFound", "当前工序不存在!");
        }
        List<WsStep> originalWsStepList = null;
        boolean existSubWsStep = true;
        // 获取当前工单定制工序
        if (subWorksheetProductionMode) {
            // 如果是子工单投产
            originalWsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if(CollectionUtils.isEmpty(originalWsStepList)){
            existSubWsStep = false;
            originalWsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        // 校验生产工单定制工序
        if (CollectionUtils.isEmpty(originalWsStepList)) {
            return new BaseResultDTO(Constants.KO, "WsStepNotFound", "工单工序快照不存在!");
        }
        // 获取转工艺路线绑定工序
        List<WorkFlowStep> convertWorkFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(targetWorkflowId, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(convertWorkFlowStepList)) {
            return new BaseResultDTO(Constants.KO, "ConvertWorkFlowStepConfigNotFound", "转工艺流程工序配置不存在");
        }
        // 获取前置工序为空的工序
        List<WorkFlowStep> newFirstWfSteps = convertWorkFlowStepList.stream().filter(s -> !StringUtils.hasLength(s.getPreStepId())).toList();
        if (newFirstWfSteps.size() > Constants.INT_ONE) {
            return new BaseResultDTO(Constants.KO, "ConvertWorkflowCannotHaveParallelSteps", "转工艺流程不能存在并行工序!");
        }
        //获取转工艺第一道工序
        WorkFlowStep newFirstWfStep = newFirstWfSteps.get(Constants.INT_ZERO);
        // 校验当前工序是否在原来定制工序中是否存在
        Optional<WsStep> wsStepOptional = originalWsStepList.stream().filter(s -> Objects.equals(s.getStep(), currentStep)).findFirst();
        if (wsStepOptional.isEmpty()) {
            return new BaseResultDTO(Constants.KO, "CurrentStepNotFoundInOriginalWsStep", "当前工序在原来定制工序中不存在!");
        }
        //当前工序的定制工序
        WsStep currentWsStep = wsStepOptional.get();
        // 是否存在并行工序
        boolean exist = existParallelStep(originalWsStepList, currentWsStep);
        if (exist) {
            return new BaseResultDTO(Constants.KO, "CurrentStepHaveParallelStep", "当前工序存在并行工序!");
        }
        List<WsStep> preStepList = new ArrayList<>();
        List<WsStep> afterWsStepList = new ArrayList<>();
        //获取该工序之前的所有工序
        getPreStepList(originalWsStepList, preStepList, currentWsStep);
        //获取该工序之后的所有工序
        getAfterStepList(originalWsStepList, afterWsStepList, currentWsStep);
        afterWsStepList.add(currentWsStep);
        // 转工艺流程的工序不能和当前生产的工序前置工序相同
        boolean match = convertWorkFlowStepList.stream().anyMatch(s -> preStepList.stream().anyMatch(ps -> Objects.equals(ps.getStep(), s.getStep())));
        if (match) {
            return new BaseResultDTO(Constants.KO, "CurrentWorkFlowHasSamePrecedingStep", "当前工艺流程存在和前置工序相同的工序!");
        }
        //验证转工艺的工艺路线是否配置工序配置
        HashMap<Step, StepDTO> stepConfigMap = new HashMap<Step, StepDTO>();
        for (WorkFlowStep workFlowStep : convertWorkFlowStepList) {
            StepDTO pedigreeStepConfig = commonService.findPedigreeStepConfig(workSheet.getClientId(), workSheet.getPedigree(), workFlowStep.getWorkFlow(), workFlowStep.getStep());
            if (null == pedigreeStepConfig) {
                return new BaseResultDTO(Constants.KO, "WaStepConfigNotFound", "工序(" + workFlowStep.getStep().getName() + ")配置不存在,前往工艺管控模型-工序配置中完善");
            }
            stepConfigMap.put(workFlowStep.getStep(), pedigreeStepConfig);
        }
        // 创建新的转工艺路线的定制工序
        List<WsStep> newWsStepList = Lists.newArrayList();
        for (WorkFlowStep workFlowStep : convertWorkFlowStepList) {
            StepDTO stepDTO = stepConfigMap.get(workFlowStep.getStep());
            //获取工序配置
            WsStep wsStep = new WsStep();
            Double inputRate = Optional.ofNullable(stepDTO).map(StepDTO::getInputRate).orElse(null);
            // 子工单投产
            if (subWorksheetProductionMode) {
                wsStep.setSubWorkSheet(subWorkSheet)
                        .setWorkSheet(null)
                        .setStep(workFlowStep.getStep())
                        .setCategory(workFlowStep.getStep().getCategory())
                        .setPreStepId(workFlowStep.getPreStepId())
                        .setWorkFlow(convertWorkflow)
                        .setInputRate(Objects.nonNull(inputRate) ? inputRate : Constants.INT_ONE)
                        .setAfterStepId(workFlowStep.getAfterStepId()).setDeleted(Constants.LONG_ZERO);
            } else {
                // 工单投产
                wsStep.setWorkSheet(workSheet)
                        .setStep(workFlowStep.getStep())
                        .setCategory(workFlowStep.getStep().getCategory())
                        .setPreStepId(workFlowStep.getPreStepId())
                        .setWorkFlow(convertWorkflow)
                        .setInputRate(Objects.nonNull(inputRate) ? inputRate : Constants.INT_ONE)
                        .setAfterStepId(workFlowStep.getAfterStepId()).setDeleted(Constants.LONG_ZERO);
            }
            // 添加工序配置
            StepDTO pedigreeStepConfig = stepConfigMap.get(workFlowStep.getStep());
            wsStep.setIsBindContainer(pedigreeStepConfig.getIsBindContainer()).setIsControlMaterial(pedigreeStepConfig.getIsControlMaterial())
                    .setRequestMode(pedigreeStepConfig.getRequestMode()).setControlMode(pedigreeStepConfig.getControlMode());
            newWsStepList.add(wsStep);
        }
        //将新工艺路线工序快照的的第一道工序与拷贝的原来工序快照末尾工序连接
        newWsStepList.stream().filter(s -> Objects.equals(s.getStep(), newFirstWfStep.getStep())).forEach(s -> s.setPreStepId(currentWsStep.getPreStepId()));
        List<WsStep> removeAfterWsStepList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(afterWsStepList)){
            newWsStepList.forEach(newWsStep->{
                afterWsStepList.forEach(afterWsStep->{
                    if((!subWorksheetProductionMode || (subWorksheetProductionMode && null != afterWsStep.getSubWorkSheet())) && afterWsStep.getStep().getId().equals(newWsStep.getStep().getId())){
                        newWsStep.setId(afterWsStep.getId());
                        removeAfterWsStepList.add(afterWsStep);
                    }
                });
            });
        }
        if(!CollectionUtils.isEmpty(removeAfterWsStepList)){
            afterWsStepList.removeAll(removeAfterWsStepList);
        }
        // 删除原始后置工序(包括自身)
        if (!CollectionUtils.isEmpty(afterWsStepList) && (!subWorksheetProductionMode || existSubWsStep)) {
            wsStepRepository.deleteAll(afterWsStepList);
        }
        //对于子工单投产粒度的前置快照来说需新增子工单工序快照，同时修改前置快照的后续工序ID列表
        if (!CollectionUtils.isEmpty(preStepList) && subWorksheetProductionMode) {
            preStepList.forEach(preWsStep -> {
                WsStep newSubWsStep = new WsStep() ;
                if(Objects.nonNull(preWsStep.getWorkSheet())){
                    BeanUtils.copyProperties(preWsStep, newSubWsStep);
                    newSubWsStep.setWorkSheet(null).setSubWorkSheet(subWorkSheet).setId(null);
                }else {
                    newSubWsStep = preWsStep;
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(newSubWsStep.getAfterStepId()) && newSubWsStep.getAfterStepId().contains(currentWsStep.getStep().getId().toString())) {
                    newSubWsStep.setAfterStepId(newSubWsStep.getAfterStepId().replace(currentWsStep.getStep().getId().toString(), newFirstWfSteps.get(Constants.INT_ZERO).getStep().getId().toString()));
                }
                wsStepRepository.save(newSubWsStep);
            });
        }
        //对于工单投产粒度的前置快照来说无需新增，只需修改后置工序ID列表即可
        if(!CollectionUtils.isEmpty(preStepList) && !subWorksheetProductionMode){
            preStepList.forEach(preWsStep -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(preWsStep.getAfterStepId()) && preWsStep.getAfterStepId().contains(currentWsStep.getStep().getId().toString())) {
                    preWsStep.setAfterStepId(preWsStep.getAfterStepId().replace(currentWsStep.getStep().getId().toString(), newFirstWfSteps.get(Constants.INT_ZERO).getStep().getId().toString()));
                }
                preWsStep.setDeleted(Constants.LONG_ZERO);
                wsStepRepository.save(preWsStep);
            });
        }
        wsStepRepository.saveAll(newWsStepList);
        // 保存转工艺记录
        WorkflowConvertHistory workflowConvertHistory = subWorksheetProductionMode?this.saveSubWsWorkFlowConvertHistory(workSheet, subWorkSheet, null != currentWsStep.getWorkFlow()?currentWsStep.getWorkFlow():workSheet.getWorkFlow(),convertWorkflow,
                currentStep, newFirstWfStep):this.saveWsWorkFlowConvertHistory(workSheet, null, null != currentWsStep.getWorkFlow()?currentWsStep.getWorkFlow():workSheet.getWorkFlow(),convertWorkflow,
                currentStep, newFirstWfStep);
        //更新工单工序在制看板数据
        workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsWhenConvertWorkFlow(workSheet, subWorkSheet, currentStep, newFirstWfStep.getStep());
        return new BaseResultDTO(Constants.OK, workflowConvertHistory);
    }

    /**
     * 转工艺校验
     *
     * @param stepId                     工序id
     * @param targetWorkflowId           目标工艺路线
     * @param clientId                   客户id
     * @param pedigreeId                 产品谱系id
     * @param originWorkflowId           原来工艺路线
     * @param workSheetId                工单id
     * @param subWorkSheetId             子工单id
     * @param subWorksheetProductionMode 投产类型
     * @return net.airuima.dto.base.BaseResultDTO 结果信息
     */
    private BaseResultDTO checkBeforeConvertWorkFlow(Long stepId, Long targetWorkflowId, Long clientId, Long pedigreeId,
                                                     Long originWorkflowId, Long workSheetId, Long subWorkSheetId,
                                                     Boolean subWorksheetProductionMode) {
        // 子工单投产
        if (subWorksheetProductionMode) {
            Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.
                    findBySubWorkSheetIdAndStepIdAndDeleted(
                            subWorkSheetId, stepId, Constants.LONG_ZERO);
            // 子工单当前工序已开工，不能在此工序处转工艺
            if (batchWorkDetailOptional.isPresent()) {
                return new BaseResultDTO(Constants.KO, "CurrentStepHaveProduced", "当前工序已生产!");
            }
        } else {
            Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.
                    findByWorkSheetIdAndStepIdAndDeleted(
                            workSheetId, stepId, Constants.LONG_ZERO);
            // 工单当前工序已开工，不能在此工序处转工艺
            if (batchWorkDetailOptional.isPresent()) {
                return new BaseResultDTO(Constants.KO, "CurrentStepHaveProduced", "当前工序已生产!");
            }
        }
        // 校验工序
        Step step = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(step)) {
            return new BaseResultDTO(Constants.KO, "CurrentStepNotFound", "当前工序不存在!");
        }
        //工单当前工序为（预调整）或者为调整时不能转工艺 1:在线调整；2为在线预调整
        if (Constants.INT_ONE == step.getCategory() || Constants.INT_TWO == step.getCategory()) {
            return new BaseResultDTO(Constants.KO, "AdjustStepCannotConvertWorkflow", "当前工序为调整工序不能转工艺!");
        }
        return null;
    }

    /**
     * 保存工单转工艺历史
     *
     * @param workSheet       工单
     * @param convertWorkflow 转工艺的目标工艺路线
     * @param currentStep     当前工序
     * @param newFirstWfStep  新的第一道工序
     * @return net.airuima.domain.procedure.process.WorkflowConvertHistory 工艺路线历史
     */
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#workSheet.id")
    public WorkflowConvertHistory saveWsWorkFlowConvertHistory(WorkSheet workSheet,SubWorkSheet subWorkSheet,WorkFlow originWorkFlow,
                                                               WorkFlow convertWorkflow, Step currentStep, WorkFlowStep newFirstWfStep) {
        WorkflowConvertHistory workflowConvertHistory = new WorkflowConvertHistory();
        workflowConvertHistory.setOriginWorkFlow(originWorkFlow)
                .setOriginStep(currentStep)
                .setWorkSheet(workSheet)
                .setSubWorkSheet(subWorkSheet)
                .setConvertWorkFlow(convertWorkflow)
                .setConvertStep(newFirstWfStep.getStep())
                .setRecordTime(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        workflowConvertHistoryRepository.save(workflowConvertHistory);
        return workflowConvertHistory;
    }

    /**
     * 保存子工单转工艺历史
     *
     * @param subWorkSheet       子工单
     * @param convertWorkflow 转工艺的目标工艺路线
     * @param currentStep     当前工序
     * @param newFirstWfStep  新的第一道工序
     * @return net.airuima.domain.procedure.process.WorkflowConvertHistory 工艺路线历史
     */
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#subWorkSheet.id")
    public WorkflowConvertHistory saveSubWsWorkFlowConvertHistory(WorkSheet workSheet,SubWorkSheet subWorkSheet,WorkFlow originWorkFlow,
                                                                  WorkFlow convertWorkflow, Step currentStep, WorkFlowStep newFirstWfStep) {
        WorkflowConvertHistory workflowConvertHistory = new WorkflowConvertHistory();
        workflowConvertHistory.setOriginWorkFlow(originWorkFlow)
                .setOriginStep(currentStep)
                .setWorkSheet(workSheet)
                .setSubWorkSheet(subWorkSheet)
                .setConvertWorkFlow(convertWorkflow)
                .setConvertStep(newFirstWfStep.getStep())
                .setRecordTime(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        workflowConvertHistoryRepository.save(workflowConvertHistory);
        return workflowConvertHistory;
    }

    /**
     * 获取该工序之前的所有工序
     *
     * @param wsStepList    定制工序集合
     * @param preStepList   当前工序的所有前置工序
     * @param currentWsStep 当前工序的定制工序
     */
    private void getPreStepList(List<WsStep> wsStepList, List<WsStep> preStepList, WsStep currentWsStep) {
        if (Objects.nonNull(currentWsStep.getPreStepId())) {
            List<WsStep> preWsStepList = wsStepList.stream().filter(wsStep -> currentWsStep.getPreStepId().contains(wsStep.getStep().getId().toString())).toList();
            preStepList.addAll(preWsStepList);
            preWsStepList.forEach(preWsStep -> {
                getPreStepList(wsStepList, preStepList, preWsStep);
            });
        }
    }

    private void getAfterStepList(List<WsStep> wsStepList, List<WsStep> afterStepList, WsStep currentWsStep) {
        if (Objects.nonNull(currentWsStep.getAfterStepId())) {
            List<WsStep> afterWsStepList = wsStepList.stream().filter(wsStep -> currentWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).toList();
            afterStepList.addAll(afterWsStepList);
            afterWsStepList.forEach(afterWsStep -> {
                getAfterStepList(wsStepList, afterStepList, afterWsStep);
            });
        }
    }

    /**
     * 判断当前工序是否有并行工序
     *
     * @param wsStepList    生产工单定制工序
     * @param currentWsStep 当前工序的定制工序
     * @return boolean true存在 false不存在
     */
    private boolean existParallelStep(List<WsStep> wsStepList, WsStep currentWsStep) {
        Optional<WsStep> nextWsStepOptional = wsStepList.stream().filter(s -> Objects.equals(currentWsStep.getAfterStepId(), s.getStep().getId().toString())).findFirst();
        if (nextWsStepOptional.isEmpty()) {
            return false;
        }
        WsStep nextWsStep = nextWsStepOptional.get();
        String[] preStepIds = nextWsStep.getPreStepId().split(",");
        if (preStepIds.length > Constants.INT_ONE) {
            return true;
        }
        return existParallelStep(wsStepList, nextWsStep);
    }
}
