package net.airuima.rbase.service.report;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Service
@Transactional(rollbackFor = {Exception.class})
public class StaffPerformUnqualifiedItemService extends CommonJpaService<StaffPerformUnqualifiedItem> {
    private static final String STAFF_PERFORM_UNQUALIFIED_ITEM_ENTITY_GRAPH = "staffPerformUnqualifiedItemEntityGraph";
    private final StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;

    public StaffPerformUnqualifiedItemService(StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository) {
        this.staffPerformUnqualifiedItemRepository = staffPerformUnqualifiedItemRepository;
    }

    @Override
    @FetchMethod
    public Page<StaffPerformUnqualifiedItem> find(Specification<StaffPerformUnqualifiedItem> spec, Pageable pageable) {
        return staffPerformUnqualifiedItemRepository.findAll(spec,pageable,new NamedEntityGraph(STAFF_PERFORM_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public List<StaffPerformUnqualifiedItem> find(Specification<StaffPerformUnqualifiedItem> spec) {
        return staffPerformUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(STAFF_PERFORM_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<StaffPerformUnqualifiedItem> findAll(Pageable pageable) {
        return staffPerformUnqualifiedItemRepository.findAll(pageable,new NamedEntityGraph(STAFF_PERFORM_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

}
