package net.airuima.rbase.service.procedure.aps;

import net.airuima.constant.Constants;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsSaleOrderDetail;
import net.airuima.rbase.repository.procedure.aps.WsSaleOrderDetailRepository;
import net.airuima.rbase.service.procedure.aps.plugin.ISaleOrderService;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.service.CommonJpaService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单明细
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsSaleOrderDetailService extends CommonJpaService<WsSaleOrderDetail> {

    private final WsSaleOrderDetailRepository wsSaleOrderDetailRepository;
    @Autowired
    private ISaleOrderService[] saleOrderServices;

    public WsSaleOrderDetailService(WsSaleOrderDetailRepository wsSaleOrderDetailRepository) {
        this.wsSaleOrderDetailRepository = wsSaleOrderDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsSaleOrderDetail> find(Specification<WsSaleOrderDetail> spec, Pageable pageable) {
        return wsSaleOrderDetailRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsSaleOrderDetail> find(Specification<WsSaleOrderDetail> spec) {
        return wsSaleOrderDetailRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsSaleOrderDetail> findAll(Pageable pageable) {
        return wsSaleOrderDetailRepository.findAll(pageable);
    }

    /**
     * 根据销售订单详情ID列表查询工作表信息
     * 此方法首先根据提供的销售订单详情ID列表查询相关的订单详情记录，
     * 然后对这些记录进行分组处理，最后生成并返回一个包含销售订单处理DTO的列表
     *
     * @param ids 销售订单详情ID列表，用于查询相关的订单详情记录
     * @return 返回一个List<SaleOrderProcessDTO>对象，包含根据提供的ID列表查询到的工作表信息
     */
    public List<SaleOrderProcessDTO> findWorkSheetByIds(List<Long> ids) {

        List<WsSaleOrderDetail> wsSaleOrderDetails = wsSaleOrderDetailRepository.findBySaleOrderDetailIdInAndDeleted(ids, Constants.LONG_ZERO);

        if (CollectionUtils.isEmpty(wsSaleOrderDetails)) {
            return Lists.newArrayList();
        }

        List<SaleOrderProcessDTO> saleOrderProcessList = Lists.newArrayList();
        wsSaleOrderDetails.stream().collect(Collectors.groupingBy(WsSaleOrderDetail::getSaleOrderDetail)).forEach((saleOrderDetail, datas) -> {

            List<WorkSheet> workSheets = datas.stream().map(WsSaleOrderDetail::getWorkSheet).distinct().collect(Collectors.toList());
            SaleOrderProcessDTO wsOrderProcessDto = saleOrderServices[0].getWsOrderProcessDTO(workSheets, saleOrderDetail.getSaleOrder().getNumber());
            wsOrderProcessDto.setSaleOrderDetailId(saleOrderDetail.getId());
            saleOrderProcessList.add(wsOrderProcessDto);
        });
        return saleOrderProcessList;
    }
}