package net.airuima.rbase.service.procedure.reinspect;

import net.airuima.rbase.domain.procedure.reinspect.StepReinspectResult;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectResultRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检结果Service
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepReinspectResultService extends CommonJpaService<StepReinspectResult> {

    private final StepReinspectResultRepository stepReinspectResultRepository;

    public StepReinspectResultService(StepReinspectResultRepository stepReinspectResultRepository) {
        this.stepReinspectResultRepository = stepReinspectResultRepository;
    }

    @Override
    public Page<StepReinspectResult> find(Specification<StepReinspectResult> spec, Pageable pageable) {
        return stepReinspectResultRepository.findAll(spec, pageable);
    }

    @Override
    public List<StepReinspectResult> find(Specification<StepReinspectResult> spec) {
        return stepReinspectResultRepository.findAll(spec);
    }

    @Override
    public Page<StepReinspectResult> findAll(Pageable pageable) {
        return stepReinspectResultRepository.findAll(pageable);
    }
}
