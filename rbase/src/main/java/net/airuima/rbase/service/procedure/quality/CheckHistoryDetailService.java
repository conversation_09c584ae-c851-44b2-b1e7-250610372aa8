package net.airuima.rbase.service.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史明细Service
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CheckHistoryDetailService extends CommonJpaService<CheckHistoryDetail> {
    private final CheckHistoryDetailRepository checkHistoryDetailRepository;
    private final String CHECK_HISTORY_DETAIL_GRAPH = "checkHistoryDetailEntityGraph";
    public CheckHistoryDetailService(CheckHistoryDetailRepository checkHistoryDetailRepository) {
        this.checkHistoryDetailRepository = checkHistoryDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistoryDetail> find(Specification<CheckHistoryDetail> spec, Pageable pageable) {
        return checkHistoryDetailRepository.findAll(spec,pageable,new NamedEntityGraph(CHECK_HISTORY_DETAIL_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CheckHistoryDetail> find(Specification<CheckHistoryDetail> spec) {
        return checkHistoryDetailRepository.findAll(spec,new NamedEntityGraph(CHECK_HISTORY_DETAIL_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistoryDetail> findAll(Pageable pageable) {
        return checkHistoryDetailRepository.findAll(pageable,new NamedEntityGraph(CHECK_HISTORY_DETAIL_GRAPH));
    }

    /**
     * 通过历史ID获取明细
     * <AUTHOR>
     * @param historyId 历史ID
     * @return List<CheckHistoryDetail>
     * @date 2021-03-23
     **/
    @Transactional(readOnly = true)
    public List<CheckHistoryDetail> findByHistoryId(Long historyId){
        return checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(historyId, Boolean.TRUE,Constants.LONG_ZERO);
    }
}
