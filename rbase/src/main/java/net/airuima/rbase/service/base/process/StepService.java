package net.airuima.rbase.service.base.process;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncStepDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.pedigree.PedigreeStepService;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.process.dto.StepCreateDTO;
import net.airuima.rbase.web.rest.base.process.dto.StepDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工序Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepService extends CommonJpaService<Step> {

    private final StepRepository stepRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepGroupRepository stepGroupRepository;


    @Autowired
    private PedigreeStepService pedigreeStepService;

    @Autowired
    private PedigreeStepRepository pedigreeStepRepository;

    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;

    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public StepService(StepRepository stepRepository, WorkFlowRepository workFlowRepository, StepGroupRepository stepGroupRepository) {
        this.stepRepository = stepRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepGroupRepository = stepGroupRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Step> find(Specification<Step> spec, Pageable pageable) {
        return stepRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Step> find(Specification<Step> spec) {
        return stepRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Step> findAll(Pageable pageable) {
        return stepRepository.findAll(pageable);
    }

    /**
     * 根据工序编码或者名称(可能存在工序类型的指定)获取启用的工序列表
     *
     * @param text 编码或者名称
     * @param size 返回条数
     * @param categoryList 工序类型列表
     * @return List<Step>
     */
    public List<Step> findByCodeOrName(String text, Integer size,Boolean isEnable,List<Integer> categoryList) {
        Page<Step> page ;
        if (ValidateUtils.isValid(categoryList)){
            page = stepRepository.findByNameOrCodeAndCategories(StringUtils.isBlank(text)?null:text, Boolean.TRUE,categoryList, PageRequest.of(Constants.INT_ZERO, size));
        }else {
            page = stepRepository.findByNameOrCode(StringUtils.isBlank(text)?null:text, Boolean.TRUE, PageRequest.of(Constants.INT_ZERO, size));
        }
        return Optional.ofNullable(page).map(Slice::getContent).orElse(Lists.newArrayList());
    }

    /**
     * 同时保存工序以及工序属性配置
     *
     * @param stepDto 请求参数
     * @return ResponseEntity<Step>
     * <AUTHOR>
     * @date 2021-05-07
     **/
    public Step updateInstance(StepDTO stepDto) {
        WorkFlow workFlow = workFlowRepository.findByIdAndDeleted(stepDto.getStepConfigDto().getWorkFlowId(), Constants.LONG_ZERO).orElse(null);
        if (null == workFlow) {
            throw new ResponseException("error.WorkFlowNotFound", "工艺路线不存在");
        }
        StepGroup stepGroup = stepGroupRepository.findByIdAndDeleted(stepDto.getStepGroupDto().getId(), Constants.LONG_ZERO).orElse(null);
        if (null == stepGroup) {
            ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(Step.class.getSimpleName()), "stepGroupNotExist", "工序组别不存在"));
        }
        Step step = stepRepository.findByCode(stepDto.getCode()).orElseGet(Step::new);
        List<Integer> combination = Arrays.asList(Constants.STEP_ELEMENT, Constants.WORKFLOW_ELEMENT);
        if (null == stepDto.getId()) {
            if (null != step.getId()) {
                throw new ResponseException("error.StepCodeExist", "工序编码重复");
            }
            step = MapperUtils.map(stepDto, step);
            step.setStepGroup(stepGroup).setIsEnable(stepDto.getEnable()).setDeleted(Constants.LONG_ZERO);
            stepRepository.save(step);
            PedigreeStep pedigreeStep = net.airuima.rbase.util.MapperUtils.map(stepDto.getStepConfigDto(), PedigreeStep.class);
            pedigreeStep.setStep(step).setWorkFlow(workFlow).setEnable(ObjectUtils.isEmpty(stepDto.getStepConfigDto().getEnable()) ? Boolean.TRUE : stepDto.getStepConfigDto().getEnable()).setDeleted(Constants.LONG_ZERO);
            PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_THREE,combination);
            if (null == priorityElementConfig) {
                throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
            }
            if(pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName()) {
                throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "单支请求模式下只允许单支管控");
            }
//            if (pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getIsBindContainer()) {
//                throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "工序单支请求模式下不可绑定容器");
//            }
            pedigreeStep.setPriorityElementConfig(priorityElementConfig);
            pedigreeStepRepository.save(pedigreeStep);
        }
        if (stepDto.getId() != null) {
            if (null != step.getId() && !step.getId().equals(stepDto.getId())) {
                throw new ResponseException("error.StepCodeRepeated", "工序编码重复");
            }
            step.setStepGroup(stepGroup).setCategory(stepDto.getCategory()).setCode(stepDto.getCode()).setName(stepDto.getName()).setIsEnable(stepDto.getEnable()).setDeleted(Constants.LONG_ZERO);
            stepRepository.save(step);
            StepDTO.StepConfigDTO stepConfigDto = stepDto.getStepConfigDto();
            PedigreeStep pedigreeStep =  pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(null,null,workFlow.getId(), step.getId(),Constants.LONG_ZERO).orElseGet(PedigreeStep::new);
            pedigreeStep.setStep(step).setWorkFlow(workFlow)
                    .setControlMode(ObjectUtils.isEmpty(stepConfigDto.getControlMode()) ? Constants.INT_ZERO : stepConfigDto.getControlMode())
                    .setIsControlMaterial(ObjectUtils.isEmpty(stepConfigDto.getIsControlMaterial()) ? Boolean.FALSE : stepConfigDto.getIsControlMaterial())
                    .setRequestMode(ObjectUtils.isEmpty(stepConfigDto.getRequestMode()) ? Constants.INT_ZERO : stepConfigDto.getRequestMode())
                    .setIsBindContainer(ObjectUtils.isEmpty(stepConfigDto.getIsBindContainer()) ? Boolean.FALSE : stepConfigDto.getIsBindContainer())
                    .setStandardTime(ObjectUtils.isEmpty(stepConfigDto.getStandardTime()) ? BigDecimal.ZERO:stepConfigDto.getStandardTime())
                    .setInputRate(ObjectUtils.isEmpty(stepConfigDto.getInputRate()) ? Constants.INT_ONE :stepConfigDto.getInputRate())
                    .setEnable(ObjectUtils.isEmpty(stepConfigDto.getEnable()) ? Boolean.TRUE : stepConfigDto.getEnable())
                    .setDeleted(Constants.LONG_ZERO);
            PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_THREE,combination);
            if (null == priorityElementConfig) {
                throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
            }
            if(pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName()) {
                throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "单支请求模式下只允许单支管控");
            }
//            if (pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getIsBindContainer()) {
//                throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "工序单支请求模式下不可绑定容器");
//            }
            pedigreeStep.setPriorityElementConfig(priorityElementConfig);
            pedigreeStepRepository.save(pedigreeStep);
        }
        return step;
    }

    /**
     * 通过工序ID及工艺路线ID获取工序及配置信息
     *
     * @param stepId     工序ID
     * @param workFlowId 工艺路线ID
     * @return StepDTO
     * <AUTHOR>
     * @date 2021-05-07
     **/
    @Transactional(readOnly = true)
    public StepDTO findStepAndConfigByStepId(Long stepId, Long workFlowId) {
        Step step = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO).orElse(null);
        if (null == step) {
            return null;
        }
        StepDTO stepDto = net.airuima.rbase.util.MapperUtils.map(step, StepDTO.class);
        stepDto.setStepGroupDto(null != step.getStepGroup() ? net.airuima.rbase.util.MapperUtils.map(step.getStepGroup(), StepDTO.StepGroupDTO.class):null);
        stepDto.setEnable(ObjectUtils.isEmpty(step.getIsEnable()) ? Boolean.TRUE : step.getIsEnable());
        if (null != workFlowId) {
            PedigreeStep pedigreeStep = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted( null,null,workFlowId,stepId,Constants.LONG_ZERO).orElse(null);
            if (null != pedigreeStep) {
                stepDto.setStepConfigDto(net.airuima.rbase.util.MapperUtils.map(pedigreeStep, StepDTO.StepConfigDTO.class));
            }else {
                //如果没有配置，则自动添加默认配置
                StepDTO.StepConfigDTO stepConfigDto = new StepDTO.StepConfigDTO();
                stepConfigDto.setControlMode(ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName())
                        .setRequestMode(ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName())
                        .setEnable(Boolean.TRUE).setInputRate(Constants.DOUBLE_ZERRO).setIsBindContainer(Constants.FALSE).setIsControlMaterial(Constants.FALSE);
                stepDto.setStepConfigDto(stepConfigDto);
            }
            stepDto.getStepConfigDto().setWorkFlowId(workFlowId);
        }
        return stepDto;
    }

    /**
     * 生产工序数据
     *
     * @param entity
     * @return : net.airuima.rbase.domain.base.process.Step
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public Step create(StepCreateDTO entity) {
        Step step = new Step();
        //如果未手动输入编码则按照编码规则自动生成，如果手动输入则判断唯一性
        if (StringUtils.isBlank(entity.getCode())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_STEP_CODE, null, null);
            step.setCode(rbaseSerialNumberProxy.generate(serialNumberDto));
        } else {
            Optional<Step> stepOptional = stepRepository.findByCode(entity.getCode());
            if (stepOptional.isPresent()) {
                throw new ResponseException("error.CodeIsExist", "编码已存在");
            }
            step.setCode(entity.getCode());
        }
        step.setName(entity.getName()).setCategory(entity.getCategory()).setIsEnable(Boolean.TRUE).setStepGroup(new StepGroup(entity.getStepGroupId()));
        stepRepository.save(step);
        return step;
    }

    /**
     * 启用/禁用指定工序
     *
     * @param stepId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByStepId(Long stepId) {
        Step step = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.stepNotExist", "生产工序不存在"));
        step.setIsEnable(!step.getIsEnable());
        stepRepository.save(step);
    }

    /**
     * 同步工序信息
     * @param syncStepDtoList
     * <AUTHOR>
     * @date  2023/3/14
     * @return List<SyncResultDTO>
     */
    public List<SyncResultDTO> syncStep(List<SyncStepDTO> syncStepDtoList) {
        List<SyncResultDTO> syncResultDTOList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncStepDtoList)){
            syncStepDtoList.forEach(syncStepDto -> {
                SyncResultDTO syncResultDto = new SyncResultDTO(syncStepDto.getId(), Constants.INT_ONE, "");
                if (syncStepDto.getOperate() == Constants.INT_TWO){
                    stepRepository.deleteByCode(syncStepDto.getCode());
                }
                Optional<Step> stepOptional = stepRepository.findByCode(syncStepDto.getCode());
                if (syncStepDto.getOperate() == Constants.INT_ZERO){
                    if (stepOptional.isPresent()){
                        //工序已存在
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("工序已存在"));
                        return;
                    }
                    Step step = net.airuima.rbase.util.MapperUtils.map(syncStepDto, Step.class).setIsEnable(Constants.TRUE);
                    step.setId(null);
                    stepRepository.save(step);
                }
                if (syncStepDto.getOperate() == Constants.INT_ONE){
                    if (!stepOptional.isPresent()){
                        //工序不存在
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("工序不存在"));
                        return;
                    }
                    Step step = stepOptional.get();
                    step.setName(syncStepDto.getName()).setCategory(syncStepDto.getCategory());
                    stepRepository.save(step);
                }
                syncResultDTOList.add(syncResultDto);
            });
        }
        return syncResultDTOList;
    }

    /**
     * 通过编码和否启用查询对应工序
     * @param code 编码
     * @param isEnable 是否启用
     * @return net.airuima.domain.base.step.Step 工序
     */
    @Transactional(readOnly = true)
    public Step findByCode(String code, Boolean isEnable) {
        Step step = stepRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(step) && Objects.nonNull(isEnable)){
            return step.getIsEnable() == isEnable ? step : null;
        }
        return step;
    }

}
