package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep;
import net.airuima.rbase.repository.procedure.batch.CustomPedigreeStepRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系最新下单定制工序Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CustomPedigreeStepService extends CommonJpaService<CustomPedigreeStep> {
    private static final String CUSTOM_PEDIGREE_STEP_ENTITY_GRAPH = "customPedigreeStepEntityGraph";
    private final CustomPedigreeStepRepository customPedigreeStepRepository;

    public CustomPedigreeStepService(CustomPedigreeStepRepository customPedigreeStepRepository) {
        this.customPedigreeStepRepository = customPedigreeStepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CustomPedigreeStep> find(Specification<CustomPedigreeStep> spec, Pageable pageable) {
        return customPedigreeStepRepository.findAll(spec, pageable,new NamedEntityGraph(CUSTOM_PEDIGREE_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<CustomPedigreeStep> find(Specification<CustomPedigreeStep> spec) {
        return customPedigreeStepRepository.findAll(spec,new NamedEntityGraph(CUSTOM_PEDIGREE_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CustomPedigreeStep> findAll(Pageable pageable) {
        return customPedigreeStepRepository.findAll(pageable,new NamedEntityGraph(CUSTOM_PEDIGREE_STEP_ENTITY_GRAPH));
    }

    /**
     * 根据产品谱系ID获取最新正常工单定制工序
     *
     * @param pedigreeId 产品谱系ID
     * @return
     */
    @Transactional(readOnly = true)
    public List<CustomPedigreeStep> findByPedigreeId(Long pedigreeId) {
        return customPedigreeStepRepository.findByPedigreeIdAndDeleted(pedigreeId, Constants.LONG_ZERO);
    }

}
