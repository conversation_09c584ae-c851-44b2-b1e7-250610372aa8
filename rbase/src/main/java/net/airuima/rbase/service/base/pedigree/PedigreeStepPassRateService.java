package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepPassRateRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepPassRateService extends CommonJpaService<PedigreeStepPassRate> {
    private final String ENTITY_GRAPH_NAME = "pedigreeStepPassRateEntityGraph";
    private final PedigreeStepPassRateRepository pedigreeStepPassRateRepository;

    public PedigreeStepPassRateService(PedigreeStepPassRateRepository pedigreeStepPassRateRepository) {
        this.pedigreeStepPassRateRepository = pedigreeStepPassRateRepository;
    }

    @Override
    public Page<PedigreeStepPassRate> find(Specification<PedigreeStepPassRate> spec, Pageable pageable) {
        return pedigreeStepPassRateRepository.findAll(spec, pageable,new NamedEntityGraph(ENTITY_GRAPH_NAME));
    }

    @Override
    public List<PedigreeStepPassRate> find(Specification<PedigreeStepPassRate> spec) {
        return pedigreeStepPassRateRepository.findAll(spec,new NamedEntityGraph(ENTITY_GRAPH_NAME));
    }

    @Override
    public Page<PedigreeStepPassRate> findAll(Pageable pageable) {
        return pedigreeStepPassRateRepository.findAll(pageable,new NamedEntityGraph(ENTITY_GRAPH_NAME));
    }
}
