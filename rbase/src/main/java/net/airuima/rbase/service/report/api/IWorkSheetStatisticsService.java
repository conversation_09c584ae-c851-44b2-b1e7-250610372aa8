package net.airuima.rbase.service.report.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.OperationEnum;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单统计Service
 *
 * <AUTHOR>
 * @date 2023/07/08
 */
@FuncDefault
public interface IWorkSheetStatisticsService {

    /**
     * 更新工单统计表
     *
     * @param workSheetId       工单id
     * @param recordDate        记录时间(yyyyMMdd)
     * @param qualifiedNumber   合格数
     * @param unQualifiedNumber 不合格数
     * @param operationEnum     运算符
     */
    @FuncInterceptor("ProductionPlan")
    default void updateWorkSheetNumber(Long workSheetId, String recordDate, Integer qualifiedNumber, Integer unQualifiedNumber, OperationEnum operationEnum) {

    }
}
