package net.airuima.rbase.service.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryItemSnapshot;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryItemSnapshotRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 检测历史条件详情记录表Service
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CheckHistoryItemSnapshotService extends CommonJpaService<CheckHistoryItemSnapshot> {
    private final String CHECK_HISTORY_ITEM_SNAPSHOT_ENTITY_GRAPH = "checkHistoryItemSnapshotEntityGraph";
    private final CheckHistoryItemSnapshotRepository checkHistoryItemSnapshotRepository;

    public CheckHistoryItemSnapshotService(CheckHistoryItemSnapshotRepository checkHistoryItemSnapshotRepository) {
        this.checkHistoryItemSnapshotRepository = checkHistoryItemSnapshotRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistoryItemSnapshot> find(Specification<CheckHistoryItemSnapshot> spec, Pageable pageable) {
        return checkHistoryItemSnapshotRepository.findAll(spec, pageable,new NamedEntityGraph(CHECK_HISTORY_ITEM_SNAPSHOT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CheckHistoryItemSnapshot> find(Specification<CheckHistoryItemSnapshot> spec) {
        return checkHistoryItemSnapshotRepository.findAll(spec,new NamedEntityGraph(CHECK_HISTORY_ITEM_SNAPSHOT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistoryItemSnapshot> findAll(Pageable pageable) {
        return checkHistoryItemSnapshotRepository.findAll(pageable,new NamedEntityGraph(CHECK_HISTORY_ITEM_SNAPSHOT_ENTITY_GRAPH));
    }

}
