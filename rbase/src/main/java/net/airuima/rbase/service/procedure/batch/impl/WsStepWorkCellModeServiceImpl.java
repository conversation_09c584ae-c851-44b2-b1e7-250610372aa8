package net.airuima.rbase.service.procedure.batch.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStepWorkCell;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.repository.procedure.batch.WsStepWorkCellRepository;
import net.airuima.rbase.service.procedure.batch.WsStepWorkCellModeService;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsStepWorkCellModeServiceImpl implements WsStepWorkCellModeService {

    @Autowired
    private WsStepWorkCellRepository wsStepWorkCellRepository;

    /**
     * 验证当前（子）工单工位，是否符合工单工序指派的工位
     * @param subWorkSheet 子工单
     * @param step 工序
     * @param workCell  工位
     * <AUTHOR>
     * @date  2022/8/11
     * @return BaseDTO
     */
    @Override
    public BaseClientDTO validWsStepWorkCell(SubWorkSheet subWorkSheet, Step step, WorkCell workCell) {
        List<WsStepWorkCell> wsStepWorkCellList = wsStepWorkCellRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId() ,Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepWorkCellList)){
            wsStepWorkCellList = wsStepWorkCellRepository.findByWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getWorkSheet().getId(),step.getId(),Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepWorkCellList)){
            return new BaseClientDTO(Constants.OK);
        }
        boolean isExist = wsStepWorkCellList.stream().anyMatch(wsStepWorkCell -> workCell.getId().equals(wsStepWorkCell.getWorkCell().getId()));
        if (isExist){
            return new BaseClientDTO(Constants.OK);
        }else {
            return new BaseClientDTO(Constants.KO,"(子)工单工序指定的工位，不包括当前工位！");
        }
    }
}
