package net.airuima.rbase.service.base.quality;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.quality.UnqualifiedItemWarningStandardImportDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedGroupRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemWarningStandardRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.util.ResponseDataUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良项目预警标准Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedItemWarningStandardService extends CommonJpaService<UnqualifiedItemWarningStandard> {
    private final String UNQUALIFIED_ITEM_WARNING_GRAPH = "unqualifiedItemWarningStandardEntityGraph";
    private final UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository;
    private final WorkSheetRepository workSheetRepository;
    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private StepGroupRepository stepGroupRepository;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private UnqualifiedGroupRepository unqualifiedGroupRepository;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public UnqualifiedItemWarningStandardService(UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository, WorkSheetRepository workSheetRepository) {
        this.unqualifiedItemWarningStandardRepository = unqualifiedItemWarningStandardRepository;
        this.workSheetRepository = workSheetRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<UnqualifiedItemWarningStandard> find(Specification<UnqualifiedItemWarningStandard> spec, Pageable pageable) {
        return unqualifiedItemWarningStandardRepository.findAll(spec, pageable,new NamedEntityGraph(UNQUALIFIED_ITEM_WARNING_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<UnqualifiedItemWarningStandard> find(Specification<UnqualifiedItemWarningStandard> spec) {
        return unqualifiedItemWarningStandardRepository.findAll(spec,new NamedEntityGraph(UNQUALIFIED_ITEM_WARNING_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<UnqualifiedItemWarningStandard> findAll(Pageable pageable) {
        return unqualifiedItemWarningStandardRepository.findAll(pageable,new NamedEntityGraph(UNQUALIFIED_ITEM_WARNING_GRAPH));
    }

    /**
     * 新增不良预警规则
     * <AUTHOR>
     * @date 2022/11/1
     * @param unqualifiedItemWarningStandard 不良预警规则
     */
    public void saveInstance(UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        // 验证是否添加预警条件
        if (null == unqualifiedItemWarningStandard.getPriorityElementConfig()) {
            throw new ResponseException("error.NoWarningCondition", "未添加预警条件");
        }
        //验证预警标准
        UnqualifiedItemWarningStandard byUnqualifiedItemWarningStandard = checkUnqualifiedItemWarningStandard(unqualifiedItemWarningStandard);
        if (null != byUnqualifiedItemWarningStandard) {
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }
        // 工单问题(直接用实体类接收工单对象的话会保存异常,需要查询出来set一下才可以正常保存)
        if (null != unqualifiedItemWarningStandard.getWorkSheet()) {
            Optional<WorkSheet> workSheet = workSheetRepository.findById(unqualifiedItemWarningStandard.getWorkSheet().getId());
            workSheet.ifPresent(unqualifiedItemWarningStandard::setWorkSheet);
        }
        save(unqualifiedItemWarningStandard);
    }

    /**
     * 验证预警标准
     * <AUTHOR>
     * @date 2022/11/2
     * @param unqualifiedItemWarningStandard 接收的预警规则
     * @return UnqualifiedItemWarningStandard
     */
    private UnqualifiedItemWarningStandard checkUnqualifiedItemWarningStandard(UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        // 验证预警标准范围
        if (unqualifiedItemWarningStandard.getBaseNumber() <= Constants.INT_ZERO ||
                unqualifiedItemWarningStandard.getWaringNumber() <= Constants.INT_ZERO ||
                unqualifiedItemWarningStandard.getWaringRate() <= Constants.INT_ZERO ||
                unqualifiedItemWarningStandard.getStopRate() <= Constants.INT_ZERO ||
                unqualifiedItemWarningStandard.getWaringRate() > Constants.WARING_STOP_RATE_NUMBER ||
                unqualifiedItemWarningStandard.getStopRate() > Constants.WARING_STOP_RATE_NUMBER) {
            throw new ResponseException("error.WarningCriteriaError", "预警标准输入错误");
        }
        // 验证是否相同预警规则
        return unqualifiedItemWarningStandardRepository.findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndUnqualifiedGroupIdAndUnqualifiedItemIdAndDeleted(
                null != unqualifiedItemWarningStandard.getPedigree() ? unqualifiedItemWarningStandard.getPedigree().getId() : null,
                null != unqualifiedItemWarningStandard.getStep() ? unqualifiedItemWarningStandard.getStep().getId() : null,
                null != unqualifiedItemWarningStandard.getWorkFlow() ? unqualifiedItemWarningStandard.getWorkFlow().getId() : null,
                null != unqualifiedItemWarningStandard.getStepGroup() ? unqualifiedItemWarningStandard.getStepGroup().getId() : null,
                null != unqualifiedItemWarningStandard.getWorkSheet() ? unqualifiedItemWarningStandard.getWorkSheet().getId() : null,
                unqualifiedItemWarningStandard.getClientId(),
                unqualifiedItemWarningStandard.getWorkSheetCategory(),
                null != unqualifiedItemWarningStandard.getUnqualifiedGroup() ? unqualifiedItemWarningStandard.getUnqualifiedGroup().getId() : null,
                null != unqualifiedItemWarningStandard.getUnqualifiedItem() ? unqualifiedItemWarningStandard.getUnqualifiedItem().getId() : null,
                Constants.LONG_ZERO);
    }

    /**
     * 修改不良预警规则
     * <AUTHOR>
     * @date 2022/11/1
     * @param unqualifiedItemWarningStandard 不良预警规则
     */
    public void updateInstance(UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        if (null == unqualifiedItemWarningStandard.getId()) {
            throw new ResponseException("error.NoObjectSelected", "未选择需要修改的对象");
        }
        UnqualifiedItemWarningStandard byIdUnqualifiedItemWarningStandard  = unqualifiedItemWarningStandardRepository.findByIdAndDeleted(unqualifiedItemWarningStandard.getId(),Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.unqualifiedItemWarningStandardNotExist", "产品谱系工序不良项目预警标准不存在"));
        // 验证预警标准
        UnqualifiedItemWarningStandard byUnqualifiedItemWarningStandard = checkUnqualifiedItemWarningStandard(unqualifiedItemWarningStandard);
        //通过id查找出来的和通过组合条件查找出来的对比,看是否是同一条规则,如果不是则报错
        if (null != byUnqualifiedItemWarningStandard && !byUnqualifiedItemWarningStandard.getId().equals(byIdUnqualifiedItemWarningStandard.getId())) {
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }

        //如果是同一条规则,则修改预警基数,预警数量,和预警标准
        byIdUnqualifiedItemWarningStandard.setBaseNumber(unqualifiedItemWarningStandard.getBaseNumber());
        byIdUnqualifiedItemWarningStandard.setWaringNumber(unqualifiedItemWarningStandard.getWaringNumber());
        byIdUnqualifiedItemWarningStandard.setStopRate(unqualifiedItemWarningStandard.getStopRate());
        byIdUnqualifiedItemWarningStandard.setWaringRate(unqualifiedItemWarningStandard.getWaringRate());
        byIdUnqualifiedItemWarningStandard.setNote(unqualifiedItemWarningStandard.getNote());
        update(byIdUnqualifiedItemWarningStandard);
    }


    /**
     * 导入预警规则数据
     * <AUTHOR>
     * @date 2022/10/26
     * @param stepWarningStandardImportDTOList 预警规则导入数据
     * @return BaseClientDTO
     */
    public net.airuima.rbase.dto.client.base.BaseClientDTO importExcel(List<UnqualifiedItemWarningStandardImportDTO> unqualifiedItemWarningStandardImportDTOS) {
        //校验表格内是否有重复的预警规则
        List<UnqualifiedItemWarningStandardImportDTO> list = unqualifiedItemWarningStandardImportDTOS.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(i -> i.getPedigreeCode() + "_" + i.getWorkFlowCode()+ "_"+i.getWorkSheetCode()+"_"+i.getWorkSheetCategory()+"_"+i.getStepCode()+"_"+i.getStepGroupCode()+"_"+i.getUnqualifiedItemCode()+"_"+i.getUnqualifiedItemGroupCode()+"_"+i.getClientCode()))), ArrayList::new));
        if (list.size() < unqualifiedItemWarningStandardImportDTOS.size()) {
            throw new ResponseException("error.SameRule", "预警规则不可重复");
        }

        List<UnqualifiedItemWarningStandard> stepWarningStandardList = new ArrayList<>();
        for (UnqualifiedItemWarningStandardImportDTO unqualifiedItemWarningStandardImportDTO : unqualifiedItemWarningStandardImportDTOS) {
            List<Integer> combination = new ArrayList<>();
            UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = new UnqualifiedItemWarningStandard();

            //产品谱系
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getPedigreeCode())) {
                combination.add(Constants.PEDIGREE_ELEMENT);
                Optional<Pedigree> pedigree = pedigreeRepository.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getPedigreeCode(), Constants.LONG_ZERO);
                if (!pedigree.isPresent()) {
                    throw new ResponseException("error.NoPedigree", "没有该产品谱系");
                }
                if(!pedigree.get().getIsEnable()){
                    throw new ResponseException("error.pedigreeDisabled", "产品谱系已禁用");
                }
                pedigree.ifPresent(unqualifiedItemWarningStandard::setPedigree);
            }
            //工艺路线
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getWorkFlowCode())) {
                combination.add(Constants.WORKFLOW_ELEMENT);
                Optional<WorkFlow> workFlow = workFlowRepository.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getWorkFlowCode(), Constants.LONG_ZERO);
                if (!workFlow.isPresent()) {
                    throw new ResponseException("error.NoWorkFlow", "没有该工艺路线");
                }
                if(!workFlow.get().getIsEnable()){
                    throw new ResponseException("error.workFlowDisabled", "工艺路线("+workFlow.get().getCode()+")已禁用");
                }
                workFlow.ifPresent(unqualifiedItemWarningStandard::setWorkFlow);
            }
            //工单
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getWorkSheetCode())) {
                combination.add(Constants.WORKSHEET_ELEMENT);
                Optional<WorkSheet> workSheet = workSheetRepository.findBySerialNumberAndDeleted(unqualifiedItemWarningStandardImportDTO.getWorkSheetCode(), Constants.LONG_ZERO);
                if (!workSheet.isPresent()) {
                    throw new ResponseException("error.NoWorkSheet", "没有该工单");
                }
                workSheet.ifPresent(unqualifiedItemWarningStandard::setWorkSheet);
            }
            //工单类型
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getWorkSheetCategory())) {
                combination.add(Constants.WORKSHEET_CATEGORY_ELEMENT);
                DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.WORK_SHEET_CATEGORY,Constants.LONG_ZERO).orElse(new DictionaryDTO());
                String workSheetCategoryData = dictionaryDTO.getData();
                if (StringUtils.isNotBlank(workSheetCategoryData)) {
                    List<Map<String, String>> workSheetCategoryList = JSON.parseObject(workSheetCategoryData, new TypeReference<List<Map<String, String>>>() {
                    });
                    Integer workSheetCategory = workSheetCategoryList.stream().filter(map -> map.get(Constants.WORK_SHEET_CATEGORY_DATA).equals(unqualifiedItemWarningStandardImportDTO.getWorkSheetCategory())).findFirst().map(map -> map.get(Constants.WORK_SHEET_CATEGORY_DATA_KEY)).map(Integer::parseInt).orElse(null);
                    unqualifiedItemWarningStandard.setWorkSheetCategory(workSheetCategory);
                }
                if (null == unqualifiedItemWarningStandard.getWorkSheetCategory()) {
                    throw new ResponseException("error.WorkSheetCategoryError", "工单类型填写错误");
                }
            }
            //工序
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getStepCode())) {
                combination.add(Constants.STEP_ELEMENT);
                Optional<Step> step = stepRepository.findByCode(unqualifiedItemWarningStandardImportDTO.getStepCode());
                if (!step.isPresent()) {
                    throw new ResponseException("error.NoStep", "没有该工序");
                }
                if(!step.get().getIsEnable()){
                    throw new ResponseException("error.stepDisabled", "工序("+step.get().getCode()+")已禁用");
                }
                step.ifPresent(unqualifiedItemWarningStandard::setStep);
            }
            //工序组
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getStepGroupCode())) {
                combination.add(Constants.STEP_GROUP_ELEMENT);
                Optional<StepGroup> stepGroup = stepGroupRepository.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getStepGroupCode(), Constants.LONG_ZERO);
                if (!stepGroup.isPresent()) {
                    throw new ResponseException("error.NoStepGroup", "没有该工序组");
                }
                if(!stepGroup.get().getIsEnable()){
                    throw new ResponseException("error.stepGroupDisabled", "工序组("+stepGroup.get().getCode()+")已禁用");
                }
                stepGroup.ifPresent(unqualifiedItemWarningStandard::setStepGroup);
            }

            //不良项目
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getUnqualifiedItemCode())) {
                combination.add(Constants.UNQUALIFIED_ITEM_ELEMENT);
                Optional<UnqualifiedItem> unqualifiedItem = unqualifiedItemRepository.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getUnqualifiedItemCode(), Constants.LONG_ZERO);
                if (!unqualifiedItem.isPresent()) {
                    throw new ResponseException("error.NoStepGroup", "没有该不良项目");
                }
                if(!unqualifiedItem.get().getIsEnable()){
                    throw new ResponseException("error.stepGroupDisabled", "不良项目("+unqualifiedItem.get().getCode()+")已禁用");
                }
                unqualifiedItem.ifPresent(unqualifiedItemWarningStandard::setUnqualifiedItem);
            }

            //不良项目组别
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getUnqualifiedItemGroupCode())) {
                combination.add(Constants.UNQUALIFIED_GROUP);
                Optional<UnqualifiedGroup> unqualifiedGroup = unqualifiedGroupRepository.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getUnqualifiedItemGroupCode(), Constants.LONG_ZERO);
                if (!unqualifiedGroup.isPresent()) {
                    throw new ResponseException("error.NoStepGroup", "没有该不良项目组别("+unqualifiedItemWarningStandardImportDTO.getUnqualifiedItemGroupCode()+")");
                }
                if(!unqualifiedGroup.get().getIsEnable()){
                    throw new ResponseException("error.stepGroupDisabled", "不良项目组别("+unqualifiedGroup.get().getCode()+")已禁用");
                }
                unqualifiedGroup.ifPresent(unqualifiedItemWarningStandard::setUnqualifiedGroup);
            }

            //客户
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getClientCode())) {
                combination.add(Constants.CLIENT_ELEMENT);
                ClientDTO clientDTO = rbaseClientProxy.findByCodeAndDeleted(unqualifiedItemWarningStandardImportDTO.getClientCode(),Constants.LONG_ZERO);
                if (null == clientDTO) {
                    throw new ResponseException("error.NoClient", "没有该客户");
                }
                if(!clientDTO.getEnable()){
                    throw new ResponseException("error.clientDisabled", "客户("+clientDTO.getCode()+")已禁用");
                }
                unqualifiedItemWarningStandard.setClientId(clientDTO.getId());
            }
            //备注
            if (StringUtils.isNotBlank(unqualifiedItemWarningStandardImportDTO.getNote())) {
                unqualifiedItemWarningStandard.setNote(unqualifiedItemWarningStandardImportDTO.getNote());
            }
            //根据组合条件集合判断优先级配置列表中是否有匹配的条件,如果匹配上则自动选择该优先级配置，如果无匹配则报错。
            PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_ZERO,combination);
            if (null == priorityElementConfig) {
                throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
            }

            unqualifiedItemWarningStandard.setPriorityElementConfig(priorityElementConfig);
            unqualifiedItemWarningStandard.setBaseNumber(unqualifiedItemWarningStandardImportDTO.getBaseNumber()).setWaringRate(unqualifiedItemWarningStandardImportDTO.getWaringRate()).setStopRate(unqualifiedItemWarningStandardImportDTO.getStopRate());
            //校验数据库内是否已有重复预警规则
            checkWarningStandard(unqualifiedItemWarningStandard);
            stepWarningStandardList.add(unqualifiedItemWarningStandard);
        }

        unqualifiedItemWarningStandardRepository.saveAll(stepWarningStandardList);
        return new net.airuima.rbase.dto.client.base.BaseClientDTO(Constants.OK);
    }

    /**
     * 预警规则检验
     * <AUTHOR>
     * @date 2022/10/27
     * @param stepWarningStandard 预警规则
     */
    private void checkWarningStandard(UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        // 验证是否添加预警条件
        if (null == unqualifiedItemWarningStandard.getPriorityElementConfig()) {
            throw new ResponseException("error.NoWarningCondition", "未添加预警条件");
        }
        // 验证预警标准
        if (unqualifiedItemWarningStandard.getBaseNumber()<=Constants.INT_ZERO || unqualifiedItemWarningStandard.getWaringRate()<=Constants.INT_ZERO || unqualifiedItemWarningStandard.getStopRate()<=Constants.INT_ZERO ||
                unqualifiedItemWarningStandard.getWaringRate() > Constants.WARING_STOP_RATE_NUMBER || unqualifiedItemWarningStandard.getStopRate() > Constants.WARING_STOP_RATE_NUMBER) {
            throw new ResponseException("error.WarningCriteriaError", "预警标准输入错误");
        }
        // 验证是否相同预警规则
        UnqualifiedItemWarningStandard queryStepWarningStandard = unqualifiedItemWarningStandardRepository.findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndUnqualifiedGroupIdAndUnqualifiedItemIdAndDeleted(
                null != unqualifiedItemWarningStandard.getPedigree() ? unqualifiedItemWarningStandard.getPedigree().getId() : null,
                null != unqualifiedItemWarningStandard.getStep() ? unqualifiedItemWarningStandard.getStep().getId() : null,
                null != unqualifiedItemWarningStandard.getWorkFlow() ? unqualifiedItemWarningStandard.getWorkFlow().getId() : null,
                null != unqualifiedItemWarningStandard.getStepGroup() ? unqualifiedItemWarningStandard.getStepGroup().getId() : null,
                null != unqualifiedItemWarningStandard.getWorkSheet() ? unqualifiedItemWarningStandard.getWorkSheet().getId() : null,
                unqualifiedItemWarningStandard.getClientId(),
                unqualifiedItemWarningStandard.getWorkSheetCategory(),
                null != unqualifiedItemWarningStandard.getUnqualifiedGroup() ? unqualifiedItemWarningStandard.getUnqualifiedGroup().getId() : null,
                null != unqualifiedItemWarningStandard.getUnqualifiedItem() ? unqualifiedItemWarningStandard.getUnqualifiedItem().getId() : null,
                Constants.LONG_ZERO);
        if(Objects.isNull(unqualifiedItemWarningStandard.getId()) && Objects.nonNull(queryStepWarningStandard)){
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }
        if(Objects.nonNull(unqualifiedItemWarningStandard.getId()) && Objects.nonNull(queryStepWarningStandard) && !queryStepWarningStandard.getId().equals(unqualifiedItemWarningStandard.getId())){
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }
    }
}
