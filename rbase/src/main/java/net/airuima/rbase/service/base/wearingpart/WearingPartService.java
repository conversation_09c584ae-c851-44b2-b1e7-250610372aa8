package net.airuima.rbase.service.base.wearingpart;

import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WearingPartCategoryEnum;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.base.wearingpart.WearingPartGroup;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.wearingpart.WearingPartGroupRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.WearingPartResetHistoryRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司 易损件基础信息service
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WearingPartService extends CommonJpaService<WearingPart> {

    @Autowired
    private WearingPartRepository wearingPartRepository;
    @Autowired
    private WearingPartResetHistoryRepository wearingPartResetHistoryRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;
    @Autowired
    private WearingPartGroupRepository wearingPartGroupRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPart> find(Specification<WearingPart> spec, Pageable pageable) {
        return wearingPartRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WearingPart> find(Specification<WearingPart> spec) {
        return wearingPartRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WearingPart> findAll(Pageable pageable) {
        return wearingPartRepository.findAll(pageable);
    }

    /**
     * 根据易损件编码或者名称获取易损件基础信息列表
     *
     * @param text nameOrCode
     * @param size 个数
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart>
     * <AUTHOR>
     * @date 2021/6/25
     */
    @Transactional(readOnly = true)
    public List<WearingPart> findByCodeOrName(String text, int size) {
        Page<WearingPart> wearingPartPage = wearingPartRepository.findByCodeOrName(text,
                PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(wearingPartPage).map(Slice::getContent).orElse(null);
    }


    /**
     * 重置累计使用次数,时间，提示易损件不能再进行重置
     *
     * @param id 易损件ID
     */
    public void reset(Long id) {
        WearingPart wearingPart = wearingPartRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.wearingPart.notExist", "易损件不存在!"));
        // 易损件使用状态（0可用，1在用，2超期，3报废）
        if (wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory()) {
            throw new ResponseException("error.wearingPart.scraped", "易损件已报废!");
        }
        if (wearingPart.getAccumulateResetNumber() + Constants.INT_ONE > wearingPart.getMaxResetNumber()) {
            throw new ResponseException("error.wearingPart.cannotBeResetAgain", "易损件已超过最大重置次数");
        }
       StaffDTO staffDTO = new StaffDTO();
        SecurityUtils.getCurrentUserLogin().ifPresent(loginName->{
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(loginName);
            staffDTO.setId(Objects.nonNull(userDTO) && Objects.nonNull(userDTO.getStaffDTO()) && Objects.nonNull(userDTO.getStaffDTO().getId()) ? userDTO.getStaffDTO().getId():null);
        });
        // 根据 次数和时间分类 易损件管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
        // 易损件管控类型为0，重置 次数
        if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
            wearingPartRepository.updateAccumulateUseNumberAndAccumulateResetNumberById(Constants.INT_ZERO,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
        // 易损件管控类型为1，重置 时长
        if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
            wearingPartRepository.updateAccumulateUseTimeAndAccumulateResetNumberById(Constants.INT_ZERO,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
        // 易损件管控类型为2，重置 有效期
        if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
            LocalDateTime expireDate = LocalDateTime.now().plusDays(wearingPart.getWearingPartGroup().getEffectiveDay());
            wearingPartRepository.updateExpireDateAndAccumulateResetNumberById(expireDate,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
        // 易损件管控类型为3，重置 时长+次数
        if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeAndAccumulateResetNumberById(
                    Constants.INT_ZERO, Constants.INT_ZERO,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
        // 易损件管控类型为4，重置 时长+有效期
        if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
            LocalDateTime expireDate = LocalDateTime.now().plusDays(wearingPart.getWearingPartGroup().getEffectiveDay());
            wearingPartRepository.updateAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(
                    Constants.INT_ZERO, expireDate,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
        // 易损件管控类型为5，重置 次数+有效期
        if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
            LocalDateTime expireDate = LocalDateTime.now().plusDays(wearingPart.getWearingPartGroup().getEffectiveDay());
            wearingPartRepository.updateAccumulateUseNumberAndExpireDateAndAccumulateResetNumberById(
                    Constants.INT_ZERO, expireDate,
                    wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                    wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart,staffDTO.getId());
        }
        // 易损件管控类型为6，重置 时长+次数+有效期
        if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
            LocalDateTime expireDate = LocalDateTime.now().plusDays(wearingPart.getWearingPartGroup().getEffectiveDay());
            wearingPartRepository
                    .updateAccumulateUseNumberAndAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(
                            Constants.INT_ZERO, Constants.INT_ZERO, expireDate,
                            wearingPart.getAccumulateResetNumber() + Constants.INT_ONE,
                            wearingPart.getId());
            saveWearingPartResetHistoryInfo(wearingPart, staffDTO.getId());
        }
    }

    /**
     * 保存易损件重置历史
     *
     * @param wearingPart 易损件基础信息
     * @param staffId     员工id
     * @param note        备注
     */
    public void saveWearingPartResetHistoryInfo(WearingPart wearingPart, Long staffId) {
        WearingPartResetHistory wearingPartResetHistory = new WearingPartResetHistory();
        wearingPartResetHistory.setWearingPart(wearingPart).setStaffId(staffId)
                .setResetNumber(wearingPart.getAccumulateResetNumber() + Constants.INT_ONE)
                .setDeleted(Constants.LONG_ZERO);
        wearingPartResetHistoryRepository.save(wearingPartResetHistory);
    }

    /**
     * 新增或者更新易损件
     * @param entity 待保存易损件
     * @return WearingPart
     */
    public WearingPart saveInstance(WearingPart entity) throws InvocationTargetException, IllegalAccessException {
        List<WearingPart> wearingPartList = wearingPartRepository.findByCodeAndDeleted(entity.getCode(),Constants.LONG_ZERO);
        if(Objects.isNull(entity.getId()) && !CollectionUtils.isEmpty(wearingPartList)){
            throw new ResponseException("error.wearingPartExist","存在相同编码的易损件");
        }
        if(Objects.nonNull(entity.getId())){
            WearingPart wearingPart = wearingPartRepository.findByCodeAndSerialNumberAndDeleted(entity.getCode(),entity.getSerialNumber(),Constants.LONG_ZERO).orElse(null);
            if(Objects.nonNull(wearingPart) && !wearingPart.getId().equals(entity.getId())) {
                throw new ResponseException("error.wearingPartExist", "存在相同编码的易损件");
            }
        }
        WearingPartGroup wearingPartGroup = wearingPartGroupRepository.getReferenceById(entity.getWearingPartGroup().getId());
        if (Objects.isNull(entity.getId()) && wearingPartGroup.getGranularity() == Constants.INT_ONE
                && Objects.nonNull(entity.getNumber()) && entity.getNumber() > Constants.INT_ZERO) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_WEARING_PART_BATCH_CODE, LocalDate.now(), entity.getId()).setPrefixCode(entity.getCode());
           List<WearingPart> wearingParts = new ArrayList<>();
            for (int index = Constants.INT_ZERO; index < entity.getNumber(); index++) {
                WearingPart wearingPart = new WearingPart(entity);
                wearingPart.setId(null);
                wearingPart.setDeleted(Constants.LONG_ZERO);
                wearingPart.setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
                wearingParts.add(wearingPart);
            }
            wearingPartRepository.saveAll(wearingParts);
            return wearingParts.get(wearingParts.size()-Constants.INT_ONE);
        }else {
           return this.save(entity);
        }
    }

    /**
     * 通用导入保存前需要进行验证易损件合法性
     * @param entity 待导入保存的易损件对象
     * @return 验证结果
     */
    @Override
    public String validateBeforeSave(WearingPart entity) {
        if(Objects.isNull(entity.getWearingPartGroup())){
            return "易损件类型编码不可以为空";
        }
        if(entity.getWearingPartGroup().getGranularity() == Constants.INT_ZERO && StringUtils.isNotBlank(entity.getSerialNumber())){
            return "易损件编码方式为单支序列号时不可以有流水号";
        }
        if(entity.getWearingPartGroup().getGranularity() == Constants.INT_ONE && StringUtils.isBlank(entity.getSerialNumber())){
            return "易损件编码方式为批次号时流水号不可以为空";
        }
        return StringUtils.EMPTY;
    }

    public WearingPart save(WearingPart wearingPart){
        return super.save(wearingPart);
    }

    public List<WearingPart> saveAll(List<WearingPart> wearingParts){
        return super.save(wearingParts);
    }
}
