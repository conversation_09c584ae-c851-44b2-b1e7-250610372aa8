package net.airuima.rbase.service.report.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.report.WorkSheetStepStatistics;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsRequestDTO;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 生产在制看板/生产在制质量看板Interface
 * <AUTHOR>
 * @date 2024/1/9
 */
@FuncDefault
public interface IWorkSheetStepStatisticsService {

    /**
     * 自动分单、新增工单或修改工单时初始化更新生产在制看板数据
     *
     * @param workSheet           工单
     * @param subWorkSheetList    子工单列表
     * @param subWsProductionMode 是否子工单投产
     */
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    default void initWorkSheetStepStatisticsInfo(WorkSheet workSheet, List<SubWorkSheet> subWorkSheetList, boolean subWsProductionMode) {

    }


    /**
     * 工单完成、异常结单或者取消时需要删除在线看板与工单相关的信息
     *
     * @param workSheet           工单
     * @param subWorkSheetList    子工单列表
     * @param subWsProductionMode 投产粒度
     */
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    default void deleteWorkSheetStepStatisticsInfo(WorkSheet workSheet, List<SubWorkSheet> subWorkSheetList, boolean subWsProductionMode) {

    }

    /**
     * 保存生产数据或者回退生产数据时更新当前生产工序在制看板及更新后置工序生产看板
     *
     * @param workSheet         工单
     * @param subWorkSheet      子工单
     * @param step              工序
     * @param inputNumber       投入数
     * @param qualifiedNumber   合格数
     * @param unqualifiedNumber 不合格数
     * @param transferNumber    流转数
     * <AUTHOR>
     * @date 2023/12/19
     */
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    default void updateWorkSheetStepStatisticsInfo(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step, List<WsStep> wsStepList , int inputNumber, int qualifiedNumber, int unqualifiedNumber, int transferNumber) {

    }

    /**
     * 工单转工艺时更新在制看板数据
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param originStep   原始工序
     * @param targetStep   转换后工序
     */
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    default void updateWorkSheetStepStatisticsWhenConvertWorkFlow(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step originStep, Step targetStep) {

    }


    /**
     * 获取产品在制看板数据
     * @param onlineProductStatisticsRequestDTO 请求参数
     * @return net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    @FuncInterceptor("PedigreeOnlineProductionReport || PedigreeOnlineQualityReport")
    default OnlineProductStatisticsResultDTO findOnlineProductStatisticsInfo(OnlineProductStatisticsRequestDTO onlineProductStatisticsRequestDTO) {
        return null;
    }

    /**
     * 获取工序在制看板数据
     * @param workSheetStepStatisticsList 工单工序生产在制数据列表
     * @param subWsProductionMode 生产模式
     * @return net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    default OnlineProductStatisticsResultDTO findOnlineProductStepStatisticsInfo(List<WorkSheetStepStatistics> workSheetStepStatisticsList, List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList, boolean subWsProductionMode) {
        return null;
    }

    /**
     * 获取工序组别在制看板数据
     * @param workSheetStepStatisticsList 工单工序生产在制数据列表
     * @param subWsProductionMode 生产模式
     * @return net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    default OnlineProductStatisticsResultDTO findOnlineProductStepGroupStatisticsInfo(List<WorkSheetStepStatistics> workSheetStepStatisticsList,List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList,boolean subWsProductionMode) {
        return null;
    }
}
