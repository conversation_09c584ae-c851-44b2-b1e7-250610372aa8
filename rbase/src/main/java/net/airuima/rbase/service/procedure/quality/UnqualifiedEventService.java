package net.airuima.rbase.service.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.quality.UnqualifiedEventRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 预警停线事件表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedEventService extends CommonJpaService<UnqualifiedEvent> {
    private static final String UNQUALIFIED_EVENT_ENTITY_GRAPH = "unqualifiedEventEntityGraph";
    private final UnqualifiedEventRepository unqualifiedEventRepository;

    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetRepository workSheetRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    public UnqualifiedEventService(UnqualifiedEventRepository unqualifiedEventRepository,
                                   SubWorkSheetRepository subWorkSheetRepository,
                                   WorkSheetRepository workSheetRepository) {
        this.unqualifiedEventRepository = unqualifiedEventRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<UnqualifiedEvent> find(Specification<UnqualifiedEvent> spec, Pageable pageable) {
        return unqualifiedEventRepository.findAll(spec, pageable, new NamedEntityGraph(UNQUALIFIED_EVENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<UnqualifiedEvent> find(Specification<UnqualifiedEvent> spec) {
        return unqualifiedEventRepository.findAll(spec, new NamedEntityGraph(UNQUALIFIED_EVENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<UnqualifiedEvent> findAll(Pageable pageable) {
        return unqualifiedEventRepository.findAll(pageable, new NamedEntityGraph(UNQUALIFIED_EVENT_ENTITY_GRAPH));
    }

    /**
     * 处理预警停线事件
     *
     * @param unqualifiedEvent 预警停线事件
     * @return UnqualifiedEvent
     * <AUTHOR>
     * @date 2021-03-19
     **/
    public UnqualifiedEvent updateInstance(UnqualifiedEvent unqualifiedEvent) {
        SubWorkSheet subWorkSheet = null != unqualifiedEvent.getSubWorkSheet()
                ? subWorkSheetRepository.findByIdAndDeleted(unqualifiedEvent.getSubWorkSheet().getId(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet workSheet = null != subWorkSheet
                ? subWorkSheet.getWorkSheet()
                : workSheetRepository.findByIdAndDeleted(unqualifiedEvent.getWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
        //预警类型事件处理
        if (unqualifiedEvent.getEventType() == ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName() && null != subWorkSheet) {
            if (unqualifiedEvent.getDealWay() == Constants.UNQUALIFIED_EVENT_RESTORE) {
                subWorkSheet.setStatus(Constants.PRODUCING);
            } else if (unqualifiedEvent.getDealWay() == Constants.UNQUALIFIED_EVENT_PAUSE) {
                subWorkSheet.setStatus(Constants.PAUSE);
            }
            if (Objects.nonNull(subWorkSheet.getActualEndDate())){
                subWorkSheet.setStatus(Constants.FINISH);
            }
            subWorkSheetRepository.save(subWorkSheet);
        }
        //停线事件类型处理
        if ((unqualifiedEvent.getEventType() == ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName() || null == subWorkSheet) && workSheet != null) {
            if (unqualifiedEvent.getDealWay() == Constants.UNQUALIFIED_EVENT_RESTORE) {
                workSheet.setStatus(Constants.PRODUCING);
            } else if (unqualifiedEvent.getDealWay() == Constants.UNQUALIFIED_EVENT_PAUSE) {
                workSheet.setStatus(Constants.PAUSE);
            }
            if (Objects.nonNull(workSheet.getActualEndDate())){
                workSheet.setStatus(Constants.FINISH);
            }
            workSheetRepository.save(workSheet);
        }
        UnqualifiedEvent oldInstance = unqualifiedEventRepository.getReferenceById(unqualifiedEvent.getId());
        unqualifiedEvent.setDealTime(LocalDateTime.now()).setStatus(Constants.INT_ONE);
        unqualifiedEvent.setQualifiedRate(oldInstance.getQualifiedRate()).setUnqualifiedRate(oldInstance.getUnqualifiedRate()).setUnqualifiedNumber(oldInstance.getUnqualifiedNumber());
        return this.save(unqualifiedEvent);
    }

    /**
     * 流程审批恢复预警停线
     *
     * @param serialNumber 预警停线事件序列号
     */
    public void processRecover(String serialNumber) {
        Optional<UnqualifiedEvent> unqualifiedEventOptional = unqualifiedEventRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        unqualifiedEventOptional.ifPresent(unqualifiedEvent -> {
            unqualifiedEvent.setDealWay(Constants.UNQUALIFIED_EVENT_RESTORE);
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(SecurityUtils.getCurrentUserLogin().orElse(null));
            StaffDTO staffDto = Objects.nonNull(userDTO) ? userDTO.getStaffDTO() : new StaffDTO();
            unqualifiedEvent.setDealStaffId(null != staffDto ? staffDto.getId() : null);
            unqualifiedEvent.setStatus(Constants.INT_ONE);
            this.updateInstance(unqualifiedEvent);
        });
    }

    /**
     * 通过流水号获取预警停线事件记录
     *
     * @param serialNumber 预警停线事件序列号
     * @return net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent 预警停线事件记录
     */
    @Transactional(readOnly = true)
    public UnqualifiedEvent findBySerialNumber(String serialNumber) {
        return unqualifiedEventRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
    }

}
