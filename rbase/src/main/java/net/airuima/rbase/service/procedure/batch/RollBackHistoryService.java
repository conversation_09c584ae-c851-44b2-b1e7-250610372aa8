package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.batch.RollBackHistory;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.RollBackHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工序回退历史
 * <AUTHOR>
 * @date 2022/2/9
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RollBackHistoryService extends CommonJpaService<RollBackHistory> {
    private static final String ROLL_BACK_HISTORY_ENTITY_GRAPH = "rollBackHistoryEntityGraph";
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;


    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<RollBackHistory> find(Specification<RollBackHistory> spec, Pageable pageable) {
        return rollBackHistoryRepository.findAll(spec,pageable,new NamedEntityGraph(ROLL_BACK_HISTORY_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<RollBackHistory> find(Specification<RollBackHistory> spec) {
        return rollBackHistoryRepository.findAll(spec,new NamedEntityGraph(ROLL_BACK_HISTORY_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<RollBackHistory> findAll(Pageable pageable) {
        return rollBackHistoryRepository.findAll(pageable,new NamedEntityGraph(ROLL_BACK_HISTORY_ENTITY_GRAPH));
    }
}
