package net.airuima.rbase.service.report;

import io.micrometer.core.instrument.util.StringUtils;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.proxy.organization.RbaseOrganizationProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.report.api.IProductionPlanReportService;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.*;
import net.airuima.rbase.web.rest.report.dto.perform.StaffRankDataDTO;
import net.airuima.rbase.web.rest.report.dto.perform.StaffRankQueryDataDTO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional(rollbackFor = Exception.class)
public class DigitalWorkshopReportService {


    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;

    @Autowired
    private IProductionPlanReportService[] productionPlanReportServices;

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;

    @Autowired
    private StaffPerformRepository staffPerformRepository;

    @Autowired
    private StaffPerformStatisticReportService staffPerformStatisticReportService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseOrganizationProxy rbaseOrganizationProxy;

    /**
     * 通过车间（部门） 获取 各产线的 工单计划 在制数量信息
     * @param digitalWorkshopBase 查询条件基础数据
     * @return
     */
    public List<WorkLineStatisticsDTO> workLineStatisticsCharts(DigitalWorkshopBaseDTO digitalWorkshopBase) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        //获取当前部门对应生产线的今日数据统计
        if (subWsProductionMode) {
            return workLineStatisticsChartsBySubWorkSheet(digitalWorkshopBase);
        } else {
            return workLineStatisticsChartsByWorkSheet(digitalWorkshopBase);
        }

    }


    /**
     * 通过车间（部门） 获取 各产线的 工单计划 在制数量信息
     * @param digitalWorkshopBase 查询条件基础数据
     * @return
     */
    private List<WorkLineStatisticsDTO> workLineStatisticsChartsBySubWorkSheet(DigitalWorkshopBaseDTO digitalWorkshopBase) {
        List<WorkLineStatisticsDTO> workLineStatisticsDTOList = Lists.newArrayList();

        //今日子工单数统计 - 计划完工日期为今日及今日以前的，且状态为投产中和已下单的子工单数量
        //今日计划完成数  - 查询生产计划为今日的计划产出 -     1. 如生产计划功能key关闭，则计划完成数为“今日工单数”中所有子工单的投产数之和
        List<WorkLineStatisticsDTO> wokLineNumbers = subWorkSheetRepository.countByNowBeforeAndStatus(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(),LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        if (ValidateUtils.isValid(wokLineNumbers)) {
            workLineStatisticsDTOList.addAll(wokLineNumbers);
        }
        //今日已完成数：状态为已完成的子工单的合格数+不合格数
        //今日合格数：状态为已完成的子工单对应工单的合格数
        List<WorkLineStatisticsDTO> finishOrQualifiedNumbers = subWorkSheetRepository.countByNowAndFinishNumber(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(), LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        if (ValidateUtils.isValid(finishOrQualifiedNumbers)) {
            workLineStatisticsDTOList.addAll(finishOrQualifiedNumbers);
        }
        return workLineStatisticsChartsGroupByWorkLine(workLineStatisticsDTOList);
    }


    /**
     * 通过车间（部门） 获取 各产线的 子工单计划 在制数量信息
     * @param digitalWorkshopBase 查询条件基础数据
     * @return
     */
    private List<WorkLineStatisticsDTO> workLineStatisticsChartsByWorkSheet(DigitalWorkshopBaseDTO digitalWorkshopBase) {
        List<WorkLineStatisticsDTO> workLineStatisticsDTOList = Lists.newArrayList();

        //今日工单数统计 - 计划完工日期为今日及今日以前的，且状态为投产中和已下单的工单数量
        //今日计划完成数  - 查询生产计划为今日的计划产出 -     1. 如生产计划功能key关闭，则计划完成数为“今日工单数”中所有工单的投产数之和
        List<WorkLineStatisticsDTO> wokLineNumbers = workSheetRepository.countByNowBeforeAndStatus(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        if (ValidateUtils.isValid(wokLineNumbers)) {
            workLineStatisticsDTOList.addAll(wokLineNumbers);
        }
        //今日已完成数：状态为已完成的工单的合格数+不合格数
        //今日合格数：状态为已完成的工单对应工单的合格数
        List<WorkLineStatisticsDTO> finishOrQualifiedNumbers = workSheetRepository.countByNowAndFinishNumber(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(),LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        if (net.airuima.util.ValidateUtils.isValid(finishOrQualifiedNumbers)) {
            workLineStatisticsDTOList.addAll(finishOrQualifiedNumbers);
        }
        return workLineStatisticsChartsGroupByWorkLine(workLineStatisticsDTOList);
    }


    /**
     * 对生产线分组数据累加
     * @param workLineStatisticsDTOList 产线数据
     * @return
     */
    private List<WorkLineStatisticsDTO> workLineStatisticsChartsGroupByWorkLine(List<WorkLineStatisticsDTO> workLineStatisticsDTOList) {

        List<WorkLineStatisticsDTO> workLineStatistics = Lists.newArrayList();

        if (!ValidateUtils.isValid(workLineStatisticsDTOList)) {
            return workLineStatisticsDTOList;
        }
        //通过生产线
        Map<Long ,List<WorkLineStatisticsDTO>> group = workLineStatisticsDTOList.stream().collect(Collectors.groupingBy(WorkLineStatisticsDTO::getId));
        group.forEach((workLineId, datas) -> {
            WorkLineStatisticsDTO workLineStatisticsDTO = datas.get(Constants.INT_ZERO);

            //今日工单数总数
            Long number = datas.stream().map(WorkLineStatisticsDTO::getNumber).reduce(Constants.LONG_ZERO, Long::sum);
            //今日计划完成数总数
            Long planFinishNumber = datas.stream().map(WorkLineStatisticsDTO::getPlanFinishNumber).reduce(Constants.LONG_ZERO, Long::sum);
            //今日完成数总数
            long finishNumber = datas.stream().map(WorkLineStatisticsDTO::getFinishNumber).reduce(Constants.LONG_ZERO, Long::sum);
            //今日合格数总数
            Long qualifiedNumber = datas.stream().map(WorkLineStatisticsDTO::getQualifiedNumber).reduce(Constants.LONG_ZERO, Long::sum);
            //今日不合格数总数
            Long reworkQualifiedNumber = datas.stream().map(WorkLineStatisticsDTO::getReworkQualifiedNumber).reduce(Constants.LONG_ZERO, Long::sum);

            //统计合格率-直通率 -以及达成率（达成率需要通过功能key来计算，有则计算，没有则不计算）
            List<WorkLineStatisticsDTO.RateInfo> rateInfos = Lists.newArrayList();
            Double fty = finishNumber != Constants.LONG_ZERO ? NumberUtils.divide(qualifiedNumber - reworkQualifiedNumber, finishNumber, Constants.INT_FOUR).doubleValue() : Constants.DOUBLE_ZERRO;
            rateInfos.add(new WorkLineStatisticsDTO.RateInfo("直通率", fty));
            Double pfy = finishNumber != Constants.LONG_ZERO ? NumberUtils.divide(qualifiedNumber, finishNumber, Constants.INT_FOUR).doubleValue() : Constants.DOUBLE_ZERRO;
            rateInfos.add(new WorkLineStatisticsDTO.RateInfo("合格率", pfy));

            workLineStatisticsDTO.setNumber(number).setPlanFinishNumber(planFinishNumber).setFinishNumber(finishNumber).setQualifiedNumber(qualifiedNumber)
                    .setReworkQualifiedNumber(reworkQualifiedNumber).setRateInfoList(rateInfos);

            workLineStatistics.add(productionPlanReportServices[0].getOrgWorkLineIdProductionPlanAchieveRate(workLineStatisticsDTO));

        });
        return workLineStatistics;
    }


    /**
     * 通过车间（部门） 获取 各工单状态对应总数
     *
     * @param organizationId 部门id
     * @return List<WorksheetStatisticsDTO>
     */
    public WorksheetStatisticsDTO worksheetStatisticsCharts(Long organizationId) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        //获取需要处理的工单状态
        List<Integer> status = Stream.of(0, 1, 2).collect(Collectors.toList());

        //获取当前部门对应生产线的今日数据统计
        if (subWsProductionMode) {
            return worksheetStatisticsChartsBySubWorkSheet(status, organizationId);
        } else {
            return worksheetStatisticsChartsByWorkSheet(status, organizationId);
        }

    }

    /**
     * 当前车间下所有投产中、已暂停、已下单的工单详情信息
     * @param status 工单状态列表
     * @param organizationId 部门id
     * @return WorksheetStatisticsDTO
     */
    public WorksheetStatisticsDTO worksheetStatisticsChartsByWorkSheet(List<Integer> status, Long organizationId) {
        WorksheetStatisticsDTO worksheetStatisticsDto = new WorksheetStatisticsDTO();

        List<WorksheetStatisticsDTO.WorkSheetData> workSheetDataList = Lists.newArrayList();

        //  1. 总工单数：当前车间下所有投产中、已暂停、已下单的工单总数
        Long countNumber = workSheetRepository.countByStatusInAndOrganizationIdAndDeletedAndCategoryGreaterThan(status, organizationId, Constants.LONG_ZERO,Constants.NEGATIVE_ONE);
        worksheetStatisticsDto.setCountNumber(Optional.ofNullable(countNumber).orElse(Constants.LONG_ZERO));
        //  2. 已逾期：以上工单中，计划完工日期<当前日期的
        Long overdueNumber = workSheetRepository.countByPlanEndDateBeforeAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("已逾期", overdueNumber));
        //  3. 今日计划完成：以上工单中，计划完工日期=当前日期的
        Long planFinishNumber = workSheetRepository.countByPlanEndDateBetweenAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("今日计划完成", planFinishNumber));

        //  4. 未来计划完成：以上工单中，计划完工日期>当前日期的
        Long unPlanFinishNumber = workSheetRepository.countByPlanEndDateAfterAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MAX), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("未来计划完成", unPlanFinishNumber));
        return worksheetStatisticsDto.setWorkSheetDataList(workSheetDataList);
    }

    /**
     * 当前车间下所有投产中、已暂停、已下单的子工单详情信息
     * @param status 子工单状态列表
     * @param organizationId 部门id
     * @return WorksheetStatisticsDTO
     */
    private WorksheetStatisticsDTO worksheetStatisticsChartsBySubWorkSheet(List<Integer> status, Long organizationId) {
        WorksheetStatisticsDTO worksheetStatisticsDto = new WorksheetStatisticsDTO();

        List<WorksheetStatisticsDTO.WorkSheetData> workSheetDataList = Lists.newArrayList();

        //  1. 总工单数：当前车间下所有投产中、已暂停、已下单的子工单总数
        Long countNumber = subWorkSheetRepository.countByStatusInAndWorkSheetOrganizationIdAndWorkSheetCategoryGreaterThanAndDeleted(status, organizationId, Constants.NEGATIVE_ONE,Constants.LONG_ZERO);
        worksheetStatisticsDto.setCountNumber(Optional.ofNullable(countNumber).orElse(Constants.LONG_ZERO));
        //  2. 已逾期：以上子工单中，计划完工日期<当前日期的
        Long overdueNumber = subWorkSheetRepository.countByPlanEndDateBeforeAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("计划完成逾期", Optional.ofNullable(overdueNumber).orElse(Constants.LONG_ZERO)));
        //  3. 今日计划完成：以上子工单中，计划完工日期=当前日期的
        Long planFinishNumber = subWorkSheetRepository.countByPlanEndDateBetweenAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("计划今日完成", Optional.ofNullable(planFinishNumber).orElse(Constants.LONG_ZERO)));

        //  4. 未来计划完成：以上子工单中，计划完工日期>当前日期的
        Long unPlanFinishNumber = subWorkSheetRepository.countByPlanEndDateAfterAndWorkSheetOrganizationIdAndDeletedAndStatusInAndWorkSheetCategoryGreaterThan(LocalDateTime.of(LocalDate.now(), LocalTime.MAX), organizationId, Constants.LONG_ZERO, status,Constants.NEGATIVE_ONE);
        workSheetDataList.add(new WorksheetStatisticsDTO.WorkSheetData("计划未来完成", Optional.ofNullable(unPlanFinishNumber).orElse(Constants.LONG_ZERO)));
        return worksheetStatisticsDto.setWorkSheetDataList(workSheetDataList);
    }

    /**
     *  通过车间（部门） 获取 谱系对应不良项，工序对应不良
     * @param digitalWorkshopBase 部门id
     * @return
     */
    public UnqualifiedStatisticsDTO unqualifiedStatisticsCharts(DigitalWorkshopBaseDTO digitalWorkshopBase) {
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        UnqualifiedStatisticsDTO unqualifiedStatisticsDto = new UnqualifiedStatisticsDTO();

        if (subWsProductionMode) {
            //不良项目汇总数量
            List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfos = staffPerformUnqualifiedItemRepository.findBySubWorkSheetWorkSheetOrganizationIdGroupByUnqualifiedItemId(digitalWorkshopBase.getOrganizationId(),digitalWorkshopBase.getWorkLineId(),LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            // 只取前五条数据
            unqualifiedItemNumberInfos = getTopSortedElements(unqualifiedItemNumberInfos, Comparator.comparingLong(UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo::getNumber), 5);
            unqualifiedStatisticsDto.setUnqualifiedItemNumberInfoList(unqualifiedItemNumberInfos);

            //工序对应的不良数量汇总
            List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> stepUnqualifiedItemNumberInfos = staffPerformUnqualifiedItemRepository.findBySubWorkSheetStepUnqualifiedItemNumberInfo(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(), LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            // 只取前五条数据
            stepUnqualifiedItemNumberInfos = getTopSortedElements(stepUnqualifiedItemNumberInfos, Comparator.comparingLong(UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo::getNumber), 5);
            unqualifiedStatisticsDto.setStepUnqualifiedItemNumberInfoList(stepUnqualifiedItemNumberInfos);
        } else {
            //不良项目汇总数量
            List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfos = staffPerformUnqualifiedItemRepository.findByWorkSheetOrganizationIdGroupByUnqualifiedItemId(digitalWorkshopBase.getOrganizationId(),digitalWorkshopBase.getWorkLineId(),LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            // 只取前五条数据
            unqualifiedItemNumberInfos = getTopSortedElements(unqualifiedItemNumberInfos, Comparator.comparingLong(UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo::getNumber), 5);
            unqualifiedStatisticsDto.setUnqualifiedItemNumberInfoList(unqualifiedItemNumberInfos);

            //工序对应的不良数量汇总
            List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> stepUnqualifiedItemNumberInfos = staffPerformUnqualifiedItemRepository.findByWorkSheetStepUnqualifiedItemNumberInfo(digitalWorkshopBase.getOrganizationId(), digitalWorkshopBase.getWorkLineId(), LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            // 只取前五条数据
            stepUnqualifiedItemNumberInfos = getTopSortedElements(stepUnqualifiedItemNumberInfos, Comparator.comparingLong(UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo::getNumber), 5);
            unqualifiedStatisticsDto.setStepUnqualifiedItemNumberInfoList(stepUnqualifiedItemNumberInfos);
        }

        if (!ValidateUtils.isValid(unqualifiedStatisticsDto.getUnqualifiedItemNumberInfoList())){
            List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfos = Lists.newArrayList();
            unqualifiedItemNumberInfos.add(new UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo(new UnqualifiedItem(),Constants.LONG_ZERO));
            unqualifiedStatisticsDto.setUnqualifiedItemNumberInfoList(unqualifiedItemNumberInfos);
        }
        if (!ValidateUtils.isValid(unqualifiedStatisticsDto.getStepUnqualifiedItemNumberInfoList())){
            List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> stepUnqualifiedItemNumberInfos = Lists.newArrayList();
            stepUnqualifiedItemNumberInfos.add(new UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo(new Step(),Constants.LONG_ZERO));
            unqualifiedStatisticsDto.setStepUnqualifiedItemNumberInfoList(stepUnqualifiedItemNumberInfos);
        }
        return unqualifiedStatisticsDto;
    }

    /**
     * 仅检索排序后的列表中的指定数量的元素。
     *
     * @param <T> 泛型类型参数，表示列表中的元素类型
     * @param list 要检索的元素列表
     * @param comparator 用于排序元素的比较器
     * @param maxSize 指定要检索的元素的最大数量
     * @return 排序后的前 maxSize 个元素的列表
     */
    public <T> List<T> getTopSortedElements(List<T> list, Comparator<T> comparator, long maxSize) {
        if (!ValidateUtils.isValid(list)) {
            return list;
        }
        return list.stream()
                .sorted(comparator.reversed())
                .limit(maxSize)
                .collect(Collectors.toList());
    }

    /**
     * 通过车间（部门） 获取 谱系生产直通率，产品趋势
     * @param digitalWorkshopBase 部门id
     * @return List<PedigreeStatisticsDTO>
     */
    public List<PedigreeStatisticsDTO> pedigreeStatisticsCharts(DigitalWorkshopBaseDTO digitalWorkshopBase) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        List<PedigreeStatisticsDTO> pedigreeStatisticsDTOList = null;

        if (subWsProductionMode) {
            pedigreeStatisticsDTOList = subWorkSheetRepository.getPedigreeStatisticsChartData(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(-6), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), digitalWorkshopBase.getOrganizationId(),digitalWorkshopBase.getWorkLineId(), Constants.LONG_ZERO);
        } else {
            pedigreeStatisticsDTOList = workSheetRepository.getPedigreeStatisticsChartData(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(-6), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), digitalWorkshopBase.getOrganizationId(),digitalWorkshopBase.getWorkLineId(), Constants.LONG_ZERO);
        }
        List<String> datesList = DateUtils.getDatesBetween(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(-6), LocalDateTime.of(LocalDate.now(), LocalTime.MAX),"MM-dd");
        List<PedigreeStatisticsDTO> nullPedigreeStatisticsDTOList = Lists.newArrayList();

        List<PedigreeStatisticsDTO> finalPedigreeStatisticsDTOList = pedigreeStatisticsDTOList;
        //没有数据直接返回，存在部分数据，添加对应的 日期样式
        if (!ValidateUtils.isValid(pedigreeStatisticsDTOList)){
            return pedigreeStatisticsDTOList;
        }
        datesList.forEach(date -> {
            boolean anyMatch = ValidateUtils.isValid(finalPedigreeStatisticsDTOList)?finalPedigreeStatisticsDTOList.stream().anyMatch(pedigreeStatisticsDto -> pedigreeStatisticsDto.getTime().equals(date)):Constants.FALSE;
            if (!anyMatch){
                nullPedigreeStatisticsDTOList.add(new PedigreeStatisticsDTO(null,date,Constants.LONG_ZERO,Constants.LONG_ZERO,Constants.LONG_ZERO));
            }
        });
        if (ValidateUtils.isValid(nullPedigreeStatisticsDTOList)){
            pedigreeStatisticsDTOList.addAll(nullPedigreeStatisticsDTOList);
        }
        return pedigreeStatisticsDTOList.stream().sorted(Comparator.comparing(PedigreeStatisticsDTO::getTime)).collect(Collectors.toList());
    }

    /**
     * 通过车间（部门） 获取 今日产品生产周期
     * @param organizationId 部门id
     * @return ProductionCycleStatisticsDTO
     */
    public ProductionCycleStatisticsDTO productionCycleStatisticsCharts(Long organizationId) {
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode) {
            return productionCycleStatisticsChartsBySubWorkSheet(organizationId);

        } else {
            return productionCycleStatisticsChartsByWorkSheet(organizationId);
        }


    }

    /**
     * 通过车间（部门） 获取 今日产品生产周期 工单模块
     * @param organizationId 部门id
     * @return ProductionCycleStatisticsDTO
     */
    public ProductionCycleStatisticsDTO productionCycleStatisticsChartsByWorkSheet(Long organizationId) {

        ProductionCycleStatisticsDTO productionCycleStatisticsDTO = new ProductionCycleStatisticsDTO();

        //产品谱系使用时长
        List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> pedigreeAverageTimeConsumptionInfos = workSheetRepository.findByPedigreeAverageTimeConsumptionInfo(organizationId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));


        List<ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo> pedigreeStepAverageTimeConsumptionInfos = Lists.newArrayList();

        //产品谱系工序使用时长
        List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> stepAverageTimeConsumptionInfoList = batchWorkDetailRepository.findByWorkSheetStepAverageTimeConsumptionInfo(organizationId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));

        if (ValidateUtils.isValid(stepAverageTimeConsumptionInfoList)) {
            stepAverageTimeConsumptionInfoList.stream().collect(Collectors.groupingBy(ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo::getPedigreeId))
                    .forEach((id, stepAverageTimeConsumptionInfos) -> {
                        ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo pedigreeStepAverageTimeConsumptionInfo = new ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo(stepAverageTimeConsumptionInfos.get(Constants.INT_ZERO), stepAverageTimeConsumptionInfos);
                        pedigreeStepAverageTimeConsumptionInfos.add(pedigreeStepAverageTimeConsumptionInfo);
                    });
        }
        return productionCycleStatisticsDTO.setPedigreeStepAverageTimeConsumptionInfos(pedigreeStepAverageTimeConsumptionInfos)
                .setPedigreeAverageTimeConsumptionInfos(pedigreeAverageTimeConsumptionInfos);
    }

    /**
     * 通过车间（部门） 获取 今日产品生产周期 子工单模块
     * @param organizationId 部门id
     * @return ProductionCycleStatisticsDTO
     */
    public ProductionCycleStatisticsDTO productionCycleStatisticsChartsBySubWorkSheet(Long organizationId) {

        ProductionCycleStatisticsDTO productionCycleStatisticsDTO = new ProductionCycleStatisticsDTO();

        //产品谱系使用时长
        List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> pedigreeAverageTimeConsumptionInfos = subWorkSheetRepository.findByPedigreeAverageTimeConsumptionInfo(organizationId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));


        List<ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo> pedigreeStepAverageTimeConsumptionInfos = Lists.newArrayList();

        //产品谱系工序使用时长
        List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> stepAverageTimeConsumptionInfoList = batchWorkDetailRepository.findBySubWorkSheetStepAverageTimeConsumptionInfo(organizationId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));

        if (ValidateUtils.isValid(stepAverageTimeConsumptionInfoList)) {
            stepAverageTimeConsumptionInfoList.stream().collect(Collectors.groupingBy(ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo::getPedigreeId))
                    .forEach((id, stepAverageTimeConsumptionInfos) -> {
                        ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo pedigreeStepAverageTimeConsumptionInfo = new ProductionCycleStatisticsDTO.PedigreeStepAverageTimeConsumptionInfo(stepAverageTimeConsumptionInfos.get(Constants.INT_ZERO), stepAverageTimeConsumptionInfos);
                        pedigreeStepAverageTimeConsumptionInfos.add(pedigreeStepAverageTimeConsumptionInfo);
                    });
        }
        return productionCycleStatisticsDTO.setPedigreeStepAverageTimeConsumptionInfos(pedigreeStepAverageTimeConsumptionInfos)
                .setPedigreeAverageTimeConsumptionInfos(pedigreeAverageTimeConsumptionInfos);
    }

    /**
     * 通过车间（产线） 获取 员工报工数
     * @param workLineId 部门id
     * @return List<StaffRankDataDTO>
     */
    public List<StaffRankDataDTO> staffRankDataStatisticsCharts(Long workLineId,int top) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        if (subWsProductionMode) {
            //查询员工报工排行榜查询数据 (子工单投产)
            List<StaffRankQueryDataDTO> staffRankQueryDataDTOList = Optional.ofNullable(staffPerformRepository.findSubWorkSheetStaffPerformStatisticStaffRankChart(null, workLineId, null, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), Constants.LONG_ZERO, PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
            return staffPerformStatisticReportService.convertStaffRankQueryToChartData(staffRankQueryDataDTOList);

        } else {
            //查询员工报工排行榜查询数据 (工单投产)
            List<StaffRankQueryDataDTO> staffRankQueryDataDTOList = Optional.ofNullable(staffPerformRepository.findWorkSheetStaffPerformStatisticStaffRankChart(null, workLineId, null, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), Constants.LONG_ZERO,PageRequest.of(Constants.INT_ZERO, top))).map(Slice::getContent).orElse(null);
            return staffPerformStatisticReportService.convertStaffRankQueryToChartData(staffRankQueryDataDTOList);
        }
    }

    /**
     * 通过车间（产线） 获取 今日工单排产
     * @param workLineId 产线id
     * @return
     */
    public List<WorkOrderSchedulingDTO> wsScheduleStatisticsCharts(Long workLineId) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        List<Integer> status = Stream.of(0, 1, 2).collect(Collectors.toList());
        if (subWsProductionMode) {
            List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByStatusInAndWorkLineIdAndPlanEndDateBeforeAndPlanEndDateIsNotNullAndDeletedAndWorkSheetCategoryGreaterThanOrderByPlanEndDateDesc(status,workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MAX), Constants.LONG_ZERO,Constants.NEGATIVE_ONE);
            if (ValidateUtils.isValid(subWorkSheets)){
                return subWorkSheets.stream().map(WorkOrderSchedulingDTO::new).sorted(Comparator.comparing(WorkOrderSchedulingDTO::getPlanEndDate)).collect(Collectors.toList());
            }

        } else {
            List<WorkSheet> workSheets = workSheetRepository.findByStatusInAndWorkLineIdAndPlanEndDateBeforeAndPlanEndDateIsNotNullAndDeletedAndCategoryGreaterThanOrderByPlanEndDateDesc(status,workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MAX), Constants.LONG_ZERO,Constants.NEGATIVE_ONE);
            if (ValidateUtils.isValid(workSheets)){
                return workSheets.stream().map(WorkOrderSchedulingDTO::new).sorted(Comparator.comparing(WorkOrderSchedulingDTO::getPlanEndDate)).collect(Collectors.toList());
            }
        }
        return null;
    }

    /**
     * 通过车间（产线） 获取 工位工序统计信息
     * @param workLineId 产线id
     * @return
     */
    public List<WorkCellStepStatisticsDTO> workCellStepStatisticsCharts(Long workLineId) {

        List<WorkCellStepStatisticsDTO> workCellStepStatisticsDtos = Lists.newArrayList();

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        //工位工序投产详情数据
        List<WorkCellStepStatisticsDTO.WorkCellStepProductionInfo> workCellStepProductionInfos = null;
        //工位工序不良投产详情
        List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> workCellStepInfoUnqualifiedItems = null;

        if (subWsProductionMode){
             workCellStepProductionInfos = staffPerformRepository.findBySubWorkSheetWorkCellStepProductionInfo(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
             workCellStepInfoUnqualifiedItems = staffPerformUnqualifiedItemRepository.findBySubWorkSheetUnqualifiedItemNumberInfo(null, workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        }else {
             workCellStepProductionInfos = staffPerformRepository.findByWorkSheetWorkCellStepProductionInfo(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
             workCellStepInfoUnqualifiedItems = staffPerformUnqualifiedItemRepository.findByWorkSheetUnqualifiedItemNumberInfo(null, workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        }

        if (ValidateUtils.isValid(workCellStepProductionInfos)){
            List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> finalWorkCellStepInfoUnqualifiedItems = workCellStepInfoUnqualifiedItems;
            //将同一组的 工位id，以及工序对比，将不良项目添加到对应dto
            workCellStepProductionInfos.stream().collect(Collectors.groupingBy(WorkCellStepStatisticsDTO.WorkCellStepProductionInfo::getWorkCellInfo))
                    .forEach(((workCellInfo, workCellStepProductionInfoList) -> {
                        WorkCellStepStatisticsDTO workCellStepStatisticsDto = new WorkCellStepStatisticsDTO();
                        workCellStepStatisticsDto.setWorkCellInfo(workCellInfo);
                        workCellStepProductionInfoList = workCellStepProductionInfoList.stream().map(workLineStatisticsDto -> {
                            if (ValidateUtils.isValid(finalWorkCellStepInfoUnqualifiedItems)){
                                List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> unqualifiedItems = finalWorkCellStepInfoUnqualifiedItems.stream().filter(unqualifiedItemNumberInfo -> unqualifiedItemNumberInfo.getWorkCellInfo().getId().equals(workCellInfo.getId()) && unqualifiedItemNumberInfo.getStepInfo().getId().equals(workLineStatisticsDto.getStepInfo().getId())).collect(Collectors.toList());
                                // 使用 Stream API 对列表按数量降序排序，并取最大的三个元素
                                return workLineStatisticsDto.setWorkCellStepInfoUnqualifiedItems(getTopSortedElements(unqualifiedItems, Comparator.comparingLong(WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem::getUnqualifiedNumber), 3));
                            }
                            return workLineStatisticsDto;
                        }).collect(Collectors.toList());
                        workCellStepStatisticsDtos.add(workCellStepStatisticsDto.setWorkCellStepProductionInfos(workCellStepProductionInfoList));
                    }));
        }

        return workCellStepStatisticsDtos;
    }
}
