package net.airuima.rbase.service.procedure.material;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.base.BaseDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单核料相关Interface
 * <AUTHOR>
 * @date 2023/2/15
 */
@FuncDefault
public interface IWsCheckMaterialService {

    /**
     * 导入正常单核料表
     *
     * @param file 核料信息表文件
     */
    @FuncInterceptor("WorksheetMaterial && WsMaterialBatch")
    default void importMaterials(MultipartFile file) throws Exception{

    }

    /**
     * 删除导入的核料凭证
     *
     * @param id 核料记录ID
     * @return ResponseEntity<Void>
     * <AUTHOR>
     * @date 2021-05-23
     **/
    default BaseDTO delete(Long id) { return new BaseDTO(Constants.OK);}
}
