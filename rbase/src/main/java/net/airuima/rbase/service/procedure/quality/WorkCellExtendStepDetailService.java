package net.airuima.rbase.service.procedure.quality;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendStepDetail;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendStepDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工位宽放过站记录详情
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellExtendStepDetailService extends CommonJpaService<WorkCellExtendStepDetail> {

    private final WorkCellExtendStepDetailRepository workCellExtendStepDetailRepository;

    public WorkCellExtendStepDetailService(WorkCellExtendStepDetailRepository workCellExtendStepDetailRepository) {
        this.workCellExtendStepDetailRepository = workCellExtendStepDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<WorkCellExtendStepDetail> find(Specification<WorkCellExtendStepDetail> spec, Pageable pageable) {
        return workCellExtendStepDetailRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<WorkCellExtendStepDetail> find(Specification<WorkCellExtendStepDetail> spec) {
        return workCellExtendStepDetailRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<WorkCellExtendStepDetail> findAll(Pageable pageable) {
        return workCellExtendStepDetailRepository.findAll(pageable);
    }


}
