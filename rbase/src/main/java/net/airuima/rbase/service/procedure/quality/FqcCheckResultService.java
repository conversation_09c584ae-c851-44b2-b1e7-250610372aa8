package net.airuima.rbase.service.procedure.quality;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResult;
import net.airuima.rbase.dto.quality.FqcDealDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.quality.FqcCheckResultRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WsReworkService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FqcCheckResultService extends CommonJpaService<FqcCheckResult> {
    private static final String FQC_CHECK_RESULT_ENTITY_GRAPH = "fqcCheckResultEntityGraph";
    private final FqcCheckResultRepository fqcCheckResultRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private WsReworkService wsReworkService;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ISerialNumberGenerate[] serialNumberGenerate;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public FqcCheckResultService(FqcCheckResultRepository fqcCheckResultRepository) {
        this.fqcCheckResultRepository = fqcCheckResultRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FqcCheckResult> find(Specification<FqcCheckResult> spec, Pageable pageable) {
        return fqcCheckResultRepository.findAll(spec, pageable, new NamedEntityGraph(FQC_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<FqcCheckResult> find(Specification<FqcCheckResult> spec) {
        return fqcCheckResultRepository.findAll(spec, new NamedEntityGraph(FQC_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FqcCheckResult> findAll(Pageable pageable) {
        return fqcCheckResultRepository.findAll(pageable, new NamedEntityGraph(FQC_CHECK_RESULT_ENTITY_GRAPH));
    }

    /**
     * 保存fqc处理结果
     *
     * @param fqcDealDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Void> fqcDeal(FqcDealDTO fqcDealDto) {
        Optional<FqcCheckResult> fqcCheckResultOptional = fqcCheckResultRepository.findByIdAndDeleted(fqcDealDto.getFqcCheckResultId(), Constants.LONG_ZERO);
        if (!fqcCheckResultOptional.isPresent()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("error", "fqcCheckResultIsNull", "FQC检测结果为空!")).build();
        }
        FqcCheckResult fqcCheckResult = fqcCheckResultOptional.get();
        //批退生成Fqc批退单
        if (fqcDealDto.getDealWay().equals(Constants.INT_ZERO)) {
            generate(fqcDealDto, fqcCheckResult);
        } else if (fqcDealDto.getDealWay().equals(Constants.INT_ONE)) {
            //放行
            fqcCheckResult.setDealStaffId(fqcDealDto.getDealStaffId())
                    .setStatus(Constants.INT_ONE)
                    .setType(Constants.INT_ONE);
            fqcCheckResultRepository.save(fqcCheckResult);
            //更新子工单状态
            SubWorkSheet originalSubWorkSheet = fqcCheckResult.getOriginalSubWorkSheet();
            originalSubWorkSheet.setStatus(Constants.PRODUCING);
            subWorkSheetRepository.save(originalSubWorkSheet);
        } else {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(FqcCheckResult.class.getSimpleName()), "requestNotSupport", "请求类型不支持!")).build();
        }
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(FqcCheckResult.class.getSimpleName()), "")).build();
    }

    /**
     * 生成Fqc批退单
     *
     * @param fqcDealDto
     * @param fqcCheckResult
     */
    @Transactional(rollbackFor = Exception.class)
    public void generate(FqcDealDTO fqcDealDto, FqcCheckResult fqcCheckResult) {
        //解绑当前工单所有容器
        List<ContainerDetail> containerDetails = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndStatusAndDeleted(fqcCheckResult.getSubWorkSheet().getId(), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetails)) {
            containerDetailRepository.unbindAllContainerByContainerDetailIds(containerDetails.stream().map(ContainerDetail::getId).collect(Collectors.toList()), LocalDateTime.now());
        }
        //生成对应的Fqc返修单
        WorkSheet workSheet = generateFqcWorkSheet(fqcCheckResult, fqcCheckResult.getSubWorkSheet(), fqcDealDto.getWorkFlowId());
        //保存对应工序流程信息
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(fqcDealDto.getWorkFlowId(), Constants.LONG_ZERO);
        Assert.notEmpty(workFlowStepList, "流程框图不存在!");
        workFlowStepList.forEach(workFlowStep -> {
            WsStep wsStep = new WsStep();
            wsStep.setStep(workFlowStep.getStep())
                    .setAfterStepId(workFlowStep.getAfterStepId())
                    .setPreStepId(workFlowStep.getPreStepId())
                    .setWorkSheet(workSheet).setWorkFlow(workSheet.getWorkFlow());
            wsStepRepository.save(wsStep);
        });
        //TODO 考虑自动生成流水号事务分段问题，目前先用固定流水号生成
        //生成对应Fqc返修单子工单
        SubWorkSheet subWorkSheet = subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(),
                Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null),
                workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                workSheet.getNumber(),
                false);
        //保存正常单与批退单关系
        WsRework wsRework = new WsRework();
        wsRework.setOriginalWorkSheet(fqcCheckResult.getOriginalSubWorkSheet().getWorkSheet());
        wsRework.setReworkWorkSheet(workSheet);
        wsReworkService.save(wsRework);
        //保存Fqc信息
        fqcCheckResult.setSubWorkSheet(subWorkSheet)
                .setType(Constants.INT_ZERO)
                .setStatus(Constants.INT_ONE)
                .setDealStaffId(fqcDealDto.getDealStaffId());
        fqcCheckResultRepository.save(fqcCheckResult);
        //更新原始子工单状态为完成
        SubWorkSheet originalSubWorkSheet = fqcCheckResult.getOriginalSubWorkSheet();
        originalSubWorkSheet.setStatus(Constants.FINISH);
        subWorkSheetRepository.save(originalSubWorkSheet);
        //更新SN对应状态
        snWorkStatusRepository.updateSnStatus(originalSubWorkSheet.getId());
        //更新SN绑定工单
        snWorkStatusRepository.updateSnSubWorkSheet(subWorkSheet.getId(), originalSubWorkSheet.getId());
    }

    /**
     * 生成对应的Fqc返修单
     *
     * @param fqcCheckResult
     * @param subWorkSheet
     * @param workFlowId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WorkSheet generateFqcWorkSheet(FqcCheckResult fqcCheckResult, SubWorkSheet subWorkSheet, Long workFlowId) {
        //生成Fqc返修单号
        SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumberByWorkSheet(Constants.KEY_SERIAL_NUMBER_FQC_REWORK_WORK_SHEET, fqcCheckResult.getSubWorkSheet().getWorkSheet().getId());
        String serialNumber = rbaseSerialNumberProxy.generate(serialNumberDto);
        Assert.isTrue(!ValidateUtils.isValid(serialNumber), "Fqc返修单号生成失败!");
        WorkSheet workSheet = new WorkSheet();
        workSheet.setSerialNumber(serialNumber);
        workSheet.setNumber(subWorkSheet.getNumber());
        //取当前日期为计划开工时间
        LocalDateTime planStartDate = LocalDateTime.now();
        workSheet.setPlanStartDate(planStartDate);
        //获得配置的计划结单
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(subWorkSheet.getWorkSheet().getPedigree());
        //计划完成时间取当前时间加上对应产品谱系配置的完成天数，没有则取当前时间加7天作为完成时间
        workSheet.setPlanEndDate(planStartDate.plusDays(Optional.ofNullable(pedigreeConfig).map(PedigreeConfig::getPlanFinishDay).orElse(Constants.INT_SEVEN)));
        //获得fqc工单的实际类型
        Integer fqcWorkSheetCategory = null;
        String workSheetCategory = commonService.getDictionaryData(Constants.WORK_SHEET_CATEGORY);
        if (StringUtils.isNotBlank(workSheetCategory)) {
            List<Map<String, String>> workSheetCategoryList = JSON.parseObject(workSheetCategory, new TypeReference<List<Map<String, String>>>() {
            });
            fqcWorkSheetCategory = workSheetCategoryList.stream().filter(
                            map -> map.get(Constants.SERIAL_NUMBER_CONFIG_CODE).equals(Constants.KEY_SERIAL_NUMBER_FQC_REWORK_WORK_SHEET))
                    .findFirst().map(map -> map.get(Constants.WORK_SHEET_CATEGORY_DATA))
                    .map(Integer::parseInt).orElse(null);
        }
        Assert.notNull(fqcWorkSheetCategory, "Fqc工单类型不存在!");
        workSheet.setCategory(fqcWorkSheetCategory);
        workSheet.setPedigree(subWorkSheet.getWorkSheet().getPedigree());
        //保存选择的流程框图
        Optional<WorkFlow> workFlowOptional = workFlowRepository.findByIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        WorkFlow workFlow = workFlowOptional.orElse(null);
        Assert.notNull(workFlow, "流程框图不存在!");
        workSheet.setOrganizationId(subWorkSheet.getWorkSheet().getOrganizationId());
        workSheet.setWorkLine(subWorkSheet.getWorkSheet().getWorkLine());
        workSheet.setBomInfoId(subWorkSheet.getWorkSheet().getBomInfoId());
        workSheet.setClientId(subWorkSheet.getWorkSheet().getClientId());
        return workSheet;
    }

}
