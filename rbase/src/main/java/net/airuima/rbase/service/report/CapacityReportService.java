package net.airuima.rbase.service.report;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 质量报表Service
 *
 * <AUTHOR>
 * @date 2021-3-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CapacityReportService {
    @Autowired
    private WorkSheetRepository statementRepository;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private CommonService commonService;
    /**
     * 产品分类日产量增长趋势
     *
     * @param workLineId 生产线ID
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2022/12/29
     **/
    public List<Map<String, Object>> productCategoryDailyOutputGrowthTrend(Long workLineId) {
        //获取投产粒度(true:子工单投产,false:工单投产)
        String productionModeStr = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        Boolean productionMode = StringUtils.isBlank(productionModeStr) ? Boolean.TRUE : Boolean.parseBoolean(productionModeStr);
        //当前日期向前推30天
        LocalDateTime delay30DayTime = DateUtils.delayDate(LocalDateTime.now(), -30, Constants.INT_ONE).withHour(0).withMinute(0).withSecond(0);
        //查询产品分类日产量增长趋势基础数据List<Map<pedigreeId:产品谱系ID, time:日期, sun:子工单不合格数, sqn:子工单合格数, wun:工单不合格数, wqn:工单合格数>>
        List<Map<String, String>> baseDataList;
        if (productionMode) {
            //子工单投产
            baseDataList = statementRepository.productCategoryDailyOutputGrowthTrendBySubWorkSheet(delay30DayTime, workLineId);
        } else {
            //工单投产
            baseDataList = statementRepository.productCategoryDailyOutputGrowthTrendByWorkSheet(delay30DayTime, workLineId);
        }
        //根据日期及产品种类分组
        List<Map<String, String>> baseDataListGroupByTimeAndPedigree = groupByTimeAndPedigree(baseDataList, productionMode);
        //进行动态行转列
        List<Map<String, Object>> baseDataListGroupByTimeAndPedigreeRowToColumn = rowToColumn(baseDataListGroupByTimeAndPedigree);
        //补齐30天数据
        return replenishDays(baseDataListGroupByTimeAndPedigreeRowToColumn);
    }

    /**
     * 根据日期及产品种类分组
     *
     * @return : void
     * <AUTHOR>
     * @date 2022/12/30
     **/
    private List<Map<String, String>> groupByTimeAndPedigree(List<Map<String, String>> baseDataList, Boolean productionMode) {
        //根据ID集合获取产品种类
        List<Long> pedigreeIdList = baseDataList.stream().map(i -> Long.valueOf(String.valueOf(i.get("pedigreeId")))).collect(Collectors.toList());
        List<Pedigree> pedigreeList = pedigreeRepository.findByIdInAndDeleted(pedigreeIdList, Constants.LONG_ZERO);
        //Map<产品种类ID, 产品种类名称>
        Map<Long, String> pedigreeIdAndNameMap = pedigreeList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i.getName()));
        //根据实际完成时间+产品种类ID 进行分组
        Map<String, List<Map<String, String>>> baseDataMap = baseDataList.stream().collect(Collectors.groupingBy(i -> String.valueOf(i.get("time")) + "_" + String.valueOf(i.get("pedigreeId"))));

        List<Map<String, String>> baseDataListGroupByTimeAndPedigree = new ArrayList<Map<String, String>>();
        for (Map.Entry<String, List<Map<String, String>>> entry : baseDataMap.entrySet()) {
            String timeAndPedigreeIdStr = entry.getKey();
            List<Map<String, String>> subBaseDataList = entry.getValue();

            List<String> timeAndPedigreeIdList = Arrays.asList(timeAndPedigreeIdStr.split("_"));
            //实际完成时间
            String time = timeAndPedigreeIdList.get(0);
            //产品种类ID
            String pedigreeId = timeAndPedigreeIdList.get(1);

            Integer number = subBaseDataList.stream().mapToInt(i -> {
                if (productionMode) {
                    //子工单投产
                    return Integer.valueOf(String.valueOf(i.get("sqn"))) + Integer.valueOf(String.valueOf(i.get("sun")));
                } else {
                    //工单投产
                    return Integer.valueOf(String.valueOf(i.get("wqn"))) + Integer.valueOf(String.valueOf(i.get("wun")));
                }
            }).sum();

            Map<String, String> map = new HashMap<String, String>();
            //实际完成时间
            map.put("time", time);
            //产品谱系名称
            map.put("name", pedigreeIdAndNameMap.get(Long.valueOf(pedigreeId)));
            //完成数
            map.put("number", String.valueOf(number));
            baseDataListGroupByTimeAndPedigree.add(map);
        }

        return baseDataListGroupByTimeAndPedigree;
    }

    /**
     * 进行动态行转列
     *
     * @return : void
     * <AUTHOR>
     * @date 2022/12/30
     **/
    private List<Map<String, Object>> rowToColumn(List<Map<String, String>> baseDataList) {
        //1. 所有产品种类名称集合
        List<String> nameList = baseDataList.stream().map(i -> i.get("name")).collect(Collectors.toList());
        //2. 按天进行分组Map<某天, List<Map<Key, Value>>>
        Map<String, List<Map<String, String>>> listByTime = baseDataList.stream().collect(Collectors.groupingBy(i -> String.valueOf(i.get("time"))));
        List<Map<String, Object>> all = new ArrayList<Map<String, Object>>();
        for (Map.Entry<String, List<Map<String, String>>> entry : listByTime.entrySet()) {
            String time = entry.getKey();
            List<Map<String, String>> subList = entry.getValue();

            //3. 转换后某天数据集合，包含时间、产品种类及数量
            Map<String, Object> baseSubMap = new HashMap<String, Object>();
            baseSubMap.put("time", time);
            for (String name : nameList) {
                //4. 赋值产品种类及数据，当天不存在的则为0
                Optional<Map<String, String>> mapOptional = subList.stream().filter(i -> i.get("name").equals(name)).findFirst();
                if (mapOptional.isPresent()) {
                    baseSubMap.put(name, Integer.valueOf(mapOptional.get().get("number")));
                } else {
                    baseSubMap.put(name, 0);
                }
            }
            all.add(baseSubMap);
        }

        return all;
    }

    /**
     * 补齐30天数据
     *
     * @param baseDataListGroupByTimeAndPedigreeList 行转列后数据集合
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2023/1/3
     **/
    public List<Map<String, Object>> replenishDays(List<Map<String, Object>> baseDataListGroupByTimeAndPedigreeList) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        //基础数据模板
        Map<String, Object> baseDataListGroupByTimeAndPedigreeMap = CollectionUtils.isEmpty(baseDataListGroupByTimeAndPedigreeList) ? new HashMap<String, Object>() : baseDataListGroupByTimeAndPedigreeList.get(Constants.INT_ZERO);
        //从30天前开始补充数据
        for (int day = Constants.INT_THIRTY; day > Constants.INT_ZERO; day--) {
            String localDate = DateUtils.delayDate(LocalDateTime.now(), -day, Constants.INT_ONE).toLocalDate().toString();
            //如果指定时间数据不存在，则补充为Map<time:指定时间，产量数据:0>
            Optional<Map<String, Object>> isExistMap = baseDataListGroupByTimeAndPedigreeList.stream().filter(j -> String.valueOf(j.get(Constants.TIME)).equals(localDate)).findFirst();
            if (!isExistMap.isPresent()) {
                Map<String, Object> newMap = new HashMap<String, Object>();
                newMap.put(Constants.TIME, localDate);
                for (Map.Entry<String, Object> entry : baseDataListGroupByTimeAndPedigreeMap.entrySet()) {
                    String key = entry.getKey();
                    if (!Constants.TIME.equals(key)) {
                        newMap.put(key, Constants.INT_ZERO);
                    }
                }
                result.add(newMap);
            } else {
                //如果存在则按顺序添加
                result.add(isExistMap.get());
            }
        }
        return result;
    }
}
