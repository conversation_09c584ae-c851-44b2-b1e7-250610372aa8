package net.airuima.rbase.service.procedure.aps.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.sync.SyncWorkSheetDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/31
 */
@Schema(description = "同步工单参数")
public class WorkSheetSyncDTO {

    /**
     * 工单同步dto
     */
    @Schema(description = "工单同步dto")
    private SyncWorkSheetDTO syncWorkSheetDto;

    /**
     * 工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet ;

    /**
     * 产品谱系
     */
    @Schema(description = "产品谱系")
    private Pedigree pedigree;

    /**
     * 产品谱系
     */
    @Schema(description = "生产线")
    private WorkLine workLine;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    private WorkFlow workFlow;

    /**
     * 工艺路线工序信息
     */
    @Schema(description = "工艺路线工序信息")
    private List<WorkFlowStep> workFlowStepList;

    /**
     * bomInfoId
     */
    @Schema(description = "bomInfoId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bomInfoId;

    /**
     * 组织架构id
     */
    @Schema(description = "组织架构id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long  organizationId;

    public WorkSheetSyncDTO() {
    }

    public WorkSheetSyncDTO(SyncWorkSheetDTO syncWorkSheetDto, WorkSheet workSheet, Pedigree pedigree, Long bomInfoId, Long organizationId) {
        this.syncWorkSheetDto = syncWorkSheetDto;
        this.workSheet = workSheet;
        this.pedigree = pedigree;
        this.bomInfoId = bomInfoId;
        this.organizationId = organizationId;
    }

    public SyncWorkSheetDTO getSyncWorkSheetDto() {
        return syncWorkSheetDto;
    }

    public WorkSheetSyncDTO setSyncWorkSheetDto(SyncWorkSheetDTO syncWorkSheetDto) {
        this.syncWorkSheetDto = syncWorkSheetDto;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WorkSheetSyncDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public WorkSheetSyncDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Long getBomInfoId() {
        return bomInfoId;
    }

    public WorkSheetSyncDTO setBomInfoId(Long bomInfoId) {
        this.bomInfoId = bomInfoId;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkSheetSyncDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WorkSheetSyncDTO setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public WorkSheetSyncDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public List<WorkFlowStep> getWorkFlowStepList() {
        return workFlowStepList;
    }

    public WorkSheetSyncDTO setWorkFlowStepList(List<WorkFlowStep> workFlowStepList) {
        this.workFlowStepList = workFlowStepList;
        return this;
    }
}
