package net.airuima.rbase.service.base.pedigree;

import net.airuima.query.QueryCondition;
import net.airuima.query.SearchFilter;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class PedigreeStepSpecificationExtraQueryService implements IPedigreeStepSpecificationExtraQueryService{

    @Override
    public List<SearchFilter> getExtraQueryConditionWhenGetAll(List<SearchFilter> lists) {
        return IPedigreeStepSpecificationExtraQueryService.super.getExtraQueryConditionWhenGetAll(lists);
    }

    @Override
    public List<QueryCondition> getExtraQueryConditionWhenSearchQuery(List<QueryCondition> queryConditions) {
        return IPedigreeStepSpecificationExtraQueryService.super.getExtraQueryConditionWhenSearchQuery(queryConditions);
    }
}
