package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ReportTimeRangeEnum;
import net.airuima.rbase.constant.WorkLineCompletionCategoryEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.report.WorkSheetStatistics;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.rbase.web.rest.report.dto.worklinecompletion.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线完成Service
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkLineCompletionReportService {

    private final ProductionPlanRepository productionPlanRepository;


    private final WorkSheetStatisticsService workSheetStatisticsService;

    /**
     * 工单
     */
    private final String WORK_SHEET = "workSheet";

    /**
     * 子工单
     */
    private final String SUB_WORK_SHEET = "subWorkSheet";

    /**
     * 记录时间
     */
    private final String RECORD_DATE = "recordDate";


    public WorkLineCompletionReportService(ProductionPlanRepository productionPlanRepository, WorkSheetStatisticsService workSheetStatisticsService) {
        this.productionPlanRepository = productionPlanRepository;
        this.workSheetStatisticsService = workSheetStatisticsService;
    }

    /**
     * 产线达成报表统计图表
     *
     * @param workLineCompletionRequestDto 产线达成报表请求参数
     */
    public WorkLineCompletionReportChartResultDTO workLineCompletionReportChart(WorkLineCompletionReportRequestDTO workLineCompletionRequestDto) {
        // 计划时间类型解析
        parseTimeCategory(workLineCompletionRequestDto);
        // 产品谱系id
        Long pedigreeId = workLineCompletionRequestDto.getPedigreeId();
        // 产线id
        Long workLineId = workLineCompletionRequestDto.getWorkLineId();
        // 查询开始时间
        LocalDate startDate = workLineCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = workLineCompletionRequestDto.getEndDate();
        //报表类型
        Integer reportType = workLineCompletionRequestDto.getReportType();
        // 获取图表数据
        WorkLineCompletionReportChartResultDTO workLineCompletionReportChartResultDto = getWorkLineCompletionReportChartData(pedigreeId, workLineId, startDate, endDate, reportType);
        return workLineCompletionReportChartResultDto;
    }

    /**
     * 获取产线交付达成报表图形数据 (统计数字 条形图 折线图)
     *
     * @param pedigreeId 产品谱系id
     * @param workLineId 产线id
     * @param startDate  开始查询时间
     * @param endDate    结束查询时间
     * @param reportType 报表类型
     * @return net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionReportChartResultDTO 产线交付达成报表图形数据
     */
    private WorkLineCompletionReportChartResultDTO getWorkLineCompletionReportChartData(Long pedigreeId, Long workLineId, LocalDate startDate, LocalDate endDate, Integer reportType) {
        WorkLineCompletionReportChartResultDTO reportChartResultDto = new WorkLineCompletionReportChartResultDTO();
        // 报表统计数字查询
        WorkLineCompletionNumberQueryDTO workLineCompletionNumberQueryData = productionPlanRepository.findWorkLineCompletionChartNumberData(workLineId, pedigreeId, startDate, endDate, Constants.LONG_ZERO);
        // 计划产出
        Long planNumber = Optional.ofNullable(workLineCompletionNumberQueryData).map(WorkLineCompletionNumberQueryDTO::getPlanNumber).orElse(Constants.LONG_ZERO);
        // 实际产出
        Long actualNumber = Optional.ofNullable(workLineCompletionNumberQueryData).map(WorkLineCompletionNumberQueryDTO::getActualNumber).orElse(Constants.LONG_ZERO);
        double completionRate = 0d;
        // 计算达成比率
        if (Constants.LONG_ZERO != planNumber) {
            completionRate = BigDecimal.valueOf((float) (actualNumber * 100) / planNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        reportChartResultDto.setPlanNumber(planNumber).setActualNumber(actualNumber).setCompletionRate(completionRate);
        // 查询条形图和折线图数据
        List<WorkLineCompletionChartQueryDTO> workLineCompletionChartDataList = productionPlanRepository.findWorkLineCompletionChartData(workLineId, pedigreeId, startDate, endDate, Constants.LONG_ZERO);
        // 条形图数据
        List<WorkLineCompletionInfoDTO> workLineCompletionInfoList = Lists.newLinkedList();
        //折线图数据
        List<WorkLineCompletionRateInfoDTO> workLineCompletionRateInfoList = Lists.newLinkedList();
        // 生产条形图数据和折线图数据
        workLineCompletionChartDataList.forEach(i -> {
            WorkLineCompletionInfoDTO planWorkLineCompletionInfo = new WorkLineCompletionInfoDTO();
            planWorkLineCompletionInfo.setNumber(i.getPlanNumber())
                    .setType(WorkLineCompletionCategoryEnum.PLAN.getKey())
                    .setName(i.getName());
            WorkLineCompletionInfoDTO actualWorkLineCompletionInfo = new WorkLineCompletionInfoDTO();
            actualWorkLineCompletionInfo.setNumber(i.getActualNumber())
                    .setType(WorkLineCompletionCategoryEnum.ACTUAL.getKey())
                    .setName(i.getName());
            workLineCompletionInfoList.add(planWorkLineCompletionInfo);
            workLineCompletionInfoList.add(actualWorkLineCompletionInfo);
            double rate = 0d;
            // 计算达成比率
            if (Constants.LONG_ZERO != i.getPlanNumber()) {
                rate = BigDecimal.valueOf((float) (i.getActualNumber() * 100) / i.getPlanNumber()).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            WorkLineCompletionRateInfoDTO workLineCompletionRateInfoDto = new WorkLineCompletionRateInfoDTO();
            workLineCompletionRateInfoDto.setRate(rate).setName(i.getName());
            workLineCompletionRateInfoList.add(workLineCompletionRateInfoDto);
        });
        reportChartResultDto.setWorkLineCompletionInfoList(workLineCompletionInfoList)
                .setWorkLineCompletionRateList(workLineCompletionRateInfoList);
        return reportChartResultDto;
    }

    /**
     * 产线交付达成报表表格数据
     *
     * @param workLineCompletionRequestDto 产线交付达成报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionReportTableResultDTO 产线交付达成报表表格数据
     */
    public WorkLineCompletionReportTableResultDTO workLineCompletionReportTable(WorkLineCompletionReportRequestDTO workLineCompletionRequestDto) {
        // 计划时间类型解析
        parseTimeCategory(workLineCompletionRequestDto);
        // 产品谱系id
        Long pedigreeId = workLineCompletionRequestDto.getPedigreeId();
        // 产线id
        Long workLineId = workLineCompletionRequestDto.getWorkLineId();
        // 查询开始时间
        LocalDate startDate = workLineCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = workLineCompletionRequestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = workLineCompletionRequestDto.getExportStatus();
        // 报表类型
        Integer reportType = workLineCompletionRequestDto.getReportType();
        //当前页
        Integer currentPage = workLineCompletionRequestDto.getCurrentPage();
        // 分页大小
        Integer pageSize = workLineCompletionRequestDto.getPageSize();
        return getWorkLineCompletionReportTableResultDTO(pedigreeId, workLineId, startDate, endDate, exportStatus, currentPage, pageSize, reportType);
    }

    /**
     * 获取产线达成统计报表表格数据
     *
     * @param pedigreeId   产品谱系id
     * @param workLineId   产线id
     * @param startDate    开始查询时间
     * @param endDate      结束查询时间
     * @param exportStatus 是否导出
     * @param currentPage  当前页
     * @param pageSize     分页大小
     * @param reportType   报表类型
     * @return net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionReportTableResultDTO 获取产线达成统计报表表格数据
     */
    private WorkLineCompletionReportTableResultDTO getWorkLineCompletionReportTableResultDTO(Long pedigreeId, Long workLineId, LocalDate startDate, LocalDate endDate, Boolean exportStatus, Integer currentPage, Integer pageSize, Integer reportType) {
        // 分页查询
        Specification<WorkSheetStatistics> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            // 时间范围筛选
            if ( endDate != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(RECORD_DATE).as(LocalDate.class), endDate));
            }
            if (startDate != null ) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(RECORD_DATE).as(LocalDate.class), startDate));
            }
            // 产品谱系筛选
            if (pedigreeId != null) {
                Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                predicateList.add(pedigreePredicate);
            }
            if (workLineId != null) {
                Predicate workLineIdPredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("workLine").get("id"), workLineId);
                predicateList.add(workLineIdPredicate);
            }
            return query.where(predicateList.toArray(new Predicate[predicateList.size()])).orderBy(criteriaBuilder.desc(root.get(RECORD_DATE))).getRestriction();
        };
        // 工单统计集合
        List<WorkSheetStatistics> workSheetStatisticsList;
        Page<WorkSheetStatistics> workSheetStatisticsPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            workSheetStatisticsList = workSheetStatisticsService.find(specification);
        } else {
            //分页查询
            workSheetStatisticsPage = workSheetStatisticsService.find(specification, PageRequest.of(currentPage, pageSize));
            workSheetStatisticsList = Optional.ofNullable(workSheetStatisticsPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        WorkLineCompletionReportTableResultDTO tableResultDto = new WorkLineCompletionReportTableResultDTO();
        // 转换产线达成看板表格数据
        List<WorkLineCompletionReportTableItemDTO> workLineCompletionReportTableItemList = covertToWorkSheetStatisticReportTableData(workSheetStatisticsList);
        tableResultDto.setWorkLineCompletionReportTableItemList(workLineCompletionReportTableItemList);
        // 设置分页数据
        tableResultDto.setCurrentPage(currentPage);
        tableResultDto.setPageSize(pageSize);
        tableResultDto.setCountSize(exportStatus ? Optional.ofNullable(workSheetStatisticsList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(workSheetStatisticsPage).map(s -> s.getTotalElements()).orElse(0L));
        return tableResultDto;
    }

    /**
     * 转化工单产量统计数据为产线达成表格明细数据
     *
     * @param workSheetStatisticsList
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.worklinecompletion.WorkLineCompletionReportTableItemDTO> 产线达成表格明细数据
     */
    private List<WorkLineCompletionReportTableItemDTO> covertToWorkSheetStatisticReportTableData(List<WorkSheetStatistics> workSheetStatisticsList) {
        List<WorkLineCompletionReportTableItemDTO> workLineCompletionReportTableItemList = Lists.newArrayList();
        workSheetStatisticsList.forEach(s -> {
            WorkSheet workSheet = Optional.ofNullable(s).map(p -> p.getWorkSheet()).orElse(null);
            //产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(i -> i.getPedigree()).orElse(null);
            WorkLineCompletionReportTableItemDTO workLineCompletionReportTableItemDto = new WorkLineCompletionReportTableItemDTO();
            workLineCompletionReportTableItemDto.setWorkOrderNumber(Optional.ofNullable(workSheet).map(i -> i.getSerialNumber()).orElse(null))
                    .setSubWorkOrderNumber("")
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(p -> p.getCode()).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(p -> p.getName()).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(p -> p.getSpecification()).orElse(null))
                    .setOrganizationName(Optional.ofNullable(workSheet).map(w -> w.getOrganizationDto()).map(o -> o.getName()).orElse(null))
                    .setWorkLineName(Optional.ofNullable(workSheet).map(w -> w.getWorkLine()).map(l -> l.getName()).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getInputNumber())).orElse(null))
                    .setQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setUnQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO));
            workLineCompletionReportTableItemList.add(workLineCompletionReportTableItemDto);
        });
        return workLineCompletionReportTableItemList;
    }

    /**
     * 解析计划时间类型
     *
     * @param workLineCompletionRequestDto) 工序达成报表请求参数
     */
    private void parseTimeCategory(WorkLineCompletionReportRequestDTO workLineCompletionRequestDto) {
        // 计划时间类型
        Integer planFinishTimeCategory = workLineCompletionRequestDto.getPlanFinishTimeCategory();
        // 查询开始时间
        LocalDate startDate = workLineCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = workLineCompletionRequestDto.getEndDate();
        if (Objects.isNull(startDate) && Objects.isNull(endDate) && Objects.nonNull(planFinishTimeCategory)) {
            // 今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                workLineCompletionRequestDto.setStartDate(LocalDate.now());
                workLineCompletionRequestDto.setEndDate(LocalDate.now());
            }
            // 本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                Long day = now.getDayOfWeek().getValue() - 1L;
                LocalDate weekStart = now.minusDays(day);
                workLineCompletionRequestDto.setStartDate(weekStart);
                workLineCompletionRequestDto.setEndDate(now);
            }
            // 本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDate monthStart = now.with(TemporalAdjusters.firstDayOfMonth());
                workLineCompletionRequestDto.setStartDate(monthStart);
                workLineCompletionRequestDto.setEndDate(now);
            }
        }
    }
}
