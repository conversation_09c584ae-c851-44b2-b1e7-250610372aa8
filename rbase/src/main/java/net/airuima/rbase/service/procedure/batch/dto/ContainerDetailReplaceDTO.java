package net.airuima.rbase.service.procedure.batch.dto;

import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.dto.base.BaseDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/1/26
 */
public class ContainerDetailReplaceDTO extends BaseDTO {

    /**
     *  上一道工序被占用的容器详情
     */
    private List<ContainerDetail> containerDetailList;

    public ContainerDetailReplaceDTO() {
    }

    public ContainerDetailReplaceDTO(String status, String message) {
        super(status, message);
    }

    public ContainerDetailReplaceDTO(String status, String message, List<ContainerDetail> containerDetailList) {
        super(status, message);
        this.containerDetailList = containerDetailList;
    }

    public List<ContainerDetail> getContainerDetailList() {
        return containerDetailList;
    }

    public ContainerDetailReplaceDTO setContainerDetailList(List<ContainerDetail> containerDetailList) {
        this.containerDetailList = containerDetailList;
        return this;
    }
}
