package net.airuima.rbase.service.base.pedigree;

import jakarta.persistence.EntityManager;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.repository.base.pedigree.*;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.util.HeaderUtil;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.pedigree.dto.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/2/7
 */
@Service
@Transactional
public class PedigreeWorkflowEditorService {

    private final EntityManager entityManager;

    private final PedigreeRepository pedigreeRepository;

    private final WorkFlowRepository workFlowRepository;

    private final StepRepository stepRepository;

    private final WorkFlowStepRepository workFlowStepRepository;

    private final PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;

    private final PedigreeWorkFlowRepository pedigreeWorkFlowRepository;

    private final PedigreeStepRepository pedigreeStepRepository;

    private final PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository;

    private final PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PedigreeStepUnqualifiedItemService pedigreeStepUnqualifiedItemService;
    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;
    @Autowired
    private PedigreeReworkWorkFlowService pedigreeReworkWorkFlowService;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public PedigreeWorkflowEditorService(EntityManager entityManager, PedigreeRepository pedigreeRepository, WorkFlowRepository workFlowRepository, StepRepository stepRepository, WorkFlowStepRepository workFlowStepRepository, PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository, PedigreeWorkFlowRepository pedigreeWorkFlowRepository, PedigreeStepRepository pedigreeStepRepository, PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository, PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository) {
        this.entityManager = entityManager;
        this.pedigreeRepository = pedigreeRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
        this.workFlowStepRepository = workFlowStepRepository;
        this.pedigreeReworkWorkFlowRepository = pedigreeReworkWorkFlowRepository;
        this.pedigreeWorkFlowRepository = pedigreeWorkFlowRepository;
        this.pedigreeStepRepository = pedigreeStepRepository;
        this.pedigreeStepMaterialRuleRepository = pedigreeStepMaterialRuleRepository;
        this.pedigreeStepSpecificationRepository = pedigreeStepSpecificationRepository;
    }

    /**
     * 通过工艺路线ID获取产品谱系列表
     *
     * @param workFlowId 工艺路线ID
     * @return List<PedigreeUnqualifiedGroupDTO>
     * <AUTHOR>
     * @date 2021-05-06
     **/
    @Transactional(readOnly = true)
    public List<PedigreeUnqualifiedGroupDTO> findPedigreeByWorkFlowId(Long workFlowId) {
        List<Pedigree> pedigrees = pedigreeWorkFlowRepository.findPedigreeByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository.findByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        List<PedigreeUnqualifiedGroupDTO> list = Lists.newArrayList();
        if (ValidateUtils.isValid(pedigrees)) {
            pedigrees.forEach(pedigree -> {
                PedigreeUnqualifiedGroupDTO pedigreeUnqualifiedGroupDto = new PedigreeUnqualifiedGroupDTO();
                pedigreeUnqualifiedGroupDto.setPedigree(pedigree);
                list.add(pedigreeUnqualifiedGroupDto);
            });
        }
        if (ValidateUtils.isValid(pedigreeReworkWorkFlowList)) {
            Set<String> existKeySet = new HashSet<>();
            pedigreeReworkWorkFlowList.forEach(pedigreeReworkWorkFlow -> {
                PedigreeUnqualifiedGroupDTO pedigreeUnqualifiedGroupDto = new PedigreeUnqualifiedGroupDTO();
                 String key = pedigreeReworkWorkFlow.getPedigree().getId() + "-" + pedigreeReworkWorkFlow.getUnqualifiedGroup().getId();
                 if(!existKeySet.contains(key)){
                     pedigreeUnqualifiedGroupDto.setPedigree(pedigreeReworkWorkFlow.getPedigree()).setUnqualifiedGroup(pedigreeReworkWorkFlow.getUnqualifiedGroup());
                     list.add(pedigreeUnqualifiedGroupDto);
                     existKeySet.add(key);
                 }
            });
        }
        return list;
    }

    /**
     * 获取产品谱系绑定流程框图,包括父级
     *
     * @param pedigreeId 产品谱系
     * @param keyword    流程框图检索关键字
     * @return 流程框图列表, 按产品谱系区分
     */
    public List<WorkflowPoolDTO> findWorkflowByPedigreeAndKeyword(Long pedigreeId, Long clientId,String keyword) {
        List<WorkflowPoolDTO> workflowPoolDTOList = new ArrayList<>();
        findWorkflow(workflowPoolDTOList, pedigreeId, clientId,keyword);
        return workflowPoolDTOList;
    }

    private void findWorkflow(List<WorkflowPoolDTO> workflowPoolDTOList, Long pedigreeId,Long clientId, String keyword) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, 0L);
        pedigreeOptional.ifPresent(pedigree -> {
            List<WorkFlow> workFlows = StringUtils.isBlank(keyword) ? commonService.findWorkFlowByPedigreeIdAndClientId(pedigreeId, null) :
                    pedigreeWorkFlowRepository.findWorkFlowByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword, clientId);
            List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlows = StringUtils.isBlank(keyword) ? pedigreeReworkWorkFlowRepository.findByPedigreeIdAndClientIdAndDeleted(pedigreeId,clientId, Constants.LONG_ZERO) :
                    pedigreeReworkWorkFlowRepository.findWorkFlowByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword,clientId);
            List<WorkflowPoolDTO.WorkFlowInfo> workFlowInfoList = MapperUtils.mapAll(workFlows, WorkflowPoolDTO.WorkFlowInfo.class);
            pedigreeReworkWorkFlows.forEach(pedigreeReworkWorkFlow -> {
                WorkflowPoolDTO.WorkFlowInfo workFlowInfo = MapperUtils.map(pedigreeReworkWorkFlow.getWorkFlow(), WorkflowPoolDTO.WorkFlowInfo.class);
                workFlowInfo.setUnqualifiedGroupInfo(MapperUtils.map(pedigreeReworkWorkFlow.getUnqualifiedGroup(), WorkflowPoolDTO.WorkFlowInfo.UnqualifiedGroupInfo.class));
                workFlowInfoList.add(workFlowInfo);
            });
            WorkflowPoolDTO workflowPoolDTO = new WorkflowPoolDTO();
            workflowPoolDTO.setPedigreeId(pedigree.getId())
                    .setName(pedigree.getName())
                    .setCode(pedigree.getCode())
                    .setType(pedigree.getType())
                    .setWorkFlowInfoList(workFlowInfoList);
            workflowPoolDTOList.add(workflowPoolDTO);
            if (pedigree.getParent() == null || pedigree.getId().equals(pedigree.getParent().getId())) {
                return;
            }
            findWorkflow(workflowPoolDTOList, pedigree.getParent().getId(),clientId, keyword);
        });
    }

    /**
     * 获取产品谱系绑定工序,包括父级
     *
     * @param pedigreeId 产品谱系
     * @param keyword    工序检索关键字
     * @return 流程框图列表, 按产品谱系区分
     */
    public List<StepPoolDTO> findStepByPedigreeAndKeyword(Long pedigreeId, String keyword) {
        List<StepPoolDTO> stepPoolDTOList = new ArrayList<>();
        findStep(stepPoolDTOList, pedigreeId, keyword);
        return stepPoolDTOList;
    }

    private void findStep(List<StepPoolDTO> stepPoolDTOList, Long pedigreeId, String keyword) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, 0L);
        pedigreeOptional.ifPresent(pedigree -> {
            List<Step> steps = StringUtils.isBlank(keyword) ? pedigreeStepRepository.findStepByPedigreeIdAndDeletedAndClientId(pedigreeId, 0L,null) :
                    pedigreeStepRepository.findStepByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword,null);
            StepPoolDTO stepPoolDTO = new StepPoolDTO();
            stepPoolDTO.setPedigreeId(pedigree.getId())
                    .setName(pedigree.getName())
                    .setCode(pedigree.getCode())
                    .setType(pedigree.getType())
                    .setStepList(steps);
            stepPoolDTOList.add(stepPoolDTO);
            if (pedigree.getParent() == null || pedigree.getId().equals(pedigree.getParent().getId())) {
                return;
            }
            findStep(stepPoolDTOList, pedigree.getParent().getId(), keyword);
        });
    }


    /**
     * 新建流程框图, 并绑定产品谱系
     *
     * @param pedigreeWorkFlowEditDTO 流程框图 及 绑定的产品谱系
     * @return 保存成功的流程框图
     */
    public ResponseEntity<WorkFlow> saveWorkFlow(PedigreeWorkFlowEditDTO pedigreeWorkFlowEditDTO) {
        // 判断流程框图是否重复
        Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCode(pedigreeWorkFlowEditDTO.getWorkFlow().getCode());
        if (workFlowOptional.isPresent()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WorkFlow.class.getSimpleName()), "workFlowExist", "流程框图已存在")).body(workFlowOptional.get());
        }
        PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_SEVEN,List.of(Constants.PEDIGREE_ELEMENT));
        if(null == priorityElementConfig){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(PriorityElementConfig.class.getSimpleName()), "priorityElementConfigExist", "产品谱系工艺路线组合条件不存在")).body(null);
        }
        // 保存流程框图,并绑定到产品谱系
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeWorkFlowEditDTO.getPedigreeId(), 0L);
        WorkFlow savedWorkflow = pedigreeOptional.map(pedigree -> {
            WorkFlow workFlow = workFlowRepository.save(pedigreeWorkFlowEditDTO.getWorkFlow());
            PedigreeWorkFlow pedigreeWorkFlow = new PedigreeWorkFlow();
            pedigreeWorkFlow.setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setPriorityElementConfig(priorityElementConfig)
                    .setIsEnable(true);
            pedigreeWorkFlowRepository.save(pedigreeWorkFlow);
            return workFlow;
        }).orElse(null);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedWorkflow);
    }

    /**
     * 新增工序, 并绑定到产品谱系
     *
     * @param pedigreeStepEditDTO 工序 及 绑定的产品谱系
     * @return 保存成功的工序
     */
    public ResponseEntity<Step> saveStep(PedigreeStepEditDTO pedigreeStepEditDTO) {
        // 判断工序是否重复
        Optional<Step> stepOptional = stepRepository.findByCode(pedigreeStepEditDTO.getStep().getCode());
        if (stepOptional.isPresent()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(Step.class.getSimpleName()), "stepExist", "工序已存在")).body(stepOptional.get());
        }
        PriorityElementConfig priorityElementConfig =  priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_THREE,List.of(Constants.PEDIGREE_ELEMENT));
        if(null == priorityElementConfig){
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(PriorityElementConfig.class.getSimpleName()), "priorityElementConfigExist", "工序配置组合条件不存在")).body(null);
        }
        // 保存工序,并绑定到产品谱系
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeStepEditDTO.getPedigreeId(), 0L);
        Step savedStep = pedigreeOptional.map(pedigree -> {
            Step step = stepRepository.save(pedigreeStepEditDTO.getStep());
            PedigreeStep pedigreeStep = new PedigreeStep();
            pedigreeStep.setPedigree(pedigree).setPriorityElementConfig(priorityElementConfig)
                    .setStep(step);
            pedigreeStepRepository.save(pedigreeStep);
            return step;
        }).orElse(null);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedStep);
    }

    /**
     * 复制产品谱系绑定工序, 同时复制工序所有配置属性
     *
     * @param pedigreeStepCopyDTO 复制的工序 及 需要绑定的产品谱系
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Step> copyStep(PedigreeStepCopyDTO pedigreeStepCopyDTO) {
        Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(pedigreeStepCopyDTO.getStepId(), 0L);
        if (stepOptional.isEmpty()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(Step.class.getSimpleName()), "stepNotExist", "工序不存在")).body(null);
        }
        Optional<WorkFlow> workFlowOptional = workFlowRepository.findByIdAndDeleted(pedigreeStepCopyDTO.getWorkFlowId(), Constants.LONG_ZERO);
        if (workFlowOptional.isEmpty()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WorkFlow.class.getSimpleName()), "workFlowNotExist", "工艺路线不存在")).body(null);

        }
        Step step = stepOptional.get();
        // 判断 复制的工序 是否重复绑定
        Optional<PedigreeStep> pedigreeStepOptional = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(null,pedigreeStepCopyDTO.getTargetPedigreeId(), pedigreeStepCopyDTO.getWorkFlowId(), step.getId(),Constants.LONG_ZERO);
        if (pedigreeStepOptional.isPresent()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(PedigreeStep.class.getSimpleName(), "repeatBindPedigreeStep", "产品谱系绑定工序重复")).body(step);
        }
        Optional<Pedigree> sourcePedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeStepCopyDTO.getSourcePedigreeId(), 0L);
        sourcePedigreeOptional.ifPresent(source -> {
            Optional<Pedigree> targetPedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeStepCopyDTO.getTargetPedigreeId(), 0L);
            targetPedigreeOptional.ifPresent(target -> {
                Consumer<Object> copyEntity = s -> {
                    entityManager.detach(s);
                    entityManager.persist(s);
                };
                // 复制 产品谱系-工序 相关配置属性
                pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(null,source.getId(), pedigreeStepCopyDTO.getWorkFlowId(), step.getId(),Constants.LONG_ZERO).map(s -> {
                    s.setId(null);
                    return s.setPedigree(target);
                }).ifPresent(copyEntity);
                Optional.ofNullable(commonService.findPedigreeStepSpecification(source.getId(), pedigreeStepCopyDTO.getWorkFlowId(), step.getId(), null)).map(s -> {
                    s.setId(null);
                    return s.setPedigree(target);
                }).ifPresent(copyEntity);
                commonService.findPedigreeStepMaterialRule(null,source.getId(), pedigreeStepCopyDTO.getWorkFlowId(), step.getId()).stream().map(s -> {
                    s.setId(null);
                    return s.setPedigree(target);
                }).forEach(copyEntity);
                pedigreeStepUnqualifiedItemService.getValidPedigreeStepUnqualifiedItem(source.getId(), pedigreeStepCopyDTO.getWorkFlowId(), step.getId(), null).stream().map(s -> {
                    s.setId(null);
                    return s.setPedigree(target);
                }).forEach(copyEntity);
            });
        });
        return ResponseEntity.status(HttpStatus.OK).body(step);
    }

    /**
     * 复制框图绑定的工序数据,同时绑定到产品谱系
     *
     * @param pedigreeWorkflowCopyDTO 复制的流程框图 及 需要绑定的产品谱系
     * @return 流程框图
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<WorkFlow> copyWorkflow(PedigreeWorkflowCopyDTO pedigreeWorkflowCopyDTO) {
        // 判断新增的流程框图是否重复
        Optional<WorkFlow> existWorkFlowOptional = workFlowRepository.findByCode(pedigreeWorkflowCopyDTO.getTargetWorkflow().getCode());
        if (existWorkFlowOptional.isPresent()) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WorkFlow.class.getSimpleName()), "repeatWorkFlow", "流程框图编码重复")).body(existWorkFlowOptional.get());
        }
        // 保存复制的流程框图及属性
        WorkFlow targetWorkflow = workFlowRepository.save(pedigreeWorkflowCopyDTO.getTargetWorkflow());
        List<WorkFlowStep> workFlowSteps = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(pedigreeWorkflowCopyDTO.getSourceWorkflowId(), 0L);
        workFlowSteps.forEach(s -> {
            s.setId(null);
            s.setWorkFlow(targetWorkflow);
            entityManager.detach(s);
            entityManager.persist(s);
        });
        // 绑定到产品谱系
        Optional<Pedigree> targetPedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeWorkflowCopyDTO.getTargetPedigreeId(), 0L);
        targetPedigreeOptional.ifPresent(pedigree -> {
            PedigreeWorkFlow pedigreeWorkFlow = new PedigreeWorkFlow();
            pedigreeWorkFlow.setPedigree(pedigree)
                    .setWorkFlow(targetWorkflow)
                    .setIsEnable(true);
            pedigreeWorkFlowRepository.save(pedigreeWorkFlow);
        });
        return ResponseEntity.status(HttpStatus.OK).body(targetWorkflow);
    }

}
