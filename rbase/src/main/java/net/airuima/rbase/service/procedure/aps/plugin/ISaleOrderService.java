package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.aps.SaleOrderDetailInfo;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;

import java.util.List;

/**
 * 销售订单相关的接口
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@FuncDefault
public interface ISaleOrderService {

    @FuncInterceptor("SaleOrder")
    default String generateWsSaleOrderSerialNumber(Pedigree pedigree, WorkLine workLine){
        return null;
    }

    /**
     * 创建销售订单与工单的关联关系记录
     *
     * @param workSheet 工单信息
     * @param saleOrderDetailInfos 销售订单详情ids
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    @FuncInterceptor("SaleOrder")
    default void generateWsSaleOrder(WorkSheet workSheet, List<SaleOrderDetailInfo> saleOrderDetailInfos) {}

    /**
     * 更新工单与销售订单的投产数量
     *
     * @param workSheet 工单信息
     * @param saleOrderDetailInfos 销售订单详情ids
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    @FuncInterceptor("SaleOrder")
    default void updateWsSaleOrder(WorkSheet workSheet,List<SaleOrderDetailInfo> saleOrderDetailInfos) {}


    /**
     * 更新销售订单的完成数量
     *
     * @param workSheet 工单
     * @param number    完成数量
     */
    @FuncInterceptor("SaleOrder")
    default void calculateBatchSaleOrderAfterAllStepFinished(WorkSheet workSheet, int number) {

    }

    /**
     * 获取销售订单进度
     *
     * @param workSheets      工单列表
     * @param saleOrderNumber 销售订单数量
     * @return SaleOrderProcessDTO
     */
    @FuncInterceptor("SaleOrder")
    default SaleOrderProcessDTO getWsOrderProcessDTO(List<WorkSheet> workSheets, Integer saleOrderNumber){return null;}
}
