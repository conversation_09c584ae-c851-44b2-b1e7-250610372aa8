package net.airuima.rbase.service.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.material.MaterialBatchAbatementDTO;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWsCheckMaterialDTO;
import net.airuima.rbase.integrate.rwms.IReceiveOrderSyncWmsService;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialReturnRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsMaterialReturnService extends CommonJpaService<WsMaterialReturn> {
    private static final String WS_MATERIAL_RETURN_ENTITY_GRAPH = "WsMaterialReturnEntityGraph";
    @Autowired
    private WsMaterialReturnRepository wsMaterialReturnRepository;

    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;

    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;

    @Autowired
    private WsCheckMaterialDetailService wsCheckMaterialDetailService;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    @Autowired
    private PIWipLedgerService wipLedgerServices;

    @Autowired
    private IReceiveOrderSyncWmsService[] receiveOrderSyncWmsServices;


    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsMaterialReturn> find(Specification<WsMaterialReturn> spec, Pageable pageable) {
        return wsMaterialReturnRepository.findAll(spec,pageable,new NamedEntityGraph(WS_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsMaterialReturn> find(Specification<WsMaterialReturn> spec) {
        return wsMaterialReturnRepository.findAll(spec,new NamedEntityGraph(WS_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsMaterialReturn> findAll(Pageable pageable) {
        return wsMaterialReturnRepository.findAll(pageable,new NamedEntityGraph(WS_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    /**
     * 保存工单退料记录
     * @param rollBackMaterialDto 工单退料信息
     * <AUTHOR>
     * @date  2022/3/28
     * @return BaseDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Klock(
            keys = {"#rollBackMaterialDto.workSheetId","#rollBackMaterialDto.operatorId"},
            waitTime = 60,
            leaseTime = 60,
            lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BaseDTO saveWsMaterialReturn(RollBackMaterialDTO rollBackMaterialDto) {

        List<MaterialBatchAbatementDTO> materialBatchAbatementDtoList = Lists.newArrayList();

        BaseDTO baseDto = validateMaterialReturn(rollBackMaterialDto, materialBatchAbatementDtoList);

        if (Constants.KO.equals(baseDto.getStatus())){
            return baseDto;
        }

        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(rollBackMaterialDto.getWorkSheetId(), Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.workSheetNotExist", "生产总工单不存在"));
        if (ValidateUtils.isValid(materialBatchAbatementDtoList)){
            List<WsMaterialReturn> wsMaterialReturnList = new ArrayList<>();
            materialBatchAbatementDtoList.forEach(materialBatchAbatementDto -> {
                WsMaterialBatch wsMaterialBatch = materialBatchAbatementDto.getWsMaterialBatch();
                // 处理线边仓工单库存、新增台账
                wipLedgerServices.processWarehouseAndSaveLedger(workSheet.getWorkLine(),wsMaterialBatch,-materialBatchAbatementDto.getNumber(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory(),rollBackMaterialDto.getOperatorId());
                //修改工单库存剩余数量
                wsMaterialBatch.setLeftNumber(NumberUtils.subtract(wsMaterialBatch.getLeftNumber(),materialBatchAbatementDto.getNumber()).doubleValue());
                wsMaterialBatchRepository.save(wsMaterialBatch);
                //添加工单退料记录
                WsMaterialReturn wsMaterialReturn = new WsMaterialReturn(wsMaterialBatch.getWorkSheet(),wsMaterialBatch.getMaterialId(), rollBackMaterialDto.getOperatorId(), wsMaterialBatch.getBatch(),materialBatchAbatementDto.getNumber());
                wsMaterialReturn.setNote(materialBatchAbatementDto.getNote());
                wsMaterialReturn = wsMaterialReturnRepository.save(wsMaterialReturn);
                wsMaterialReturnList.add(wsMaterialReturn);
            });
            if (!wsMaterialReturnList.isEmpty()) {
                receiveOrderSyncWmsServices[0].syncReceiveOrder(wsMaterialReturnList);
            }
        }

        return new BaseDTO(Constants.OK,"saveWsMaterialReturnSuccess");
    }


    /**
     * 验证工单退料数据
     *
     * @param rollBackMaterialDto           物料回退数据
     * @param materialBatchAbatementDtoList 回退物料列表
     * @return BaseDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    public BaseDTO validateMaterialReturn(RollBackMaterialDTO rollBackMaterialDto, List<MaterialBatchAbatementDTO> materialBatchAbatementDtoList){

        if (null == rollBackMaterialDto.getWorkCellId()){
            if (!ValidateUtils.isValid(rollBackMaterialDto.getWsRollBackMaterialDtoList())){
                return new BaseDTO(Constants.KO,"wsMaterialReturnIsNull");
            }
            rollBackMaterialDto.getWsRollBackMaterialDtoList().forEach(wsRollBackMaterialDto -> {
                //工单物料库存
                Optional<WsMaterialBatch> wsMaterialBatchOptional = wsMaterialBatchRepository.findByIdAndDeleted(wsRollBackMaterialDto.getWsMaterialBatchId(), Constants.LONG_ZERO);
                //剩余数量大于等于回退数量
                if (wsMaterialBatchOptional.isPresent() && wsMaterialBatchOptional.get().getLeftNumber() >= wsRollBackMaterialDto.getNumber()){
                    MaterialBatchAbatementDTO materialBatchAbatementDto = new MaterialBatchAbatementDTO();
                    materialBatchAbatementDto.setWsMaterialBatch(wsMaterialBatchOptional.get()).setNumber(wsRollBackMaterialDto.getNumber())
                            .setNote(wsRollBackMaterialDto.getNote());
                    materialBatchAbatementDtoList.add(materialBatchAbatementDto);
                }
            });
        }else {
            if (!ValidateUtils.isValid(rollBackMaterialDto.getWsWorkCellRollBackMaterialDtoList())){
                return new BaseDTO(Constants.KO,"wsMaterialReturnIsNull");
            }
            rollBackMaterialDto.getWsWorkCellRollBackMaterialDtoList().forEach(wsWorkCellRollBackMaterialDto -> {
                //工位库存
                Optional<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchOptional = wsWorkCellMaterialBatchRepository.findByIdAndDeleted(wsWorkCellRollBackMaterialDto.getWsWorkCellMaterialBatchId(), Constants.LONG_ZERO);
                if ( wsWorkCellMaterialBatchOptional.isPresent() && wsWorkCellMaterialBatchOptional.get().getLeftNumber() >= wsWorkCellRollBackMaterialDto.getNumber() ){
                    MaterialBatchAbatementDTO materialBatchAbatementDto = new MaterialBatchAbatementDTO();
                    materialBatchAbatementDto.setWsWorkCellMaterialBatch(wsWorkCellMaterialBatchOptional.get()).setNumber(wsWorkCellRollBackMaterialDto.getNumber())
                            .setNote(wsWorkCellRollBackMaterialDto.getNote());
                    materialBatchAbatementDtoList.add(materialBatchAbatementDto);
                }
            });
        }

        return new BaseDTO(Constants.OK);
    }

    /**
     * 工单退料单同步
     *
     * @param syncWsCheckMaterialDtoList 退料单同步数据
     * @return List
     */
    public List<SyncResultDTO> syncWsMaterialReturn(List<SyncWsCheckMaterialDTO> syncWsCheckMaterialDtoList) {
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncWsCheckMaterialDtoList)) {
            // 使用工单分组
            Map<String, List<SyncWsCheckMaterialDTO>> syncWsCheckMaterialDTOMap = syncWsCheckMaterialDtoList.stream().collect(Collectors.groupingBy(SyncWsCheckMaterialDTO::getSerialNumber));
            syncWsCheckMaterialDTOMap.forEach((serialNumber, syncWsCheckMaterials) -> {

                WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
                if (workSheet == null) {
                    syncWsCheckMaterials.forEach(syncWsCheckMaterialDto -> {
                        syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, serialNumber+ " 当前工单不存在"));
                    });
                    return;
                }

                // 首先要判断工单物料库存 + 工单未核料数量 是否 < 退料总数，扣减方式为 先扣工单物料库存 再扣工单未核料的数量 已后进先出的顺序来减扣
                // 获取核料明细
                List<WsCheckMaterialDetail> wsCheckMaterialDetailList = wsCheckMaterialDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
                if (!ValidateUtils.isValid(wsCheckMaterialDetailList)) {
                    syncWsCheckMaterials.forEach(syncWsCheckMaterialDto -> {
                        syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, syncWsCheckMaterialDto.getSerialNumber()+ "工单不存在核料信息"));
                    });
                    return;
                }

                // 获取工单物料库存
                List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndLeftNumberIsGreaterThanAndDeleted(workSheet.getId(), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);

                syncWsCheckMaterials.forEach(syncWsCheckMaterialDto -> {
                    syncWsCheckMaterialDto.getCheckMaterialInfoList().forEach(checkMaterialInfo -> {
                        // 获取物料信息
                        MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(checkMaterialInfo.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
                        if (materialDto == null) {
                            syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, checkMaterialInfo.getMaterialCode() + "物料编码不存在"));
                            return;
                        }
                        List<WsMaterialBatch> newWsMaterialBatchList = Lists.newArrayList();
                        List<WsCheckMaterialDetail> newWsCheckMaterialDetailList;
                        // 获取当前工单库存对应的【批次-物料 可退数量的总和】
                        if (ValidateUtils.isValid(wsMaterialBatchList)) {
                            newWsMaterialBatchList = wsMaterialBatchList.stream()
                                    .filter(wsMaterialBatch -> ObjectUtils.equals(wsMaterialBatch.getBatch(), checkMaterialInfo.getMaterialBatch()))
                                    .filter(wsMaterialBatch -> ObjectUtils.equals(wsMaterialBatch.getMaterialDto().getCode(), checkMaterialInfo.getMaterialCode()))
                                    // 已后进先出的顺序来减扣
                                    .sorted((time1, time2) -> time2.getCreatedDate().compareTo(time1.getCreatedDate()))
                                    .collect(Collectors.toList());
                        }
                        // 获取核料明细
                        newWsCheckMaterialDetailList = wsCheckMaterialDetailList.stream()
                                .filter(wsMaterialBatch -> ObjectUtils.equals(wsMaterialBatch.getBatch(), checkMaterialInfo.getMaterialBatch()))
                                .filter(wsMaterialBatch -> ObjectUtils.equals(wsMaterialBatch.getMaterialDto().getCode(), checkMaterialInfo.getMaterialCode()))
                                // 已后进先出的顺序来减扣
                                .sorted((time1, time2) -> time2.getCreatedDate().compareTo(time1.getCreatedDate()))
                                .collect(Collectors.toList());

                        // 计算工单物料库存 + 工单未核料的 最大可退数量
                        double number = computeNumber(newWsMaterialBatchList, newWsCheckMaterialDetailList);

                        if (checkMaterialInfo.getMaterialNumber() > number) {
                            syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO,
                                    "退料单" + syncWsCheckMaterialDto.getCheckMaterialCode() +
                                            "物料" + checkMaterialInfo.getMaterialCode() +
                                            "批次" + checkMaterialInfo.getMaterialBatch() +
                                            "最大只能退" + number + "数量"));
                            return;
                        }
                        // 处理工单退料信息
                        dealWsMaterialReturn(checkMaterialInfo, newWsMaterialBatchList, newWsCheckMaterialDetailList);
                        // 保存工单退料详情
                        WsMaterialReturn wsMaterialReturn = new WsMaterialReturn();
                        wsMaterialReturn.setWorkSheet(workSheet)
                                .setMaterialId(materialDto.getId())
                                .setBatch(checkMaterialInfo.getMaterialBatch())
                                .setNumber(checkMaterialInfo.getMaterialNumber())
                                .setRecordTime(LocalDateTime.now())
                                .setRecordDate(LocalDateTime.now());
                        wsMaterialReturnRepository.save(wsMaterialReturn);
                    });
                });
            });
        }
        return syncResultDtoList;
    }

    /**
     * 处理工单退料信息
     *
     * @param checkMaterialInfo         退料信息
     * @param wsMaterialBatchList       工单物料库存列表
     * @param wsCheckMaterialDetailList 工单未核料数量列表
     */
    private void dealWsMaterialReturn(SyncWsCheckMaterialDTO.CheckMaterialInfo checkMaterialInfo, List<WsMaterialBatch> wsMaterialBatchList, List<WsCheckMaterialDetail> wsCheckMaterialDetailList) {
        // 先退工单物料库存， 再退工单未核料数量
        AtomicDouble returnNumber = new AtomicDouble(checkMaterialInfo.getMaterialNumber());
        wsMaterialBatchList.forEach(wsMaterialBatch -> {
            double leftNumber = NumberUtils.subtract(wsMaterialBatch.getLeftNumber(), returnNumber.get()).doubleValue();
            // 剩余数量小于0, 说明工单物料库存不够
            if (leftNumber < Constants.DOUBLE_ZERRO) {
                returnNumber.set(leftNumber * Constants.APPROVE);
                // 工单物料库存设置为0
                wsMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                wsMaterialBatchRepository.save(wsMaterialBatch);
                return;
            }
            // >= 0 说明够减扣
            wsMaterialBatch.setLeftNumber(leftNumber);
            returnNumber.set(Constants.DOUBLE_ZERRO);
            wsMaterialBatchRepository.save(wsMaterialBatch);
        });

        // 如果减去工单库存数量之后还有剩余数量，那么开始退工单未核料数量
        if (returnNumber.get() != Constants.INT_ZERO) {
            wsCheckMaterialDetailList.forEach(wsCheckMaterialDetail -> {
                double leftNumber = NumberUtils.subtract(wsCheckMaterialDetail.getUncheckNumber(), returnNumber.get()).doubleValue();
                // 剩余数量小于0, 说明未核料数量不够
                if (leftNumber < Constants.DOUBLE_ZERRO) {
                    returnNumber.set(leftNumber * Constants.APPROVE);
                    // 修改工单物料库存数量 leftNumber 为负数
                    wsCheckMaterialDetail.setUncheckNumber(Constants.DOUBLE_ZERRO);
                    wsCheckMaterialDetailRepository.save(wsCheckMaterialDetail);
                    return;
                }
                // >= 0 说明够减扣
                wsCheckMaterialDetail.setUncheckNumber(leftNumber);
                returnNumber.set(Constants.DOUBLE_ZERRO);
                wsCheckMaterialDetailRepository.save(wsCheckMaterialDetail);
            });
        }
    }

    /**
     * 计算工单物料库存 + 工单未核料的总数量
     *
     * @param wsMaterialBatchList       工单物料库存列表
     * @param wsCheckMaterialDetailList 工单物料库存列表
     * @return double
     */
    private double computeNumber(List<WsMaterialBatch> wsMaterialBatchList, List<WsCheckMaterialDetail> wsCheckMaterialDetailList) {
        double number = Constants.DOUBLE_ZERRO;
        // 获取当前工单库存对应的【批次-物料 可退数量的总和】
        if (ValidateUtils.isValid(wsMaterialBatchList)) {
            // 获取剩余数量的总和
            double sumNumber = wsMaterialBatchList.stream().mapToDouble(WsMaterialBatch::getLeftNumber).sum();
            number = NumberUtils.add(sumNumber, number).doubleValue();
        }
        // 获取未核料剩余数量的总和
        double sumNumber = wsCheckMaterialDetailList.stream().mapToDouble(WsCheckMaterialDetail::getUncheckNumber).sum();
        return NumberUtils.add(sumNumber, number).doubleValue();
    }
}
