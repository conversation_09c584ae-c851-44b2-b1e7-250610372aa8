package net.airuima.rbase.service.procedure.wearingpart;

import net.airuima.rbase.domain.procedure.wearingpart.LatestStepWearingPart;
import net.airuima.rbase.repository.procedure.wearingpart.LatestStepWearingPartRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序最新使用易损件Service
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class LatestStepWearingPartService extends CommonJpaService<LatestStepWearingPart> {
    private final LatestStepWearingPartRepository latestStepWearingPartRepository;

    public LatestStepWearingPartService(LatestStepWearingPartRepository latestStepWearingPartRepository) {
        this.latestStepWearingPartRepository = latestStepWearingPartRepository;
    }

    @Override
    public LatestStepWearingPart save(LatestStepWearingPart entity) {
        return super.save(entity);
    }

    public List<LatestStepWearingPart> saveAll(List<LatestStepWearingPart> entities) {
        return this.save(entities);
    }
}
