package net.airuima.rbase.service.procedure.quality;

import net.airuima.rbase.domain.procedure.quality.SnStepFppStatus;
import net.airuima.rbase.repository.procedure.quality.SnStepFppStatusRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 单支FPP状态Service
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnStepFppStatusService extends CommonJpaService<SnStepFppStatus> {
    private final SnStepFppStatusRepository snStepFppStatusRepository;

    public SnStepFppStatusService(SnStepFppStatusRepository snStepFppStatusRepository) {
        this.snStepFppStatusRepository = snStepFppStatusRepository;
    }

    @Override
    public Page<SnStepFppStatus> find(Specification<SnStepFppStatus> spec, Pageable pageable) {
        return snStepFppStatusRepository.findAll(spec, pageable);
    }

    @Override
    public List<SnStepFppStatus> find(Specification<SnStepFppStatus> spec) {
        return snStepFppStatusRepository.findAll(spec);
    }

    @Override
    public Page<SnStepFppStatus> findAll(Pageable pageable) {
        return snStepFppStatusRepository.findAll(pageable);
    }
}
