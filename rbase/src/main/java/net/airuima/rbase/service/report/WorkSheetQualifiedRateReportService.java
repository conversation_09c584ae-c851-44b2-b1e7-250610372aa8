package net.airuima.rbase.service.report;

import com.alibaba.fastjson.JSON;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.ReportTimeRangeEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.report.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单合格率报表Service
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetQualifiedRateReportService {

    private final WorkSheetRepository workSheetRepository;

    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PedigreeRepository pedigreeRepository;

    /**
     * 实际完成时间
     */
    private static final String ACTUAL_END_DATE = "actualEndDate";

    public WorkSheetQualifiedRateReportService(WorkSheetRepository workSheetRepository) {
        this.workSheetRepository = workSheetRepository;
    }

    /**
     * 工单合格率图形数据
     *
     * @param workSheetQualifiedRateRequestDto 工单报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportChartResultDTO 工单报表统计图表返回结果
     */
    @Transactional(readOnly = true)
    public WorkSheetQualifiedRateReportChartResultDTO getWorkSheetQualifiedReportChart(WorkSheetQualifiedRateReportRequestDTO workSheetQualifiedRateRequestDto) {
        //解析开始和结束时间
        parseTimeFinishTimeCategory(workSheetQualifiedRateRequestDto);
        // 产品谱系
        Long pedigreeId = workSheetQualifiedRateRequestDto.getPedigreeId();
        // 产线
        Long workLineId = workSheetQualifiedRateRequestDto.getWorkLineId();
        // 工单号
        String serialNumber = StringUtils.isNotBlank(workSheetQualifiedRateRequestDto.getSerialNumber())?workSheetQualifiedRateRequestDto.getSerialNumber():null;
        // 开始查询时间
        LocalDateTime startDateTime = workSheetQualifiedRateRequestDto.getStartDate();
        // 结束查询时间
        LocalDateTime endDateTime = workSheetQualifiedRateRequestDto.getEndDate();
        List<Long> pedigreeIdList = null;
        // 合格率图形查询结果
        if(Objects.nonNull(pedigreeId)){
            pedigreeIdList = commonService.getMinLevelPedigreeIds(pedigreeRepository.getReferenceById(pedigreeId));
        }
        pedigreeIdList = CollectionUtils.isEmpty(pedigreeIdList)?null:pedigreeIdList;
        QualifiedRateNumberQueryDTO qualifiedRateNumberQueryDto = workSheetRepository.countWorkSheetQualifiedRateNumber(startDateTime, endDateTime, workLineId, pedigreeIdList, serialNumber,Constants.LONG_ZERO);
        // 合格数
        Long qualifiedNumber = Optional.ofNullable(qualifiedRateNumberQueryDto).map(QualifiedRateNumberQueryDTO::getWorkSheetQualifiedNumber).orElse(Constants.LONG_ZERO);
        // 不合格数
        Long unQualifiedNumber = Optional.ofNullable(qualifiedRateNumberQueryDto).map(QualifiedRateNumberQueryDTO::getWorkSheetUnQualifiedNumber).orElse(Constants.LONG_ZERO);
        // 在线返修合格数
        Long reworkQualifiedNumber = Optional.ofNullable(qualifiedRateNumberQueryDto).map(QualifiedRateNumberQueryDTO::getWorkSheetReworkQualifiedNumber).orElse(Constants.LONG_ZERO);
        // 设置合格率返回结果
        WorkSheetQualifiedRateReportChartResultDTO chartResultDto = new WorkSheetQualifiedRateReportChartResultDTO();
        chartResultDto.setWorkSheetCount(Optional.ofNullable(qualifiedRateNumberQueryDto).map(QualifiedRateNumberQueryDTO::getWorkSheetCount).orElse(Constants.LONG_ZERO))
                .setQualifiedNumber(qualifiedNumber)
                .setNumber(Optional.ofNullable(qualifiedRateNumberQueryDto).map(QualifiedRateNumberQueryDTO::getWorkSheetPlanNumber).orElse(Constants.LONG_ZERO));
        // 直通率
        double firstQualifiedRate = 0d;
        // 合格率
        double qualifiedRate = 0d;
        // 实际完成数
        long actualNumber = qualifiedNumber + unQualifiedNumber;
        if (Constants.LONG_ZERO != actualNumber) {
            // 直通率计算
            firstQualifiedRate = BigDecimal.valueOf((float) ((qualifiedNumber - reworkQualifiedNumber) * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            // 合格率计算
            qualifiedRate = BigDecimal.valueOf((float) (qualifiedNumber * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        // 设置直通率和合格率
        chartResultDto.setFirstQualifiedRate(firstQualifiedRate).setQualifiedRate(qualifiedRate);
        // 合格率相关图形查询结果
        List<QualifiedChartQueryDataDTO> qualifiedChartQueryDataList = workSheetRepository.getQualifiedChartData(startDateTime, endDateTime, workLineId, pedigreeIdList,serialNumber, Constants.LONG_ZERO);
        // 设置合格率折线图数据
        setQualifiedLineChartData(qualifiedChartQueryDataList, chartResultDto);
        return chartResultDto;
    }

    /**
     * 设置合格率折线图数据
     *
     * @param queryDataList  折线图数据查询结果
     * @param chartResultDto 合格率报表图形数据
     */
    private void setQualifiedLineChartData(List<QualifiedChartQueryDataDTO> queryDataList, WorkSheetQualifiedRateReportChartResultDTO chartResultDto) {
        //直通率折线图数据
        List<FirstQualifiedChartDataDTO> firstQualifiedChartDataList = Lists.newLinkedList();
        //合格率折线图数据
        List<QualifiedChartDataDTO> qualifiedChartDataList = Lists.newLinkedList();
        queryDataList.forEach(q -> {
            //直通率折线图数据明细
            FirstQualifiedChartDataDTO firstQualifiedChartDataDto = new FirstQualifiedChartDataDTO();
            //合格率折线图数据明细
            QualifiedChartDataDTO qualifiedChartDataDto = new QualifiedChartDataDTO();
            // 时间完成数
            long actualNumber = Optional.ofNullable(q).map(QualifiedChartQueryDataDTO::getActualNumber).orElse(Constants.LONG_ZERO);
            //在线返修合格数
            Long reworkQualifiedNumber = Optional.ofNullable(q).map(QualifiedChartQueryDataDTO::getReworkQualifiedNumber).orElse(Constants.LONG_ZERO);
            // 合格数
            Long qualifiedNumber = Optional.ofNullable(q).map(QualifiedChartQueryDataDTO::getQualifiedNumber).orElse(Constants.LONG_ZERO);
            // 产品型号
            String name = Optional.ofNullable(q).map(QualifiedChartQueryDataDTO::getName).orElse("");
            // 折线图x轴 时间
            String time = Optional.ofNullable(q).map(QualifiedChartQueryDataDTO::getTime).map(i -> i.length() > 5 ? i.substring(5) : "").orElse("");
            // 直通率
            double firstQualifiedRate = 0d;
            // 合格率
            double qualifiedRate = 0d;
            if (Constants.LONG_ZERO != actualNumber) {
                // 直通率计算
                firstQualifiedRate = BigDecimal.valueOf((float) ((qualifiedNumber - reworkQualifiedNumber) * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                // 合格率计算
                qualifiedRate = BigDecimal.valueOf((float) ((qualifiedNumber) * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            // 设置直通率折线图数据
            firstQualifiedChartDataDto.setName(name)
                    .setValue(firstQualifiedRate)
                    .setTime(time);
            // 设置合格率折线图数据
            qualifiedChartDataDto.setName(name)
                    .setValue(qualifiedRate)
                    .setTime(time);
            firstQualifiedChartDataList.add(firstQualifiedChartDataDto);
            qualifiedChartDataList.add(qualifiedChartDataDto);
        });
        if(!CollectionUtils.isEmpty(firstQualifiedChartDataList)){
            firstQualifiedChartDataList.sort(Comparator.comparing(FirstQualifiedChartDataDTO::getTime));
        }
        if(!CollectionUtils.isEmpty(qualifiedChartDataList)){
            qualifiedChartDataList.sort(Comparator.comparing(QualifiedChartDataDTO::getTime));
        }
        // 设置折线图数据
        chartResultDto.setQualifiedChartDataList(qualifiedChartDataList).setFirstQualifiedChartDataList(firstQualifiedChartDataList);
    }

    /**
     * 工单合格率表格数据
     *
     * @param requestDto 工单报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportTableResultDTO 工单报表表格数据返回结果
     */
    @Transactional(readOnly = true)
    public WorkSheetQualifiedRateReportTableResultDTO getWorkSheetQualifiedReportTable(WorkSheetQualifiedRateReportRequestDTO requestDto) {
        // 设置开始查询时间和结束查询时间
        parseTimeFinishTimeCategory(requestDto);
        //产品谱系
        Long pedigreeId = requestDto.getPedigreeId();
        List<Long> pedigreeIdList;
        // 合格率图形查询结果
        if(Objects.nonNull(pedigreeId)){
            pedigreeIdList = commonService.getMinLevelPedigreeIds(pedigreeRepository.getReferenceById(pedigreeId));
        } else {
            pedigreeIdList = null;
        }
        pedigreeIdList = CollectionUtils.isEmpty(pedigreeIdList)?null:pedigreeIdList;
        //产线
        Long workLineId = requestDto.getWorkLineId();
        // 工单号
        String serialNumber = StringUtils.isNotBlank(requestDto.getSerialNumber())?requestDto.getSerialNumber():null;
        // 开始查询时间
        LocalDateTime startDateTime = requestDto.getStartDate();
        //结束查询时间
        LocalDateTime endDateTime = requestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = requestDto.getExportStatus();
        //当前叶
        Integer currentPage = requestDto.getCurrentPage();
        // 分页大小
        Integer pageSize = requestDto.getPageSize();
        // 分页查询
        List<Long> finalPedigreeIdList = pedigreeIdList;
        Specification<WorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //统计生成完成和已结单的
            CriteriaBuilder.In<Integer> in = criteriaBuilder.in(root.get("status"));
            in.value(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
            in.value(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
            predicateList.add(in);
            //只统计工单类型为正常单的
            Predicate categoryPredicate = criteriaBuilder.equal(root.get("category"), ConstantsEnum.WORK_SHEET_CUSTOM_CATEGORY.getCategoryName());
            predicateList.add(categoryPredicate);
            if(StringUtils.isNotBlank(serialNumber)){
                predicateList.add(criteriaBuilder.like(root.get("serialNumber").as(String.class), serialNumber+"%"));
            }
            //开始时间和结束时间筛选
            if ( endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), startDateTime));
            }
            // 产品谱系筛选
            if (!CollectionUtils.isEmpty(finalPedigreeIdList)) {
                CriteriaBuilder.In<Long> pedigreePredicate  = criteriaBuilder.in(root.get("pedigree").get("id"));
                for (Long id : finalPedigreeIdList) {
                    pedigreePredicate.value(id);
                }
                predicateList.add(pedigreePredicate);
            }
            // 产线筛选
            if (workLineId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("workLine").get("id"), workLineId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).orderBy(criteriaBuilder.desc(root.get(ACTUAL_END_DATE))).getRestriction();
        };
        // 工单集合
        List<WorkSheet> workSheetList;
        Page<WorkSheet> workSheetPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            workSheetList = workSheetRepository.findAll(specification,new NamedEntityGraph("workSheetEntityGraph"));
        } else {
            //分页查询
            workSheetPage = workSheetRepository.findAll(specification, PageRequest.of(currentPage, pageSize),new NamedEntityGraph("workSheetEntityGraph"));
            workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        // 设置报表表格数据
        WorkSheetQualifiedRateReportTableResultDTO reportTableResultDto = new WorkSheetQualifiedRateReportTableResultDTO();
        // 转换工单数据为合格率表格数据
        List<WorkSheetQualifiedRateReportTableItemDTO> qualifiedRateReportTableItemList = convertWorkSheetToQualifiedTableData(workSheetList);
        reportTableResultDto.setQualifiedRateReportTableItemList(qualifiedRateReportTableItemList);
        // 设置分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(exportStatus ? Optional.of(workSheetList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(workSheetPage).map(Page::getTotalElements).orElse(0L));
        return reportTableResultDto;
    }

    /**
     * 转换工单数据为合格率表格数据
     *
     * @param workSheetList 工单集合
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportTableItemDTO> 合格率表格数据
     */
    private List<WorkSheetQualifiedRateReportTableItemDTO> convertWorkSheetToQualifiedTableData(List<WorkSheet> workSheetList) {
        // 合格率报表表格数据
        List<WorkSheetQualifiedRateReportTableItemDTO> tableDataList = Lists.newLinkedList();
        if(CollectionUtils.isEmpty(workSheetList)){
            return tableDataList;
        }
        boolean subWsProductionMode = commonService.subWsProductionMode();
        List<WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo> unqualifiedItemInfoList = new ArrayList<>();
        if(subWsProductionMode && !CollectionUtils.isEmpty(workSheetList)){
            unqualifiedItemInfoList = wsStepUnqualifiedItemRepository.findWorkSheetUnqualifiedItemWhenSubWorkSheet(workSheetList.stream().map(WorkSheet::getId).toList());
        }else if(!subWsProductionMode && !CollectionUtils.isEmpty(workSheetList)){
            unqualifiedItemInfoList = wsStepUnqualifiedItemRepository.findWorkSheetUnqualifiedItemWhenWorkSheet(workSheetList.stream().map(WorkSheet::getId).toList());
        }
        Map<String,List<WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo>> unqualifiedItemInfoGroup;
        if(!CollectionUtils.isEmpty(unqualifiedItemInfoList)){
            unqualifiedItemInfoGroup = unqualifiedItemInfoList.stream().collect(Collectors.groupingBy(WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo::getSerialNumber));
        } else {
            unqualifiedItemInfoGroup = new HashMap<>();
        }
        DecimalFormat df = new DecimalFormat("0.00%");
        workSheetList.forEach(w -> {
            //产品谱系
            Pedigree pedigree = Optional.ofNullable(w).map(WorkSheet::getPedigree).orElse(null);
            //合格数
            Long qualifiedNumber = Optional.ofNullable(w).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO);
            // 不合格数
            Long unQualifiedNumber = Optional.ofNullable(w).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO);
            // 在线返修合格数
            Long reworkQualifiedNumber = Optional.ofNullable(w).map(i -> Long.valueOf(i.getReworkQualifiedNumber())).orElse(Constants.LONG_ZERO);
            // 直通率
            double firstQualifiedRate = 0d;
            // 合格率
            double qualifiedRate = 0d;
            // 实际完成总数
            long actualNumber = qualifiedNumber + unQualifiedNumber;
            if (Constants.LONG_ZERO != actualNumber) {
                // 直通率计算
                firstQualifiedRate = BigDecimal.valueOf((float) ((qualifiedNumber - reworkQualifiedNumber) * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                // 合格率计算
                qualifiedRate = BigDecimal.valueOf((float) (qualifiedNumber * 100) / actualNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            // 设置表格明细数据
            WorkSheetQualifiedRateReportTableItemDTO reportTableItemDto = new WorkSheetQualifiedRateReportTableItemDTO();
            reportTableItemDto.setActualEndDate(Optional.ofNullable(w).map(WorkSheet::getActualEndDate).orElse(null))
                    .setWorkSheetSerialNumber(Optional.ofNullable(w).map(WorkSheet::getSerialNumber).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse(null))
                    .setWorkLineName(Optional.ofNullable(w).map(WorkSheet::getWorkLine).map(WorkLine::getName).orElse(null))
                    .setNumber(Optional.ofNullable(w).map(i -> Long.valueOf(i.getNumber())).orElse(Constants.LONG_ZERO))
                    .setQualifiedNumber(Optional.ofNullable(w).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setFirstQualifiedNumber(qualifiedNumber - reworkQualifiedNumber)
                    .setFirstQualifiedRate(firstQualifiedRate)
                    .setUnQualifiedNumber(unQualifiedNumber)
                    .setQualifiedRate(qualifiedRate);
            if(!CollectionUtils.isEmpty(unqualifiedItemInfoGroup) && unqualifiedItemInfoGroup.containsKey(w.getSerialNumber())){
                List<WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo> unqualifiedItemInfos = unqualifiedItemInfoGroup.get(w.getSerialNumber());
                long sumNumber = unqualifiedItemInfos.stream().mapToLong(WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo::getNumber).sum();
                double totalPercent = Constants.INT_ZERO;
                for (int index = 0; index < unqualifiedItemInfos.size(); index++){
                    WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo unqualifiedItemInfo  = unqualifiedItemInfos.get(index);
                    if(index == unqualifiedItemInfos.size() - Constants.INT_ONE){
                        unqualifiedItemInfo.setPercent(df.format(Constants.INT_ONE - totalPercent));
                    }else {
                        totalPercent = totalPercent+NumberUtils.divide(unqualifiedItemInfo.getNumber(),sumNumber,Constants.INT_TWO).doubleValue();
                        unqualifiedItemInfo.setPercent(df.format(NumberUtils.divide(unqualifiedItemInfo.getNumber(),sumNumber,Constants.INT_TWO).doubleValue()));
                    }
                }
                reportTableItemDto.setUnqualifiedItemInfoList(unqualifiedItemInfos);
            }
            tableDataList.add(reportTableItemDto);
        });
        return tableDataList;
    }

    /**
     * 计划完成时间解析
     *
     * @param workSheetQualifiedRateRequestDto 工单报表请求参数
     */
    private void parseTimeFinishTimeCategory(WorkSheetQualifiedRateReportRequestDTO workSheetQualifiedRateRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = workSheetQualifiedRateRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = workSheetQualifiedRateRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = workSheetQualifiedRateRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                workSheetQualifiedRateRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                workSheetQualifiedRateRequestDto.setEndDate(LocalDateTime.now());
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                workSheetQualifiedRateRequestDto.setStartDate(weekStart);
                workSheetQualifiedRateRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                workSheetQualifiedRateRequestDto.setStartDate(monthStart);
                workSheetQualifiedRateRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
        }
    }

}
