package net.airuima.rbase.service.procedure.wearingpart;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;
import net.airuima.rbase.repository.procedure.wearingpart.WearingPartResetHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *易损件重置历史Service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WearingPartResetHistoryService extends CommonJpaService<WearingPartResetHistory> {

    @Autowired
    private WearingPartResetHistoryRepository wearingPartResetHistoryRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WearingPartResetHistory> find(Specification<WearingPartResetHistory> spec, Pageable pageable) {
        return wearingPartResetHistoryRepository.findAll(spec,pageable);
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WearingPartResetHistory> find(Specification<WearingPartResetHistory> spec) {
        return wearingPartResetHistoryRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WearingPartResetHistory> findAll(Pageable pageable) {
        return wearingPartResetHistoryRepository.findAll(pageable);
    }

    @Override
    public WearingPartResetHistory save(WearingPartResetHistory entity) {
        return super.save(entity);
    }
}
