package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产过程产生不良记录Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnUnqualifiedItemService extends CommonJpaService<SnUnqualifiedItem> {
    private static final String SN_UNQUALIFIED_ITEM_ENTITY_GRAPH = "snUnqualifiedItemEntityGraph";
    private final SnUnqualifiedItemRepository snUnqualifiedItemRepository;

    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;

    public SnUnqualifiedItemService(SnUnqualifiedItemRepository snUnqualifiedItemRepository) {
        this.snUnqualifiedItemRepository = snUnqualifiedItemRepository;
    }

    @Override
    @FetchMethod
    public Page<SnUnqualifiedItem> find(Specification<SnUnqualifiedItem> spec, Pageable pageable) {
        Page<SnUnqualifiedItem> page = snUnqualifiedItemRepository.findAll(spec, pageable,
                new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));
        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

    @Override
    @FetchMethod
    public List<SnUnqualifiedItem> find(Specification<SnUnqualifiedItem> spec) {
        return snUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<SnUnqualifiedItem> findAll(Pageable pageable) {
        Page<SnUnqualifiedItem> page = snUnqualifiedItemRepository.findAll(pageable,
                new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));

        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

}
