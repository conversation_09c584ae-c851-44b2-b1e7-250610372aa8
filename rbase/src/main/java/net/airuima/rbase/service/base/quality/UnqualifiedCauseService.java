package net.airuima.rbase.service.base.quality;

import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedCause;
import net.airuima.rbase.repository.base.quality.UnqualifiedCauseRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良原因Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedCauseService extends CommonJpaService<UnqualifiedCause> {

    private final UnqualifiedCauseRepository unqualifiedCauseRepository;

    public UnqualifiedCauseService(UnqualifiedCauseRepository unqualifiedCauseRepository) {
        this.unqualifiedCauseRepository = unqualifiedCauseRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedCause> find(Specification<UnqualifiedCause> spec, Pageable pageable) {
        return unqualifiedCauseRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedCause> find(Specification<UnqualifiedCause> spec) {
        return unqualifiedCauseRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedCause> findAll(Pageable pageable) {
        return unqualifiedCauseRepository.findAll(pageable);
    }

    /**
     * 根据不良原因编码或者名称获取列表
     *
     * @param text     编码或者名称
     * @param isEnable 是否启用
     * @param size     数据条数
     * @return
     */
    public List<UnqualifiedCause> findByCodeOrName(String text, Boolean isEnable, Integer size) {
        Page<UnqualifiedCause> page = unqualifiedCauseRepository.findByNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

}
