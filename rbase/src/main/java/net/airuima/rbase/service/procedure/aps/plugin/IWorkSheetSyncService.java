package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetDTO;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetSyncDTO;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工单同步接口api
 * <AUTHOR>
 * @date 2023/6/14
 */
@FuncDefault
public interface IWorkSheetSyncService {

    /**
     * 工单同步 保存工单相关操作
     * @param workSheetSyncDto 更新同步参数
     * <AUTHOR>
     * @date  2023/3/31
     */
    default void saveSyncWorkSheet(WorkSheetSyncDTO workSheetSyncDto){

    }

    /**
     * 工单同步 更新工单相关
     * @param workSheetSyncDto 更新同步参数
     * <AUTHOR>
     * @date  2023/3/31
     */
    default void updateSyncWorkSheet(WorkSheetSyncDTO workSheetSyncDto){
    }

    /**
     * 生产工单同步
     * <AUTHOR>
     * @param syncWorkSheetDtoList    上传的需要同步的工单列表
     * @return java.util.List<net.airuima.rbase.dto.sync.SyncResultDTO> 同步结果信息列表
     * @date 2021-06-03
     **/
    default List<SyncResultDTO> syncWorkSheet(List<SyncWorkSheetDTO> syncWorkSheetDtoList) {
        return Collections.emptyList();
    }

    /**
     * 同步工单保存
     * @param workSheetSyncDto 工单同步参数
     * <AUTHOR>
     * @date  2023/3/31
     */
    default void  saveWorkSheet(WorkSheetSyncDTO workSheetSyncDto) {

    }

}
