package net.airuima.rbase.service.procedure.reinspect;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;

import java.util.*;

import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspectResult;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectResultRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.common.StatisticsDataCommonService;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.reinspect.dto.StepReinspectProcessDTO;
import net.airuima.security.SecurityUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.hibernate.jdbc.Work;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检历史Service
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class StepReinspectService extends CommonJpaService<StepReinspect> implements IStepReinspectService {

    private final String STEP_REINSPECT_HISTORY_GRAPH = "stepReinspectEntityGraph";

    private final StepReinspectRepository stepReinspectRepository;

    @Autowired
    private StepReinspectResultRepository stepReinspectResultRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private StatisticsDataCommonService statisticsDataCommonService;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private CheckHistoryService checkHistoryService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;


    public StepReinspectService(StepReinspectRepository stepReinspectRepository) {
        this.stepReinspectRepository = stepReinspectRepository;
    }

    @Override
    @FetchMethod
    public Page<StepReinspect> find(Specification<StepReinspect> spec, Pageable pageable) {
        return stepReinspectRepository.findAll(spec, pageable, new NamedEntityGraph(STEP_REINSPECT_HISTORY_GRAPH));
    }

    @Override
    @FetchMethod
    public List<StepReinspect> find(Specification<StepReinspect> spec) {
        return stepReinspectRepository.findAll(spec, new NamedEntityGraph(STEP_REINSPECT_HISTORY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<StepReinspect> findAll(Pageable pageable) {
        return stepReinspectRepository.findAll(pageable, new NamedEntityGraph(STEP_REINSPECT_HISTORY_GRAPH));
    }

    /**
     * 模糊查询符合处理方式的不良项目列表
     * @param id 不良复检记录ID
     * @param result 复检结果
     * @param text 不良模糊查询名称或编码
     * @param size 返回条数
     * @return List<UnqualifiedItem>
     */
    public List<UnqualifiedItem> likeUnqualifiedItem(Long id,Integer result,String text, Integer size){
        StepReinspect stepReinspect = stepReinspectRepository.getReferenceById(id);
        if(result == StepReinspectResultEnum.RELEASE.getStatus()){
            return new ArrayList<>();
        }
        int dealWay = result == StepReinspectResultEnum.REWORK.getStatus() ? Constants.INT_ZERO:Constants.INT_TWO;
        Page<UnqualifiedItem> page = unqualifiedItemRepository.findByNameOrCodeAndDealWay(text,dealWay,Boolean.TRUE, PageRequest.of(Constants.INT_ZERO, size));
        List<UnqualifiedItem> unqualifiedItemList =  Optional.ofNullable(page).map(Slice::getContent).map(ArrayList::new).orElseGet(ArrayList::new);
        if(stepReinspect.getOriginUnqualifiedItem().getDealWay() == dealWay && (CollectionUtils.isEmpty(unqualifiedItemList)
                ||!unqualifiedItemList.stream().map(UnqualifiedItem::getId).toList().contains(stepReinspect.getOriginUnqualifiedItem().getId()))){
            unqualifiedItemList.add(stepReinspect.getOriginUnqualifiedItem());
        }
        return unqualifiedItemList;
    }


    /**
     * 通过复检记录ID获取处理结果明细数据
     * @param id 复检记录ID
     * @return List<StepReinspectResult>
     */
    public List<StepReinspectResult> findResultById(Long id) {
        return stepReinspectResultRepository.findByStepReinspectIdAndDeleted(id,Constants.LONG_ZERO);
    }

    /**
     * 保存不良复检结果以及更新生产数据和统计数据
     *
     * @param stepReinspectProcessDTOList 不良复检结果列表
     */
    public void process(List<StepReinspectProcessDTO> stepReinspectProcessDTOList) {
        List<StepReinspect> stepReinspectList = stepReinspectRepository.findByIdInAndDeleted(stepReinspectProcessDTOList.stream().map(StepReinspectProcessDTO::getId).collect(Collectors.toSet()), Constants.LONG_ZERO);
        Map<Long, List<StepReinspect>> stepReinspectGroup = stepReinspectList.stream().collect(Collectors.groupingBy(StepReinspect::getId));
        Set<StepReinspect> stepReinspectSet = new HashSet<>(stepReinspectList);
        stepReinspectProcessDTOList.forEach(stepReinspectProcessDTO -> {
            StepReinspect stepReinspect = stepReinspectGroup.get(stepReinspectProcessDTO.getId()).get(Constants.INT_ZERO);
            //sn生产状态不为空则按照SN模式进行更新生产相关数据
            if (Objects.nonNull(stepReinspect.getSnWorkStatus())) {
                this.processWhenSingleSn(stepReinspectProcessDTO, stepReinspect);
            } else if (Objects.nonNull(stepReinspect.getContainerDetail())) {
                //容器详情不为空则按照容器模式进行更新生产相关数据
                ContainerDetail containerDetail = stepReinspect.getContainerDetail();
                SubWorkSheet subWorkSheet = Objects.nonNull(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ? containerDetail.getBatchWorkDetail().getSubWorkSheet() : null;
                WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
                List<StaffPerform> staffPerformList = staffPerformRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
                StaffPerform staffPerform = !CollectionUtils.isEmpty(staffPerformList) ? staffPerformList.get(Constants.INT_ZERO) : null;
                containerDetail = this.processWhenContainer(stepReinspectProcessDTO, stepReinspect, containerDetail, staffPerform);
                List<WsStep> wsStepList = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(),Constants.LONG_ZERO):null;
                if(CollectionUtils.isEmpty(wsStepList)){
                    wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(),Constants.LONG_ZERO);
                }
                nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId():workSheet.getId(),workSheet,subWorkSheet,wsStepList,containerDetail,Boolean.FALSE);
            } else {
                //上面都不满足则按批量模式进行更新生产相关数据
                SubWorkSheet subWorkSheet = stepReinspect.getSubWorkSheet();
                WorkSheet workSheet = stepReinspect.getWorkSheet();
                BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet)
                        ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), stepReinspect.getStep().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchStepWorkDetailNotExist", "工单【" + subWorkSheet.getSerialNumber() + "】工序【" + stepReinspect.getStep().getName() + "】生产详情不存在"))
                        : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), stepReinspect.getStep().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchStepWorkDetailNotExist", "工单【" + workSheet.getSerialNumber() + "】工序【" + stepReinspect.getStep().getName() + "】生产详情不存在"));
                //更新员工产量及不良数据
                List<StaffPerform> staffPerformList = staffPerformRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
                StaffPerform staffPerform = !CollectionUtils.isEmpty(staffPerformList) ? staffPerformList.get(Constants.INT_ZERO) : null;
                batchWorkDetail = this.processWhenBatch(stepReinspectProcessDTO, stepReinspect, batchWorkDetail, staffPerform);
                //处理完成后更新可能的后置待做工序信息
                List<WsStep> wsStepList = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(),Constants.LONG_ZERO):null;
                if(CollectionUtils.isEmpty(wsStepList)){
                    wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(),Constants.LONG_ZERO);
                }
                nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId():workSheet.getId(),workSheet,subWorkSheet,wsStepList,batchWorkDetail,Boolean.FALSE);
            }
            StepReinspectResult stepReinspectResult = new StepReinspectResult();
            stepReinspectResult.setStepReinspect(stepReinspect)
                    .setResult(stepReinspectProcessDTO.getResult())
                    .setNumber(stepReinspectProcessDTO.getNumber()).
                    setTargetUnqualifiedItem(Objects.nonNull(stepReinspectProcessDTO.getTargetUnqualifiedItemId()) ? new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()):null)
                    .setDeleted(Constants.LONG_ZERO);
            stepReinspectResultRepository.save(stepReinspectResult);
        });
        //更新单据状态
        LocalDateTime processDate = LocalDateTime.now();
        Long processorId;
        if (SecurityUtils.getCurrentUserLogin().isPresent()) {
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(SecurityUtils.getCurrentUserLogin().get());
            if (Objects.nonNull(userDTO) && Objects.nonNull(userDTO.getStaffDTO()) && Objects.nonNull(userDTO.getStaffDTO().getId())) {
                processorId = userDTO.getStaffDTO().getId();
            } else {
                processorId = null;
            }
        } else {
            processorId = null;
        }
        stepReinspectList.forEach(stepReinspect -> stepReinspect.setStatus(Boolean.TRUE).setProcessorId(processorId).setProcessDate(processDate));
        stepReinspectRepository.saveAll(stepReinspectList);
        //若是最后一个工序可能需要更新工单状态
        stepReinspectSet.forEach(stepReinspect -> {
            if(stepReinspect.getLastStep()){
                SubWorkSheet subWorkSheet = stepReinspect.getSubWorkSheet();
                WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet():stepReinspect.getWorkSheet();
                checkHistoryService.updateWorkSheetStatus(Objects.nonNull(subWorkSheet),subWorkSheet,workSheet);
            }
        });
    }

    /**
     * 单支模式时：更新单支详情生产数据及统计数据
     *
     * @param stepReinspectProcessDTO 单个工序不良处理结果
     * @param stepReinspect           原始复检单
     */
    private void processWhenSingleSn(StepReinspectProcessDTO stepReinspectProcessDTO, StepReinspect stepReinspect) {
        SnWorkStatus snWorkStatus = stepReinspect.getSnWorkStatus();
        SubWorkSheet subWorkSheet = stepReinspect.getSubWorkSheet();
        WorkSheet workSheet = stepReinspect.getWorkSheet();
        //是否为单支返工
        SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
        boolean singleSnOnlineRepair = Boolean.FALSE;
        if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.RELEASE.getStatus()) {
            snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() - Constants.INT_ONE);
        }
        if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(latestSnWorkDetail.getSubWorkSheet().getId())) {
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }
        if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(latestSnWorkDetail.getWorkSheet().getId())) {
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }
        //复检结果：放行
        if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.RELEASE.getStatus()) {
            SnWorkDetail latestUnqualifiedSnWorkDetail = snWorkDetailRepository.findTop1BySnAndResultAndIdLessThanAndDeletedOrderByIdDesc(snWorkStatus.getSn(), Constants.INT_ZERO, latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
            snWorkStatus.setLatestReworkSnWorkDetail(latestUnqualifiedSnWorkDetail)
                    .setLatestUnqualifiedItem(null != latestUnqualifiedSnWorkDetail ? latestUnqualifiedSnWorkDetail.getUnqualifiedItem() : null);
            if (singleSnOnlineRepair || stepReinspect.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory()) {
                snWorkStatus.setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
            } else if (snWorkStatus.getReworkTime() == Constants.INT_ZERO) {
                snWorkStatus.setStatus(SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus());
            }
            //如果是单支在线返工的最后一个工序则需要更新工单的合格数
            if (stepReinspect.getLastStep() && singleSnOnlineRepair) {
                snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus()).setEndDate(LocalDateTime.now());
                statisticsDataCommonService.updateWorkSheetCompleteInfo(null, workSheet, Constants.INT_ONE, -Constants.INT_ONE, Constants.INT_ONE, Boolean.TRUE);
                statisticsDataCommonService.updateStepGroupProductionPlanInfo(subWorkSheet, workSheet, stepReinspect.getStep(), Constants.INT_ONE, OperationEnum.ADD);
                statisticsDataCommonService.updateProductionPlanStatistics(subWorkSheet, workSheet, Constants.INT_ONE, OperationEnum.ADD);
            }
            snWorkStatusRepository.save(snWorkStatus);
            //更新SN详情
            latestSnWorkDetail.setUnqualifiedItem(null).setResult(Constants.INT_ONE);
            latestSnWorkDetail = snWorkDetailRepository.save(latestSnWorkDetail);
            //删除SN不良
            SnUnqualifiedItem snUnqualifiedItem = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(snUnqualifiedItem)) {
                snUnqualifiedItem.setDeleted(snUnqualifiedItem.getId());
                snUnqualifiedItemRepository.save(snUnqualifiedItem);
            }
        } else if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.SCRAP.getStatus()) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
        } else if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.REWORK.getStatus()) {
            UnqualifiedItem targetUnqualifiedItem = unqualifiedItemRepository.getReferenceById(stepReinspectProcessDTO.getTargetUnqualifiedItemId());
            //若不良项目没有不良组别则sn生产状态改为报废
            if (null == targetUnqualifiedItem.getUnqualifiedGroup()) {
                snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
            } else {
                //若没有配置单支不良在线返工规则则也认为报废
                WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(workSheet.getPedigree(), targetUnqualifiedItem.getUnqualifiedGroup().getId(), workSheet.getClientId());
                if (null == reWorkFlow) {
                    snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
                } else {
                    snWorkStatus.setWorkFlow(reWorkFlow).setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus()).setReworkStartDate(null == snWorkStatus.getReworkStartDate() ? LocalDateTime.now() : snWorkStatus.getReworkStartDate());
                }
            }
        }
        if (Objects.nonNull(stepReinspectProcessDTO.getTargetUnqualifiedItemId()) && !stepReinspectProcessDTO.getTargetUnqualifiedItemId().equals(stepReinspect.getOriginUnqualifiedItem().getId())) {
            latestSnWorkDetail.setUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()));
            snWorkStatus.setLatestUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()));
            SnUnqualifiedItem snUnqualifiedItem = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(snUnqualifiedItem)) {
                snUnqualifiedItem.setUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()));
                snUnqualifiedItemRepository.save(snUnqualifiedItem);
            }
        }
        snWorkStatusRepository.save(snWorkStatus);
        StaffPerform staffPerform = staffPerformRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
        //非纯单支返工时需要更新容器及批量详情相关数据
        if (!singleSnOnlineRepair) {
            if (Objects.nonNull(latestSnWorkDetail.getContainerDetail())) {
                this.processWhenContainer(stepReinspectProcessDTO, stepReinspect, latestSnWorkDetail.getContainerDetail(), staffPerform);
            } else {
                BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet)
                        ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), stepReinspect.getStep().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchStepWorkDetailNotExist", "工单【" + subWorkSheet.getSerialNumber() + "】工序【" + stepReinspect.getStep().getName() + "】生产详情不存在"))
                        : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), stepReinspect.getStep().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchStepWorkDetailNotExist", "工单【" + workSheet.getSerialNumber() + "】工序【" + stepReinspect.getStep().getName() + "】生产详情不存在"));
                this.processWhenBatch(stepReinspectProcessDTO, stepReinspect, batchWorkDetail, staffPerform);
            }
        }else if(Objects.nonNull(staffPerform)){
            staffPerform.setQualifiedNumber(staffPerform.getQualifiedNumber() + stepReinspectProcessDTO.getNumber()).setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() - stepReinspectProcessDTO.getNumber());
            staffPerformRepository.save(staffPerform);
            StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(staffPerformUnqualifiedItem)) {
                staffPerformUnqualifiedItem.setNumber(staffPerformUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber()).setDeleted(staffPerformUnqualifiedItem.getNumber() == Constants.INT_ZERO ? staffPerformUnqualifiedItem.getId() : Constants.LONG_ZERO);
                staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
            }
        }
        List<WsStep> wsStepList = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(),Constants.LONG_ZERO):null;
        if(CollectionUtils.isEmpty(wsStepList)){
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(),Constants.LONG_ZERO);
        }
        //更新工作台下个可能的待做工序信息
        nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId():workSheet.getId(),
                workSheet,subWorkSheet,wsStepList,latestSnWorkDetail,latestSnWorkDetail.getReworkTime(),singleSnOnlineRepair,Boolean.FALSE);
    }


    /**
     * 容器模式时：更新容器详情生产数据及统计数据
     *
     * @param stepReinspectProcessDTO 单个工序不良处理结果
     * @param stepReinspect           原始复检单
     */
    private ContainerDetail processWhenContainer(StepReinspectProcessDTO stepReinspectProcessDTO, StepReinspect
            stepReinspect, ContainerDetail containerDetail, StaffPerform staffPerform) {
        //复检结果：放行
        if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.RELEASE.getStatus()) {
            //更新容器详情工序生产数据
            containerDetail.setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() - stepReinspectProcessDTO.getNumber())
                    .setQualifiedNumber(containerDetail.getQualifiedNumber() + stepReinspectProcessDTO.getNumber())
                    .setTransferNumber(containerDetail.getTransferNumber() + stepReinspectProcessDTO.getNumber());
            containerDetail =  containerDetailRepository.save(containerDetail);
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(containerDetailUnqualifiedItem)) {
                containerDetailUnqualifiedItem.setNumber(containerDetailUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber());
                containerDetailUnqualifiedItem.setDeleted(containerDetailUnqualifiedItem.getNumber() == Constants.LONG_ZERO ? containerDetailUnqualifiedItem.getId() : Constants.LONG_ZERO);
                containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
            }
        } else if (!stepReinspectProcessDTO.getTargetUnqualifiedItemId().equals(stepReinspect.getOriginUnqualifiedItem().getId())) {
            ContainerDetailUnqualifiedItem originContainerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(originContainerDetailUnqualifiedItem)) {
                originContainerDetailUnqualifiedItem.setNumber(originContainerDetailUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber());
                originContainerDetailUnqualifiedItem.setDeleted(originContainerDetailUnqualifiedItem.getNumber() == Constants.LONG_ZERO ? originContainerDetailUnqualifiedItem.getId() : Constants.LONG_ZERO);
                containerDetailUnqualifiedItemRepository.save(originContainerDetailUnqualifiedItem);
            }
            ContainerDetailUnqualifiedItem targetContainerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), stepReinspectProcessDTO.getTargetUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            targetContainerDetailUnqualifiedItem.setContainerDetail(containerDetail).setUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId())).setNumber(stepReinspectProcessDTO.getNumber()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(targetContainerDetailUnqualifiedItem);
        }
        //更新批量详情工序生产数据及相关统计数据
        this.processWhenBatch(stepReinspectProcessDTO, stepReinspect, containerDetail.getBatchWorkDetail(), staffPerform);
        return containerDetail;
    }

    /**
     * 批量模式时：更新批量详情生产数据及统计数据
     *
     * @param stepReinspectProcessDTO 单个工序不良处理结果
     * @param stepReinspect           原始复检单
     * @param batchWorkDetail         批量详情
     * @param staffPerform            员工产量
     */
    private BatchWorkDetail processWhenBatch(StepReinspectProcessDTO stepReinspectProcessDTO, StepReinspect
            stepReinspect, BatchWorkDetail batchWorkDetail, StaffPerform staffPerform) {
        SubWorkSheet subWorkSheet = stepReinspect.getSubWorkSheet();
        WorkSheet workSheet = stepReinspect.getWorkSheet();
        //复检结果：放行
        if (stepReinspectProcessDTO.getResult() == StepReinspectResultEnum.RELEASE.getStatus()) {
            //更新批量详情工序生产数据
            batchWorkDetail.setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() - stepReinspectProcessDTO.getNumber())
                    .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + stepReinspectProcessDTO.getNumber())
                    .setTransferNumber(batchWorkDetail.getTransferNumber() + stepReinspectProcessDTO.getNumber());
            batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
            //更新批量生产详情不良数据
            WsStepUnqualifiedItem wsStepUnqualifiedItem = Objects.nonNull(subWorkSheet) ? wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), stepReinspect.getStep().getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null)
                    : wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), stepReinspect.getStep().getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(wsStepUnqualifiedItem)) {
                wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber());
                wsStepUnqualifiedItem.setDeleted(wsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO ? wsStepUnqualifiedItem.getId() : Constants.LONG_ZERO);
                wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
            }
            //更新员工产量数据及不良数据
            if (Objects.nonNull(staffPerform)) {
                staffPerform.setQualifiedNumber(staffPerform.getQualifiedNumber() + stepReinspectProcessDTO.getNumber()).setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() - stepReinspectProcessDTO.getNumber());
                staffPerformRepository.save(staffPerform);
                StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
                if (Objects.nonNull(staffPerformUnqualifiedItem)) {
                    staffPerformUnqualifiedItem.setNumber(staffPerformUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber()).setDeleted(staffPerformUnqualifiedItem.getNumber() == Constants.INT_ZERO ? staffPerformUnqualifiedItem.getId() : Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                }
            }
            //最后一个工序则需要更新(子)工单相关合格数信息
            if (stepReinspect.getLastStep()) {
                statisticsDataCommonService.updateWorkSheetCompleteInfo(subWorkSheet, workSheet, stepReinspectProcessDTO.getNumber(), -stepReinspectProcessDTO.getNumber(), stepReinspectProcessDTO.getNumber(), Boolean.FALSE);
            }
            //更新在制看板数据
            if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()) {
                statisticsDataCommonService.updateWorkSheetStepStatisticsInfo(subWorkSheet, workSheet, stepReinspect.getStep(), Constants.INT_ZERO, stepReinspectProcessDTO.getNumber(), -stepReinspectProcessDTO.getNumber(), stepReinspectProcessDTO.getNumber());
            }
            //更新工序组生产计划数据
            statisticsDataCommonService.updateStepGroupProductionPlanInfo(subWorkSheet, workSheet, stepReinspect.getStep(), stepReinspectProcessDTO.getNumber(), OperationEnum.ADD);
            //更新生产计划统计数据
            statisticsDataCommonService.updateProductionPlanStatistics(subWorkSheet, workSheet, stepReinspectProcessDTO.getNumber(), OperationEnum.ADD);
            return batchWorkDetail;
        }
        //复检结果：返工或报废，只有复检的不良项目与原始不良项目不一致时才会更新
        if (Objects.isNull(stepReinspectProcessDTO.getTargetUnqualifiedItemId()) || !stepReinspectProcessDTO.getTargetUnqualifiedItemId().equals(stepReinspect.getOriginUnqualifiedItem().getId())) {
            //更新批量生产详情不良数据
            WsStepUnqualifiedItem originWsStepUnqualifiedItem = Objects.nonNull(subWorkSheet) ? wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), stepReinspect.getStep().getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null)
                    : wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), stepReinspect.getStep().getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(originWsStepUnqualifiedItem)) {
                originWsStepUnqualifiedItem.setNumber(originWsStepUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber()).setDeleted(originWsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO ? originWsStepUnqualifiedItem.getId() : Constants.LONG_ZERO);
                wsStepUnqualifiedItemRepository.save(originWsStepUnqualifiedItem);
            }
            WsStepUnqualifiedItem targetWsStepUnqualifiedItem = Objects.nonNull(subWorkSheet) ? wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), stepReinspect.getStep().getId(), stepReinspectProcessDTO.getTargetUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem())
                    : wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), stepReinspect.getStep().getId(), stepReinspectProcessDTO.getTargetUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
            targetWsStepUnqualifiedItem.setStep(stepReinspect.getStep()).setNumber(targetWsStepUnqualifiedItem.getNumber() + stepReinspectProcessDTO.getNumber())
                    .setUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()))
                    .setWorkSheet(Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : null).setSubWorkSheet(subWorkSheet)
                    .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
            if(Objects.isNull(targetWsStepUnqualifiedItem.getOperatorId())){
                targetWsStepUnqualifiedItem.setOperatorId(batchWorkDetail.getOperatorId());
            }
            wsStepUnqualifiedItemRepository.save(targetWsStepUnqualifiedItem);
            //更新员工产量数据及不良数据
            if (Objects.nonNull(staffPerform)) {
                StaffPerformUnqualifiedItem originStaffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), stepReinspect.getOriginUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
                if (Objects.nonNull(originStaffPerformUnqualifiedItem)) {
                    originStaffPerformUnqualifiedItem.setNumber(originStaffPerformUnqualifiedItem.getNumber() - stepReinspectProcessDTO.getNumber()).setDeleted(originStaffPerformUnqualifiedItem.getNumber() == Constants.INT_ZERO ? originStaffPerformUnqualifiedItem.getId() : Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(originStaffPerformUnqualifiedItem);
                }
                StaffPerformUnqualifiedItem targetStaffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), stepReinspectProcessDTO.getTargetUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                targetStaffPerformUnqualifiedItem.setStaffPerform(staffPerform).setUnqualifiedItem(new UnqualifiedItem(stepReinspectProcessDTO.getTargetUnqualifiedItemId()))
                        .setNumber(targetStaffPerformUnqualifiedItem.getNumber() + stepReinspectProcessDTO.getNumber())
                        .setRecordDate(Objects.nonNull(targetStaffPerformUnqualifiedItem.getId()) ? targetStaffPerformUnqualifiedItem.getRecordDate() : LocalDate.now())
                        .setRecordTime(Objects.nonNull(targetStaffPerformUnqualifiedItem.getId()) ? targetStaffPerformUnqualifiedItem.getRecordTime() : LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                staffPerformUnqualifiedItemRepository.save(targetStaffPerformUnqualifiedItem);
            }
        }
        return batchWorkDetail;
    }


    /**
     * 开返工单验证是否存复检，存在则提示先进行复检
     *
     * @param subWorkSheetId 子工单主键ID
     * @param workSheetId 工单主键ID
     * @param stepIds        工序主键ID列表
     */
    @Override
    public void validReWorkStepReinspect(Long subWorkSheetId, Long workSheetId,List<Long> stepIds) {
        List<StepReinspect> stepReinspectList = Objects.nonNull(subWorkSheetId)
                ?stepReinspectRepository.findBySubWorkSheetIdAndStepIdInAndStatusAndDeleted(subWorkSheetId,stepIds,Boolean.FALSE,Constants.LONG_ZERO)
                :stepReinspectRepository.findByWorkSheetIdAndStepIdInAndStatusAndDeleted(workSheetId,stepIds,Boolean.FALSE,Constants.LONG_ZERO);
        if (ValidateUtils.isValid(stepReinspectList) && Objects.nonNull(subWorkSheetId)) {
            throw new ResponseException("error.stepReinspectNotFinished", "子工单("+stepReinspectList.get(Constants.INT_ZERO).getSubWorkSheet().getSerialNumber()+")存在尚未完成的工序复检");
        }else if(ValidateUtils.isValid(stepReinspectList) && Objects.nonNull(workSheetId)){
            throw new ResponseException("error.stepReinspectNotFinished", "工单("+stepReinspectList.get(Constants.INT_ZERO).getWorkSheet().getSerialNumber()+")存在尚未完成的工序复检");
        }
    }

    /**
     * 工单批次回退删除工序复检记录
     *
     * @param batchWorkDetail 批量工序生产详情
     */
    @Override
    public void rollBackBatch(BatchWorkDetail batchWorkDetail) {
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = subWorkSheet != null ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        List<StepReinspect> stepReinspectList = Objects.nonNull(subWorkSheet)
                ?stepReinspectRepository.findBySubWorkSheetIdAndStepIdInAndStatusAndDeleted(subWorkSheet.getId(), Collections.singletonList(batchWorkDetail.getStep().getId()),Boolean.FALSE,Constants.LONG_ZERO)
                :stepReinspectRepository.findByWorkSheetIdAndStepIdInAndStatusAndDeleted(workSheet.getId(),Collections.singletonList(batchWorkDetail.getStep().getId()),Boolean.FALSE,Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(stepReinspectList)){
            stepReinspectRepository.logicDelete(stepReinspectList);
        }
    }

    /**
     * 容器回退删除工序复检记录
     *
     * @param containerDetail 容器生产详情
     */
    @Override
    public void rollBackContainer(ContainerDetail containerDetail) {
        List<StepReinspect> stepReinspectList = stepReinspectRepository.findByContainerDetailIdAndStatusAndDeleted(containerDetail.getId(),Boolean.FALSE,Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(stepReinspectList)){
            stepReinspectRepository.logicDelete(stepReinspectList);
        }
    }

    /**
     * 单支回退删除工序复检记录
     *
     * @param snWorkDetail sn生产详情
     */
    @Override
    public void rollBackSn(SnWorkDetail snWorkDetail) {
        StepReinspect stepReinspect = stepReinspectRepository.findTop1BySnWorkStatusSnAndStatusAndDeleted(snWorkDetail.getSn(),Boolean.FALSE,Constants.LONG_ZERO);
        if(Objects.nonNull(stepReinspect)){
            stepReinspectRepository.logicDelete(stepReinspect);
        }
    }
}
