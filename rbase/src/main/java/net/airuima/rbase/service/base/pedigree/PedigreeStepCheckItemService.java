package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 产品谱系检测项目Service
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepCheckItemService extends CommonJpaService<PedigreeStepCheckItem> {
    private final String qualifiedRangeRegex = "^(\\(|\\[)(-?\\d*\\.?\\d*?),\\s*(-?\\d*\\.?\\d*?)(\\)|\\])$|^(\\(|\\[)\\s*\\d*\\.?\\d*?\\s*,\\s*-?\\d*\\.?\\d*?\\s*(\\)|\\])$|^(\\(|\\[)(-?\\d*\\.?\\d*?\\s*,\\s*-?\\d*\\.?\\d*?|\\s*-?\\d*\\.?\\d*?,\\s*-?\\d*\\.?\\d*?)\\s*(\\)|\\])$|^OK$";
    private final String PEDIGREE_STEP_CHECK_ITEM_ENTITY_GRAPH = "pedigreeStepCheckItemEntityGraph";
    private final PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;

    public PedigreeStepCheckItemService(PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository) {
        this.pedigreeStepCheckItemRepository = pedigreeStepCheckItemRepository;
    }

    @Override
    public Page<PedigreeStepCheckItem> find(Specification<PedigreeStepCheckItem> spec, Pageable pageable) {
        return pedigreeStepCheckItemRepository.findAll(spec,pageable,new NamedEntityGraph(PEDIGREE_STEP_CHECK_ITEM_ENTITY_GRAPH));
    }

    @Override
    public List<PedigreeStepCheckItem> find(Specification<PedigreeStepCheckItem> spec) {
        return pedigreeStepCheckItemRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_CHECK_ITEM_ENTITY_GRAPH));
    }

    @Override
    public Page<PedigreeStepCheckItem> findAll(Pageable pageable) {
        return pedigreeStepCheckItemRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_CHECK_ITEM_ENTITY_GRAPH));
    }

    @Override
    public String validateBeforeSave(PedigreeStepCheckItem entity) {
        if( StringUtils.isBlank(entity.getQualifiedRange())){
            return  "合格范围为空";
        }
        if(!entity.getQualifiedRange().matches(qualifiedRangeRegex)){
            return "合格范围不合法";
        }
        if(!entity.getQualifiedRange().equals(net.airuima.constant.Constants.OK) && (entity.getQualifiedRange().contains("(")||entity.getQualifiedRange().contains("[")) && !ToolUtils.validateQualifiedRange(entity.getQualifiedRange())){
            return "合格范围不合法";
        }
        return super.validateBeforeSave(entity);
    }

    /**
     * 根据检测规则id查询检测项目
     * <AUTHOR>
     * @date 2022/11/3 15:20
     * @param pedigreeStepCheckRuleId 检测规则id
     * @return List<PedigreeStepCheckItem>
     */
    @Transactional(readOnly = true)
    public List<PedigreeStepCheckItem> findByPedigreeStepCheckRuleId(Long pedigreeStepCheckRuleId) {
        return pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRuleId, Constants.LONG_ZERO);
    }
}
