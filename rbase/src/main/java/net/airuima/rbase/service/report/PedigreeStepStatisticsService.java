package net.airuima.rbase.service.report;

import net.airuima.rbase.domain.procedure.report.PedigreeStepStatistics;
import net.airuima.rbase.repository.procedure.report.PedigreeStepStatisticsRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产品谱系工序生产在制统计Service
 * <AUTHOR>
 * @date 2023/12/19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepStatisticsService extends CommonJpaService<PedigreeStepStatistics> {

    private final PedigreeStepStatisticsRepository pedigreeStepStatisticsRepository;

    public PedigreeStepStatisticsService(PedigreeStepStatisticsRepository pedigreeStepStatisticsRepository) {
        this.pedigreeStepStatisticsRepository = pedigreeStepStatisticsRepository;
    }

    @Override
    public Page<PedigreeStepStatistics> find(Specification<PedigreeStepStatistics> spec, Pageable pageable) {
        return pedigreeStepStatisticsRepository.findAll(spec, pageable);
    }

    @Override
    public List<PedigreeStepStatistics> find(Specification<PedigreeStepStatistics> spec) {
        return pedigreeStepStatisticsRepository.findAll(spec);
    }

    @Override
    public Page<PedigreeStepStatistics> findAll(Pageable pageable) {
        return pedigreeStepStatisticsRepository.findAll(pageable);
    }
}
