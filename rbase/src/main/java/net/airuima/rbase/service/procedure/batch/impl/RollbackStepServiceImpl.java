package net.airuima.rbase.service.procedure.batch.impl;

import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.quality.OnlineReworkRule;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import net.airuima.rbase.proxy.maintain.RbaseIMaintainServiceProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.quality.SnStepFppStatusRepository;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendStepDetailRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.service.procedure.batch.ContainerDetailService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.service.procedure.batch.dto.MaterialBatchDTO;
import net.airuima.rbase.service.procedure.reinspect.IStepReinspectService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.service.procedure.single.SnWorkDetailService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/13
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class RollbackStepServiceImpl implements IRollbackStepService {

    private static final String BAKE_CYCLE_BAKE_AGEING_MODEL = "BakeCycleBakeAgeingModel";
    private static final String OC_MES = "ocMes";

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private ContainerDetailService containerDetailService;
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private BatchWorkDetailService batchWorkDetailService;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailService snWorkDetailService;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private RbaseIMaintainServiceProxy rbaseIMaintainServiceProxy;
    @Autowired
    private IStepReinspectService[] stepReinspectServices;
    @Autowired
    private IProductionPlanService[] productionPlanServices;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private PIRworkerCacheService rworkerCacheServices;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private OnlineReworkRuleRepository onlineReworkRuleRepository;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private SnStepFppStatusRepository snStepFppStatusRepository;
    @Autowired
    private WorkCellExtendStepDetailRepository workCellExtendStepDetailRepository;

    /**
     * 通过 容器详情id 或者工序批量工作详情id 验证是否存在上一道容器被占用
     *
     * @param id 容器详情id 或者工序批量工作详情id
     * @return @return net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     */
    @Override
    public ContainerDetailReplaceDTO checkContainerOccupation(Long id) {
        return batchWorkDetailService.checkContainerOccupation(id);
    }

    /**
     * 通过批量工作详情ID删除记录
     *
     * @param rollBackDto 工序回退参数
     * @return net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @Override
    public ContainerDetailReplaceDTO deleteBatchWorkDetailById(RollBackDTO rollBackDto) {
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByIdAndDeleted(rollBackDto.getBatchWorkDetailId(),Constants.LONG_ZERO);
        if(null == batchWorkDetail || null == batchWorkDetail.getId()){
            throw new ResponseException("error.RollbackFail", "批次详情不存在");
        }
        //工序对应的sn
        List<SnWorkDetail> snWorkDetailList = (null!=batchWorkDetail.getSubWorkSheet()) ?
                snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO)
                :snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getWorkSheet().getId(),batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
        //获取当前工序对应的容器列表
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
        //替换容器
        if (ValidateUtils.isValid(rollBackDto.getContainerDetailInfoDtoList())){
            BaseDTO baseDto = containerDetailService.replaceContainer(rollBackDto.getContainerDetailInfoDtoList());
            if (Constants.KO.equals(baseDto.getStatus())){
                return new ContainerDetailReplaceDTO(baseDto.getStatus(),baseDto.getMessage());
            }
        }
        //验证回退合法性
        ContainerDetailReplaceDTO containerDetailReplaceDto = batchWorkDetailService.checkContainerOccupation(rollBackDto.getBatchWorkDetailId());
        if (Constants.KO.equals(containerDetailReplaceDto.getStatus()) || ValidateUtils.isValid(containerDetailReplaceDto.getContainerDetailList())){
            return containerDetailReplaceDto;
        }
        List<WsStep> wsStepList = null;
        if (null != batchWorkDetail.getSubWorkSheet()) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), Constants.LONG_ZERO);
        }
        WorkSheet workSheet =  Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                ? batchWorkDetail.getSubWorkSheet().getWorkSheet():batchWorkDetail.getWorkSheet();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(batchWorkDetail.getStep().getId())).findFirst().orElse(new WsStep().setStep(batchWorkDetail.getStep()));

        //删除Rworker缓存
        rworkerCacheServices.deleteCacheByBatchWorkDetail(batchWorkDetail,containerDetailList,snWorkDetailList);
        //回退未处理的维修分析
        rbaseIMaintainServiceProxy.rollBackBatch(batchWorkDetail);
        //回退未处理的工序复检
        stepReinspectServices[0].rollBackBatch(batchWorkDetail);
        //回退 在当前工序上所用物料用量
        BeanUtil.getHighestPrecedenceBean(IRollbackStepService.class).returnStepMaterial(batchWorkDetail);
        //回退SN生产状态及SN生产数据
        batchWorkDetailService.deleteSnWorkDetail(currWsStep,snWorkDetailList);
        if(null != batchWorkDetail.getSubWorkSheet()) {
            snWorkDetailRepository.batchDeleteBySubWorkSheetIdAndStepId(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId());
        }else {
            snWorkDetailRepository.batchDeleteByWorkSheetIdAndStepId(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId());
        }
        batchWorkDetailService.deleteContainerWorkDetail(currWsStep,containerDetailList);
        containerDetailRepository.batchDeleteByBatchWorkDetailId(batchWorkDetail.getId());
        batchWorkDetailService.deleteBatchWorkDetail(batchWorkDetail);
        //添加 工序回退历史记录
        addRollBackHistory(rollBackDto, batchWorkDetail, snWorkDetailList, containerDetailList,wsStepList);
        //当前回退的是最后一个工序时，更新工单和子工单合格数与不合格数
        if(CollectionUtils.isEmpty(snWorkDetailList) || !FuncKeyUtil.checkApi(FuncKeyConstants.RWORKER_WEB)) {
            batchWorkDetailService.updateWorkSheetAndSubWorkSheet(batchWorkDetail);
        }
        //更新子工单或者工单的工序完成个数
        if (null != batchWorkDetail.getSubWorkSheet()) {
            SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
            //更新子工单及工单状态
            if(subWorkSheet.getStepNumber() == net.airuima.constant.Constants.INT_ZERO){
                subWorkSheet.setActualStartDate(null).setStatus(ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) ;
            }
            if(batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(subWorkSheet.getWorkSheet(),subWorkSheet,wsStepList,currWsStep,Boolean.FALSE);
            }
            subWorkSheetRepository.save(subWorkSheet);
            List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO);
            BatchWorkDetail batchWorkDetailTemp = batchWorkDetailRepository.findTop1BySubWorkSheetIdInAndDeleted(subWorkSheetList.stream().map(SubWorkSheet::getId).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO);
            if(Objects.isNull(batchWorkDetailTemp)){
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()).setActualStartDate(null);
                workSheetRepository.save(workSheet);
            }
        } else if(null != batchWorkDetail.getWorkSheet()){
            if(batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(batchWorkDetail.getWorkSheet(),null,wsStepList,currWsStep,Boolean.FALSE);
            }
            if(workSheet.getStepNumber() == net.airuima.constant.Constants.INT_ZERO){
                workSheet.setActualStartDate(null).setStatus(ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) ;
            }
            workSheetRepository.save(workSheet);
        }
        //回退员工产能统计表数据
        List<StaffPerform> staffPerforms = staffPerformRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
        batchWorkDetailService.staffPerformDeleteByIds(staffPerforms);
        //回退工位宽放时间段内的下交数据
        BeanUtil.getHighestPrecedenceBean(RollbackStepServiceImpl.class).rollbackBatchWorkCellExtendDetail(batchWorkDetail.getId());
        //删除待检任务
        if(null != batchWorkDetail.getSubWorkSheet()){
            inspectTaskRepository.deleteBySubWorkSheetIdAndStepId(batchWorkDetail.getSubWorkSheet().getId(),batchWorkDetail.getStep().getId());
        }else {
            inspectTaskRepository.deleteByWorkSheetIdAndStepId(batchWorkDetail.getWorkSheet().getId(),batchWorkDetail.getStep().getId());
        }
        //删除烘烤温循老化记录
        bakeCycleBakeAgeingModelServices[0].logicDeletedBakeCycleBakeAgeingHistory(batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),currWsStep,null,null, Constants.INT_ZERO);
        //更新生产计划表 工序组 回退
        rollbackUpdateStepGroupProductionPlan(batchWorkDetail);
        //更新生产在制数据
        workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsInfo(Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                        ? batchWorkDetail.getSubWorkSheet().getWorkSheet():batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),batchWorkDetail.getStep(),wsStepList,
                batchWorkDetail.getInputNumber()*Constants.NEGATIVE_ONE,batchWorkDetail.getQualifiedNumber()*Constants.NEGATIVE_ONE,
                batchWorkDetail.getUnqualifiedNumber()*Constants.NEGATIVE_ONE,batchWorkDetail.getTransferNumber()*Constants.NEGATIVE_ONE);
        return new ContainerDetailReplaceDTO(Constants.OK,"StepRollbackSucceeded");
    }

    /**
     * 回退更新工序组生产计划
     * @param batchWorkDetail 批量工序生产详情
     */
    private void rollbackUpdateStepGroupProductionPlan(BatchWorkDetail batchWorkDetail) {
        // 产品谱系id
        Long pedigreeId = null;
        Long workLineId = null;
        if(Objects.nonNull(batchWorkDetail.getWorkSheet())){
            pedigreeId =  Optional.ofNullable(batchWorkDetail).map(s -> s.getWorkSheet()).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
            workLineId = Optional.ofNullable(batchWorkDetail).map(s -> s.getWorkSheet()).map(w -> w.getWorkLine()).map(p -> p.getId()).orElse(null);
        }
        if(Objects.nonNull(batchWorkDetail.getSubWorkSheet())){
            pedigreeId =  Optional.ofNullable(batchWorkDetail).map(o ->o.getSubWorkSheet()).map(s -> s.getWorkSheet()).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
            workLineId =  Optional.ofNullable(batchWorkDetail).map(o ->o.getSubWorkSheet()).map(s -> s.getWorkSheet()).map(w -> w.getWorkLine()).map(p -> p.getId()).orElse(null);
        }
        //工序组id
        Long stepGroupId = Optional.ofNullable(batchWorkDetail).map(s ->s.getStep()).map(o ->o.getStepGroup()).map(i ->i.getId()).orElse(null);
        // 更新生产计划 工序组计划粒度
        if(Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)){
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId,batchWorkDetail.getQualifiedNumber(), OperationEnum.MINUS);
        }
    }

    /**
     * 添加回退历史记录
     * @param rollBackDto           工序回退DTO
     * @param batchWorkDetail       批量工序生产详情
     * @param snWorkDetailList      单支工序生产详情列表
     * @param containerDetailList   容器详细列表
     */
    private void addRollBackHistory(RollBackDTO rollBackDto, BatchWorkDetail batchWorkDetail, List<SnWorkDetail> snWorkDetailList, List<ContainerDetail> containerDetailList,List<WsStep> wsStepList) {
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId();
        List<RollBackHistory> rollBackHistoryList = new ArrayList<>();
        // 根据sn维度记录回退历史
        if( ! CollectionUtils.isEmpty(snWorkDetailList)) {
            for (SnWorkDetail snWorkDetail : snWorkDetailList) {
                RollBackHistory rollBackHistory = new RollBackHistory();
                rollBackHistory.setWorkSheet(null != snWorkDetail.getSubWorkSheet()?snWorkDetail.getSubWorkSheet().getWorkSheet():snWorkDetail.getWorkSheet())
                        .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                        .setStaffId(rollBackDto.getStaffId()).setNote(rollBackDto.getNote()).setNumber(Constants.INT_ONE)
                        .setQualifiedNumber(snWorkDetail.getResult()==Constants.INT_ONE?Constants.INT_ONE:Constants.INT_ZERO).setStep(snWorkDetail.getStep()).setSn(snWorkDetail.getSn())
                        .setWorkCell(snWorkDetail.getWorkCell())
                        .setContainerCode(Objects.nonNull(snWorkDetail.getContainerDetail()) ? snWorkDetail.getContainerDetail().getContainerCode() : null);
                rollBackHistoryList.add(rollBackHistory);
                //回退rworker下个可能的待做工序列表信息
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, Constants.INT_ZERO, Boolean.FALSE,Boolean.TRUE);
            }
            rollBackHistoryRepository.saveAll(rollBackHistoryList);
            return;
        }
        //根据容器维度记录回退历史
        if(!CollectionUtils.isEmpty(containerDetailList)){
            for (ContainerDetail containerDetail : containerDetailList){
                BatchWorkDetail resultBatchWorkDetail = containerDetail.getBatchWorkDetail();
                RollBackHistory rollBackHistory = new RollBackHistory();
                rollBackHistory.setWorkSheet(null != resultBatchWorkDetail.getSubWorkSheet()?resultBatchWorkDetail.getSubWorkSheet().getWorkSheet():resultBatchWorkDetail.getWorkSheet())
                        .setSubWorkSheet(resultBatchWorkDetail.getSubWorkSheet())
                        .setStep(resultBatchWorkDetail.getStep()).setWorkCell(containerDetail.getWorkCell())
                        .setContainerCode(containerDetail.getContainerCode()).setNumber(containerDetail.getInputNumber()).setQualifiedNumber(containerDetail.getQualifiedNumber())
                        .setStaffId(rollBackDto.getStaffId()).setNote(rollBackDto.getNote());
                rollBackHistoryList.add(rollBackHistory);
                //回退rworker下个可能的待做工序列表信息
                nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail,Boolean.TRUE);
            }
            rollBackHistoryRepository.saveAll(rollBackHistoryList);
            return;
        }
        //工单批量
        RollBackHistory rollBackHistory = new RollBackHistory();
        rollBackHistory.setWorkSheet(null !=batchWorkDetail.getSubWorkSheet()?batchWorkDetail.getSubWorkSheet().getWorkSheet():batchWorkDetail.getWorkSheet())
                .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                .setStaffId(rollBackDto.getStaffId()).setNote(rollBackDto.getNote()).setNumber(batchWorkDetail.getInputNumber())
                .setQualifiedNumber(batchWorkDetail.getQualifiedNumber()).setStep(batchWorkDetail.getStep())
                .setWorkCell(batchWorkDetail.getWorkCell());
        rollBackHistoryRepository.save(rollBackHistory);
        //回退rworker下个可能的待做工序列表信息
        nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail,Boolean.TRUE);
    }

    /**
     * 通过批量详情进行物料库存回退
     * @param batchWorkDetail 批量工序生产详情
     */
    @Override
    public void returnStepMaterial(BatchWorkDetail batchWorkDetail){
        List<BatchWorkDetailMaterialBatch> workDetailMaterialBatches = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndTypeNotAndDeleted(batchWorkDetail.getId(), ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workDetailMaterialBatches)){
            //过滤掉不扣料批次类型，对扣料方式进行分组
            Map<Integer, List<BatchWorkDetailMaterialBatch>> batchWorkDetailMaterialBatchClassify = workDetailMaterialBatches.stream().collect(Collectors.groupingBy(BatchWorkDetailMaterialBatch::getType));
            batchWorkDetailMaterialBatchClassify.forEach((type,batchWorkDetailMaterialBatchs)->{
                //工单层级退料
                if (type == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(batchWorkDetailMaterialBatchs)){
                    //工单+物料+批次 进行分组
                    groupBatchWorkDetailMateriaBatch(batchWorkDetail, type, batchWorkDetailMaterialBatchs);
                }

                //工位层级退料
                if (type == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(batchWorkDetailMaterialBatchs)){
                    returnWorkCellStepMaterial(batchWorkDetail, type, batchWorkDetailMaterialBatchs);

                }
            });
        }
    }

    /**
     * 工单+物料+批次 进行分组处理
     * @param batchWorkDetail                   批次工作详情
     * @param type                              扣料方式
     * @param batchWorkDetailMaterialBatchs     批量生产详情物料批次列表
     */
    private void groupBatchWorkDetailMateriaBatch(BatchWorkDetail batchWorkDetail, Integer type, List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchs) {
        batchWorkDetailMaterialBatchs.stream().collect(Collectors.groupingBy(
                workDetailMaterialBatch ->( batchWorkDetail.getSubWorkSheet() != null?workDetailMaterialBatch.getBatchWorkDetail().getSubWorkSheet().getWorkSheet().getId():workDetailMaterialBatch.getBatchWorkDetail().getWorkSheet().getId())
                        +"-"+ workDetailMaterialBatch.getMaterialId()
                        +"-"+workDetailMaterialBatch.getMaterialBatch())).forEach((key,batchWorkDetailMaterialBatchList) ->{
            //退料数量
            double sum = batchWorkDetailMaterialBatchList.stream().mapToDouble(BatchWorkDetailMaterialBatch::getNumber).sum();
            BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = batchWorkDetailMaterialBatchList.get(Constants.INT_ZERO);
            batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null!=batchWorkDetail.getSubWorkSheet()?batchWorkDetail.getSubWorkSheet().getWorkSheet().getId():batchWorkDetail.getWorkSheet().getId(),
                    batchWorkDetail.getWorkCell().getId(),batchWorkDetailMaterialBatch.getMaterialId(),batchWorkDetailMaterialBatch.getMaterialBatch(),type,sum));
        });
    }

    /**
     * 回退 在当前工序上所用物料用量
     * @param batchWorkDetail 批次工作详情
     * @param type 扣料方式
     * @param batchWorkDetailMaterialBatchs 批量生产详情物料批次
     */
    private void returnWorkCellStepMaterial(BatchWorkDetail batchWorkDetail, Integer type, List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchs) {
        //存在SN工作详情时优先按照单支详情物料批次进行回退
        List<SnWorkDetail> snWorkDetailList = null!= batchWorkDetail.getSubWorkSheet()? snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(),Constants.LONG_ZERO)
                :snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(),Constants.LONG_ZERO);
        if(ValidateUtils.isValid(snWorkDetailList)){
            snWorkDetailList.forEach(snWorkDetail -> {
                snWorkDetailService.returnSnWorkDetailMaterialBatch(snWorkDetail);
            });
            return;
        }
        //其次存在容器的情况按照容器详情物料批次进行回退
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailList)){
            List<ContainerDetailMaterialBatch> containerDetailMaterialBatchList = containerDetailMaterialBatchRepository.findByContainerDetailIdInAndDeleted(containerDetailList.stream().map(ContainerDetail::getId).distinct().collect(Collectors.toList()), Constants.LONG_ZERO);
            List<ContainerDetailMaterialBatch> containerDetailMaterialBatches = containerDetailMaterialBatchList.stream().filter(containerDetailMaterialBatch -> containerDetailMaterialBatch.getType()== type).collect(Collectors.toList());
            if (ValidateUtils.isValid(containerDetailMaterialBatches)){
                containerDetailService.wsWorkCellContainerDetailBatch(containerDetailMaterialBatches, type);
            }
            return;
        }
        //最后按照工单详情批次进行回退物料，工单+工位+物料+批次 进行分组
        batchWorkDetailMaterialBatchs.stream().collect(Collectors.groupingBy(
                workDetailMaterialBatch -> (null != workDetailMaterialBatch.getBatchWorkDetail().getSubWorkSheet()?
                        workDetailMaterialBatch.getBatchWorkDetail().getSubWorkSheet().getWorkSheet().getId()
                        :workDetailMaterialBatch.getBatchWorkDetail().getWorkSheet().getId())
                        +"-"+ workDetailMaterialBatch.getBatchWorkDetail().getWorkCell().getId()
                        +"-"+ workDetailMaterialBatch.getMaterialId()
                        +"-"+ workDetailMaterialBatch.getMaterialBatch())).forEach((key,batchWorkDetailMaterialBatchList)->{
            //退料数量
            double sum = batchWorkDetailMaterialBatchList.stream().mapToDouble(BatchWorkDetailMaterialBatch::getNumber).sum();
            BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = batchWorkDetailMaterialBatchList.get(Constants.INT_ZERO);
            batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null!= batchWorkDetail.getSubWorkSheet()? batchWorkDetail.getSubWorkSheet().getWorkSheet().getId(): batchWorkDetail.getWorkSheet().getId(),
                    batchWorkDetail.getWorkCell().getId(),batchWorkDetailMaterialBatch.getMaterialId(),batchWorkDetailMaterialBatch.getMaterialBatch(), type,sum));
        });
    }

    /**
     * 根据容器详情ID删除数据
     *
     * @param rollBackDto 容器回退参数
     * @return net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @Override
    public ContainerDetailReplaceDTO deleteContainerDetailById(RollBackDTO rollBackDto) {
        ContainerDetail containerDetail = containerDetailRepository.findById(rollBackDto.getContainerDetailId()).orElseThrow(()->new ResponseException("error.containerDetailNotExist", "容器生产详情不存在"));
        //获取容器对应的sn
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        //替换容器
        if (ValidateUtils.isValid(rollBackDto.getContainerDetailInfoDtoList())){
            BaseDTO baseDto = containerDetailService.replaceContainer(rollBackDto.getContainerDetailInfoDtoList());
            if (Constants.KO.equals(baseDto.getStatus())){
                return new ContainerDetailReplaceDTO(baseDto.getStatus(),baseDto.getMessage());
            }
        }
        //验证回退合法性
        ContainerDetailReplaceDTO containerDetailReplaceDto = batchWorkDetailService.checkContainerOccupation(rollBackDto.getContainerDetailId());
        if (Constants.KO.equals(containerDetailReplaceDto.getStatus()) || ValidateUtils.isValid(containerDetailReplaceDto.getContainerDetailList())){
            return containerDetailReplaceDto;
        }
        List<WsStep> wsStepList = null;
        BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
        if (null != batchWorkDetail.getSubWorkSheet()) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), Constants.LONG_ZERO);
        }
        WorkSheet workSheet =  Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                ? batchWorkDetail.getSubWorkSheet().getWorkSheet():batchWorkDetail.getWorkSheet();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        //删除Rworker缓存
        rworkerCacheServices.deleteCacheByContainerDetail(containerDetail,snWorkDetailList);
        //更新批量工作详情数据(容器回退后工作详情完成状态必然不是完成态)
        int originalFinish = batchWorkDetail.getFinish();
        batchWorkDetail.setInputNumber(batchWorkDetail.getInputNumber()-containerDetail.getInputNumber())
                .setFinishNumber(batchWorkDetail.getFinishNumber() - containerDetail.getInputNumber())
                .setEffectNumber(batchWorkDetail.getEffectNumber()-containerDetail.getInputNumber())
                .setQualifiedNumber(batchWorkDetail.getQualifiedNumber()-containerDetail.getQualifiedNumber())
                .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber()-containerDetail.getUnqualifiedNumber())
                .setTransferNumber(batchWorkDetail.getTransferNumber() != Constants.INT_ZERO ? batchWorkDetail.getTransferNumber()-containerDetail.getQualifiedNumber():Constants.INT_ZERO)
                .setEndDate(null)
                .setFinish(ConstantsEnum.UNFINISHED_STATUS.getCategoryName());
        batchWorkDetailRepository.save(batchWorkDetail);
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(batchWorkDetail.getStep().getId())).findFirst().orElse(new WsStep().setStep(batchWorkDetail.getStep()));
        //更新子工单或者工单的工序完成个数
        if (null != batchWorkDetail.getSubWorkSheet()) {
            if(originalFinish == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),wsStepList,currWsStep,Boolean.FALSE);
            }
        } else if(null != batchWorkDetail.getWorkSheet()){
            if(originalFinish == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),wsStepList,currWsStep,Boolean.FALSE);
            }
        }
        List<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItemList = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(),Constants.LONG_ZERO);
        if(ValidateUtils.isValid(containerDetailUnqualifiedItemList)){
            //获取系统配置的投产粒度(子工单或者工单)
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            Long workSheetId = subWsProductionMode ? batchWorkDetail.getSubWorkSheet().getId() : batchWorkDetail.getWorkSheet().getId();
            containerDetailUnqualifiedItemList.forEach(containerDetailUnqualifiedItem -> {
                Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheetId, batchWorkDetail.getStep().getId(), containerDetailUnqualifiedItem.getUnqualifiedItem().getId(), Constants.LONG_ZERO);
                wsStepUnqualifiedItemOptional.ifPresent(wsStepUnqualifiedItem -> {
                    wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber()-containerDetailUnqualifiedItem.getNumber());
                    wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                });
            });
        }
        //回退未处理的维修分析
        rbaseIMaintainServiceProxy.rollBackContainer(containerDetail);
        //回退未处理的工序复检
        stepReinspectServices[0].rollBackContainer(containerDetail);
        //容器回退 回退当前容器使用的物料用量
        BeanUtil.getHighestPrecedenceBean(IRollbackStepService.class).returnContainerMaterial(containerDetail);
        //删除SN工作详情数据
        batchWorkDetailService.deleteSnWorkDetail(currWsStep,snWorkDetailList);
        snWorkDetailRepository.batchDeleteByContainerDetailId(containerDetail.getId());
        //根据条件删除批量详情关联数据(批量详情易损件、批量详情设备等)
        containerDetailService.updateBatchWorkDetailRelationInfo(containerDetail);
        //删除容器工作详情数据
        batchWorkDetailService.deleteContainerWorkDetail(currWsStep,List.of(containerDetail));
        //删除员工产量包含当前容器详情数据
        List<StaffPerform> staffPerforms = staffPerformRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(staffPerforms)){
            batchWorkDetailService.staffPerformDeleteByIds(staffPerforms);
        }
        //删除工位宽放时长段内下交数据
        BeanUtil.getHighestPrecedenceBean(RollbackStepServiceImpl.class).rollbackContainerDetailWorkCellExtendDetail(containerDetail.getId());
        //删除容器详情记录
        containerDetail.setDeleted(containerDetail.getId());
        containerDetailRepository.save(containerDetail);
        //当回退容器后，导致投参数为0，说明当前容器所在工序已经全部回退
        if (batchWorkDetail.getInputNumber() == Constants.INT_ZERO){
            //删除当前
            batchWorkDetailService.deleteBatchWorkDetail(batchWorkDetail);
        }
        //添加 容器回退历史记录
        addRollBackHistory(rollBackDto, containerDetail.getBatchWorkDetail(), snWorkDetailList, List.of(containerDetail),wsStepList);
        //删除待检任务
        if(null != containerDetail.getBatchWorkDetail().getSubWorkSheet()){
            inspectTaskRepository.deleteBySubWorkSheetIdAndStepIdAndContainerCode(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(),containerDetail.getBatchWorkDetail().getStep().getId(),containerDetail.getContainerCode());
        }else {
            inspectTaskRepository.deleteByWorkSheetIdAndStepIdAndContainerCode(containerDetail.getBatchWorkDetail().getWorkSheet().getId(),containerDetail.getBatchWorkDetail().getStep().getId(),containerDetail.getContainerCode());
        }
        //删除容器对应的烘烤温循老化记录
        List<String> containerCodeList = Lists.newArrayList();
        containerCodeList.add(containerDetail.getContainerCode());
        String preContainerCodes = containerDetail.getPreContainerCodeList();
        if(StringUtils.isNotBlank(preContainerCodes)){
            containerCodeList.addAll(Arrays.asList(preContainerCodes.split(Constants.STR_COMMA)));
        }
        bakeCycleBakeAgeingModelServices[0].logicDeletedBakeCycleBakeAgeingHistory(containerDetail.getBatchWorkDetail().getWorkSheet(),containerDetail.getBatchWorkDetail().getSubWorkSheet(),currWsStep,containerCodeList,null,Constants.INT_ZERO);
        //更新生产计划表 工序组 回退
        updateProductionPlanAndWorkSheetStatistics(containerDetail, batchWorkDetail);
        //更新生产在制数据
        workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsInfo(Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                        ? batchWorkDetail.getSubWorkSheet().getWorkSheet():batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),batchWorkDetail.getStep(),wsStepList,
                containerDetail.getInputNumber()*Constants.NEGATIVE_ONE,containerDetail.getQualifiedNumber()*Constants.NEGATIVE_ONE,
                containerDetail.getUnqualifiedNumber()*Constants.NEGATIVE_ONE,containerDetail.getQualifiedNumber()*Constants.NEGATIVE_ONE);
        return new ContainerDetailReplaceDTO(Constants.OK,"ContainerRollbackSucceeded");
    }

    /**
     * 回退时更新生产计划和工单统计
     * @param containerDetail 容器详情记录
     * @param batchWorkDetail 批量工序生产详情
     */
    private void updateProductionPlanAndWorkSheetStatistics(ContainerDetail containerDetail, BatchWorkDetail batchWorkDetail) {
        // 产品谱系id
        Long pedigreeId = null;
        //产线ID
        Long workLineId = null;
        // 工单
        WorkSheet workSheet = null;
        // 子工单
        SubWorkSheet subWorkSheet = null;
        if(Objects.nonNull(batchWorkDetail.getWorkSheet())){
            workSheet = Optional.ofNullable(batchWorkDetail).map(s -> s.getWorkSheet()).orElse(null);
            pedigreeId =  Optional.ofNullable(workSheet).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
            workLineId =  Optional.ofNullable(workSheet).map(w -> w.getWorkLine()).map(p -> p.getId()).orElse(null);
        }
        if(Objects.nonNull(batchWorkDetail.getSubWorkSheet())){
            subWorkSheet = Optional.ofNullable(batchWorkDetail).map(o ->o.getSubWorkSheet()).orElse(null);
            pedigreeId =  Optional.ofNullable(subWorkSheet).map(s -> s.getWorkSheet()).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
            workLineId = Optional.ofNullable(subWorkSheet).map(s -> s.getWorkLine()).map(p -> p.getId()).orElse(null);
        }
        // 工序
        Step step= Optional.ofNullable(batchWorkDetail).map(s ->s.getStep()).orElse(null);
        // 工序组
        Long stepGroupId = Optional.ofNullable(step).map( o ->o.getStepGroup()).map(i ->i.getId()).orElse(null);
        if(Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)){
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId,containerDetail.getQualifiedNumber(), OperationEnum.MINUS);
        }
        //生产工单定制工序
        WsStep wsStepBySubWorkSheet = null;
        if (subWorkSheet != null) {
            wsStepBySubWorkSheet = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        }
        if (ObjectUtils.isEmpty(wsStepBySubWorkSheet) && Objects.nonNull(workSheet)) {
            //通过工单+工序查询
            wsStepBySubWorkSheet = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        }
        //若为最终工序
        if (!ObjectUtils.isEmpty(wsStepBySubWorkSheet) && StringUtils.isBlank(wsStepBySubWorkSheet.getAfterStepId())) {
            // 更新生产计划表 生产线 回退
            if(Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)){
                productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, containerDetail.getQualifiedNumber(), OperationEnum.MINUS);
            }
            // 更新工单统计表 回退
            if(Objects.nonNull(workSheet)){
                workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), containerDetail.getQualifiedNumber(), containerDetail.getUnqualifiedNumber(),OperationEnum.MINUS);
            }
        }
    }


    /**
     * 容器回退 回退当前容器使用的物料用量
     * @param containerDetail 容器生产详情
     * <AUTHOR>
     * @date  2022/4/13
     * @return void
     */
    @Override
    public void returnContainerMaterial(ContainerDetail containerDetail){
        List<ContainerDetailMaterialBatch> containerDetailMaterialBatches = containerDetailMaterialBatchRepository.findByContainerDetailIdAndTypeNotAndDeleted(containerDetail.getId(),ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailMaterialBatches)){
            //过滤掉不扣料批次类型，对扣料方式进行分组
            Map<Integer, List<ContainerDetailMaterialBatch>> containerDetailMaterialBatchClassify = containerDetailMaterialBatches.stream().collect(Collectors.groupingBy(ContainerDetailMaterialBatch::getType));
            containerDetailMaterialBatchClassify.forEach((type,containerDetailMaterialBatchs)->{
                //工单层级退料
                if (type == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(containerDetailMaterialBatchs)){
                    containerDetailService.wsContainerDetailBatch(containerDetailMaterialBatchs,type);
                }
                //工位层级退料
                if (type == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(containerDetailMaterialBatchs)){
                    containerDetailService.wsWorkCellContainerDetailBatch(containerDetailMaterialBatchs,type);
                }
            });
        }
    }

    /**
     * 回退sn详情
     * @param id        sn详情主键ID
     * @param note      备注
     * @param staffId   操作员工主键ID
     * <AUTHOR>
     * @date  2022/11/11
     * @return void
     */
    @Override
    public void deleteSnWorkDetailById(Long id, String note, Long staffId) throws BadRequestAlertException{
        SnWorkDetail snWorkDetail = snWorkDetailRepository.findById(id).orElseThrow(()->new ResponseException("error.snWorkDetailNotExist", "单支工序生产详情不存在"));
        if(null != snWorkDetail.getContainerDetail() && null != snWorkDetail.getContainerDetail().getId()){
            throw new ResponseException("error.snWorkDetailNotBeRollBackWhenContainerExist", "请删除当前工序的容器生产详情数据进行回退");
        }
        SubWorkSheet subWorkSheet = snWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet():snWorkDetail.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId():workSheet.getId();
        List<MaintainHistoryDetailDTO>  maintainHistoryDetails = Objects.nonNull(subWorkSheet) ?
                rbaseMaintainHistoryDetailProxy.findBySubWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(subWorkSheet.getId(),snWorkDetail.getStep().getId(),snWorkDetail.getSn(),Constants.LONG_ZERO):
                rbaseMaintainHistoryDetailProxy.findByWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(workSheet.getId(),snWorkDetail.getStep().getId(),snWorkDetail.getSn(),Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetails)){
            throw new ResponseException("error.snWorkDetailCreateWsReworkExist", "SN已生成返工单禁止删除");
        }
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(snWorkDetail.getSn(), Constants.LONG_ZERO).orElse(null);
        boolean singleSnOnlineRepair = Boolean.FALSE;
        if(null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(snWorkDetail.getSubWorkSheet().getId())){
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }
        if(null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(snWorkDetail.getWorkSheet().getId())){
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }
        List<WsStep> wsSteps;
        if(singleSnOnlineRepair){
            wsSteps = commonService.findOnlineSnReworkStep(snWorkStatus.getWorkFlow(),workSheet);
        }else {
            wsSteps = commonService.findBatchWsStep(workSheet,subWorkSheet);
        }
        if (CollectionUtils.isEmpty(wsSteps)) {
            throw new ResponseException("error.wsStepNotExist", "工序快照不存在");
        }
        //验证sn合法性
        WsStep currWsStep = snWorkDetailService.validDateSnWorkDetail(snWorkDetail,wsSteps,singleSnOnlineRepair);
        //外协工序 以及外协前置工序禁止回退
        BeanUtil.getHighestPrecedenceBean(IRollbackStepService.class).validStepOemRollbackStep(currWsStep);
        //删除Rworker缓存
        rworkerCacheServices.deleteCacheBySnWorkDetail(snWorkDetail);
        //回退SN工作详情批次数量
        snWorkDetailService.returnSnWorkDetailMaterialBatch(snWorkDetail);
        //删除相关sn详情数据
        snWorkDetailService.rollbackSnWorkDetail(snWorkDetail,Boolean.FALSE,singleSnOnlineRepair);
        //删除可能的FPP状态
        snStepFppStatusRepository.deleteBySn(snWorkDetail.getSn());
        //删除员工产量相关sn详情数据
        Optional<StaffPerform> staffPerformOptional = staffPerformRepository.findBySnWorkDetailIdAndDeleted(snWorkDetail.getId(), Constants.LONG_ZERO);
        staffPerformOptional.ifPresent(staffPerform -> batchWorkDetailService.staffPerformDeleteByIds(Collections.singletonList(staffPerform)));
        //删除工位宽放时长段内下交数据
        BeanUtil.getHighestPrecedenceBean(RollbackStepServiceImpl.class).rollbackSnWorkDetailWorkCellExtendDetail(snWorkDetail.getId());
        //扣减不良批次详情累加的数量
        if (snWorkDetail.getResult() == Constants.INT_ZERO && !singleSnOnlineRepair){
            Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(snWorkDetail.getSubWorkSheet().getId(), snWorkDetail.getStep().getId(), snWorkDetail.getUnqualifiedItem().getId(), Constants.LONG_ZERO);
            if (wsStepUnqualifiedItemOptional.isPresent()){
                WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.get();
                wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() - Constants.INT_ONE);
                wsStepUnqualifiedItem.setDeleted(wsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO ? wsStepUnqualifiedItem.getId():Constants.LONG_ZERO);
                wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
            }
        }
        BatchWorkDetail batchWorkDetail = null != subWorkSheet
                ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), snWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null)
                : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(),snWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        if (null != batchWorkDetail && !singleSnOnlineRepair){
            int originalFinish = batchWorkDetail.getFinish();
            List<WsStep> childWsStepList = new ArrayList<>();
            commonService.findChildWsStep(wsSteps,childWsStepList,currWsStep);
            snWorkDetailService.updateSnByBatchWorkDetail(Objects.nonNull(subWorkSheet)?subWorkSheet.getId():workSheet.getId(),snWorkDetail.getStep().getId(),batchWorkDetail,snWorkDetail,childWsStepList);
            //更新子工单或者工单的工序完成个数
            if(originalFinish == ConstantsEnum.FINISH_STATUS.getCategoryName() ){
                batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(batchWorkDetail.getWorkSheet(),batchWorkDetail.getSubWorkSheet(),wsSteps,currWsStep,Boolean.FALSE);
            }
        }
        //更新容器详情数据
        if(null != snWorkDetail.getContainerDetail()){
            snWorkDetailService.rollBackContainerDetail(snWorkDetail,currWsStep);
        }
        snWorkDetailRepository.logicDelete(snWorkDetail.getId());
        int reworkTime = Constants.INT_ZERO;
        //取出工序 - 开不良 扣减一次 返修数查询，因为 放入时没有开不良
        if(snWorkDetail.getStep().getCategory() > Constants.INT_TWO && snWorkDetail.getStep().getCategory() % Constants.INT_TWO == Constants.INT_ONE && snWorkDetail.getResult() != Constants.INT_ONE){
            reworkTime = snWorkStatus.getReworkTime() - Constants.INT_ONE;
        }else {
            reworkTime = snWorkStatus.getReworkTime();
        }
        //删除烘烤温循老化记录
        bakeCycleBakeAgeingModelServices[0].logicDeletedBakeCycleBakeAgeingHistory(snWorkDetail.getWorkSheet(),snWorkDetail.getSubWorkSheet(),currWsStep,null,snWorkDetail.getSn(),reworkTime);
        //回退未处理的维修分析
        rbaseIMaintainServiceProxy.rollBackSn(snWorkDetail);
        //回退未处理的工序复检
        stepReinspectServices[0].rollBackSn(snWorkDetail);
        //回退rworker下个可能待做的工序信息
        nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsSteps, snWorkDetail, reworkTime, singleSnOnlineRepair,Boolean.TRUE);
        //添加回退记录
        RollBackHistory rollBackHistory = new RollBackHistory();
        rollBackHistory.setWorkSheet(null != snWorkDetail.getSubWorkSheet()?snWorkDetail.getSubWorkSheet().getWorkSheet():snWorkDetail.getWorkSheet())
                .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setStaffId(staffId).setNote(note).setNumber(Constants.INT_ONE).setQualifiedNumber(snWorkDetail.getResult())
                .setStep(snWorkDetail.getStep()).setSn(snWorkDetail.getSn()).setWorkCell(snWorkDetail.getWorkCell())
                .setContainerCode(Objects.nonNull(snWorkDetail.getContainerDetail()) ? snWorkDetail.getContainerDetail().getContainerCode() : null );
        rollBackHistoryRepository.save(rollBackHistory);
        //更新生产计划表 工序组 回退
        rollbackUpdateStepGroupProductionPlan(snWorkDetail, workSheet);
        List<WsStep> wsStepList =  commonService.findBatchWsStep(workSheet,subWorkSheet);;
        //更新生产在制数据
        if (null != batchWorkDetail && !singleSnOnlineRepair) {
            workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsInfo(Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                            ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet(), batchWorkDetail.getSubWorkSheet(), batchWorkDetail.getStep(),wsStepList,
                    Constants.NEGATIVE_ONE, snWorkDetail.getResult() * Constants.NEGATIVE_ONE,
                    snWorkDetail.getResult() == Constants.INT_ONE ? Constants.INT_ZERO : Constants.NEGATIVE_ONE, snWorkDetail.getResult() * Constants.NEGATIVE_ONE);
        }
    }

    /**
     * 回退更新生产计划表 工序组计划粒度
     * @param snWorkDetail 单支工序生产详情
     * @param workSheet 工单
     */
    private void rollbackUpdateStepGroupProductionPlan(SnWorkDetail snWorkDetail, WorkSheet workSheet) {
        // 产品谱系id
        Long pedigreeId =  Optional.ofNullable(workSheet).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
        Long workLineId = Optional.ofNullable(workSheet).map(w -> w.getWorkLine()).map(p -> p.getId()).orElse(null);
        // 工序组id
        Long stepGroupId = Optional.ofNullable(snWorkDetail).map(s ->s.getStep()).map(o ->o.getStepGroup()).map(i ->i.getId()).orElse(null);
        //更新生产计划表
        if(Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)){
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId,Constants.INT_ONE, OperationEnum.MINUS);
        }
    }

    /**
     *  回退修改sn状态
     * @param snWorkDetail sn详情
     * @param deleteByBatchDetail  是否为删除容器详情或者批量详情处删除SN详情=>批量详情删除时无需根据单个SN去回退工单合格数
     * @param singleSnOnlineRepair 是否单支在线反修
     */
    @Override
    public void updateSnWorkStatus(SnWorkDetail snWorkDetail, boolean deleteByBatchDetail, boolean singleSnOnlineRepair) {
        SubWorkSheet currSubWorkSheet = snWorkDetail.getSubWorkSheet();
        WorkSheet currWorkSheet = null != currSubWorkSheet ? currSubWorkSheet.getWorkSheet() : snWorkDetail.getWorkSheet();
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(snWorkDetail.getSn(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.snWorkStatusNotExist", "单支生产状态不存在"));
        //获取当前sn详情产生的不良明细
        SnUnqualifiedItem snUnqualifiedItem = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(snWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
        //若sn详情回退则可能需要扣减累加在（子）工单上面的合格数、不合格数
        batchWorkDetailService.updateSnWorkDetailByWs(snWorkDetail, snWorkStatus, snUnqualifiedItem, deleteByBatchDetail, singleSnOnlineRepair);
        //当前sn详情不合格-》sn返修次数减一
        if (snWorkDetail.getResult() == Constants.INT_ZERO && snWorkStatus.getReworkTime()>Constants.INT_ZERO) {
            snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() - Constants.INT_ONE);
        }
        //调整 sn状态中 sn对应的最新sn详情 ->获取除当前sn详情外的最新一次sn详情 调整最新sn生产详情
        Optional<SnWorkDetail> preSnWorkDetailOptional = snWorkDetailRepository.findTop1BySnAndIdLessThanAndDeletedOrderByIdDesc(snWorkDetail.getSn(), snWorkDetail.getId(), Constants.LONG_ZERO);
        if (preSnWorkDetailOptional.isPresent()) {
            SnWorkDetail preSnWorkDetail = preSnWorkDetailOptional.get();
            //更新sn最新详情
            snWorkStatus.setLatestSnWorkDetail(preSnWorkDetail);
            //查询最新不良SN详情
            SnWorkDetail latestUnqualifiedSnWorkDetail = snWorkDetailRepository.findTop1BySnAndResultAndIdLessThanAndDeletedOrderByIdDesc(snWorkDetail.getSn(), Constants.INT_ZERO, snWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
            snWorkStatus.setLatestReworkSnWorkDetail(latestUnqualifiedSnWorkDetail);
            snWorkStatus.setLatestUnqualifiedItem(null != latestUnqualifiedSnWorkDetail ? latestUnqualifiedSnWorkDetail.getUnqualifiedItem() : null);
            boolean preSingleSnOnlineRepair = Boolean.FALSE;
            if(null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(preSnWorkDetail.getSubWorkSheet().getId())){
                if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && preSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                    preSingleSnOnlineRepair = Boolean.TRUE;
                }
            }
            if(null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(preSnWorkDetail.getWorkSheet().getId())){
                if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && preSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                    preSingleSnOnlineRepair = Boolean.TRUE;
                }
            }
            if(!preSingleSnOnlineRepair){
                snWorkStatus.setWorkFlow(null!=preSnWorkDetail.getSubWorkSheet()?preSnWorkDetail.getSubWorkSheet().getWorkFlow():preSnWorkDetail.getWorkSheet().getWorkFlow());
            }
            WorkSheet preStepWorkSheet = preSnWorkDetail.getSubWorkSheet() != null ? preSnWorkDetail.getSubWorkSheet().getWorkSheet() : preSnWorkDetail.getWorkSheet();
            //除当前sn详情外最新一次sn详情 - 合格
            if (preSnWorkDetail.getResult() == Constants.INT_ONE) {
                //如果前置工序为单支在线返工则需要获取更新SN生产状态的工艺路线
                if(preSingleSnOnlineRepair){
                    WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(preStepWorkSheet.getPedigree(), snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup().getId(),preStepWorkSheet.getClientId());
                    if(Objects.nonNull(reWorkFlow)){
                        snWorkStatus.setWorkFlow(reWorkFlow);
                        WorkFlowStep workFlowStep = workFlowStepRepository.findByWorkFlowIdAndStepIdAndDeleted(reWorkFlow.getId(),preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                        if(Objects.nonNull(workFlowStep) && StringUtils.isBlank(workFlowStep.getAfterStepId())){
                            snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus());
                        }else {
                            snWorkStatus.setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
                        }
                    }else {
                        //若找不到配置的返工路线（有可能禁用了）则默认为合格
                        snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus());
                    }
                }else {
                    WsStep preWsStep = null;
                    if (Objects.nonNull(preSnWorkDetail.getSubWorkSheet())) {
                        preWsStep = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(preSnWorkDetail.getSubWorkSheet().getId(), preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                    }
                    if (Objects.isNull(preWsStep)) {
                        preWsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(preStepWorkSheet.getId(), preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                    }
                    if(StringUtils.isBlank(preWsStep.getAfterStepId())){
                        snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus());
                    }else {
                        snWorkStatus.setStatus(preStepWorkSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()?SnWorkStatusEnum.IN_THE_REPAIR.getStatus():SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus());
                    }
                }


            } else {
                if(preSnWorkDetail.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_SCRAP.getCategoryName()){
                    snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                } else if (preSnWorkDetail.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) {
                    MaintainHistoryDTO maintainHistory = null;
                    if(currWorkSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() && !preStepWorkSheet.getId().equals(currWorkSheet.getId())){
                        MaintainHistoryDetailDTO maintainHistoryDetail = Objects.nonNull(currSubWorkSheet)?
                                rbaseMaintainHistoryDetailProxy.findTop1ByMaintainHistorySubWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(currSubWorkSheet.getId(),snWorkStatus.getId(),currWorkSheet.getId(),preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO)
                                        .orElse(null):
                                rbaseMaintainHistoryDetailProxy.findTop1ByMaintainHistoryWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(currWorkSheet.getId(),snWorkStatus.getId(),currWorkSheet.getId(),preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO)
                                        .orElse(null);

                        if (Objects.nonNull(maintainHistoryDetail)){
                            maintainHistory = maintainHistoryDetail.getMaintainHistory();
                        }
                    }
                    snWorkStatus.setStatus(Objects.nonNull(maintainHistory)?SnWorkStatusEnum.IN_THE_REPAIR.getStatus() :  SnWorkStatusEnum.MAINTAIN.getStatus());
                    if(Objects.nonNull(maintainHistory)){
                        snWorkStatus.setWorkSheet(currWorkSheet).setWorkFlow(currWorkSheet.getWorkFlow()).setSubWorkSheet(currSubWorkSheet);
                    }
                } else if(preSnWorkDetail.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName()){
                    //若不良项目没有不良组别则sn生产状态改为报废
                    if (null == snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup()) {
                        snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                        return;
                    }
                    //若没有配置单支不良在线返工规则则也认为报废
                    WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(preStepWorkSheet.getPedigree(), snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup().getId(),preStepWorkSheet.getClientId());
                    if (null == reWorkFlow) {
                        snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                        return;
                    }
                    snWorkStatus.setWorkFlow(reWorkFlow).setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
                }else if(preSnWorkDetail.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_ADJUSTMENT.getCategoryName()){
                    List<OnlineReworkRule> onlineReworkRuleList = onlineReworkRuleRepository.findByUnqualifiedItemIdAndDeleted(snWorkStatus.getLatestUnqualifiedItem().getId(), net.airuima.constant.Constants.LONG_ZERO);
                    WsStep preWsStep = null;
                    if (Objects.nonNull(preSnWorkDetail.getSubWorkSheet())) {
                        preWsStep = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(preSnWorkDetail.getSubWorkSheet().getId(), preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                    }
                    if (Objects.isNull(preWsStep)) {
                        preWsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(preStepWorkSheet.getId(), preSnWorkDetail.getStep().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                    }
                    if (CollectionUtils.isEmpty(onlineReworkRuleList) || Objects.isNull(preWsStep) || StringUtils.isEmpty(preWsStep.getAfterStepId())
                            || onlineReworkRuleList.stream().noneMatch(onlineReworkRule -> onlineReworkRule.getStep().getId().equals(snWorkDetail.getStep().getId()))) {
                        snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                        return;
                    }else {
                        snWorkStatus.setStatus(SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus());
                    }
                }else {
                    snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                }
            }
            if(currWorkSheet.getCategory()!=WsEnum.ONLINE_RE_WS.getCategory()) {
                if (null != preSnWorkDetail.getSubWorkSheet()) {
                    snWorkStatus.setSubWorkSheet(preSnWorkDetail.getSubWorkSheet());
                } else {
                    snWorkStatus.setWorkSheet(preSnWorkDetail.getWorkSheet());
                }
            }
        } else {
            snWorkStatus.setDeleted(snWorkStatus.getId());
        }
        snWorkStatusRepository.save(snWorkStatus);
    }

    /**
     * 回退前置工序容器详情的流转数量
     *
     * @param deleteContainerDetail      当前待删除的容器详情
     * @param reverseContainerDetailList 需要回退的前置工序容器详情
     * @param deletedSnWorkDetailList    待删除的容器详情对应的SN工作详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @Override
    public void reverseContainerDetail(ContainerDetail deleteContainerDetail, List<ContainerDetail> reverseContainerDetailList, List<SnWorkDetail> deletedSnWorkDetailList) {
        int inputNumber = deleteContainerDetail.getInputNumber();
        for (ContainerDetail reverseContainerDetail : reverseContainerDetailList) {
            List<SnWorkDetail> reverseSnWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(reverseContainerDetail.getId(), Constants.LONG_ZERO);
            //如果当前待删除容器详情和前置工序容器详情都存在工序则按照SN交叉比对进行回退数据，反之则按照从小到大依次回退数据
            if (ValidateUtils.isValid(deletedSnWorkDetailList) && ValidateUtils.isValid(reverseSnWorkDetailList)) {
                long reverseNumber = deletedSnWorkDetailList.stream().map(SnWorkDetail::getSn).toList().stream().filter(sn -> reverseSnWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()).stream().anyMatch(sn::equals)).count();
                reverseContainerDetail.setTransferNumber(reverseContainerDetail.getTransferNumber() + (int) reverseNumber);
                inputNumber = inputNumber - (int) reverseNumber;
            } else if (inputNumber > Constants.INT_ZERO) {
                int reverseNumber = deleteContainerDetail.getPreContainerDetailInfoList().stream().filter(preContainerDetail -> preContainerDetail.getPreContainerDetailId().equals(reverseContainerDetail.getId())).findFirst().map(PreContainerDetailInfo::getNumber).orElse(Constants.INT_ZERO);
                reverseContainerDetail.setTransferNumber(reverseContainerDetail.getTransferNumber() + reverseNumber);
                //回退后置容器流转列表
                String[] afterContainerCodeList = reverseContainerDetail.getAfterContainerCodeList() != null ? reverseContainerDetail.getAfterContainerCodeList().split(Constants.STR_SEMICOLON) : null;
                if (ValidateUtils.isValid(afterContainerCodeList)) {
                    List<String> afterContainerCodes = Arrays.stream(afterContainerCodeList).filter(afterContainerCode -> !afterContainerCode.equals(deleteContainerDetail.getContainerCode())).collect(Collectors.toList());
                    reverseContainerDetail.setAfterContainerCodeList(StringUtils.join(afterContainerCodes, Constants.STR_SEMICOLON));
                }
                inputNumber = inputNumber > reverseNumber ? inputNumber - reverseNumber : Constants.INT_ZERO;
            }
            reverseContainerDetail.setStatus(ConstantsEnum.BINDING.getCategoryName());
            reverseContainerDetail.setUnbindTime(null);
            containerDetailRepository.save(reverseContainerDetail);
        }
    }

    /**
     * 外协工序 以及外协前置工序禁止回退
     *
     * @param currWsStep 工序快照
     */
    @Override
    public void validStepOemRollbackStep(WsStep currWsStep) {
        //当前工序为外协工序 禁止回退
        if (currWsStep.getCategory() == StepCategoryEnum.OEM_STEP.getStatus()) {
            throw new ResponseException("stepOemRollBackStep","外协工序禁止回退！");
        }
        String afterStepIds = currWsStep.getAfterStepId();
        if (!ValidateUtils.isValid(afterStepIds)){
            return;
        }
        //外协前置工序禁止回退
        WsStep nextWsStep = commonService.getNextWsStep(currWsStep.getWorkSheet(), currWsStep.getSubWorkSheet(), currWsStep.getStep());
        if (Objects.nonNull(nextWsStep)) {
            if (nextWsStep.getCategory() == StepCategoryEnum.OEM_STEP.getStatus()) {
                throw new ResponseException("stepOemRollBackStep","外协前置工序禁止回退！");
            }
        }
    }

    /**
     * 批量回退删除工位宽放产生的下交记录
     *
     * @param batchWorkDetailId 批次详情id
     */
    @Override
    public void rollbackBatchWorkCellExtendDetail(Long batchWorkDetailId) {
        workCellExtendStepDetailRepository.logicDeletedBatchId(batchWorkDetailId,Constants.LONG_ZERO);
    }

    /**
     * 容器回退删除工位宽放产生的下交记录
     *
     * @param containerDetailId 容器详情id
     */
    @Override
    public void rollbackContainerDetailWorkCellExtendDetail(Long containerDetailId) {
        workCellExtendStepDetailRepository.logicDeletedContainerDetailId(containerDetailId,Constants.LONG_ZERO);
    }


    /**
     * sn回退删除工位宽放产生的下交记录
     *
     * @param snWorkDetailId 批次详情id
     */
    @Override
    public void rollbackSnWorkDetailWorkCellExtendDetail(Long snWorkDetailId) {
        workCellExtendStepDetailRepository.logicDeletedSnWorkDetailId(snWorkDetailId,Constants.LONG_ZERO);
    }
}
