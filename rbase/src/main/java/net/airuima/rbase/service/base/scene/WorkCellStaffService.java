package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStaff;
import net.airuima.rbase.dto.scene.WorkCellStaffDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStaffRepository;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位员工关系Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellStaffService extends CommonJpaService<WorkCellStaff> {
    private static final String WORK_CELL_STAFF_ENTITY_GRAPH = "workCellStaffEntityGraph";
    private final WorkCellStaffRepository workCellStaffRepository;

    private final WorkCellRepository workCellRepository;

    public WorkCellStaffService(WorkCellStaffRepository workCellStaffRepository, WorkCellRepository workCellRepository) {
        this.workCellStaffRepository = workCellStaffRepository;
        this.workCellRepository = workCellRepository;
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public Page<WorkCellStaff> find(Specification<WorkCellStaff> spec, Pageable pageable) {
        return workCellStaffRepository.findAll(spec, pageable,new NamedEntityGraph(WORK_CELL_STAFF_ENTITY_GRAPH));
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public List<WorkCellStaff> find(Specification<WorkCellStaff> spec) {
        return workCellStaffRepository.findAll(spec,new NamedEntityGraph(WORK_CELL_STAFF_ENTITY_GRAPH));
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public Page<WorkCellStaff> findAll(Pageable pageable) {
        return workCellStaffRepository.findAll(pageable,new NamedEntityGraph(WORK_CELL_STAFF_ENTITY_GRAPH));
    }

    /**
     * 员工新增工位，需要批量添加
     *
     * @param workCellStaffDto 员工工位DTO
     * @return 保存成功的员工工位数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindWorkCells(WorkCellStaffDTO workCellStaffDto) {
        //如果找到则当前为编辑，需继承之前开关状态
        List<WorkCellStaff> workCellStaffList = workCellStaffRepository.findWorkCellStaffByStaffId(workCellStaffDto.getStaffId());
        boolean isEnable = CollectionUtils.isEmpty(workCellStaffList) ? Boolean.TRUE : workCellStaffList.get(Constants.INT_ZERO).getIsEnable();
        workCellStaffRepository.deleteByStaffId(workCellStaffDto.getStaffId());
        if (workCellStaffDto.getWorkCellIds().isEmpty()) {
            return;
        }
        List<WorkCell> workCells = workCellRepository.findAllById(workCellStaffDto.getWorkCellIds());
        List<WorkCellStaff> workCellStaffs = workCells.stream().map(s ->
                new WorkCellStaff()
                        .setStaffId(workCellStaffDto.getStaffId())
                        .setWorkCell(s).setIsEnable(isEnable)
        ).collect(Collectors.toList());
        workCellStaffRepository.saveAll(workCellStaffs);
    }

    /**
     * 根据员工去重
     *
     * @param workCellStaffs 员工工位列表
     * @return 去重后的员工工位列表(一对多)
     */
    public List<WorkCellStaff> getUniqueWorkCellStaffs(List<WorkCellStaff> workCellStaffs) {
        // 员工对应所有工位
        Map<Long, Set<WorkCell>> staffWorkCellMap = workCellStaffs.stream()
                .collect(Collectors.groupingBy(WorkCellStaff::getStaffId,
                        Collectors.mapping(WorkCellStaff::getWorkCell, Collectors.toSet())));
        // 根据员工去重
        List<WorkCellStaff> uniqueWorkCellStaffs = workCellStaffs.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(WorkCellStaff::getStaffId))), ArrayList::new))
                .stream().sorted(Comparator.comparing(WorkCellStaff::getId).reversed()).collect(Collectors.toList());
        uniqueWorkCellStaffs.forEach(s -> s.setWorkCells(staffWorkCellMap.get(s.getStaffId())));
        return uniqueWorkCellStaffs;
    }

    /**
     * 模糊查询员工绑定工位信息
     *
     * @param staffId 员工id
     * @param keyword 工位名称或编码
     * @return 绑定工位
     */
    @Transactional(readOnly = true)
    public List<WorkCell> findByStaffId(Long staffId, String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            return workCellStaffRepository.findWorkCellByStaffIdAndKeyword(staffId, keyword);
        }
        return workCellStaffRepository.findWorkCellByStaffId(staffId);
    }


    /**
     * 手动分页员工工位
     * @param uniqueWorkCellStaffs 去重收集的员工工位集合
     * @param request
     * <AUTHOR>
     * @date  2021/10/23
     * @return
     */
    public List<WorkCellStaff> getWorkCellStaffList(List<WorkCellStaff> uniqueWorkCellStaffs, HttpServletRequest request) {
        int pageSize = request.getParameter("size") != null ? Integer.parseInt(request.getParameter("size")):Constants.INT_FIVE;
        int currentPage = request.getParameter("page") != null ? Integer.parseInt(request.getParameter("page")):Constants.INT_ZERO;
        List<WorkCellStaff> workCellStaffList = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < uniqueWorkCellStaffs.size(); index++) {
            workCellStaffList.add(uniqueWorkCellStaffs.get(index));
        }
        return workCellStaffList;
    }

    /**
     * 启用/禁用指定员工工位
     *
     * @param staffId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByStaffId(Long staffId) {
        List<WorkCellStaff> workCellStaffList = workCellStaffRepository.findWorkCellStaffByStaffId(staffId);
        workCellStaffList.forEach(i -> i.setIsEnable(!i.getIsEnable()));
        workCellStaffRepository.saveAll(workCellStaffList);
    }
}
