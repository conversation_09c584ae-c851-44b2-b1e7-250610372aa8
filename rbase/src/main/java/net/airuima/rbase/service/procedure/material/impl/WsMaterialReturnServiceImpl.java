package net.airuima.rbase.service.procedure.material.impl;

import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;
import net.airuima.rbase.service.procedure.material.IWsMaterialReturnService;
import net.airuima.rbase.service.procedure.material.WsMaterialReturnService;
import net.airuima.util.BeanUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Copyright(C), 2017-2023,武汉睿码智能科技有限公司
 * 工单退料相关实现
 *
 * @author: rain
 * @date: 2023/12/4 11:21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsMaterialReturnServiceImpl implements IWsMaterialReturnService {

    /**
     * 保存工单退料记录
     *
     * @param rollBackMaterialDto 工单退料信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     */
    @Override
    public BaseDTO saveWsMaterialReturn(RollBackMaterialDTO rollBackMaterialDto) {
        return BeanUtil.getBean(WsMaterialReturnService.class).saveWsMaterialReturn(rollBackMaterialDto);
    }
}
