package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.ocmes.OcReplaceContainerDTO;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.batch.dto.MaterialBatchDTO;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.batch.dto.ContainerDetailDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器生产详情Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailService extends CommonJpaService<ContainerDetail>{
    private static final String CONTAINER_DETAIL_ENTITY_GRAPH = "containerDetailEntityGraph";
    private final ContainerDetailRepository containerDetailRepository;
    private final WsStepRepository wsStepRepository;
    @Autowired
    private BatchWorkDetailService batchWorkDetailService;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailFacilityRepository containerDetailFacilityRepository;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;

    public ContainerDetailService(ContainerDetailRepository containerDetailRepository,
                                  WsStepRepository wsStepRepository) {
        this.containerDetailRepository = containerDetailRepository;
        this.wsStepRepository = wsStepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetail> find(Specification<ContainerDetail> spec, Pageable pageable) {
        return containerDetailRepository.findAll(spec, pageable,new NamedEntityGraph(CONTAINER_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<ContainerDetail> find(Specification<ContainerDetail> spec) {
        return containerDetailRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetail> findAll(Pageable pageable) {
        return containerDetailRepository.findAll(pageable,new NamedEntityGraph(CONTAINER_DETAIL_ENTITY_GRAPH));
    }

    /**
     *
     * 根据条件删除批量详情关联数据(批量详情易损件、批量详情设备等)
     * @param containerDetail 待删除容器详情
     * @return void
     */
    public void updateBatchWorkDetailRelationInfo(ContainerDetail containerDetail){
        //如果当前待删除的容器详情易损件在相同工序其他容器中不存在相同易损件的话则直接删除批量详情易损件信息
        List<ContainerDetailWearingPart> containerDetailWearingParts = containerDetailWearingPartRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(),Constants.LONG_ZERO);
        List<ContainerDetailWearingPart> containerDetailWearingPartList = containerDetailWearingPartRepository.findByContainerDetailBatchWorkDetailIdAndDeleted(containerDetail.getBatchWorkDetail().getId(),Constants.LONG_ZERO);
        if(ValidateUtils.isValid(containerDetailWearingParts) && ValidateUtils.isValid(containerDetailWearingPartList)){
           List<ContainerDetailWearingPart> notCurrentContainerWearingPartList = containerDetailWearingPartList.stream().filter(containerDetailWearingPart -> !containerDetailWearingPart.getContainerDetail().getId().equals(containerDetail.getId())).collect(Collectors.toList());
            if(ValidateUtils.isValid(notCurrentContainerWearingPartList)) {
                List<Long> notCurrentContainerWearingPartIdList = notCurrentContainerWearingPartList.stream().map(containerDetailWearingPart -> containerDetailWearingPart.getWearingPart().getId()).collect(Collectors.toList());
                containerDetailWearingParts.forEach(containerDetailWearingPart -> {
                    if(!notCurrentContainerWearingPartIdList.contains(containerDetailWearingPart.getWearingPart().getId())){
                        batchWorkDetailWearingPartRepository.batchDeleteByBatchWorkDetailIdAndWearingPartId(containerDetail.getBatchWorkDetail().getId(),containerDetailWearingPart.getWearingPart().getId());
                    }
                });
            }
        }
        updateBatchWorkDetailFacilityRelationInfo(containerDetail);
    }

    /**
     *
     * 根据条件删除批量详情关联数据(批量详情设备)
     * @param containerDetail 待删除容器详情
     */
    private void updateBatchWorkDetailFacilityRelationInfo(ContainerDetail containerDetail) {
        //如果当前待删除的容器详情设备在相同工序其他容器中不存在相同易设备的话则直接删除批量详情设备信息
        List<ContainerDetailFacility> containerDetailFacilities = containerDetailFacilityRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(),Constants.LONG_ZERO);
        List<ContainerDetailFacility> containerDetailFacilityList = containerDetailFacilityRepository.findByContainerDetailBatchWorkDetailIdAndDeleted(containerDetail.getBatchWorkDetail().getId(),Constants.LONG_ZERO);
        if(ValidateUtils.isValid(containerDetailFacilities) && ValidateUtils.isValid(containerDetailFacilityList)){
            List<ContainerDetailFacility> notCurrentContainerDetailFacilityList = containerDetailFacilityList.stream().filter(containerDetailFacility -> !containerDetailFacility.getContainerDetail().getId().equals(containerDetail.getId())).collect(Collectors.toList());
            if(ValidateUtils.isValid(notCurrentContainerDetailFacilityList)){
                List<Long> notCurrentContainerDetailWearingPartFacilityIds = notCurrentContainerDetailFacilityList.stream().map(ContainerDetailFacility::getFacilityId).collect(Collectors.toList());
                containerDetailFacilities.forEach(containerDetailFacility -> {
                    if(!notCurrentContainerDetailWearingPartFacilityIds.contains(containerDetailFacility.getFacilityId())){
                        batchWorkDetailFacilityRepository.batchDeleteByBatchWorkDetailIdAndFacilityId(containerDetail.getBatchWorkDetail().getId(),containerDetailFacility.getFacilityId());
                    }
                });
            }
        }
    }

    /**
     * 根据提供的容器对应关系进行容器替换
     * @param containerDetailInfoDtoList 容器替换关系列表
     * <AUTHOR>
     * @date  2022/2/8
     * @return BaseDTO
     */
    public BaseDTO replaceContainer(List<RollBackDTO.ContainerDetailInfoDTO> containerDetailInfoDtoList) {
        BaseDTO baseDto = validateReplaceContainer(containerDetailInfoDtoList);
        if (Constants.KO.equals(baseDto.getStatus())){
            return baseDto;
        }
        containerDetailInfoDtoList.stream().forEach(containerDetailInfoDto -> {
            ContainerDetail containerDetail = containerDetailRepository.findById(containerDetailInfoDto.getContainerDetailId()).orElse(null);
            Container container = containerRepository.findByCodeAndDeleted(containerDetailInfoDto.getContainerCode(),Constants.LONG_ZERO).orElse(null);
            //当前容器详情存在前置容器详情：需要替换对应的 转换容器列表
            if (ValidateUtils.isValid(containerDetail.getPreContainerDetailInfoList())){
                List<Long> preContainerDetailIds = containerDetail.getPreContainerDetailInfoList().stream().map(PreContainerDetailInfo::getPreContainerDetailId).collect(Collectors.toList());
                List<ContainerDetail> preContainerDetails = containerDetailRepository.findByIdInAndDeleted(preContainerDetailIds, Constants.LONG_ZERO);
                if (ValidateUtils.isValid(preContainerDetails)){
                    preContainerDetails.stream()
                            .forEach(preContainerDetail -> preContainerDetail.setAfterContainerCodeList(preContainerDetail.getAfterContainerCodeList().replace(containerDetail.getContainerCode(),container.getCode())));
                    containerDetailRepository.saveAll(preContainerDetails);
                }
            }
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            handleAfterContainerDetail(containerDetail, container, batchWorkDetail);
            //当前替换容器为烘烤温循老化工序时，同时需要替换对应的记录
            OcReplaceContainerDTO ocReplaceContainerDTO = null;
            if(null!=batchWorkDetail.getSubWorkSheet()){
                ocReplaceContainerDTO = new OcReplaceContainerDTO(batchWorkDetail.getSubWorkSheet(),containerDetail.getBatchWorkDetail().getStep(),containerDetail.getContainerCode(),container.getCode());
            }else{
                ocReplaceContainerDTO = new OcReplaceContainerDTO(batchWorkDetail.getWorkSheet(),containerDetail.getBatchWorkDetail().getStep(),containerDetail.getContainerCode(),container.getCode());
            }
            bakeCycleBakeAgeingModelServices[0].replaceContainerBakeHistory(ocReplaceContainerDTO);
            bakeCycleBakeAgeingModelServices[0].replaceContainerCycleBakeHistory(ocReplaceContainerDTO);
            bakeCycleBakeAgeingModelServices[0].replaceContainerAgeingHistory(ocReplaceContainerDTO);
            containerDetail.setContainer(container).setContainerCode(container.getCode());
            containerDetailRepository.save(containerDetail);
        });
        return new BaseDTO(Constants.OK,"ReplaceSucceed");
    }

    /**
     * 处理后置容器详情
     * @param containerDetail       容器生产详情
     * @param container             容器
     * @param batchWorkDetail       批量工序生产详情
     */
    private void handleAfterContainerDetail(ContainerDetail containerDetail, Container container, BatchWorkDetail batchWorkDetail) {
        //获取后置容器详情
        List<ContainerDetail> afterContainerDetailList = null != batchWorkDetail.getSubWorkSheet()?
                containerDetailRepository.findBySubWorkSheetId(batchWorkDetail.getSubWorkSheet().getId())
                :containerDetailRepository.findByWorkSheetId(batchWorkDetail.getWorkSheet().getId());

        if (ValidateUtils.isValid(afterContainerDetailList)){
            afterContainerDetailList = afterContainerDetailList.stream().filter(allContainerDetail -> ValidateUtils.isValid(allContainerDetail.getPreContainerDetailInfoList()))
                    .filter(allContainerDetail -> allContainerDetail.getPreContainerDetailInfoList().stream().map(PreContainerDetailInfo::getPreContainerDetailId).anyMatch(value -> value.equals(containerDetail.getId())))
                    .collect(Collectors.toList());
            //当前容器详情存在后置容器详情：需要替换对应的 原容器列表
            if (ValidateUtils.isValid(afterContainerDetailList)){
                afterContainerDetailList.stream()
                        .forEach(afterContainerDetail -> afterContainerDetail.setPreContainerCodeList(afterContainerDetail.getPreContainerCodeList()
                                .replace(containerDetail.getContainerCode(),container.getCode())));
                containerDetailRepository.saveAll(afterContainerDetailList);
            }
        }
    }

    /**
     * 验证替换的新容器的合法性
     * @param containerDetailInfoDtoList  容器替换关系列表
     * <AUTHOR>
     * @date  2022/2/8
     * @return BaseDTO
     */
    public BaseDTO validateReplaceContainer(List<RollBackDTO.ContainerDetailInfoDTO> containerDetailInfoDtoList){
        if (!ValidateUtils.isValid(containerDetailInfoDtoList)){
            return new BaseDTO(Constants.KO,"DataIsEmpty");
        }
        List<Long> containerDetailIdList = containerDetailInfoDtoList.stream().map(RollBackDTO.ContainerDetailInfoDTO::getContainerDetailId).distinct().collect(Collectors.toList());
        List<String> replaceContainerCodeList = containerDetailInfoDtoList.stream().map(RollBackDTO.ContainerDetailInfoDTO::getContainerCode).distinct().collect(Collectors.toList()).stream().filter(ValidateUtils::isValid).collect(Collectors.toList());
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByIdInAndDeleted(containerDetailIdList, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(containerDetailList) || containerDetailIdList.size() != containerDetailList.size()){
            return new BaseDTO(Constants.KO,"ContainerDetailIsNot");
        }
        if (!ValidateUtils.isValid(replaceContainerCodeList)){
            return new BaseDTO(Constants.KO,"ContainerListIsNot");
        }
        if (containerDetailIdList.size() != replaceContainerCodeList.size()){
             return new BaseDTO(Constants.KO,"ContainerIsNot");

        }
        replaceContainerCodeList.forEach(containerCode -> {
            Container container = containerRepository.findByCodeAndDeleted(containerCode, Constants.LONG_ZERO).orElse(new Container());
            container.setCode(containerCode).setName(containerCode);
            containerRepository.save(container);
        });
        List<ContainerDetail> replaceContainerDetails = containerDetailRepository.findByContainerCodeInAndStatusAndDeleted(replaceContainerCodeList, ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(replaceContainerDetails)){
            return new BaseDTO(Constants.KO,"ContainerIsBind");
        }
        return new BaseDTO(Constants.OK);
    }



    /**
     * 容器工单物料批次回退
     * @param containerDetailMaterialBatchs 容器物料批次详情列表
     * @param type 回退工单 类型
     * <AUTHOR>
     * @date  2022/4/21
     * @return void
     */
    public void wsContainerDetailBatch(List<ContainerDetailMaterialBatch> containerDetailMaterialBatchs,int type){
        //工单+物料+批次 进行分组
        containerDetailMaterialBatchs.stream().collect(Collectors.groupingBy(
                containerDetailMaterialBatch -> (containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getSubWorkSheet()!=null?containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getSubWorkSheet().getWorkSheet().getId():containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getWorkSheet().getId())
                        +"-"+ containerDetailMaterialBatch.getMaterialId()
                        +"-"+ containerDetailMaterialBatch.getBatch())).forEach((key,containerDetailMaterialBatchList)->{
            //退料数量
            double sum = containerDetailMaterialBatchList.stream().mapToDouble(ContainerDetailMaterialBatch::getNumber).sum();
            ContainerDetailMaterialBatch containerDetailMaterialBatch = containerDetailMaterialBatchList.get(Constants.INT_ZERO);
            ContainerDetail containerDetail = containerDetailMaterialBatch.getContainerDetail();
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            //扣减当前容器对应的工单批次物料用量信息
            this.abatementBatchWorkDetailMaterial(containerDetailMaterialBatch,sum);
            //分批工单
            batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null!=batchWorkDetail.getSubWorkSheet()?batchWorkDetail.getSubWorkSheet().getWorkSheet().getId():batchWorkDetail.getWorkSheet().getId(),
                    containerDetail.getWorkCell().getId(),containerDetailMaterialBatch.getMaterialId(),containerDetailMaterialBatch.getBatch(),type,sum));
        });
    }

    /**
     * 容器工单工位物料批次回退
     * @param containerDetailMaterialBatchs 容器物料批次详情列表
     * @param type 回退工单工位类型
     * <AUTHOR>
     * @date  2022/4/21
     * @return void
     */
    public void wsWorkCellContainerDetailBatch(List<ContainerDetailMaterialBatch> containerDetailMaterialBatchs,int type){
        //工单+工位+物料+批次 进行分组
        containerDetailMaterialBatchs.stream().collect(Collectors.groupingBy(
                containerDetailMaterialBatch -> (null!= containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getSubWorkSheet()
                        ?containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getSubWorkSheet().getWorkSheet().getId()
                        :containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getWorkSheet().getId())
                        +"-"+ containerDetailMaterialBatch.getContainerDetail().getWorkCell().getId()
                        +"-"+ containerDetailMaterialBatch.getMaterialId()
                        +"-"+ containerDetailMaterialBatch.getBatch())).forEach((key,containerDetailMaterialBatchList)->{
            //退料数量
            double sum = containerDetailMaterialBatchList.stream().mapToDouble(ContainerDetailMaterialBatch::getNumber).sum();
            ContainerDetailMaterialBatch containerDetailMaterialBatch = containerDetailMaterialBatchList.get(Constants.INT_ZERO);
            ContainerDetail containerDetail = containerDetailMaterialBatch.getContainerDetail();
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            //扣减当前容器对应的工单批次物料用量信息
            this.abatementBatchWorkDetailMaterial(containerDetailMaterialBatch,sum);
            //分工单工位
            batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null!=batchWorkDetail.getSubWorkSheet()?batchWorkDetail.getSubWorkSheet().getWorkSheet().getId():batchWorkDetail.getWorkSheet().getId(),
                    containerDetail.getWorkCell().getId(),containerDetailMaterialBatch.getMaterialId(),containerDetailMaterialBatch.getBatch(),type,sum));
        });
    }

    /**
     * 扣减容器详情对应的工单批次物料用料信息
     * @param containerDetailMaterialBatch 容器物料批次信息
     * @param number 容器用料数
     * <AUTHOR>
     * @date  2022/4/19
     * @return void
     */
    public void abatementBatchWorkDetailMaterial(ContainerDetailMaterialBatch containerDetailMaterialBatch,double number){

        Optional<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchOptional = Optional.empty();
        //物料存在&批次存在
        if (containerDetailMaterialBatch.getMaterialId() != null && StringUtils.isNotBlank(containerDetailMaterialBatch.getBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndMaterialBatchAndDeleted(containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getId(), containerDetailMaterialBatch.getMaterialId(), containerDetailMaterialBatch.getBatch(), Constants.LONG_ZERO);
        }
        //工单批次不存在&物料存在&批次不存在
        if (!batchWorkDetailMaterialBatchOptional.isPresent() && containerDetailMaterialBatch.getMaterialId() != null && StringUtils.isBlank(containerDetailMaterialBatch.getBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndDeleted(containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getId(), containerDetailMaterialBatch.getMaterialId(), Constants.LONG_ZERO);
        }
        //工单批次不存在&物料不存在&批次存在
        if (!batchWorkDetailMaterialBatchOptional.isPresent() && containerDetailMaterialBatch.getMaterialId() == null && StringUtils.isNotBlank(containerDetailMaterialBatch.getBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialBatchAndDeleted(containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getId(), containerDetailMaterialBatch.getBatch(), Constants.LONG_ZERO);
        }
        //扣减对应工单批次所用物料
        if (batchWorkDetailMaterialBatchOptional.isPresent()){
            BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = batchWorkDetailMaterialBatchOptional.get();
            batchWorkDetailMaterialBatch.setNumber(NumberUtils.subtract(batchWorkDetailMaterialBatch.getNumber(),number).doubleValue());
            batchWorkDetailMaterialBatchRepository.save(batchWorkDetailMaterialBatch);
        }
    }

    /**
     * 更新容器工序详情对应的容器编码
     *
     * @param containerDetailId 容器工序详情ID
     * @param containerCode     容器编码
     * <AUTHOR>
     * @date 2022/8/1
     **/
    public void updateContainerCode(Long containerDetailId, String containerCode) {
        //1. 判断当前容器工序详情是否为最新
        ContainerDetail containerDetail = containerDetailRepository.findByIdAndDeleted(containerDetailId, Constants.LONG_ZERO);
        validateContainerDetail(containerDetail);

        //2. 获取或新建容器
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByContainerCodeInAndStatusAndDeleted(Arrays.asList(containerCode), Constants.INT_ONE, Constants.LONG_ZERO);
        Container container = containerRepository.findByCodeAndDeleted(containerCode, Constants.LONG_ZERO).orElse(null);
        if (!CollectionUtils.isEmpty(containerDetailList)) {
            //如果容器生产详情（绑定状态）存在，则报错
            throw new ResponseException("error.ContainerCodeIsUse", "容器CODE已被占用");
        } else if (CollectionUtils.isEmpty(containerDetailList) && ObjectUtils.isEmpty(container)) {
            //如果容器生产详情不存在, 且容器表不存在, 则新增容器
            container = new Container().setName(containerCode).setCode(containerCode).setStatus(Constants.FALSE);
            containerRepository.save(container);
        }

        //3. 更新当前及之前的容器工序详情的容器编码
        String containerCodeDB = containerDetail.getContainerCode();
        List<ContainerDetail> containerDetailListDB = containerDetailRepository.findBySubWorkSheetId(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId());
        if (!CollectionUtils.isEmpty(containerDetailListDB)) {
            for (ContainerDetail containerDetailDB : containerDetailListDB) {
                if (containerCodeDB.equals(containerDetailDB.getContainerCode())) {
                    // 如果容器编码与旧容器编码相同，则更新
                    containerDetailDB.setContainer(container).setContainerCode(container.getCode());
                }
                if (StringUtils.isNotBlank(containerDetailDB.getPreContainerCodeList())) {
                    // 处理原始容器列表
                    String preContainerCodeListNew = getContainerCodeListNew(containerDetailDB.getPreContainerCodeList(), containerCodeDB, containerCode);
                    containerDetailDB.setPreContainerCodeList(preContainerCodeListNew);
                }
                if (StringUtils.isNotBlank(containerDetailDB.getAfterContainerCodeList())) {
                    // 处理转换容器列表
                    String afterContainerCodeListNew = getContainerCodeListNew(containerDetailDB.getAfterContainerCodeList(), containerCodeDB, containerCode);
                    containerDetailDB.setAfterContainerCodeList(afterContainerCodeListNew);
                }
            }
        }
        containerDetailRepository.saveAll(containerDetailListDB);
    }

    /**
     * 校验容器详细
     * @param containerDetail   容器生产详情
     */
    private void validateContainerDetail(ContainerDetail containerDetail) {
        if (ObjectUtils.isEmpty(containerDetail)) {
            throw new ResponseException("error.ContainerDetailNotExist", "容器工序详情不存在");
        } else if (containerDetail.getStatus() != Constants.INT_ONE) {
            throw new ResponseException("error.ContainerDetailIsOld", "容器工序详情并非最新（非绑定状态）");
        }
    }

    /**
     * 替换容器列表字符串中容器CODE
     *
     * @param containerCodeListOld 旧容器列表
     * @param containerCodeDB      待替换容器CODE
     * @param containerCodeNew     新容器CODE
     * @return : java.lang.String
     * <AUTHOR>
     * @date 2022/8/2
     **/
    public String getContainerCodeListNew(String containerCodeListOld, String containerCodeDB, String containerCodeNew) {
        String containerCodeListOldStr = ";" + containerCodeListOld + ";";
        String containerCodeListNew = containerCodeListOldStr.replaceAll(";" + containerCodeDB + ";", ";" + containerCodeNew + ";");
        return containerCodeListNew.substring(1, containerCodeListNew.length() - 1);
    }

    /**
     * 获取工序容器详情
     *
     * @param serialNumber 子工单号
     * @param containerCode 容器编码
     * @return List
     */
    public List<ContainerDetailDTO> getStepContainerDetail(String serialNumber, String containerCode) {
        List<ContainerDetailDTO> containerDetailDtoList = Lists.newArrayList();
        // 获取工单的工艺路线
        SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
        Assert.notNull(subWorkSheet, serialNumber + "子工单不存在");
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        Assert.notEmpty(wsStepList, "生产工单定制工序不存在");

        // 获取容器详情
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetSerialNumberAndContainerCodeAndDeleted(serialNumber, containerCode, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(containerDetailList)) {
            return containerDetailDtoList;
        }
        wsStepList.forEach(wsStep -> {
            ContainerDetailDTO containerDetailDto = new ContainerDetailDTO();
            containerDetailList.stream()
                    .filter(detail -> ObjectUtils.nullSafeEquals(wsStep.getStep().getId(), detail.getBatchWorkDetail().getStep().getId()))
                    .findFirst()
                    .ifPresent(containerDetailDto::setContainerDetail);
            containerDetailDto.setStep(wsStep.getStep());
            containerDetailDtoList.add(containerDetailDto);
        });
        return containerDetailDtoList;
    }
}
