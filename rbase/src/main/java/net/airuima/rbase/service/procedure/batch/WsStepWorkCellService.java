package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.batch.WsStepWorkCell;
import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepWorkCellRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.service.procedure.batch.dto.WsStepWorkCellGetDTO;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.batch.dto.WsStepWorkCellSaveDTO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsStepWorkCellService extends CommonJpaService<WsStepWorkCell>{

    private final String WS_STEP_WORK_CELL_ENTITY_GRAPH = "wsStepWorkCellEntityGraph";
    private final WsStepWorkCellRepository wsStepWorkCellRepository;

    private final WsStepRepository wsStepRepository;

    private final SubWorkSheetRepository subWorkSheetRepository;

    private final WorkSheetRepository workSheetRepository;

    private final WsStepService wsStepService;

    private final StepRepository stepRepository;

    private final WorkCellRepository workCellRepository;

    public WsStepWorkCellService(WsStepWorkCellRepository wsStepWorkCellRepository, WsStepRepository wsStepRepository, SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository, WsStepService wsStepService, StepRepository stepRepository, WorkCellRepository workCellRepository) {
        this.wsStepWorkCellRepository = wsStepWorkCellRepository;
        this.wsStepRepository = wsStepRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.wsStepService = wsStepService;
        this.stepRepository = stepRepository;
        this.workCellRepository = workCellRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WsStepWorkCell> find(Specification<WsStepWorkCell> spec, Pageable pageable) {
        return wsStepWorkCellRepository.findAll(spec,pageable,new NamedEntityGraph(WS_STEP_WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WsStepWorkCell> find(Specification<WsStepWorkCell> spec) {
        return wsStepWorkCellRepository.findAll(spec,new NamedEntityGraph(WS_STEP_WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WsStepWorkCell> findAll(Pageable pageable) {
        return wsStepWorkCellRepository.findAll(pageable,new NamedEntityGraph(WS_STEP_WORK_CELL_ENTITY_GRAPH));
    }


    /**
     * 根据（子）工单 id 获取工艺路线中工序指定的工位信息
     * @param id （子）工单id
     * <AUTHOR>
     * @date  2022/8/11
     * @return List<WsStepWorkCellGetDTO>
     */
    public List<WsStepWorkCellGetDTO> findWsStepWorkCell(Long id) {
        List<WsStepWorkCellGetDTO> wsStepWorkCellGetDtoList = Lists.newArrayList();
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        //优先获取子工单对应定制工艺流程
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(id, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)){
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(id,Constants.LONG_ZERO);
            //工序快照不存在，根据当前id查询是否为子工单，通过子工单对应的工单获取快照
            if (!ValidateUtils.isValid(wsStepList)&&subWorkSheetOptional.isPresent()){
                wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheetOptional.get().getWorkSheet().getId(),Constants.LONG_ZERO);
            }
        }
        if (!ValidateUtils.isValid(wsStepList)){
            return wsStepWorkCellGetDtoList;
        }
        //子工单 获取对应的工序指定工位
        if (subWorkSheetOptional.isPresent()){
            List<WsStepWorkCell> wsStepWorkCellList = wsStepWorkCellRepository.findBySubWorkSheetIdAndDeleted(id, Constants.LONG_ZERO);
            addWsStepWorkCell(wsStepWorkCellGetDtoList,wsStepWorkCellList,wsStepList);
        }
        //工单 获取对应的工序指定工位
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (workSheetOptional.isPresent()){
            List<WsStepWorkCell> wsStepWorkCellList = wsStepWorkCellRepository.findByWorkSheetIdAndDeleted(id, Constants.LONG_ZERO);
            addWsStepWorkCell(wsStepWorkCellGetDtoList,wsStepWorkCellList,wsStepList);
        }
        return wsStepWorkCellGetDtoList;
    }


    /**
     * 根据有序的工艺路线，添加工序 指定的工位信息
     * @param wsStepWorkCellGetDtoList 工序指定工位信息dto
     * @param wsStepWorkCellList 工序工位信息
     * @param wsStepList 有序工艺路线
     * <AUTHOR>
     * @date  2022/8/11
     * @return void
     */
    public void addWsStepWorkCell(List<WsStepWorkCellGetDTO> wsStepWorkCellGetDtoList,List<WsStepWorkCell> wsStepWorkCellList,List<WsStep> wsStepList){

        List<WsStep> treeWsSteps = Lists.newArrayList();
        //排序工艺路线
        wsStepService.findTreeStep(wsStepList,null,treeWsSteps);
        //根据工序进行分组
        Map<Step, List<WsStepWorkCell>> wsStepWorkCellMap = new HashMap<>();
        if (ValidateUtils.isValid(wsStepWorkCellList)){
            wsStepWorkCellMap = wsStepWorkCellList.stream().collect(Collectors.groupingBy(WsStepWorkCell::getStep));
        }
        Map<Step, List<WsStepWorkCell>> finalWsStepWorkCellMap = wsStepWorkCellMap;
        //根据工艺路线流程添加工序指定工位信息
        treeWsSteps.stream().forEach(wsStep -> {
            Step step = wsStep.getStep();
            WsStepWorkCellGetDTO wsStepWorkCellGetDto = new WsStepWorkCellGetDTO();
            wsStepWorkCellGetDto.setStepInfo(new WsStepWorkCellGetDTO.StepInfo(step));
            if (finalWsStepWorkCellMap.size() != Constants.INT_ZERO && ValidateUtils.isValid(finalWsStepWorkCellMap.get(step))){
                List<WsStepWorkCell> wsStepWorkCells = finalWsStepWorkCellMap.get(step);
                List<WsStepWorkCellGetDTO.WorkCellInfo> workCellInfoList = wsStepWorkCells.stream().map(WsStepWorkCell::getWorkCell).map(WsStepWorkCellGetDTO.WorkCellInfo::new).collect(Collectors.toList());
                wsStepWorkCellGetDto.setWorkCellInfoList(workCellInfoList);
            }
            wsStepWorkCellGetDtoList.add(wsStepWorkCellGetDto);
        });
    }

    /**
     * 添加子工单对应的工序所指定的工位 （子工单分单时）
     * @param finalSubWorkSheetList 子工单列表
     * @param subWorkSheetDtoList 子工单对应工序的指定工位信息
     * <AUTHOR>
     * @date  2022/8/11
     * @return void
     */
    public void addSubWorkSheetStepWorkCell(List<SubWorkSheet> finalSubWorkSheetList, List<SubWorkSheetDTO> subWorkSheetDtoList) {

        List<WsStepWorkCell> wsStepWorkCells = Lists.newArrayList();
        finalSubWorkSheetList.forEach(subWorkSheet -> {
            Optional<SubWorkSheetDTO> subWorkSheetDTOOptional = subWorkSheetDtoList.stream().filter(subWorkSheetDto -> subWorkSheetDto.getSerialNumber().equals(subWorkSheet.getSerialNumber())).findFirst();
            if (!subWorkSheetDTOOptional.isPresent()){
                return;
            }
            SubWorkSheetDTO subWorkSheetDto = subWorkSheetDTOOptional.get();
            //删除当前子工单绑定的工序指定工位
            wsStepWorkCellRepository.logicBySubWorkSheetId(subWorkSheet.getId());
            //添加子工单工序指定的工位信息
            if (ValidateUtils.isValid(subWorkSheetDto.getWsStepWorkCellGetDto())){
                subWorkSheetDto.getWsStepWorkCellGetDto().forEach(wsStepWorkCellGetDto -> {
                    Step step = stepRepository.findByIdAndDeleted(wsStepWorkCellGetDto.getStepInfo().getId(), Constants.LONG_ZERO).orElse(null);
                    if (ValidateUtils.isValid(wsStepWorkCellGetDto.getWorkCellInfoList())){
                        wsStepWorkCellGetDto.getWorkCellInfoList().stream().distinct().forEach(workCellInfo -> {
                            WorkCell workCell = workCellRepository.findByIdAndDeleted(workCellInfo.getId(), Constants.LONG_ZERO).orElse(null);
                            WsStepWorkCell wsStepWorkCell = new WsStepWorkCell();
                            wsStepWorkCell.setWorkCell(workCell).setStep(step).setSubWorkSheet(subWorkSheet);
                            wsStepWorkCells.add(wsStepWorkCell);
                        });
                    }
                });
            }
        });

        wsStepWorkCellRepository.saveAll(wsStepWorkCells);
    }

    /**
     * 保存或修改 (子)工单 工序指定工位信息
      * @param wsStepWorkCellSaveDto  工序指定工位信息DTO
     * <AUTHOR>
     * @date  2022/8/11
     * @return BaseDTO
     */
    public BaseDTO saveWsStepWorkCell(WsStepWorkCellSaveDTO wsStepWorkCellSaveDto) {

        List<WsStepWorkCell> wsStepWorkCellList = Lists.newArrayList();

        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(wsStepWorkCellSaveDto.getId(), Constants.LONG_ZERO);
        if (subWorkSheetOptional.isPresent()){
            addWsStepWc(wsStepWorkCellSaveDto.getWsStepWorkCellInfos(),wsStepWorkCellList,null,subWorkSheetOptional.get());
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(wsStepWorkCellSaveDto.getId(), Constants.LONG_ZERO);
        if (workSheetOptional.isPresent()){
            addWsStepWc(wsStepWorkCellSaveDto.getWsStepWorkCellInfos(),wsStepWorkCellList,workSheetOptional.get(),null);
        }
        if (ValidateUtils.isValid(wsStepWorkCellList)){
            wsStepWorkCellRepository.saveAll(wsStepWorkCellList);
            return new BaseDTO(Constants.OK,"saveWsStepWorkCellSucceed");
        }
        return new BaseDTO(Constants.KO,"saveWsStepWorkCellError");
    }

    /**
     * 获取(子)工单 工序指定工位信息 （web主动修改或创建时）
     * @param wsStepWorkCellInfos 工序指定工位信息
     * @param wsStepWorkCellList 工序工位获取信息
     * @param workSheet 工单
     * @param subWorkSheet 子工单
     * <AUTHOR>
     * @date  2022/8/11
     * @return void
     */
    public void addWsStepWc(List<WsStepWorkCellSaveDTO.WsStepWorkCellInfo> wsStepWorkCellInfos,List<WsStepWorkCell> wsStepWorkCellList,WorkSheet workSheet,SubWorkSheet subWorkSheet){

        if (ValidateUtils.isValid(wsStepWorkCellInfos)){
            wsStepWorkCellInfos.forEach(wsStepWorkCellInfo -> {
                Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(wsStepWorkCellInfo.getStepId(), Constants.LONG_ZERO);
                if (!stepOptional.isPresent()){
                    return;
                }
                if (ValidateUtils.isValid(wsStepWorkCellInfo.getWorkCellIds())){
                    wsStepWorkCellInfo.getWorkCellIds().stream().distinct().forEach(workCellId ->{
                        Optional<WorkCell> workCellOptional = workCellRepository.findByIdAndDeleted(workCellId, Constants.LONG_ZERO);
                        if (!workCellOptional.isPresent()){
                            return;
                        }
                        //添加子工单工序指定的工位信息
                        WsStepWorkCell wsStepWorkCell = null;
                        if (subWorkSheet != null){
                            wsStepWorkCell = wsStepWorkCellRepository.findBySubWorkSheetIdAndStepIdAndWorkCellIdAndDeleted(
                                    subWorkSheet.getId(), wsStepWorkCellInfo.getStepId(), workCellId, Constants.LONG_ZERO).orElse(new WsStepWorkCell());
                            wsStepWorkCell.setSubWorkSheet(subWorkSheet);
                        }
                        if (workSheet != null){
                            wsStepWorkCell = wsStepWorkCellRepository.findByWorkSheetIdAndStepIdAndWorkCellIdAndDeleted(
                                    workSheet.getId(), wsStepWorkCellInfo.getStepId(), workCellId, Constants.LONG_ZERO).orElse(new WsStepWorkCell());
                            wsStepWorkCell.setWorkSheet(workSheet);
                        }
                        if (null != wsStepWorkCell){
                            wsStepWorkCell.setStep(stepOptional.get())
                                    .setWorkCell(workCellOptional.get());
                        }
                        wsStepWorkCellList.add(wsStepWorkCell);
                    });

                }
            });
        }
    }

    /**
     * 根据（子）工单和工序id 获取 工序指定工位信息
      * @param id (子)工单 id
     * @param stepId 工序id
     * <AUTHOR>
     * @date  2022/8/18
     * @return List<WsStepWorkCellGetDTO.WorkCellInfo>
     */
    @Transactional(readOnly = true)
    public List<WsStepWorkCellGetDTO.WorkCellInfo> findByWsAndStepId(Long id, Long stepId){
        List<WsStepWorkCellGetDTO.WorkCellInfo> workCellInfos = Lists.newArrayList();
        List<WsStepWorkCell> wsStepWorkCells = Lists.newArrayList();
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        //子工单添加
        if (subWorkSheetOptional.isPresent()){
            wsStepWorkCells.addAll(wsStepWorkCellRepository.findBySubWorkSheetIdAndStepIdAndDeleted(id, stepId, Constants.LONG_ZERO));
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id,Constants.LONG_ZERO);
        //工单添加
        if (workSheetOptional.isPresent()) {
            wsStepWorkCells.addAll(wsStepWorkCellRepository.findByWorkSheetIdAndStepIdAndDeleted(id, stepId, Constants.LONG_ZERO));
        }
        if (ValidateUtils.isValid(wsStepWorkCells)){
            workCellInfos.addAll(wsStepWorkCells.stream().map(wsStepWorkCell -> new WsStepWorkCellGetDTO.WorkCellInfo(wsStepWorkCell.getWorkCell())).collect(Collectors.toList()));
        }
        return workCellInfos;
    }
}
