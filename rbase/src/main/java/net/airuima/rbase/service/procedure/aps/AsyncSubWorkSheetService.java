package net.airuima.rbase.service.procedure.aps;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.common.CommonService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 异步子工单生成服务
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Service
public class AsyncSubWorkSheetService {

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private SubWorkSheetService subWorkSheetService;

    @Autowired
    private CommonService commonService;

    /**
     * 异步批量处理子工单生成
     */
    @Async("subWorkSheetGenerationExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processSubWorkSheetGenerationAsync(List<Long> taskWorkSheetIds) {

        taskWorkSheetIds.parallelStream().forEach(this::generateSubWorkSheetForTask);
    }

    /**
     * 为单个任务生成子工单
     */
    private void generateSubWorkSheetForTask(Long workSheetId) {
        // 重新查询工单以确保在新事务中获取最新状态
        Optional<WorkSheet> workSheetOpt = workSheetRepository.findById(workSheetId);
        if (!workSheetOpt.isPresent()) {
            return;
        }

        WorkSheet workSheet = workSheetOpt.get();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        if (!subWsProductionMode) {
            // 如果不是子工单投产模式，则不需要生成子工单
            return;
        }

        //生成子工单
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
        int splitNumber = (null != pedigreeConfig && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) ?
                pedigreeConfig.getSplitNumber() : Constants.INT_TWO_HUNDRED;

        subWorkSheetService.autoGenerateSubWorkSheet(
                workSheet.getId(),
                Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null),
                workSheet.getPlanStartDate(),
                workSheet.getPlanEndDate(),
                splitNumber,
                Boolean.TRUE
        );
    }
}
