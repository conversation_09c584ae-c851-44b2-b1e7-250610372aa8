package net.airuima.rbase.service.base.quality;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.quality.StepWarningStandardImportDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 生产工序良率预警标准service
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepWarningStandardService extends CommonJpaService<StepWarningStandard> {
    private final String STEP_WARNING_STANDARD_GRAPH = "stepWarningStandardEntityGraph";
    private final StepWarningStandardRepository stepWarningStandardRepository;
    private final WorkSheetRepository workSheetRepository;
    private final PriorityElementConfigRepository priorityElementConfigRepository;
    private final PedigreeRepository pedigreeRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepRepository stepRepository;
    private final StepGroupRepository stepGroupRepository;
    @Autowired
    private  CommonService commonService;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public StepWarningStandardService(StepWarningStandardRepository stepWarningStandardRepository, WorkSheetRepository workSheetRepository,
                                      PriorityElementConfigRepository priorityElementConfigRepository, PedigreeRepository pedigreeRepository,
                                      WorkFlowRepository workFlowRepository, StepRepository stepRepository, StepGroupRepository stepGroupRepository) {
        this.stepWarningStandardRepository = stepWarningStandardRepository;
        this.workSheetRepository = workSheetRepository;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
        this.stepGroupRepository = stepGroupRepository;
    }

    @Override
    @FetchMethod
    public Page<StepWarningStandard> find(Specification<StepWarningStandard> spec, Pageable pageable) {
        return stepWarningStandardRepository.findAll(spec, pageable,new NamedEntityGraph(STEP_WARNING_STANDARD_GRAPH));
    }

    @Override
    @FetchMethod
    public List<StepWarningStandard> find(Specification<StepWarningStandard> spec) {
        return stepWarningStandardRepository.findAll(spec,new NamedEntityGraph(STEP_WARNING_STANDARD_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<StepWarningStandard> findAll(Pageable pageable) {
        return stepWarningStandardRepository.findAll(pageable,new NamedEntityGraph(STEP_WARNING_STANDARD_GRAPH));
    }

    /**
     * 新增预警规则
     * <AUTHOR>
     * @date 2022/10/25
     * @param stepWarningStandard 预警规则
     */
    public void saveInstance(StepWarningStandard stepWarningStandard) {
        //校验
        checkWarningStandard(stepWarningStandard);

        // 工单问题(直接用实体类接收工单对象的话会保存异常,需要查询出来set一下才可以正常保存)
        if (null != stepWarningStandard.getWorkSheet()) {
            Optional<WorkSheet> workSheet = workSheetRepository.findById(stepWarningStandard.getWorkSheet().getId());
            workSheet.ifPresent(stepWarningStandard::setWorkSheet);
        }

        save(stepWarningStandard);
    }

    /**
     * 预警规则检验
     * <AUTHOR>
     * @date 2022/10/27
     * @param stepWarningStandard 预警规则
     */
    private void checkWarningStandard(StepWarningStandard stepWarningStandard) {
        // 验证是否添加预警条件
        if (null == stepWarningStandard.getPriorityElementConfig()) {
            throw new ResponseException("error.NoWarningCondition", "未添加预警条件");
        }
        // 验证预警标准
        if (stepWarningStandard.getBaseNumber()<=Constants.INT_ZERO || stepWarningStandard.getWaringRate()<=Constants.INT_ZERO || stepWarningStandard.getStopRate()<=Constants.INT_ZERO ||
        stepWarningStandard.getWaringRate() > Constants.WARING_STOP_RATE_NUMBER || stepWarningStandard.getStopRate() > Constants.WARING_STOP_RATE_NUMBER) {
            throw new ResponseException("error.WarningCriteriaError", "预警标准输入错误");
        }
        // 验证是否相同预警规则
        StepWarningStandard queryStepWarningStandard = stepWarningStandardRepository.findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndDeleted(
                null != stepWarningStandard.getPedigree() ? stepWarningStandard.getPedigree().getId() : null,
                null != stepWarningStandard.getStep() ? stepWarningStandard.getStep().getId() : null,
                null != stepWarningStandard.getWorkFlow() ? stepWarningStandard.getWorkFlow().getId() : null,
                null != stepWarningStandard.getStepGroup() ? stepWarningStandard.getStepGroup().getId() : null,
                null != stepWarningStandard.getWorkSheet() ? stepWarningStandard.getWorkSheet().getId() : null,
                stepWarningStandard.getClientId(),
                stepWarningStandard.getWorkSheetCategory(),Constants.LONG_ZERO);
        if(Objects.isNull(stepWarningStandard.getId()) && Objects.nonNull(queryStepWarningStandard)){
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }
        if(Objects.nonNull(stepWarningStandard.getId()) && Objects.nonNull(queryStepWarningStandard) && !queryStepWarningStandard.getId().equals(stepWarningStandard.getId())){
            throw new ResponseException("error.OtherRulesAlreadyExist", "已有其他规则创建了相同的预警条件");
        }
    }

    /**
     * 修改预警规则
     * <AUTHOR>
     * @date 2022/10/25
     * @param stepWarningStandard 预警规则
     */
    public void updateInstance(StepWarningStandard stepWarningStandard) {
        if (null == stepWarningStandard.getId()) {
            throw new ResponseException("error.NoObjectSelected", "未选择需要修改的对象");
        }
        //校验
        checkWarningStandard(stepWarningStandard);

        StepWarningStandard byStepWarningStandard  = stepWarningStandardRepository.findByIdAndDeleted(stepWarningStandard.getId(),Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.stepWarningStandardNotExist", "生产工序预警标准不存在"));
        byStepWarningStandard.setBaseNumber(stepWarningStandard.getBaseNumber());
        byStepWarningStandard.setStopRate(stepWarningStandard.getStopRate());
        byStepWarningStandard.setWaringRate(stepWarningStandard.getWaringRate());
        byStepWarningStandard.setNote(stepWarningStandard.getNote());

        update(byStepWarningStandard);
    }

    /**
     * 逻辑删除预警规则
     * <AUTHOR>
     * @date 2022/10/27
     * @param id 预警规则id
     */
    public void deletedById(Long id) {
        stepWarningStandardRepository.deletedById(id);
    }

    /**
     * 导入预警规则数据
     * <AUTHOR>
     * @date 2022/10/26
     * @param stepWarningStandardImportDTOList 预警规则导入数据
     * @return BaseClientDTO
     */
    public BaseClientDTO importExcel(List<StepWarningStandardImportDTO> stepWarningStandardImportDTOList) {
        //校验表格内是否有重复的预警规则
        List<StepWarningStandardImportDTO> list = stepWarningStandardImportDTOList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(i -> i.getPedigreeCode() + "_" + i.getWorkFlowCode()+ "_"+i.getWorkSheetCode()+"_"+i.getWorkSheetCategory()+"_"+i.getStepCode()+"_"+i.getStepGroupCode()+"_"+i.getClientCode()))), ArrayList::new));
        if (list.size() < stepWarningStandardImportDTOList.size()) {
            throw new ResponseException("error.SameRule", "预警规则不可重复");
        }

        List<StepWarningStandard> stepWarningStandardList = new ArrayList<>();
        for (StepWarningStandardImportDTO stepWarningStandardImportDTO : stepWarningStandardImportDTOList) {
            List<Integer> combination = new ArrayList<>();
            StepWarningStandard stepWarningStandard = new StepWarningStandard();

            //产品谱系
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getPedigreeCode())) {
                combination.add(Constants.PEDIGREE_ELEMENT);
                Optional<Pedigree> pedigree = pedigreeRepository.findByCodeAndDeleted(stepWarningStandardImportDTO.getPedigreeCode(), Constants.LONG_ZERO);
                if (!pedigree.isPresent()) {
                    throw new ResponseException("error.NoPedigree", "没有该产品谱系");
                }
                if(!pedigree.get().getIsEnable()){
                    throw new ResponseException("error.pedigreeDisabled", "产品谱系已禁用");
                }
                pedigree.ifPresent(stepWarningStandard::setPedigree);
            }
            //工艺路线
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getWorkFlowCode())) {
                combination.add(Constants.WORKFLOW_ELEMENT);
                Optional<WorkFlow> workFlow = workFlowRepository.findByCodeAndDeleted(stepWarningStandardImportDTO.getWorkFlowCode(), Constants.LONG_ZERO);
                if (!workFlow.isPresent()) {
                    throw new ResponseException("error.NoWorkFlow", "没有该工艺路线");
                }
                if(!workFlow.get().getIsEnable()){
                    throw new ResponseException("error.workFlowDisabled", "工艺路线("+workFlow.get().getCode()+")已禁用");
                }
                workFlow.ifPresent(stepWarningStandard::setWorkFlow);
            }
            //工单
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getWorkSheetCode())) {
                combination.add(Constants.WORKSHEET_ELEMENT);
                Optional<WorkSheet> workSheet = workSheetRepository.findBySerialNumberAndDeleted(stepWarningStandardImportDTO.getWorkSheetCode(), Constants.LONG_ZERO);
                if (!workSheet.isPresent()) {
                    throw new ResponseException("error.NoWorkSheet", "没有该工单");
                }
                workSheet.ifPresent(stepWarningStandard::setWorkSheet);
            }
            //工单类型
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getWorkSheetCategory())) {
                combination.add(Constants.WORKSHEET_CATEGORY_ELEMENT);
                String workSheetCategoryData = commonService.getDictionaryData(Constants.WORK_SHEET_CATEGORY);
                if (StringUtils.isNotBlank(workSheetCategoryData)) {
                    List<Map<String, String>> workSheetCategoryList = JSON.parseObject(workSheetCategoryData, new TypeReference<List<Map<String, String>>>() {
                    });
                    Integer workSheetCategory = workSheetCategoryList.stream().filter(map -> map.get(Constants.WORK_SHEET_CATEGORY_DATA).equals(stepWarningStandardImportDTO.getWorkSheetCategory())).findFirst().map(map -> map.get(Constants.WORK_SHEET_CATEGORY_DATA_KEY)).map(Integer::parseInt).orElse(null);
                    stepWarningStandard.setWorkSheetCategory(workSheetCategory);
                }
                if (null == stepWarningStandard.getWorkSheetCategory()) {
                    throw new ResponseException("error.WorkSheetCategoryError", "工单类型填写错误");
                }
            }
            //工序
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getStepCode())) {
                combination.add(Constants.STEP_ELEMENT);
                Optional<Step> step = stepRepository.findByCode(stepWarningStandardImportDTO.getStepCode());
                if (step.isEmpty()) {
                    throw new ResponseException("error.NoStep", "没有该工序");
                }
                if(!step.get().getIsEnable()){
                    throw new ResponseException("error.stepDisabled", "工序("+step.get().getCode()+")已禁用");
                }
                step.ifPresent(stepWarningStandard::setStep);
            }
            //工序组
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getStepGroupCode())) {
                combination.add(Constants.STEP_GROUP_ELEMENT);
                Optional<StepGroup> stepGroup = stepGroupRepository.findByCodeAndDeleted(stepWarningStandardImportDTO.getStepGroupCode(), Constants.LONG_ZERO);
                if (!stepGroup.isPresent()) {
                    throw new ResponseException("error.NoStepGroup", "没有该工序组");
                }
                if(!stepGroup.get().getIsEnable()){
                    throw new ResponseException("error.stepGroupDisabled", "工序组("+stepGroup.get().getCode()+")已禁用");
                }
                stepGroup.ifPresent(stepWarningStandard::setStepGroup);
            }
            //客户
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getClientCode())) {
                combination.add(Constants.CLIENT_ELEMENT);
                ClientDTO clientDTO = rbaseClientProxy.findByCodeAndDeleted(stepWarningStandardImportDTO.getClientCode(),Constants.LONG_ZERO);
                if (null == clientDTO) {
                    throw new ResponseException("error.NoClient", "没有该客户");
                }
                if(!clientDTO.getEnable()){
                    throw new ResponseException("error.clientDisabled", "客户("+clientDTO.getCode()+")已禁用");
                }
                stepWarningStandard.setClientId(clientDTO.getId());
            }
            //备注
            if (StringUtils.isNotBlank(stepWarningStandardImportDTO.getNote())) {
                stepWarningStandard.setNote(stepWarningStandardImportDTO.getNote());
            }
            //根据组合条件集合判断优先级配置列表中是否有匹配的条件,如果匹配上则自动选择该优先级配置，如果无匹配则报错。
            PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_ZERO,combination);
            if (null == priorityElementConfig) {
                throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
            }

            stepWarningStandard.setPriorityElementConfig(priorityElementConfig);
            stepWarningStandard.setBaseNumber(stepWarningStandardImportDTO.getBaseNumber()).setWaringRate(stepWarningStandardImportDTO.getWaringRate()).setStopRate(stepWarningStandardImportDTO.getStopRate());
            //校验数据库内是否已有重复预警规则
            checkWarningStandard(stepWarningStandard);
            stepWarningStandardList.add(stepWarningStandard);
        }

        stepWarningStandardRepository.saveAll(stepWarningStandardList);
        return new BaseClientDTO(Constants.OK);
    }
}
