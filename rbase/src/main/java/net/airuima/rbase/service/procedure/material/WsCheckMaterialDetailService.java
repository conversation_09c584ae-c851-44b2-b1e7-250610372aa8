package net.airuima.rbase.service.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料明细表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsCheckMaterialDetailService extends CommonJpaService<WsCheckMaterialDetail> {
    private static final String WS_CHECK_MATERIAL_DETAIL_ENTITY_GRAPH = "wsCheckMaterialDetailEntityGraph";
    private final WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    private final WsMaterialBatchRepository wsMaterialBatchRepository;

    private final WsCheckMaterialRepository wsCheckMaterialRepository;

    @Autowired
    private PIWipLedgerService wipLedgerServices;

    public WsCheckMaterialDetailService(WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository, WsCheckMaterialRepository wsCheckMaterialRepository, WsMaterialBatchRepository wsMaterialBatchRepository) {
        this.wsCheckMaterialDetailRepository = wsCheckMaterialDetailRepository;
        this.wsCheckMaterialRepository = wsCheckMaterialRepository;
        this.wsMaterialBatchRepository = wsMaterialBatchRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsCheckMaterialDetail> find(Specification<WsCheckMaterialDetail> spec, Pageable pageable) {
        return wsCheckMaterialDetailRepository.findAll(spec, pageable,new NamedEntityGraph(WS_CHECK_MATERIAL_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsCheckMaterialDetail> find(Specification<WsCheckMaterialDetail> spec) {
        return wsCheckMaterialDetailRepository.findAll(spec,new NamedEntityGraph(WS_CHECK_MATERIAL_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsCheckMaterialDetail> findAll(Pageable pageable) {
        return wsCheckMaterialDetailRepository.findAll(pageable,new NamedEntityGraph(WS_CHECK_MATERIAL_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 通过凭证号获取待核料明细信息
     *
     * @param checkMaterialCode 凭证号
     * @return 核料明细
     */
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsCheckMaterialDetail> findByDetailByCode(String checkMaterialCode) {
        return wsCheckMaterialDetailRepository.findByWsCheckMaterialCodeAndStatus(checkMaterialCode);
    }

    /**
     * 通过凭证记录ID获取待核料明细信息
     * <AUTHOR>
     * @param wsCheckMaterialId     凭证记录ID
     * @return List<WsCheckMaterialDetail>
     * @date 2021-05-23
     **/
    @Transactional(readOnly = true)
    public List<WsCheckMaterialDetail> findByWsCheckMaterialId(Long wsCheckMaterialId){
        return wsCheckMaterialDetailRepository.findByWsCheckMaterialIdAndDeleted(wsCheckMaterialId,Constants.LONG_ZERO);
    }

}
