package net.airuima.rbase.service.base.wearingpart;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;

@FuncDefault
public interface IWearingPartBaseService {

    /**
     * 修改易损件状态
     *
     * @param id     易损件主键ID
     * @param status 易损件状态
     * <AUTHOR>
     * @date 2023/3/16
     **/
    @FuncInterceptor(value = "WearingPart")
    default void updateStatus(Long id, Integer status) {
    }
}
