package net.airuima.rbase.service.base.pedigree;

import net.airuima.config.annotation.InterfaceNote;
import net.airuima.config.annotation.MethodNote;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.service.CommonJpaInterface;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@InterfaceNote("产品谱系属性服务")
public interface PedigreeConfigInterface extends CommonJpaInterface<PedigreeConfig> {

    /**
     * 通过产品谱系主键ID查询产品谱系配置
     * @param pedigreeId 产品谱系主键ID
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeConfig 产品谱系配置
     */
    @MethodNote(value = "通过产品谱系id查询产品谱系属性", params = {"产品谱系id"}, returnParam = "产品谱系属性")
    default PedigreeConfig findByPedigreeId(Long pedigreeId) {
        return null;
    }

    /**
     * 通过产品谱系名称或者编码和查询数量是否启用是否复用sn查找产品谱系列表
     * @param text 产品谱系名称或者编码
     * @param size 询数量
     * @param isEnable 是否启用
     * @param isReuseSn 是否复用sn
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @MethodNote(value = "通过产品谱系名称或编码及是否复用SN获取谱系列表", params = {"谱系名称或编码", "查询数量", "是否启用", "是否复用SN"}, returnParam = "产品谱系集合")
    default List<Pedigree> findByPedigreeCodeOrPedigreeNameAndIsReuseSn(String text, Integer size, Boolean isEnable, Boolean isReuseSn) {
        return null;
    }

    /**
     * 启用/禁用指定产品谱系配置
     * @param pedigreeConfigId 产品谱系配置主键ID
     */
    @MethodNote(value = "启用/禁用指定产品谱系配置", params = {"产品谱系配置主键ID"})
    default void enableByPedigreeConfigId(Long pedigreeConfigId) {
    }

    /**
     * 通过产品谱系编码列表获取产品谱系配置列表
     * @param pedigreeCodeList 产品谱系编码列表
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeConfig> 产品谱系配置列表
     */
    @MethodNote(value = "通过产品谱系编码列表获取产品谱系配置列表", params = {"产品谱系编码列表"}, returnParam = "产品谱系配置列表")
    default List<PedigreeConfig> findByPedigreeCodeIn(List<String> pedigreeCodeList){return Lists.newArrayList();};
}
