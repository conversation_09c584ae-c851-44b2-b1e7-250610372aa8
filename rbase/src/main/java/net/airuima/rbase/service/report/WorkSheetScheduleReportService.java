package net.airuima.rbase.service.report;

import io.micrometer.core.instrument.util.StringUtils;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.aps.InProcessScheduleReportDTO;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.report.dto.InProcessWsScheduleReportRequestDTO;
import net.airuima.rbase.web.rest.report.dto.InProcessWsScheduleReportResultDTO;
import net.airuima.rbase.web.rest.report.dto.InProgressFinishedWorkOrderResultDTO;
import net.airuima.rbase.web.rest.report.dto.WorkOrderDTO;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/26
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetScheduleReportService {


    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private CommonService commonService;
    /**
     * 根据投产工单进行在制工单进度查询
     *
     * @param inProcessWsScheduleReportRequestDto 请求参数
     * @return 在制工单进度报表结果
     */
    public InProcessWsScheduleReportResultDTO inProcessSchedule(InProcessWsScheduleReportRequestDTO inProcessWsScheduleReportRequestDto) {
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode){
            return  inProcessScheduleBySubWorkSheet(inProcessWsScheduleReportRequestDto);
        }else {
            return inProcessScheduleByWorkSheet(inProcessWsScheduleReportRequestDto);
        }

    }

    /**
     * 根据子工单进行在制工单进度查询
     *
     * @param inProcessWsScheduleReportRequestDto 请求参数
     * @return 在制工单进度报表结果
     */
    @Transactional(readOnly = true)
    public InProcessWsScheduleReportResultDTO inProcessScheduleBySubWorkSheet(InProcessWsScheduleReportRequestDTO inProcessWsScheduleReportRequestDto){

        Page<SubWorkSheet> subWorkSheetPage = subWorkSheetRepository.findByWorkLineIdAndStatusLessThanOrderByPlanEndDateAsc(inProcessWsScheduleReportRequestDto.getWorkLineId(),
                 Constants.LONG_ZERO, PageRequest.of(inProcessWsScheduleReportRequestDto.getCurrentPage(), inProcessWsScheduleReportRequestDto.getPageSize()));

        List<SubWorkSheet> subWorkSheetList = Optional.ofNullable(subWorkSheetPage).map(Slice::getContent).orElse(null);

        if (CollectionUtils.isEmpty(subWorkSheetList)){
            return null;
        }
        List<InProcessScheduleReportDTO> inProcessScheduleReportDtos = staffPerformRepository.findInProcessScheduleBySubWorkSheet(subWorkSheetList.stream().map(SubWorkSheet::getId).collect(Collectors.toList()));

        //添加分页配置
        InProcessWsScheduleReportResultDTO inProcessWsScheduleReportResultDto = new InProcessWsScheduleReportResultDTO();
        inProcessWsScheduleReportResultDto.setCurrentPage(subWorkSheetPage.getNumber());
        inProcessWsScheduleReportResultDto.setPageSize(subWorkSheetPage.getSize());
        inProcessWsScheduleReportResultDto.setCountSize(subWorkSheetPage.getTotalElements());

        //添加工位表头
        List<WorkCell> workCells = workCellRepository.findByWorkLineIdAndDeletedOrderByOrderNumberAsc(inProcessWsScheduleReportRequestDto.getWorkLineId(), Constants.LONG_ZERO);
        List<InProcessWsScheduleReportResultDTO.WorkCellDTO> workCellDtos = null;
        if (ValidateUtils.isValid(workCells)){
            workCellDtos = workCells.stream().map(workCell -> new InProcessWsScheduleReportResultDTO.WorkCellDTO().setWorkCellCode(workCell.getCode()).setWorkCellName(workCell.getName()).setOrderNumber(workCell.getOrderNumber()))
                    .distinct().toList();
        }
        if(!CollectionUtils.isEmpty(workCellDtos)) {
            inProcessWsScheduleReportResultDto.setWorkCellList(workCellDtos.stream().sorted(Comparator.comparing(InProcessWsScheduleReportResultDTO.WorkCellDTO::getOrderNumber)).collect(Collectors.toList()));
        }
        //添加子工单对应工位工序
        if (!CollectionUtils.isEmpty(inProcessScheduleReportDtos)){
            List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = processWsStep(subWorkSheetList, null, inProcessScheduleReportDtos, Boolean.TRUE);
            inProcessWsScheduleReportResultDto.setInProcessWsDtoList(inProcessWsDTOList);
        }

        //已存在投产部分工单列表，补充工位对应工序（待做）
        if (ValidateUtils.isValid(inProcessWsScheduleReportResultDto.getInProcessWsDtoList())) {
            List<String> serialNumbersExist = inProcessWsScheduleReportResultDto.getInProcessWsDtoList().stream().map(InProcessWsScheduleReportResultDTO.InProcessWsDTO::getSerialNumber).collect(Collectors.toList());
            List<SubWorkSheet> subWorkSheetsExist = subWorkSheetList.stream().filter(subWorkSheet -> serialNumbersExist.stream().noneMatch(s -> s.equals(subWorkSheet.getSerialNumber())))
                    .toList();
            if (ValidateUtils.isValid(subWorkSheetsExist)){
                List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = subWorkSheetsExist.stream().map(subWorkSheet -> {
                    InProcessWsScheduleReportResultDTO.InProcessWsDTO inProcessWsDto = new InProcessWsScheduleReportResultDTO.InProcessWsDTO(subWorkSheet);
                    //添加工单中 工位对应工序列表
                    return inProcessWsDto.setWorkCellStepDtoList(addWorkCellStepDTOList(subWorkSheet.getWorkSheet().getId()));
                }).toList();
                inProcessWsScheduleReportResultDto.getInProcessWsDtoList().addAll(inProcessWsDTOList);
            }
        }else {
            //已下单得工单状态（全部 未做），并不会产生生成数据所以需要额外添加
            List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = subWorkSheetList.stream().map(subWorkSheet -> {
                InProcessWsScheduleReportResultDTO.InProcessWsDTO inProcessWsDto = new InProcessWsScheduleReportResultDTO.InProcessWsDTO(subWorkSheet);
                return inProcessWsDto.setWorkCellStepDtoList(addWorkCellStepDTOList(subWorkSheet.getWorkSheet().getId()));
            }).collect(Collectors.toList());
            inProcessWsScheduleReportResultDto.setInProcessWsDtoList(inProcessWsDTOList);
        }
        return inProcessWsScheduleReportResultDto;
    }

    /**
     * 根据工单进行在制工单进度查询
     *
     * @param inProcessWsScheduleReportRequestDto 请求参数
     * @return 在制工单进度报表结果
     */
    @Transactional(readOnly = true)
    public InProcessWsScheduleReportResultDTO inProcessScheduleByWorkSheet(InProcessWsScheduleReportRequestDTO inProcessWsScheduleReportRequestDto){

        Page<WorkSheet> workSheetPage = workSheetRepository.findByWorkLineIdOrderByPlanEndDateAsc(inProcessWsScheduleReportRequestDto.getWorkLineId(),
                 Constants.LONG_ZERO, PageRequest.of(inProcessWsScheduleReportRequestDto.getCurrentPage(), inProcessWsScheduleReportRequestDto.getPageSize()));

        List<WorkSheet> workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(null);

        if (!ValidateUtils.isValid(workSheetList)){
            return null;
        }
        List<InProcessScheduleReportDTO> inProcessScheduleReportDtos = staffPerformRepository.findInProcessScheduleByWorkSheet(workSheetList.stream().map(WorkSheet::getId).collect(Collectors.toList()));

        InProcessWsScheduleReportResultDTO inProcessWsScheduleReportResultDto = new InProcessWsScheduleReportResultDTO();
        inProcessWsScheduleReportResultDto.setCurrentPage(workSheetPage.getNumber());
        inProcessWsScheduleReportResultDto.setPageSize(workSheetPage.getSize());
        inProcessWsScheduleReportResultDto.setCountSize(workSheetPage.getTotalElements());

        //添加工位表头
        List<WorkCell> workCells = workCellRepository.findByWorkLineIdAndDeletedOrderByOrderNumberAsc(inProcessWsScheduleReportRequestDto.getWorkLineId(), Constants.LONG_ZERO);
        List<InProcessWsScheduleReportResultDTO.WorkCellDTO> workCellDtos = null;
        if (ValidateUtils.isValid(workCells)){
            workCellDtos = workCells.stream().map(workCell -> new InProcessWsScheduleReportResultDTO.WorkCellDTO().setWorkCellCode(workCell.getCode()).setWorkCellName(workCell.getName()).setOrderNumber(workCell.getOrderNumber()))
                    .distinct().toList();
        }
        if(!CollectionUtils.isEmpty(workCellDtos)) {
            inProcessWsScheduleReportResultDto.setWorkCellList(workCellDtos.stream().sorted(Comparator.comparing(InProcessWsScheduleReportResultDTO.WorkCellDTO::getOrderNumber)).collect(Collectors.toList()));
        }

        //添加子工单对应工位工序
        if (!CollectionUtils.isEmpty(inProcessScheduleReportDtos)){
            List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = processWsStep(null, workSheetList, inProcessScheduleReportDtos, Boolean.FALSE);
            inProcessWsScheduleReportResultDto.setInProcessWsDtoList(inProcessWsDTOList);
        }

        if (ValidateUtils.isValid(inProcessWsScheduleReportResultDto.getInProcessWsDtoList())) {
            List<String> serialNumbersExist = inProcessWsScheduleReportResultDto.getInProcessWsDtoList().stream().map(InProcessWsScheduleReportResultDTO.InProcessWsDTO::getSerialNumber).toList();
            List<WorkSheet> workSheetsExist = workSheetList.stream().filter(ws -> serialNumbersExist.stream().noneMatch(s -> s.equals(ws.getSerialNumber())))
                    .toList();

            if (ValidateUtils.isValid(workSheetsExist)){
                List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = workSheetsExist.stream().map(ws -> {
                    InProcessWsScheduleReportResultDTO.InProcessWsDTO inProcessWsDto = new InProcessWsScheduleReportResultDTO.InProcessWsDTO(ws);
                    //添加工单中 工位对应工序列表
                    return inProcessWsDto.setWorkCellStepDtoList(addWorkCellStepDTOList(ws.getId()));
                }).toList();
                inProcessWsScheduleReportResultDto.getInProcessWsDtoList().addAll(inProcessWsDTOList);
            }
        }else {
            //已下单得工单状态，并不会产生生成数据所以需要额外添加
            List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDTOList = workSheetList.stream().map(ws -> {
                InProcessWsScheduleReportResultDTO.InProcessWsDTO inProcessWsDto = new InProcessWsScheduleReportResultDTO.InProcessWsDTO(ws);
                return inProcessWsDto.setWorkCellStepDtoList(addWorkCellStepDTOList(ws.getId()));
            }).collect(Collectors.toList());
            inProcessWsScheduleReportResultDto.setInProcessWsDtoList(inProcessWsDTOList);
        }
        return inProcessWsScheduleReportResultDto;
    }


    /**
     *  添加 （子）工单 投产 工位工序信息
     * @param subWorkSheetList 子工单列表
     * @param workSheetList 工单列表
     * @param inProcessScheduleReportDtos 员工产能数据
     * @param isSubWorksheet  是否子工单投产
     * <AUTHOR>
     * @date  2023/7/3
     */
    public List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> processWsStep(List<SubWorkSheet> subWorkSheetList, List<WorkSheet> workSheetList,List<InProcessScheduleReportDTO> inProcessScheduleReportDtos, boolean isSubWorksheet){
        //已投产工单&或者已暂停工单
        List<InProcessWsScheduleReportResultDTO.InProcessWsDTO> inProcessWsDtoList = Lists.newArrayList();

        inProcessScheduleReportDtos.stream().collect(Collectors.groupingBy(InProcessScheduleReportDTO::getSerialNumber))
                .forEach((serialNumber,reports) -> {

                    WorkSheet workSheet;
                    SubWorkSheet subWorkSheet = null;
                    boolean isOverdue;
                    if (isSubWorksheet){
                        subWorkSheet = subWorkSheetList.stream().filter(sub -> sub.getSerialNumber().equals(serialNumber)).findFirst().orElse(null);
                        if (ObjectUtils.isEmpty(subWorkSheet)){
                            return;
                        }
                        isOverdue = subWorkSheet.getWorkSheet().getIsOverdue();
                        workSheet = subWorkSheet.getWorkSheet();
                    }else {
                        workSheet = workSheetList.stream().filter(ws -> ws.getSerialNumber().equals(serialNumber)).findFirst().orElse(null);
                        if (ObjectUtils.isEmpty(workSheet)){
                            return;
                        }
                        isOverdue = workSheet.getIsOverdue();
                    }
                    if (!ValidateUtils.isValid(reports)){
                        return;
                    }
                    InProcessWsScheduleReportResultDTO.InProcessWsDTO inProcessWsDto = MapperUtils.map(reports.get(Constants.INT_ZERO), InProcessWsScheduleReportResultDTO.InProcessWsDTO.class);
                    //添加工单状态
                    if (isOverdue){
                        inProcessWsDto.setStatus(Constants.APPROVE);
                    }else {
                        inProcessWsDto = ObjectUtils.isNotEmpty(subWorkSheet) ? inProcessWsDto.setStatus(subWorkSheet.getStatus()) : inProcessWsDto.setStatus(workSheet.getStatus());
                    }

                    //添加已产生的工位工序数据
                    List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO>  workCellStepDtoList = Lists.newArrayList();
                    //根据工位进行分组，一个工位对应多个工序并记录工序投产以及完成状态
                    reports.stream().collect(Collectors.groupingBy(InProcessScheduleReportDTO::getWorkCellCode))
                            .forEach((workCellCode,reportList) -> {
                                InProcessWsScheduleReportResultDTO.WorkCellStepDTO workCellStepDto = new InProcessWsScheduleReportResultDTO.WorkCellStepDTO();
                                workCellStepDto.setWorkCellCode(reportList.get(Constants.INT_ZERO).getWorkCellCode()).setWorkCellName(reportList.get(Constants.INT_ZERO).getWorkCellName());
                                //添加工位对应工序信息详情
                                List<InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO> workCellStepInputNumberDtoList =
                                        reportList.stream().map(InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO::new).collect(Collectors.toList());
                                workCellStepDto.setWorkCellStepInputNumberDtoList(workCellStepInputNumberDtoList);
                                //添加工位工序对应关系
                                workCellStepDtoList.add(workCellStepDto);
                            });
                    //添加 未开工的工序
                    List<String> processSteps = reports.stream().map(InProcessScheduleReportDTO::getStepCode).distinct().collect(Collectors.toList());

                    List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
                    List<Step> steps = getStepsNotInProcessSteps(wsSteps, processSteps);
                    if (ValidateUtils.isValid(steps)){
                        steps.forEach(step -> processStep(step, workCellStepDtoList));
                    }
                    //添加工单中 工位对应工序列表
                    inProcessWsDto.setWorkCellStepDtoList(workCellStepDtoList);
                    //添加当前条工单信息
                    inProcessWsDtoList.add(inProcessWsDto);
                });
        return inProcessWsDtoList;
    }

    /**
     * 添加未投产 工位 对应的 工序信息
     * @param procedureId 投产工单id
     * <AUTHOR>
     * @date  2023/7/3
     */
    public List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO> addWorkCellStepDTOList(Long procedureId){
        List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(procedureId, Constants.LONG_ZERO);
        List<Step> steps = getStepsNotInProcessSteps(wsSteps, null);
        List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO> workCellStepDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(steps)){
            List<WorkCellStep> workCellSteps = workCellStepRepository.findByStepIdInAndDeletedAndIsEnable(steps.stream().map(Step::getId).collect(Collectors.toList()), Constants.LONG_ZERO, Boolean.TRUE);
            if (ValidateUtils.isValid(workCellSteps)){
                workCellSteps.stream().collect(Collectors.groupingBy(WorkCellStep::getWorkCell))
                        .forEach((workCell,workCellStepList) -> {
                            InProcessWsScheduleReportResultDTO.WorkCellStepDTO workCellStepDto = new InProcessWsScheduleReportResultDTO.WorkCellStepDTO();
                            workCellStepDto.setWorkCellName(workCell.getName()).setWorkCellCode(workCell.getCode());
                            List<InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO> workCellStepInputNumberDtoList = workCellStepList.stream().map(InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO::new).collect(Collectors.toList());
                            workCellStepDto.setWorkCellStepInputNumberDtoList(workCellStepInputNumberDtoList);
                            workCellStepDtoList.add(workCellStepDto);
                        });
            }
        }
        //添加工单中 工位对应工序列表
        return workCellStepDtoList;
    }


    /**
     * 获取工序快照中 未投产的工序信息
     * @param wsSteps 工序快照
     * @param processSteps 投产工序编码
     * <AUTHOR>
     * @date  2023/7/3
     */
    private List<Step> getStepsNotInProcessSteps(List<WsStep> wsSteps, List<String> processSteps) {
        if (!ValidateUtils.isValid(processSteps)){
            return wsSteps.stream()
                    .map(WsStep::getStep)
                    .collect(Collectors.toList());
        }else {
            return wsSteps.stream()
                    .map(WsStep::getStep)
                    .filter(step -> processSteps.stream().noneMatch(code -> code.equals(step.getCode())))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 获取 工序 对应的  工位信息
      * @param step 工序信息
     * @param workCellStepDtoList 工位对应的工序列表
     * <AUTHOR>
     * @date  2023/7/3
     * @return void
     */
    private void processStep(Step step, List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO> workCellStepDtoList) {
        List<WorkCell> workCells = workCellStepRepository.findByStepIdAndDeletedAndIsEnable(step.getId(), Boolean.TRUE);
        if (!ValidateUtils.isValid(workCells)) return;

        addExistingWorkCells(workCells, step, workCellStepDtoList);
        addNonExistingWorkCells(workCells, step, workCellStepDtoList);
    }

    /**
     * 存在（工序对应的 工位信息） 获取 工序对应的 工位信息
     * @param workCells 工位列表
     * @param step 工序信息
     * @param workCellStepDtoList  工序对应的 工位信息
     * <AUTHOR>
     * @date  2023/7/3
     * @return void
     */
    private void addExistingWorkCells(List<WorkCell> workCells, Step step, List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO> workCellStepDtoList) {
        workCellStepDtoList.forEach(workCellStepDTO -> {
            if (workCells.stream().anyMatch(workCell -> workCell.getCode().equals(workCellStepDTO.getWorkCellCode()))) {
                List<InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO> workCellStepInputNumberDtoList = new ArrayList<>(workCellStepDTO.getWorkCellStepInputNumberDtoList());
                workCellStepInputNumberDtoList.add(new InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO().setStepCode(step.getCode()).setStepName(step.getName()).setInputNumber(Constants.LONG_ZERO).setIsFinish(0));
                workCellStepDTO.setWorkCellStepInputNumberDtoList(workCellStepInputNumberDtoList);
            }
        });
    }

    /**
     * 不存在 （工序对应的 工位信息） 获取 工序对应的 工位信息
     * @param workCells 工位列表
     * @param step 工序信息
     * @param workCellStepDtoList  工序对应的 工位信息
     * <AUTHOR>
     * @date  2023/7/3
     * @return void
     */
    private void addNonExistingWorkCells(List<WorkCell> workCells, Step step, List<InProcessWsScheduleReportResultDTO.WorkCellStepDTO> workCellStepDtoList) {
        List<WorkCell> noWorkCells = workCells.stream().filter(workCell -> workCellStepDtoList.stream().noneMatch(workCellStepDTO -> workCell.getCode().equals(workCellStepDTO.getWorkCellCode()))).collect(Collectors.toList());
        if (!ValidateUtils.isValid(noWorkCells)) return;
        noWorkCells.forEach(noWorkCell -> {
            InProcessWsScheduleReportResultDTO.WorkCellStepDTO workCellStepDto = new InProcessWsScheduleReportResultDTO.WorkCellStepDTO();
            workCellStepDto.setWorkCellName(noWorkCell.getName()).setWorkCellCode(noWorkCell.getCode());
            workCellStepDto.setWorkCellStepInputNumberDtoList(Collections.singletonList(new InProcessWsScheduleReportResultDTO.WorkCellStepInputNumberDTO().setStepCode(step.getCode()).setStepName(step.getName()).setInputNumber(Constants.LONG_ZERO).setIsFinish(0)));
            workCellStepDtoList.add(workCellStepDto);
        });
    }

    /**
     * 今日已完成工单信息
     * @param workLineId 生产线
     * @param currentPage 当前页数
     * @param pageSize  每页显示数
     * <AUTHOR>
     * @date  2023/6/26
     * @return void
     */
    public InProgressFinishedWorkOrderResultDTO inProgressFinishedWorkOrder(Long workLineId,Integer currentPage,Integer pageSize){

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        InProgressFinishedWorkOrderResultDTO inProgressFinishedWorkOrderResultDto = new InProgressFinishedWorkOrderResultDTO();

        Page<WorkOrderDTO> inProgressFinishedWorkOrderResultDTOPage = subWsProductionMode ?
                subWorkSheetRepository.findByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), PageRequest.of(currentPage, pageSize)):
                workSheetRepository.findByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), PageRequest.of(currentPage, pageSize));
        List<WorkOrderDTO> workOrderDTOList = Optional.ofNullable(inProgressFinishedWorkOrderResultDTOPage).map(Slice::getContent).orElse(null);
        if (!ObjectUtils.isEmpty(workOrderDTOList)){
            inProgressFinishedWorkOrderResultDto.setWorkOrderDtoList(workOrderDTOList);
            inProgressFinishedWorkOrderResultDto.setCurrentPage(inProgressFinishedWorkOrderResultDTOPage.getNumber());
            inProgressFinishedWorkOrderResultDto.setPageSize(inProgressFinishedWorkOrderResultDTOPage.getSize());
            inProgressFinishedWorkOrderResultDto.setCountSize(inProgressFinishedWorkOrderResultDTOPage.getTotalElements());
        }
        return inProgressFinishedWorkOrderResultDto;
    }


    /**
     * 导出进行中和已完成的工单列表
     * @param workLineId 工作线id
     * @return 工单列表
     */
    public List<WorkOrderDTO> exportInProgressFinishedWorkOrder(Long workLineId) {

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        Long count = subWsProductionMode ? subWorkSheetRepository.countByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX)):
                workSheetRepository.countByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX));

        List<WorkOrderDTO> workOrderDTOList = Lists.newArrayList();

        if (!ObjectUtils.isEmpty(count) && count > Constants.INT_ZERO){

            for (int currentPage = 0; count > 0; currentPage++) {
                Page<WorkOrderDTO> inProgressFinishedWorkOrderResultDTOPage = subWsProductionMode ?
                        subWorkSheetRepository.findByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), PageRequest.of(currentPage, Constants.EXPORTNUMBER)):
                        workSheetRepository.findByWorkLineAndStatusAndDateNow(workLineId, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(LocalDate.now(), LocalTime.MAX), PageRequest.of(currentPage, Constants.EXPORTNUMBER));
                List<WorkOrderDTO> workOrderDTOs = Optional.ofNullable(inProgressFinishedWorkOrderResultDTOPage).map(Slice::getContent).orElse(null);

                if (ValidateUtils.isValid(workOrderDTOs)){
                    workOrderDTOList.addAll(workOrderDTOs);
                }
                count = count - (long) currentPage *Constants.EXPORTNUMBER + Constants.EXPORTNUMBER;
            }
        }
        return workOrderDTOList;
    }
}
