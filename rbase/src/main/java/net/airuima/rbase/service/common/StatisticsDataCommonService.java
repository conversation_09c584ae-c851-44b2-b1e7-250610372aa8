package net.airuima.rbase.service.common;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.OperationEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.proxy.downgrade.RbaseDownGradeProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 统计数据通用Service
 *
 * <AUTHOR>
 */
@Service
public class StatisticsDataCommonService {
    @Autowired
    private IProductionPlanService[] productionPlanServices;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private RbaseDownGradeProxy rbaseDownGradeProxy;

    /**
     * 更新生产计划统计数据
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     * @param number 数量
     * @param operationEnum 更新类型(加或减)
     */
    public void updateProductionPlanStatistics(SubWorkSheet subWorkSheet, WorkSheet workSheet, int number, OperationEnum operationEnum){
        // 产品谱系id
        Long pedigreeId = null;
        Long workLineId = null;
        if (Objects.nonNull(subWorkSheet)) {
            pedigreeId = subWorkSheet.getWorkSheet().getPedigree().getId();
            workLineId = Objects.nonNull(subWorkSheet.getWorkLine()) ? subWorkSheet.getWorkLine().getId() : null;
        } else if (Objects.nonNull(workSheet)) {
            pedigreeId = workSheet.getPedigree().getId();
            workLineId = Objects.nonNull(workSheet.getWorkLine()) ? workSheet.getWorkLine().getId() : null;
        }
        if(Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)){
            productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, number, operationEnum);
        }
    }

    /**
     * 更新工序组生产计划数据
     *
     * @param subWorkSheet  子工单
     * @param workSheet     工单
     * @param step          工序
     * @param number        变更数量
     * @param operationEnum 增加还是减少
     */
    public void updateStepGroupProductionPlanInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, int number, OperationEnum operationEnum) {
        // 产品谱系id
        Long pedigreeId = null;
        Long workLineId = null;
        if (Objects.nonNull(subWorkSheet)) {
            pedigreeId = subWorkSheet.getWorkSheet().getPedigree().getId();
            workLineId = Objects.nonNull(subWorkSheet.getWorkLine()) ? subWorkSheet.getWorkLine().getId() : null;
        } else if (Objects.nonNull(workSheet)) {
            pedigreeId = workSheet.getPedigree().getId();
            workLineId = Objects.nonNull(workSheet.getWorkLine()) ? workSheet.getWorkLine().getId() : null;
        }
        //工序组id
        Long stepGroupId = Objects.nonNull(step.getStepGroup()) ? step.getStepGroup().getId() : null;
        // 更新生产计划 工序组计划粒度
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)) {
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId, number, operationEnum);
        }
    }

    /**
     * 更新在制看板数据
     *
     * @param subWorkSheet      子工单
     * @param workSheet         工单
     * @param step              工序
     * @param inputNumber       投产数
     * @param qualifiedNumber   合格数
     * @param unqualifiedNumber 不合格数
     * @param transferNumber    流转数
     */
    public void updateWorkSheetStepStatisticsInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, int inputNumber, int qualifiedNumber, int unqualifiedNumber, int transferNumber) {
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        List<WsStep> wsStepList = null;
        if (Objects.nonNull(subWorkSheet)) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsInfo(workSheet, subWorkSheet, step, wsStepList, inputNumber, qualifiedNumber, unqualifiedNumber, transferNumber);
    }

    /**
     * 更新子工单、工单及返工单合格数统计数据
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     * @param qualifiedNumber 合格数
     * @param unqualifiedNumber 不合格数
     */
    public void updateWorkSheetCompleteInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, int qualifiedNumber, int unqualifiedNumber,int reWorkQualifiedNumber,boolean singleSnOnlineRepair) {
        if (Objects.nonNull(subWorkSheet)) {
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - subWorkSheet.getUnqualifiedNumber());
            subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() + qualifiedNumber)
                    .setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + unqualifiedNumber);
            subWorkSheetRepository.save(subWorkSheet);
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + subWorkSheet.getUnqualifiedNumber());
            workSheetRepository.save(workSheet);
            if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory() && (subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber()) == subWorkSheet.getNumber()) {
                //更新在制看板数据
                workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), Boolean.TRUE);
            }
        } else {
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + qualifiedNumber)
                    .setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + unqualifiedNumber);
            if(singleSnOnlineRepair){
                workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() + reWorkQualifiedNumber);
            }
            workSheetRepository.save(workSheet);
            if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory() && (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber()) == workSheet.getNumber() && !singleSnOnlineRepair) {
                //更新在制看板数据
                workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, null, Boolean.FALSE);
            }
        }
        if (workSheet.getCategory() != ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()) {
            return;
        }
        //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
        if(workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()){
            rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(workSheet);
        }
        Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        wsReworkOptional.ifPresent(wsRework -> {
            WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
            originWorkSheet.setReworkQualifiedNumber(originWorkSheet.getReworkQualifiedNumber() + reWorkQualifiedNumber);
            originWorkSheet.setUnqualifiedNumber(originWorkSheet.getUnqualifiedNumber() - reWorkQualifiedNumber);
            originWorkSheet.setQualifiedNumber(originWorkSheet.getQualifiedNumber() + reWorkQualifiedNumber);
            workSheetRepository.save(originWorkSheet);
            //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
            if(originWorkSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()){
                rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(originWorkSheet);
            }
        });
    }
}
