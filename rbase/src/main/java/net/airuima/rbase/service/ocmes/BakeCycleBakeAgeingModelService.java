package net.airuima.rbase.service.ocmes;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO;
import net.airuima.rbase.dto.client.ClientGetSnStepInfoDTO;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;
import net.airuima.rbase.dto.ocmes.*;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
@FuncInterceptor(value = "Bake||CycleBake||Ageing")
public interface BakeCycleBakeAgeingModelService {

    /**
     * 删除烘烤温循老化历史记录
     * @param workSheet 工单
     * @param subWorkSheet 子工单
     * @param step 工序
     * @param containerCodes 容器编码列表
     * @param sn sn编码
     */
    default void logicDeletedBakeCycleBakeAgeingHistory(WorkSheet workSheet, SubWorkSheet subWorkSheet, WsStep currWsStep, List<String> containerCodes, String sn,Integer reworkTime){

    }

    /**
     * 替换已容器下交的烘烤记录的容器编号
     * @param ocReplaceContainerDto 替换容器参数
     */
    @FuncInterceptor(value = "Bake")
    default void replaceContainerBakeHistory(OcReplaceContainerDTO ocReplaceContainerDto){

    }


    /**
     * 替换已容器下交的温循记录的容器编号
     * @param ocReplaceContainerDto 替换容器参数
     */
    @FuncInterceptor(value = "CycleBake")
    default void replaceContainerCycleBakeHistory(OcReplaceContainerDTO ocReplaceContainerDto){

    }


    /**
     * 替换已容器下交的老化记录的容器编号
     * @param ocReplaceContainerDto 替换容器参数
     */
    @FuncInterceptor(value = "Ageing")
    default void replaceContainerAgeingHistory(OcReplaceContainerDTO ocReplaceContainerDto){

    }

    /**
     * 获取烘烤温循老化配置（批量）
     * @param clientGetStepInfoDto 待做工序信息
     * @return net.airuima.dto.client.ClientGetStepInfoDTO  工序返回信息
     */
    default ClientGetStepInfoDTO queryBakeCycleBakeAgeingConfig(ClientGetStepInfoDTO clientGetStepInfoDto){
        return clientGetStepInfoDto;
    }

    /**
     * 获取烘烤温循老化配置（单支）
     * @param clientGetSnStepInfoDto 待做工序信息
     * @return net.airuima.dto.client.ClientGetStepInfoDTO  工序返回信息(单支)
     */
    default ClientGetSnStepInfoDTO queryBakeCycleBakeAgeingConfig(ClientGetSnStepInfoDTO clientGetSnStepInfoDto){
        return clientGetSnStepInfoDto;
    }

    /**
     * 下交保存验证烘烤温循老化状态
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "Bake")
    default BaseClientDTO validateBakeStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 下交保存验证烘烤温循老化状态
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "CycleBake")
    default BaseClientDTO validateCycleBakeStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 下交保存验证烘烤温循老化状态
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "Ageing")
    default BaseClientDTO validateAgeingStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 保存烘烤历史记录
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "Bake")
    default BaseClientDTO saveBakeHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }


    /**
     * 保存温循历史记录
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存温循参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "CycleBake")
    default BaseClientDTO saveCycleBakeHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }



    /**
     * 保存老化历史记录
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "Ageing")
    default BaseClientDTO saveAgeingHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 替换烘烤历史对应的sn
     * @param snReplaceDtoList 替换详情列表
     * <AUTHOR>
     */
    @FuncInterceptor(value = "Bake")
    default void replaceBakeHistorySn(List<SnReplaceDTO> snReplaceDtoList){

    }

    /**
     * 替换温循历史对应的sn
     * @param snReplaceDtoList 替换详情列表
     * <AUTHOR>
     */
    @FuncInterceptor(value = "CycleBake")
    default void replaceCycleBakeHistorySn(List<SnReplaceDTO> snReplaceDtoList){

    }

    /**
     * 替换老化历史对应的sn
     * @param snReplaceDtoList 替换详情列表
     * <AUTHOR>
     */
    @FuncInterceptor(value = "Ageing")
    default void replaceAgeingHistorySn(List<SnReplaceDTO> snReplaceDtoList){

    }

    /**
     * 查询烘烤历史的放入时间参数
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId 子工单ID
     * @param category 工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  烘烤历史放入时间参数
     */
    @FuncInterceptor(value = "Bake")
    default BakeCycleBakeAgeingHistoryPutInDateDTO queryBakeHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        return null;
    }

    /**
     * 查询温循历史的放入时间参数
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId 子工单ID
     * @param category 工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  温循历史放入时间参数
     */
    @FuncInterceptor(value = "CycleBake")
    default BakeCycleBakeAgeingHistoryPutInDateDTO queryCycleBakeHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        return null;
    }

    /**
     * 查询老化历史的放入时间参数
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId 子工单ID
     * @param category 工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  老化历史放入时间参数
     */
    @FuncInterceptor(value = "Ageing")
    default BakeCycleBakeAgeingHistoryPutInDateDTO queryAgeingHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        return null;
    }

    /**
     * 获取烘烤温循老化配置
     * @param category 工序类型
     * @param step 工序信息
     * @param pedigree 产品谱系
     * <AUTHOR>
     * @date  2023/3/29
     * @return net.airuima.rmes.dto.ocmes.BakeCycleBakeAgeingConfigDTO 烘烤温循老化配置
     */
    default BakeCycleBakeAgeingConfigDTO getBakeCycleBakeAgeingConfigInfo(Integer category, Step step, Pedigree pedigree, Long workFlowId, Long clientId){
        return null;
    }

    /**
     * 验证烘烤温循老化工序的合法性
     * @param  bakeCycleBakeAgeingValidDto 验证烘烤温循老化请求取出参数
     * <AUTHOR>
     * @date  2023/3/29
     */
    default void validBakeCycleBakeAgeingHistory(BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDto, WsStep wsStep){
    }

    /**
     * 烘烤温循老化历史记录保存
     * @param bakeCycleBakeAgeingSaveRequestDto 烘烤温循老化请求保存参数
     * <AUTHOR>
     * @date  2023/3/30
     */
    default void saveBakeCycleBakeAgeingHistoryInfo(BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDto){

    }

    /**
     *  获取工单待做烘烤温循老化工序信息
     * @param rworkerBatchToDoStepGetDTO 批量待做工序信息
     * @param batchStepSaveBaseInfo 请求工序基础信息
     */
    default void bakeCycleBakeAgeingBatchInfo(RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo){}

    /**
     * 获取容器待做烘烤温循老化工序信息
     * @param containerNextToDoStepInfo 容器待做工序信息
     * @param stepProcessBaseDTO 请求工序基础信息
     */
    default void bakeCycleBakeAgeingContainerInfo(RworkerContainerToDoStepGetDTO containerNextToDoStepInfo, RworkerStepProcessBaseDTO stepProcessBaseDTO){}


    /**
     *
     * 获取单支SN待做烘烤温循老化工序信息
     * @param sn 投产SN
     * @param rworkerSnToDoStepGetDTO SN待做工序信息
     * @param rworkerStepProcessBaseDTO 请求工序基础信息
     */
    default void bakeCycleBakeAgeingSnInfo(String sn, Integer snReworkTime,RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){};

}
