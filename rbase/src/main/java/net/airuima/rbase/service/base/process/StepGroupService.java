package net.airuima.rbase.service.base.process;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.process.dto.StepGroupCreateDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组别Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepGroupService extends CommonJpaService<StepGroup> {

    private final StepGroupRepository stepGroupRepository;

    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public StepGroupService(StepGroupRepository stepGroupRepository) {
        this.stepGroupRepository = stepGroupRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StepGroup> find(Specification<StepGroup> spec, Pageable pageable) {
        return stepGroupRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<StepGroup> find(Specification<StepGroup> spec) {
        return stepGroupRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StepGroup> findAll(Pageable pageable) {
        return stepGroupRepository.findAll(pageable);
    }

    /**
     * 获取所有未删除的工序组别
     * @param isEnable 是否启用
     * @return
     */
    public List<StepGroup> findAllNotDeleted(Boolean isEnable) {
        List<StepGroup> list = stepGroupRepository.findByDeleted(Constants.LONG_ZERO);
        if (ValidateUtils.isValid(list)) {
            return null != isEnable ? list.stream().filter(pedigree -> isEnable == pedigree.getIsEnable()).collect(Collectors.toList()) : list;
        }
        return list;
    }

    /**
     * 根据名称或编码模糊查询工序组
     * <AUTHOR>
     * @date 2022/11/1
     * @param text 工序组的名称或编码
     * @param size 行数
     * @return List<StepGroup>
     */
    @Transactional(readOnly = true)
    public List<StepGroup> findByCodeOrName(String text, Integer size) {
        return Optional.ofNullable(stepGroupRepository
                .findByNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 新增工序组
     *
     * @param entity 新增工序组DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.StepGroup>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public StepGroup create(StepGroupCreateDTO entity) {
        StepGroup stepGroup = new StepGroup();
        //如果未手动输入编码则按照编码规则自动生成，如果手动输入则判断唯一性
        if (StringUtils.isBlank(entity.getCode())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_STEP_GROUP_CODE, null, null);
            stepGroup.setCode(rbaseSerialNumberProxy.generate(serialNumberDto));
        } else {
            Optional<StepGroup> stepGroupOptional = stepGroupRepository.findByCodeAndDeleted(entity.getCode(), Constants.LONG_ZERO);
            if (stepGroupOptional.isPresent()) {
                throw new ResponseException("error.CodeIsExist", "工序组编码已存在");
            }
            stepGroup.setCode(entity.getCode());
        }
        stepGroup.setName(entity.getName()).setIsEnable(Boolean.TRUE);
        stepGroupRepository.save(stepGroup);
        return stepGroup;
    }

    /**
     * 启用/禁用指定工序组
     *
     * @param stepGroupId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByStepGroupId(Long stepGroupId) {
        StepGroup stepGroup = stepGroupRepository.findByIdAndDeleted(stepGroupId, Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.stepGroupNotExist", "工序组别不存在"));
        stepGroup.setIsEnable(!stepGroup.getIsEnable());
        stepGroupRepository.save(stepGroup);
    }
}
