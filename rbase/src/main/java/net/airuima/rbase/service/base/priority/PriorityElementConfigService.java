package net.airuima.rbase.service.base.priority;

import com.alibaba.fastjson.JSON;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepSpecificationRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemWarningStandardRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 条件优先级配置Service
 *
 * <AUTHOR>
 * @date 2022-10-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PriorityElementConfigService extends CommonJpaService<PriorityElementConfig> {

    private final PriorityElementConfigRepository priorityElementConfigRepository;
    private final StepWarningStandardRepository stepWarningStandardRepository;
    private final UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository;
    private final PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;

    private final PedigreeStepRepository pedigreeStepRepository;
    private final PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository;

    private final PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository;

    public PriorityElementConfigService(PriorityElementConfigRepository priorityElementConfigRepository, StepWarningStandardRepository stepWarningStandardRepository, UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository, PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository, PedigreeStepRepository pedigreeStepRepository, PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository, PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository) {
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.stepWarningStandardRepository = stepWarningStandardRepository;
        this.unqualifiedItemWarningStandardRepository = unqualifiedItemWarningStandardRepository;
        this.pedigreeStepCheckRuleRepository = pedigreeStepCheckRuleRepository;
        this.pedigreeStepRepository = pedigreeStepRepository;
        this.pedigreeStepUnqualifiedItemRepository = pedigreeStepUnqualifiedItemRepository;
        this.pedigreeStepSpecificationRepository = pedigreeStepSpecificationRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PriorityElementConfig> find(Specification<PriorityElementConfig> spec, Pageable pageable) {
        return priorityElementConfigRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PriorityElementConfig> find(Specification<PriorityElementConfig> spec) {
        return priorityElementConfigRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PriorityElementConfig> findAll(Pageable pageable) {
        return priorityElementConfigRepository.findAll(pageable);
    }

    /**
     * 新增条件优先级配置
     * <AUTHOR>
     * @date 2022/10/21
     * @param priorityElementConfig 条件优先级
     */
    public void saveInstance(PriorityElementConfig priorityElementConfig) {
        //如果组合条件是不良,校验是否选择不良其中一个
        if (Constants.WARNING_STANDARD_TARGET_UNQUALIFIED == priorityElementConfig.getTarget() && !(priorityElementConfig.getCombination().contains(Constants.UNQUALIFIED_GROUP) || priorityElementConfig.getCombination().contains(Constants.UNQUALIFIED_ITEM))) {
            throw new ResponseException("error.ChooseAtLeastOneUnqualified", "请添加“不良种类”或“不良项目”的条件");
        }
        //校验优先级是否是正整数
        if (Constants.INT_ZERO >= priorityElementConfig.getPriority()) {
            throw new ResponseException("error.PriorityError", "优先级请输入正整数");
        }
        //校验是否已有相同优先级
        PriorityElementConfig byPriorityPriorityElementConfig = priorityElementConfigRepository.findByTargetAndPriorityAndDeleted(priorityElementConfig.getTarget(), priorityElementConfig.getPriority(), Constants.LONG_ZERO);
        if (null != byPriorityPriorityElementConfig) {
            throw new ResponseException("error.SamePriority", "已有相同的优先级");
        }
        List<Integer> priorityElementConfigCombinationList = priorityElementConfig.getCombination();
        if (priorityElementConfigCombinationList.stream().distinct().toList().size() != priorityElementConfig.getCombination().size()) {
            throw new ResponseException("error.SameCombination", "组合条件不可重复选择");
        }

        PriorityElementConfig queryPriorityElementConfig = this.findUniquePriorityElementConfig(priorityElementConfig.getTarget(),priorityElementConfig.getCombination());
        if(Objects.isNull(priorityElementConfig.getId()) && Objects.nonNull(queryPriorityElementConfig)){
            throw new ResponseException("error.SameCombination", "已有相同的组合条件");
        }
        if(Objects.nonNull(priorityElementConfig.getId()) && Objects.nonNull(queryPriorityElementConfig) && !priorityElementConfig.getId().equals(queryPriorityElementConfig.getId())){
            throw new ResponseException("error.SameCombination", "已有相同的组合条件");
        }
        //校验互斥关系
        checkMutualExclusion(priorityElementConfig);
        priorityElementConfig.setCombination(priorityElementConfigCombinationList);
        save(priorityElementConfig);
    }

    public PriorityElementConfig findUniquePriorityElementConfig(int target,List<Integer> combination){
        List<PriorityElementConfig> priorityElementConfigList = priorityElementConfigRepository.findAllByTargetAndDeletedOrderByPriorityDesc(target,Constants.LONG_ZERO);
        String sortedToSaveCombination = combination.stream().sorted().map(Object::toString).collect(Collectors.joining());
        AtomicReference<PriorityElementConfig> originPriorityElementConfig = new AtomicReference<>();
        if(!CollectionUtils.isEmpty(priorityElementConfigList)){
            priorityElementConfigList.forEach(priorityElementConfig -> {
                String sortedCombination =  priorityElementConfig.getCombination().stream().sorted().map(Object::toString).collect(Collectors.joining());
                if(sortedCombination.equals(sortedToSaveCombination)){
                    originPriorityElementConfig.set(priorityElementConfig);
                }
            });
        }
        return originPriorityElementConfig.get();
    }

    /**
     * 校验互斥关系
     * <AUTHOR>
     * @date 2022/10/24
     * @param priorityElementConfig 条件优先级
     */
    private void checkMutualExclusion(PriorityElementConfig priorityElementConfig) {
        //校验互斥关系
        ResponseException badRequestAlertException = new ResponseException("error.ConditionsMutuallyExclusive", "和已添加的条件发生冲突，请重新选择");
        List<Integer> combination = priorityElementConfig.getCombination();
        for (Integer integer : combination) {
            //产品谱系和 (工单) 互斥
            if (Constants.INT_ZERO == integer) {
                for (Integer integer1 : combination) {
                    if (Constants.INT_ONE == integer1) {
                        throw badRequestAlertException;
                    }
                }
                //工单和 (工单类型,工序组,工艺路线,客户) 互斥
            } else if (Constants.INT_ONE == integer) {
                for (Integer integer1 : combination) {
                    if (Constants.INT_ZERO == integer1 || Constants.INT_TWO == integer1 || Constants.INT_THREE == integer1 || Constants.INT_FIVE == integer1 || Constants.INT_SIX == integer1) {
                        throw badRequestAlertException;
                    }
                }
                //工序组和 (工艺路线) 互斥
            } else if (Constants.INT_THREE == integer) {
                for (Integer integer1 : combination) {
                    if (Constants.INT_FIVE == integer1) {
                        throw badRequestAlertException;
                    }
                }
            }
        }
    }

    /**
     * 修改条件优先级配置
     * <AUTHOR>
     * @date 2022/10/24
     * @param priorityElementConfig 条件优先级
     */
    public void updateInstance(PriorityElementConfig priorityElementConfig) {
        // 校验id
        if (null == priorityElementConfig.getId()) {
            throw new ResponseException("error.NoObjectSelected", "未选择需要修改的对象");
        }
        //校验是否已有相同优先级
        PriorityElementConfig byPriorityPriorityElementConfig = priorityElementConfigRepository.findByTargetAndPriorityAndDeleted(priorityElementConfig.getTarget(), priorityElementConfig.getPriority(), Constants.LONG_ZERO);
        if (null != byPriorityPriorityElementConfig && !byPriorityPriorityElementConfig.getId().equals(priorityElementConfig.getId())) {
            throw new ResponseException("error.SamePriority", "已有相同的优先级配置");
        }
        //根据id查出数据库内的优先级配置
        PriorityElementConfig byPriorityElementConfig = priorityElementConfigRepository.getReferenceById(priorityElementConfig.getId());
        //只能修改优先级和备注
        byPriorityElementConfig.setPriority(priorityElementConfig.getPriority());
        byPriorityElementConfig.setNote(priorityElementConfig.getNote());
        byPriorityElementConfig.setCombination(priorityElementConfig.getCombination());
        update(byPriorityElementConfig);
    }

    /**
     * 逻辑删除条件优先级配置
     * <AUTHOR>
     * @date 2022/10/25
     * @param priorityElementConfigId 优先级配置id
     */
    public void deleteInstance(Long priorityElementConfigId) {
        // 校验id
        if (null == priorityElementConfigId) {
            throw new ResponseException("error.NoObjectSelected", "未选择需要删除的对象");
        }
        ResponseException badRequestAlertException = new ResponseException("error.DeleteTheCreatedRulesFirst", "当前条件优先级已创建规则，请先删除规则");
        // 根据优先级配置id查询关联的预警规则
        StepWarningStandard byPriorityElementConfigStepWarningStandard = stepWarningStandardRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if (Objects.nonNull(byPriorityElementConfigStepWarningStandard)) {
            throw badRequestAlertException;
        }
        //根据优先级配置id查询是否有关联的不良预警规则
        UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = unqualifiedItemWarningStandardRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if (Objects.nonNull(unqualifiedItemWarningStandard)) {
            throw badRequestAlertException;
        }
        //根据优先级配置id查询是否有关联的检测规则
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if (Objects.nonNull(pedigreeStepCheckRule)) {
            throw badRequestAlertException;
        }
        // 优先级id是否关联工序配置
        PedigreeStep pedigreeStep = pedigreeStepRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if(Objects.nonNull(pedigreeStep)){
            throw badRequestAlertException;
        }
        // 优先级id是否关联工序不良配置
        PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if(Objects.nonNull(pedigreeStepUnqualifiedItem)){
            throw badRequestAlertException;
        }
        // 优先级配置是否关联产品谱系工序指标
        PedigreeStepSpecification pedigreeStepSpecification = pedigreeStepSpecificationRepository.findTop1ByPriorityElementConfigIdAndDeleted(priorityElementConfigId, Constants.LONG_ZERO);
        if(Objects.nonNull(pedigreeStepSpecification)){
            throw badRequestAlertException;
        }
        deleteById(priorityElementConfigId);
    }

    /**
     * 根据对象查询所有优先级配置
     * <AUTHOR>
     * @date 2022/10/25 14:41
     * @param target 对象
     * @return List<PriorityElementConfig>
     */
    @Transactional(readOnly = true)
    public List<PriorityElementConfig> findAllByTarget(Integer target) {
        if (null == target) {
            throw new ResponseException("error.NoObject", "未选择对象");
        }
        return priorityElementConfigRepository.findAllByTargetAndDeletedOrderByPriorityDesc(target, Constants.LONG_ZERO);
    }

    /**
     * 通过条件组合和条件对象查找优先级配置
     *
     * @param combination 条件组合
     * @param target 条件对象
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    @Transactional(readOnly = true)
    public PriorityElementConfig byCombinationAndTarget(String combination, Integer target) {
        return this.findUniquePriorityElementConfig(target,JSON.parseArray(combination, Integer.class));
    }
}
