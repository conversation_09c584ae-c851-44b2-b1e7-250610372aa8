package net.airuima.rbase.service.ocmes.impl;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.StepCategoryEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.client.*;
import net.airuima.rbase.dto.ocmes.*;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.proxy.optical.RbaseBakeCycleBakeAgeingProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class BakeCycleBakeAgeingServiceImpl implements BakeCycleBakeAgeingModelService {

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private RbaseBakeCycleBakeAgeingProxy rbaseBakeCycleBakeAgeingProxy;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private WsStepRepository wsStepRepository;


    /**
     * 获取烘烤温循老化配置
     *
     * @param category 工序类型
     * @param step     工序信息
     * @param pedigree 产品谱系
     * @return net.airuima.rmes.dto.ocmes.BakeCycleBakeAgeingConfigDTO 烘烤温循老化配置
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Override
    public BakeCycleBakeAgeingConfigDTO getBakeCycleBakeAgeingConfigInfo(Integer category, Step step, Pedigree pedigree, Long workFlowId, Long clientId) {
        BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto = new BakeCycleBakeAgeingConfigGetDTO(pedigree, step);
        bakeCycleBakeAgeingConfigGetDto.setWorkFlowId(workFlowId).setClientId(clientId);
        //若是烘烤放入工序则需要获取烘烤配置信息
        if (category == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus()) {
            BakeConfigInfoDTO bakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (ObjectUtils.isEmpty(bakeConfigInfoDto)){
                throw new ResponseException("error.bakeConfigIsNotExist", "工序(+"+bakeCycleBakeAgeingConfigGetDto.getStep().getCode()+"+)未配置烘烤参数");
            }
            return new BakeCycleBakeAgeingConfigDTO().setBakeConfigInfo(bakeConfigInfoDto);
        }
        //若是温循放入工序则需要获取烘烤配置信息
        if (category == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus()) {
            CycleBakeConfigInfoDTO cycleBakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryCycleBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (ObjectUtils.isEmpty(cycleBakeConfigInfoDto)){
                throw new ResponseException("error.cycleBakeConfigIsNotExist", "工序(+"+bakeCycleBakeAgeingConfigGetDto.getStep().getCode()+"+)未配置温循参数");
            }
            return new BakeCycleBakeAgeingConfigDTO().setCycleBakeConfigInfo(cycleBakeConfigInfoDto);
        }
        //若是老化放入工序则需要获取烘烤配置信息
        if (category == StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus()) {
            AgeingConfigInfoDTO ageingConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryAgeingConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (ObjectUtils.isEmpty(ageingConfigInfoDto)){
                throw new ResponseException("error.ageingConfigIsNotExist", "工序(+"+bakeCycleBakeAgeingConfigGetDto.getStep().getCode()+"+)未配置老化参数");
            }
            return new BakeCycleBakeAgeingConfigDTO().setAgeingConfigInfo(ageingConfigInfoDto);
        }
        return null;
    }

    /**
     * 验证烘烤温循老化工序的合法性
     *
     * @param bakeCycleBakeAgeingValidDto 验证烘烤温循老化请求取出参数
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Override
    public void validBakeCycleBakeAgeingHistory(BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDto, WsStep wsStep) {
        List<WsStep> wsStepList = bakeCycleBakeAgeingValidDto.getWsStepList();
        //验证是否为取出放入工序类型
        if (Boolean.TRUE.equals(StepCategoryEnum.isBakeCycleBakeAgeingStep(wsStep.getCategory()))) {
            //取出-找到最近一条投产工单的放入工序 (放入-用当前工序)
            if (wsStep.getCategory() % net.airuima.constant.Constants.INT_TWO == net.airuima.constant.Constants.INT_ZERO) {
                if(StringUtils.isBlank(wsStep.getPreStepId())){
                    throw new ResponseException("error.BatchWorkDetailNull", "未检测放入工序记录");
                }
                List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).toList();
                List<WsStep> preWsStepList = wsStepList.stream().filter(wsStep1 -> preStepIds.contains(wsStep1.getStep().getId())).toList();
                if (CollectionUtils.isEmpty(preWsStepList)){
                    throw new ResponseException("error.stepTypeError", "取出工序的前置工序不是放入工序");
                }
                WsStep preWsStep = preWsStepList.stream().filter(wsStep1 -> (wsStep1.getCategory() > Constants.INT_TWO) && (wsStep1.getCategory() % Constants.INT_TWO == Constants.INT_ONE)).findFirst()
                        .orElseThrow(() -> new ResponseException("error.stepTypeError", "取出工序的前置工序不是放入工序"));
                //修改为放入工序id
                bakeCycleBakeAgeingValidDto.setStepId(preWsStep.getStep().getId());
            }
            //烘烤验证
            if (StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory() || StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory()) {
                BaseDTO baseDto = rbaseBakeCycleBakeAgeingProxy.validBakeStepHistory(bakeCycleBakeAgeingValidDto);
                if (Constants.KO.equals(baseDto.getStatus())) {
                    throw new ResponseException("error.bakeError", baseDto.getMessage());
                }
            }
            //温循验证
            if (StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory() || StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory()) {
                BaseDTO baseDto = rbaseBakeCycleBakeAgeingProxy.validCycleBakeStepHistory(bakeCycleBakeAgeingValidDto);
                if (Constants.KO.equals(baseDto.getStatus())) {
                    throw new ResponseException("error.cycleBakeError", baseDto.getMessage());
                }
            }
            //老化验证
            if (StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory() || StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus() == bakeCycleBakeAgeingValidDto.getCategory()) {
                BaseDTO baseDto = rbaseBakeCycleBakeAgeingProxy.validAgeingStepHistory(bakeCycleBakeAgeingValidDto);
                if (Constants.KO.equals(baseDto.getStatus())) {
                    throw new ResponseException("error.ageingError", baseDto.getMessage());
                }
            }
        }
    }

    /**
     * 烘烤温循老化历史记录保存
     *
     * @param bakeCycleBakeAgeingSaveRequestDto 烘烤温循老化请求保存参数
     * <AUTHOR>
     * @date 2023/3/30
     */
    @Override
    public void saveBakeCycleBakeAgeingHistoryInfo(BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDto) {
        //验证是否为取出放入工序类型
        if (Boolean.TRUE.equals(StepCategoryEnum.isBakeCycleBakeAgeingStep(bakeCycleBakeAgeingSaveRequestDto.getCategory()))) {
            //烘烤保存
            if (StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory() || StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory()) {
                rbaseBakeCycleBakeAgeingProxy.saveBakeStepHistory(bakeCycleBakeAgeingSaveRequestDto);
            }
            //温循保存
            if (StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory() || StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory()) {
                rbaseBakeCycleBakeAgeingProxy.saveCycleBakeStepHistory(bakeCycleBakeAgeingSaveRequestDto);
            }
            //老化保存
            if (StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory() || StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus() == bakeCycleBakeAgeingSaveRequestDto.getCategory()) {
                rbaseBakeCycleBakeAgeingProxy.saveAgeingStepHistory(bakeCycleBakeAgeingSaveRequestDto);
            }
        }
    }

    /**
     * 获取工单待做烘烤温循老化工序信息
     *
     * @param rworkerBatchToDoStepGetDTO 批量待做工序信息
     * @param batchStepSaveBaseInfo      请求工序基础信息
     * @return void
     * <AUTHOR>
     * @Date 2023/3/31
     */
    @Override
    public void bakeCycleBakeAgeingBatchInfo(RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        //工单请求且只有在不绑定容器且管控模式以及没有SN列表为批量时才可以提前验证烘烤温循老化历史
        if (Boolean.TRUE.equals(!rworkerBatchToDoStepGetDTO.getBindContainer())
                && rworkerBatchToDoStepGetDTO.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName()
                && CollectionUtils.isEmpty(rworkerBatchToDoStepGetDTO.getSnInfoList())) {
            this.validBakeCycleBakeAgeingHistory(new BakeCycleBakeAgeingValidDTO(batchStepSaveBaseInfo.getSubWsProductionMode(), batchStepSaveBaseInfo.getWsStepList(),Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ? batchStepSaveBaseInfo.getSubWorkSheet().getId() : batchStepSaveBaseInfo.getWorkSheet().getId(), rworkerBatchToDoStepGetDTO.getCategory(), batchStepSaveBaseInfo.getStep().getId()),batchStepSaveBaseInfo.getWsStep());        }
        //放入工序获取烘烤温循老化配置
        BakeCycleBakeAgeingConfigDTO bakeCycleBakeAgeingConfigInfo = this.getBakeCycleBakeAgeingConfigInfo(rworkerBatchToDoStepGetDTO.getCategory(), batchStepSaveBaseInfo.getStep(), batchStepSaveBaseInfo.getWorkSheet().getPedigree(), batchStepSaveBaseInfo.getWorkFlow().getId(), batchStepSaveBaseInfo.getWorkSheet().getClientId());
        if (!ObjectUtils.isEmpty(bakeCycleBakeAgeingConfigInfo)) {
            rworkerBatchToDoStepGetDTO.setBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getBakeConfigInfo())
                    .setAgeingConfigInfo(bakeCycleBakeAgeingConfigInfo.getAgeingConfigInfo())
                    .setCycleBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getCycleBakeConfigInfo());
        }
    }

    /**
     * 获取容器待做烘烤温循老化工序信息
     *
     * @param containerNextToDoStepInfo 容器待做工序信息
     * @param stepProcessBaseDTO        请求工序基础信息
     * @return void
     * <AUTHOR>
     * @Date 2023/3/31
     */
    @Override
    public void bakeCycleBakeAgeingContainerInfo(RworkerContainerToDoStepGetDTO containerNextToDoStepInfo, RworkerStepProcessBaseDTO stepProcessBaseDTO) {
        //容器请求且只有在管控模式为批量时以及没有SN列表下才可以提前验证烘烤温循老化历史验证
        if (containerNextToDoStepInfo.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName() && CollectionUtils.isEmpty(containerNextToDoStepInfo.getSnInfoList())) {
            this.validBakeCycleBakeAgeingHistory(new BakeCycleBakeAgeingValidDTO(stepProcessBaseDTO.getSubWsProductionMode(),stepProcessBaseDTO.getWsStepList(), Boolean.TRUE.equals(stepProcessBaseDTO.getSubWsProductionMode()) ? stepProcessBaseDTO.getSubWorkSheet().getId() : stepProcessBaseDTO.getWorkSheet().getId(), stepProcessBaseDTO.getWsStep().getCategory(), stepProcessBaseDTO.getStep().getId(), containerNextToDoStepInfo.getContainerInfo().getCode()),stepProcessBaseDTO.getWsStep());
        }
        //获取烘烤温循老化配置
        BakeCycleBakeAgeingConfigDTO bakeCycleBakeAgeingConfigInfo = this.getBakeCycleBakeAgeingConfigInfo(containerNextToDoStepInfo.getCategory(), stepProcessBaseDTO.getStep(), stepProcessBaseDTO.getWorkSheet().getPedigree(), stepProcessBaseDTO.getWorkFlow().getId(), stepProcessBaseDTO.getWorkSheet().getClientId());
        if (!ObjectUtils.isEmpty(bakeCycleBakeAgeingConfigInfo)) {
            containerNextToDoStepInfo.setBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getBakeConfigInfo())
                    .setAgeingConfigInfo(bakeCycleBakeAgeingConfigInfo.getAgeingConfigInfo())
                    .setCycleBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getCycleBakeConfigInfo());
        }
    }

    /**
     * 获取单支SN待做烘烤温循老化工序信息
     *
     * @param sn                        投产SN
     * @param rworkerSnToDoStepGetDTO   SN待做工序信息
     * @param rworkerStepProcessBaseDTO 请求工序基础信息
     */
    @Override
    public void bakeCycleBakeAgeingSnInfo(String sn,Integer snReworkTime, RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        BakeCycleBakeAgeingValidDTO bakeCycleBakeAgeingValidDTO = new BakeCycleBakeAgeingValidDTO(rworkerStepProcessBaseDTO.getSubWsProductionMode(),rworkerStepProcessBaseDTO.getWsStepList(), Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ? rworkerStepProcessBaseDTO.getSubWorkSheet().getId() : rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getWsStep().getCategory(), rworkerStepProcessBaseDTO.getStep().getId());
        bakeCycleBakeAgeingValidDTO.setSn(sn).setReworkTime(snReworkTime);
        this.validBakeCycleBakeAgeingHistory(bakeCycleBakeAgeingValidDTO,rworkerStepProcessBaseDTO.getWsStep());
        //获取烘烤温循老化配置
        BakeCycleBakeAgeingConfigDTO bakeCycleBakeAgeingConfigInfo = this.getBakeCycleBakeAgeingConfigInfo(rworkerStepProcessBaseDTO.getWsStep().getCategory(), rworkerStepProcessBaseDTO.getStep(), rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getWorkFlow().getId(), rworkerStepProcessBaseDTO.getWorkSheet().getClientId());
        if (!ObjectUtils.isEmpty(bakeCycleBakeAgeingConfigInfo)) {
            rworkerSnToDoStepGetDTO.setBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getBakeConfigInfo())
                    .setAgeingConfigInfo(bakeCycleBakeAgeingConfigInfo.getAgeingConfigInfo())
                    .setCycleBakeConfigInfo(bakeCycleBakeAgeingConfigInfo.getCycleBakeConfigInfo());
        }
    }

    /**
     * 删除烘烤温循老化历史记录
     *
     * @param workSheet      工单
     * @param subWorkSheet   子工单
     * @param step           工序
     * @param containerCodes 容器编码列表
     * @param sn             sn编码
     */
    @Override
    public void logicDeletedBakeCycleBakeAgeingHistory(WorkSheet workSheet, SubWorkSheet subWorkSheet,  WsStep currWsStep, List<String> containerCodes, String sn,Integer reworkTime) {
        if (Boolean.TRUE.equals(commonService.validDateBakeCycleBakeAgeingStep( currWsStep))) {
            if (currWsStep.getCategory() == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() || currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus()) {
                rbaseBakeCycleBakeAgeingProxy.logicDeletedBakeHistory(new RollBackBakeCycleBakeAgeingHistoryDTO(workSheet,
                        subWorkSheet, currWsStep.getStep(), containerCodes, sn,reworkTime));
                return;
            }
            if (currWsStep.getCategory() == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus() || currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus()) {
                rbaseBakeCycleBakeAgeingProxy.logicDeletedCycleBakeHistory(new RollBackBakeCycleBakeAgeingHistoryDTO(workSheet,
                        subWorkSheet, currWsStep.getStep(), containerCodes, sn,reworkTime));
                return;
            }
            rbaseBakeCycleBakeAgeingProxy.logicDeletedAgeingHistory(new RollBackBakeCycleBakeAgeingHistoryDTO(workSheet,
                    subWorkSheet, currWsStep.getStep(), containerCodes, sn,reworkTime));
        }
    }

    /**
     * 替换已容器下交的烘烤温循老化记录的容器编号
     *
     * @param ocReplaceContainerDto 替换容器参数
     */
    @Override
    public void replaceContainerBakeHistory(OcReplaceContainerDTO ocReplaceContainerDto) {
        Step step = ocReplaceContainerDto.getStep();
        if (step.getCategory() == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() || step.getCategory() == StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus()) {
            rbaseBakeCycleBakeAgeingProxy.replaceContainerBakeHistory(ocReplaceContainerDto);
        }
    }

    /**
     * 替换已容器下交的温循记录的容器编号
     *
     * @param ocReplaceContainerDto 替换容器参数
     */
    @Override
    public void replaceContainerCycleBakeHistory(OcReplaceContainerDTO ocReplaceContainerDto) {
        Step step = ocReplaceContainerDto.getStep();
        if (step.getCategory() == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus() || step.getCategory() == StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus()) {
            rbaseBakeCycleBakeAgeingProxy.replaceContainerCycleBakeHistory(ocReplaceContainerDto);
        }
    }

    /**
     * 替换已容器下交的老化记录的容器编号
     *
     * @param ocReplaceContainerDto 替换容器参数
     */
    @Override
    public void replaceContainerAgeingHistory(OcReplaceContainerDTO ocReplaceContainerDto) {
        Step step = ocReplaceContainerDto.getStep();
        if (step.getCategory() == StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus() || step.getCategory() == StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            rbaseBakeCycleBakeAgeingProxy.replaceContainerAgeingHistory(ocReplaceContainerDto);
        }
    }

    /**
     * 获取烘烤温循老化配置（批量）
     *
     * @param clientGetStepInfoDto 待做工序信息
     * @return net.airuima.dto.client.ClientGetStepInfoDTO  工序返回信息
     */
    @Override
    public ClientGetStepInfoDTO queryBakeCycleBakeAgeingConfig(ClientGetStepInfoDTO clientGetStepInfoDto) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(clientGetStepInfoDto.getPedigreeCode(), Constants.LONG_ZERO);
        Optional<Step> stepOptional = stepRepository.findByCode(clientGetStepInfoDto.getStepCode());
        if (!pedigreeOptional.isPresent()) {
            clientGetStepInfoDto.setStatus(Constants.KO).setMessage("产品谱系不存在");
            return clientGetStepInfoDto;
        }
        if (!stepOptional.isPresent()) {
            clientGetStepInfoDto.setStatus(Constants.KO).setMessage("工序不存在");
            return clientGetStepInfoDto;
        }
        BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto = new BakeCycleBakeAgeingConfigGetDTO(pedigreeOptional.get(), stepOptional.get());
        //若是烘烤放入工序则需要获取烘烤配置信息
        if (clientGetStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus()) {
            BakeConfigInfoDTO bakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == bakeConfigInfoDto) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetStepInfoDto.getStepName() + ")未配置烘烤参数!"));
            }
            return clientGetStepInfoDto.setBakeConfigInfo(bakeConfigInfoDto);
        }
        //若是温循放入工序则需要获取烘烤配置信息
        if (clientGetStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus()) {
            CycleBakeConfigInfoDTO cycleBakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryCycleBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == cycleBakeConfigInfoDto) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetStepInfoDto.getStepName() + ")未配置温循参数!"));
            }
            return clientGetStepInfoDto.setCycleBakeConfigInfo(cycleBakeConfigInfoDto);
        }
        //若是老化放入工序则需要获取烘烤配置信息
        if (clientGetStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus()) {
            AgeingConfigInfoDTO ageingConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryAgeingConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == ageingConfigInfoDto) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetStepInfoDto.getStepName() + ")未配置老化参数!"));
            }
            return clientGetStepInfoDto.setAgeingConfigInfo(ageingConfigInfoDto);
        }
        return clientGetStepInfoDto;
    }

    /**
     * 查询烘烤历史的放入时间参数
     *
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId      子工单ID
     * @param category            工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  烘烤历史放入时间参数
     */
    @Override
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryBakeHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        //烘烤历史查询参数
        BakeCycleBakeAgeingHistoryGetDTO bakeCycleBakeAgeingHistoryGetDto = null;
        //容器生产详情
        Optional<ContainerDetail> containerDetailOptional = Optional.empty();
        //批量工作详情
        BatchWorkDetail batchWorkDetail = null;
        //若存在容器则查询容器生产详情 否则查询批量工作详情  工序类型为烘烤放入类型
        if (ValidateUtils.isValid(requestContainerIds)) {
            containerDetailOptional = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndBatchWorkDetailStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, requestContainerIds, category - 1, Constants.LONG_ZERO);
        } else {
            batchWorkDetail = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, category - 1, Constants.LONG_ZERO);
        }
        //根据是否存在容器 获取烘烤历史查询参数
        if (containerDetailOptional.isPresent()) {
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, containerDetailOptional.get().getBatchWorkDetail().getStep().getId(), containerDetailOptional.get().getContainer().getCode(), category);
        } else {
            if (null == batchWorkDetail) {
                throw new ResponseException("error.batchWorkDetailNotExist", "批量工序生产详情不存在");
            }
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, batchWorkDetail.getStep().getId(), null, category);
        }
        return rbaseBakeCycleBakeAgeingProxy.queryBakeHistoryPutInDate(bakeCycleBakeAgeingHistoryGetDto);
    }

    /**
     * 查询温循历史的放入时间参数
     *
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId      子工单ID
     * @param category            工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  温循历史放入时间参数
     */
    @Override
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryCycleBakeHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        //温循历史查询参数
        BakeCycleBakeAgeingHistoryGetDTO bakeCycleBakeAgeingHistoryGetDto = null;
        //容器生产详情
        Optional<ContainerDetail> containerDetailOptional = Optional.empty();
        //批量工作详情
        BatchWorkDetail batchWorkDetail = null;
        //若存在容器则查询容器生产详情 否则查询批量工作详情  工序类型为温循放入类型
        if (ValidateUtils.isValid(requestContainerIds)) {
            containerDetailOptional = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndBatchWorkDetailStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, requestContainerIds, category - 1, Constants.LONG_ZERO);
        } else {
            batchWorkDetail = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, category - 1, Constants.LONG_ZERO);
        }
        //根据是否存在容器 获取温循历史查询参数
        if (containerDetailOptional.isPresent()) {
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, containerDetailOptional.get().getBatchWorkDetail().getStep().getId(), containerDetailOptional.get().getContainer().getCode(), category);
        } else {
            if (null == batchWorkDetail) {
                throw new ResponseException("error.batchWorkDetailNotExist", "批量工序生产详情不存在");
            }
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, batchWorkDetail.getStep().getId(), null, category);
        }
        return rbaseBakeCycleBakeAgeingProxy.queryCycleBakeHistoryPutInDate(bakeCycleBakeAgeingHistoryGetDto);
    }

    /**
     * 查询老化历史的放入时间参数
     *
     * @param requestContainerIds 容器ID
     * @param subWorkSheetId      子工单ID
     * @param category            工序类型
     * @return net.airuima.dto.client.BakeCycleBakeAgeingHistoryPutInDateDTO  老化历史放入时间参数
     */
    @Override
    public BakeCycleBakeAgeingHistoryPutInDateDTO queryAgeingHistoryPutInDate(List<Long> requestContainerIds, Long subWorkSheetId, int category) {
        //老化历史查询参数
        BakeCycleBakeAgeingHistoryGetDTO bakeCycleBakeAgeingHistoryGetDto = null;
        //容器生产详情
        Optional<ContainerDetail> containerDetailOptional = Optional.empty();
        //批量工作详情
        BatchWorkDetail batchWorkDetail = null;
        //若存在容器则查询容器生产详情 否则查询批量工作详情  工序类型为老化放入类型
        if (ValidateUtils.isValid(requestContainerIds)) {
            containerDetailOptional = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndBatchWorkDetailStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, requestContainerIds, category - 1, Constants.LONG_ZERO);
        } else {
            batchWorkDetail = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndStepCategoryAndDeletedOrderByIdDesc(subWorkSheetId, category - 1, Constants.LONG_ZERO);
        }
        //根据是否存在容器 获取温循历史查询参数
        if (containerDetailOptional.isPresent()) {
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, containerDetailOptional.get().getBatchWorkDetail().getStep().getId(), containerDetailOptional.get().getContainer().getCode(), category);
        } else {
            if (null == batchWorkDetail) {
                throw new ResponseException("error.batchWorkDetailNotExist", "批量工序生产详情不存在");
            }
            bakeCycleBakeAgeingHistoryGetDto = new BakeCycleBakeAgeingHistoryGetDTO(subWorkSheetId, batchWorkDetail.getStep().getId(), null, category);
        }
        return rbaseBakeCycleBakeAgeingProxy.queryAgeingHistoryPutInDate(bakeCycleBakeAgeingHistoryGetDto);
    }

    /**
     * 获取烘烤温循老化配置（单支）
     *
     * @param clientGetSnStepInfoDto 待做工序信息
     * @return net.airuima.dto.client.ClientGetStepInfoDTO  工序返回信息(单支)
     */
    @Override
    public ClientGetSnStepInfoDTO queryBakeCycleBakeAgeingConfig(ClientGetSnStepInfoDTO clientGetSnStepInfoDto) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(clientGetSnStepInfoDto.getPedigreeCode(), Constants.LONG_ZERO);
        Optional<Step> stepOptional = stepRepository.findByCode(clientGetSnStepInfoDto.getStepCode());
        if (!pedigreeOptional.isPresent()) {
            clientGetSnStepInfoDto.setStatus(Constants.KO).setMessage("产品谱系不存在");
            return clientGetSnStepInfoDto;
        }
        if (!stepOptional.isPresent()) {
            clientGetSnStepInfoDto.setStatus(Constants.KO).setMessage("工序不存在");
            return clientGetSnStepInfoDto;
        }
        BakeCycleBakeAgeingConfigGetDTO bakeCycleBakeAgeingConfigGetDto = new BakeCycleBakeAgeingConfigGetDTO(pedigreeOptional.get(), stepOptional.get());
        //若是烘烤放入工序则需要获取烘烤配置信息
        if (clientGetSnStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus()) {
            BakeConfigInfoDTO bakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == bakeConfigInfoDto) {
                return new ClientGetSnStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetSnStepInfoDto.getStepName() + ")未配置烘烤参数!"));
            }
            clientGetSnStepInfoDto.setBakeConfigInfo(bakeConfigInfoDto);
            return clientGetSnStepInfoDto;
        }
        //若是温循放入工序则需要获取烘烤配置信息
        if (clientGetSnStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus()) {
            CycleBakeConfigInfoDTO cycleBakeConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryCycleBakeConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == cycleBakeConfigInfoDto) {
                return new ClientGetSnStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetSnStepInfoDto.getStepName() + ")未配置温循参数!"));
            }
            clientGetSnStepInfoDto.setCycleBakeConfigInfo(cycleBakeConfigInfoDto);
            return clientGetSnStepInfoDto;
        }
        //若是老化放入工序则需要获取烘烤配置信息
        if (clientGetSnStepInfoDto.getStepCategory() == StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus()) {
            AgeingConfigInfoDTO ageingConfigInfoDto = rbaseBakeCycleBakeAgeingProxy.queryAgeingConfigInfo(bakeCycleBakeAgeingConfigGetDto);
            if (null == ageingConfigInfoDto) {
                return new ClientGetSnStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + clientGetSnStepInfoDto.getStepName() + ")未配置老化参数!"));
            }
            clientGetSnStepInfoDto.setAgeingConfigInfo(ageingConfigInfoDto);
            return clientGetSnStepInfoDto;
        }
        return clientGetSnStepInfoDto;
    }
    /**
     * 下交保存验证烘烤状态
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO validateBakeStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.validateBakeStepInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 下交保存验证温循状态
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO validateCycleBakeStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.validateCycleBakeStepInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 下交保存验证老化状态
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤温循老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO validateAgeingStepInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.validateAgingStepInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 保存烘烤历史记录
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存烘烤参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO saveBakeHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.saveBakeHistoryInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 保存温循历史记录
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存温循参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO saveCycleBakeHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.saveCycleBakeHistoryInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 保存老化历史记录
     *
     * @param bakeCycleBakeAgeingSaveDto 下交工序保存老化参数信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO saveAgeingHistoryInfo(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto) {
        bakeCycleBakeAgeingSaveDto = updateBakeCycleBakeAgeingSaveDTO(bakeCycleBakeAgeingSaveDto);
        return rbaseBakeCycleBakeAgeingProxy.saveAgeingHistoryInfo(bakeCycleBakeAgeingSaveDto);
    }

    /**
     * 替换烘烤温循老化历史对应的sn
     *
     * @param snReplaceDtoList 替换详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/2/23
     */
    @Override
    public void replaceBakeHistorySn(List<SnReplaceDTO> snReplaceDtoList) {
        rbaseBakeCycleBakeAgeingProxy.replaceBakeHistorySn(snReplaceDtoList);
    }

    /**
     * 替换温循历史对应的sn
     *
     * @param snReplaceDtoList 替换详情列表
     * <AUTHOR>
     */
    @Override
    public void replaceCycleBakeHistorySn(List<SnReplaceDTO> snReplaceDtoList) {
        rbaseBakeCycleBakeAgeingProxy.replaceCycleBakeHistorySn(snReplaceDtoList);
    }

    /**
     * 替换老化历史对应的sn
     *
     * @param snReplaceDtoList 替换详情列表
     * <AUTHOR>
     */
    @Override
    public void replaceAgeingHistorySn(List<SnReplaceDTO> snReplaceDtoList) {
        rbaseBakeCycleBakeAgeingProxy.replaceAgeingHistorySn(snReplaceDtoList);
    }

    /**
     * 添加sn的返修次数
     * @param bakeCycleBakeAgeingSaveDto 烘烤温循老化
     * @return BakeCycleBakeAgeingSaveDTO
     */
    public BakeCycleBakeAgeingSaveDTO updateBakeCycleBakeAgeingSaveDTO(BakeCycleBakeAgeingSaveDTO bakeCycleBakeAgeingSaveDto){
        if (net.airuima.util.ValidateUtils.isValid(bakeCycleBakeAgeingSaveDto.getBakeHistoryInfoList())){
            List<String> snList = bakeCycleBakeAgeingSaveDto.getBakeHistoryInfoList().stream().map(BakeHistoryInfoDTO::getSn)
                    .filter(ValidateUtils::isValid).collect(Collectors.toList());
            if (net.airuima.util.ValidateUtils.isValid(snList)){
                List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snList, Constants.LONG_ZERO);
                if (net.airuima.util.ValidateUtils.isValid(snWorkStatuses)){
                    List<BakeHistoryInfoDTO> bakeHistoryInfoDTOList = bakeCycleBakeAgeingSaveDto.getBakeHistoryInfoList().stream()
                            .peek(bakeHistoryInfoDTO -> {
                                if (net.airuima.util.ValidateUtils.isValid(bakeHistoryInfoDTO.getSn())) {
                                    Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatuses.stream().filter(sn -> sn.getSn().equals(bakeHistoryInfoDTO.getSn())).findFirst();
                                    snWorkStatusOptional.ifPresent(snWorkStatus -> bakeHistoryInfoDTO.setReworkTime(snWorkStatus.getReworkTime()));
                                }
                            }).collect(Collectors.toList());
                    bakeCycleBakeAgeingSaveDto.setBakeHistoryInfoList(bakeHistoryInfoDTOList);
                }
            }
        }
        if (net.airuima.util.ValidateUtils.isValid(bakeCycleBakeAgeingSaveDto.getCycleBakeHistoryInfoList())){
            List<String> snList = bakeCycleBakeAgeingSaveDto.getCycleBakeHistoryInfoList().stream().filter(cycleBakeHistoryInfoDTO -> net.airuima.util.ValidateUtils.isValid(cycleBakeHistoryInfoDTO.getSn()))
                    .map(CycleBakeHistoryInfoDTO::getSn).collect(Collectors.toList());
            if (net.airuima.util.ValidateUtils.isValid(snList)){
                List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snList, net.airuima.constant.Constants.LONG_ZERO);
                if (net.airuima.util.ValidateUtils.isValid(snWorkStatuses)){
                    List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoDTOList = bakeCycleBakeAgeingSaveDto.getCycleBakeHistoryInfoList().stream()
                            .map(cycleBakeHistoryInfoDTO -> {
                                if (net.airuima.util.ValidateUtils.isValid(cycleBakeHistoryInfoDTO.getSn())) {
                                    Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatuses.stream().filter(sn -> sn.getSn().equals(cycleBakeHistoryInfoDTO.getSn())).findFirst();
                                    snWorkStatusOptional.ifPresent(snWorkStatus -> cycleBakeHistoryInfoDTO.setReworkTime(snWorkStatus.getReworkTime()));
                                }
                                return cycleBakeHistoryInfoDTO;
                            }).collect(Collectors.toList());
                    bakeCycleBakeAgeingSaveDto.setCycleBakeHistoryInfoList(cycleBakeHistoryInfoDTOList);
                }
            }
        }
        if (net.airuima.util.ValidateUtils.isValid(bakeCycleBakeAgeingSaveDto.getAgeingHistoryInfoList())){
            List<String> snList = bakeCycleBakeAgeingSaveDto.getAgeingHistoryInfoList().stream().filter(ageingHistoryInfoDTO -> net.airuima.util.ValidateUtils.isValid(ageingHistoryInfoDTO.getSn()))
                    .map(AgeingHistoryInfoDTO::getSn).collect(Collectors.toList());
            if (net.airuima.util.ValidateUtils.isValid(snList)){
                List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snList, net.airuima.constant.Constants.LONG_ZERO);
                if (net.airuima.util.ValidateUtils.isValid(snWorkStatuses)){
                    List<AgeingHistoryInfoDTO> ageingHistoryInfoDTOList = bakeCycleBakeAgeingSaveDto.getAgeingHistoryInfoList().stream()
                            .map(ageingHistoryInfoDTO -> {
                                if (net.airuima.util.ValidateUtils.isValid(ageingHistoryInfoDTO.getSn())) {
                                    Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatuses.stream().filter(sn -> sn.getSn().equals(ageingHistoryInfoDTO.getSn())).findFirst();
                                    snWorkStatusOptional.ifPresent(snWorkStatus -> ageingHistoryInfoDTO.setReworkTime(snWorkStatus.getReworkTime()));
                                }
                                return ageingHistoryInfoDTO;
                            }).collect(Collectors.toList());
                    bakeCycleBakeAgeingSaveDto.setAgeingHistoryInfoList(ageingHistoryInfoDTOList);
                }
            }
        }
        return bakeCycleBakeAgeingSaveDto;
    }
}
