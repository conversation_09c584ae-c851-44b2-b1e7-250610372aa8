package net.airuima.rbase.service.procedure.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.scene.WorkCellStaffStatus;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.scene.WorkCellStaffStatusRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 员工工位状态Service
 * <AUTHOR>
 * @date 2022/9/15
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellStaffStatusService extends CommonJpaService<WorkCellStaffStatus> {
    private final String WORK_CELL_STAFF_STATUS_ENTITY_GRAPH = "workCellStaffStatusEntityGraph";
    private final WorkCellStaffStatusRepository workCellStaffStatusRepository;

    public WorkCellStaffStatusService(WorkCellStaffStatusRepository workCellStaffStatusRepository) {
        this.workCellStaffStatusRepository = workCellStaffStatusRepository;
    }

    @Override
    @FetchMethod
    public Page<WorkCellStaffStatus> find(Specification<WorkCellStaffStatus> spec, Pageable pageable) {
        return workCellStaffStatusRepository.findAll(spec,pageable,new NamedEntityGraph("workCellStaffStatusEntityGraph"));
    }

    @Override
    @FetchMethod
    public List<WorkCellStaffStatus> find(Specification<WorkCellStaffStatus> spec) {
        return workCellStaffStatusRepository.findAll(spec,new NamedEntityGraph("workCellStaffStatusEntityGraph"));
    }

    @Override
    @FetchMethod
    public Page<WorkCellStaffStatus> findAll(Pageable pageable) {
        return workCellStaffStatusRepository.findAll(pageable,new NamedEntityGraph("workCellStaffStatusEntityGraph"));
    }

    /**
     * 重置最新登陆时间为当前时间
     * @param id 状态ID
     */
    public WorkCellStaffStatus resetLatestLoginTime(long id){
        WorkCellStaffStatus workCellStaffStatus = workCellStaffStatusRepository.getReferenceById(id);
        workCellStaffStatus.setLatestLoginTime(LocalDateTime.now());
        return workCellStaffStatusRepository.save(workCellStaffStatus);
    }
}
