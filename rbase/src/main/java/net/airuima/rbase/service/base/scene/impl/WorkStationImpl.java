package net.airuima.rbase.service.base.scene.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkStation;
import net.airuima.rbase.dto.scene.WorkLineDTO;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkStationRepository;
import net.airuima.rbase.service.base.scene.api.IWorkStationService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WorkStationImpl implements IWorkStationService {

    @Autowired
    private WorkStationRepository workStationRepository;
    @Autowired
    private WorkCellRepository workCellRepository;

    /**
     * 通过生产线主键ID获取工站及对应工位信息
     * @param workLineId 生产线主键ID
     * @return java.util.List<net.airuima.rbase.dto.scene.WorkLineDTO.WorkStationInfo> 工站工位信息
     */
    @Override
    public List<WorkLineDTO.WorkStationInfo> findWorStationWorkCellInfo(Long workLineId) {
        List<WorkLineDTO.WorkStationInfo> workStationInfoList = Lists.newArrayList();
        //根据生产线获取所有工站
        Optional<List<WorkStation>> workStationListOption = Optional.ofNullable(workStationRepository.findByWorkLineIdAndDeleted(workLineId, Constants.LONG_ZERO));
        workStationListOption.ifPresent(workStations -> workStations.forEach(workStation -> {
            WorkLineDTO.WorkStationInfo workStationInfo = MapperUtils.map(workStation, WorkLineDTO.WorkStationInfo.class);
            workStationInfo.setWorkLineId(workLineId);
            //根据工站获取所有工位
            List<WorkCell> workCellList = workCellRepository.findByWorkStationIdAndDeletedOrderByOrderNumberAsc(workStation.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(workCellList)) {
                List<WorkLineDTO.WorkCellInfo> workCellInfoList = MapperUtils.mapAll(workCellList, WorkLineDTO.WorkCellInfo.class);
                workCellInfoList.forEach(workCellInfo -> workCellInfo.setWorkStationId(workStation.getId()));
                workStationInfo.setWorkCellInfoList(workCellInfoList);
            }
            workStationInfoList.add(workStationInfo);
        }));
        return workStationInfoList;
    }
}
