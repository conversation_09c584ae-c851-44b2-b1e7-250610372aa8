package net.airuima.rbase.service.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.batch.ReworkGetSubWsDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/12/27
 */
@Schema(description = "工单工序生产不良项目信息")
public class WsStepUnqualifiedItemDTO extends BaseDTO {

    /**
     * 获取待生成在线返修单的不良信息DTO列表
     */
    @Schema(description = "工序生产不良项目信息",type = "object")
    private ReworkGetSubWsDTO reworkGetSubWsDto;

    public WsStepUnqualifiedItemDTO(){

    }

    public WsStepUnqualifiedItemDTO(String status, ReworkGetSubWsDTO reworkGetSubWsDto) {
        super(status);
        this.reworkGetSubWsDto = reworkGetSubWsDto;
    }

    public WsStepUnqualifiedItemDTO(String status, String message) {
        super(status,message);
    }

    public WsStepUnqualifiedItemDTO(String status, String message, ReworkGetSubWsDTO reworkGetSubWsDto) {
        super(status, message);
        this.reworkGetSubWsDto = reworkGetSubWsDto;
    }

    public ReworkGetSubWsDTO getReworkGetSubWsDto() {
        return reworkGetSubWsDto;
    }

    public WsStepUnqualifiedItemDTO setReworkGetSubWsDto(ReworkGetSubWsDTO reworkGetSubWsDto) {
        this.reworkGetSubWsDto = reworkGetSubWsDto;
        return this;
    }
}
