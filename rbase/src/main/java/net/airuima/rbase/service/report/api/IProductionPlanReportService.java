package net.airuima.rbase.service.report.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO;

@FuncDefault
public interface IProductionPlanReportService {

    /**
     * 获取数字车间-产线统计信息
     * @param workLineStatisticsDTO 数字车间-产线统计参数
     * @return net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO 获取数字车间-产线统计信息
     */
    @FuncInterceptor("ProductionPlan")
    default WorkLineStatisticsDTO getOrgWorkLineIdProductionPlanAchieveRate(WorkLineStatisticsDTO workLineStatisticsDTO){
        return workLineStatisticsDTO;
    }

}
