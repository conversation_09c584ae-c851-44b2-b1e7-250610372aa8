package net.airuima.rbase.service.procedure.aps;

import io.micrometer.common.util.StringUtils;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.*;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.aps.SaleOrderDetailInfo;
import net.airuima.rbase.dto.aps.SaleOrderIssuedDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncSaleOrderDTO;
import net.airuima.rbase.integrate.rmps.IWorkSheetSyncMpsService;
import net.airuima.rbase.integrate.rwms.IShipmentOrderSyncWmsService;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.*;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.plugin.ISaleOrderService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.service.report.WorkSheetStepStatisticsService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderCreateDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderDetailDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderGetDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Service
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SaleOrderService extends CommonJpaService<SaleOrder> {

    private final SaleOrderRepository saleOrderRepository;
    private final WorkLineRepository workLineRepository;
    private final WorkSheetRepository workSheetRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepRepository stepRepository;
    private final PedigreeRepository pedigreeRepository;
    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private IWorkSheetSyncMpsService[] workSheetSyncMpsFeignClients;
    @Autowired
    private IShipmentOrderSyncWmsService[] shipmentOrderSyncWmsServices;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private CascadeWorkSheetRepository cascadeWorkSheetRepository;
    @Autowired
    private SaleOrderDetailRepository saleOrderDetailRepository;
    @Autowired
    private WsSaleOrderDetailRepository wsSaleOrderDetailRepository;
    @Autowired
    private ISaleOrderService[] saleOrderServices;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private PlannedOrderService plannedOrderService;

    public SaleOrderService(SaleOrderRepository saleOrderRepository, WorkLineRepository workLineRepository, WorkSheetRepository workSheetRepository, WorkFlowRepository workFlowRepository
            , StepRepository stepRepository, PedigreeRepository pedigreeRepository) {
        this.saleOrderRepository = saleOrderRepository;
        this.workLineRepository = workLineRepository;
        this.workSheetRepository = workSheetRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
        this.pedigreeRepository = pedigreeRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SaleOrder> find(Specification<SaleOrder> spec, Pageable pageable) {
        Page<SaleOrder> saleOrderPage = saleOrderRepository.findAll(spec, pageable);
        if (saleOrderPage != null && !saleOrderPage.isEmpty()) {
            List<SaleOrder> saleOrders = saleOrderPage.getContent();
            loadSaleOrderDetailsInBatch(saleOrders);
        }
        return saleOrderPage;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SaleOrder> find(Specification<SaleOrder> spec) {
        List<SaleOrder> saleOrders = saleOrderRepository.findAll(spec);
        if (ValidateUtils.isValid(saleOrders)) {
            loadSaleOrderDetailsInBatch(saleOrders);
        }
        return saleOrders;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SaleOrder> findAll(Pageable pageable) {
        Page<SaleOrder> saleOrderPage = saleOrderRepository.findAll(pageable);
        if (saleOrderPage != null && !saleOrderPage.isEmpty()) {
            List<SaleOrder> saleOrders = saleOrderPage.getContent();
            loadSaleOrderDetailsInBatch(saleOrders);
        }
        return saleOrderPage;
    }

    /**
     * 批量加载销售订单详情
     *
     * @param saleOrders 销售订单列表
     */
    private void loadSaleOrderDetailsInBatch(List<SaleOrder> saleOrders) {
        if (ValidateUtils.isValid(saleOrders)) {
            // 1. 提取所有订单ID
            List<Long> saleOrderIds = saleOrders.stream()
                    .map(SaleOrder::getId)
                    .collect(Collectors.toList());

            // 2. 一次性查询所有订单的详情
            List<SaleOrderDetail> allDetails = saleOrderDetailRepository.findBySaleOrderIdInAndDeleted(saleOrderIds, Constants.LONG_ZERO);

            // 3. 按订单ID分组
            Map<Long, List<SaleOrderDetail>> detailMap = new HashMap<>();
            for (SaleOrderDetail detail : allDetails) {
                Long orderId = detail.getSaleOrder().getId();
                if (!detailMap.containsKey(orderId)) {
                    detailMap.put(orderId, new ArrayList<>());
                }
                detailMap.get(orderId).add(detail);
            }

            // 4. 为每个订单设置其详情
            for (SaleOrder order : saleOrders) {
                List<SaleOrderDetail> details = detailMap.getOrDefault(order.getId(), new ArrayList<>());
                order.setSaleOrderDetailList(details);
            }
        }
    }

    /**
     * 订单下发
     *
     * @param saleOrderIssuedDTO 订单下发
     * @date 2022-12-21
     * <AUTHOR>
     */
    public BaseDTO orderIssued(SaleOrderIssuedDTO saleOrderIssuedDTO) {
        if (LocalDate.now().isAfter(saleOrderIssuedDTO.getPlanStartDate()) || LocalDate.now().isAfter(saleOrderIssuedDTO.getPlanEndDate())) {
            throw new ResponseException("error.planEndDateOrStartDateLessThanCurrentTime", "计划时间不能小于当前时间");
        }

        Pedigree pedigree = pedigreeRepository.findByIdAndDeleted(saleOrderIssuedDTO.getPedigreeId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.pedigreeNotExist", "产品谱系不存在"));
        WorkLine workLine = workLineRepository.findByIdAndDeleted(saleOrderIssuedDTO.getWorkLineId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.workLineNotExist", "生产线不存在"));
        WorkFlow workFlow = workFlowRepository.findByIdAndDeleted(saleOrderIssuedDTO.getWorkFlowId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.workFlowStepNotExist", "工艺路线不存在"));
        WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
        if (CollectionUtils.isEmpty(workFlowDTO.getStepDtoList())) {
            throw new ResponseException("error.workFlowStepNotExist", "工艺路线【" + workFlow.getName() + "】未绑定工序");
        }
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigree);

        List<WorkSheet> workSheets = new ArrayList<>();
        if (ValidateUtils.isValid(saleOrderIssuedDTO.getWorkSheetInfos())) {
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            saleOrderIssuedDTO.getWorkSheetInfos().forEach(workSheetInfo -> {
                String serialNumber = workSheetInfo.getSerialNumber();
                if (!ValidateUtils.isValid(serialNumber)) {
                    serialNumber = saleOrderServices[0].generateWsSaleOrderSerialNumber(pedigree,workLine);
                }
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
                if (workSheetOptional.isPresent()) {
                    throw new ResponseException("error.serialNumberIsExist", "工单号已存在");
                }

                WorkSheet workSheet = new WorkSheet();
                workSheet.setSerialNumber(serialNumber);
                workSheet.setNumber(workSheetInfo.getNumber());
                workSheet.setPlanStartDate(saleOrderIssuedDTO.getPlanStartDate().atStartOfDay());
                workSheet.setPlanEndDate(saleOrderIssuedDTO.getPlanEndDate().atStartOfDay());
                workSheet.setCategory(Constants.INT_ONE);
                workSheet.setStatus(Constants.INT_ZERO);
                workSheet.setNote(workSheetInfo.getNote());
                workSheet.setOrganizationId(workLine.getOrganizationId());
                workSheet.setBomInfoId(saleOrderIssuedDTO.getBomInfoId());
                workSheet.setPedigree(pedigree);
                workSheet.setWorkFlow(workFlow);
                workSheet.setWorkLine(workLine);
                workSheet.setGenerateSubWsStatus(Constants.INT_ZERO);
                workSheet.setDownGradeNumber(Constants.INT_ZERO);
                workSheet.setDeleted(Constants.LONG_ZERO);
                WorkSheet fianlWorkSheet = workSheetRepository.save(workSheet);
                //保存工单与销售订单的关系
                saleOrderServices[0].generateWsSaleOrder(fianlWorkSheet, workSheetInfo.getSaleOrderDetailInfoList());
                //更新计划下单列表状态
                plannedOrderService.updatePlanOrdersStatus(workSheetInfo.getSaleOrderDetailInfoList().stream()
                        .map(SaleOrderDetailInfo::getSaleOrderDetailId).distinct().toList());
                //工单快照
                List<WsStep> wsSteps = workSheetService.saveWsSteps(subWsProductionMode, workSheet, workFlow, workSheet.getPedigree(), workFlowDTO.getStepDtoList());
                //工单投料单
                List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(saleOrderIssuedDTO.getBomInfoId());
                wsMaterialServices[0].saveWsMaterial(fianlWorkSheet, bomDtoList);

                //如果分单配置存在且子工单投产则自动分单
                if (null != pedigreeConfig && pedigreeConfig.getIsEnable() && subWsProductionMode && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) {
                    subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                            pedigreeConfig.getSplitNumber(), Boolean.TRUE);
                }
                if (!subWsProductionMode){
                    //初始化第一个工序待做数据，便于工作台展示
                    nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(workSheet.getId(),workSheet,null,wsSteps,null,Boolean.FALSE);
                    //初始化更新生产在制看板数据
                    workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet, null, subWsProductionMode);
                }
                //保存可能的级联的下级工单信息
                List<WorkSheet> subordinateWorkSheetList = this.saveCascadeWorkSheet(subWsProductionMode, fianlWorkSheet, workSheetInfo.getCascadeWorkSheetInfoList());
                workSheets.add(fianlWorkSheet);
                if (!CollectionUtils.isEmpty(subordinateWorkSheetList)) {
                    workSheets.addAll(subordinateWorkSheetList);
                }
            });
        }
        if (ValidateUtils.isValid(workSheets)) {
            for (WorkSheet workSheet : workSheets) {
                // 同步工单信息到MPS
                workSheetSyncMpsFeignClients[0].syncMpsWorkSheet(workSheet.getId());
                // 同步工单信息到WMS
                shipmentOrderSyncWmsServices[0].syncShipmentOrder(workSheet);
            }
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 保存级联下单的下级工单信息
     *
     * @param subWsProductionMode      投产模式
     * @param superiorWorkSheet        上级工单
     * @param cascadeWorkSheetInfoList 下级工单下单参数列表
     * @return List<WorkSheet> 下级工单列表
     */
    public List<WorkSheet> saveCascadeWorkSheet(Boolean subWsProductionMode, WorkSheet superiorWorkSheet,
                                                List<SaleOrderIssuedDTO.WorkSheetInfo.CascadeWorkSheetInfo> cascadeWorkSheetInfoList) {
        if (CollectionUtils.isEmpty(cascadeWorkSheetInfoList)) {
            return null;
        }
        List<WorkSheet> subordinateWorkSheet = new ArrayList<>();
        cascadeWorkSheetInfoList.forEach(cascadeWorkSheetInfo -> {
            String serialNumber = cascadeWorkSheetInfo.getSerialNumber();
            WorkLine workLine = workLineRepository.getReferenceById(cascadeWorkSheetInfo.getWorkLineId());
            if (!ValidateUtils.isValid(serialNumber)) {
                SerialNumberDTO serialNumberDTO = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET, null, workLine.getOrganizationId());
                serialNumber = rbaseSerialNumberProxy.generate(serialNumberDTO);
            }
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
            if (workSheetOptional.isPresent()) {
                throw new ResponseException("error.serialNumberIsExist", "工单号已存在");
            }
            Pedigree pedigree = pedigreeRepository.getReferenceById(cascadeWorkSheetInfo.getPedigreeId());
            WorkFlow workFlow = workFlowRepository.getReferenceById(cascadeWorkSheetInfo.getWorkFlowId());
            WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
            if (CollectionUtils.isEmpty(workFlowDTO.getStepDtoList())) {
                throw new ResponseException("error.workFlowStepNotExist", "工艺路线【" + workFlow.getName() + "未绑定工序");
            }
            for (StepDTO stepDto : workFlowDTO.getStepDtoList()) {
                Step step = stepRepository.getReferenceById(stepDto.getId());
                StepDTO stepConfigDto = commonService.findPedigreeStepConfig(null, pedigree, workFlow, step);
                if (null == stepConfigDto) {
                    throw new ResponseException("error.stepConfigNotExist", step.getCode() + "工序配置不存在");
                }
            }
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigree);
            WorkSheet workSheet = new WorkSheet();
            workSheet.setSerialNumber(serialNumber);
            workSheet.setNumber(cascadeWorkSheetInfo.getNumber());
            workSheet.setClientId(null);
            workSheet.setPlanStartDate(cascadeWorkSheetInfo.getPlanStartDate().atStartOfDay());
            workSheet.setPlanEndDate(cascadeWorkSheetInfo.getPlanEndDate().atStartOfDay());
            workSheet.setCategory(Constants.INT_ONE);
            workSheet.setStatus(Constants.INT_ZERO);
            workSheet.setOrganizationId(workLine.getOrganizationId());
            workSheet.setBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            workSheet.setPedigree(pedigree);
            workSheet.setPriority(superiorWorkSheet.getPriority());
            workSheet.setWorkFlow(workFlow);
            workSheet.setWorkLine(workLine);
            workSheet.setGenerateSubWsStatus(Constants.INT_ZERO);
            workSheet.setDownGradeNumber(Constants.INT_ZERO);
            workSheet.setDeleted(Constants.LONG_ZERO);
            workSheet.setDeliveryDate(superiorWorkSheet.getDeliveryDate());
            WorkSheet fianlWorkSheet = workSheetRepository.save(workSheet);
            workSheetService.saveWsSteps(subWsProductionMode, workSheet, workFlow, workSheet.getPedigree(), workFlowDTO.getStepDtoList());
            List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            wsMaterialServices[0].saveWsMaterial(fianlWorkSheet, bomDtoList);
            //如果分单配置存在且子工单投产则自动分单
            if (null != pedigreeConfig && pedigreeConfig.getIsEnable() && subWsProductionMode && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) {
                subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                        pedigreeConfig.getSplitNumber(), Boolean.TRUE);
            }
            CascadeWorkSheet cascadeWorkSheet = new CascadeWorkSheet(superiorWorkSheet, workSheet);
            cascadeWorkSheet.setDeleted(Constants.LONG_ZERO);
            cascadeWorkSheetRepository.save(cascadeWorkSheet);
            subordinateWorkSheet.add(fianlWorkSheet);
        });
        return subordinateWorkSheet;
    }

    /**
     * 新增销售订单
     *
     * @param saleOrderCreateDto 新增销售订单详情
     * @return SaleOrder
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    public SaleOrder saveInstance(SaleOrderCreateDTO saleOrderCreateDto) {

        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(saleOrderCreateDto.getSerialNumber(), Constants.LONG_ZERO);
        if (LocalDate.now().isAfter(saleOrderCreateDto.getDeliveryDate())) {
            throw new ResponseException("error.deliveryDateIsOverNow", "交付日期必须大于等于当前日期");
        }
        SaleOrder saleOrder = null;
        if (saleOrderOptional.isEmpty()) {
            saleOrder = saleOrderOptional.orElse(new SaleOrder());
            BeanUtils.copyProperties(saleOrderCreateDto, saleOrder);
            saleOrder = saleOrderRepository.save(saleOrder);
        } else {
            saleOrder = saleOrderOptional.get();
            if (!saleOrder.getId().equals(saleOrderCreateDto.getSaleOrderId())) {
                throw new ResponseException("error.serialNumberIsExist", "销售订单号已存在禁止修改");
            }
            //验证销售订单修改属性
            saleOrder.setItemCode(saleOrderCreateDto.getItemCode())
                    .setPriority(saleOrderCreateDto.getPriority()).setCategory(saleOrderCreateDto.getCategory())
                    .setClientId(saleOrderCreateDto.getClientId()).setDeliveryDate(saleOrderCreateDto.getDeliveryDate());
            //扩展属性
            saleOrder.setNote(saleOrderCreateDto.getNote());
            saleOrder.setCustom1(saleOrderCreateDto.getCustom1());
            saleOrder.setCustom2(saleOrderCreateDto.getCustom2());
            saleOrder.setCustom3(saleOrderCreateDto.getCustom3());
            saleOrder.setCustom4(saleOrderCreateDto.getCustom4());
            saleOrder.setCustom5(saleOrderCreateDto.getCustom5());
            saleOrder = saleOrderRepository.save(saleOrder);
        }

        //保存销售订单详情变更
        saveSaleOrderDetail(saleOrderCreateDto.getSaleOrderDetailDtoList(), saleOrder);
        return saleOrder;
    }

    /**
     * 保存销售订单详情
     *
     * @param saleOrderDetailDtoList 销售订单详情
     * @param saleOrder              销售订单
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    public void saveSaleOrderDetail(List<SaleOrderDetailDTO> saleOrderDetailDtoList, SaleOrder saleOrder) {
        //验证销售订单详情变更
        validSaleOrderDetailProcess(saleOrderDetailDtoList, saleOrder);
        //保存销售订单详情
        if (ValidateUtils.isValid(saleOrderDetailDtoList)) {
            saleOrderDetailDtoList.stream().distinct().forEach(saleOrderDetailDto -> {
                SaleOrderDetail saleOrderDetail = saleOrderDetailRepository.findBySaleOrderIdAndPedigreeIdAndDeleted(saleOrder.getId(),
                        saleOrderDetailDto.getPedigreeId(), Constants.LONG_ZERO).orElse(new SaleOrderDetail());

                saleOrderDetail.setContractNo(saleOrderDetailDto.getContractNo())
                        .setPedigree(new Pedigree(saleOrderDetailDto.getPedigreeId()))
                        .setSaleOrder(saleOrder).setNumber(saleOrderDetailDto.getNumber())
                        .setPlanStartDate(saleOrderDetailDto.getPlanStartDate()).setPlanEndDate(saleOrderDetailDto.getPlanEndDate());
                saleOrderDetailRepository.save(saleOrderDetail);
            });
            saleOrder.setNumber(saleOrderDetailDtoList.stream().mapToInt(SaleOrderDetailDTO::getNumber).sum());
            saleOrderRepository.save(saleOrder);
        }
    }

    /**
     * 验证销售订单详情变更
     *
     * @param saleOrderDetailDtoList 销售订单详情
     * @param saleOrder              销售订单
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    public void validSaleOrderDetailProcess(List<SaleOrderDetailDTO> saleOrderDetailDtoList, SaleOrder saleOrder) {

        if (!ValidateUtils.isValid(saleOrderDetailDtoList)) {
            throw new ResponseException("error.saleOrderDetailNotExist", "销售订单详情不存在");
        }

        if (saleOrderDetailDtoList.size() != saleOrderDetailDtoList.stream().distinct().toList().size()) {
            throw new ResponseException("error.saleOrderDetailIsExist", "销售订单详情存在重复产品谱系");
        }
        List<SaleOrderDetail> saleOrderDetails = saleOrderDetailRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(saleOrderDetails)) {
            return;
        }
        //检查是否存在删除的销售订单详情，如果存在则需要判断当前详情是否已下单，已下单禁止删除
        List<SaleOrderDetail> logicDeletedDetails = saleOrderDetails.stream().filter(saleOrderDetail -> saleOrderDetailDtoList.stream().noneMatch(saleOrderDetailDto ->
                saleOrderDetailDto.getPedigreeId().equals(saleOrderDetail.getPedigree().getId()))).collect(Collectors.toList());

        if (ValidateUtils.isValid(logicDeletedDetails)) {
            List<WsSaleOrderDetail> wsSaleOrderDetails = wsSaleOrderDetailRepository.findBySaleOrderDetailIdInAndDeleted(logicDeletedDetails.stream().map(SaleOrderDetail::getId).toList()
                    , Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsSaleOrderDetails)) {
                String errormessage = wsSaleOrderDetails.stream().map(wsSaleOrderDetail -> wsSaleOrderDetail.getWorkSheet().getPedigree().getName())
                        .distinct().collect(Collectors.joining(","));
                throw new ResponseException("error.saleOrderDetailIsIssued", "销售订单产品谱系" + errormessage + "已下单，禁止删除");
            }
        }
        //删除销售订单详情
        saleOrderDetailRepository.logicDelete(logicDeletedDetails);
    }


    /**
     * 同步订单
     *
     * @param syncSaleOrderList
     * <AUTHOR>
     * @date 2023/3/17
     */
    public List<SyncResultDTO> syncSaleOrder(List<SyncSaleOrderDTO> syncSaleOrderList) {
        List<SyncResultDTO> syncResultDTOList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncSaleOrderList)) {
            syncSaleOrderList.forEach(syncSaleOrder -> {
                SyncResultDTO syncResultDto = new SyncResultDTO(syncSaleOrder.getId(), Constants.INT_ONE, "");
                switch (syncSaleOrder.getOperate()) {
                    case Constants.INT_TWO: // 删除
                        processDeleteOrder(syncSaleOrder, syncResultDto);
                        break;
                    case Constants.INT_ZERO: // 新增
                        processCreateOrder(syncSaleOrder, syncResultDto);
                        break;
                    case Constants.INT_ONE: // 修改
                        processUpdateOrder(syncSaleOrder, syncResultDto);
                        break;
                    default:
                        syncResultDto.setStatus(Constants.INT_TWO).setMessage("未知操作类型");
                }
                syncResultDTOList.add(syncResultDto);
            });
        }
        return syncResultDTOList;
    }

    /**
     * 处理订单修改
     */
    private void processUpdateOrder(SyncSaleOrderDTO syncSaleOrder, SyncResultDTO syncResultDto) {
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(syncSaleOrder.getSerialNumber(), Constants.LONG_ZERO);
        if (saleOrderOptional.isEmpty()) {
            syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号不存在");
            return;
        }
        SaleOrder saleOrder = saleOrderOptional.get();
        if (hasRelatedWsSaleOrderDetails(saleOrder.getId())) {
            String errorMessage = getRelatedWsSaleOrderDetailNames(saleOrder.getId());
            syncResultDto.setStatus(Constants.INT_TWO).setMessage("销售订单物料" + errorMessage + "已下单，无法修改");
            return;
        }
        saleOrder.setItemCode(syncSaleOrder.getItemCode())
                .setDeliveryDate(syncSaleOrder.getDeliveryDate())
                .setNumber(syncSaleOrder.getNumber())
                .setNote(syncSaleOrder.getNote());
        // 设置客户ID
        setClientIdIfExists(syncSaleOrder.getClientCode(), saleOrder);
        saleOrderRepository.save(saleOrder);

        // 清空销售订单详情
        List<SaleOrderDetail> saleOrderDetails = saleOrderDetailRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        saleOrderDetailRepository.logicDelete(saleOrderDetails);
        if (ValidateUtils.isValid(syncSaleOrder.getSaleOrderDetailDTOList())) {
            List<SaleOrderDetail> saleOrderDetailList = createSaleOrderDetails(saleOrder, syncSaleOrder.getSaleOrderDetailDTOList());
            saleOrderDetailRepository.saveAll(saleOrderDetailList);
        }
    }

    /**
     * 处理订单新增
     */
    private void processCreateOrder(SyncSaleOrderDTO syncSaleOrder, SyncResultDTO syncResultDto) {
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(syncSaleOrder.getSerialNumber(), Constants.LONG_ZERO);
        if (saleOrderOptional.isPresent()) {
            syncResultDto.setStatus(Constants.INT_TWO).setMessage("销售订单已存在");
            return;
        }

        if (ValidateUtils.isValid(syncSaleOrder.getSaleOrderDetailDTOList())) {
            for (SyncSaleOrderDTO.SaleOrderDetailDTO saleOrderDetailDTO : syncSaleOrder.getSaleOrderDetailDTOList()) {
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(saleOrderDetailDTO.getPedigreeCode(), Constants.LONG_ZERO);
                if (pedigreeOptional.isEmpty()) {
                    syncResultDto.setStatus(Constants.INT_TWO).setMessage(saleOrderDetailDTO.getPedigreeCode() + "销售订单明细物料编码不存在");
                    return;
                }
            }
        }

        SaleOrder saleOrder = MapperUtils.map(syncSaleOrder, SaleOrder.class);
        saleOrder.setId(null);
        // 设置客户ID
        setClientIdIfExists(syncSaleOrder.getClientCode(), saleOrder);
        saleOrderRepository.save(saleOrder);

        if (ValidateUtils.isValid(syncSaleOrder.getSaleOrderDetailDTOList())) {
            List<SaleOrderDetail> saleOrderDetailList = createSaleOrderDetails(saleOrder, syncSaleOrder.getSaleOrderDetailDTOList());
            saleOrderDetailRepository.saveAll(saleOrderDetailList);
        }
    }

    /**
     * 处理订单删除
     */
    private void processDeleteOrder(SyncSaleOrderDTO syncSaleOrder, SyncResultDTO syncResultDto) {
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(syncSaleOrder.getSerialNumber(), Constants.LONG_ZERO);
        if (saleOrderOptional.isEmpty()) {
            syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号不存在");
            return;
        }
        SaleOrder saleOrder = saleOrderOptional.get();
        if (hasRelatedWsSaleOrderDetails(saleOrder.getId())) {
            syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号已下发工单不能删除");
            return;
        }
        // 删除销售订单
        saleOrderRepository.logicDelete(saleOrder);
        List<SaleOrderDetail> details = saleOrderDetailRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        // 删除销售订单详情
        if (ValidateUtils.isValid(details)) {
            saleOrderDetailRepository.logicDelete(details);
        }
    }

    /**
     * 创建销售订单详情列表
     */
    private List<SaleOrderDetail> createSaleOrderDetails(SaleOrder saleOrder, List<SyncSaleOrderDTO.SaleOrderDetailDTO> detailDTOs) {
        return detailDTOs.stream().map(detailDTO -> {
            SaleOrderDetail detail = new SaleOrderDetail();
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(detailDTO.getPedigreeCode(), Constants.LONG_ZERO);
            detail.setSaleOrder(saleOrder)
                    .setPedigree(pedigreeOptional.orElse(null))
                    .setNumber(detailDTO.getNumber())
                    .setProductionQuantity(detailDTO.getProductionQuantity())
                    .setFinishNumber(detailDTO.getFinishNumber())
                    .setPlanStartDate(detailDTO.getPlanStartDate())
                    .setPlanEndDate(detailDTO.getPlanEndDate());
            detail.setCustom1(detailDTO.getCustom1());
            detail.setCustom2(detailDTO.getCustom2());
            detail.setCustom3(detailDTO.getCustom3());
            detail.setCustom4(detailDTO.getCustom4());
            detail.setCustom5(detailDTO.getCustom5());
            return detail;
        }).toList();
    }

    /**
     * 设置客户ID（如果存在）
     */
    private void setClientIdIfExists(String clientCode, SaleOrder saleOrder) {
        if (!ObjectUtils.isEmpty(clientCode)) {
            ClientDTO client = rbaseClientProxy.findByCodeAndDeleted(clientCode, Constants.LONG_ZERO);
            if (!ObjectUtils.isEmpty(client)) {
                saleOrder.setClientId(client.getId());
            }
        }
    }

    /**
     * 检查订单是否有相关WsSaleOrderDetail
     */
    private boolean hasRelatedWsSaleOrderDetails(Long saleOrderId) {
        List<SaleOrderDetail> details = saleOrderDetailRepository.findBySaleOrderIdAndDeleted(saleOrderId, Constants.LONG_ZERO);
        List<Long> detailIds = details.stream().map(SaleOrderDetail::getId).toList();
        return ValidateUtils.isValid(wsSaleOrderDetailRepository.findBySaleOrderDetailIdInAndDeleted(detailIds, Constants.LONG_ZERO));
    }

    /**
     * 获取相关WsSaleOrderDetail的物料名称
     */
    private String getRelatedWsSaleOrderDetailNames(Long saleOrderId) {
        List<SaleOrderDetail> details = saleOrderDetailRepository.findBySaleOrderIdAndDeleted(saleOrderId, Constants.LONG_ZERO);
        List<Long> detailIds = details.stream().map(SaleOrderDetail::getId).toList();

        return wsSaleOrderDetailRepository.findBySaleOrderDetailIdInAndDeleted(detailIds, Constants.LONG_ZERO)
                .stream()
                .map(wsDetail -> wsDetail.getWorkSheet().getPedigree().getName())
                .distinct()
                .collect(Collectors.joining(","));
    }

    /**
     * 获取销售订单详情
     *
     * @param saleOrderId 销售订单id
     * @return SaleOrderGetDTO
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    public SaleOrderGetDTO getSaleOrderDetail(Long saleOrderId) {
        SaleOrder saleOrder = saleOrderRepository.findByIdAndDeleted(saleOrderId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.saleOrderNotExist", "销售订单不存在"));

        SaleOrderGetDTO saleOrderGetDto = new SaleOrderGetDTO();
        saleOrderGetDto.setSaleOrder(saleOrder);

        //获取销售订单详情
        List<SaleOrderDetail> saleOrderDetails = saleOrderDetailRepository.findBySaleOrderIdAndDeletedOrderByIdAsc(saleOrderId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(saleOrderDetails)) {
            saleOrderGetDto.setSaleOrderDetailDtoList(saleOrderDetails);
        }
        return saleOrderGetDto;
    }

    /**
     * 根据条件分页查询销售订单
     *
     * @param pageable 分页
     * @param type     类型:0按优先级排序desc，1 按交付时间排序 desc
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.rbase.domain.procedure.aps.SaleOrder>>> 销售订单列表
     */
    public List<SaleOrder> findByPageSort(Pageable pageable, Integer type) {
        Sort sort = null;
        if (type == 0) {
            sort = Sort.by(Sort.Direction.DESC, "priority");
        } else {
            sort = Sort.by(Sort.Direction.DESC, "deliveryDate");
        }
        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                sort
        );
        return Optional.of(saleOrderRepository.findAll(pageRequest)).map(Slice::getContent).orElse(Lists.newArrayList());
    }
}
