package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailMaterialBatchRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情物料批次Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnWorkDetailMaterialBatchService extends CommonJpaService<SnWorkDetailMaterialBatch> {
    private static final String SN_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH = "snWorkDetailMaterialBatchEntityGraph";
    private final SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;

    public SnWorkDetailMaterialBatchService(SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository) {
        this.snWorkDetailMaterialBatchRepository = snWorkDetailMaterialBatchRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<SnWorkDetailMaterialBatch> find(Specification<SnWorkDetailMaterialBatch> spec, Pageable pageable) {
        return snWorkDetailMaterialBatchRepository.findAll(spec, pageable,new NamedEntityGraph(SN_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<SnWorkDetailMaterialBatch> find(Specification<SnWorkDetailMaterialBatch> spec) {
        return snWorkDetailMaterialBatchRepository.findAll(spec,new NamedEntityGraph(SN_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<SnWorkDetailMaterialBatch> findAll(Pageable pageable) {
        return snWorkDetailMaterialBatchRepository.findAll(pageable,new NamedEntityGraph(SN_WORK_DETAIL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

}
