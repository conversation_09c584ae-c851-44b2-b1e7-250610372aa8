package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ReportTimeRangeEnum;
import net.airuima.rbase.constant.StepGroupCompletionCategoryEnum;
import net.airuima.rbase.constant.WorkSheetReportTypeEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.rbase.web.rest.report.dto.stepcompletion.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序完成Service
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepCompletionReportService {

    private final ProductionPlanRepository productionPlanRepository;

    private final StaffPerformService staffPerformService;

    /**
     * 工单
     */
    private final String WORK_SHEET = "workSheet";

    /**
     * 子工单
     */
    private final String SUB_WORK_SHEET = "subWorkSheet";

    /**
     * 报工时间
     */
    private final String RECORD_TIME = "recordTime";


    public StepCompletionReportService(ProductionPlanRepository pproductionPlanRepository, StaffPerformService staffPerformService) {
        this.productionPlanRepository = pproductionPlanRepository;
        this.staffPerformService = staffPerformService;
    }

    /**
     * 工序达成报表统计图表
     *
     * @param stepCompletionRequestDto 工序达成报表请求参数
     */
    public StepCompletionReportChartResultDTO stepCompletionReportChart(StepCompletionReportRequestDTO stepCompletionRequestDto) {
        // 计划时间类型解析
        parseTimeCategory(stepCompletionRequestDto);
        // 产品谱系id
        Long pedigreeId = stepCompletionRequestDto.getPedigreeId();
        // 工序组id
        Long stepGroupId = stepCompletionRequestDto.getStepGroupId();
        // 查询开始时间
        LocalDate startDate = stepCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = stepCompletionRequestDto.getEndDate();
        //报表类型
        Integer reportType = stepCompletionRequestDto.getReportType();
        // 获取图表数据
        StepCompletionReportChartResultDTO stepCompletionReportChartResultDto = getStepCompletionReportChartData(pedigreeId, stepGroupId, startDate, endDate, reportType);
        return stepCompletionReportChartResultDto;
    }

    /**
     * 获取工序达成图形数据(条形图和数子统计)
     *
     * @param pedigreeId  产品谱系id
     * @param stepGroupId 工序组id
     * @param startDate   查询开始时间
     * @param endDate     查询结束时间
     * @param reportType  报表类型
     * @return net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionReportChartResultDTO 工序达成图形数据
     */
    private StepCompletionReportChartResultDTO getStepCompletionReportChartData(Long pedigreeId, Long stepGroupId, LocalDate startDate, LocalDate endDate, Integer reportType) {
        StepCompletionReportChartResultDTO reportChartResultDto = new StepCompletionReportChartResultDTO();
        StepCompletionNumberQueryDTO stepCompletionNumberQueryData = productionPlanRepository.findStepCompletionChartNumberData(stepGroupId, pedigreeId, startDate, endDate, Constants.LONG_ZERO);
        Long planNumber = Optional.ofNullable(stepCompletionNumberQueryData).map(StepCompletionNumberQueryDTO::getPlanNumber).orElse(Constants.LONG_ZERO);
        Long actualNumber = Optional.ofNullable(stepCompletionNumberQueryData).map(StepCompletionNumberQueryDTO::getActualNumber).orElse(Constants.LONG_ZERO);
        double completionRate = 0d;
        // 计算达成比率
        if (Constants.LONG_ZERO != planNumber) {
            completionRate = BigDecimal.valueOf((float) (actualNumber * 100) / planNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        reportChartResultDto.setPlanNumber(planNumber).setActualNumber(actualNumber).setCompletionRate(completionRate);
        List<StepCompletionChartQueryDTO> stepCompletionChartDataList = productionPlanRepository.findStepCompletionChartData(stepGroupId, pedigreeId, startDate, endDate, Constants.LONG_ZERO);
        List<StepGroupCompletionInfoDTO> stepGroupCompletionInfoList = Lists.newLinkedList();
        List<StepGroupCompletionRateInfoDTO> stepGroupCompletionRateInfoList = Lists.newLinkedList();
        stepCompletionChartDataList.forEach(i -> {
            StepGroupCompletionInfoDTO planStepGroupCompletionInfo = new StepGroupCompletionInfoDTO();
            planStepGroupCompletionInfo.setNumber(i.getPlanNumber())
                    .setType(StepGroupCompletionCategoryEnum.PLAN.getKey())
                    .setName(i.getName());
            StepGroupCompletionInfoDTO actualStepGroupCompletionInfo = new StepGroupCompletionInfoDTO();
            actualStepGroupCompletionInfo.setNumber(i.getActualNumber())
                    .setType(StepGroupCompletionCategoryEnum.ACTUAL.getKey())
                    .setName(i.getName());
            stepGroupCompletionInfoList.add(planStepGroupCompletionInfo);
            stepGroupCompletionInfoList.add(actualStepGroupCompletionInfo);
            double rate = 0d;
            // 计算达成比率
            if (Constants.LONG_ZERO != i.getPlanNumber()) {
                rate = BigDecimal.valueOf((float) (i.getActualNumber() * 100) / i.getPlanNumber()).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            StepGroupCompletionRateInfoDTO stepGroupCompletionRateInfoDto = new StepGroupCompletionRateInfoDTO();
            stepGroupCompletionRateInfoDto.setRate(rate).setName(i.getName());
            stepGroupCompletionRateInfoList.add(stepGroupCompletionRateInfoDto);
        });
        reportChartResultDto.setStepGroupCompletionInfoList(stepGroupCompletionInfoList)
                .setStepGroupCompletionRateList(stepGroupCompletionRateInfoList);
        return reportChartResultDto;
    }

    /**
     * 工序达成报表表格数据
     *
     * @param stepCompletionRequestDto 工序达成报表请求参数
     */
    public StepCompletionReportTableResultDTO stepCompletionReportTable(StepCompletionReportRequestDTO stepCompletionRequestDto) {
        // 计划时间类型解析
        parseTimeCategory(stepCompletionRequestDto);
        // 产品谱系id
        Long pedigreeId = stepCompletionRequestDto.getPedigreeId();
        // 工序组id
        Long stepGroupId = stepCompletionRequestDto.getStepGroupId();
        // 查询开始时间
        LocalDate startDate = stepCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = stepCompletionRequestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = stepCompletionRequestDto.getExportStatus();
        // 报表类型
        Integer reportType = stepCompletionRequestDto.getReportType();
        //当前页
        Integer currentPage = stepCompletionRequestDto.getCurrentPage();
        // 分页大小
        Integer pageSize = stepCompletionRequestDto.getPageSize();
        return getStepCompletionReportTableResultDTO(pedigreeId, stepGroupId, startDate, endDate, exportStatus, currentPage, pageSize, reportType);
    }

    /**
     * 获取工序达成表格数据
     *
     * @param pedigreeId   产品谱系id
     * @param stepGroupId  工序组id
     * @param startDate    开始查询时间
     * @param endDate      结束查询时间
     * @param exportStatus 是否导出
     * @param currentPage  当前页
     * @param pageSize     每页大小
     * @param reportType   报表类型
     * @return net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionReportTableResultDTO 工序达成表格数据
     */
    private StepCompletionReportTableResultDTO getStepCompletionReportTableResultDTO(Long pedigreeId, Long stepGroupId, LocalDate startDate, LocalDate endDate, Boolean exportStatus, Integer currentPage, Integer pageSize, Integer reportType) {
        // 分页查询
        Specification<StaffPerform> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            // 查询报表类型
            if (WorkSheetReportTypeEnum.WORK_SHEET.getCategory() == reportType) {
                // 筛选工单投产
                Predicate subWorkSheetPredicate = criteriaBuilder.isNull(root.get(SUB_WORK_SHEET));
                predicateList.add(subWorkSheetPredicate);
                // 产品谱系筛选
                if (pedigreeId != null) {
                    Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                    predicateList.add(pedigreePredicate);
                }
            }
            if (WorkSheetReportTypeEnum.SUB_WORK_SHEET.getCategory() == reportType) {
                // 筛选子工单投产
                Predicate subWorkSheetPredicate = criteriaBuilder.isNotNull(root.get(SUB_WORK_SHEET));
                predicateList.add(subWorkSheetPredicate);
                // 产品谱系筛选
                if (pedigreeId != null) {
                    Predicate pedigreePredicate = criteriaBuilder.equal(root.get(SUB_WORK_SHEET).get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                    predicateList.add(pedigreePredicate);
                }
            }
            //实际范围查询
            if ( endDate != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class),
                        LocalDateTime.of(endDate, LocalTime.MAX)));
            }
            if (startDate != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class),
                        LocalDateTime.of(startDate, LocalTime.MIN)));
            }
            // 工序组筛选
            if (stepGroupId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("step").get("stepGroup").get("id"), stepGroupId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).orderBy(criteriaBuilder.desc(root.get(RECORD_TIME))).getRestriction();
        };
        // 工单集合
        List<StaffPerform> staffPerformList;
        Page<StaffPerform> staffPerformPage = null;
        //导出时 导出全部数据
        if (exportStatus) {
            staffPerformList = staffPerformService.find(specification);
        } else {
            //分页查询
            staffPerformPage = staffPerformService.find(specification, PageRequest.of(currentPage, pageSize));
            staffPerformList = Optional.ofNullable(staffPerformPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        StepCompletionReportTableResultDTO tableResultDto = new StepCompletionReportTableResultDTO();
        // 转换员工产量为工序达成统计看板表格数据
        List<StepCompletionReportTableItemDTO> stepCompletionReportTableItemList = covertToStaffPerformStatisticReportTableData(staffPerformList, reportType);
        tableResultDto.setStepCompletionReportTableItemList(stepCompletionReportTableItemList);
        // 设置分页数据
        tableResultDto.setCurrentPage(currentPage);
        tableResultDto.setPageSize(pageSize);
        tableResultDto.setCountSize(exportStatus ? Optional.ofNullable(staffPerformList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(staffPerformPage).map(s -> s.getTotalElements()).orElse(0L));
        return tableResultDto;
    }


    /**
     * 转换员工产量信息为工序完成表格明细数据
     *
     * @param staffPerformList 员工产量数据
     * @param reportType       报表类型
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.stepcompletion.StepCompletionReportTableItemDTO> 工序达成表格明细数据
     */
    private List<StepCompletionReportTableItemDTO> covertToStaffPerformStatisticReportTableData(List<StaffPerform> staffPerformList, Integer reportType) {
        List<StepCompletionReportTableItemDTO> stepCompletionReportTableItemList = Lists.newLinkedList();
        staffPerformList.forEach(s -> {
            StepCompletionReportTableItemDTO stepCompletionReportTableItemDto = new StepCompletionReportTableItemDTO();
            // 投产类型工单
            WorkSheet workSheet = null;
            // 子工单
            SubWorkSheet subWorkSheet = Optional.ofNullable(s).map(p -> p.getSubWorkSheet()).orElse(null);
            if (WorkSheetReportTypeEnum.WORK_SHEET.getCategory() == reportType) {
                workSheet = Optional.ofNullable(s).map(p -> p.getWorkSheet()).orElse(null);
            }
            // 投产类型子工单
            if (WorkSheetReportTypeEnum.SUB_WORK_SHEET.getCategory() == reportType) {
                workSheet = Optional.ofNullable(subWorkSheet).map(p -> p.getWorkSheet()).orElse(null);
            }
            // 合格数
            Long qualifiedNumber = Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO);
            // 不合格数
            Long unQualifiedNumber = Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO);
            //产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(i -> i.getPedigree()).orElse(null);
            //设置工序达成统计表格数据
            stepCompletionReportTableItemDto.setWorkOrderNumber(Optional.ofNullable(workSheet).map(i -> i.getSerialNumber()).orElse(null))
                    .setSubWorkOrderNumber(Optional.ofNullable(subWorkSheet).map(i -> i.getSerialNumber()).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(p -> p.getCode()).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(p -> p.getName()).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(p -> p.getSpecification()).orElse(null))
                    .setWorkFlowName(Optional.ofNullable(workSheet).map(w -> w.getWorkFlow()).map(f -> f.getName()).orElse(null))
                    .setOrganizationName(Optional.ofNullable(workSheet).map(w -> w.getOrganizationDto()).map(o -> o.getName()).orElse(null))
                    .setWorkLineName(Optional.ofNullable(workSheet).map(w -> w.getWorkLine()).map(l -> l.getName()).orElse(null))
                    .setStepCode(Optional.ofNullable(s).map(p -> p.getStep()).map(st -> st.getName()).orElse(null))
                    .setStepName(Optional.ofNullable(s).map(p -> p.getStep()).map(st -> st.getCode()).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getInputNumber())).orElse(null))
                    .setQualifiedNumber(qualifiedNumber)
                    .setStaffName(Optional.ofNullable(s).map(i -> i.getStaffDto()).map(d -> d.getName()).orElse(null))
                    .setUnQualifiedNumber(unQualifiedNumber);
            stepCompletionReportTableItemList.add(stepCompletionReportTableItemDto);
        });
        return stepCompletionReportTableItemList;
    }


    /**
     * 计划时间范围解析
     *
     * @param stepCompletionRequestDto 工序达成时间解析
     */
    private void parseTimeCategory(StepCompletionReportRequestDTO stepCompletionRequestDto) {
        // 计划时间类型
        Integer planFinishTimeCategory = stepCompletionRequestDto.getPlanFinishTimeCategory();
        // 查询开始时间
        LocalDate startDate = stepCompletionRequestDto.getStartDate();
        // 查询结束时间
        LocalDate endDate = stepCompletionRequestDto.getEndDate();
        if (Objects.isNull(startDate) && Objects.isNull(endDate) && Objects.nonNull(planFinishTimeCategory)) {
            // 今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                stepCompletionRequestDto.setStartDate(LocalDate.now());
                stepCompletionRequestDto.setEndDate(LocalDate.now());
            }
            // 本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                Long day = now.getDayOfWeek().getValue() - 1L;
                LocalDate weekStart = now.minusDays(day);
                stepCompletionRequestDto.setStartDate(weekStart);
                stepCompletionRequestDto.setEndDate(now);
            }
            // 本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDate monthStart = now.with(TemporalAdjusters.firstDayOfMonth());
                stepCompletionRequestDto.setStartDate(monthStart);
                stepCompletionRequestDto.setEndDate(now);
            }
        }
    }

}
