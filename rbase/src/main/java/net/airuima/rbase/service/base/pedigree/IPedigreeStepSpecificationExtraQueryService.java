package net.airuima.rbase.service.base.pedigree;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.query.QueryCondition;
import net.airuima.query.SearchFilter;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface IPedigreeStepSpecificationExtraQueryService {

    default List<SearchFilter> getExtraQueryConditionWhenGetAll(List<SearchFilter> lists){
        return lists;
    }

    default  List<QueryCondition> getExtraQueryConditionWhenSearchQuery(List<QueryCondition> queryConditions){
        return queryConditions;
    }
}
