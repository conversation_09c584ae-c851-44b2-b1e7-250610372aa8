package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.batch.WsStepDTO;
import net.airuima.query.QueryCondition;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工单定制工序Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsStepService extends CommonJpaService<WsStep> {
    private static final String WS_STEP_ENTITY_GRAPH = "wsStepEntityGraph";
    private final WsStepRepository wsStepRepository;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetRepository workSheetRepository;
    private final StepRepository stepRepository;

    public WsStepService(WsStepRepository wsStepRepository, BatchWorkDetailRepository batchWorkDetailRepository, SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository, StepRepository stepRepository) {
        this.wsStepRepository = wsStepRepository;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WsStep> find(Specification<WsStep> spec, Pageable pageable) {
        return wsStepRepository.findAll(spec, pageable,new NamedEntityGraph(WS_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<WsStep> find(Specification<WsStep> spec) {
        return wsStepRepository.findAll(spec,new NamedEntityGraph(WS_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WsStep> findAll(Pageable pageable) {
        return wsStepRepository.findAll(pageable,new NamedEntityGraph(WS_STEP_ENTITY_GRAPH));
    }

    /**
     * 根据总工单ID获取定制工序信息
     *
     * @param workSheetId 总工单ID
     * @return
     */
    @Transactional(readOnly = true)
    public List<WsStep> findByWorkSheetId(Long workSheetId) {
        return wsStepRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
    }

    /**
     * 根据子工单ID获取定制工序信息
     *
     * @param subWorkSheetId 子工单ID
     * @return
     */
    @Transactional(readOnly = true)
    public List<WsStep> findBySubWorkSheetId(Long subWorkSheetId) {
        return wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
    }

    /**
     * 修改工单的定制投产工序
     *
     * @param wsStepDto
     * @return
     */
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#productWorkSheetId")
    public ResponseEntity<String> updateInstance(WsStepDTO wsStepDto,Long productWorkSheetId) {
        //对于已投产的工单不允许进行修改定制工序
        if (null != wsStepDto.getWorkSheet().getId()) {
            Long number = batchWorkDetailRepository.countBySubWorkSheetWorkSheetIdAndDeleted(wsStepDto.getWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (null != number && number.intValue() > net.airuima.constant.Constants.INT_ZERO) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsStep.class.getSimpleName()), "updateFailed", "工单已经投产")).build();
            }
        } else if (null != wsStepDto.getSubWorkSheet().getId()) {
            Long number = batchWorkDetailRepository.countBySubWorkSheetIdAndDeleted(wsStepDto.getSubWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (null != number && number.intValue() > net.airuima.constant.Constants.INT_ZERO) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(WsStep.class.getSimpleName()), "updateFailed", "工单已经投产")).build();
            }
        }
        List<WsStep> originWsStepList = null;
        if(Objects.nonNull(wsStepDto.getSubWorkSheet()) && Objects.nonNull(wsStepDto.getSubWorkSheet().getId())){
            originWsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(wsStepDto.getSubWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO);
        }
        if(CollectionUtils.isEmpty(originWsStepList)){
            originWsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(wsStepDto.getWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO);
        }
        if (net.airuima.util.ValidateUtils.isValid(wsStepDto.getStepDtoList())) {
            if(Objects.nonNull(wsStepDto.getSubWorkSheet()) && Objects.nonNull(wsStepDto.getSubWorkSheet().getId())){
                wsStepRepository.deleteBySubWorkSheetId(wsStepDto.getSubWorkSheet().getId());
            }else {
                wsStepRepository.deleteByWorkSheetId(wsStepDto.getWorkSheet().getId());
            }
            ModelMapper modelMapper = new ModelMapper();
            List<WsStep> finalOriginWsStepList = originWsStepList;
            wsStepDto.getStepDtoList().forEach(stepDto -> {
                WsStep wsStep = new WsStep();
                WorkFlow workFlow = Objects.nonNull(wsStepDto.getSubWorkSheet()) ?
                        wsStepDto.getSubWorkSheet().getWorkFlow() : wsStepDto.getWorkSheet().getWorkFlow();
                WsStep originWsStep = finalOriginWsStepList.stream().filter(wsStep1 -> wsStep1.getStep().getId().equals(stepDto.getId())).findFirst().orElse(new WsStep());
                wsStep.setStep(modelMapper.map(stepDto, Step.class))
                        .setWorkFlow(Objects.nonNull(originWsStep.getWorkFlow()) ?originWsStep.getWorkFlow(): workFlow)
                        .setAfterStepId(stepDto.getAfterStepId())
                        .setPreStepId(stepDto.getPreStepId())
                        .setCategory(stepDto.getCategory())
                        .setRequestMode(stepDto.getRequestMode())
                        .setControlMode(stepDto.getControlMode())
                        .setIsControlMaterial(stepDto.getIsControlMaterial())
                        .setIsBindContainer(stepDto.getIsBindContainer())
                        .setSubWorkSheet(wsStepDto.getSubWorkSheet())
                        .setWorkSheet(wsStepDto.getWorkSheet());
                if(wsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && wsStep.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName()) {
                    throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "单支请求模式下只允许单支管控");
                }
//                if (wsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && wsStep.getIsBindContainer()) {
//                    throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "工序单支请求模式下不可绑定容器");
//                }
                wsStepRepository.save(wsStep);
            });
        }
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WsStep.class.getSimpleName()),""))
                .body("UpdateCustom Success");
    }


    /**
     * 通用CRUD修改工单工序
     * <AUTHOR>
     * @param entity
     * @return ResponseEntity<WsStep>
     * @date 2021-05-23
     **/
    @CacheEvict(cacheNames = {"wsStepQueryCache"},key = "#productWorkSheetId")
    public ResponseEntity<WsStep> updateEntity(WsStep entity,Long productWorkSheetId) {
        WsStep wsStep = wsStepRepository.getOne(entity.getId());
        SubWorkSheet subWorkSheet = null == entity.getSubWorkSheet() ? null : subWorkSheetRepository.getOne(entity.getSubWorkSheet().getId());
        WorkSheet workSheet = null == entity.getWorkSheet() ? null : workSheetRepository.getOne(entity.getWorkSheet().getId());
        WorkFlow workFlow = Objects.nonNull(subWorkSheet) ?
                subWorkSheet.getWorkFlow() : workSheet.getWorkFlow();
        wsStep.setStep(stepRepository.getOne(entity.getStep().getId()))
                .setCategory(entity.getCategory())
                .setRequestMode(entity.getRequestMode())
                .setControlMode(entity.getControlMode())
                .setIsControlMaterial(entity.getIsControlMaterial())
                .setIsBindContainer(entity.getIsBindContainer())
                .setSubWorkSheet(subWorkSheet)
                .setWorkSheet(workSheet).setWorkFlow(Objects.nonNull(wsStep.getWorkFlow())? wsStep.getWorkFlow():workFlow);
        wsStepRepository.save(wsStep);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WsStep.class.getSimpleName()),entity.getId().toString())).body(wsStep);
    }

    /**
     * 通过传入的数据，如果存在工单号或者子工单则按工艺路线顺序排序分页
     * @param spec 过滤条件
     * @param qcs 传输的过滤条件
     * @param pageable 分页条件
     * @param request 请求
     * <AUTHOR>
     * @date  2021/11/19
     * @return Page<WsStep>
     */
    public Page<WsStep> findByPage(Specification<WsStep> spec, List<QueryCondition> qcs, Pageable pageable, HttpServletRequest request){
        //验证是否存在存在子工单或总工单是否存在
        List<String> workSheetFieldNames = Arrays.asList("workSheet.serialNumber","subWorkSheet.serialNumber");
        List<QueryCondition> collect = qcs.stream().filter(value -> workSheetFieldNames.contains(value.getFieldName())).collect(Collectors.toList())
                .stream().filter(value -> ValidateUtils.isValid((String) value.getFieldValue())).collect(Collectors.toList());
        //【子】工单存在
        if (ValidateUtils.isValid(collect)){
            List<WsStep> wsSteps = this.find(spec);
            List<WsStep> wsStepList = new ArrayList<>();
            qcs.removeAll(collect);
            if(ValidateUtils.isValid(qcs) && qcs.stream().anyMatch(queryCondition -> ValidateUtils.isValid((String) queryCondition.getFieldValue()))){
                wsStepList.addAll(wsSteps);
            }else {
                //排序
                this.findTreeStep(wsSteps,null,wsStepList);
            }
            //手动分页
            int totalSize = wsStepList.size();
           return PageableExecutionUtils.getPage(this.getWsStepList(wsStepList, request), pageable, () -> totalSize);
        }
        //不存在
        return this.find(spec,pageable);
    }

    /**
     * 将工艺快照顺序化
     * @param wsStepList
     * @param parentIds
     * @param newWsStepList
     * <AUTHOR>
     * @date  2021/11/19
     * @return void
     */
    public void findTreeStep(List<WsStep> wsStepList, List<Long> parentIds, List<WsStep> newWsStepList) {
        List<WsStep> currStageWsSteps = null;
        if (!ValidateUtils.isValid(wsStepList)) {
            return;
        }
        //区分前置工序为空与前置工序不为空的情形
        if (ValidateUtils.isValid(parentIds)) {
            List<Long> finalParentIds = parentIds;
            currStageWsSteps = wsStepList.stream().filter(wsStep -> finalParentIds.stream().anyMatch(parentId -> wsStep.getPreStepId().contains(parentId.toString()))).collect(Collectors.toList());
        } else {
            currStageWsSteps = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
        }
        newWsStepList.addAll(currStageWsSteps);
        parentIds = Lists.newArrayList();
        parentIds.addAll(newWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()));
        wsStepList.removeAll(currStageWsSteps);
        findTreeStep(wsStepList, parentIds, newWsStepList);
    }

    /**
     * 手动分页工序快照
     * @param wsSteps 工序快照
     * @param request
     * <AUTHOR>
     * @date  2021/11/19
     * @return
     */
    public List<WsStep> getWsStepList(List<WsStep> wsSteps, HttpServletRequest request) {
        int pageSize = Integer.parseInt(request.getParameter("size"));
        int currentPage = Integer.parseInt(request.getParameter("page"));
        List<WsStep> wsStepList = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < wsSteps.size(); index++) {
            wsStepList.add(wsSteps.get(index));
        }
        return wsStepList;
    }
}
