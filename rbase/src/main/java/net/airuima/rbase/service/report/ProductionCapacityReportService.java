package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO;
import net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartResultDTO;
import net.airuima.rbase.web.rest.report.dto.production.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量统计报表Service
 *
 * <AUTHOR>
 * @date 2023/06/29
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionCapacityReportService {

    private final WorkSheetRepository workSheetRepository;

    private final SubWorkSheetRepository subWorkSheetRepository;

    private final WorkSheetService workSheetService;

    private final SubWorkSheetService subWorkSheetService;

    private final PedigreeStepRepository pedigreeStepRepository;
    private final ProductionPlanRepository productionPlanRepository;

    @Autowired
    private CommonService commonService;

    /**
     * 工单
     */
    private static final String WORK_SHEET = "workSheet";

    /**
     * 实际完成时间
     */
    private static final String ACTUAL_END_DATE = "actualEndDate";
    private final StaffPerformRepository staffPerformRepository;

    public ProductionCapacityReportService(WorkSheetRepository workSheetRepository, SubWorkSheetRepository subWorkSheetRepository, WorkSheetService workSheetService, SubWorkSheetService subWorkSheetService,
                                           PedigreeStepRepository pedigreeStepRepository, ProductionPlanRepository productionPlanRepository, StaffPerformRepository staffPerformRepository) {
        this.workSheetRepository = workSheetRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetService = workSheetService;
        this.subWorkSheetService = subWorkSheetService;
        this.pedigreeStepRepository = pedigreeStepRepository;
        this.productionPlanRepository = productionPlanRepository;
        this.staffPerformRepository = staffPerformRepository;
    }

    /**
     * 产量统计图形数据
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO 产量统计报表统计图表返回结果
     */
    @Transactional(readOnly = true)
    public ProductionCapacityReportChartResultDTO getProductionCapacityReportChart(ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(productionCapacityReportRequestDto);
        // 产品谱系id
        Long pedigreeId = productionCapacityReportRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = productionCapacityReportRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = productionCapacityReportRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = productionCapacityReportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = productionCapacityReportRequestDto.getEndDate();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        //产量统计图形数据
        ProductionCapacityReportChartResultDTO productionCapacityReportChartResultDto = null;
        //获取子工单报表
        if (subWsProductionMode) {
            productionCapacityReportChartResultDto = getSubWorkSheetProductionCapacityReportChartResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime);
        }
        //获取工单报表
        if (!subWsProductionMode) {
            productionCapacityReportChartResultDto = getWorkSheetProductionCapacityReportChartResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime);
        }
        return productionCapacityReportChartResultDto;
    }

    /**
     * 产量分布统计图形数据
     *
     * @param productionCapacityReportRequestDto 产量分布统计报表请求参数
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO 产量统计报表统计图表返回结果
     */
    @Transactional(readOnly = true)
    public ProductionCapacityReportDistributionResultDTO getProductionCapacityReportDistribution(ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(productionCapacityReportRequestDto);
        // 产品谱系id
        Long pedigreeId = productionCapacityReportRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = productionCapacityReportRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = productionCapacityReportRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = productionCapacityReportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = productionCapacityReportRequestDto.getEndDate();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        // 分布类型
        Integer distributionCategory = productionCapacityReportRequestDto.getDistributionCategory();
        //产量统计图形数据
        ProductionCapacityReportDistributionResultDTO productionCapacityReportDistributionResultDto = null;
        //获取子工单报表
        if (subWsProductionMode) {
            productionCapacityReportDistributionResultDto = getSubWorkSheetProductionCapacityReportDistributionResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, distributionCategory);
        }
        //获取工单报表
        if (!subWsProductionMode) {
            productionCapacityReportDistributionResultDto = getWorkSheetProductionCapacityReportDistributionResultDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, distributionCategory);
        }
        return productionCapacityReportDistributionResultDto;
    }

    /**
     * 子工单产量分布统计图形数据统计
     *
     * @param pedigreeId     产品谱系id
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param startDateTime  开始查询时间
     * @param endDateTime    结束查询时间
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportDistributionResultDTO 子工单产量分布统计图形数据
     */
    private ProductionCapacityReportDistributionResultDTO getSubWorkSheetProductionCapacityReportDistributionResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer distributionCategory) {
        ProductionCapacityReportDistributionResultDTO chartResultDto = new ProductionCapacityReportDistributionResultDTO();
        // 查询产量按部门统计
        List<ProductionCapacityOrganizationQueryDTO> productionCapacityOrganizationQueryDataList = subWorkSheetRepository.findProductionCapacityOrganizationData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        if (Objects.nonNull(distributionCategory) && ProductionCapacityDistributionCategoryEnum.ORGANIZATION.getCategory() == distributionCategory) {
            // 产量按部门统计查询结果为图形数据
            List<ProductionCapacityOrganizationDTO> productionCapacityOrganizationDataList = convertProductionCapacityOrganizationChartData(productionCapacityOrganizationQueryDataList);
            chartResultDto.setProductionCapacityOrganizationDataList(productionCapacityOrganizationDataList);
        }
        if (Objects.nonNull(distributionCategory) && ProductionCapacityDistributionCategoryEnum.WORK_LINE.getCategory() == distributionCategory) {
            // 查询产量按产线统计
            List<ProductionCapacityWorkLineDTO> productionCapacityWorkLineDataList = subWorkSheetRepository.findProductionCapacityWorkLineData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
            chartResultDto.setProductionCapacityWorkLineDataList(productionCapacityWorkLineDataList);
        }
        return chartResultDto;
    }

    /**
     * 工单产量分布统计图形数据统计
     *
     * @param pedigreeId     产品谱系id
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param startDateTime  开始查询时间
     * @param endDateTime    结束查询时间
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportDistributionResultDTO 工单产量分布统计图形数据
     */
    private ProductionCapacityReportDistributionResultDTO getWorkSheetProductionCapacityReportDistributionResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer distributionCategory) {
        ProductionCapacityReportDistributionResultDTO chartResultDto = new ProductionCapacityReportDistributionResultDTO();
        if (Objects.nonNull(distributionCategory) && ProductionCapacityDistributionCategoryEnum.ORGANIZATION.getCategory() == distributionCategory) {
            // 查询产量按部门统计
            List<ProductionCapacityOrganizationQueryDTO> productionCapacityOrganizationQueryDataList = workSheetRepository.findProductionCapacityOrganizationData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
            // 产量按部门统计查询结果为图形数据
            List<ProductionCapacityOrganizationDTO> productionCapacityOrganizationDataList = convertProductionCapacityOrganizationChartData(productionCapacityOrganizationQueryDataList);
            chartResultDto.setProductionCapacityOrganizationDataList(productionCapacityOrganizationDataList);
        }
        if (Objects.nonNull(distributionCategory) && ProductionCapacityDistributionCategoryEnum.WORK_LINE.getCategory() == distributionCategory) {
            // 查询产量按产线统计
            List<ProductionCapacityWorkLineDTO> productionCapacityWorkLineDataList = workSheetRepository.findProductionCapacityWorkLineData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
            chartResultDto.setProductionCapacityWorkLineDataList(productionCapacityWorkLineDataList);
        }
        return chartResultDto;
    }


    /**
     * 子工单产量统计图形数据统计
     *
     * @param pedigreeId     产品谱系id
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param startDateTime  开始查询时间
     * @param endDateTime    结束查询时间
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO 子工单产量统计图形数据
     */
    private ProductionCapacityReportChartResultDTO getSubWorkSheetProductionCapacityReportChartResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        //产量数量统计查询
        ProductionCapacityQueryDTO productionCapacityNumberData = subWorkSheetRepository.findProductionCapacityNumberData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        // 设置产量数量统计数字
        ProductionCapacityReportChartResultDTO chartResultDto = new ProductionCapacityReportChartResultDTO();
        BeanUtils.copyProperties(productionCapacityNumberData, chartResultDto);
        // 按产品型号和时间统计
        List<ProductionCapacityLineChartDataDTO> productionCapacityLineChartDataList = subWorkSheetRepository.findProductionCapacityLineChartData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        // x轴样式
        productionCapacityLineChartDataList.forEach(i -> {
            i.setTime(i.getTime().length() > 5 ? i.getTime().substring(5) : "");
        });
        // 设置子工单折线图数据
        chartResultDto.setProductionCapacityLineChartDataList(productionCapacityLineChartDataList);
        return chartResultDto;
    }

    /**
     * 工单产量统计图形数据统计
     *
     * @param pedigreeId     产品谱系id
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param startDateTime  开始查询时间
     * @param endDateTime    结束查询时间
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO 工单产量统计图形数据
     */
    private ProductionCapacityReportChartResultDTO getWorkSheetProductionCapacityReportChartResultDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        //产量数量统计查询
        ProductionCapacityQueryDTO productionCapacityNumberData = workSheetRepository.findProductionCapacityNumberData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        // 设置产量数量统计数字
        ProductionCapacityReportChartResultDTO chartResultDto = new ProductionCapacityReportChartResultDTO();
        BeanUtils.copyProperties(productionCapacityNumberData, chartResultDto);
        // 按产品型号和时间统计
        List<ProductionCapacityLineChartDataDTO> productionCapacityLineChartDataList = workSheetRepository.findProductionCapacityLineChartData(startDateTime, endDateTime, organizationId, workLineId, pedigreeId, Constants.LONG_ZERO);
        // x轴样式
        productionCapacityLineChartDataList.forEach(i -> i.setTime(i.getTime().length() > 5 ? i.getTime().substring(5) : ""));
        // 设置产量折线图数据
        chartResultDto.setProductionCapacityLineChartDataList(productionCapacityLineChartDataList);
        return chartResultDto;
    }

    /**
     * 产量按部门统计查询结果为图形数据
     *
     * @param productionCapacityOrganizationQueryDataList 产量按部门统计查询结果
     * @return java.util.List<net.airuima.web.rest.report.dto.ProductionCapacityOrganizationDTO> 产量按部门统计查询结果
     */
    private List<ProductionCapacityOrganizationDTO> convertProductionCapacityOrganizationChartData(List<ProductionCapacityOrganizationQueryDTO> productionCapacityOrganizationQueryDataList) {
        // 产量按部门统计
        List<ProductionCapacityOrganizationDTO> productionCapacityOrganizationDataList = Lists.newLinkedList();
        productionCapacityOrganizationQueryDataList.forEach(s -> {
            // 部门名字
            String name = Optional.ofNullable(s).map(ProductionCapacityOrganizationQueryDTO::getOrganizationDto).map(OrganizationDTO::getName).orElse("");
            ProductionCapacityOrganizationDTO productionCapacityOrganizationDto = new ProductionCapacityOrganizationDTO();
            // 部门产量
            productionCapacityOrganizationDto.setNumber(Optional.ofNullable(s).map(ProductionCapacityOrganizationQueryDTO::getNumber).orElse(Constants.LONG_ZERO)).setName(name);
            if (!StringUtils.isEmpty(name)) {
                productionCapacityOrganizationDataList.add(productionCapacityOrganizationDto);
            }
        });
        return productionCapacityOrganizationDataList;
    }


    /**
     * 产量统计表格数据
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportChartResultDTO 产量统计报表表格数据返回结果
     */
    @Transactional(readOnly = true)
    public ProductionCapacityReportTableResultDTO getProductionCapacityReportTable(ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(productionCapacityReportRequestDto);
        // 产品谱系id
        Long pedigreeId = productionCapacityReportRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = productionCapacityReportRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = productionCapacityReportRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = productionCapacityReportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = productionCapacityReportRequestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = productionCapacityReportRequestDto.getExportStatus();
        //当前页
        Integer currentPage = productionCapacityReportRequestDto.getCurrentPage();
        //页数
        Integer pageSize = productionCapacityReportRequestDto.getPageSize();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = org.apache.commons.lang3.StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        ProductionCapacityReportTableResultDTO productionCapacityReportTableResultDto = new ProductionCapacityReportTableResultDTO();

        //获取子工单报表
        if (subWsProductionMode) {
            productionCapacityReportTableResultDto = getSubWorkSheetProductionCapacityReportTabletDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize);
        }
        //获取工单报表
        if (!subWsProductionMode) {
            productionCapacityReportTableResultDto = getWorkSheetProductionCapacityReportTabletDTO(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize);
        }
        return productionCapacityReportTableResultDto;
    }

    /**
     * 工单投产产量统计表格数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @param exportStatus   是否导出
     * @param currentPage    当前页
     * @param pageSize       分页大小
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportTableResultDTO 产量统计表格数据
     */
    private ProductionCapacityReportTableResultDTO getWorkSheetProductionCapacityReportTabletDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize) {
        // 产量统计表格数据
        ProductionCapacityReportTableResultDTO reportTableResultDto = new ProductionCapacityReportTableResultDTO();
        // 分页查询
        Specification<WorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //统计生成完成和已结单的
            CriteriaBuilder.In<Integer> in = criteriaBuilder.in(root.get("status"));
            in.value(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
            in.value(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
            predicateList.add(in);
            predicateList.add(criteriaBuilder.equal(root.get("category"), WsEnum.NORMAL_WS.getCategory()));
            //开始时间和结束时间筛选
            if (endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), startDateTime));
            }
            // 产品谱系筛选
            if (pedigreeId != null) {
                Predicate pedigreePredicate = criteriaBuilder.equal(root.get("pedigree").get("id"), pedigreeId);
                predicateList.add(pedigreePredicate);
            }
            // 产线筛选
            if (workLineId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("workLine").get("id"), workLineId);
                predicateList.add(workLinePredicate);
            }
            //部门筛选
            if (organizationId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get("organizationId"), organizationId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        List<WorkSheet> workSheetList;
        Page<WorkSheet> workSheetPage = null;
        //导出时 导出全部数据
        if (Boolean.TRUE.equals(exportStatus)) {
            workSheetList = workSheetService.find(specification);
        } else {
            //分页查询
            workSheetPage = workSheetService.find(specification, PageRequest.of(currentPage, pageSize));
            workSheetList = Optional.ofNullable(workSheetPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        //设置工单报表数据
        reportTableResultDto.setProductionCapacityReportTableItemList(convertWorkSheetToProductionCapacityReportData(workSheetList));
        //设置工单表格分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(Boolean.TRUE.equals(exportStatus) ? Optional.ofNullable(workSheetList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(workSheetPage).map(s -> s.getTotalElements()).orElse(0L));
        return reportTableResultDto;
    }

    /**
     * 子工单投产产量统计表格数据
     *
     * @param pedigreeId     设备谱系id
     * @param organizationId 部门id
     * @param workLineId     生产线id
     * @param startDateTime  查询开始时间
     * @param endDateTime    查询结束时间
     * @param exportStatus   是否导出
     * @param currentPage    当前页
     * @param pageSize       分页大小
     * @return net.airuima.web.rest.report.dto.ProductionCapacityReportTableResultDTO 产量统计表格数据
     */
    private ProductionCapacityReportTableResultDTO getSubWorkSheetProductionCapacityReportTabletDTO(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize) {
        ProductionCapacityReportTableResultDTO reportTableResultDto = new ProductionCapacityReportTableResultDTO();
        // 分页查询
        Specification<SubWorkSheet> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //统计生成完成和已结单的
            CriteriaBuilder.In<Integer> in = criteriaBuilder.in(root.get("status"));
            in.value(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
            in.value(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
            predicateList.add(in);
            predicateList.add(criteriaBuilder.equal(root.get("workSheet").get("category"), WsEnum.NORMAL_WS.getCategory()));
            //开始时间和结束时间筛选
            if (endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(ACTUAL_END_DATE).as(LocalDateTime.class), startDateTime));
            }
            // 产品谱系筛选
            if (pedigreeId != null) {
                Predicate pedigreePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("pedigree").get("id"), pedigreeId);
                predicateList.add(pedigreePredicate);
            }
            // 产线筛选
            if (workLineId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("workLine").get("id"), workLineId);
                predicateList.add(workLinePredicate);
            }
            //部门筛选
            if (organizationId != null) {
                Predicate workLinePredicate = criteriaBuilder.equal(root.get(WORK_SHEET).get("organizationId"), organizationId);
                predicateList.add(workLinePredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        List<SubWorkSheet> subWorkSheetList;
        Page<SubWorkSheet> subWorkSheetPage = null;
        //导出时 导出全部数据
        if (Boolean.TRUE.equals(exportStatus)) {
            subWorkSheetList = subWorkSheetService.find(specification);
        } else {
            // 分页查询
            subWorkSheetPage = subWorkSheetService.find(specification, PageRequest.of(currentPage, pageSize));
            subWorkSheetList = Optional.ofNullable(subWorkSheetPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        //设置子工单报表数据
        reportTableResultDto.setProductionCapacityReportTableItemList(convertSubWorkSheetToProductionCapacityReportData(subWorkSheetList));
        //设置子工单表格分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(Boolean.TRUE.equals(exportStatus) ? Optional.ofNullable(subWorkSheetList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(subWorkSheetPage).map(s -> s.getTotalElements()).orElse(0L));
        return reportTableResultDto;
    }

    /**
     * 转换子工单查询结果为产量报表表格数据
     *
     * @param subWorkSheetList 子工单查询结果
     * @return java.util.List<net.airuima.web.rest.report.dto.ProductionCapacityReportTableItemDTO>  产量报表表格数据集合
     */
    private List<ProductionCapacityReportTableItemDTO> convertSubWorkSheetToProductionCapacityReportData(List<SubWorkSheet> subWorkSheetList) {
        List<ProductionCapacityReportTableItemDTO> reportTableDataList = Lists.newLinkedList();
        subWorkSheetList.forEach(s -> {
            // 工单
            WorkSheet workSheet = Optional.ofNullable(s).map(SubWorkSheet::getWorkSheet).orElse(null);
            // 产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).orElse(null);
            // 产品表格明细
            ProductionCapacityReportTableItemDTO productionCapacityReportTableItem = new ProductionCapacityReportTableItemDTO();
            productionCapacityReportTableItem.setActualEndDate(Optional.ofNullable(s).map(SubWorkSheet::getActualEndDate).orElse(null))
                    .setWorkSheetSerialNumber(Optional.ofNullable(workSheet).map(WorkSheet::getSerialNumber).orElse(null))
                    .setSubWorkSheetSerialNumber(Optional.ofNullable(s).map(SubWorkSheet::getSerialNumber).orElse(null))
                    .setStatus(Optional.ofNullable(s).map(SubWorkSheet::getStatus).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse(null))
                    .setOrganizationName(Optional.ofNullable(workSheet).map(WorkSheet::getOrganizationDto).map(OrganizationDTO::getName).orElse(null))
                    .setWorkLineName(Optional.ofNullable(s).map(SubWorkSheet::getWorkLine).map(WorkLine::getName).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getNumber())).orElse(Constants.LONG_ZERO))
                    .setQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setUnqualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO));
            reportTableDataList.add(productionCapacityReportTableItem);
        });
        return reportTableDataList;
    }

    /**
     * 转换工单查询结果为产量报表表格数据
     *
     * @param workSheetList 子工单查询结果
     * @return java.util.List<net.airuima.web.rest.report.dto.ProductionCapacityReportTableItemDTO>  产量报表表格数据集合
     */
    private List<ProductionCapacityReportTableItemDTO> convertWorkSheetToProductionCapacityReportData(List<WorkSheet> workSheetList) {
        //产量报表表格数据
        List<ProductionCapacityReportTableItemDTO> reportTableDataList = Lists.newLinkedList();
        workSheetList.forEach(s -> {
            // 产品谱系
            Pedigree pedigree = Optional.ofNullable(s).map(WorkSheet::getPedigree).orElse(null);
            // 产品表格明细
            ProductionCapacityReportTableItemDTO productionCapacityReportTableItem = new ProductionCapacityReportTableItemDTO();
            productionCapacityReportTableItem.setActualEndDate(Optional.ofNullable(s).map(WorkSheet::getActualEndDate).orElse(null))
                    .setWorkSheetSerialNumber(Optional.ofNullable(s).map(WorkSheet::getSerialNumber).orElse(null))
                    .setStatus(Optional.ofNullable(s).map(WorkSheet::getStatus).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse(null))
                    .setOrganizationName(Optional.ofNullable(s).map(WorkSheet::getOrganizationDto).map(OrganizationDTO::getName).orElse(null))
                    .setWorkLineName(Optional.ofNullable(s).map(WorkSheet::getWorkLine).map(WorkLine::getName).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getNumber())).orElse(Constants.LONG_ZERO))
                    .setQualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getQualifiedNumber())).orElse(Constants.LONG_ZERO))
                    .setUnqualifiedNumber(Optional.ofNullable(s).map(i -> Long.valueOf(i.getUnqualifiedNumber())).orElse(Constants.LONG_ZERO));
            reportTableDataList.add(productionCapacityReportTableItem);
        });
        return reportTableDataList;
    }


    /**
     * 计划完成时期解析
     *
     * @param productionCapacityReportRequestDto 产量统计请求参数
     */
    private void parseTimeFinishTimeCategory(ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = productionCapacityReportRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = productionCapacityReportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = productionCapacityReportRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                productionCapacityReportRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                productionCapacityReportRequestDto.setEndDate(LocalDateTime.now());
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                productionCapacityReportRequestDto.setStartDate(weekStart);
                productionCapacityReportRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                productionCapacityReportRequestDto.setStartDate(monthStart);
                productionCapacityReportRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
        }
    }

    /**
     * 工序和工序组产出推移图数据
     *
     * @param productionCapacityReportRequestDto 产量统计报表请求参数
     * @return net.airuima.web.rest.report.dto.StepProductCapacityReportChartResultDTO 工序和工序组产出推移图数据
     */
    @Transactional(readOnly = true)
    public StepProductCapacityReportChartResultDTO getStepProductionCapacityReportChart(ProductionCapacityReportRequestDTO productionCapacityReportRequestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(productionCapacityReportRequestDto);
        // 产品谱系id
        Long pedigreeId = productionCapacityReportRequestDto.getPedigreeId();
        // 部门id
        Long organizationId = productionCapacityReportRequestDto.getOrganizationId();
        //产线 id
        Long workLineId = productionCapacityReportRequestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = productionCapacityReportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = productionCapacityReportRequestDto.getEndDate();
        // 推移图分类
        Integer stepCategory = productionCapacityReportRequestDto.getStepCategory();
        // 日期
        String day = productionCapacityReportRequestDto.getDay();
        // 小时 只支持工序推移图
        Integer hour = productionCapacityReportRequestDto.getHour();
        // 计划完工时间类型
        Integer planFinishTimeCategory = productionCapacityReportRequestDto.getPlanFinishTimeCategory();
        // 工艺路线
        Long workFlowId = productionCapacityReportRequestDto.getWorkFlowId();
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = ObjectUtils.isEmpty(mode) || Boolean.parseBoolean(mode);
        //产量统计图形数据
        StepProductCapacityReportChartResultDTO resultDTO = new StepProductCapacityReportChartResultDTO();
        // 设置日期值
        day = setDayValue(startDateTime, day, planFinishTimeCategory);
        if (ObjectUtils.isEmpty(day)) {
            resultDTO.setStepDataList(Lists.newArrayList());
            resultDTO.setStepGroupDataList(Lists.newArrayList());
            return resultDTO;
        }
        // 是否设置小时
        if (Objects.isNull(hour)) {
            LocalDate localDate = LocalDate.parse(day);
            startDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
            endDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        } else {
            LocalDateTime[] dateTimeRange = generateDateTime(day, hour);
            startDateTime = dateTimeRange[0];
            endDateTime = dateTimeRange[1];
        }
        // 子工单投产
        if (subWsProductionMode) {
            resultDTO = getSubWorkSheetStepProductionCapacityReportChart(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, stepCategory, hour, day, workFlowId);
        }
        // 工单投产
        if (!subWsProductionMode) {
            resultDTO = getWorkSheetStepProductionCapacityReportChart(pedigreeId, organizationId, workLineId, startDateTime, endDateTime, stepCategory, hour, day, workFlowId);
        }
        return resultDTO;
    }

    /**
     * 工序和工序组产出推移图数据 工单投产
     * @param pedigreeId 产品谱系id
     * @param organizationId 组织架构id
     * @param workLineId 产线id
     * @param startDateTime 开始时间
     * @param endDateTime 结束时间
     * @param stepCategory 工序类型
     * @param hour 小时
     * @param day 日期
     * @param workFlowId 工艺路线id
     * @return net.airuima.web.rest.report.dto.StepProductCapacityReportChartResultDTO  工序和工序组产出推移图数据
     */
    private StepProductCapacityReportChartResultDTO getWorkSheetStepProductionCapacityReportChart(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime,
                                                                                                  LocalDateTime endDateTime, Integer stepCategory, Integer hour, String day, Long workFlowId) {
        StepProductCapacityReportChartResultDTO resultDTO = new StepProductCapacityReportChartResultDTO();
        final LocalDate localDate = LocalDate.parse(day);
        // 工序
        if (StepProductCategoryEnum.STEP.getCategory().equals(stepCategory)) {
            // 实际产量
            List<StepProductCapacityReportChartDTO> stepDataList = staffPerformRepository.findWorkSheetStepProductionCapacityReportChart(pedigreeId, workFlowId, workLineId, organizationId, startDateTime, endDateTime, Constants.LONG_ZERO);
            // 设置工序计划数据
            setPlanStepData(pedigreeId, stepDataList, workFlowId);
            resultDTO.setStepDataList(Optional.ofNullable(stepDataList).orElse(Lists.newArrayList()));
            resultDTO.setStepGroupDataList(Lists.newArrayList());
        }
        // 工序组
        if (StepProductCategoryEnum.STEP_GROUP.getCategory().equals(stepCategory)) {
            List<StepProductCapacityReportChartDTO> stepGroupDataList = staffPerformRepository.findWorkSheetStepGroupProductionCapacityReportChart(pedigreeId, workLineId, organizationId, startDateTime, endDateTime, Constants.LONG_ZERO);
            // 设置工序组计划数据
            setPlanStepGroupData(pedigreeId, localDate, stepGroupDataList);
            resultDTO.setStepDataList(Lists.newArrayList());
            resultDTO.setStepGroupDataList(Optional.ofNullable(stepGroupDataList).orElse(Lists.newArrayList()));
        }
        // 工序组
        return resultDTO;
    }

    /**
     * 设置工序组计划产出数据
     * @param pedigreeId 产品谱系id
     * @param localDate  日期
     * @param stepGroupDataList 工序组实际产出数据
     */
    private void setPlanStepGroupData(Long pedigreeId, LocalDate localDate, List<StepProductCapacityReportChartDTO> stepGroupDataList) {
        if (!CollectionUtils.isEmpty(stepGroupDataList)) {
            List<StepProductCapacityReportChartDTO> stepPlanDataList = Lists.newArrayList();
            stepGroupDataList.forEach(stepGroupData -> {
                // 计划产量
                ProductionPlan productionPlan= productionPlanRepository.findByPlanDateAndPedigreeIdAndStepGroupIdAndDeleted(localDate, pedigreeId, stepGroupData.getId(), Constants.LONG_ZERO).orElse(null);
                StepProductCapacityReportChartDTO stepGroupPlanData = new StepProductCapacityReportChartDTO();
                stepGroupPlanData.setId(stepGroupData.getId())
                        .setName("plan")
                        .setChartName(stepGroupData.getChartName())
                        .setNumber(Long.valueOf(Optional.ofNullable(productionPlan).map(ProductionPlan::getPlanNumber).orElse(Constants.INT_ZERO)));
                stepPlanDataList.add(stepGroupPlanData);
            });
            stepGroupDataList.addAll(stepPlanDataList);
        }
    }

    /**
     * 设置工序计划产出数据
     * @param pedigreeId 产品谱系id
     * @param stepDataList 工序实际产出数据
     * @param workFlowId  工艺路线id
     */
    private void setPlanStepData(Long pedigreeId, List<StepProductCapacityReportChartDTO> stepDataList, Long workFlowId) {
        if (!CollectionUtils.isEmpty(stepDataList)) {
            List<StepProductCapacityReportChartDTO> stepPlanDataList = Lists.newArrayList();
            stepDataList.forEach(stepData -> {
                // 计划产量
                PedigreeStep queryPedigreeStep = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(null, pedigreeId, workFlowId, stepData.getId(), Constants.LONG_ZERO).orElse(null);
                StepProductCapacityReportChartDTO stepPlanData = new StepProductCapacityReportChartDTO();
                stepPlanData.setId(stepData.getId())
                        .setName("plan")
                        .setChartName(stepData.getChartName())
                        .setNumber(Optional.ofNullable(queryPedigreeStep).map(PedigreeStep::getStandardDailyOutput)
                                .orElse(Constants.LONG_ZERO));
                stepPlanDataList.add(stepPlanData);
            });
            stepDataList.addAll(stepPlanDataList);
        }
    }

    /**
     * 根据日期和小时生成时间段
     *
     * @param day  日期
     * @param hour 小时
     * @return java.time.LocalDateTime 时间段
     */
    public LocalDateTime[] generateDateTime(String day, int hour) {
        // 解析传入的日期
        LocalDate date = LocalDate.parse(day);
        // 创建开始时间的日期时间对象
        LocalDateTime startDateTime = LocalDateTime.of(date, LocalTime.of(hour, 0));
        // 创建结束时间的日期时间对象，即开始时间加一小时
        LocalDateTime endDateTime = startDateTime.plusHours(1);
        // 返回包含开始和结束日期时间的数组
        return new LocalDateTime[]{startDateTime, endDateTime};
    }


    /**
     * 工序和工序组产出推移图数据 子工单投产
     * @param pedigreeId 产品谱系id
     * @param organizationId 组织架构id
     * @param workLineId 产线id
     * @param startDateTime 开始时间
     * @param endDateTime 结束时间
     * @param stepCategory 工序类型
     * @param hour 小时
     * @param day 日期
     * @param workFlowId 工艺路线
     * @return net.airuima.web.rest.report.dto.StepProductCapacityReportChartResultDTO  工序和工序组产出推移图数据
     */
    private StepProductCapacityReportChartResultDTO getSubWorkSheetStepProductionCapacityReportChart(Long pedigreeId, Long organizationId, Long workLineId, LocalDateTime startDateTime,
                                                                                                     LocalDateTime endDateTime, Integer stepCategory, Integer hour, String day, Long workFlowId) {
        StepProductCapacityReportChartResultDTO resultDTO = new StepProductCapacityReportChartResultDTO();
        final LocalDate localDate = LocalDate.parse(day);
        // 工序
        if (StepProductCategoryEnum.STEP.getCategory().equals(stepCategory)) {
            List<StepProductCapacityReportChartDTO> stepDataList = staffPerformRepository.findSubWorkSheetStepProductionCapacityReportChart(pedigreeId,workFlowId, workLineId, organizationId, startDateTime, endDateTime, Constants.LONG_ZERO);
            // 设置工序计划数据
            setPlanStepData(pedigreeId, stepDataList, workFlowId);
            resultDTO.setStepDataList(Optional.ofNullable(stepDataList).orElse(Lists.newArrayList()));
            resultDTO.setStepGroupDataList(Lists.newArrayList());
        }
        // 工序组
        if (StepProductCategoryEnum.STEP_GROUP.getCategory().equals(stepCategory)) {
            List<StepProductCapacityReportChartDTO> stepGroupDataList = staffPerformRepository.findSubWorkSheetStepGroupProductionCapacityReportChart(pedigreeId, workLineId, organizationId, startDateTime, endDateTime, Constants.LONG_ZERO);
            // 设置工序组计划数据
            setPlanStepGroupData(pedigreeId, localDate, stepGroupDataList);
            resultDTO.setStepDataList(Lists.newArrayList());
            resultDTO.setStepGroupDataList(Optional.ofNullable(stepGroupDataList).orElse(Lists.newArrayList()));
        }
        return resultDTO;
    }

    /**
     * 设置查询的日期
     * @param startDateTime 开始时间
     * @param day 日期
     * @param planFinishTimeCategory 时间类型
     * @return java.lang.String 日期
     */
    private String setDayValue(LocalDateTime startDateTime, String day, Integer planFinishTimeCategory) {
        // 设置day的值
        if (Objects.nonNull(planFinishTimeCategory) && ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
            // 今天 直接设置
            day = LocalDate.now().toString();
        } else {
            // 时间段 如果day没传值 默认取时间段第一天
            if (ObjectUtils.isEmpty(day) && Objects.nonNull(startDateTime)) {
                day = startDateTime.toLocalDate().toString();
            }
        }
        return day;
    }

    /**
     * 获取工序产出推移图数据的工艺路线
     * @param pedigreeId 产品谱系id
     * @return java.util.List<net.airuima.domain.base.process.WorkFlow> 工艺路线列表
     */
    @Transactional(readOnly = true)
    public List<WorkFlow> getStepProductionCapacityReportChartWorkFlow(Long pedigreeId) {
        return pedigreeStepRepository.findByPedigreeIdAndEnableAndDeleted(pedigreeId, Boolean.TRUE, Constants.LONG_ZERO);
    }
}
