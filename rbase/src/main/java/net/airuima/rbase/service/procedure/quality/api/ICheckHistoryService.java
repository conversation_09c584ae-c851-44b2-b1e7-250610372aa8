package net.airuima.rbase.service.procedure.quality.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;

import java.util.List;

/**
 * 质检历史相关接口处理
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@FuncDefault
public interface ICheckHistoryService {


    /**
     * 发送MRB评审请求
     *
     * @param checkHistory           检验历史
     * @param checkHistoryDetailList 明细
     * @param reason                 发起原因
     */
    default void applicantMrb(CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetailList, String reason) {
        return;
    }


    /**
     * 放行 - 有效单支进入维修分析，虚拟sn则放行直接回显生产数据
     *
     * @param checkHistoryDetails 质检详情记录
     * @param wsStepList          快照列表
     */
    default void addReleasedInspectUnqualified(List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        return;
    }

    /**
     * 容器器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    default void handleContainerMaintain(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

    }


    /**
     * 工单器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    default void handleWorkSheetMaintain(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

    }


    /**
     * 维修分析处理
     *
     * @param containerDetail     批退数量
     * @param snWorkDetailList    sn详情列表
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    default void addMaintainInspectUnqualified(ContainerDetail containerDetail, BatchWorkDetail batchWorkDetail, List<SnWorkDetail> snWorkDetailList, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

    }


    /**
     * 单只批退-容器器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    default void handleContainerBatchRejected(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails) {

    }

    /**
     * 单只批退-工单器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    default void handleWorkSheetBatchRejected(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails) {

    }

    /**
     * 单只批退-sn-容器-工序详情处理
     *
     * @param containerDetail     容器详情
     * @param snWorkDetailList    sn详情
     * @param checkHistoryDetails 检测历史
     * @since 1.8.1
     */
    default void addBatchRejectInspectUnqualified(ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList, List<CheckHistoryDetail> checkHistoryDetails) {
    }


    /**
     * 重检
     *
     * @param checkHistory 检测历史
     */
    default void handleRecheckHistory(CheckHistory checkHistory) {
    }


}
