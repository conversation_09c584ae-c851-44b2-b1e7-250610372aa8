package net.airuima.rbase.service.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.StepIntervalEvent;
import net.airuima.rbase.repository.procedure.quality.StepIntervalEventRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序间隔异常Service
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepIntervalEventService extends CommonJpaService<StepIntervalEvent> {
    private final StepIntervalEventRepository stepIntervalEventRepository;
    private final String STEP_INTERVAL_EVENT_GRAPH = "stepIntervalEventEntityGraph";

    public StepIntervalEventService(StepIntervalEventRepository stepIntervalEventRepository) {
        this.stepIntervalEventRepository = stepIntervalEventRepository;
    }
    @Override
    @FetchMethod
    public Page<StepIntervalEvent> find(Specification<StepIntervalEvent> spec, Pageable pageable) {
        return stepIntervalEventRepository.findAll(spec,pageable,new NamedEntityGraph(STEP_INTERVAL_EVENT_GRAPH));
    }

    @Override
    @FetchMethod
    public List<StepIntervalEvent> find(Specification<StepIntervalEvent> spec) {
        return stepIntervalEventRepository.findAll(spec,new NamedEntityGraph(STEP_INTERVAL_EVENT_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<StepIntervalEvent> findAll(Pageable pageable) {
        return stepIntervalEventRepository.findAll(pageable,new NamedEntityGraph(STEP_INTERVAL_EVENT_GRAPH));
    }
}
