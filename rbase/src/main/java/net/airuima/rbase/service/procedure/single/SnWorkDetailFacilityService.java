package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailFacilityRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情设备Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnWorkDetailFacilityService extends CommonJpaService<SnWorkDetailFacility> {
    private static final String SN_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH = "snWorkDetailEquipmentEntityGraph";
    private final SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;

    public SnWorkDetailFacilityService(SnWorkDetailFacilityRepository snWorkDetailFacilityRepository) {
        this.snWorkDetailFacilityRepository = snWorkDetailFacilityRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnWorkDetailFacility> find(Specification<SnWorkDetailFacility> spec, Pageable pageable) {
        return snWorkDetailFacilityRepository.findAll(spec, pageable,new NamedEntityGraph(SN_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SnWorkDetailFacility> find(Specification<SnWorkDetailFacility> spec) {
        return snWorkDetailFacilityRepository.findAll(spec,new NamedEntityGraph(SN_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnWorkDetailFacility> findAll(Pageable pageable) {
        return snWorkDetailFacilityRepository.findAll(pageable,new NamedEntityGraph(SN_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

}
