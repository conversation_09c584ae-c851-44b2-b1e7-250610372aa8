package net.airuima.rbase.service.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工位上料表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsWorkCellMaterialBatchService extends CommonJpaService<WsWorkCellMaterialBatch> {
    private static final String WS_WORK_CELL_MATERIAL_BATCH_ENTITY_GRAPH = "wsWorkCellMaterialBatchEntityGraph";
    private final WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;

    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;

    public WsWorkCellMaterialBatchService(WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository) {
        this.wsWorkCellMaterialBatchRepository = wsWorkCellMaterialBatchRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsWorkCellMaterialBatch> find(Specification<WsWorkCellMaterialBatch> spec, Pageable pageable) {
        return wsWorkCellMaterialBatchRepository.findAll(spec, pageable,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsWorkCellMaterialBatch> find(Specification<WsWorkCellMaterialBatch> spec) {
        return wsWorkCellMaterialBatchRepository.findAll(spec,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsWorkCellMaterialBatch> findAll(Pageable pageable) {
        return wsWorkCellMaterialBatchRepository.findAll(pageable,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    /**
     * 通用CRUD修改工单工位物料
     * <AUTHOR>
     * @param entity
     * @return ResponseEntity<WsWorkCellMaterialBatch>
     * @date 2021-11-17
     **/
    @FetchMethod
    public ResponseEntity<WsWorkCellMaterialBatch> updateEntity(WsWorkCellMaterialBatch entity) {
        WsWorkCellMaterialBatch wsWorkCellMaterialBatch  = wsWorkCellMaterialBatchRepository.findById(entity.getId()).orElse(new WsWorkCellMaterialBatch());
        wsWorkCellMaterialBatch.setBatch(entity.getBatch())
                .setWorkCell(null == entity.getWorkCell()? wsWorkCellMaterialBatch.getWorkCell() : workCellRepository.getReferenceById(entity.getWorkCell().getId()))
                .setWorkSheet(null == entity.getWorkSheet() ? wsWorkCellMaterialBatch.getWorkSheet():workSheetRepository.getReferenceById(entity.getWorkSheet().getId()))
                .setMaterialId(entity.getMaterialId())
                .setLeftNumber(entity.getLeftNumber())
                .setNumber(entity.getNumber());
        wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WsWorkCellMaterialBatch.class.getSimpleName()),entity.getId().toString())).body(wsWorkCellMaterialBatch);
    }

    /**
     * 根据工单id 获取工单工位 对应的工位列表
     * @param wsId 工单id
     * <AUTHOR>
     * @date  2022/3/25
     * @return List<WorkCell>
     */
    @Transactional(readOnly = true)
    public List<WorkCell> findByWsId(Long wsId) {
        List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsWorkCellMaterialBatchList)){
           return wsWorkCellMaterialBatchList.stream().map(WsWorkCellMaterialBatch::getWorkCell).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取对应的工单工位领料列表
     * @param wsId 工单id
     * @param workCellId 工位id
     * <AUTHOR>
     * @date  2022/3/25
     * @return List<WsWorkCellMaterialBatch>
     */
    
    @Transactional(readOnly = true)
    public List<WsWorkCellMaterialBatch> findByWsIdAndWorkCellId(Long wsId, Long workCellId) {
        return wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndDeleted(wsId, workCellId, Constants.LONG_ZERO);
    }
}
