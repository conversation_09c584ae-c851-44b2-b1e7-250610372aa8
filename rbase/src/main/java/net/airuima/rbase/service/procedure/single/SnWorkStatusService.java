package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.SnWorkStatusEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import net.airuima.rbase.proxy.rqms.RbaseQmsProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产状态Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnWorkStatusService extends CommonJpaService<SnWorkStatus> {
    private static final String SN_WORK_STATUS_ENTITY_GRAPH = "snWorkStatusEntityGraph";
    private final SnWorkStatusRepository snWorkStatusRepository;
    private final WorkSheetRepository workSheetRepository;
    private final WsReworkRepository wsReworkRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private RbaseQmsProxy rbaseQmsProxy;

    public SnWorkStatusService(SnWorkStatusRepository snWorkStatusRepository, WorkSheetRepository workSheetRepository, WsReworkRepository wsReworkRepository) {
        this.snWorkStatusRepository = snWorkStatusRepository;
        this.workSheetRepository = workSheetRepository;
        this.wsReworkRepository = wsReworkRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SnWorkStatus> find(Specification<SnWorkStatus> spec, Pageable pageable) {
        return snWorkStatusRepository.findAll(spec, pageable, new NamedEntityGraph(SN_WORK_STATUS_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<SnWorkStatus> find(Specification<SnWorkStatus> spec) {
        return snWorkStatusRepository.findAll(spec, new NamedEntityGraph(SN_WORK_STATUS_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SnWorkStatus> findAll(Pageable pageable) {
        return snWorkStatusRepository.findAll(pageable, new NamedEntityGraph(SN_WORK_STATUS_ENTITY_GRAPH));
    }

    /**
     * 验证待包装sn状态
     *
     * @param sn           待包装SN
     * @param serialNumber 工单号
     * @return net.airuima.rbase.dto.base.BaseDTO
     * <AUTHOR>
     * @date 2023/5/19
     */
    public BaseDTO validatePackageSn(String sn, String serialNumber) {
        BaseDTO baseDto = new BaseDTO(Constants.KO);
        Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO);
        if (snWorkStatusOptional.isEmpty()) {
            return baseDto.setMessage("SN尚未在RMES投产");
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return baseDto.setMessage("工单在RMES中不存在");
        }
        SnWorkStatus snWorkStatus = snWorkStatusOptional.get();
        WorkSheet snWorkStatusWorkSheet = null != snWorkStatus.getSubWorkSheet() ? snWorkStatus.getSubWorkSheet().getWorkSheet() : snWorkStatus.getWorkSheet();
        if (snWorkStatusWorkSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(snWorkStatusWorkSheet.getId(), Constants.LONG_ZERO).orElse(new WsRework());
            snWorkStatusWorkSheet = wsRework.getOriginalWorkSheet();
        }
        if (!snWorkStatusWorkSheet.getId().equals(workSheetOptional.get().getId())) {
            return baseDto.setMessage("SN在RMPS和RMES中所对应工单不一致");
        }
        String remark = SnWorkStatusEnum.getRemark(snWorkStatus.getStatus());
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
            return baseDto.setStatus(Constants.OK).setMessage(remark);
        }
        return baseDto.setMessage(remark);

    }

    /**
     *
     * @param sn
     * @return
     */
    @Transactional(readOnly = true)
    public BaseDTO validateDiscardSn(String sn){
        Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO);
        if(snWorkStatusOptional.isEmpty()){
            return new BaseDTO(Constants.OK);
        }else {
            return new BaseDTO(Constants.KO);
        }

    }

    /**
     * 替换SN
     * @param originSn 原始SN
     * @param targetSn 目标SN
     * @return
     */
    public BaseDTO replaceSn(String originSn,String targetSn){
        Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(originSn, Constants.LONG_ZERO);
        snWorkStatusOptional.ifPresent(snWorkStatus -> {
            snWorkStatus.setSn(targetSn);
            snWorkStatusRepository.save(snWorkStatus);
            snWorkDetailRepository.exchangeSn(originSn,targetSn);
            List<SnReplaceDTO> snReplaceDtoList = new ArrayList<>();
            SnReplaceDTO snReplaceDTO = new SnReplaceDTO();
            snReplaceDTO.setOriginalSn(originSn).setReplaceSn(targetSn);
            snReplaceDtoList.add(snReplaceDTO);
            //修改烘烤历史记录SN
            bakeCycleBakeAgeingModelServices[0].replaceBakeHistorySn(snReplaceDtoList);
            //修改温循历史记录SN
            bakeCycleBakeAgeingModelServices[0].replaceCycleBakeHistorySn(snReplaceDtoList);
            //修改老化历史记录SN
            bakeCycleBakeAgeingModelServices[0].replaceAgeingHistorySn(snReplaceDtoList);
            rbaseQmsProxy.replaceSn(originSn,targetSn);
        });
        return new BaseDTO(Constants.OK);
    }
}
