package net.airuima.rbase.service.procedure.aps.plugin.impl;

import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.rbase.client.feign.digiwin.WorkSheetFeignClient;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.digiwin.DigiwinSubWorkSheetCompleteDTO;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetCompleteService;
import net.airuima.rbase.service.procedure.batch.WsStepService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.security.SecurityUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 默认的工单完工上传service
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WorkSheetCompleteServiceImpl implements IWorkSheetCompleteService {


    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetFeignClient workSheetFeignClient;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WsStepService wsStepService;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    public WorkSheetCompleteServiceImpl(SubWorkSheetRepository subWorkSheetRepository,
                                        WorkSheetFeignClient workSheetFeignClient) {
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetFeignClient = workSheetFeignClient;
    }

    /**
     * 工单完工上传
     * @param subWorkSheetIdList 子工单主键ID列表
     * @return java.lang.String 结果信息
     */
    @Override
    public String subWorkSheetComplete(List<Long> subWorkSheetIdList) {
        DigiwinSubWorkSheetCompleteDTO digiwinSubWorkSheetCompleteDto = new DigiwinSubWorkSheetCompleteDTO();
        List<DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO> wsCompleteInfos = Lists.newArrayList();

        List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByIdInAndDeleted(subWorkSheetIdList, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(subWorkSheets)) {
            throw new ResponseException("error.subWorkSheetListIsNotNull", "上传子工单列表不存在");
        }
        if (subWorkSheets.stream().anyMatch(subWorkSheet -> (subWorkSheet.getStatus() < Constants.FINISH || subWorkSheet.getSyncStatus() == Constants.INT_ONE))) {
            throw new ResponseException("error.subWorkSheetListStatusError", "上传子工单状态异常");
        }

        //采集上传操作人-聚合同一
        // 获取当前操作人信息
        SecurityUtils.getCurrentUserLogin().ifPresent(name -> {
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(name);
            StaffDTO staff = Objects.nonNull(userDTO) ? userDTO.getStaffDTO():new StaffDTO() ;
            if (!ObjectUtils.isEmpty(staff.getId())) {
                digiwinSubWorkSheetCompleteDto.setStaffCode(staff.getCode()).setStaffName(staff.getName());
                if (staff.getOrganization() != null && !ObjectUtils.isEmpty(staff.getOrganization().getId())) {
                    digiwinSubWorkSheetCompleteDto.setOrganizationName(staff.getOrganization().getName()).setOrganizationCode(staff.getOrganization().getCode());
                }
            }
        });
        //根据工单分组
        subWorkSheets.stream().collect(Collectors.groupingBy(SubWorkSheet::getWorkSheet)).forEach((workSheet, subWorkSheetList) -> {
            DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO wsComplete = new DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO();
            wsComplete.setSerialNumber(workSheet.getSerialNumber())
                    .setBomInfoCode(workSheet.getBomInfoDto().getCode())
                    .setMaterialCode(workSheet.getPedigree().getMaterialDto().getCode())
                    .setCategory(workSheet.getCategory());
            int qualifiedNumber = subWorkSheetList.stream().mapToInt(SubWorkSheet::getQualifiedNumber).sum();
            int unQualifiedNumber = subWorkSheetList.stream().mapToInt(SubWorkSheet::getUnqualifiedNumber).sum();
            wsComplete.setUnqualifiedNumber(unQualifiedNumber).setQualifiedNumber(qualifiedNumber);
            //添加工单扣料信息
            List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailSubWorkSheetIdInAndDeleted(subWorkSheetList.stream().map(SubWorkSheet::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(batchWorkDetailMaterialBatchList)) {
                List<DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailMaterialBatchInfo> batchWorkDetailMaterialBatchInfos = Lists.newArrayList();
                //根据物料编码以及物料批次进行分组累加扣减数量
                batchWorkDetailMaterialBatchList.stream().map(DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailMaterialBatchInfo::new).toList()
                        .stream().collect(Collectors.groupingBy(batchWorkDetailMaterialBatchInfo -> batchWorkDetailMaterialBatchInfo))
                        .forEach((batchWorkDetailMaterialBatchInfo, batchWorkDetailMaterialBatchInfoList) -> {
                            double number = batchWorkDetailMaterialBatchInfoList.stream().mapToDouble(DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailMaterialBatchInfo::getNumber).sum();
                            batchWorkDetailMaterialBatchInfos.add(batchWorkDetailMaterialBatchInfo.setNumber(number));
                        });
                wsComplete.setBatchWorkDetailMaterialBatchInfos(batchWorkDetailMaterialBatchInfos);
            }
            //获取子工单生产详情信息
            List<DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailInfo> batchWorkDetailInfos = Lists.newArrayList();
            List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsSteps)) {
                List<WsStep> wsStepList = new ArrayList<>();
                wsStepService.findTreeStep(wsSteps, null, wsStepList);
                //有序添加工序生产数据
                wsStepList.forEach(wsStep -> {
                    List<BatchWorkDetail> batchWorkDetails = batchWorkDetailRepository.findBySubWorkSheetIdInAndStepIdAndDeleted(subWorkSheetList.stream().map(SubWorkSheet::getId).collect(Collectors.toList()), wsStep.getStep().getId(), Constants.LONG_ZERO);

                    if (ValidateUtils.isValid(batchWorkDetails)) {
                        int stepQualifiedNumber = batchWorkDetails.stream().mapToInt(BatchWorkDetail::getQualifiedNumber).sum();
                        int stepUnqualifiedNumber = batchWorkDetails.stream().mapToInt(BatchWorkDetail::getUnqualifiedNumber).sum();
                        double stepWorkHour = batchWorkDetails.stream().mapToDouble(BatchWorkDetail::getWorkHour).sum();

                        DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailInfo batchWorkDetailInfo = new DigiwinSubWorkSheetCompleteDTO.BatchWorkDetailInfo();
                        batchWorkDetailInfo.setStepCode(wsStep.getStep().getCode()).setStepName(wsStep.getStep().getName())
                                .setSerialNumber(workSheet.getSerialNumber()).setQualifiedNumber(stepQualifiedNumber).setUnqualifiedNumber(stepUnqualifiedNumber).setWorkHour(stepWorkHour)
                                .setStaffCode(batchWorkDetails.get(Constants.INT_ZERO).getOperatorDto().getCode())
                                .setStaffName(batchWorkDetails.get(Constants.INT_ZERO).getOperatorDto().getName());
                        batchWorkDetailInfos.add(batchWorkDetailInfo);
                    }
                });
                wsComplete.setBatchWorkDetailInfoList(batchWorkDetailInfos);
            }
            wsCompleteInfos.add(wsComplete);

        });
        //添加同步完工工单信息
        digiwinSubWorkSheetCompleteDto.setWorkSheetCompleteDtoList(wsCompleteInfos);
        //总工单的完工信息收集并保存 推送到erp
        if (!StringUtils.isBlank(workSheetFeignClient.syncWorkSheetComplete(digiwinSubWorkSheetCompleteDto))) {
            //保存子工单上传状态
            subWorkSheets = subWorkSheets.stream().map(subWorkSheet -> subWorkSheet.setSyncStatus(Constants.INT_ONE)).collect(Collectors.toList());
            subWorkSheetRepository.saveAll(subWorkSheets);
        } else {
            throw new RuntimeException("工单完工上传失败");
        }
        return null;
    }
}
