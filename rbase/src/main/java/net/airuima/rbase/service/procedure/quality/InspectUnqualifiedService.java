package net.airuima.rbase.service.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.constant.enums.MaintainHistorySourceEnum;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualified;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualifiedDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.quality.InspectUnqualifiedDetailRepository;
import net.airuima.rbase.repository.procedure.quality.InspectUnqualifiedRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectUnqualifiedDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectUnqualifiedResultDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良品管理记录表Service
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectUnqualifiedService extends CommonJpaService<InspectUnqualified> {

    private static final String INSPECT_UNQUALIFIED_ENTITY_GRAPH = "inspectUnqualifiedEntityGraph";

    private final InspectUnqualifiedRepository inspectUnqualifiedRepository;

    @Autowired
    private InspectUnqualifiedDetailRepository inspectUnqualifiedDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;

    public InspectUnqualifiedService(InspectUnqualifiedRepository inspectUnqualifiedRepository) {
        this.inspectUnqualifiedRepository = inspectUnqualifiedRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<InspectUnqualified> find(Specification<InspectUnqualified> spec, Pageable pageable) {
        return inspectUnqualifiedRepository.findAll(spec, pageable, new NamedEntityGraph(INSPECT_UNQUALIFIED_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<InspectUnqualified> find(Specification<InspectUnqualified> spec) {
        return inspectUnqualifiedRepository.findAll(spec, new NamedEntityGraph(INSPECT_UNQUALIFIED_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<InspectUnqualified> findAll(Pageable pageable) {
        return inspectUnqualifiedRepository.findAll(pageable, new NamedEntityGraph(INSPECT_UNQUALIFIED_ENTITY_GRAPH));
    }

    /**
     * 根据id获取明细信息
     * @param id    不良管理记录id
     * @return java.util.List<net.airuima.web.rest.procedure.quality.dto.InspectUnqualifiedVO>详细列表信息
     */
    @Transactional(readOnly = true)
    public List<InspectUnqualifiedDTO> findDetailById(Long id) {
        // 创建返回结果集
        List<InspectUnqualifiedDTO> result = new ArrayList<>();
        List<InspectUnqualifiedDetail> inspectUnqualifiedDetailList = inspectUnqualifiedDetailRepository.findByInspectUnqualifiedIdAndDeleted(id, Constants.LONG_ZERO);
        // 按照前端要求格式处理数据
        if(ValidateUtils.isValid(inspectUnqualifiedDetailList)){
            Map<String, List<InspectUnqualifiedDetail>> snMap = inspectUnqualifiedDetailList.stream().collect(Collectors.groupingBy(InspectUnqualifiedDetail::getSn));
            if(ValidateUtils.isValid(snMap)){
                for (String sn : snMap.keySet()){
                    // 检测项目列表
                    List<InspectUnqualifiedDTO.CheckItemInfo> checkItemInfoList = new ArrayList<>();
                    List<InspectUnqualifiedDetail> inspectUnqualifiedDetails = snMap.get(sn);
                    inspectUnqualifiedDetails.forEach(detail -> {
                        // 创建返回的检测项目对象
                        InspectUnqualifiedDTO.CheckItemInfo checkItemInfo = new InspectUnqualifiedDTO.CheckItemInfo();
                        BeanUtils.copyProperties(detail.getCheckItem(), checkItemInfo);
                        // 创建返回的不良详细对象
                        InspectUnqualifiedDTO.InspectUnqualifiedDetailInfo inspectUnqualifiedDetailVO = new InspectUnqualifiedDTO.InspectUnqualifiedDetailInfo();
                        BeanUtils.copyProperties(detail, inspectUnqualifiedDetailVO);
                        checkItemInfo.setInspectUnqualifiedDetail(inspectUnqualifiedDetailVO);
                        // 把项目添加到集合
                        checkItemInfoList.add(checkItemInfo);
                    });
                    result.add(new InspectUnqualifiedDTO(sn, checkItemInfoList));
                }
            }
        }
        return result;
    }

    /**
     * 抽检终检 不良处理
     * @param inspectUnqualifiedResultDto
     * <AUTHOR>
     * @date  2023/5/9
     * @return void
     */
    public void inspectionUnqualifiedResult(InspectUnqualifiedResultDTO inspectUnqualifiedResultDto) {
        InspectUnqualified inspectUnqualified = inspectUnqualifiedRepository.findByIdAndDeleted(inspectUnqualifiedResultDto.getInspectUnqualifiedId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.inspectUnqualifiedIsNotExist", "不良品记录不存在"));

        List<InspectUnqualifiedDetail> inspectUnqualifiedDetailList = inspectUnqualifiedDetailRepository.findByInspectUnqualifiedIdAndDeleted(inspectUnqualified.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(inspectUnqualifiedDetailList)){
            return;
        }
        if (!ValidateUtils.isValid(inspectUnqualifiedResultDto.getSnUnqualifiedItemList())){
            throw new ResponseException("error.snUnqualifiedItemNotFound", "sn对应不良项不存在");
        }
        //验证sn真实性
        boolean virtual = inspectUnqualifiedDetailList.get(Constants.INT_ZERO).getVirtual();
        CheckHistory checkHistory = inspectUnqualified.getCheckHistory();
        //真实sn
        if (!virtual){
            //sn状态
            processSn(inspectUnqualifiedResultDto, inspectUnqualified,checkHistory);
        }else {
            //容器
            if(!ObjectUtils.isEmpty(checkHistory.getContainerCode())){
                Optional<ContainerDetail>  containerDetailOptional = !ObjectUtils.isEmpty(inspectUnqualified.getCheckHistory().getSubWorkSheet())?
                        containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(inspectUnqualified.getCheckHistory().getSubWorkSheet().getId(),inspectUnqualified.getCheckHistory().getStep().getId(),inspectUnqualified.getCheckHistory().getContainerCode(),Constants.LONG_ZERO):
                        containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(inspectUnqualified.getCheckHistory().getWorkSheet().getId(),inspectUnqualified.getCheckHistory().getStep().getId(),inspectUnqualified.getCheckHistory().getContainerCode(),Constants.LONG_ZERO);
                ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));
                //虚拟sn 容器批量
                    MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
                    maintainHistory.setNumber(inspectUnqualified.getNumber())
                            .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                            .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                            .setStep(inspectUnqualified.getCheckHistory().getStep())
                            .setSubWorkSheet(inspectUnqualified.getCheckHistory().getSubWorkSheet())
                            .setWorkSheet(inspectUnqualified.getCheckHistory().getWorkSheet())
                            .setContainerDetail(containerDetail)
                            .setUnqualifiedItem(unqualifiedItemRepository.findByIdAndDeleted(inspectUnqualifiedResultDto.getSnUnqualifiedItemList().get(Constants.INT_ZERO).getUnqualifiedItemId(),Constants.LONG_ZERO).orElse(null))
                            .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                //增加维修分析来源
                maintainHistory.setCheckHistoryId(checkHistory.getId())
                        .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
                rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
            }else {
                MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
                maintainHistory.setNumber(inspectUnqualified.getNumber())
                        .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                        .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                        .setStep(inspectUnqualified.getCheckHistory().getStep())
                        .setSubWorkSheet(inspectUnqualified.getCheckHistory().getSubWorkSheet())
                        .setWorkSheet(inspectUnqualified.getCheckHistory().getWorkSheet())
                        .setUnqualifiedItem(unqualifiedItemRepository.findByIdAndDeleted(inspectUnqualifiedResultDto.getSnUnqualifiedItemList().get(Constants.INT_ZERO).getUnqualifiedItemId(),Constants.LONG_ZERO).orElse(null))
                        .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                //增加维修分析来源
                maintainHistory.setCheckHistoryId(checkHistory.getId())
                        .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
                rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
            }
        }
        inspectUnqualified.setDealResult(inspectUnqualifiedResultDto.getDealResult());
        inspectUnqualifiedRepository.save(inspectUnqualified);
    }

    /**
     * 抽检终检 不良处理
     * @param inspectUnqualifiedResultDto
     * @param inspectUnqualified
     */
    private void processSn(InspectUnqualifiedResultDTO inspectUnqualifiedResultDto, InspectUnqualified inspectUnqualified,CheckHistory checkHistory) {
        List<String> snList = inspectUnqualifiedResultDto.getSnUnqualifiedItemList().stream().map(InspectUnqualifiedResultDTO.SnUnqualifiedItem::getSn).distinct().collect(Collectors.toList());
        List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snList, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(snWorkStatuses) || snWorkStatuses.size() != snList.size()){
            throw new ResponseException("error.snStatusError", "sn状态异常");
        }
        Map<String, SnWorkStatus> snWorkStatusMap = snWorkStatuses.stream()
                .collect(Collectors.toMap(SnWorkStatus::getSn, Function.identity()));
        //不良项目
        List<Long> unqualifiedItemIds = inspectUnqualifiedResultDto.getSnUnqualifiedItemList().stream().map(InspectUnqualifiedResultDTO.SnUnqualifiedItem::getUnqualifiedItemId).distinct().collect(Collectors.toList());
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(unqualifiedItemIds, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemList) || unqualifiedItemList.size() != unqualifiedItemIds.size()){
            throw new ResponseException("error.unqualifiedItemNotFound", "部分不良项目不存在");
        }
        Map<Long, UnqualifiedItem> unqualifiedItemMap =  unqualifiedItemList.stream()
                .collect(Collectors.toMap(UnqualifiedItem::getId, Function.identity()));

        List<MaintainHistoryDTO> maintainHistoryList = Lists.newArrayList();
        inspectUnqualifiedResultDto.getSnUnqualifiedItemList().forEach(snUnqualifiedItem -> {
            MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
            maintainHistory.setNumber(inspectUnqualified.getNumber())
                    .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                    .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                    .setStep(inspectUnqualified.getCheckHistory().getStep())
                    .setSubWorkSheet(inspectUnqualified.getCheckHistory().getSubWorkSheet())
                    .setWorkSheet(inspectUnqualified.getCheckHistory().getWorkSheet())
                    .setSnWorkStatus(snWorkStatusMap.get(snUnqualifiedItem.getSn()))
                    .setUnqualifiedItem(unqualifiedItemMap.get(snUnqualifiedItem.getUnqualifiedItemId()))
                    .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            //增加维修分析来源
            maintainHistory.setCheckHistoryId(checkHistory.getId())
                    .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));

            maintainHistoryList.add(maintainHistory);
        });
        rbaseMaintainHistoryProxy.batchSaveInstance(maintainHistoryList);
    }
}
