package net.airuima.rbase.service.base.scene;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.scene.WorkLineDTO;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.service.base.scene.api.IWorkStationService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产线Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkLineService extends CommonJpaService<WorkLine> {


    private final WorkLineRepository workLineRepository;
    private final WorkCellRepository workCellRepository;
    @Autowired
    private IWorkStationService[] workStationServices;

    public WorkLineService(WorkLineRepository workLineRepository,
                           WorkCellRepository workCellRepository) {
        this.workLineRepository = workLineRepository;
        this.workCellRepository = workCellRepository;
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public Page<WorkLine> find(Specification<WorkLine> spec, Pageable pageable) {
        return workLineRepository.findAll(spec, pageable);
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public List<WorkLine> find(Specification<WorkLine> spec) {
        return workLineRepository.findAll(spec);
    }

    @FetchMethod
    @Override
    @Transactional(readOnly = true)
    public Page<WorkLine> findAll(Pageable pageable) {
        return workLineRepository.findAll(pageable);
    }

    @FetchMethod
    @Override
    public <S extends WorkLine> S save(S entity) {
        return super.save(entity);
    }

    /**
     * 通过名称和编码模糊查询生产线
     *
     * @param isEnable 是否启用
     * @param size     前N行
     * @param text     名称或编码
     * @return 生产线列表
     */
    @Transactional(readOnly = true)
    public List<WorkLine> findByNameOrCode(String text, Boolean isEnable, Integer size) {
        Page<WorkLine> page = workLineRepository.findByNameOrCode(text, PageRequest.of(net.airuima.constant.Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     * 通过组织架构ID获取生产线列表
     *
     * @param organizationId 组织架构ID
     * @return List<WorkLine>
     * <AUTHOR>
     * @date 2020-12-28
     **/
    @Transactional(readOnly = true)
    public List<WorkLine> findByOrganizationId(Long organizationId) {
        return workLineRepository.findByOrganizationIdAndIsEnableAndDeleted(organizationId, Boolean.TRUE, Constants.LONG_ZERO);
    }

    /**
     * 通过生产线ID获取工站及工位信息
     *
     * @param id 生产线ID
     * @return WorkLineDTO
     * <AUTHOR>
     * @date 2021-04-12
     **/
    @Transactional(readOnly = true)
    public WorkLineDTO findTreeDataById(Long id) {
        AtomicReference<WorkLineDTO> workLineDto = new AtomicReference<>(new WorkLineDTO());
        Optional<WorkLine> workLineOptional = workLineRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        workLineOptional.ifPresent(workLine -> {
            workLineDto.set(MapperUtils.map(workLine, WorkLineDTO.class));
            List<WorkLineDTO.WorkStationInfo> workStationInfoList = workStationServices[0].findWorStationWorkCellInfo(workLine.getId());
            //若配置的生产线有工站则返回各个工站及工站内的工位信息，否则直接返回生产线内的各个工位信息
            if (!CollectionUtils.isEmpty(workStationInfoList)) {
                workLineDto.get().setWorkStationInfoList(workStationInfoList);
            } else {
                List<WorkCell> workCellList = workCellRepository.findByWorkLineIdAndDeletedOrderByOrderNumberAsc(workLine.getId(), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(workCellList)) {
                    workLineDto.get().setWorkCellInfoList(MapperUtils.mapAll(workCellList, WorkLineDTO.WorkCellInfo.class));
                }
            }
        });
        return workLineDto.get();
    }
}
