package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.service.CommonJpaService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellService extends CommonJpaService<WorkCell> {
    private static final String WORK_CELL_ENTITY_GRAPH = "workCellEntityGraph";
    private final WorkCellRepository workCellRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;

    public WorkCellService(WorkCellRepository workCellRepository) {
        this.workCellRepository = workCellRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkCell> find(Specification<WorkCell> spec, Pageable pageable) {
        return workCellRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WorkCell> find(Specification<WorkCell> spec) {
        return workCellRepository.findAll(spec, new NamedEntityGraph(WORK_CELL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkCell> findAll(Pageable pageable) {
        return workCellRepository.findAll(pageable, new NamedEntityGraph(WORK_CELL_ENTITY_GRAPH));
    }

    /**
     * 通过名称和编码模糊查询工位
     *
     * @param size     前N行
     * @param isEnable 是否启用
     * @param text     名称或编码
     * @return 工位列表
     */
    @Transactional(readOnly = true)
    public List<WorkCell> findByNameOrCode(String text, Boolean isEnable, Integer size) {
        Page<WorkCell> page = workCellRepository.findByNameOrCode(text, Boolean.TRUE, PageRequest.of(net.airuima.constant.Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     * 根据工站ID获取所有工位
     *
     * @param workStationId 工站ID
     * @return List<WorkCell>
     * <AUTHOR>
     * @date 2021-04-14
     **/
    @Transactional(readOnly = true)
    public List<WorkCell> findByWorkStationId(Long workStationId) {
        return workCellRepository.findByWorkStationIdAndDeletedOrderByOrderNumberAsc(workStationId, Constants.LONG_ZERO);
    }

    @Override
    public <S extends WorkCell> S save(S entity) {
        Long id = entity.getId();
        if (Objects.nonNull(id)) {
            List<Step> stepList = workCellStepRepository.findByWorkCellId(id);
            if (CollectionUtils.isNotEmpty(stepList)) {
                entity.setSteps(new HashSet<>(stepList));
            }
        }
        entity.setDeleted(Constants.LONG_ZERO);
        workCellRepository.save(entity);
        return entity;
    }
}
