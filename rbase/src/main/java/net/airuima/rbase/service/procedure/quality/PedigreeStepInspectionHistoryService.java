package net.airuima.rbase.service.procedure.quality;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistory;
import net.airuima.rbase.repository.procedure.quality.PedigreeStepInspectionHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工序检查历史记录Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepInspectionHistoryService extends CommonJpaService<PedigreeStepInspectionHistory> {

    private final PedigreeStepInspectionHistoryRepository pedigreeStepInspectionHistoryRepository;

    public PedigreeStepInspectionHistoryService(PedigreeStepInspectionHistoryRepository pedigreeStepInspectionHistoryRepository) {
        this.pedigreeStepInspectionHistoryRepository = pedigreeStepInspectionHistoryRepository;
    }

    @Override
    @FetchMethod
    public Page<PedigreeStepInspectionHistory> find(Specification<PedigreeStepInspectionHistory> spec, Pageable pageable) {
        return pedigreeStepInspectionHistoryRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<PedigreeStepInspectionHistory> find(Specification<PedigreeStepInspectionHistory> spec) {
        return pedigreeStepInspectionHistoryRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<PedigreeStepInspectionHistory> findAll(Pageable pageable) {
        return pedigreeStepInspectionHistoryRepository.findAll(pageable);
    }
}
