package net.airuima.rbase.service.procedure.batch.impl;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PIRworkerCacheService {
    @BeanDefine(value = "net.airuima.rworker.service.rworker.cache.IRworkerCacheService", interfaceBean = true)
    public void deleteCacheByBatchWorkDetail(BatchWorkDetail batchWorkDetail, List<ContainerDetail> containerDetailList, List<SnWorkDetail> snWorkDetailList) {

    }

    @BeanDefine(value = "net.airuima.rworker.service.rworker.cache.IRworkerCacheService", interfaceBean = true)
    public void deleteCacheByContainerDetail(ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList) {

    }

    @BeanDefine(value = "net.airuima.rworker.service.rworker.cache.IRworkerCacheService", interfaceBean = true)
    public void deleteCacheBySnWorkDetail(SnWorkDetail snWorkDetail) {

    }

    @BeanDefine(value = "net.airuima.rworker.service.rworker.cache.IRworkerCacheService", interfaceBean = true)
    public void validateCacheWhenRequestTodoStep(int category, String serialNumber) {

    }
}
