package net.airuima.rbase.service.procedure.aps.plugin;

import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.aps.WorkSheetDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/31
 */
@FuncDefault
public interface IWorkSheetService {

    /**
     * 新增生产工单信息
     *
     * @param workSheetDto 总工单参数信息
     * @return net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO 正常单与返工单下交返回数据
     * <AUTHOR>
     * @date 2021-01-12
     **/
    default WorkSheetResDTO saveInstance(WorkSheetDTO workSheetDto) throws Exception {
       return new WorkSheetResDTO(new BaseDTO(Constants.KO));
    }

    /**
     * 更新工单
     * @param workSheetDto 总工单参数信息
     * <AUTHOR>
     * @date  2023/4/20
     * @return net.airuima.rbase.service.procedure.aps.dto.WorkSheetResDTO 正常单与返工单下交返回数据
     */
    default WorkSheetResDTO updateInstance(WorkSheetDTO workSheetDto){
        return new WorkSheetResDTO();
    }

    /**
     * 新增修改工单 后置处理
     * @param workSheetDto 总工单参数信息
     */
    default void afterUpdateInstance(WorkSheetDTO workSheetDto){return;}


    /**
     * 导出或者打印(子)工单工序流程卡
     * @param workSheetId 工单ID
     * @param subWorkSheetId 子工单ID
     * @param templateCode 模板编码
     * @param response 响应
     * <AUTHOR>
     * @since 1.8.1
     */
    default void  printWorkSheetStepCard(Long workSheetId, Long subWorkSheetId,String templateCode, HttpServletResponse response) throws Exception{

    }

    /**
     * 同步WMS入库
     * @param id 工单ID
     */
    default void syncWms(Long id) {

    }
}
