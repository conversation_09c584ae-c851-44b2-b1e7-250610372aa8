package net.airuima.rbase.service.report;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.PoiMergeCellUtil;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailFacilityRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.SnWorkDetailWearingPartRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailMaterialBatchService;
import net.airuima.rbase.service.procedure.batch.WsStepService;
import net.airuima.rbase.service.report.api.ITraceReportService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.report.dto.*;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.BatchForwardTraceStepDetailExportDTO;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.ContainerForwardTraceStepDetailExportDTO;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 追溯报表Service
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class TraceReportService implements ITraceReportService {
    private static final String NO_DATA = "查无数据!";
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;
    @Autowired
    private ContainerDetailFacilityRepository containerDetailFacilityRepository;
    @Autowired
    private SnWorkDetailWearingPartRepository snWorkDetailWearingPartRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private WearingPartRepository wearingPartRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WsStepService wsStepService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;

    @Autowired
    private BatchWorkDetailMaterialBatchService batchWorkDetailMaterialBatchService;
    /**
     * 生成过程正向追溯
     *
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * @return : net.airuima.web.rest.report.dto.ForwardTraceResultDTO
     * <AUTHOR>
     * @date 2023/6/6
     **/
    @Transactional(readOnly = true)
    public List<ForwardTraceResultDTO> workSheetTraceReport(Integer category, String serialNumber) {
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        //子工单号
        if (category == Constants.INT_TWO) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeletedWhenDataFilter(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.SubWorkSheetNotFound", "子工单不存在"));
            ForwardTraceResultDTO forwardTraceResultDTO = getSubWorkSheet(subWorkSheet, subWsProductionMode);
            return Collections.singletonList(forwardTraceResultDTO);
        }
        //工单号
        else if (category == Constants.INT_ONE) {
            WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeletedWhenDataFilter(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.WorkSheetNotFound", "工单不存在"));
            ForwardTraceResultDTO forwardTraceResultDTO = getWorkSheet(workSheet, subWsProductionMode, getReturnCategory(workSheet.getCategory()));
            return Collections.singletonList(forwardTraceResultDTO);
        }
        //订单号
        else if (category == Constants.INT_ZERO) {
            SaleOrder saleOrder = saleOrderRepository.findBySerialNumberAndDeletedWhenDataFilter(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.SaleOrderNotFound", "订单不存在"));
            ForwardTraceResultDTO forwardTraceResultDTO = getSaleOrder(saleOrder, subWsProductionMode);
            return Collections.singletonList(forwardTraceResultDTO);
        }
        //容器
        else if (category == Constants.INT_THREE) {
            if (subWsProductionMode) {
                List<SubWorkSheet> subWorkSheetList = containerDetailRepository.findSubWorkSheetByContainerCodeAndDeleted(serialNumber, Constants.LONG_ZERO);
                return subWorkSheetList.stream().map(i -> this.getSubWorkSheet(i, subWsProductionMode)).collect(Collectors.toList());
            } else {
                List<WorkSheet> workSheetList = containerDetailRepository.findWorkSheetByContainerCodeAndDeleted(serialNumber, Constants.LONG_ZERO);
                return workSheetList.stream().map(i -> this.getWorkSheet(i, subWsProductionMode, getReturnCategory(i.getCategory()))).collect(Collectors.toList());
            }
        }
        //SN
        else if (category == Constants.INT_FOUR) {
            if (subWsProductionMode) {
                List<SubWorkSheet> subWorkSheetList = snWorkDetailRepository.findSubWorkSheetBySnAndDeleted(serialNumber, Constants.LONG_ZERO);
                return subWorkSheetList.stream().map(i -> this.getSubWorkSheet(i, subWsProductionMode)).collect(Collectors.toList());
            } else {
                List<WorkSheet> workSheetList = snWorkDetailRepository.findWorkSheetBySnAndDeleted(serialNumber, Constants.LONG_ZERO);
                return workSheetList.stream().map(i -> this.getWorkSheet(i, subWsProductionMode, getReturnCategory(i.getCategory()))).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    /**
     * 工单类型 映射为 返回前端展示类型
     *
     * @param category
     * @return : java.lang.Integer
     * <AUTHOR>
     * @date 2023/6/8
     **/
    public Integer getReturnCategory(Integer category) {
        //1:正常单,-1:返工单,0返修单 -> 1:正常单,3:返工单,4:返修单
        if (category == Constants.INT_ONE) {
            return Constants.INT_ONE;
        } else if (category == Constants.APPROVE) {
            return Constants.INT_THREE;
        } else if (category == Constants.INT_ZERO) {
            return Constants.INT_FOUR;
        } else {
            throw new ResponseException("error.CategoryError", "不为正常单、返工单或返修单");
        }
    }

    /**
     * 组装订单返回DTO
     *
     * @param saleOrder 销售订单
     * @return : net.airuima.web.rest.report.dto.ForwardTraceResultDTO
     * <AUTHOR>
     * @date 2023/6/6
     **/
    public ForwardTraceResultDTO getSaleOrder(SaleOrder saleOrder, boolean subWsProductionMode) {
        //组装订单返回DTO TODO:销售订单
        ForwardTraceResultDTO forwardTraceResultDTO = new ForwardTraceResultDTO().setCategory(Constants.INT_ZERO)
                .setSerialNumber(saleOrder.getSerialNumber())
                .setSerialProgress(saleOrder.getProgress())
//                .setPedigreeNames(saleOrder.getPedigree().getName())
                .setPassRate(Constants.SLASH)
                .setFirstPassRate(Constants.SLASH)
                .setNumber(saleOrder.getNumber()).setInputNumber(saleOrder.getProductionQuantity()).setFinishNumber(saleOrder.getFinishNumber())
                .setDeliveryDate(saleOrder.getDeliveryDate())
//                .setPlanStartDate(saleOrder.getPlanStartDate().atTime(Constants.INT_ZERO, Constants.INT_ZERO, Constants.INT_ZERO))
//                .setPlanEndDate(saleOrder.getPlanEndDate().atTime(Constants.INT_ZERO, Constants.INT_ZERO, Constants.INT_ZERO))
                .setShowStepDetail(Constants.FALSE)
                .setClientName(Optional.ofNullable(saleOrder.getClientDTO()).orElse(new ClientDTO()).getName());
        forwardTraceResultDTO.setId(saleOrder.getId());
        forwardTraceResultDTO.setCreatedBy(saleOrder.getCreatedBy());
        forwardTraceResultDTO.setCreatedDate(saleOrder.getCreatedDate());
        forwardTraceResultDTO.setCustom1(saleOrder.getCustom1()).setCustom2(saleOrder.getCustom2()).setCustom3(saleOrder.getCustom3()).setCustom4(saleOrder.getCustom4()).setCustom5(saleOrder.getCustom5());
        //组装工单返回DTO
        List<WorkSheet> workSheetList = workSheetRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        //过滤已取消和审批中的工单
        if(!CollectionUtils.isEmpty(workSheetList)){
            workSheetList = workSheetList.stream().filter(workSheet -> workSheet.getStatus()>= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()).toList();
        }
        List<ForwardTraceResultDTO> forwardTraceResultDTOList = workSheetList.stream().map(i -> getWorkSheet(i, subWsProductionMode, Constants.INT_ONE)).collect(Collectors.toList());
        forwardTraceResultDTO.setSubForwardTraceResultDTOList(forwardTraceResultDTOList);

        if (ValidateUtils.isValid(forwardTraceResultDTOList)){
            forwardTraceResultDTO
                    .setQualifiedNumber(forwardTraceResultDTOList.stream().mapToInt(ForwardTraceResultDTO::getQualifiedNumber).sum())
                    .setUnqualifiedNumber(forwardTraceResultDTOList.stream().mapToInt(ForwardTraceResultDTO::getUnqualifiedNumber).sum());
        }
        return forwardTraceResultDTO;
    }

    /**
     * 组装工单返回DTO
     *
     * @param workSheet         工单
     * @param categoryWorkSheet 工单类型（1:正常单，3:返工单, 4:返修单）
     * @return : net.airuima.web.rest.report.dto.ForwardTraceResultDTO
     * <AUTHOR>
     * @date 2023/6/6
     **/
    public ForwardTraceResultDTO getWorkSheet(WorkSheet workSheet, boolean subWsProductionMode, Integer categoryWorkSheet) {
        //组装工单返回DTO
        ForwardTraceResultDTO forwardTraceResultDTO = new ForwardTraceResultDTO().setCategory(categoryWorkSheet)
                .setSerialNumber(workSheet.getSerialNumber())
                .setSerialProgress(workSheet.getProgress())
                .setPassRate(NumberUtils.percentNumber(workSheet.getNumber() != Constants.INT_ZERO ? NumberUtils.divide(workSheet.getQualifiedNumber(), workSheet.getNumber(), Constants.INT_FOUR) : BigDecimal.ZERO, Constants.INT_TWO))
                .setFirstPassRate(NumberUtils.percentNumber(workSheet.getNumber() != Constants.INT_ZERO ? NumberUtils.divide(workSheet.getQualifiedNumber() - workSheet.getReworkQualifiedNumber(), workSheet.getNumber(), Constants.INT_FOUR) : BigDecimal.ZERO, Constants.INT_TWO))
                .setPedigreeNames(workSheet.getPedigree().getName())
                .setQualifiedNumber(workSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber())
                .setInputNumber(workSheet.getNumber()).setFinishNumber(workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber())
                .setDeliveryDate(workSheet.getDeliveryDate()).setPlanStartDate(workSheet.getPlanStartDate()).setPlanEndDate(workSheet.getPlanEndDate())
                .setShowStepDetail(!subWsProductionMode)
                .setClientName(Optional.ofNullable(workSheet.getClientDTO()).orElse(new ClientDTO()).getName());
        forwardTraceResultDTO.setCreatedDate(workSheet.getCreatedDate());
        forwardTraceResultDTO.setCreatedBy(workSheet.getCreatedBy());
        forwardTraceResultDTO.setId(workSheet.getId());
        forwardTraceResultDTO.setCustom1(workSheet.getCustom1()).setCustom2(workSheet.getCustom2()).setCustom3(workSheet.getCustom3()).setCustom4(workSheet.getCustom4()).setCustom5(workSheet.getCustom5());
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            forwardTraceResultDTO.setFirstPassRate(Constants.SLASH);
        }
        List<ForwardTraceResultDTO> forwardTraceResultDTOList = new ArrayList<>();
        if (subWsProductionMode){
            //组装子工单返回DTO
            List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(subWorkSheetList)){
                //通过子工单现象获取对应的工序不了总和
                List<ForwardTraceResultDTO.UnqualifiedItemDetail> unqualifiedItemDetails = wsStepUnqualifiedItemRepository.findBySubWorkSheetUnqualifiedItem(subWorkSheetList.stream().map(SubWorkSheet::getId).collect(Collectors.toList()));
                forwardTraceResultDTO.setUnqualifiedItemDetailList(unqualifiedItemDetails);
            }
            forwardTraceResultDTOList.addAll(subWorkSheetList.stream().map(i -> getSubWorkSheet(i, subWsProductionMode)).toList());
        }else {
            //通过工单获取工序不了信息总和
            List<ForwardTraceResultDTO.UnqualifiedItemDetail> unqualifiedItemDetails = wsStepUnqualifiedItemRepository.findByWorkSheetUnqualifiedItem(Collections.singletonList(workSheet.getId()));
            forwardTraceResultDTO.setUnqualifiedItemDetailList(unqualifiedItemDetails);
        }
        //如果当前为正常单，则组装返工单返回DTO
        if (categoryWorkSheet == Constants.INT_ONE) {
            List<WsRework> wsReworkList = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            List<WorkSheet> reworkSheetList = wsReworkList.stream().map(WsRework::getReworkWorkSheet).toList();
            forwardTraceResultDTOList.addAll(reworkSheetList.stream().map(reworkSheet -> getWorkSheet(reworkSheet, subWsProductionMode, Constants.INT_THREE)).toList());
        }
        forwardTraceResultDTO.setSubForwardTraceResultDTOList(forwardTraceResultDTOList);
        return forwardTraceResultDTO;
    }

    /**
     * 组装子工单返回DTO
     *
     * @param subWorkSheet 子工单
     * @return : net.airuima.web.rest.report.dto.ForwardTraceResultDTO
     * <AUTHOR>
     * @date 2023/6/6
     **/
    public ForwardTraceResultDTO getSubWorkSheet(SubWorkSheet subWorkSheet, boolean subWsProductionMode) {
        //组装子工单返回DTO
        ForwardTraceResultDTO forwardTraceResultDTO = new ForwardTraceResultDTO().setCategory(Constants.INT_TWO)
                .setSerialNumber(subWorkSheet.getSerialNumber())
                .setSerialProgress(subWorkSheet.getProgress())
                .setFirstPassRate(Constants.SLASH)
                .setPassRate(NumberUtils.percentNumber(subWorkSheet.getNumber() != Constants.INT_ZERO ? NumberUtils.divide(subWorkSheet.getQualifiedNumber(), subWorkSheet.getNumber(), Constants.INT_FOUR) : BigDecimal.ZERO, Constants.INT_TWO))
                .setPedigreeNames(subWorkSheet.getWorkSheet().getPedigree().getName()).setInputNumber(subWorkSheet.getNumber())
                .setQualifiedNumber(subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber())
                .setFinishNumber(subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber())
                .setDeliveryDate(subWorkSheet.getWorkSheet().getDeliveryDate()).setPlanStartDate(subWorkSheet.getWorkSheet().getPlanStartDate())
                .setPlanEndDate(subWorkSheet.getWorkSheet().getPlanEndDate()).setShowStepDetail(subWsProductionMode)
                .setClientName(Optional.ofNullable(subWorkSheet.getWorkSheet().getClientDTO()).orElse(new ClientDTO()).getName());
        forwardTraceResultDTO.setId(subWorkSheet.getId());
        forwardTraceResultDTO.setCreatedDate(subWorkSheet.getCreatedDate());
        forwardTraceResultDTO.setCreatedBy(subWorkSheet.getCreatedBy());
        forwardTraceResultDTO.setCustom1(subWorkSheet.getCustom1()).setCustom2(subWorkSheet.getCustom2()).setCustom3(subWorkSheet.getCustom3()).setCustom4(subWorkSheet.getCustom4()).setCustom5(subWorkSheet.getCustom5());
        List<ForwardTraceResultDTO.UnqualifiedItemDetail> unqualifiedItemDetails = wsStepUnqualifiedItemRepository.findBySubWorkSheetUnqualifiedItem(Collections.singletonList(subWorkSheet.getId()));
        return forwardTraceResultDTO.setUnqualifiedItemDetailList(unqualifiedItemDetails);
    }

    /**
     * 通过子工单ID查询工序信息
     *
     * @param wsId         工单ID/子工单ID
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * @return : net.airuima.web.rest.report.dto.ForwardTraceStepDetailDTO
     * <AUTHOR>
     * @date 2023/6/6
     **/
    public List<ForwardTraceStepDetailDTO> stepDetail(Long wsId, int category, String serialNumber) {
        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        //批量
        List<BatchForwardTraceStepDetailExportDTO> batchDataList = subWsProductionMode ?
                batchWorkDetailRepository.findByForwardTraceReportExportBatchAndSubWorkSheet(wsId) :
                batchWorkDetailRepository.findByForwardTraceReportExportBatchAndWorkSheet(wsId);
        if (!ValidateUtils.isValid(batchDataList)) {
            return Collections.emptyList();
        }
        //容器
        Map<String, List<ContainerForwardTraceStepDetailExportDTO>> containerMap = new HashMap<>();
        List<ContainerForwardTraceStepDetailExportDTO> containerDataList = subWsProductionMode ?
                batchWorkDetailRepository.findByForwardTraceReportExportContainerAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null) :
                batchWorkDetailRepository.findByForwardTraceReportExportContainerAndWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null);

        //容器sn
        List<SnForwardTraceStepDetailExportDTO> containerSnDataList;
        Map<String, List<SnForwardTraceStepDetailExportDTO>> containerSnMap = new HashMap<>();
        if (ValidateUtils.isValid(containerDataList)) {
            containerMap = containerDataList.stream().collect(Collectors.groupingBy(ContainerForwardTraceStepDetailExportDTO::getStepCode));

            containerSnDataList = subWsProductionMode ?
                    batchWorkDetailRepository.findByForwardTraceReportExportContainerSnAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null) :
                    batchWorkDetailRepository.findByForwardTraceReportExportContainerSnAndWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null);
            if (ValidateUtils.isValid(containerSnDataList)) {
                containerSnMap = containerSnDataList.stream().collect(Collectors.groupingBy(SnForwardTraceStepDetailExportDTO::getStepCode));
            }
        } else {
            containerSnDataList = Lists.newArrayList();
        }
        //sn
        List<SnForwardTraceStepDetailExportDTO> snDataList = subWsProductionMode ?
                batchWorkDetailRepository.findByForwardTraceReportExportSnAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null) :
                batchWorkDetailRepository.findByForwardTraceReportExportSnAndWorkSheet(wsId, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null);
        Map<String, List<SnForwardTraceStepDetailExportDTO>> snMap = new HashMap<>();
        if (ValidateUtils.isValid(snDataList)) {
            snMap = snDataList.stream().collect(Collectors.groupingBy(SnForwardTraceStepDetailExportDTO::getStepCode));
        }

        Map<String, List<ContainerForwardTraceStepDetailExportDTO>> finalContainerMap = containerMap;
        Map<String, List<SnForwardTraceStepDetailExportDTO>> finalSnMap = snMap;

        List<ForwardTraceStepDetailDTO> forwardTraceStepDetailDtoList = Lists.newArrayList();
        Map<String, List<SnForwardTraceStepDetailExportDTO>> finalContainerSnMap = containerSnMap;
        batchDataList.stream().collect(Collectors.groupingBy(BatchForwardTraceStepDetailExportDTO::getStepCode))
                .forEach((stepCode, batchList) -> {
                    List<ForwardTraceStepDetailDTO.DateUnit> dateUnits = null;

                    //容器不存在 -》 只存在两种 批量 或者  单支
                    if (!ValidateUtils.isValid(containerDataList) || (ValidateUtils.isValid(containerDataList) && !ValidateUtils.isValid(finalContainerMap.get(stepCode)))) {
                        //容器不存在，那么直接跳过本道工序
                        if (Objects.equals(category, Constants.INT_THREE)) {
                            return;
                        }
                        //批量
                        if (!ValidateUtils.isValid(snDataList) || (ValidateUtils.isValid(snDataList) && !ValidateUtils.isValid(finalSnMap.get(stepCode)))) {
                            if (Objects.equals(category, Constants.INT_FOUR)) {
                                return;
                            }
                            dateUnits = readBatchInfo(batchList);
                        }
                        //单支
                        else {
                            dateUnits = readSn(finalSnMap.get(stepCode));
                        }
                    } else {
                        //容器
                        if (!ValidateUtils.isValid(containerSnDataList) || (ValidateUtils.isValid(containerSnDataList) && !ValidateUtils.isValid(finalContainerSnMap.get(stepCode)))) {
                            dateUnits = readContainer(finalContainerMap.get(stepCode));
                        }
                        //容器单支
                        else {
                            dateUnits = readContainerSn(finalContainerSnMap.get(stepCode));
                        }

                    }
                    if (ValidateUtils.isValid(dateUnits)) {
                        BatchForwardTraceStepDetailExportDTO result = batchList.get(Constants.INT_ZERO);
                        ForwardTraceStepDetailDTO forwardTraceStepDetailDto = new ForwardTraceStepDetailDTO();
                        forwardTraceStepDetailDto
                                .setStepName(result.getStepName())
                                .setStepCode(result.getStepCode())
                                .setStartDate(result.getStartDate())
                                .setEndDate(result.getEndDate())
                                .setWorkHour(BigDecimal.valueOf(result.getWorkHour()))
                                .setCustom1(result.getCustom1()).setCustom2(result.getCustom2())
                                .setInputNumber(result.getStepInputNumber())
                                .setQualifiedNumber(result.getStepQualifiedNumber())
                                .setUnqualifiedNumber(result.getStepUnqualifiedNumber())
                                .setQualifiedRate(NumberUtils.divide(result.getStepQualifiedNumber(), result.getStepInputNumber(), Constants.INT_TWO))
                                .setDataUnitList(dateUnits);
                        forwardTraceStepDetailDtoList.add(forwardTraceStepDetailDto);
                    }

                });
        if (ValidateUtils.isValid(forwardTraceStepDetailDtoList)) {
            return forwardTraceStepDetailDtoList.stream()
                    .sorted(Comparator.comparing(ForwardTraceStepDetailDTO::getStartDate))
                    .collect(Collectors.toList());
        }
        return forwardTraceStepDetailDtoList;
    }

    /**
     * 组装生产过程正向追溯导出DTO集合
     *
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号）
     * @param serialNumber 单据号
     * @return : java.util.List<net.airuima.web.rest.report.dto.ForwardTraceResultDTO>
     * <AUTHOR>
     * @date 2023/6/7
     **/
    @Override
    public void workSheetTraceReportExport(Integer category, String serialNumber,String excelType, HttpServletResponse response) {
        try{
            List<ForwardTraceResultDTO> forwardTraceResultDTOList = this.workSheetTraceReport(category, serialNumber);
            List<ForwardTraceResultDTO> result = new ArrayList<>();
            if (!CollectionUtils.isEmpty(forwardTraceResultDTOList)) {
                //如果不为空，则递归将追溯DTO转为DTOList并进行合并
                forwardTraceResultDTOList.forEach(forwardTraceResultDTO -> result.addAll(forwardTraceResultDTOToList(forwardTraceResultDTO)));
            }
            String prefix = Objects.nonNull(excelType) && excelType.equals("xls")?".xls":".xlsx";
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setFreezeCol(net.airuima.constant.Constants.INT_TWO);
            if(Objects.nonNull(excelType) && excelType.equals("xls")){
                exportParams.setType(ExcelType.HSSF);
            }
            String fileName = "生产过程正向追溯" + System.currentTimeMillis() + prefix;
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ForwardTraceResultDTO.class, result);
            response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 递归将生产过程正向追溯DTO转为DTOList用于导出
     *
     * @param forwardTraceResultDTO
     * @return : java.util.List<net.airuima.web.rest.report.dto.ForwardTraceResultDTO>
     * <AUTHOR>
     * @date 2023/6/7
     **/
    public List<ForwardTraceResultDTO> forwardTraceResultDTOToList(ForwardTraceResultDTO forwardTraceResultDTO) {
        List<ForwardTraceResultDTO> forwardTraceResultDTOList = new ArrayList<>();

        ForwardTraceResultDTO forwardTraceResultDTONow = new ForwardTraceResultDTO();
        BeanUtils.copyProperties(forwardTraceResultDTO, forwardTraceResultDTONow, "subForwardTraceResultDTOList");
        forwardTraceResultDTOList.add(forwardTraceResultDTONow);

        if (!CollectionUtils.isEmpty(forwardTraceResultDTO.getSubForwardTraceResultDTOList())) {
            for (ForwardTraceResultDTO subForwardTraceResultDTO : forwardTraceResultDTO.getSubForwardTraceResultDTOList()) {
                List<ForwardTraceResultDTO> forwardTraceResultDTOListReturn = forwardTraceResultDTOToList(subForwardTraceResultDTO);
                forwardTraceResultDTOList.addAll(forwardTraceResultDTOListReturn);
            }
        }

        return forwardTraceResultDTOList;
    }

    /**
     * 生成过程正向追溯（工序详情）-导出
     *
     * @param wsId
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     * @return : java.util.List<net.airuima.web.rest.report.dto.ForwardTraceStepDetailExportDTO>
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @Override
    public void exportStepDetail(Long wsId, int category, String serialNumber,String excelType, HttpServletResponse response) {
        try{
            //获取系统配置的投产粒度(子工单或者工单)
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

            List<ForwardTraceStepDetailExportDTO> forwardTraceStepDetailExportDTOList = new ArrayList<>();
            //批量
            List<BatchForwardTraceStepDetailExportDTO> batchDataList = subWsProductionMode ?
                    batchWorkDetailRepository.findByForwardTraceReportExportBatchAndSubWorkSheet(wsId) :
                    batchWorkDetailRepository.findByForwardTraceReportExportBatchAndWorkSheet(wsId);
            if (ValidateUtils.isValid(batchDataList)) {

                //容器
                Map<String, List<ContainerForwardTraceStepDetailExportDTO>> containerMap = new HashMap<>();
                List<ContainerForwardTraceStepDetailExportDTO> containerDataList = subWsProductionMode ?
                        batchWorkDetailRepository.findByForwardTraceReportExportContainerAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null) :
                        batchWorkDetailRepository.findByForwardTraceReportExportContainerAndWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null);

                //容器sn
                List<SnForwardTraceStepDetailExportDTO> containerSnDataList;
                Map<String, List<SnForwardTraceStepDetailExportDTO>> containerSnMap = new HashMap<>();
                if (ValidateUtils.isValid(containerDataList)) {
                    containerMap = containerDataList.stream().collect(Collectors.groupingBy(ContainerForwardTraceStepDetailExportDTO::getStepCode));

                    containerSnDataList = subWsProductionMode ?
                            batchWorkDetailRepository.findByForwardTraceReportExportContainerSnAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null) :
                            batchWorkDetailRepository.findByForwardTraceReportExportContainerSnAndWorkSheet(wsId, Objects.equals(category, Constants.INT_THREE) ? serialNumber : null, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null);
                    if (ValidateUtils.isValid(containerSnDataList)) {
                        containerSnMap = containerSnDataList.stream().collect(Collectors.groupingBy(SnForwardTraceStepDetailExportDTO::getStepCode));
                    }
                } else {
                    containerSnDataList = Lists.newArrayList();
                }
                //sn
                List<SnForwardTraceStepDetailExportDTO> snDataList = subWsProductionMode ?
                        batchWorkDetailRepository.findByForwardTraceReportExportSnAndSubWorkSheet(wsId, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null) :
                        batchWorkDetailRepository.findByForwardTraceReportExportSnAndWorkSheet(wsId, Objects.equals(category, Constants.INT_FOUR) ? serialNumber : null);
                Map<String, List<SnForwardTraceStepDetailExportDTO>> snMap = new HashMap<>();
                if (ValidateUtils.isValid(snDataList)) {
                    snMap = snDataList.stream().collect(Collectors.groupingBy(SnForwardTraceStepDetailExportDTO::getStepCode));
                }

                Map<String, List<ContainerForwardTraceStepDetailExportDTO>> finalContainerMap = containerMap;
                Map<String, List<SnForwardTraceStepDetailExportDTO>> finalSnMap = snMap;

                Map<String, List<SnForwardTraceStepDetailExportDTO>> finalContainerSnMap = containerSnMap;
                batchDataList.stream().collect(Collectors.groupingBy(BatchForwardTraceStepDetailExportDTO::getStepCode))
                        .forEach((stepCode, batchList) -> {
                            List<ForwardTraceStepDetailExportDTO> exportList = null;

                            //容器不存在 -》 只存在两种 批量 或者  单支
                            if (!ValidateUtils.isValid(containerDataList) || (ValidateUtils.isValid(containerDataList) && !ValidateUtils.isValid(finalContainerMap.get(stepCode)))) {
                                //容器不存在，那么直接跳过本道工序
                                if (Objects.equals(category, Constants.INT_THREE)) {
                                    return;
                                }
                                //批量
                                if (!ValidateUtils.isValid(snDataList) || (ValidateUtils.isValid(snDataList) && !ValidateUtils.isValid(finalSnMap.get(stepCode)))) {
                                    if (Objects.equals(category, Constants.INT_FOUR)) {
                                        return;
                                    }
                                    exportList = exportBatchInfo(batchList);
                                }
                                //单支
                                else {
                                    exportList = exportSn(finalSnMap.get(stepCode));
                                }
                            } else {
                                //容器
                                if (!ValidateUtils.isValid(containerSnDataList) || (ValidateUtils.isValid(containerSnDataList) && !ValidateUtils.isValid(finalContainerSnMap.get(stepCode)))) {
                                    exportList = exportContainer(finalContainerMap.get(stepCode));
                                }
                                //容器单支
                                else {
                                    exportList = exportContainerSn(finalContainerSnMap.get(stepCode));
                                }

                            }
                            if (ValidateUtils.isValid(exportList)) {
                                forwardTraceStepDetailExportDTOList.addAll(exportList);
                            }
                        });
                if (ValidateUtils.isValid(forwardTraceStepDetailExportDTOList)) {
                    forwardTraceStepDetailExportDTOList.stream()
                            .sorted(Comparator.comparing(ForwardTraceStepDetailExportDTO::getStartDate))
                            .toList();
                }
            }
            String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(excelType) && excelType.equals("xls")?".xls":".xlsx";
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setFreezeCol(net.airuima.constant.Constants.INT_TWO);
            if(StringUtils.isNotBlank(excelType) && excelType.equals("xls")){
                exportParams.setType(ExcelType.HSSF);
            }
            String fileName = serialNumber + "正向追溯工序详情" + System.currentTimeMillis() +prefix;
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ForwardTraceStepDetailExportDTO.class, forwardTraceStepDetailExportDTOList);
            this.mergeCellsData(workbook);
            response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 指定当前sheet页，列合并默认参数
     *
     * @param workbook
     * @return
     */
    public Workbook mergeCellsData(Workbook workbook) {
        Map<Integer, int[]> mergeMap = new HashMap<>();
        int arr[] = {0, 1, 2, 3, 4, 5, 6, 7, 8};
        mergeMap.put(0, arr);
        mergeMap.put(1, arr);
        mergeMap.put(2, arr);
        mergeMap.put(3, arr);
        mergeMap.put(4, arr);
        mergeMap.put(5, arr);
        mergeMap.put(6, arr);
        mergeMap.put(7, arr);
        mergeMap.put(8, arr);
        PoiMergeCellUtil.mergeCells(workbook.getSheetAt(Constants.INT_ZERO), mergeMap, Constants.INT_ONE);
        return workbook;
    }

    /**
     * 生成过程正向追溯（工序详情）-浏览-批量
     *
     * @param resultList 工序详情查询数据
     * @return List<ForwardTraceStepDetailDTO.DateUnit>
     */
    public List<ForwardTraceStepDetailDTO.DateUnit> readBatchInfo(List<BatchForwardTraceStepDetailExportDTO> resultList) {

        if (!CollectionUtils.isEmpty(resultList)) {

            ForwardTraceStepDetailDTO.DateUnit dateUnit = new ForwardTraceStepDetailDTO.DateUnit().setCategory(Constants.INT_ZERO);

            ForwardTraceStepDetailDTO.Properties properties = new ForwardTraceStepDetailDTO.Properties();

            BatchForwardTraceStepDetailExportDTO result = resultList.get(Constants.INT_ZERO);

            //员工
            ForwardTraceStepDetailDTO.StaffDetailDTO staffDetailDto = new ForwardTraceStepDetailDTO.StaffDetailDTO();
            staffDetailDto.setCode(result.getStaffDto().getCode()).setName(result.getStaffDto().getName());
            properties.setStaffDTO(staffDetailDto);

            //生产工位及设备信息 - 批量
            ForwardTraceStepDetailDTO.MachineDTO machineDTO = new ForwardTraceStepDetailDTO.MachineDTO();
            machineDTO.setWorkCellCode(result.getWorkCellCode()).setWorkCellName(result.getWorkCellName());
            List<ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO> facilityDetailDTOList = Lists.newArrayList();
            resultList.stream().filter(entity -> Objects.nonNull(entity.getFacilityId()))
                    .collect(Collectors.groupingBy(entity -> (entity.getStepCode() + entity.getFacilityId()))).forEach((uniqueContainerId, entityList) -> {
                        ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO facilityDetailDto = new ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO();
                        facilityDetailDto.setCode(entityList.get(Constants.INT_ZERO).getFacilityDto().getCode()).setName(entityList.get(Constants.INT_ZERO).getFacilityDto().getName());
                        facilityDetailDTOList.add(facilityDetailDto);
                    });
            machineDTO.setFacilityDTOList(facilityDetailDTOList.stream().distinct().toList());
            properties.setMachineDTO(machineDTO);

            //生产物料信息列表 批量 工序+ 物料id+批次号
            List<ForwardTraceStepDetailDTO.MaterialDTO> materialDtoList = Lists.newArrayList();
            resultList.stream().filter(entity -> Objects.nonNull(entity.getMaterialId())).collect(Collectors.groupingBy(e -> (e.getStepCode() + e.getMaterialId() + (Objects.isNull(e.getMaterialBatch()) ? Constants.HYPHEN : e.getMaterialBatch()))))
                    .forEach((uniqueMaterial, list) -> {
                        BatchForwardTraceStepDetailExportDTO entity = list.get(Constants.INT_ZERO);
                        ForwardTraceStepDetailDTO.MaterialDTO materialDto = new ForwardTraceStepDetailDTO.MaterialDTO();
                        materialDto.setBatch(entity.getMaterialBatch()).setCode(entity.getMaterialDto().getCode()).setName(entity.getMaterialDto().getName())
                                .setNumber(BigDecimal.valueOf(entity.getMaterialNumber()));
                        materialDtoList.add(materialDto);
                    });
            properties.setMaterialDTO(materialDtoList.stream().distinct().toList());

            //易损件
            List<ForwardTraceStepDetailDTO.WearingPartDTO> wearingPartDtoList = Lists.newArrayList();
            resultList.stream().filter(entity -> Objects.nonNull(entity.getWearingPartCode())).collect(Collectors.groupingBy(entity -> (entity.getStepCode() + entity.getWearingPartCode())))
                    .forEach((uniqueWearingPart, list) -> {
                        BatchForwardTraceStepDetailExportDTO entity = list.get(Constants.INT_ZERO);
                        ForwardTraceStepDetailDTO.WearingPartDTO wearingPartDto = new ForwardTraceStepDetailDTO.WearingPartDTO();
                        wearingPartDto.setCode(entity.getWearingPartCode()).setName(entity.getWearingPartName()).setUseNumber(Constants.INT_ZERO).setUseTime(Constants.INT_ZERO);
                        wearingPartDtoList.add(wearingPartDto);
                    });
            properties.setWearingPartDTO(wearingPartDtoList.stream().distinct().toList());

            //不良详情
            ForwardTraceStepDetailDTO.QualifiedDTO qualifiedDto = new ForwardTraceStepDetailDTO.QualifiedDTO().setInputNumber(result.getStepInputNumber()).setQualifiedNumber(result.getStepQualifiedNumber()).setUnqualifiedNumber(result.getStepUnqualifiedNumber());
            List<ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail> unqualifiedDetails = new ArrayList<>();
            resultList.stream().filter(entity -> Objects.nonNull(entity.getUnqualifiedItemCode())).collect(Collectors.groupingBy(entity -> (entity.getStepCode() + entity.getUnqualifiedItemCode())))
                    .forEach((uniqueUnqualifiedItem, list) -> {
                        BatchForwardTraceStepDetailExportDTO entity = list.get(Constants.INT_ZERO);
                        ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail unqualifiedDetail = new ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail();
                        unqualifiedDetail.setNumber(entity.getUnqualifiedItemNumber()).setUnqualifiedItemCode(entity.getUnqualifiedItemCode()).setUnqualifiedItemName(entity.getUnqualifiedItemName());
                        unqualifiedDetails.add(unqualifiedDetail);
                    });
            qualifiedDto.setUnqualifiedDetails(unqualifiedDetails.stream().distinct().toList());
            properties.setQualifiedDTO(qualifiedDto);

            dateUnit.setProperties(properties);

            return Collections.singletonList(dateUnit);
        }
        return null;
    }

    /**
     * 生成过程正向追溯（工序详情）-导出-批量
     *
     * @param resultList 工序详情查询数据
     * @return List<ForwardTraceStepDetailExportDTO>
     */
    public List<ForwardTraceStepDetailExportDTO> exportBatchInfo(List<BatchForwardTraceStepDetailExportDTO> resultList) {
        return !CollectionUtils.isEmpty(resultList) ? resultList.stream().map(result -> MapperUtils.map(result, ForwardTraceStepDetailExportDTO.class)
                .setStaffCode(result.getStaffDto().getCode()).setStaffName(result.getStaffDto().getName())
                .setMaterialName(result.getMaterialDto().getName()).setMaterialCode(result.getMaterialDto().getCode())
                .setFacilityCode(result.getFacilityDto().getCode()).setFacilityName(result.getFacilityDto().getName())
        ).collect(Collectors.toList()) : new ArrayList<>();

    }

    /**
     * 生成过程正向追溯（容器详情）-浏览-容器批量
     *
     * @param resultList 容器详情查询数据
     * @return List<ForwardTraceStepDetailDTO.DateUnit>
     */
    public List<ForwardTraceStepDetailDTO.DateUnit> readContainer(List<ContainerForwardTraceStepDetailExportDTO> resultList) {
        List<ForwardTraceStepDetailDTO.DateUnit> dateUnitList = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(resultList)) {

            resultList.stream().filter(entity -> Objects.nonNull(entity.getStepCode()) && Objects.nonNull(entity.getContainerCode()))
                    .collect(Collectors.groupingBy(entity -> (entity.getContainerCode() + entity.getStepCode())))
                    .forEach((uniqueContainer, list) -> {

                        ForwardTraceStepDetailDTO.DateUnit dateUnit = new ForwardTraceStepDetailDTO.DateUnit().setCategory(Constants.INT_ONE);
                        ContainerForwardTraceStepDetailExportDTO result = list.get(Constants.INT_ZERO);
                        dateUnit.setCode(result.getContainerCode()).setName(result.getContainerCode());

                        ForwardTraceStepDetailDTO.Properties properties = new ForwardTraceStepDetailDTO.Properties();

                        //员工
                        ForwardTraceStepDetailDTO.StaffDetailDTO staffDetailDto = new ForwardTraceStepDetailDTO.StaffDetailDTO();
                        staffDetailDto.setCode(result.getStaffDto().getCode()).setName(result.getStaffDto().getName());
                        properties.setStaffDTO(staffDetailDto);

                        //生产工位及设备信息 - 批量
                        ForwardTraceStepDetailDTO.MachineDTO machineDTO = new ForwardTraceStepDetailDTO.MachineDTO();
                        machineDTO.setWorkCellCode(result.getWorkCellCode()).setWorkCellName(result.getWorkCellName());
                        List<ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO> facilityDetailDTOList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getFacilityId()))
                                .collect(Collectors.groupingBy(entity -> (entity.getContainerCode() + entity.getFacilityId()))).forEach((uniqueContainerId, entityList) -> {
                                    ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO facilityDetailDto = new ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO();
                                    facilityDetailDto.setCode(entityList.get(Constants.INT_ZERO).getFacilityDto().getCode()).setName(entityList.get(Constants.INT_ZERO).getFacilityDto().getName());
                                    facilityDetailDTOList.add(facilityDetailDto);
                                });
                        machineDTO.setFacilityDTOList(facilityDetailDTOList.stream().distinct().toList());
                        properties.setMachineDTO(machineDTO);

                        //生产物料信息列表 批量 工序+ 物料id+批次号
                        List<ForwardTraceStepDetailDTO.MaterialDTO> materialDtoList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getMaterialId())).collect(Collectors.groupingBy(e -> (e.getContainerCode() + e.getMaterialId() + (Objects.isNull(e.getMaterialBatch()) ? Constants.HYPHEN : e.getMaterialBatch()))))
                                .forEach((uniqueMaterial, entityList) -> {
                                    ContainerForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.MaterialDTO materialDto = new ForwardTraceStepDetailDTO.MaterialDTO();
                                    materialDto.setBatch(entity.getMaterialBatch()).setCode(entity.getMaterialDto().getCode()).setName(entity.getMaterialDto().getName())
                                            .setNumber(BigDecimal.valueOf(entity.getMaterialNumber()));
                                    materialDtoList.add(materialDto);
                                });
                        properties.setMaterialDTO(materialDtoList.stream().distinct().toList());

                        //易损件
                        List<ForwardTraceStepDetailDTO.WearingPartDTO> wearingPartDtoList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getWearingPartCode())).collect(Collectors.groupingBy(entity -> (entity.getContainerCode() + entity.getWearingPartCode())))
                                .forEach((uniqueWearingPart, entityList) -> {
                                    ContainerForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.WearingPartDTO wearingPartDto = new ForwardTraceStepDetailDTO.WearingPartDTO();
                                    wearingPartDto.setCode(entity.getWearingPartCode()).setName(entity.getWearingPartName()).setUseNumber(Constants.INT_ZERO).setUseTime(Constants.INT_ZERO);
                                    wearingPartDtoList.add(wearingPartDto);
                                });
                        properties.setWearingPartDTO(wearingPartDtoList.stream().distinct().toList());

                        //不良详情
                        ForwardTraceStepDetailDTO.QualifiedDTO qualifiedDto = new ForwardTraceStepDetailDTO.QualifiedDTO().setInputNumber(result.getInputNumber()).setQualifiedNumber(result.getQualifiedNumber()).setUnqualifiedNumber(result.getInputNumber() - result.getQualifiedNumber());
                        List<ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail> unqualifiedDetails = new ArrayList<>();
                        list.stream().filter(entity -> Objects.nonNull(entity.getUnqualifiedItemCode())).collect(Collectors.groupingBy(entity -> (entity.getContainerCode() + entity.getUnqualifiedItemCode())))
                                .forEach((uniqueUnqualifiedItem, entityList) -> {
                                    ContainerForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail unqualifiedDetail = new ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail();
                                    unqualifiedDetail.setNumber(entity.getUnqualifiedItemNumber()).setUnqualifiedItemCode(entity.getUnqualifiedItemCode()).setUnqualifiedItemName(entity.getUnqualifiedItemName());
                                    unqualifiedDetails.add(unqualifiedDetail);
                                });
                        qualifiedDto.setUnqualifiedDetails(unqualifiedDetails.stream().distinct().toList());
                        properties.setQualifiedDTO(qualifiedDto);
                        dateUnit.setProperties(properties);
                        dateUnitList.add(dateUnit);
                    });
        }
        return dateUnitList;
    }

    /**
     * 生成过程正向追溯（容器详情）-导出-容器批量
     *
     * @param resultList 容器详情查询数据
     * @return List<ForwardTraceStepDetailExportDTO>
     */
    public List<ForwardTraceStepDetailExportDTO> exportContainer(List<ContainerForwardTraceStepDetailExportDTO> resultList) {
        return !CollectionUtils.isEmpty(resultList) ? resultList.stream().map(result -> MapperUtils.map(result, ForwardTraceStepDetailExportDTO.class)
                .setStaffCode(result.getStaffDto().getCode()).setStaffName(result.getStaffDto().getName())
                .setMaterialName(result.getMaterialDto().getName()).setMaterialCode(result.getMaterialDto().getCode())
                .setFacilityCode(result.getFacilityDto().getCode()).setFacilityName(result.getFacilityDto().getName())
        ).collect(Collectors.toList()) : new ArrayList<>();
    }

    /**
     * 生成过程正向追溯（容器sn详情）-浏览-容器sn
     *
     * @param resultList 容器详情查询数据
     * @return List<ForwardTraceStepDetailDTO.DateUnit>
     */
    public List<ForwardTraceStepDetailDTO.DateUnit> readContainerSn(List<SnForwardTraceStepDetailExportDTO> resultList) {
        List<ForwardTraceStepDetailDTO.DateUnit> dateUnitList = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(resultList)) {

            resultList.stream().filter(entity -> Objects.nonNull(entity.getStepCode()) && Objects.nonNull(entity.getContainerCode()) && Objects.nonNull(entity.getSn()))
                    .collect(Collectors.groupingBy(entity -> (entity.getContainerCode() + entity.getStepCode()))) //容器+工序
                    .forEach((uniqueSnContainer, list) -> {

                        ForwardTraceStepDetailDTO.DateUnit dateUnit = new ForwardTraceStepDetailDTO.DateUnit().setCategory(Constants.INT_THREE)
                                .setName(list.get(Constants.INT_ZERO).getContainerCode()).setCode(list.get(Constants.INT_ZERO).getContainerCode());
                        Set<ForwardTraceStepDetailDTO.SnProperties> allSnProperties = new HashSet<>();
                        //sn分组
                        list.stream().collect(Collectors.groupingBy(SnForwardTraceStepDetailExportDTO::getSn))
                                .forEach((sn, snList) -> {
                                    ForwardTraceStepDetailDTO.SnProperties snproperties = new ForwardTraceStepDetailDTO.SnProperties();
                                    snproperties.setSn(sn);
                                    Set<ForwardTraceStepDetailDTO.SnProperties> snProperties = snList.stream().map(snEntity -> {
                                        ForwardTraceStepDetailDTO.Properties properties = new ForwardTraceStepDetailDTO.Properties();
                                        //员工
                                        ForwardTraceStepDetailDTO.StaffDetailDTO staffDetailDto = new ForwardTraceStepDetailDTO.StaffDetailDTO();
                                        staffDetailDto.setCode(snEntity.getStaffDto().getCode()).setName(snEntity.getStaffDto().getName());
                                        properties.setStaffDTO(staffDetailDto);

                                        //生产工位及设备信息 - sn+设备
                                        ForwardTraceStepDetailDTO.MachineDTO machineDTO = new ForwardTraceStepDetailDTO.MachineDTO();
                                        machineDTO.setWorkCellCode(snEntity.getWorkCellCode()).setWorkCellName(snEntity.getWorkCellName());
                                        List<ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO> facilityDetailDTOList = Lists.newArrayList();
                                        snList.stream().filter(entity -> Objects.nonNull(entity.getFacilityId()))
                                                .collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getFacilityId())))
                                                .forEach((uniqueSnContainerId, entityList) -> {
                                                    ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO facilityDetailDto = new ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO();
                                                    facilityDetailDto.setCode(entityList.get(Constants.INT_ZERO).getFacilityDto().getCode()).setName(entityList.get(Constants.INT_ZERO).getFacilityDto().getName());
                                                    facilityDetailDTOList.add(facilityDetailDto);
                                                });
                                        machineDTO.setFacilityDTOList(facilityDetailDTOList.stream().distinct().toList());
                                        properties.setMachineDTO(machineDTO);

                                        //生产物料信息列表 批量 工序+ 物料id+批次号
                                        List<ForwardTraceStepDetailDTO.MaterialDTO> materialDtoList = Lists.newArrayList();
                                        snList.stream().filter(entity -> Objects.nonNull(entity.getMaterialId())).collect(Collectors.groupingBy(e -> (e.getSn() + e.getMaterialId() + (Objects.isNull(e.getMaterialBatch()) ? Constants.HYPHEN : e.getMaterialBatch()))))
                                                .forEach((uniqueSnContainerMaterial, entityList) -> {
                                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                                    ForwardTraceStepDetailDTO.MaterialDTO materialDto = new ForwardTraceStepDetailDTO.MaterialDTO();
                                                    materialDto.setBatch(entity.getMaterialBatch()).setCode(entity.getMaterialDto().getCode()).setName(entity.getMaterialDto().getName())
                                                            .setNumber(BigDecimal.valueOf(entity.getMaterialNumber()));
                                                    materialDtoList.add(materialDto);
                                                });
                                        properties.setMaterialDTO(materialDtoList.stream().distinct().toList());

                                        //易损件
                                        List<ForwardTraceStepDetailDTO.WearingPartDTO> wearingPartDtoList = Lists.newArrayList();
                                        snList.stream().filter(entity -> Objects.nonNull(entity.getWearingPartCode())).collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getWearingPartCode())))
                                                .forEach((uniqueWearingPart, entityList) -> {
                                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                                    ForwardTraceStepDetailDTO.WearingPartDTO wearingPartDto = new ForwardTraceStepDetailDTO.WearingPartDTO();
                                                    wearingPartDto.setCode(entity.getWearingPartCode()).setName(entity.getWearingPartName()).setUseNumber(Constants.INT_ZERO).setUseTime(Constants.INT_ZERO);
                                                    wearingPartDtoList.add(wearingPartDto);
                                                });
                                        properties.setWearingPartDTO(wearingPartDtoList.stream().distinct().toList());

                                        //不良详情
                                        ForwardTraceStepDetailDTO.QualifiedDTO qualifiedDto = new ForwardTraceStepDetailDTO.QualifiedDTO();
                                        List<ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail> unqualifiedDetails = new ArrayList<>();
                                        snList.stream().filter(entity -> Objects.nonNull(entity.getUnqualifiedItemCode())).collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getUnqualifiedItemCode())))
                                                .forEach((uniqueUnqualifiedItem, entityList) -> {
                                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                                    ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail unqualifiedDetail = new ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail();
                                                    unqualifiedDetail.setNumber(entity.getUnqualifiedItemNumber()).setUnqualifiedItemCode(entity.getUnqualifiedItemCode()).setUnqualifiedItemName(entity.getUnqualifiedItemName());
                                                    unqualifiedDetails.add(unqualifiedDetail);
                                                });
                                        qualifiedDto.setUnqualifiedDetails(unqualifiedDetails.stream().distinct().toList());
                                        qualifiedDto.setInputNumber(Constants.INT_ONE).setQualifiedNumber(CollectionUtils.isEmpty(unqualifiedDetails)?Constants.INT_ONE:Constants.INT_ZERO).setUnqualifiedNumber(!CollectionUtils.isEmpty(unqualifiedDetails)?Constants.INT_ONE:Constants.INT_ZERO);
                                        properties.setQualifiedDTO(qualifiedDto);

                                        return snproperties.setProperties(properties);
                                    }).collect(Collectors.toSet());
                                    allSnProperties.addAll(snProperties);
                                });
                        dateUnit.setSnProperties(allSnProperties.stream().toList());
                        dateUnitList.add(dateUnit);
                    });

        }
        return dateUnitList;
    }

    /**
     * 生成过程正向追溯（容器sn详情）-导出-容器sn
     *
     * @param resultList 容器详情查询数据
     * @return List<ForwardTraceStepDetailExportDTO>
     */
    public List<ForwardTraceStepDetailExportDTO> exportContainerSn(List<SnForwardTraceStepDetailExportDTO> resultList) {
        return !CollectionUtils.isEmpty(resultList) ? resultList.stream().map(result -> MapperUtils.map(result, ForwardTraceStepDetailExportDTO.class)
                .setStaffCode(result.getStaffDto().getCode()).setStaffName(result.getStaffDto().getName())
                .setMaterialName(result.getMaterialDto().getName()).setMaterialCode(result.getMaterialDto().getCode())
                .setFacilityCode(result.getFacilityDto().getCode()).setFacilityName(result.getFacilityDto().getName())
        ).collect(Collectors.toList()) : new ArrayList<>();
    }

    /**
     * 生成过程正向追溯（sn详情）-浏览-sn
     *
     * @param resultList sn详情查询数据
     * @return List<ForwardTraceStepDetailDTO.DateUnit>
     */
    public List<ForwardTraceStepDetailDTO.DateUnit> readSn(List<SnForwardTraceStepDetailExportDTO> resultList) {
        List<ForwardTraceStepDetailDTO.DateUnit> dateUnitList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(resultList)) {

            resultList.stream().filter(entity -> Objects.nonNull(entity.getStepCode()) && Objects.nonNull(entity.getSn()))
                    .collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getStepCode()))) //容器+工序
                    .forEach((uniqueSn, list) -> {

                        SnForwardTraceStepDetailExportDTO snEntity = list.get(Constants.INT_ZERO);

                        ForwardTraceStepDetailDTO.DateUnit dateUnit = new ForwardTraceStepDetailDTO.DateUnit().setCategory(Constants.INT_FOUR)
                                .setSn(snEntity.getSn());

                        ForwardTraceStepDetailDTO.Properties properties = new ForwardTraceStepDetailDTO.Properties();
                        //员工
                        ForwardTraceStepDetailDTO.StaffDetailDTO staffDetailDto = new ForwardTraceStepDetailDTO.StaffDetailDTO();
                        staffDetailDto.setCode(snEntity.getStaffDto().getCode()).setName(snEntity.getStaffDto().getName());
                        properties.setStaffDTO(staffDetailDto);

                        //生产工位及设备信息 - sn+设备
                        ForwardTraceStepDetailDTO.MachineDTO machineDTO = new ForwardTraceStepDetailDTO.MachineDTO();
                        machineDTO.setWorkCellCode(snEntity.getWorkCellCode()).setWorkCellName(snEntity.getWorkCellName());
                        List<ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO> facilityDetailDTOList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getFacilityId()))
                                .collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getFacilityId())))
                                .forEach((uniqueSnContainerId, entityList) -> {
                                    ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO facilityDetailDto = new ForwardTraceStepDetailDTO.MachineDTO.FacilityDetailDTO();
                                    facilityDetailDto.setCode(entityList.get(Constants.INT_ZERO).getFacilityDto().getCode()).setName(entityList.get(Constants.INT_ZERO).getFacilityDto().getName());
                                    facilityDetailDTOList.add(facilityDetailDto);
                                });
                        machineDTO.setFacilityDTOList(facilityDetailDTOList.stream().distinct().toList());
                        properties.setMachineDTO(machineDTO);

                        //生产物料信息列表 批量 工序+ 物料id+批次号
                        List<ForwardTraceStepDetailDTO.MaterialDTO> materialDtoList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getMaterialId())).collect(Collectors.groupingBy(e -> (e.getSn() + e.getMaterialId() + (Objects.isNull(e.getMaterialBatch()) ? Constants.HYPHEN : e.getMaterialBatch()))))
                                .forEach((uniqueSnContainerMaterial, entityList) -> {
                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.MaterialDTO materialDto = new ForwardTraceStepDetailDTO.MaterialDTO();
                                    materialDto.setBatch(entity.getMaterialBatch()).setCode(entity.getMaterialDto().getCode()).setName(entity.getMaterialDto().getName())
                                            .setNumber(BigDecimal.valueOf(entity.getMaterialNumber()));
                                    materialDtoList.add(materialDto);
                                });
                        properties.setMaterialDTO(materialDtoList.stream().distinct().toList());

                        //易损件
                        List<ForwardTraceStepDetailDTO.WearingPartDTO> wearingPartDtoList = Lists.newArrayList();
                        list.stream().filter(entity -> Objects.nonNull(entity.getWearingPartCode())).collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getWearingPartCode())))
                                .forEach((uniqueWearingPart, entityList) -> {
                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.WearingPartDTO wearingPartDto = new ForwardTraceStepDetailDTO.WearingPartDTO();
                                    wearingPartDto.setCode(entity.getWearingPartCode()).setName(entity.getWearingPartName()).setUseNumber(Constants.INT_ZERO).setUseTime(Constants.INT_ZERO);
                                    wearingPartDtoList.add(wearingPartDto);
                                });
                        properties.setWearingPartDTO(wearingPartDtoList.stream().distinct().toList());

                        //不良详情
                        ForwardTraceStepDetailDTO.QualifiedDTO qualifiedDto = new ForwardTraceStepDetailDTO.QualifiedDTO().setInputNumber(snEntity.getInputNumber()).setQualifiedNumber(snEntity.getQualifiedNumber()).setUnqualifiedNumber(snEntity.getInputNumber() - snEntity.getQualifiedNumber());
                        List<ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail> unqualifiedDetails = new ArrayList<>();
                        list.stream().filter(entity -> Objects.nonNull(entity.getUnqualifiedItemCode())).collect(Collectors.groupingBy(entity -> (entity.getSn() + entity.getUnqualifiedItemCode())))
                                .forEach((uniqueUnqualifiedItem, entityList) -> {
                                    SnForwardTraceStepDetailExportDTO entity = entityList.get(Constants.INT_ZERO);
                                    ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail unqualifiedDetail = new ForwardTraceStepDetailDTO.QualifiedDTO.UnqualifiedDetail();
                                    unqualifiedDetail.setNumber(entity.getUnqualifiedItemNumber()).setUnqualifiedItemCode(entity.getUnqualifiedItemCode()).setUnqualifiedItemName(entity.getUnqualifiedItemName());
                                    unqualifiedDetails.add(unqualifiedDetail);
                                });
                        qualifiedDto.setUnqualifiedDetails(unqualifiedDetails.stream().distinct().toList());
                        properties.setQualifiedDTO(qualifiedDto);
                        dateUnit.setProperties(properties);
                        dateUnitList.add(dateUnit);
                    });
        }
        return dateUnitList;
    }

    /**
     * 生成过程正向追溯（sn详情）-导出-sn
     *
     * @param resultList sn详情查询数据
     * @return List<ForwardTraceStepDetailExportDTO>
     */
    public List<ForwardTraceStepDetailExportDTO> exportSn(List<SnForwardTraceStepDetailExportDTO> resultList) {
        return !CollectionUtils.isEmpty(resultList) ? resultList.stream().map(result -> MapperUtils.map(result, ForwardTraceStepDetailExportDTO.class)
                .setStaffCode(result.getStaffDto().getCode()).setStaffName(result.getStaffDto().getName())
                .setMaterialName(result.getMaterialDto().getName()).setMaterialCode(result.getMaterialDto().getCode())
                .setFacilityCode(result.getFacilityDto().getCode()).setFacilityName(result.getFacilityDto().getName())
        ).collect(Collectors.toList()) : new ArrayList<>();
    }


    /**
     * 反向追溯详情 物料-设备-sn 导出
     *
     * @param reverseTraceReportRequestDto 反向追溯请求详情
     */
    @Override
    public void exportReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto,HttpServletResponse response) {
        try {
            String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(reverseTraceReportRequestDto.getExcelType()) && reverseTraceReportRequestDto.getExcelType().equals("xls")?".xls":".xlsx";
            ReverseTraceReportResultDTO reverseTraceReport = this.getReverseTraceReport(reverseTraceReportRequestDto);

            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);

            HashMap<Workbook, String> map = new HashMap<>();
            if (reverseTraceReportRequestDto.getCategory() == 0) {
                List<ReverseTraceReportResultDTO.ReverseTraceMaterial> reverseTraceMaterials = reverseTraceReport.getReverseTraceMaterials();
                if (!ValidateUtils.isValid(reverseTraceMaterials)) {
                    throw new ResponseException("error.ReverseTraceMaterialNotFound", "查询信息为空");
                }
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ReverseTraceReportResultDTO.ReverseTraceMaterial.class, reverseTraceMaterials);
                map.put(workbook, "物料批次反向追溯");
            }else if (reverseTraceReportRequestDto.getCategory() == 1) {
                List<ReverseTraceReportResultDTO.ReverseTraceFacility> reverseTraceFacilities = reverseTraceReport.getReverseTraceFacilities();
                if (!ValidateUtils.isValid(reverseTraceFacilities)) {
                    throw new ResponseException("error.ReverseTraceMaterialNotFound", "查询信息为空");
                }
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ReverseTraceReportResultDTO.ReverseTraceFacility.class, reverseTraceFacilities);
                map.put(workbook, "设备反向追溯");
            } else if (reverseTraceReportRequestDto.getCategory() == 2) {
                List<ReverseTraceReportResultDTO.ReverseTraceWearingPart> reverseTraceWearingParts = reverseTraceReport.getReverseTraceWearingParts();
                if (!ValidateUtils.isValid(reverseTraceWearingParts)) {
                    throw new ResponseException("error.ReverseTraceMaterialNotFound", "查询信息为空");
                }
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ReverseTraceReportResultDTO.ReverseTraceWearingPart.class, reverseTraceWearingParts);
                map.put(workbook, "易损件反向追溯");
            }else {
                throw new ResponseException("error.categoryError", "追溯类型参数异常");
            }
            exportParams.setType(ExcelType.XSSF);
            if(StringUtils.isNotBlank(reverseTraceReportRequestDto.getExcelType()) && reverseTraceReportRequestDto.getExcelType().equals("xls")){
                exportParams.setType(ExcelType.HSSF);
            }
            Workbook workbook = map.keySet().stream().findFirst().get();
            response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + URLEncoder.encode( map.get(workbook)+prefix, StandardCharsets.UTF_8));
            response.setCharacterEncoding("utf-8");
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            e.printStackTrace();
        }

    }


    /**
     * 反向追溯详情 物料-设备-sn
     *
     * @param reverseTraceReportRequestDto 反向追溯请求详情
     * @return
     */
    @FetchMethod
    public ReverseTraceReportResultDTO getReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto) {

        //请求参数验证
        validReverseTraceReportData(reverseTraceReportRequestDto);

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);

        //物料批次反向追溯
        if (reverseTraceReportRequestDto.getCategory() == 0) {
            return getMaterialReverseTraceReport(reverseTraceReportRequestDto, subWsProductionMode);
        }
        //设备反向追溯
        if (reverseTraceReportRequestDto.getCategory() == 1) {
            return getFacilityReverseTraceReport(reverseTraceReportRequestDto, subWsProductionMode);
        }
        //易损件反向追溯
        if (reverseTraceReportRequestDto.getCategory() == 2) {
            return getWearingPartReverseTraceReport(reverseTraceReportRequestDto, subWsProductionMode);
        }
        throw new ResponseException("error.categoryError", "追溯类型参数异常");
    }

    /**
     * 验证反向追溯请求参数合法性
     *
     * @param reverseTraceReportRequestDto 请求参数
     */
    public void validReverseTraceReportData(ReverseTraceReportRequestDTO reverseTraceReportRequestDto) {
        if (ObjectUtils.isEmpty(reverseTraceReportRequestDto.getCategory())) {
            throw new ResponseException("error.RequiredCategory", "请选择需要追溯类型");
        }
        if ((reverseTraceReportRequestDto.getCategory() == 0 || reverseTraceReportRequestDto.getCategory() == 1 || reverseTraceReportRequestDto.getCategory() == 2)
                && (ObjectUtils.isEmpty(reverseTraceReportRequestDto.getStartTime()) || ObjectUtils.isEmpty(reverseTraceReportRequestDto.getEndTime()))) {
            throw new ResponseException("error.RequiredRange", "请输入合理的范围范围");
        }
    }

    /**
     * 物料反向追溯报表
     *
     * @param reverseTraceReportRequestDto 反向追溯参数
     * @param subWsProductionMode          管控力度
     * @return
     */
    @FetchMethod
    public ReverseTraceReportResultDTO getMaterialReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, Boolean subWsProductionMode) {
        ReverseTraceReportResultDTO reverseTraceReportResultDto = new ReverseTraceReportResultDTO();
        List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatches = getBatchWorkDetailMaterials(reverseTraceReportRequestDto, reverseTraceReportResultDto, subWsProductionMode);
        //工单对应的 工序物料使用详情
        List<ReverseTraceReportResultDTO.ReverseTraceMaterial> reverseTraceMaterials = Lists.newArrayList();
        if (subWsProductionMode) {
            batchWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getSubWorkSheet()))
                    .forEach((subWorkSheet, batchWorkDetailMaterialBatcheList) -> {
                        List<BatchWorkDetailMaterialBatch> sortList = batchWorkDetailMaterialBatcheList.stream().sorted(Comparator.comparing(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        //子工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceMaterial reverseTraceMaterial = new ReverseTraceReportResultDTO.ReverseTraceMaterial();
                        reverseTraceMaterial.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(subWorkSheet));
                        //修改工单 对应的 工序 中 存在的 批次信息
                        updateReverseTraceMaterial(reverseTraceMaterial, sortList, reverseTraceReportRequestDto);
                        reverseTraceMaterials.add(reverseTraceMaterial);
                    });
            reverseTraceReportResultDto.setReverseTraceMaterials(reverseTraceMaterials);
        } else {
            batchWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getWorkSheet()))
                    .forEach((workSheet, batchWorkDetailMaterialBatcheList) -> {
                        //工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceMaterial reverseTraceMaterial = new ReverseTraceReportResultDTO.ReverseTraceMaterial();
                        reverseTraceMaterial.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(workSheet));
                        List<BatchWorkDetailMaterialBatch> sortList = batchWorkDetailMaterialBatcheList.stream().sorted(Comparator.comparing(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        //修改工单 对应的 工序 中 存在的 批次信息
                        updateReverseTraceMaterial(reverseTraceMaterial, sortList, reverseTraceReportRequestDto);
                        reverseTraceMaterials.add(reverseTraceMaterial);
                    });
            reverseTraceReportResultDto.setReverseTraceMaterials(reverseTraceMaterials);
        }
        return reverseTraceReportResultDto;

    }

    /**
     * 添加查询过滤条件
     *
     * @param reverseTraceReportRequestDto 查询条件
     * @return
     */
    @FetchMethod
    private List<BatchWorkDetailMaterialBatch> getBatchWorkDetailMaterials(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, ReverseTraceReportResultDTO reverseTraceReportResultDto, Boolean subWsProductionMode) {
        Specification<BatchWorkDetailMaterialBatch> specification = (Specification<BatchWorkDetailMaterialBatch>) (root, query, criteriaBuilder) -> {

            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //批次逻辑删除
            Join<BatchWorkDetailMaterialBatch, BatchWorkDetail> batchWorkDetailJoin = root.join("batchWorkDetail");
            Predicate batchWorkDetailDeletedPredicate = criteriaBuilder.equal(batchWorkDetailJoin.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(batchWorkDetailDeletedPredicate);
            //物料id筛选
            if (!ObjectUtils.isEmpty(reverseTraceReportRequestDto.getMaterialId())) {
                Predicate materialId = criteriaBuilder.equal(root.get("materialId"), reverseTraceReportRequestDto.getMaterialId());
                predicateList.add(materialId);
            }
            //物料批次
            if (!ObjectUtils.isEmpty(reverseTraceReportRequestDto.getMaterialBatch())) {
                Predicate materialBatch = criteriaBuilder.equal(root.get("materialBatch"), reverseTraceReportRequestDto.getMaterialBatch());
                predicateList.add(materialBatch);
            }
            if (reverseTraceReportRequestDto.getStartTime() != null && reverseTraceReportRequestDto.getEndTime() != null) {
                predicateList.add(criteriaBuilder.between(root.get("createdDate").as(Instant.class),
                        reverseTraceReportRequestDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant(),
                        reverseTraceReportRequestDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            }
            if (subWsProductionMode) {
                Predicate subWorkSheetPredicate = criteriaBuilder.isNotNull(batchWorkDetailJoin.get("subWorkSheet"));
                predicateList.add(subWorkSheetPredicate);
            } else {
                Predicate workSheetPredicate = criteriaBuilder.isNotNull(batchWorkDetailJoin.get("workSheet"));
                predicateList.add(workSheetPredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };

        if (!ObjectUtils.isEmpty(reverseTraceReportRequestDto.getCurrentPage()) && !ObjectUtils.isEmpty(reverseTraceReportRequestDto.getPageSize())) {
            Page<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchePage = batchWorkDetailMaterialBatchService.find(specification, PageRequest.of(reverseTraceReportRequestDto.getCurrentPage(), reverseTraceReportRequestDto.getPageSize()));
            List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatches = Optional.ofNullable(batchWorkDetailMaterialBatchePage).map(Slice::getContent).orElse(null);

            if (!ValidateUtils.isValid(batchWorkDetailMaterialBatches)) {
                throw new ResponseException("error.BatchWorkDetailMaterialBatchesNotFound", "未获取到查询结果");
            }
            //添加分页条件
            reverseTraceReportResultDto.setCurrentPage(batchWorkDetailMaterialBatchePage.getNumber());
            reverseTraceReportResultDto.setPageSize(batchWorkDetailMaterialBatchePage.getSize());
            reverseTraceReportResultDto.setCountSize(batchWorkDetailMaterialBatchePage.getTotalElements());

            return batchWorkDetailMaterialBatches;
        } else {
            long count = batchWorkDetailMaterialBatchRepository.count(specification);
            if (count < Constants.LONG_ZERO) {
                throw new ResponseException("error.BatchWorkDetailMaterialBatchNotFound", "未获取到查询结果");
            }
            if (count > Constants.EXPORTNUMBER) {
                throw new ResponseException("error.toManyData", "数据量较大,精确检索范围!");
            }
            return batchWorkDetailMaterialBatchService.find(specification);
        }
    }

    /**
     * 修改工单 对应的 工序 中 存在的 批次信息
     *
     * @param reverseTraceMaterial              工单 对应的 工序 中 存在的 批次信息
     * @param batchWorkDetailMaterialBatcheList 工序批次详情物料
     * @param reverseTraceReportRequestDto      请求参数
     */
    private void updateReverseTraceMaterial(ReverseTraceReportResultDTO.ReverseTraceMaterial reverseTraceMaterial, List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatcheList, ReverseTraceReportRequestDTO reverseTraceReportRequestDto) {
        //工单对应工序详情信息
        List<ReverseTraceReportResultDTO.ReverseTraceMaterialInfo> reverseTraceMaterialInfos = Lists.newArrayList();
        //根据工序将物料分别进行分组 -》 工序对应的物料信息
        batchWorkDetailMaterialBatcheList.stream().collect(Collectors.groupingBy(BatchWorkDetailMaterialBatch::getBatchWorkDetail, LinkedHashMap::new, Collectors.toList()))
                .forEach((batchWorkDetail, stepMaterialBatcheList) -> {
                    //工序对应的  物料详情
                    ReverseTraceReportResultDTO.ReverseTraceMaterialInfo reverseTraceMaterialInfo = getReverseTraceMaterialInfos(reverseTraceReportRequestDto, batchWorkDetail, stepMaterialBatcheList);
                    reverseTraceMaterialInfos.add(reverseTraceMaterialInfo);
                });
        reverseTraceMaterial.setReverseTraceMaterialInfos(reverseTraceMaterialInfos);
    }

    /**
     * 获取 反向追溯物料详情 -》工序对应的  物料详情
     *
     * @param reverseTraceReportRequestDto 请求参数
     * @param batchWorkDetail              批次详情
     * @param stepMaterialBatcheList       工序批次详情物料列表
     * @return
     */
    private ReverseTraceReportResultDTO.ReverseTraceMaterialInfo getReverseTraceMaterialInfos(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, BatchWorkDetail batchWorkDetail, List<BatchWorkDetailMaterialBatch> stepMaterialBatcheList) {

        ReverseTraceReportResultDTO.ReverseTraceMaterialInfo reverseTraceMaterialInfo = new ReverseTraceReportResultDTO.ReverseTraceMaterialInfo();
        reverseTraceMaterialInfo.setProcessStepInfo(new ReverseTraceReportResultDTO.ProcessStepInfo(batchWorkDetail));

        List<ReverseTraceReportResultDTO.ProcessStepMaterialInfo> processStepMaterialInfos = Lists.newArrayList();

        //验证 是否存在 容器投产
        List<ProcessStepMaterialBatchDTO> containerDetailMaterialBatches = containerDetailMaterialBatchRepository.findByContainerDetailBatchWorkDetailIdAndMaterialIdAndBatchAndDeleted(batchWorkDetail.getId(), reverseTraceReportRequestDto.getMaterialId(), reverseTraceReportRequestDto.getMaterialBatch(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailMaterialBatches)) {
            containerDetailMaterialBatches = containerDetailMaterialBatches.stream().sorted(Comparator.comparing(ProcessStepMaterialBatchDTO::getId)).collect(Collectors.toList());
            containerDetailMaterialBatches.stream().collect(Collectors.groupingBy(ProcessStepMaterialBatchDTO::getId))
                    .forEach((containerDetailId, containerMaterials) -> {
                        List<ProcessStepMaterialBatchDTO> snWorkDetailMaterialBatches = snWorkDetailMaterialBatchRepository.findBySnWorkDetailContainerDetailIdAndMaterialIdAndMaterialBatchAndDeleted(containerDetailId, reverseTraceReportRequestDto.getMaterialId(), reverseTraceReportRequestDto.getMaterialBatch(), Constants.LONG_ZERO);
                        //根据sn详情进行分组
                        if (ValidateUtils.isValid(snWorkDetailMaterialBatches)) {
                            snWorkDetailMaterialBatches = snWorkDetailMaterialBatches.stream().sorted(Comparator.comparing(ProcessStepMaterialBatchDTO::getId)).collect(Collectors.toList());
                            snWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(ProcessStepMaterialBatchDTO::getId))
                                    .forEach((snWorkDetail, snMaterials) -> {
                                        //sn 对应的工序物料详情
                                        ReverseTraceReportResultDTO.ProcessStepMaterialInfo snProcessStepMaterialInfo = getSnProcessStepMaterialInfo(snMaterials.get(Constants.INT_ZERO), snMaterials);
                                        processStepMaterialInfos.add(snProcessStepMaterialInfo);
                                    });
                        }
                        //不存在 sn 只存在 容器
                        else {
                            ReverseTraceReportResultDTO.ProcessStepMaterialInfo containerProcessStepMaterialInfo = new ReverseTraceReportResultDTO.ProcessStepMaterialInfo(containerMaterials.get(Constants.INT_ZERO));
                            List<ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo> containerMaterialBatchInfos = containerMaterials.stream().map(ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo::new).collect(Collectors.toList());
                            containerProcessStepMaterialInfo.setMaterialBatchInfos(containerMaterialBatchInfos);
                            processStepMaterialInfos.add(containerProcessStepMaterialInfo);

                        }
                    });
        }
        //验证是否存在 sn投产
        else {
            List<ProcessStepMaterialBatchDTO> snWorkDetailMaterialBatches = !ObjectUtils.isEmpty(batchWorkDetail.getSubWorkSheet()) ?
                    snWorkDetailMaterialBatchRepository.findBySubWsStepMaterialIdAndMaterialBatchAndDeleted(batchWorkDetail.getStep().getId(), batchWorkDetail.getSubWorkSheet().getId(), reverseTraceReportRequestDto.getMaterialId(), reverseTraceReportRequestDto.getMaterialBatch(), Constants.LONG_ZERO) :
                    snWorkDetailMaterialBatchRepository.findByWsStepMaterialIdAndMaterialBatchAndDeleted(batchWorkDetail.getStep().getId(), batchWorkDetail.getWorkSheet().getId(), reverseTraceReportRequestDto.getMaterialId(), reverseTraceReportRequestDto.getMaterialBatch(), Constants.LONG_ZERO);

            if (ValidateUtils.isValid(snWorkDetailMaterialBatches)) {
                snWorkDetailMaterialBatches = snWorkDetailMaterialBatches.stream().sorted(Comparator.comparing(ProcessStepMaterialBatchDTO::getId)).collect(Collectors.toList());
                snWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(ProcessStepMaterialBatchDTO::getId))
                        .forEach((snWorkDetail, snMaterials) -> {
                            //sn 对应的工序物料详情
                            ReverseTraceReportResultDTO.ProcessStepMaterialInfo snProcessStepMaterialInfo = getSnProcessStepMaterialInfo(snMaterials.get(0), snMaterials);
                            processStepMaterialInfos.add(snProcessStepMaterialInfo);
                        });
            }
        }
        //批量投产
        if (!ValidateUtils.isValid(processStepMaterialInfos)) {
            ReverseTraceReportResultDTO.ProcessStepMaterialInfo stepProcessStepMaterialInfo = new ReverseTraceReportResultDTO.ProcessStepMaterialInfo(batchWorkDetail);
            List<ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo> stepMaterialBatchInfos = stepMaterialBatcheList.stream().map(ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo::new).collect(Collectors.toList());
            stepProcessStepMaterialInfo.setMaterialBatchInfos(stepMaterialBatchInfos);
            processStepMaterialInfos.add(stepProcessStepMaterialInfo);
        }
        reverseTraceMaterialInfo.setProcessStepMaterialInfos(processStepMaterialInfos);
        return reverseTraceMaterialInfo;
    }

    /**
     * 获取 sn反向追溯物料详情 -》工序对应的  物料详情
     *
     * @param snWorkDetail                sn详情
     * @param snWorkDetailMaterialBatches sn使用物料详情批次
     * @return
     */
    private ReverseTraceReportResultDTO.ProcessStepMaterialInfo getSnProcessStepMaterialInfo(ProcessStepMaterialBatchDTO snWorkDetail, List<ProcessStepMaterialBatchDTO> snWorkDetailMaterialBatches) {
        ReverseTraceReportResultDTO.ProcessStepMaterialInfo snProcessStepMaterialInfo = new ReverseTraceReportResultDTO.ProcessStepMaterialInfo(snWorkDetail);
        List<ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo> snMaterialBatchInfos = snWorkDetailMaterialBatches.stream().map(ReverseTraceReportResultDTO.ProcessStepMaterialInfo.MaterialBatchInfo::new).collect(Collectors.toList());
        return snProcessStepMaterialInfo.setMaterialBatchInfos(snMaterialBatchInfos);
    }

    /**
     * 反向追溯获取设备信息
     *
     * @param reverseTraceReportRequestDto
     * @param subWsProductionMode
     */
    @FetchMethod
    public ReverseTraceReportResultDTO getFacilityReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, Boolean subWsProductionMode) {

        ReverseTraceReportResultDTO reverseTraceReportResultDto = new ReverseTraceReportResultDTO();
        List<BatchWorkDetailFacility> batchWorkDetailFacilities = this.getBatchWorkDetailFacilities(reverseTraceReportRequestDto, reverseTraceReportResultDto, subWsProductionMode);
        List<ReverseTraceReportResultDTO.ReverseTraceFacility> reverseTraceFacilities = Lists.newArrayList();
        if (subWsProductionMode) {
            batchWorkDetailFacilities.stream().filter(batchWorkDetailFacility -> ObjectUtils.isNotEmpty(batchWorkDetailFacility.getBatchWorkDetail().getSubWorkSheet())).collect(Collectors.groupingBy(batchWorkDetailFacility -> batchWorkDetailFacility.getBatchWorkDetail().getSubWorkSheet()))
                    .forEach((subWorkSheet, batchWorkDetailFacilitiyList) -> {
                        //子工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceFacility reverseTraceFacility = new ReverseTraceReportResultDTO.ReverseTraceFacility();
                        reverseTraceFacility.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(subWorkSheet));
                        List<BatchWorkDetailFacility> sortList = batchWorkDetailFacilitiyList.stream().sorted(Comparator.comparing(batchWorkDetailFacility -> batchWorkDetailFacility.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        ;
                        //修改工单 对应的 工序 中 存在的 批次信息
                        updateReverseTraceFacility(reverseTraceFacility, sortList, reverseTraceReportRequestDto);
                        reverseTraceFacilities.add(reverseTraceFacility);
                    });
            reverseTraceReportResultDto.setReverseTraceFacilities(reverseTraceFacilities);
        } else {

            batchWorkDetailFacilities.stream().filter(batchWorkDetailFacility -> ObjectUtils.isNotEmpty(batchWorkDetailFacility.getBatchWorkDetail().getWorkSheet())).collect(Collectors.groupingBy(batchWorkDetailFacility -> batchWorkDetailFacility.getBatchWorkDetail().getWorkSheet()))
                    .forEach((workSheet, batchWorkDetailFacilityList) -> {
                        //工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceFacility reverseTraceFacility = new ReverseTraceReportResultDTO.ReverseTraceFacility();
                        reverseTraceFacility.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(workSheet));
                        List<BatchWorkDetailFacility> sortList = batchWorkDetailFacilityList.stream().sorted(Comparator.comparing(batchWorkDetailFacility -> batchWorkDetailFacility.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        ;
                        //修改工单 对应的 工序 中 存在的 批次信息
                        updateReverseTraceFacility(reverseTraceFacility, sortList, reverseTraceReportRequestDto);
                        reverseTraceFacilities.add(reverseTraceFacility);
                    });
            reverseTraceReportResultDto.setReverseTraceFacilities(reverseTraceFacilities);
        }
        return reverseTraceReportResultDto;
    }

    /**
     * 更新设备追溯 中 工单对应工序信息
     *
     * @param reverseTraceFacility         设备最追溯信息
     * @param batchWorkDetailFacilities    设备使用详列表
     * @param reverseTraceReportRequestDto 请求参数
     */
    private void updateReverseTraceFacility(ReverseTraceReportResultDTO.ReverseTraceFacility reverseTraceFacility, List<BatchWorkDetailFacility> batchWorkDetailFacilities, ReverseTraceReportRequestDTO reverseTraceReportRequestDto) {
        //工单对应工序详情信息
        List<ReverseTraceReportResultDTO.ReverseTraceFacilityInfo> reverseTraceFacilityInfos = Lists.newArrayList();

        //根据工序将设备分别进行分组 -》 工序对应的设备信息
        batchWorkDetailFacilities.stream().collect(Collectors.groupingBy(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail(), LinkedHashMap::new, Collectors.toList()))
                .forEach((batchWorkDetail, stepFacilityBatcheList) -> {
                    //工序对应的  设备详情
                    ReverseTraceReportResultDTO.ReverseTraceFacilityInfo reverseTraceFacilityInfo = getReverseTraceFacilityInfos(reverseTraceReportRequestDto, batchWorkDetail, stepFacilityBatcheList);
                    reverseTraceFacilityInfos.add(reverseTraceFacilityInfo);
                });
        reverseTraceFacility.setReverseTraceFacilityInfos(reverseTraceFacilityInfos);
    }

    /**
     * 获取设备追溯中，工序对应的 设备详情使用记录信息
     * <p>
     * 优先 -》 工单-容器-sn -》 工单-容器 -》 工单 -sn -》 工单 的层级获取设备使用记录信息
     *
     * @param reverseTraceReportRequestDto 请求参数
     * @param batchWorkDetail              批次信息
     * @param stepFacilityBatcheList       设备使用详列表
     * @return
     */
    private ReverseTraceReportResultDTO.ReverseTraceFacilityInfo getReverseTraceFacilityInfos(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, BatchWorkDetail batchWorkDetail, List<BatchWorkDetailFacility> stepFacilityBatcheList) {

        ReverseTraceReportResultDTO.ReverseTraceFacilityInfo reverseTraceFacilityInfo = new ReverseTraceReportResultDTO.ReverseTraceFacilityInfo();
        reverseTraceFacilityInfo.setProcessStepInfo(new ReverseTraceReportResultDTO.ProcessStepInfo(batchWorkDetail));
        List<ReverseTraceReportResultDTO.ProcessStepFacilityInfo> processStepFacilityInfos = Lists.newArrayList();
        List<ContainerDetailFacility> containerDetailFacilities = containerDetailFacilityRepository.findByContainerDetailBatchWorkDetailIdAndFacilityIdAndDeleted(batchWorkDetail.getId(), reverseTraceReportRequestDto.getFacilityId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailFacilities)) {
            containerDetailFacilities = containerDetailFacilities.stream().sorted(Comparator.comparing(containerDetailFacility -> containerDetailFacility.getContainerDetail().getId())).collect(Collectors.toList());
            containerDetailFacilities.stream().collect(Collectors.groupingBy(containerDetailFacility -> containerDetailFacility.getContainerDetail()))
                    .forEach((containerDetail, containerFacilities) -> {
                        List<SnWorkDetailFacility> snWorkDetailFacilities = snWorkDetailFacilityRepository.findBySnWorkDetailContainerDetailIdAndFacilityIdAndDeleted(containerDetail.getId(), reverseTraceReportRequestDto.getFacilityId(), Constants.LONG_ZERO);
                        //根据sn详情进行分组
                        if (ValidateUtils.isValid(snWorkDetailFacilities)) {
                            snWorkDetailFacilities = snWorkDetailFacilities.stream().sorted(Comparator.comparing(snWorkDetailFacility -> snWorkDetailFacility.getSnWorkDetail().getId())).collect(Collectors.toList());
                            snWorkDetailFacilities.stream().collect(Collectors.groupingBy(snWorkDetailFacility -> snWorkDetailFacility.getSnWorkDetail()))
                                    .forEach((snWorkDetail, snFacilities) -> {
                                        //sn 对应的工序设备详情
                                        ReverseTraceReportResultDTO.ProcessStepFacilityInfo snProcessStepFacilityInfo = getSnProcessStepFacilityInfo(snWorkDetail, snFacilities);
                                        processStepFacilityInfos.add(snProcessStepFacilityInfo);
                                    });
                        }
                        //不存在 sn 只存在 容器
                        else {
                            ReverseTraceReportResultDTO.ProcessStepFacilityInfo containerProcessStepFacilityInfo = new ReverseTraceReportResultDTO.ProcessStepFacilityInfo(containerDetail);
                            List<ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo> facilityInfos = containerFacilities.stream().map(containerFacility -> {
                                return new ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo(containerFacility.getFacilityDto());
                            }).collect(Collectors.toList());
                            containerProcessStepFacilityInfo.setFacilityInfos(facilityInfos);
                            processStepFacilityInfos.add(containerProcessStepFacilityInfo);

                        }
                    });
        }
        //验证是否存在 sn投产
        else {
            List<SnWorkDetailFacility> snWorkDetailFacilities = !ObjectUtils.isEmpty(batchWorkDetail.getSubWorkSheet()) ?
                    snWorkDetailFacilityRepository.findBySnWorkDetailSubWorkSheetIdAndSnWorkDetailStepIdAndFacilityIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), reverseTraceReportRequestDto.getFacilityId(), Constants.LONG_ZERO) :
                    snWorkDetailFacilityRepository.findBySnWorkDetailWorkSheetIdAndSnWorkDetailStepIdAndFacilityIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), reverseTraceReportRequestDto.getFacilityId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(snWorkDetailFacilities)) {
                snWorkDetailFacilities = snWorkDetailFacilities.stream().sorted(Comparator.comparing(snWorkDetailFacility -> snWorkDetailFacility.getSnWorkDetail().getId())).collect(Collectors.toList());
                snWorkDetailFacilities.stream().collect(Collectors.groupingBy(snWorkDetailFacility -> snWorkDetailFacility.getSnWorkDetail()))
                        .forEach((snWorkDetail, snFacilities) -> {
                            //sn 对应的工序设备详情
                            ReverseTraceReportResultDTO.ProcessStepFacilityInfo snProcessStepFacilityInfo = getSnProcessStepFacilityInfo(snWorkDetail, snFacilities);
                            processStepFacilityInfos.add(snProcessStepFacilityInfo);
                        });
            }
        }
        //批量投产
        if (!ValidateUtils.isValid(processStepFacilityInfos)) {
            ReverseTraceReportResultDTO.ProcessStepFacilityInfo stepProcessStepFacilityInfo = new ReverseTraceReportResultDTO.ProcessStepFacilityInfo(batchWorkDetail);
            List<ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo> stepFacilities = stepFacilityBatcheList.stream().map(stepFacility -> {
                return new ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo(stepFacility.getFacilityDto());
            }).collect(Collectors.toList());
            stepProcessStepFacilityInfo.setFacilityInfos(stepFacilities);
            processStepFacilityInfos.add(stepProcessStepFacilityInfo);
        }
        reverseTraceFacilityInfo.setProcessStepFacilityInfos(processStepFacilityInfos);
        return reverseTraceFacilityInfo;

    }

    /**
     * 获取设备追溯 中， 容器 sn 的 基础信息
     *
     * @param snWorkDetail
     * @param snFacilities
     * @return
     */
    private ReverseTraceReportResultDTO.ProcessStepFacilityInfo getSnProcessStepFacilityInfo(SnWorkDetail snWorkDetail, List<SnWorkDetailFacility> snFacilities) {

        ReverseTraceReportResultDTO.ProcessStepFacilityInfo snProcessStepFacilityInfo = new ReverseTraceReportResultDTO.ProcessStepFacilityInfo(snWorkDetail);

        List<ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo> snFacilityInfos = snFacilities.stream().map(snFacility -> {
            return new ReverseTraceReportResultDTO.ProcessStepFacilityInfo.FacilityInfo(snFacility.getFacilityDto());
        }).collect(Collectors.toList());
        return snProcessStepFacilityInfo.setFacilityInfos(snFacilityInfos);
    }

    /**
     * 获取反向追溯设备信息列表
     *
     * @param reverseTraceReportRequestDto 请求反向追溯信息
     * @param reverseTraceReportResultDto  返回反向追溯信息
     * @return
     */
    private List<BatchWorkDetailFacility> getBatchWorkDetailFacilities(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, ReverseTraceReportResultDTO reverseTraceReportResultDto, Boolean subWsProductionMode) {

        Instant startDate = reverseTraceReportRequestDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant();

        Instant endDate = reverseTraceReportRequestDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant();

        Long facilityId = reverseTraceReportRequestDto.getFacilityId();

        if (!ObjectUtils.isEmpty(reverseTraceReportRequestDto.getCurrentPage()) && !ObjectUtils.isEmpty(reverseTraceReportRequestDto.getPageSize())) {
            Page<BatchWorkDetailFacility> batchWorkDetailFacilityPage = subWsProductionMode ?
                    batchWorkDetailFacilityRepository.findBySubWorkSheetAndFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(facilityId, startDate, endDate,
                            Constants.LONG_ZERO, PageRequest.of(reverseTraceReportRequestDto.getCurrentPage(), reverseTraceReportRequestDto.getPageSize())) :
                    batchWorkDetailFacilityRepository.findByWorkSheetAndFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(facilityId, startDate, endDate,
                            Constants.LONG_ZERO, PageRequest.of(reverseTraceReportRequestDto.getCurrentPage(), reverseTraceReportRequestDto.getPageSize()));

            List<BatchWorkDetailFacility> batchWorkDetailFacilities = Optional.ofNullable(batchWorkDetailFacilityPage).map(Slice::getContent).orElse(null);

            if (!ValidateUtils.isValid(batchWorkDetailFacilities)) {
                throw new ResponseException("error.BatchWorkDetailFacilitiesNotFound", "未获取到查询结果");
            }
            //添加分页条件
            reverseTraceReportResultDto.setCurrentPage(batchWorkDetailFacilityPage.getNumber());
            reverseTraceReportResultDto.setPageSize(batchWorkDetailFacilityPage.getSize());
            reverseTraceReportResultDto.setCountSize(batchWorkDetailFacilityPage.getTotalElements());

            return batchWorkDetailFacilities;
        } else {
            Long count = subWsProductionMode ? batchWorkDetailFacilityRepository.countBySubWorkSheetAndFacilityIdAndCreatedDateAndDeleted(facilityId, startDate, endDate, Constants.LONG_ZERO) :
                    batchWorkDetailFacilityRepository.countByWorkSheetAndFacilityIdAndCreatedDateAndDeleted(facilityId, startDate, endDate, Constants.LONG_ZERO);
            if (count == null || count < Constants.LONG_ZERO) {
                throw new ResponseException("error.BatchWorkDetailFacilitiesNotFound", "未获取到查询结果");
            }
            if (count > Constants.EXPORTNUMBER) {
                throw new ResponseException("error.TooManyData", "数据量较大,精确检索范围!");
            }
            List<BatchWorkDetailFacility> batchWorkDetailFacilities = subWsProductionMode ?
                    batchWorkDetailFacilityRepository.findBySubWorkSheetAndFacilityIdAndCreatedDateBetween(facilityId, startDate, endDate, Constants.LONG_ZERO) :
                    batchWorkDetailFacilityRepository.findByWorkSheetAndFacilityIdAndCreatedDateBetween(facilityId, startDate, endDate, Constants.LONG_ZERO);
            return batchWorkDetailFacilities;
        }

    }


    /**
     * 反向追溯获取易损件信息
     *
     * @param reverseTraceReportRequestDto
     * @param subWsProductionMode
     */
    @FetchMethod
    public ReverseTraceReportResultDTO getWearingPartReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, Boolean subWsProductionMode) {

        ReverseTraceReportResultDTO reverseTraceReportResultDto = new ReverseTraceReportResultDTO();
        List<BatchWorkDetailWearingPart> batchWorkDetailWearingParts = this.getBatchWorkDetailWearingParts(reverseTraceReportRequestDto, reverseTraceReportResultDto, subWsProductionMode);
        List<ReverseTraceReportResultDTO.ReverseTraceWearingPart> reverseTraceWearingParts = Lists.newArrayList();
        if (subWsProductionMode) {
            batchWorkDetailWearingParts.stream().collect(Collectors.groupingBy(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail().getSubWorkSheet()))
                    .forEach((subWorkSheet, batchWorkDetailWearingPartList) -> {
                        //子工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceWearingPart reverseTraceWearingPart = new ReverseTraceReportResultDTO.ReverseTraceWearingPart();
                        reverseTraceWearingPart.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(subWorkSheet));
                        List<BatchWorkDetailWearingPart> sortList = batchWorkDetailWearingPartList.stream().sorted(Comparator.comparing(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        //修改工单 对应的 工序 中 存在的 易损件信息
                        updateReverseTraceWearingPart(reverseTraceWearingPart, sortList, reverseTraceReportRequestDto);
                        reverseTraceWearingParts.add(reverseTraceWearingPart);
                    });
            reverseTraceReportResultDto.setReverseTraceWearingParts(reverseTraceWearingParts);
        } else {

            batchWorkDetailWearingParts.stream().collect(Collectors.groupingBy(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail().getWorkSheet()))
                    .forEach((workSheet, batchWorkDetailWearingPartList) -> {
                        //工单详情信息
                        ReverseTraceReportResultDTO.ReverseTraceWearingPart reverseTraceWearingPart = new ReverseTraceReportResultDTO.ReverseTraceWearingPart();
                        reverseTraceWearingPart.setReverseWorkSheetInfo(new ReverseTraceReportResultDTO.ReverseWorkSheetInfo(workSheet));
                        List<BatchWorkDetailWearingPart> sortList = batchWorkDetailWearingPartList.stream().sorted(Comparator.comparing(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail().getId())).collect(Collectors.toList());
                        //修改工单 对应的 工序 中 存在的 易损件信息
                        updateReverseTraceWearingPart(reverseTraceWearingPart, sortList, reverseTraceReportRequestDto);
                        reverseTraceWearingParts.add(reverseTraceWearingPart);
                    });
            reverseTraceReportResultDto.setReverseTraceWearingParts(reverseTraceWearingParts);
        }
        return reverseTraceReportResultDto;


    }

    /**
     * 更新易损件追溯 中 工单对应工序信息
     *
     * @param reverseTraceWearingPart        易损件追溯信息
     * @param batchWorkDetailWearingPartList 易损件使用详列表
     * @param reverseTraceReportRequestDto   请求参数
     */
    private void updateReverseTraceWearingPart(ReverseTraceReportResultDTO.ReverseTraceWearingPart reverseTraceWearingPart, List<BatchWorkDetailWearingPart> batchWorkDetailWearingPartList, ReverseTraceReportRequestDTO reverseTraceReportRequestDto) {

        //工单对应工序详情信息
        List<ReverseTraceReportResultDTO.ReverseTraceWearingPartInfo> reverseTraceWearingPartInfos = Lists.newArrayList();

        //根据工序将易损件分别进行分组 -》 工序对应的易损件信息
        batchWorkDetailWearingPartList.stream().collect(Collectors.groupingBy(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail(), LinkedHashMap::new, Collectors.toList()))
                .forEach((batchWorkDetail, batchWorkDetailWearingParts) -> {
                    //工序对应的  易损件详情
                    ReverseTraceReportResultDTO.ReverseTraceWearingPartInfo reverseTraceWearingPartInfo = getReverseTraceWearingPartInfos(reverseTraceReportRequestDto, batchWorkDetail, batchWorkDetailWearingParts);
                    reverseTraceWearingPartInfos.add(reverseTraceWearingPartInfo);
                });
        reverseTraceWearingPart.setReverseTraceWearingPartInfos(reverseTraceWearingPartInfos);
    }

    /**
     * 获取易损件追溯中，工序对应的 易损件详情使用记录信息
     * <p>
     * 优先 -》 工单-容器-sn -》 工单-容器 -》 工单 -sn -》 工单 的层级获取易损件使用记录信息
     *
     * @param reverseTraceReportRequestDto 请求参数
     * @param batchWorkDetail              批次信息
     * @param batchWorkDetailWearingParts  易损件使用详列表
     * @return
     */
    private ReverseTraceReportResultDTO.ReverseTraceWearingPartInfo getReverseTraceWearingPartInfos(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, BatchWorkDetail batchWorkDetail, List<BatchWorkDetailWearingPart> batchWorkDetailWearingParts) {

        ReverseTraceReportResultDTO.ReverseTraceWearingPartInfo reverseTraceWearingPartInfo = new ReverseTraceReportResultDTO.ReverseTraceWearingPartInfo();
        reverseTraceWearingPartInfo.setProcessStepInfo(new ReverseTraceReportResultDTO.ProcessStepInfo(batchWorkDetail));
        List<ReverseTraceReportResultDTO.ProcessStepWearingPartInfo> processStepWearingPartInfos = Lists.newArrayList();
        List<ContainerDetailWearingPart> containerDetailWearingParts = containerDetailWearingPartRepository.findByContainerDetailBatchWorkDetailIdAndWearingPartIdAndDeleted(batchWorkDetail.getId(), reverseTraceReportRequestDto.getWearingPartId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailWearingParts)) {
            containerDetailWearingParts = containerDetailWearingParts.stream().sorted(Comparator.comparing(containerDetailWearingPart -> containerDetailWearingPart.getContainerDetail().getId())).collect(Collectors.toList());
            containerDetailWearingParts.stream().collect(Collectors.groupingBy(ContainerDetailWearingPart::getContainerDetail))
                    .forEach((containerDetail, containerWearingParts) -> {
                        List<SnWorkDetailWearingPart> snWorkDetailWearingParts = snWorkDetailWearingPartRepository.findBySnWorkDetailContainerDetailIdAndWearingPartIdAndDeleted(containerDetail.getId(), reverseTraceReportRequestDto.getWearingPartId(), Constants.LONG_ZERO);
                        //根据sn详情进行分组
                        if (ValidateUtils.isValid(snWorkDetailWearingParts)) {
                            snWorkDetailWearingParts = snWorkDetailWearingParts.stream().sorted(Comparator.comparing(snWorkDetailWearingPart -> snWorkDetailWearingPart.getSnWorkDetail().getId())).collect(Collectors.toList());
                            snWorkDetailWearingParts.stream().collect(Collectors.groupingBy(SnWorkDetailWearingPart::getSnWorkDetail))
                                    .forEach((snWorkDetail, snWorkDetailWearingPartList) -> {
                                        //sn 对应的工序易损件详情
                                        ReverseTraceReportResultDTO.ProcessStepWearingPartInfo snProcessStepWearingPartInfo = getSnProcessStepWearingPartInfo(snWorkDetail, snWorkDetailWearingPartList);
                                        processStepWearingPartInfos.add(snProcessStepWearingPartInfo);
                                    });
                        }
                        //不存在 sn 只存在 容器
                        else {
                            ReverseTraceReportResultDTO.ProcessStepWearingPartInfo processStepWearingPartInfo = new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo(containerDetail);
                            List<ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo> wearingPartInfos = containerWearingParts.stream().map(containerDetailWearingPart -> {
                                return new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo(containerDetailWearingPart.getWearingPart());
                            }).collect(Collectors.toList());
                            processStepWearingPartInfo.setWearingPartInfos(wearingPartInfos);
                            processStepWearingPartInfos.add(processStepWearingPartInfo);
                        }
                    });
        }
        //验证是否存在 sn投产
        else {
            List<SnWorkDetailWearingPart> snWorkDetailWearingParts = !ObjectUtils.isEmpty(batchWorkDetail.getSubWorkSheet()) ?
                    snWorkDetailWearingPartRepository.findBySnWorkDetailSubWorkSheetIdAndSnWorkDetailStepIdAndWearingPartIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), reverseTraceReportRequestDto.getWearingPartId(), Constants.LONG_ZERO) :
                    snWorkDetailWearingPartRepository.findBySnWorkDetailWorkSheetIdAndSnWorkDetailStepIdAndWearingPartIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), reverseTraceReportRequestDto.getWearingPartId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(snWorkDetailWearingParts)) {
                snWorkDetailWearingParts = snWorkDetailWearingParts.stream().sorted(Comparator.comparing(snWorkDetailWearingPart -> snWorkDetailWearingPart.getSnWorkDetail().getId())).collect(Collectors.toList());
                snWorkDetailWearingParts.stream().collect(Collectors.groupingBy(snWorkDetailWearingPart -> snWorkDetailWearingPart.getSnWorkDetail()))
                        .forEach((snWorkDetail, snWearingParts) -> {
                            //sn 对应的工序易损件详情
                            ReverseTraceReportResultDTO.ProcessStepWearingPartInfo snProcessStepWearingPartInfo = getSnProcessStepWearingPartInfo(snWorkDetail, snWearingParts);
                            processStepWearingPartInfos.add(snProcessStepWearingPartInfo);
                        });
            }
        }
        //批量投产
        if (!ValidateUtils.isValid(processStepWearingPartInfos)) {
            ReverseTraceReportResultDTO.ProcessStepWearingPartInfo stepProcessStepWearingPartInfo = new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo(batchWorkDetail);
            List<ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo> stepWearingParts = batchWorkDetailWearingParts.stream().map(stepWearingPart -> {
                return new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo(stepWearingPart.getWearingPart());
            }).collect(Collectors.toList());
            stepProcessStepWearingPartInfo.setWearingPartInfos(stepWearingParts);
            processStepWearingPartInfos.add(stepProcessStepWearingPartInfo);
        }
        reverseTraceWearingPartInfo.setProcessStepWearingPartInfos(processStepWearingPartInfos);
        return reverseTraceWearingPartInfo;

    }

    /**
     * 获取易损件追溯 中， 容器 sn 的 基础信息
     *
     * @param snWorkDetail   sn详情
     * @param snWearingParts sn易损件使用信息
     * @return
     */
    private ReverseTraceReportResultDTO.ProcessStepWearingPartInfo getSnProcessStepWearingPartInfo(SnWorkDetail snWorkDetail, List<SnWorkDetailWearingPart> snWearingParts) {

        ReverseTraceReportResultDTO.ProcessStepWearingPartInfo snProcessStepWearingPartInfo = new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo(snWorkDetail);

        List<ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo> snWearingPartInfos = snWearingParts.stream().map(snWearingPart -> {
            return new ReverseTraceReportResultDTO.ProcessStepWearingPartInfo.WearingPartInfo(snWearingPart.getWearingPart());
        }).collect(Collectors.toList());
        return snProcessStepWearingPartInfo.setWearingPartInfos(snWearingPartInfos);
    }

    /**
     * 获取反向追溯易损件信息列表
     *
     * @param reverseTraceReportRequestDto 请求反向追溯信息
     * @param reverseTraceReportResultDto  返回反向追溯信息
     * @return
     */
    private List<BatchWorkDetailWearingPart> getBatchWorkDetailWearingParts(ReverseTraceReportRequestDTO reverseTraceReportRequestDto, ReverseTraceReportResultDTO reverseTraceReportResultDto, Boolean subWsProductionMode) {

        Instant startDate = reverseTraceReportRequestDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant();

        Instant endDate = reverseTraceReportRequestDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant();

        Long wearingPartId = reverseTraceReportRequestDto.getWearingPartId();


        if (!ObjectUtils.isEmpty(reverseTraceReportRequestDto.getCurrentPage()) && !ObjectUtils.isEmpty(reverseTraceReportRequestDto.getPageSize())) {
            Page<BatchWorkDetailWearingPart> batchWorkDetailWearingPartPage = subWsProductionMode ?
                    batchWorkDetailWearingPartRepository.findBySubWorkSheetAndWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(wearingPartId, startDate, endDate, Constants.LONG_ZERO, PageRequest.of(reverseTraceReportRequestDto.getCurrentPage(), reverseTraceReportRequestDto.getPageSize())) :
                    batchWorkDetailWearingPartRepository.findByWorkSheetAndWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(wearingPartId, startDate, endDate, Constants.LONG_ZERO, PageRequest.of(reverseTraceReportRequestDto.getCurrentPage(), reverseTraceReportRequestDto.getPageSize()));

            List<BatchWorkDetailWearingPart> batchWorkDetailWearingParts = Optional.ofNullable(batchWorkDetailWearingPartPage).map(Slice::getContent).orElse(null);

            if (!ValidateUtils.isValid(batchWorkDetailWearingParts)) {
                throw new ResponseException("error.BatchWorkDetailFacilitiesNotFound", "未获取到查询结果");
            }
            //添加分页条件
            reverseTraceReportResultDto.setCurrentPage(batchWorkDetailWearingPartPage.getNumber());
            reverseTraceReportResultDto.setPageSize(batchWorkDetailWearingPartPage.getSize());
            reverseTraceReportResultDto.setCountSize(batchWorkDetailWearingPartPage.getTotalElements());

            return batchWorkDetailWearingParts;
        } else {
            Long count = subWsProductionMode ?
                    batchWorkDetailWearingPartRepository.countBySubWorkSheetAndWearingPartIdAndCreatedDateAndDeleted(wearingPartId, startDate, endDate, Constants.LONG_ZERO) :
                    batchWorkDetailWearingPartRepository.countByWorkSheetAndWearingPartIdAndCreatedDateAndDeleted(wearingPartId, startDate, endDate, Constants.LONG_ZERO);
            if (count == null || count < Constants.LONG_ZERO) {
                throw new ResponseException("error.BatchWorkDetailWearingPartsNotFound", "未获取到查询结果");
            }
            if (count > Constants.EXPORTNUMBER) {
                throw new ResponseException("error.TooManyData", "数据量较大,精确检索范围!");
            }
            List<BatchWorkDetailWearingPart> batchWorkDetailWearingParts = subWsProductionMode ?
                    batchWorkDetailWearingPartRepository.findBySubWorkSheetAndWearingPartIdAndCreatedDateBetween(wearingPartId, startDate, endDate, Constants.LONG_ZERO) :
                    batchWorkDetailWearingPartRepository.findByWorkSheetAndWearingPartIdAndCreatedDateBetween(wearingPartId, startDate, endDate, Constants.LONG_ZERO);
            return batchWorkDetailWearingParts;
        }
    }

}
