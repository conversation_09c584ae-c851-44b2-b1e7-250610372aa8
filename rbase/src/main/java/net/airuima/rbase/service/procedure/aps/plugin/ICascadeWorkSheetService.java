package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.web.rest.procedure.aps.dto.CreateCascadeWsGetDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface ICascadeWorkSheetService {

    @FuncInterceptor("CascadePlanOrder")
    default List<CreateCascadeWsGetDTO> findCascadeWorkSheetPlanToOrderInfo(List<BomDTO> bomDtoList) {
        return new ArrayList<>();
    }
}
