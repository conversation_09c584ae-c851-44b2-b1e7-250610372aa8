package net.airuima.rbase.service.wip;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import org.springframework.stereotype.Component;

@Component
public class PIWipLedgerService {

    @BeanDefine(value = "net.airuima.wip.service.procedure.api.IWipLedgerService", interfaceBean = true,funcKey = "SideWarehouse")
    public void processWarehouseAndSaveLedger(WorkLine workLine, WsMaterialBatch wsMaterialBatch, double changeNumber, int operation, Long operatorId) {
    }
}
