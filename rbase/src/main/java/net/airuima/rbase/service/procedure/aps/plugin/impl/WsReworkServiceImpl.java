package net.airuima.rbase.service.procedure.aps.plugin.impl;

import com.google.common.collect.Lists;
import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.rbase.client.feign.digiwin.SyncReworkSheetFeignClient;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.dto.sync.SyncReworkSheetDTO;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.service.procedure.aps.plugin.IWsReworkService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsReworkServiceImpl implements IWsReworkService {
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private SyncReworkSheetFeignClient syncReworkSheetFeignClient;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    /**
     * 同步返工单至erp
     * @param wsReworkList 返工单列表
     * <AUTHOR>
     * @date  2023/3/20
     * @return void
     */
    @Override
    public void syncReworkSheetToErp(List<WsRework> wsReworkList) {
        try{
            if (ValidateUtils.isValid(wsReworkList)){
                List<SyncReworkSheetDTO> syncReworkSheetDtoList = Lists.newArrayList();
                // 获取当前操作人信息
                UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(SecurityUtils.getCurrentUserLogin().orElse(null));
                StaffDTO staff = Objects.nonNull(userDTO) ? userDTO.getStaffDTO():new StaffDTO() ;
                //添加返工单信息
                wsReworkList.forEach(wsRework -> {
                    SyncReworkSheetDTO syncReworkSheetDto = new SyncReworkSheetDTO(wsRework);
                    //添加部门员工
                    if (!ObjectUtils.isEmpty(staff.getId())){
                        syncReworkSheetDto.setStaffCode(staff.getCode());
                        if (staff.getOrganization() != null && !ObjectUtils.isEmpty(staff.getOrganization().getId())){
                            syncReworkSheetDto.setOrganizationCode(staff.getOrganization().getCode());
                        }
                    }
                    //添加返工单投料单信息
                    List<WsMaterial> wsMaterials = wsMaterialRepository.findByWorkSheetIdAndDeleted(wsRework.getReworkWorkSheet().getId(), Constants.LONG_ZERO);
                    if (ValidateUtils.isValid(wsMaterials)){
                        List<SyncReworkSheetDTO.WsReworkMaterialDTO> wsReworkMaterialDtos = wsMaterials.stream().map(SyncReworkSheetDTO.WsReworkMaterialDTO::new).distinct().collect(Collectors.toList());
                        syncReworkSheetDto.setWsReworkMaterialDtoList(wsReworkMaterialDtos);
                    }
                    syncReworkSheetDtoList.add(syncReworkSheetDto);
                });
                //返工单信息推送erp
                if (ValidateUtils.isValid(syncReworkSheetDtoList)){
                    syncReworkSheetFeignClient.syncReworkSheet(syncReworkSheetDtoList);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
