package net.airuima.rbase.service.procedure.aps;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SaleOrderDetail;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.procedure.aps.SaleOrderDetailRepository;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 销售订单详情Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SaleOrderDetailService extends CommonJpaService<SaleOrderDetail> {

    private final SaleOrderDetailRepository saleOrderDetailRepository;

    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;

    public SaleOrderDetailService(SaleOrderDetailRepository saleOrderDetailRepository) {
        this.saleOrderDetailRepository = saleOrderDetailRepository;
    }


    @Override
    @FetchMethod
    public Page<SaleOrderDetail> find(Specification<SaleOrderDetail> spec, Pageable pageable) {
        return saleOrderDetailRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<SaleOrderDetail> find(Specification<SaleOrderDetail> spec) {
        return saleOrderDetailRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<SaleOrderDetail> findAll(Pageable pageable) {
        return saleOrderDetailRepository.findAll(pageable);
    }

    /**
     * 根据订单号模糊查询
     *
     * @param text 根据订单号模糊查询
     * @param size 返回数据个数
     * @return List<SaleOrderDetail>
     * <AUTHOR>
     * @date 2022/12/29
     */
    @Transactional(readOnly = true)
    public List<SaleOrderDetail> bySerialNumberAndPedigreeId(String text, Long pedigreeId, int size) {
        return Optional.ofNullable(saleOrderDetailRepository.bySerialNumberAndPedigreeId(text, pedigreeId, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 根据订单号和客户id查询
     *
     * @param pedigreeId 订单号
     * @param clientId   客户id
     * @param pageable   分页参数
     * @return List<SaleOrderDetail>
     * <AUTHOR>
     */
    public List<SaleOrderDetail> findByPedigreeIdAndClientId(Long pedigreeId, Long clientId, Pageable pageable) {
        String result = rbaseSysCodeProxy.findByCode("key_so_to_ws_multi_client");
        Boolean isMultiClient = Boolean.FALSE;
        if (!StringUtils.isBlank(result) && Boolean.parseBoolean(result)) {
            isMultiClient = Boolean.TRUE;
        }
        if (isMultiClient){
            return Optional.ofNullable(saleOrderDetailRepository.findByPedigreeIdAndDeleted(pedigreeId, Constants.LONG_ZERO, pageable)).map(Slice::getContent).orElse(null);
        }else {
            return Optional.ofNullable(saleOrderDetailRepository.findByPedigreeIdAndSaleOrderClientIdAndDeleted(pedigreeId, clientId, Constants.LONG_ZERO, pageable)).map(Slice::getContent).orElse(null);
        }
    }
}
