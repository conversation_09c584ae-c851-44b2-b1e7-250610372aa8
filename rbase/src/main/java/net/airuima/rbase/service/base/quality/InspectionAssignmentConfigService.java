package net.airuima.rbase.service.base.quality;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.quality.InspectionAssignmentConfig;
import net.airuima.rbase.repository.base.quality.InspectionAssignmentConfigRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 质检人员设置
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InspectionAssignmentConfigService extends CommonJpaService<InspectionAssignmentConfig> {

    private final InspectionAssignmentConfigRepository inspectionAssignmentConfigRepository;

    public InspectionAssignmentConfigService(InspectionAssignmentConfigRepository inspectionAssignmentConfigRepository) {
        this.inspectionAssignmentConfigRepository = inspectionAssignmentConfigRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<InspectionAssignmentConfig> find(Specification<InspectionAssignmentConfig> spec, Pageable pageable) {
        return inspectionAssignmentConfigRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<InspectionAssignmentConfig> find(Specification<InspectionAssignmentConfig> spec) {
        return inspectionAssignmentConfigRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<InspectionAssignmentConfig> findAll(Pageable pageable) {
        return inspectionAssignmentConfigRepository.findAll(pageable);
    }
}
