package net.airuima.rbase.service.procedure.material;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.material.CheckMaterialDetailDTO;

import java.util.List;
@FuncDefault
public interface IWsCheckMaterialDetailService {

    /**
     * 保存工单核料数量信息
     *
     * @param checkMaterialDetailDtoList 核料数量
     * @return
     */
    @FuncInterceptor("WorksheetMaterial && WsMaterialBatch")
    default void saveCheckMaterialDetail(List<CheckMaterialDetailDTO> checkMaterialDetailDtoList) {}

}
