package net.airuima.rbase.service.procedure.aps;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.OperationEnum;
import net.airuima.rbase.constant.ProductionPlanCategoryEnum;
import net.airuima.rbase.constant.ProductionPlanExcelConstants;
import net.airuima.domain.base.AuditSfIdEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepGroupRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.util.ResponseException;
import net.airuima.rbase.web.rest.procedure.aps.dto.ProductionPlanDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划 Service
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionPlanService extends CommonJpaService<ProductionPlan> implements IProductionPlanService {

    private final String PRODUCTION_PLAN_ENTITY_GRAPH = "productionPlanEntityGraph";
    private final Logger log = LoggerFactory.getLogger(ProductionPlanService.class);

    private final ProductionPlanRepository productionPlanRepository;

    private final WorkLineRepository workLineRepository;

    private final PedigreeRepository pedigreeRepository;

    private final StepGroupRepository stepGroupRepository;

    public ProductionPlanService(ProductionPlanRepository productionPlanRepository, WorkLineRepository workLineRepository, PedigreeRepository pedigreeRepository, StepGroupRepository stepGroupRepository) {
        this.productionPlanRepository = productionPlanRepository;
        this.workLineRepository = workLineRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.stepGroupRepository = stepGroupRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ProductionPlan> find(Specification<ProductionPlan> spec, Pageable pageable) {
        return productionPlanRepository.findAll(spec, pageable,new NamedEntityGraph(PRODUCTION_PLAN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<ProductionPlan> find(Specification<ProductionPlan> spec) {
        return productionPlanRepository.findAll(spec,new NamedEntityGraph(PRODUCTION_PLAN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ProductionPlan> findAll(Pageable pageable) {
        return productionPlanRepository.findAll(pageable,new NamedEntityGraph(PRODUCTION_PLAN_ENTITY_GRAPH));
    }


    /**
     * 提交生产计划
     *
     * @param productionPlanDto 生产计划提交参数
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息DTO
     */
    public BaseDTO saveProductionPlan(ProductionPlanDTO productionPlanDto) {
        //生产线
        Long workLineId = productionPlanDto.getWorkLineId();
        //工序组别
        Long stepGroupId = productionPlanDto.getStepGroupId();
        //产品谱系
        Long pedigreeId = productionPlanDto.getPedigreeId();
        //计划粒度
        Integer category = productionPlanDto.getCategory();
        //计划日期
        LocalDate planDate = productionPlanDto.getPlanDate();
        // 校验生产计划唯一性
        checkProductionPlan(workLineId, stepGroupId, pedigreeId, category, planDate);
        ProductionPlan productionPlan = new ProductionPlan();
        //保存生产计划
        BeanUtils.copyProperties(productionPlanDto, productionPlan);
        setProductionPlan(productionPlan, workLineId, stepGroupId, pedigreeId);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 检查生产计划是否已存在
     *
     * @param workLineId  产线ID
     * @param stepGroupId 工序组ID
     * @param pedigreeId  产品谱系ID
     * @param category    计划粒度
     * @param planDate    计划日期
     */
    private void checkProductionPlan(Long workLineId, Long stepGroupId, Long pedigreeId, Integer category, LocalDate planDate) {
        // 工序组粒度
        if (ProductionPlanCategoryEnum.STEP_GROUP.getCategory() == category) {
            Optional<ProductionPlan> productionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndStepGroupIdAndDeleted(planDate, pedigreeId,workLineId,stepGroupId, Constants.LONG_ZERO);
            if (productionPlanOptional.isPresent()) {
                throw new ResponseException("error.ProductionPlanExistNotRepeat", "生产计划已存在，不能重复添加");
            }
        }
        // 产线粒度
        if (ProductionPlanCategoryEnum.WORK_LINE.getCategory() == category) {
            Optional<ProductionPlan> productionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId, Constants.LONG_ZERO);
            if (productionPlanOptional.isPresent()) {
                throw new ResponseException("error.ProductionPlanExistNotRepeat", "生产计划已存在，不能重复添加");
            }
        }
    }

    /**
     * 更新生产计划
     *
     * @param productionPlanDto 生产计划提交参数
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息DTO
     */
    public BaseDTO updateProductionPlan(ProductionPlanDTO productionPlanDto) {
        // 生产计划id
        Long productionPlanId = productionPlanDto.getId();
        // 提取新计划参数
        //生产线
        Long workLineId = productionPlanDto.getWorkLineId();
        //工序组别
        Long stepGroupId = productionPlanDto.getStepGroupId();
        //产品谱系
        Long pedigreeId = productionPlanDto.getPedigreeId();
        //新的生产计划粒度
        Integer category = productionPlanDto.getCategory();
        //计划日期
        LocalDate planDate = productionPlanDto.getPlanDate();
        Optional<ProductionPlan> productionPlanOptional = productionPlanRepository.findByIdAndDeleted(productionPlanId, Constants.LONG_ZERO);
        if (productionPlanOptional.isEmpty()) {
            throw new ResponseException("error.ProductionPlanNotFound", "生产计划不存在");
        }
        ProductionPlan productionPlan = productionPlanOptional.get();
        //生产计划已确认 不能修改
        if (Boolean.TRUE.equals(productionPlan.getStatus())) {
            throw new ResponseException("error.ProductionPlanConfirmNotAllowUpdate", "生产计划已确认,不能修改");
        }
        // 检查新计划是否与已有的计划冲突
        checkForPlanConflict(productionPlan, category, planDate, pedigreeId, workLineId, stepGroupId);
        // 更新生产计划
        BeanUtils.copyProperties(productionPlanDto, productionPlan);
        setProductionPlan(productionPlan, workLineId, stepGroupId, pedigreeId);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 校验更新的生产计划是否和已有计划冲突
     *
     * @param productionPlan 生产计划
     * @param newCategory    新的计划粒度
     * @param planDate       计划日期
     * @param pedigreeId     产品谱系id
     * @param workLineId     产线id
     * @param stepGroupId    工序组id
     */
    private void checkForPlanConflict(ProductionPlan productionPlan, Integer newCategory, LocalDate planDate, Long pedigreeId, Long workLineId, Long stepGroupId) {
        //原来的生产计划粒度
        Integer originalCategory = productionPlan.getCategory();
        // 处理计划粒度变化的情况
        if (!newCategory.equals(originalCategory)) {
            checkForNewCategoryConflict(newCategory, planDate, pedigreeId, workLineId, stepGroupId);
        } else {
            checkForSameCategoryConflict(productionPlan, newCategory, planDate, pedigreeId, workLineId, stepGroupId);
        }
    }

    /**
     * 校验计划粒度和原来不一致情况
     *
     * @param newCategory 新的计划粒度
     * @param planDate    计划日期
     * @param pedigreeId  产品谱系id
     * @param workLineId  产线id
     * @param stepGroupId 工序组id
     */
    private void checkForNewCategoryConflict(Integer newCategory, LocalDate planDate, Long pedigreeId, Long workLineId, Long stepGroupId) {
        Optional<ProductionPlan> queryProductionPlanOptional = Optional.empty();
        if (ProductionPlanCategoryEnum.WORK_LINE.getCategory() == newCategory) {
            queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId, Constants.LONG_ZERO);
        }
        if (ProductionPlanCategoryEnum.STEP_GROUP.getCategory() == newCategory) {
            queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndStepGroupIdAndDeleted(planDate, pedigreeId,workLineId, stepGroupId, Constants.LONG_ZERO);
        }
        if (queryProductionPlanOptional.isPresent()) {
            throw new ResponseException("error.ProductionPlanConflict", "新的生产计划与已存在的计划冲突");
        }
    }

    /**
     * 校验计划粒度和原来一致情况
     *
     * @param productionPlan 生产计划
     * @param category       计划粒度
     * @param planDate       计划日期
     * @param pedigreeId     产品谱系id
     * @param workLineId     产线id
     * @param stepGroupId    工序组id
     */
    private void checkForSameCategoryConflict(ProductionPlan productionPlan, Integer category, LocalDate planDate, Long pedigreeId, Long workLineId, Long stepGroupId) {
        // 计划粒度为生产线
        if (ProductionPlanCategoryEnum.WORK_LINE.getCategory() == category) {
            //计划日期 产品谱系 产线 改变了
            if (!productionPlan.getPlanDate().equals(planDate) || !productionPlan.getPedigree().getId().equals(pedigreeId) || !productionPlan.getWorkLine().getId().equals(workLineId)) {
                Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId, Constants.LONG_ZERO);
                if (queryProductionPlanOptional.isPresent()) {
                    throw new ResponseException("error.ProductionPlanConflict", "新的生产计划与已存在的计划冲突");
                }
            }
        }
        // 计划粒度为工序组
        if (ProductionPlanCategoryEnum.STEP_GROUP.getCategory() == category) {
            //计划日期 产品谱系 工序组改变了
            if (!productionPlan.getPlanDate().equals(planDate) || !productionPlan.getPedigree().getId().equals(pedigreeId) || !productionPlan.getStepGroup().getId().equals(stepGroupId)) {
                Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndStepGroupIdAndDeleted(planDate, pedigreeId,workLineId, stepGroupId, Constants.LONG_ZERO);
                if (queryProductionPlanOptional.isPresent()) {
                    throw new ResponseException("error.ProductionPlanExistNotRepeat", "生产计划已存在，不能重复添加");
                }
            }
        }
    }

    /**
     * 设置生产计划
     *
     * @param productionPlan 生产计划
     * @param workLineId     生产线id
     * @param stepGroupId    工序组id
     * @param pedigreeId     产品谱系id
     */
    private void setProductionPlan(ProductionPlan productionPlan, Long workLineId, Long stepGroupId, Long pedigreeId) {
        // 根据Id获取相关实体，并进行异常处理
        WorkLine workLine = workLineId == null ? null : workLineRepository.findByIdAndDeleted(workLineId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.workLineNotFound", "生产线不存在"));
        Pedigree pedigree = pedigreeId == null ? null : pedigreeRepository.findByIdAndDeleted(pedigreeId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.pedigreeNotFound", "产品型号不存在"));
        StepGroup stepGroup = stepGroupId == null ? null : stepGroupRepository.findByIdAndDeleted(stepGroupId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.stepGroupNotFound", "工序组不存在"));
        // 设置实体
        productionPlan.setWorkLine(workLine);
        productionPlan.setStepGroup(stepGroup);
        productionPlan.setPedigree(pedigree);
        productionPlan.setStatus(Boolean.FALSE);
        productionPlanRepository.save(productionPlan);
    }

    /**
     * 生产计划导入
     *
     * @param file excel文件
     */
    public void importProductionPlanExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = parseExcel(file);
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            //excel当前数据行号
            Integer rowNumber = (Integer) row.get("excelRowNum");
            String rowNumberText = rowNumber != null ? String.valueOf(rowNumber) : "末尾";
            // 获取生产线
            String workLineCode = String.valueOf(row.get(ProductionPlanExcelConstants.WORK_LINE_CODE));
            // 将产线编码转换为对象
            WorkLine workLine = !StringUtils.hasText(workLineCode) || "null".equals(workLineCode) ? null : workLineRepository.findByCodeAndDeleted(workLineCode, Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("error.ProductionPlanErrorWorkLineNotFoundError", "原因产线不存在"));
            // 获取工序组编码并转换为对象
            String stepGroupCode = (String) row.get(ProductionPlanExcelConstants.STEP_GROUP_CODE);
            StepGroup stepGroup = !StringUtils.hasText(stepGroupCode) || "null".equals(stepGroupCode) ? null : stepGroupRepository.findByCodeAndDeleted(stepGroupCode, Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("error.ProductionPlanErrorStepGroupNotFoundError", "原因工序组别不存在"));
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(ProductionPlanExcelConstants.PEDIGREE_CODE));
            Pedigree pedigree = !StringUtils.hasText(pedigreeCode) ? null : pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException( "ProductionPlanErrorPedigreeNotFoundError","原因产品谱系不存在"));
            // 获取计划产出
            Integer planNumber = Integer.valueOf(String.valueOf(row.get(ProductionPlanExcelConstants.PLAN_NUMBER)));
            // 获取计划粒度
            String categoryName = (String) row.get(ProductionPlanExcelConstants.CATEGORY);
            // 获取计划日期
            LocalDate planDate = LocalDate.parse((String) row.get(ProductionPlanExcelConstants.PLAN_DATE), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 获取备注
            String note = (String) row.get(ProductionPlanExcelConstants.NOTE);
            // 产品谱系id
            Long pedigreeId = Optional.ofNullable(pedigree).map(AuditSfIdEntity::getId)
                    .orElseThrow(() -> new ResponseException( "ProductionPlanErrorPedigreeNotFoundError", "原因产品谱系不存在" ));
            if(!pedigree.getIsEnable()){
                throw new ResponseException("error.ProductionPlanErrorPedigreeDisabled", "导入Excel失败, 第" + rowNumberText + "行数据有误, 原因产品谱系已禁用");
            }
            // 产线id
            Long workLineId = Optional.ofNullable(workLine).map(AuditSfIdEntity::getId)
                    .orElseThrow(() -> new ResponseException( "ProductionPlanErrorWorkLineNotFoundError","原因产线不存在"));
            if(!workLine.getIsEnable()){
                throw new ResponseException("error.ProductionWorkLineDisabled", "导入Excel失败, 第" + rowNumberText + "行数据有误, 原因产线已禁用");
            }
            //计划粒度为产线
            if (ProductionPlanCategoryEnum.WORK_LINE.getName().equals(categoryName)) {
                // 查询生产计划
                Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId, Constants.LONG_ZERO);
                saveProductionPlanInfo(queryProductionPlanOptional, planNumber, planDate, note, workLine, stepGroup, pedigree, ProductionPlanCategoryEnum.WORK_LINE);
            }
            //计划粒度为工序组
            if (ProductionPlanCategoryEnum.STEP_GROUP.getName().equals(categoryName)) {
                //工序组
                Long stepGroupId = Optional.ofNullable(stepGroup).map(AuditSfIdEntity::getId)
                        .orElseThrow(() -> new ResponseException("error.ProductionPlanErrorStepGroupNotFoundError","原因工序组别不存在"));
                if(!stepGroup.getIsEnable()){
                    throw new ResponseException("error.ProductionStepGroupDisabled", "导入Excel失败, 第" + rowNumberText + "行数据有误, 原因工序组已禁用");
                }
                // 查询生产计划
                Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findByPlanDateAndPedigreeIdAndWorkLineIdAndStepGroupIdAndDeleted(planDate, pedigreeId,workLineId, stepGroupId, Constants.LONG_ZERO);
                saveProductionPlanInfo(queryProductionPlanOptional, planNumber, planDate, note, workLine, stepGroup, pedigree, ProductionPlanCategoryEnum.STEP_GROUP);
            }
        });
    }

    /**
     * 保存生产计划信息，如果存在则更新，否则创建
     *
     * @param queryProductionPlanOptional 查询到的生产计划 Optional 对象
     * @param planNumber                  计划产出
     * @param planDate                    计划日期
     * @param note                        备注
     * @param workLine                    生产线
     * @param stepGroup                   工序组别
     * @param pedigree                    产品谱系
     * @param categoryEnum                计划粒度枚举值
     */
    private void saveProductionPlanInfo(Optional<ProductionPlan> queryProductionPlanOptional, Integer planNumber, LocalDate planDate, String note, WorkLine workLine, StepGroup stepGroup, Pedigree pedigree, ProductionPlanCategoryEnum categoryEnum) {
        //如果存在则覆盖
        if (queryProductionPlanOptional.isPresent()) {
            ProductionPlan existProduction = queryProductionPlanOptional.get();
            existProduction.setPlanNumber(planNumber).setPlanDate(planDate).setNote(note);
            productionPlanRepository.save(existProduction);
        } else {
            // 不存在则创建对象并设置字段
            ProductionPlan productionPlan = new ProductionPlan();
            productionPlan.setWorkLine(workLine)
                    .setStepGroup(stepGroup)
                    .setPedigree(pedigree)
                    .setCategory(categoryEnum.getCategory())
                    .setPlanNumber(planNumber)
                    .setPlanDate(planDate)
                    .setStatus(Boolean.FALSE)
                    .setNote(note);
            productionPlanRepository.save(productionPlan);
        }
    }


    /**
     * 解析excel文件数据
     *
     * @param file excel文件
     * @return List<Map < String, Object>> excel 行数据集合
     */
    public List<Map<String, Object>> parseExcel(MultipartFile file) {
        //设置解析表头
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        List<Map<String, Object>> rowList;
        try {
            rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, importParams);
        } catch (Exception e) {
            log.error("解析excel失败 {}", e);
            throw new ResponseException("error.ParseExcelError", "解析excel失败");
        }
        //校验数据是否为空
        if (CollectionUtils.isEmpty(rowList)) {
            throw new ResponseException("error.ParseExcelEmptyError", "excel数据为空");
        }
        return rowList;
    }

    /**
     * 生产计划确认
     *
     * @param id 生产计划id
     */
    public void confirm(Long id) {
        //id和删除标记查询生产计划
        Optional<ProductionPlan> productionPlanOptional = productionPlanRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (productionPlanOptional.isEmpty()) {
            throw new ResponseException("error.ProductionPlanNotFound", "生产计划不存在");
        }
        ProductionPlan productionPlan = productionPlanOptional.get();
        // 设置状态为确认
        productionPlan.setStatus(Boolean.TRUE);
        productionPlanRepository.save(productionPlan);
    }



    /**
     * 计划粒度为生产线生产计划更新
     *
     * @param pedigreeId    产品谱系id
     * @param workLineId    产线id
     * @param actualNumber  实际产出
     * @param operationEnum 运算符号
     */
    @Klock(keys = {"#pedigreeId", "#workLineId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    @Override
    public void updateWorkLineActualNumber(Long pedigreeId, Long workLineId, Integer actualNumber, OperationEnum operationEnum) {
        LocalDate planDate = LocalDate.now();
        Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findConfirmedWorkLinePlanByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId, net.airuima.constant.Constants.LONG_ZERO);
        if (queryProductionPlanOptional.isEmpty()) {
            return;
        }
        ProductionPlan productionPlan = queryProductionPlanOptional.get();
        //设置实际产出值
        calculateActualNumber(actualNumber, operationEnum, productionPlan);
    }

    /**
     * 计划粒度为工序组生产计划更新
     *
     * @param pedigreeId    产品谱系id
     * @param stepGroupId   工序组id
     * @param actualNumber  实际产出
     * @param operationEnum 运算符号
     */
    @Klock(keys = {"#pedigreeId", "#stepGroupId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    @Override
    public void updateStepGroupIdActualNumber(Long pedigreeId, Long workLineId,Long stepGroupId, Integer actualNumber, OperationEnum operationEnum) {
        LocalDate planDate = LocalDate.now();
        Optional<ProductionPlan> queryProductionPlanOptional = productionPlanRepository.findConfirmedStepGroupPlanByPlanDateAndPedigreeIdAndWorkLineIdAndDeleted(planDate, pedigreeId, workLineId,stepGroupId, net.airuima.constant.Constants.LONG_ZERO);
        if (queryProductionPlanOptional.isEmpty()) {
            return;
        }
        ProductionPlan productionPlan = queryProductionPlanOptional.get();
        //设置实际产出值
        calculateActualNumber(actualNumber, operationEnum, productionPlan);
    }

    /**
     * 设置实际产出值
     *
     * @param actualNumber   当前产出
     * @param operationEnum  运算
     * @param productionPlan 生产计划
     */
    private void calculateActualNumber(Integer actualNumber, OperationEnum operationEnum, ProductionPlan productionPlan) {
        // 已确认才更新
        if (Boolean.TRUE.equals(productionPlan.getStatus()) && Objects.nonNull(actualNumber)) {
            Integer originActualNumber = Optional.of(productionPlan).map(ProductionPlan::getActualNumber).orElse(Constants.INT_ZERO);
            // 加
            if (OperationEnum.ADD.equals(operationEnum)) {
                productionPlan.setActualNumber(originActualNumber + actualNumber);
            }
            //减
            if (OperationEnum.MINUS.equals(operationEnum)) {
                if (originActualNumber >= actualNumber) {
                    productionPlan.setActualNumber(originActualNumber - actualNumber);
                } else {
                    productionPlan.setActualNumber(Constants.INT_ZERO);
                }
            }
            productionPlanRepository.save(productionPlan);
        }
    }

}
