package net.airuima.rbase.service.procedure.aps.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.OperationEnum;

/**
 * 更新生产计划Service
 */
@FuncDefault
public interface IProductionPlanService {


    /**
     * 计划粒度为生产线生产计划更新
     *
     * @param pedigreeId    产品谱系id
     * @param workLineId    产线id
     * @param actualNumber  实际产出
     * @param operationEnum 运算符号
     */
    @FuncInterceptor("ProductionPlan")
    default void updateWorkLineActualNumber(Long pedigreeId, Long workLineId, Integer actualNumber, OperationEnum operationEnum){

    }

    /**
     * 计划粒度为工序组生产计划更新
     *
     * @param pedigreeId    产品谱系id
     * @param stepGroupId   工序组id
     * @param actualNumber  实际产出
     * @param operationEnum 运算符号
     */
    @FuncInterceptor("ProductionPlan")
    default void updateStepGroupIdActualNumber(Long pedigreeId, Long stepGroupId, Long workLineId,Integer actualNumber, OperationEnum operationEnum) {

    }
}
