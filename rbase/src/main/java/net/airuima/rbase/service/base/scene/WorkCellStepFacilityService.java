package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCellStepFacility;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.dto.scene.WorkCellStepFacilityDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.scene.WorkCellStepFacilityRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序设备Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellStepFacilityService extends CommonJpaService<WorkCellStepFacility> {
    private static final String WORK_CELL_STEP_EQUIPMENT_ENTITY_GRAPH = "workCellStepEquipmentEntityGraph";
    private final WorkCellStepFacilityRepository workCellStepFacilityRepository;

    private final WorkCellRepository workCellRepository;

    private final StepRepository stepRepository;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;


    public WorkCellStepFacilityService(WorkCellStepFacilityRepository workCellStepFacilityRepository, WorkCellRepository workCellRepository,
                                       StepRepository stepRepository) {
        this.workCellStepFacilityRepository = workCellStepFacilityRepository;
        this.workCellRepository = workCellRepository;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkCellStepFacility> find(Specification<WorkCellStepFacility> spec, Pageable pageable) {
        return workCellStepFacilityRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_CELL_STEP_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WorkCellStepFacility> find(Specification<WorkCellStepFacility> spec) {
        return workCellStepFacilityRepository.findAll(spec, new NamedEntityGraph(WORK_CELL_STEP_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkCellStepFacility> findAll(Pageable pageable) {
        return workCellStepFacilityRepository.findAll(pageable, new NamedEntityGraph(WORK_CELL_STEP_EQUIPMENT_ENTITY_GRAPH));
    }

    /**
     * 根据工位-工序去重
     *
     * @param workCellStepFacilities 工位-工序-设备列表
     * @return 根据工位-工序去重后的数据列表
     */
    public List<WorkCellStepFacility> getUniqueWorkCellStepEquipments(List<WorkCellStepFacility> workCellStepFacilities) {
        List<WorkCellStepFacility> uniqueWorkCellStepFacilities;
        //根据数据字典配置获取工位设备关联关系是否存在工序
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_WORK_CELL_FACILITY_CONFIG,Constants.LONG_ZERO).orElse(null);
        boolean existStep = Objects.isNull(dictionaryDTO)||Objects.isNull(dictionaryDTO.getId()) ? Boolean.TRUE:Boolean.parseBoolean(dictionaryDTO.getData());
        if(existStep) {
            // 根据工位-工序分组对应设备列表
            Map<String, Set<FacilityDTO>> wsFacilityMap = workCellStepFacilities.stream().collect(Collectors.groupingBy(wse -> StringUtils.join(wse.getWorkCell().getId(), Objects.nonNull(wse.getStep())?wse.getStep().getId():StringUtils.EMPTY)
                    , Collectors.mapping(WorkCellStepFacility::getFacilityDto, Collectors.toSet())));
            // 根据工位-工序去重
            uniqueWorkCellStepFacilities = workCellStepFacilities.stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(wse -> StringUtils.join(wse.getWorkCell().getId(), Objects.nonNull(wse.getStep())?wse.getStep().getId():StringUtils.EMPTY)))), ArrayList::new))
                    .stream().sorted(Comparator.comparing(WorkCellStepFacility::getId).reversed()).toList();
            uniqueWorkCellStepFacilities.forEach(wse -> wse.setFacilityDtos(wsFacilityMap.get(StringUtils.join(wse.getWorkCell().getId(), Objects.nonNull(wse.getStep())?wse.getStep().getId():StringUtils.EMPTY))));
        }else {
            // 根据工位分组对应设备列表
            Map<Long, Set<FacilityDTO>> wsFacilityMap = workCellStepFacilities.stream().collect(Collectors.groupingBy(wse -> wse.getWorkCell().getId()
                    , Collectors.mapping(WorkCellStepFacility::getFacilityDto, Collectors.toSet())));
            uniqueWorkCellStepFacilities = workCellStepFacilities.stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(wse -> wse.getWorkCell().getId()))), ArrayList::new))
                    .stream().sorted(Comparator.comparing(WorkCellStepFacility::getId).reversed()).toList();
            uniqueWorkCellStepFacilities.forEach(wse -> wse.setFacilityDtos(wsFacilityMap.get(wse.getWorkCell().getId())));
        }
        return uniqueWorkCellStepFacilities;
    }

    /**
     * 工位工序 绑定 单个/多个设备
     *
     * @param workCellStepFacilityDto 工位工序 和 设备 绑定关系
     */
    public void bindEquipments(WorkCellStepFacilityDTO workCellStepFacilityDto) {
        List<WorkCellStepFacility> workCellStepFacilities = new ArrayList<>();
        // 先删除
        if(Objects.isNull(workCellStepFacilityDto.getStepId())){
            workCellStepFacilityRepository.deleteByWorkCellId(workCellStepFacilityDto.getWorkCellId());
        }else {
            workCellStepFacilityRepository.deleteByWorkCellIdAndStepId(workCellStepFacilityDto.getWorkCellId(), workCellStepFacilityDto.getStepId());
        }
        // 后绑定
        workCellRepository.findById(workCellStepFacilityDto.getWorkCellId()).ifPresent(workCell -> {
            if(Objects.nonNull(workCellStepFacilityDto.getStepId())) {
                stepRepository.findById(workCellStepFacilityDto.getStepId()).ifPresent(step -> {
                    if (workCellStepFacilityDto.getFacilityIds().isEmpty()) {
                        return;
                    }
                    workCellStepFacilityDto.getFacilityIds().forEach(facilityId -> {
                        workCellStepFacilities.add(new WorkCellStepFacility(workCell, step, facilityId));
                    });
                });
            }else {
                workCellStepFacilityDto.getFacilityIds().forEach(facilityId -> {
                    workCellStepFacilities.add(new WorkCellStepFacility(workCell, facilityId));
                });
            }
        });
        workCellStepFacilityRepository.saveAll(workCellStepFacilities);
    }

    /**
     * 根据工位和工序获取绑定设备列表
     *
     * @param workCellId 工位id
     * @param stepId     工序id
     * @return 设备列表
     */
    @Transactional(readOnly = true)
    public List<FacilityDTO> findByWorkCellIdAndStepId(Long workCellId, Long stepId) {
        List<WorkCellStepFacility> workCellStepFacilityList = Objects.nonNull(stepId)
                ? workCellStepFacilityRepository.findByWorkCellIdAndStepIdAndDeleted(workCellId, stepId, Constants.LONG_ZERO)
                : workCellStepFacilityRepository.findByWorkCellIdAndDeleted(workCellId,Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellStepFacilityList)) {
            return Collections.emptyList();
        }
        return workCellStepFacilityList.stream().map(WorkCellStepFacility::getFacilityDto).collect(Collectors.toList());
    }

    /**
     * 根据工位和工序获取绑定设备列表
     *
     * @param workCellId 工位id
     * @return 设备列表
     */
    @Transactional(readOnly = true)
    public List<FacilityDTO> findByWorkCellId(Long workCellId) {
        List<WorkCellStepFacility> workCellFacilityList = workCellStepFacilityRepository.findByWorkCellIdAndDeleted(workCellId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellFacilityList)) {
            return Collections.emptyList();
        }
        return workCellFacilityList.stream().map(WorkCellStepFacility::getFacilityDto).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FacilityDTO::getCode))), ArrayList::new));
    }

    /**
     * 手动分页 工序工位设备
     *
     * @param workCellStepFacilities
     * @param request
     * @return List<WorkCellStepEquipment>
     * <AUTHOR>
     * @date 2021/10/23
     */
    public List<WorkCellStepFacility> getWorkCellStepEquipmentList(List<WorkCellStepFacility> workCellStepFacilities, HttpServletRequest request) {
        int pageSize = request.getParameter("size") != null ? Integer.parseInt(request.getParameter("size")) : Constants.INT_FIVE;
        int currentPage = request.getParameter("page") != null ? Integer.parseInt(request.getParameter("page")) : Constants.INT_ZERO;
        List<WorkCellStepFacility> workCellStepFacilityList = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < workCellStepFacilities.size(); index++) {
            workCellStepFacilityList.add(workCellStepFacilities.get(index));
        }
        return workCellStepFacilityList;
    }
}
