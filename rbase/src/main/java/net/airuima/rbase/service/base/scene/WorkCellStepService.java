package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.dto.scene.WorkCellStepDTO;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellStepService extends CommonJpaService<WorkCellStep> {
    private static final String WORK_CELL_STEP_ENTITY_GRAPH = "workCellStepEntityGraph";
    private final WorkCellStepRepository workCellStepRepository;

    private final StepRepository stepRepository;

    private final WorkCellRepository workCellRepository;

    public WorkCellStepService(WorkCellStepRepository workCellStepRepository, StepRepository stepRepository, WorkCellRepository workCellRepository) {
        this.workCellStepRepository = workCellStepRepository;
        this.stepRepository = stepRepository;
        this.workCellRepository = workCellRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkCellStep> find(Specification<WorkCellStep> spec, Pageable pageable) {
        return workCellStepRepository.findAll(spec, pageable,new NamedEntityGraph(WORK_CELL_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkCellStep> find(Specification<WorkCellStep> spec) {
        return workCellStepRepository.findAll(spec,new NamedEntityGraph(WORK_CELL_STEP_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkCellStep> findAll(Pageable pageable) {
        return workCellStepRepository.findAll(pageable,new NamedEntityGraph(WORK_CELL_STEP_ENTITY_GRAPH));
    }

    /**
     * 工位新增工序，需要批量添加
     *
     * @param workCellStepDto 工位工序DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindSteps(WorkCellStepDTO workCellStepDto) {
        //如果找到则当前为编辑，需继承之前开关状态
        List<WorkCellStep> workCellStepList = workCellStepRepository.findWorkCellStepByWorkCellId(workCellStepDto.getWorkCellId());
        boolean isEnable = CollectionUtils.isEmpty(workCellStepList) ? Boolean.TRUE : workCellStepList.get(Constants.INT_ZERO).getIsEnable();
        // 先删除
        workCellStepRepository.deleteByWorkCellId(workCellStepDto.getWorkCellId());
        // 再添加
        workCellRepository.findById(workCellStepDto.getWorkCellId()).ifPresent(workCell -> {
            if (workCellStepDto.getStepIds().isEmpty()) {
                return;
            }
            List<Step> steps = stepRepository.findAllById(workCellStepDto.getStepIds());
            List<WorkCellStep> workCellSteps = steps.stream().map(s ->
                    new WorkCellStep().setWorkCell(workCell).setStep(s).setIsEnable(isEnable)
            ).collect(Collectors.toList());
            workCellStepRepository.saveAll(workCellSteps);
        });
    }

    /**
     * 根据工位去重
     *
     * @param workCellSteps 工位工序列表
     * @return 根据工位去重后工位工序列表(一对多)
     */
    public List<WorkCellStep> getUniqueWorkCellSteps(List<WorkCellStep> workCellSteps) {
        // 工位对应工序列表
        Map<WorkCell, Set<Step>> workCellStepsMap = workCellSteps.stream().collect(
                Collectors.groupingBy(WorkCellStep::getWorkCell,
                        Collectors.mapping(WorkCellStep::getStep, Collectors.toSet())));
        // 根据工位去重
        List<WorkCellStep> uniqueWorkCellSteps = workCellSteps.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WorkCellStep::getWorkCell, Comparator.comparing(WorkCell::getId)))),
                ArrayList::new)).stream().sorted(Comparator.comparing(WorkCellStep::getId).reversed()).collect(Collectors.toList());
        uniqueWorkCellSteps.forEach(w -> w.setSteps(workCellStepsMap.get(w.getWorkCell())));
        return uniqueWorkCellSteps;
    }

    /**
     * 模糊查询工位绑定的工序
     *
     * @param workCellId 工位id
     * @param keyword    工序名称或编码
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @Transactional(readOnly = true)
    public List<Step> findByWorkCellId(Long workCellId, Boolean isEnable, String keyword) {
        List<Step> stepList;
        if (StringUtils.isNotBlank(keyword)) {
            stepList =  workCellStepRepository.findByWorkCellIdAndKeyword(workCellId, keyword);
        } else {
            stepList = workCellStepRepository.findByWorkCellId(workCellId);
        }
        if(!CollectionUtils.isEmpty(stepList)){
            return null != isEnable ? stepList.stream().filter(i -> isEnable.equals(i.getIsEnable())).collect(Collectors.toList()) : stepList;
        }
        return stepList;
    }

    /**
     * 手动分页 工位工序
     * @param uniqueWorkCellSteps
     * @param request
     * <AUTHOR>
     * @date  2021/10/23
     * @return List<WorkCellStep>
     */
    public List<WorkCellStep> getWorkCellStepList(List<WorkCellStep> uniqueWorkCellSteps, HttpServletRequest request){
        int pageSize = request.getParameter("size") != null ? Integer.parseInt(request.getParameter("size")): Constants.INT_FIVE;
        int currentPage = request.getParameter("page") != null ? Integer.parseInt(request.getParameter("page")):Constants.INT_ZERO;
        List<WorkCellStep> workCellStepList = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < uniqueWorkCellSteps.size(); index++) {
            workCellStepList.add(uniqueWorkCellSteps.get(index));
        }
        return workCellStepList;
    }

    /**
     * 启用/禁用指定工位工序
     *
     * @param workCellId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByWorkCellId(Long workCellId) {
        List<WorkCellStep> workCellStepList = workCellStepRepository.findWorkCellStepByWorkCellId(workCellId);
        workCellStepList.forEach(i -> i.setIsEnable(!i.getIsEnable()));
        workCellStepRepository.saveAll(workCellStepList);
    }
}
