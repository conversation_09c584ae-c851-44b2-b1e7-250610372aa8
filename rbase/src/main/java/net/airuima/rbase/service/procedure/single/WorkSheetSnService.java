package net.airuima.rbase.service.procedure.single;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.dto.ExportDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;
import net.airuima.rbase.proxy.rmps.RbaseRmpsProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.repository.procedure.single.WorkSheetSnRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.web.rest.procedure.single.dto.WorkSheetSnImportDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单SN关联Service
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetSnService extends CommonJpaService<WorkSheetSn> {

    private static final String WORK_SHEET_SN_ENTITY_GRAPH = "workSheetSnEntityGraph";

    private final WorkSheetSnRepository workSheetSnRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private RbaseRmpsProxy rbaseRmpsProxy;

    public WorkSheetSnService(WorkSheetSnRepository workSheetSnRepository) {
        this.workSheetSnRepository = workSheetSnRepository;
    }

    @Override
    public Page<WorkSheetSn> find(Specification<WorkSheetSn> spec, Pageable pageable) {
        return workSheetSnRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_SHEET_SN_ENTITY_GRAPH));
    }

    @Override
    public List<WorkSheetSn> find(Specification<WorkSheetSn> spec) {
        return workSheetSnRepository.findAll(spec, new NamedEntityGraph(WORK_SHEET_SN_ENTITY_GRAPH));
    }

    @Override
    public Page<WorkSheetSn> findAll(Pageable pageable) {
        return workSheetSnRepository.findAll(pageable, new NamedEntityGraph(WORK_SHEET_SN_ENTITY_GRAPH));
    }

    /**
     * 修改关联的SN
     * @param id 记录ID
     * @param sn SN
     */
    public WorkSheetSn updateInstance(Long id,String sn){
        WorkSheetSn oldWorkSheetSn = workSheetSnRepository.getReferenceById(id);
        if(sn.equals(oldWorkSheetSn.getSn())){
            return oldWorkSheetSn;
        }
        WorkSheetSn workSheetSn = workSheetSnRepository.findBySnAndDeleted(sn,Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(workSheetSn) && !workSheetSn.getId().equals(id)){
            throw new ResponseException("error.snAlreadyBind", "新SN("+sn+")已经存在其他工单预绑记录!");
        }
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(sn,Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(snWorkStatus)){
            throw new ResponseException("error.snAlreadyDevoted", "新SN("+sn+")已经处于投产中!");
        }
        SnWorkStatus oldSnworkStatus = snWorkStatusRepository.findBySnAndDeleted(oldWorkSheetSn.getSn(),Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(oldSnworkStatus)){
            throw new ResponseException("error.snAlreadyDevoted", "原始SN("+oldWorkSheetSn.getSn()+")已经处于投产中!");
        }
        rbaseRmpsProxy.findSn(sn,oldWorkSheetSn.getWorkSheet().getSerialNumber());
        WorkSheet workSheet = workSheetRepository.getReferenceById(oldWorkSheetSn.getWorkSheet().getId());
        if (workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
            throw new ResponseException("error.workSheetIsFinishedOrCanceled", "工单已完成!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.workSheetIsPaused", "工单已暂停!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
            throw new ResponseException("error.workSheetIsCanceled", "工单已取消!");
        }
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(oldWorkSheetSn.getSubWorkSheet().getId());
            if (subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsFinishedOrCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已完成!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsPaused", "子工单(" + subWorkSheet.getSerialNumber() + ")已暂停!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已取消!");
            }

        }
        oldWorkSheetSn.setSn(sn).setDeleted(Constants.LONG_ZERO);
        return workSheetSnRepository.save(oldWorkSheetSn);
    }

    /**
     * 一键同步RMPS的SN到(子)工单上
     * @param workSheetId 工单Id
     * <AUTHOR>
     */
    public void sync(Long workSheetId) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(workSheet)) {
            throw new ResponseException("error.workSheetNotExist", "工单不存在!");
        }
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            throw new ResponseException("error.workSheetNotRight", "工单是非正常单,不可预绑定SN!");
        }
        if (workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
            throw new ResponseException("error.workSheetIsFinishedOrCanceled", "工单已完成!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.workSheetIsPaused", "工单已暂停!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
            throw new ResponseException("error.workSheetIsCanceled", "工单已取消!");
        }
        List<WorkSheetSn> workSheetSnList = workSheetSnRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isNotEmpty(workSheetSnList)) {
            throw new ResponseException("error.workSheetNotAllGenerateSubWs", "工单已经存在SN绑定记录，请Excel导入方式进行新增或修改!");
        }
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode) {
            this.syncSubWorkSheetSn(workSheet);
        } else {
            this.syncWorkSheetSn(workSheet);
        }
    }

    /**
     * 一键同步RMPS的SN到工单上
     * @param workSheet 工单
     * <AUTHOR>
     */
    public void syncWorkSheetSn(WorkSheet workSheet) {
        List<String> snList = rbaseRmpsProxy.findWorkSheetProcessSn(workSheet.getSerialNumber(),Boolean.FALSE);
        if (CollectionUtils.isEmpty(snList)) {
            throw new ResponseException("error.workSheetRmpsSnNotRight", "工单尚未在进行打标生成SN!");
        }
        if (snList.size() < workSheet.getNumber()) {
            throw new ResponseException("error.workSheetRmpsSnNotRight", "打标生成的SN个数不得小于工单投产数量!");
        }
        if(batchWorkDetailRepository.findTop1ByWorkSheetIdAndDeletedOrderByIdDesc(workSheet.getId(),Constants.LONG_ZERO).isPresent()){
            throw new ResponseException("error.workSheetIsCanceled", "工单已投产!");
        }
        List<WorkSheetSn> workSheetSns = workSheetSnRepository.findBySnInAndDeleted(snList,Constants.LONG_ZERO);
        if(CollectionUtils.isNotEmpty(workSheetSns)){
            String devotedSn = workSheetSns.stream().map(WorkSheetSn::getSn).collect(Collectors.joining(Constants.STR_COMMA));
            throw new ResponseException("error.workSheetSnInvalid", "SN(" + devotedSn + ")已存在预绑定记录!");
        }
        for (int index = Constants.INT_ZERO; index < workSheet.getNumber(); index++) {
            this.save(new WorkSheetSn(workSheet,null,snList.get(index)));
        }
    }

    /**
     * 一键同步RMPS的SN到各个子工单上
     * @param workSheet 工单
     * <AUTHOR>
     */
    public void syncSubWorkSheetSn(WorkSheet workSheet) {
        if (workSheet.getGenerateSubWsStatus() != Constants.INT_TWO) {
            throw new ResponseException("error.workSheetNotAllGenerateSubWs", "工单尚未全部分单,不可预绑定SN!");
        }
        List<String> snList = rbaseRmpsProxy.findWorkSheetProcessSn(workSheet.getSerialNumber(),Boolean.FALSE);
        if (CollectionUtils.isEmpty(snList)) {
            throw new ResponseException("error.workSheetRmpsSnNotRight", "工单尚未在进行打标生成SN!");
        }
        if (snList.size() < workSheet.getNumber()) {
            throw new ResponseException("error.workSheetRmpsSnNotRight", "打标生成的SN个数不得小于工单投产数量!");
        }
        List<WorkSheetSn> workSheetSns = workSheetSnRepository.findBySnInAndDeleted(snList,Constants.LONG_ZERO);
        if(CollectionUtils.isNotEmpty(workSheetSns)){
            String devotedSn = workSheetSns.stream().map(WorkSheetSn::getSn).collect(Collectors.joining(Constants.STR_COMMA));
            throw new ResponseException("error.workSheetSnInvalid", "SN(" + devotedSn + ")已存在预绑定记录!");
        }
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(subWorkSheetList)) {
            throw new ResponseException("error.workSheetNotAllGenerateSubWs", "工单尚未全部分单,不可预绑定SN!");
        }
        AtomicInteger snIndex = new AtomicInteger();
        subWorkSheetList.forEach(subWorkSheet -> {
            if (subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsFinishedOrCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已完成!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsPaused", "子工单(" + subWorkSheet.getSerialNumber() + ")已暂停!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已取消!");
            }
            if(batchWorkDetailRepository.findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(subWorkSheet.getId(),Constants.LONG_ZERO).isPresent()){
                throw new ResponseException("error.subWorkSheetIsCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已投产!");
            }
            for (int index = Constants.INT_ZERO; index < subWorkSheet.getNumber(); index++) {
                this.save(new WorkSheetSn(workSheet,subWorkSheet,snList.get(snIndex.get())));
                snIndex.getAndIncrement();
            }
        });
    }


    /**
     * 导入工单SN关联信息
     *
     * @param file 导入的Excel文件
     * <AUTHOR>
     */
    public void importWorkSheetSn(MultipartFile file) throws Exception {
        ImportParams importParams = new ImportParams();
        importParams.setTitleRows(0);
        importParams.setHeadRows(1);
        List<WorkSheetSnImportDTO> workSheetSnImportDTOList = ExcelImportUtil.importExcel(file.getInputStream(), WorkSheetSnImportDTO.class, importParams);
        if (CollectionUtils.isEmpty(workSheetSnImportDTOList)) {
            return;
        }
        Set<String> snList = workSheetSnImportDTOList.stream().map(WorkSheetSnImportDTO::getSn).collect(Collectors.toCollection(HashSet::new));
        if (snList.size() != workSheetSnImportDTOList.size()) {
            throw new ResponseException("error.existRepeatSn", "导入的Excel中存在重复的SN");
        }
        List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(snList.stream().toList(), Constants.LONG_ZERO);
        if (CollectionUtils.isNotEmpty(snWorkStatusList)) {
            String devotedSn = snWorkStatusList.stream().map(SnWorkStatus::getSn).collect(Collectors.joining(Constants.STR_COMMA));
            throw new ResponseException("error.workSheetSnInvalid", "SN(" + devotedSn + ")已经投产!");
        }
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode) {
            this.importSubWorkSheetSn(workSheetSnImportDTOList);
        } else {
            this.importWorkSheetSn(workSheetSnImportDTOList);
        }

    }

    /**
     * 导入子工单SN
     *
     * @param workSheetSnImportDTOList 导入参数
     * <AUTHOR>
     */
    public void importSubWorkSheetSn(List<WorkSheetSnImportDTO> workSheetSnImportDTOList) {
        Map<String, Set<String>> subWsSnGroup = workSheetSnImportDTOList.stream().collect(
                Collectors.groupingBy(WorkSheetSnImportDTO::getSubWsSerialNumber, Collectors.mapping(WorkSheetSnImportDTO::getSn, Collectors.toSet())));
        Map<String, Set<String>> workSheetGroup = workSheetSnImportDTOList.stream().collect(
                Collectors.groupingBy(WorkSheetSnImportDTO::getWsSerialNumber, Collectors.mapping(WorkSheetSnImportDTO::getSubWsSerialNumber, Collectors.toSet())));
        boolean rmpsExist = FuncKeyUtil.checkApi("RmpsService");
        Map<Long, List<String>> wsSnMap = new HashMap<>();
        workSheetGroup.forEach((wsSerialNumber, subWsSerialNumbers) -> {
            WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(wsSerialNumber, Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(workSheet)) {
                throw new ResponseException("error.workSheetNotExist", "工单(" + wsSerialNumber + ")不存在!");
            }
            if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
                throw new ResponseException("error.workSheetNotRight", "工单(" + wsSerialNumber + ")非正常单,不可导入!");
            }
            if (rmpsExist) {
                List<String> snList = rbaseRmpsProxy.findWorkSheetProcessSn(wsSerialNumber,Boolean.FALSE);
                if (CollectionUtils.isEmpty(snList)) {
                    throw new ResponseException("error.workSheetRmpsSnNotRight", "工单(" + wsSerialNumber + ")尚未在进行打标生成SN!");
                }
                wsSnMap.put(workSheet.getId(), snList);
            }
        });
        subWsSnGroup.forEach((subWsSerialNumber, sns) -> {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(subWsSerialNumber, Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(subWorkSheet)) {
                throw new ResponseException("error.subWorkSheetNotExist", "子工单(" + subWsSerialNumber + ")不存在!");
            }
            if (!workSheetGroup.containsKey(subWorkSheet.getWorkSheet().getSerialNumber())) {
                throw new ResponseException("error.subWorkSheetNotMatchedWorkSheet", "子工单(" + subWsSerialNumber + ")在导入的Excel中没有对应的工单号!");
            }
            if (!workSheetGroup.get(subWorkSheet.getWorkSheet().getSerialNumber()).contains(subWsSerialNumber)) {
                throw new ResponseException("error.subWorkSheetNotMatchedWorkSheet", "子工单(" + subWsSerialNumber + ")在导入的Excel中对应的工单号不正确!");
            }
            if (subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsFinishedOrCanceled", "子工单(" + subWsSerialNumber + ")已完成!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsPaused", "子工单(" + subWsSerialNumber + ")已暂停!");
            }
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException("error.subWorkSheetIsCanceled", "子工单(" + subWsSerialNumber + ")已取消!");
            }
            List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(),Constants.LONG_ZERO);
            if(CollectionUtils.isNotEmpty(snWorkStatusList)){
                throw new ResponseException("error.subWorkSheetIsCanceled", "子工单(" + subWorkSheet.getSerialNumber() + ")已投产!");
            }
            BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(subWorkSheet.getId(),Constants.LONG_ZERO).orElse(null);
            if(Objects.nonNull(batchWorkDetail) && batchWorkDetail.getFinish()!=Constants.INT_ONE){
                throw new ResponseException("error.workSheetSnNumberInvalid", "工序(" + batchWorkDetail.getStep().getName() + ")尚未完成,请完成后再导入!");
            }
            if(Objects.nonNull(batchWorkDetail) && batchWorkDetail.getTransferNumber()!=sns.size()){
                throw new ResponseException("error.workSheetSnNumberInvalid", "工单(" + subWsSerialNumber + ")最新待投产工序数量与导入的SN个数不相等!");
            } else if (Objects.isNull(batchWorkDetail) && sns.size() != subWorkSheet.getNumber()) {
                throw new ResponseException("error.subWorkSheetSnNumberInvalid", "子工单(" + subWsSerialNumber + ")投产数与导入的SN个数不相等!");
            }
            if (rmpsExist) {
                List<String> notMatchedRmpsSns = sns.stream().filter(s -> !wsSnMap.get(subWorkSheet.getWorkSheet().getId()).contains(s)).toList();
                if (CollectionUtils.isNotEmpty(notMatchedRmpsSns)) {
                    String notMatchedRmpsSn = String.join(Constants.STR_COMMA, notMatchedRmpsSns);
                    throw new ResponseException("error.workSheetRmpsSnInvalid", "SN(" + notMatchedRmpsSn + ")在工单(" + subWorkSheet.getWorkSheet().getSerialNumber() + ")打标生成的SN中不存在!");
                }
            }
            List<WorkSheetSn> workSheetSnList = workSheetSnRepository.findBySnInAndSubWorkSheetIdNotAndDeleted(sns.stream().toList(), subWorkSheet.getId(), Constants.LONG_ZERO);
            if (CollectionUtils.isNotEmpty(workSheetSnList)) {
                String devotedSn = workSheetSnList.stream().map(WorkSheetSn::getSn).collect(Collectors.joining(Constants.STR_COMMA));
                throw new ResponseException("error.workSheetSnInvalid", "SN(" + devotedSn + ")已经关联其他子工单!");
            }
            workSheetSnRepository.deleteAllBySubWorkSheetId(subWorkSheet.getId());
            List<WorkSheetSn> workSheetSns = new ArrayList<>();
            sns.forEach(sn -> workSheetSns.add(new WorkSheetSn(subWorkSheet.getWorkSheet(), subWorkSheet, sn)));
            this.save(workSheetSns);
        });
    }

    /**
     * 导入工单SN
     *
     * @param workSheetSnImportDTOList 导入参数
     * <AUTHOR>
     */
    public void importWorkSheetSn(List<WorkSheetSnImportDTO> workSheetSnImportDTOList) {
        Map<String, Set<String>> wsSnGroup = workSheetSnImportDTOList.stream().collect(
                Collectors.groupingBy(WorkSheetSnImportDTO::getWsSerialNumber, Collectors.mapping(WorkSheetSnImportDTO::getSn, Collectors.toSet())));
        boolean rmpsExist = FuncKeyUtil.checkApi("RmpsService");
        wsSnGroup.forEach((wsSerialNumber, sns) -> {
            WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(wsSerialNumber, Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(workSheet)) {
                throw new ResponseException("error.workSheetNotExist", "工单(" + wsSerialNumber + ")不存在!");
            }
            if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
                throw new ResponseException("error.workSheetNotRight", "工单(" + wsSerialNumber + ")非正常单,不可导入!");
            }
            if (workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                throw new ResponseException("error.workSheetIsFinishedOrCanceled", "工单(" + wsSerialNumber + ")已完成!");
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
                throw new ResponseException("error.workSheetIsPaused", "工单(" + wsSerialNumber + ")已暂停!");
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException("error.workSheetIsCanceled", "工单(" + wsSerialNumber + ")已取消!");
            }
            List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findByWorkSheetIdAndDeleted(workSheet.getId(),Constants.LONG_ZERO);
            if(CollectionUtils.isNotEmpty(snWorkStatusList)){
                throw new ResponseException("error.workSheetIsCanceled", "工单已投产!");
            }
            BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findTop1ByWorkSheetIdAndDeletedOrderByIdDesc(workSheet.getId(),Constants.LONG_ZERO).orElse(null);
            if(Objects.nonNull(batchWorkDetail) && batchWorkDetail.getFinish()!=Constants.INT_ONE){
                throw new ResponseException("error.workSheetSnNumberInvalid", "工序(" + batchWorkDetail.getStep().getName() + ")尚未完成,请完成后再导入!");
            }
            if(Objects.nonNull(batchWorkDetail) && batchWorkDetail.getTransferNumber()!=sns.size()){
                throw new ResponseException("error.workSheetSnNumberInvalid", "工单(" + wsSerialNumber + ")最新待投产工序数数量与导入的SN个数不相等!");
            } else if (Objects.isNull(batchWorkDetail) && sns.size() != workSheet.getNumber()) {
                throw new ResponseException("error.workSheetSnNumberInvalid", "工单(" + wsSerialNumber + ")投产数与导入的SN个数不相等!");
            }
            if (rmpsExist) {
                List<String> snList = rbaseRmpsProxy.findWorkSheetProcessSn(workSheet.getSerialNumber(),Boolean.FALSE);
                if (CollectionUtils.isEmpty(snList)) {
                    throw new ResponseException("error.workSheetRmpsSnNotRight", "工单(" + wsSerialNumber + ")尚未在进行打标生成SN!");
                }
                List<String> notMatchedRmpsSns = sns.stream().filter(s -> !snList.contains(s)).toList();
                if (CollectionUtils.isNotEmpty(notMatchedRmpsSns)) {
                    String notMatchedRmpsSn = String.join(Constants.STR_COMMA, notMatchedRmpsSns);
                    throw new ResponseException("error.workSheetRmpsSnInvalid", "SN(" + notMatchedRmpsSn + ")在工单(" + wsSerialNumber + ")打标生成的SN中不存在!");
                }
            }
            List<WorkSheetSn> workSheetSnList = workSheetSnRepository.findBySnInAndWorkSheetIdNotAndDeleted(sns.stream().toList(), workSheet.getId(), Constants.LONG_ZERO);
            if (CollectionUtils.isNotEmpty(workSheetSnList)) {
                String devotedSn = workSheetSnList.stream().map(WorkSheetSn::getSn).collect(Collectors.joining(Constants.STR_COMMA));
                throw new ResponseException("error.workSheetSnInvalid", "SN(" + devotedSn + ")已经关联其他工单!");
            }
            workSheetSnRepository.deleteAllByWorkSheetId(workSheet.getId());
            List<WorkSheetSn> workSheetSns = new ArrayList<>();
            sns.forEach(sn -> workSheetSns.add(new WorkSheetSn(workSheet, null, sn)));
            this.save(workSheetSns);
        });
    }


    /**
     * 导出工单关联SN信息
     *
     * @param workSheetSnList
     * @param exportDTO
     * @param response
     * @throws IOException
     */
    public void exportExcel(List<WorkSheetSn> workSheetSnList, ExportDTO exportDTO, HttpServletResponse response) throws IOException {
        List<WorkSheetSnImportDTO> workSheetSnImportDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(workSheetSnList)) {
            workSheetSnList.forEach(workSheetSn -> workSheetSnImportDTOList.add(new WorkSheetSnImportDTO(workSheetSn)));
        }
        ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
        if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
            exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
        }
        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, WorkSheetSnImportDTO.class, workSheetSnImportDTOList);
        String fileName = URLEncoder.encode(exportDTO.getExcelTitle() + prefix, StandardCharsets.UTF_8);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.close();
    }
}
