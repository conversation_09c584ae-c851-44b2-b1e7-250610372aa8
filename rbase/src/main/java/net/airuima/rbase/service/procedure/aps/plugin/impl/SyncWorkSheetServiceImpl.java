package net.airuima.rbase.service.procedure.aps.plugin.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.organization.RbaseOrganizationProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeWorkFlowRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.service.procedure.aps.dto.WorkSheetSyncDTO;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetSyncService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.BeanUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工单同步实现
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SyncWorkSheetServiceImpl implements IWorkSheetSyncService {

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private WorkSheetService workSheetService;

    @Autowired
    private RbaseOrganizationProxy rbaseOrganizationProxy;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private PedigreeWorkFlowRepository pedigreeWorkFlowRepository;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;

    /**
     * 工单同步 保存工单
     *
     * @param workSheetSyncDto 更新同步参数
     * @return void
     * <AUTHOR>
     * @date 2023/3/31
     */
    @Override
    public void saveSyncWorkSheet(WorkSheetSyncDTO workSheetSyncDto) {
        this.saveWorkSheet(workSheetSyncDto);
    }

    /**
     * 工单同步 更新工单
     *
     * @param workSheetSyncDto 更新同步参数
     * @return void
     * <AUTHOR>
     * @date 2023/3/31
     */
    @Override
    public void updateSyncWorkSheet(WorkSheetSyncDTO workSheetSyncDto) {
        WorkSheet workSheet = workSheetSyncDto.getWorkSheet();
        SyncWorkSheetDTO syncWorkSheetDto = workSheetSyncDto.getSyncWorkSheetDto();
        //删除工单同时删除子工单
        if (syncWorkSheetDto.getOperate() == Constants.INT_TWO) {
            subWorkSheetRepository.batchDeleteByWorkSheetId(workSheet.getId());
            workSheet.setDeleted(workSheet.getId());
            workSheetRepository.save(workSheet);
            //删除销售订单投产数
            SaleOrder saleOrder = workSheet.getSaleOrder();
            if (!ObjectUtils.isEmpty(saleOrder)) {
                saleOrder.setProductionQuantity(saleOrder.getProductionQuantity() - workSheet.getNumber());
                saleOrderRepository.save(saleOrder);
            }
        }
        //删除领料单
        wsMaterialRepository.deleteByWorkSheetId(workSheet.getId());
        //修改工单基础信息
        if (syncWorkSheetDto.getOperate() == Constants.INT_ONE) {
            this.saveWorkSheet(workSheetSyncDto);
        }
    }

    /**
     * 同步工单保存
     *
     * @param workSheetSyncDto 工单同步参数
     * <AUTHOR>
     * @date 2023/3/31
     */
    @Override
    public void saveWorkSheet(WorkSheetSyncDTO workSheetSyncDto) {

        WorkSheet workSheet = workSheetSyncDto.getWorkSheet();
        SyncWorkSheetDTO syncWorkSheetDto = workSheetSyncDto.getSyncWorkSheetDto();

        //获取销售订单号
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(syncWorkSheetDto.getSaleOrderSerialNumber(), Constants.LONG_ZERO);

        //更新销售订单投产数
        saleOrderOptional.ifPresent(saleOrder -> {
            if (null != workSheet.getSaleOrder() && saleOrder.getSerialNumber().equals(workSheet.getSaleOrder().getSerialNumber())) {
                saleOrder.setProductionQuantity(saleOrder.getProductionQuantity() - workSheet.getNumber());
            }
            saleOrder.setProductionQuantity(saleOrder.getProductionQuantity() + syncWorkSheetDto.getNumber());
            saleOrderRepository.save(saleOrder);
        });

        //保存工单
        workSheet.setPlanStartDate(syncWorkSheetDto.getPlanStartDate())
                .setPlanEndDate(syncWorkSheetDto.getPlanEndDate())
                .setPedigree(workSheetSyncDto.getPedigree())
                .setOrganizationId(workSheetSyncDto.getOrganizationId())
                .setBomInfoId(workSheetSyncDto.getBomInfoId())
                .setSerialNumber(syncWorkSheetDto.getSerialNumber())
                .setNumber(syncWorkSheetDto.getNumber())
                .setCategory(syncWorkSheetDto.getCategory())
                .setSaleOrder(saleOrderOptional.orElse(null))
                .setWorkLine(Objects.nonNull(workSheetSyncDto.getWorkLine())?workSheetSyncDto.getWorkLine(): workSheet.getWorkLine())
                .setWorkFlow(Objects.nonNull(workSheetSyncDto.getWorkFlow())?workSheetSyncDto.getWorkFlow(): workSheet.getWorkFlow())
                .setDeleted(Constants.LONG_ZERO);
        this.copyCustomData(workSheet, syncWorkSheetDto);
        // 如果存在客户编码 验证客户是否存在
        if(StringUtils.isNotBlank(syncWorkSheetDto.getClientCode())){
            //获取客户 如果存在 设置工单的客户信息
            ClientDTO clientDto = rbaseClientProxy.findByCodeAndDeleted(syncWorkSheetDto.getClientCode(),Constants.LONG_ZERO);
            if(Objects.nonNull(clientDto) && Objects.nonNull(clientDto.getId())){
                workSheet.setClientId(clientDto.getId());
            }
        }
        if (ValidateUtils.isValid(workSheetSyncDto.getWorkFlowStepList())) {
            List<WsStep> wsSteps = Lists.newArrayList();
            workSheetSyncDto.getWorkFlowStepList().forEach(workFlowStep -> {
                StepDTO stepConfigDto = commonService.findPedigreeStepConfig(workSheet.getClientId(),workSheetSyncDto.getPedigree(), workSheetSyncDto.getWorkFlow(), workFlowStep.getStep());
                WsStep wsStep = new WsStep();
                wsStep.setStep(workFlowStep.getStep())
                        .setAfterStepId(workFlowStep.getAfterStepId())
                        .setPreStepId(workFlowStep.getPreStepId())
                        .setCategory(stepConfigDto.getCategory())
                        .setControlMode(stepConfigDto.getControlMode())
                        .setIsBindContainer(stepConfigDto.getIsBindContainer())
                        .setIsControlMaterial(stepConfigDto.getIsControlMaterial())
                        .setRequestMode(stepConfigDto.getRequestMode())
                        .setInputRate(stepConfigDto.getInputRate())
                        .setWorkSheet(workSheet);
                wsSteps.add(wsStep);
            });
            wsStepRepository.deleteByWorkSheetId(workSheet.getId());
            wsStepRepository.saveAll(wsSteps);
            //工单投产粒度时需要更新工单的工序个数
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            if (!subWsProductionMode && !CollectionUtils.isEmpty(wsSteps)) {
                workSheet.setStepNumber(wsSteps.size());
            }
        }

        WorkSheet finalWorkSheet = workSheetRepository.save(workSheet);
        //优先使用erp提交上来的物料清单，如果不存在则使用bom的物料清单(这样可提前做的修改工单投料单)
        if (ValidateUtils.isValid(syncWorkSheetDto.getWsMaterialDtoList())) {
            workSheetService.saveErpWsMaterial(finalWorkSheet, syncWorkSheetDto.getWsMaterialDtoList());
        } else if (null != workSheetSyncDto.getBomInfoId()) {
            List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(workSheetSyncDto.getBomInfoId());
            wsMaterialServices[0].saveWsMaterial(finalWorkSheet, bomDtoList);
        }

    }


    /**
     * 复制自定义字段
     *
     * @param workSheet 工单
     * @param syncWorkSheetDto 同步DTO
     */
    private void copyCustomData(WorkSheet workSheet, SyncWorkSheetDTO syncWorkSheetDto) {
        workSheet.setCustom1(syncWorkSheetDto.getCustom1());
        workSheet.setCustom2(syncWorkSheetDto.getCustom2());
        workSheet.setCustom3(syncWorkSheetDto.getCustom3());
        workSheet.setCustom4(syncWorkSheetDto.getCustom4());
        workSheet.setCustom5(syncWorkSheetDto.getCustom5());
    }


    /**
     * 生产工单同步
     *
     * @param syncWorkSheetDtoList 上传的需要同步的工单列表
     * @return java.util.List<net.airuima.rbase.dto.sync.SyncResultDTO> 同步结果信息列表
     * <AUTHOR>
     * @date 2021-06-03
     **/
    @Override
    public List<SyncResultDTO> syncWorkSheet(List<SyncWorkSheetDTO> syncWorkSheetDtoList) {
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncWorkSheetDtoList)) {
            syncWorkSheetDtoList.forEach(syncWorkSheetDto -> {
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(syncWorkSheetDto.getSerialNumber(), Constants.LONG_ZERO);
                OrganizationDTO organizationDto = rbaseOrganizationProxy.findByCodeAndDeleted(syncWorkSheetDto.getOrganizationCode(),Constants.LONG_ZERO).orElse(null);
                BomInfoDTO bomInfoDto = rbaseBomProxy.findByCode(syncWorkSheetDto.getBomCode(),null);
                MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(syncWorkSheetDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
                //其次验证工单的BOM是否存在
                if (null == bomInfoDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的BOM不存在"));
                    return;
                }
                //继续验证工单对应的物料主件是否存在
                if (null == materialDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的主键物料不存在"));
                    return;
                }
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByMaterialIdAndDeleted(materialDto.getId(), Constants.LONG_ZERO);
                //继续验证工单的产品谱系是否存在
                if (pedigreeOptional.isEmpty()) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单产品谱系不存在"));
                    return;
                }
                //继续验证工单的组织架构是否存在
                if (null == organizationDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的组织架构不存在"));
                    return;
                }
                WorkSheetSyncDTO workSheetSyncDTO = new WorkSheetSyncDTO(syncWorkSheetDto, new WorkSheet(), pedigreeOptional.get(), bomInfoDto.getId(), organizationDto.getId());
                // 验证产线是否存在
                if (StringUtils.isNoneBlank(syncWorkSheetDto.getWorkLineCode())) {
                    WorkLine workLine = workLineRepository.findByCodeAndDeleted(syncWorkSheetDto.getWorkLineCode(), Constants.LONG_ZERO).orElse(null);
                    if (workLine == null) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的生产线" + syncWorkSheetDto.getWorkLineCode() + "不存在"));
                        return;
                    }
                    workSheetSyncDTO.setWorkLine(workLine);
                }
                // 验证工艺路线是否存在
                if (StringUtils.isNoneBlank(syncWorkSheetDto.getWorkFlowCode())) {
                    WorkFlow workFlow = workFlowRepository.findByCode(syncWorkSheetDto.getWorkFlowCode()).orElse(null);
                    if (workFlow == null) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的工艺路线" + syncWorkSheetDto.getWorkFlowCode() + "不存在"));
                        return;
                    }
                    workSheetSyncDTO.setWorkFlow(workFlow);
                    // 验证工艺路线工序，以及工序配置
                    List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlow.getId(), Constants.LONG_ZERO);
                    if (!ValidateUtils.isValid(workFlowStepList)) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单的工艺路线工序" + syncWorkSheetDto.getWorkFlowCode() + "不存在"));
                        return;
                    }
                    for (WorkFlowStep workFlowStep : workFlowStepList) {
                        Step step = stepRepository.findByIdAndDeleted(workFlowStep.getStep().getId(), Constants.LONG_ZERO).orElse(null);
                        if (step == null) {
                            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "不存在工序(" + workFlowStep.getStep().getId() + ")"));
                            return;
                        }
                        StepDTO stepConfigDto = commonService.findPedigreeStepConfig(null,pedigreeOptional.get(), workFlow, step);
                        if (null == stepConfigDto) {
                            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "不存在工序(" + step.getCode() + ")配置"));
                            return;
                        }
                        if (stepConfigDto.getInputRate() == Constants.DOUBLE_ZERRO) {
                            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "工序(" + step.getCode() + ")配置的投产比例为0"));
                            return;
                        }
                    }
                    workSheetSyncDTO.setWorkFlowStepList(workFlowStepList);
                }
                //同步类型(0:新增;1:修改;2:删除;3:退料结单)
                //新增工单时需要进行一系列验证
                if (syncWorkSheetDto.getOperate() == Constants.INT_ZERO) {
                    //首先验证工单号是否存在
                    if (workSheetOptional.isPresent()) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单记录已存在"));
                        return;
                    }
                    //保存SAP新增的工单信息以及保存投料单信息
                    BeanUtil.getHighestPrecedenceBean(IWorkSheetSyncService.class).saveSyncWorkSheet(workSheetSyncDTO);
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_ONE, ""));
                    return;
                }
                //同步非新增的工单时需要一系列的验证
                if (syncWorkSheetDto.getOperate() != Constants.INT_ZERO) {
                    //首先验证工单号是否存在
                    if (workSheetOptional.isEmpty()) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单记录不存在"));
                        return;
                    }
                    workSheetSyncDTO.setWorkSheet(workSheetOptional.get());
                    updateWorkSheetInfo(syncResultDtoList, syncWorkSheetDto, workSheetOptional, workSheetSyncDTO);
                }
            });
        }
        return syncResultDtoList;
    }

    /**
     * 同步非新增的工单时需要一系列的验证
     *
     * @param syncResultDtoList 同步结果信息DTO列表
     * @param syncWorkSheetDto  同步工单信息
     * @param workSheetOptional 工单
     * @param workSheetSyncDTO
     */
    private void updateWorkSheetInfo(List<SyncResultDTO> syncResultDtoList, SyncWorkSheetDTO syncWorkSheetDto, Optional<WorkSheet> workSheetOptional, WorkSheetSyncDTO workSheetSyncDTO) {
        //首先验证工单是否存在
        if (workSheetOptional.isEmpty()) {
            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单记录不存在"));
            return;
        }
        List<WsWorkCellMaterialBatch> workCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndLeftNumberGreaterThanAndDeleted(workSheetOptional.get().getId(), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
        List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndLeftNumberIsGreaterThanAndDeleted(workSheetOptional.get().getId(), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
        //对于修改和删除工单同步继续验证是否已领料以及是否已开工
        if (syncWorkSheetDto.getOperate() == Constants.INT_ONE || syncWorkSheetDto.getOperate() == Constants.INT_TWO) {
            if (ValidateUtils.isValid(wsMaterialBatchList) || ValidateUtils.isValid(workCellMaterialBatchList)) {
                syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单已领料"));
                return;
            }
            if (null != workSheetOptional.get().getActualStartDate()) {
                syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单已投产"));
                return;
            }
            //修改和删除工单信息
            BeanUtil.getHighestPrecedenceBean(IWorkSheetSyncService.class).updateSyncWorkSheet(workSheetSyncDTO);
            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_ONE, ""));
            return;
        }
        //对于退料结单的同步需要验证工单线上库存是否为0
        if (syncWorkSheetDto.getOperate() == Constants.INT_THREE) {
            if (ValidateUtils.isValid(wsMaterialBatchList) || ValidateUtils.isValid(workCellMaterialBatchList)) {
                syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_TWO, "当前工单线上物料库存不为0"));
                return;
            }
            //SAP退料结单更新总工单以及子工单信息
            workSheetService.finishWorkSheetWithReturnMaterial(workSheetOptional.get());
            syncResultDtoList.add(new SyncResultDTO(syncWorkSheetDto.getId(), Constants.INT_ONE, ""));
        }
    }
}
