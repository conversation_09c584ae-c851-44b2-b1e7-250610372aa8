package net.airuima.rbase.service.procedure.quality;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.SnStepFppHistory;
import net.airuima.rbase.repository.procedure.quality.SnStepFppHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 单支FPP历史Service
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnStepFppHistoryService extends CommonJpaService<SnStepFppHistory> {

    private final SnStepFppHistoryRepository snStepFppHistoryRepository;

    public SnStepFppHistoryService(SnStepFppHistoryRepository snStepFppHistoryRepository) {
        this.snStepFppHistoryRepository = snStepFppHistoryRepository;
    }

    @Override
    @FetchMethod
    public Page<SnStepFppHistory> find(Specification<SnStepFppHistory> spec, Pageable pageable) {
        return snStepFppHistoryRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<SnStepFppHistory> find(Specification<SnStepFppHistory> spec) {
        return snStepFppHistoryRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<SnStepFppHistory> findAll(Pageable pageable) {
        return snStepFppHistoryRepository.findAll(pageable);
    }
}
