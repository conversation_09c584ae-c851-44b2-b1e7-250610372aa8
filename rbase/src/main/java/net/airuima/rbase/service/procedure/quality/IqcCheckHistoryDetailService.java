package net.airuima.rbase.service.procedure.quality;

import net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail;
import net.airuima.rbase.repository.procedure.quality.IqcCheckHistoryDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验详情Service
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class IqcCheckHistoryDetailService extends CommonJpaService<IqcCheckHistoryDetail> {

    private final IqcCheckHistoryDetailRepository iqcCheckHistoryDetailRepository;

    public IqcCheckHistoryDetailService(IqcCheckHistoryDetailRepository iqcCheckHistoryDetailRepository) {
        this.iqcCheckHistoryDetailRepository = iqcCheckHistoryDetailRepository;
    }


    @Override
    @Transactional(readOnly = true)
    public Page<IqcCheckHistoryDetail> find(Specification<IqcCheckHistoryDetail> spec, Pageable pageable) {
        return iqcCheckHistoryDetailRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IqcCheckHistoryDetail> find(Specification<IqcCheckHistoryDetail> spec) {
        return iqcCheckHistoryDetailRepository.findAll(spec);
    }


    /**
     * 查询所有
     * @param pageable 分页
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public Page<IqcCheckHistoryDetail> findAll(Pageable pageable) {
        return iqcCheckHistoryDetailRepository.findAll(pageable);
    }



}
