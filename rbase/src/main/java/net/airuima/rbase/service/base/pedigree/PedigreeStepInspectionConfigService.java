package net.airuima.rbase.service.base.pedigree;

import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepInspectionConfig;
import net.airuima.rbase.dto.pedigree.PedigreeStepInspectionConfigDTO;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepInspectionConfigRepository;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工序检查配置Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepInspectionConfigService extends CommonJpaService<PedigreeStepInspectionConfig> {

    private final PedigreeStepInspectionConfigRepository pedigreeStepInspectionConfigRepository;

    private static Logger log = LoggerFactory.getLogger(PedigreeStepInspectionConfigService.class);

    public PedigreeStepInspectionConfigService(
            PedigreeStepInspectionConfigRepository pedigreeStepInspectionConfigRepository) {
        this.pedigreeStepInspectionConfigRepository = pedigreeStepInspectionConfigRepository;
    }

    @Override
    public Page<PedigreeStepInspectionConfig> find(Specification<PedigreeStepInspectionConfig> spec,
                                                   Pageable pageable) {
        return pedigreeStepInspectionConfigRepository.findAll(spec, pageable);
    }

    @Override
    public List<PedigreeStepInspectionConfig> find(Specification<PedigreeStepInspectionConfig> spec) {
        return pedigreeStepInspectionConfigRepository.findAll(spec);
    }

    @Override
    public Page<PedigreeStepInspectionConfig> findAll(Pageable pageable) {
        return pedigreeStepInspectionConfigRepository.findAll(pageable);
    }

    /**
     * 保存所有工序检查配置
     *
     * @param pedigreeStep      工序
     * @param inspectionConfigs 工序检查配置列表
     * <AUTHOR>
     * @since 1.8.1
     */
    public void saveAllPedigreeStepInspectionConfig(PedigreeStep pedigreeStep,
                                                    List<PedigreeStepInspectionConfigDTO> inspectionConfigs) {
        if (!ValidateUtils.isValid(inspectionConfigs)) {
            // 删除工序检查配置
            pedigreeStepInspectionConfigRepository.deleteByPedigreeStepId(pedigreeStep.getId());
            return;
        }
        // 验证同一个工序配置不能存在相同的检查类型
        inspectionConfigs.stream()
                .collect(Collectors.groupingBy(PedigreeStepInspectionConfigDTO::getInspectionType))
                .forEach((inspectionType, entitys) -> {
                    if (entitys.size() > Constants.INT_ONE) {
                        throw new ResponseException("pedigreeStepInspectionTypeUnique", "同一个工序配置不能存在相同的检查类型");
                    }
                });
        // 删除工序检查配置
        pedigreeStepInspectionConfigRepository.deleteByPedigreeStepId(pedigreeStep.getId());

        // 保存更新的工序检查配置
        List<PedigreeStepInspectionConfig> pedigreeStepInspectionConfigs = inspectionConfigs.stream().map(entity -> {
            PedigreeStepInspectionConfig pedigreeStepInspectionConfig = new PedigreeStepInspectionConfig();
            pedigreeStepInspectionConfig.setPedigreeStep(pedigreeStep)
                    .setInspectionType(entity.getInspectionType())
                    .setOverrideStrategy(entity.getOverrideStrategy())
                    .setMaxNgCount(entity.getMaxNgCount())
                    .setHandlingType(entity.getHandlingType()).setDeleted(Constants.LONG_ZERO);
            return pedigreeStepInspectionConfig;
        }).collect(Collectors.toList());
        pedigreeStepInspectionConfigRepository.saveAll(pedigreeStepInspectionConfigs);
    }

    /**
     * 逻辑删除工序检查配置
     *
     * @param pedigreeStepId 工序配置id
     * <AUTHOR>
     * @since 1.8.1
     */
    public void logicDeletedByPedigreeStepId(Long pedigreeStepId) {
        pedigreeStepInspectionConfigRepository.logicDeleteByPedigreeStepId(pedigreeStepId);
    }
}
