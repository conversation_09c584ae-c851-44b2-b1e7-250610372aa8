package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.single.*;
import net.airuima.rbase.repository.procedure.wearingpart.SnWorkDetailWearingPartRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.MaterialBatchDTO;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支工序生产详情Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnWorkDetailService extends CommonJpaService<SnWorkDetail> {

    private static final String ERR_MSG = "sn已生成在线返修单不可回退";
    private static final String REWORK_IS_EXIST = "reworkIsExist";
    private static final String SN_WORK_DETAIL_ENTITY_GRAPH = "snWorkDetailEntityGraph";

    private final SnWorkDetailRepository snWorkDetailRepository;

    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private SnReworkRepository snReworkRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private BatchWorkDetailService batchWorkDetailService;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailWearingPartRepository snWorkDetailWearingPartRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IRollbackStepService[] iRollbackStepServices;

    public SnWorkDetailService(SnWorkDetailRepository snWorkDetailRepository) {
        this.snWorkDetailRepository = snWorkDetailRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<SnWorkDetail> find(Specification<SnWorkDetail> spec, Pageable pageable) {
        return snWorkDetailRepository.findAll(spec, pageable, new NamedEntityGraph(SN_WORK_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<SnWorkDetail> find(Specification<SnWorkDetail> spec) {
        return snWorkDetailRepository.findAll(spec, new NamedEntityGraph(SN_WORK_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<SnWorkDetail> findAll(Pageable pageable) {
        return snWorkDetailRepository.findAll(pageable, new NamedEntityGraph(SN_WORK_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 回退sn，修改批次详情信息
     *
     * @param batchWorkDetail 批量详情记录
     * @param snWorkDetail    sn详情记录
     * @return BatchWorkDetail
     * <AUTHOR>
     * @date 2022/11/18
     */
    @Klock(keys = {"#productWorkSheetId", "#stepId"}, waitTime = 300, leaseTime = 120, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BatchWorkDetail updateSnByBatchWorkDetail(long productWorkSheetId,long stepId,BatchWorkDetail batchWorkDetail, SnWorkDetail snWorkDetail,List<WsStep> childWsStepList) {
        batchWorkDetail.setInputNumber(batchWorkDetail.getInputNumber() - net.airuima.constant.Constants.INT_ONE)
                .setEffectNumber(batchWorkDetail.getEffectNumber() - net.airuima.constant.Constants.INT_ONE)
                .setFinishNumber(batchWorkDetail.getFinishNumber() - net.airuima.constant.Constants.INT_ONE);
        if (snWorkDetail.getResult() == net.airuima.constant.Constants.INT_ZERO) {
            batchWorkDetail.setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() - net.airuima.constant.Constants.INT_ONE);
        } else {
            batchWorkDetail.setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - net.airuima.constant.Constants.INT_ONE)
                    .setTransferNumber(batchWorkDetail.getTransferNumber() - net.airuima.constant.Constants.INT_ONE);
        }
        batchWorkDetail.setFinish(net.airuima.constant.Constants.INT_ZERO).setEndDate(null);
        if (batchWorkDetail.getInputNumber() == net.airuima.constant.Constants.INT_ZERO) {
            batchWorkDetail.setDeleted(batchWorkDetail.getId());
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        if(!CollectionUtils.isEmpty(childWsStepList)){
            List<Long> childStepIdList = childWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
            List<BatchWorkDetail> batchWorkDetails = Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                    ?batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndFinishAndDeleted(batchWorkDetail.getSubWorkSheet().getId(),childStepIdList, ConstantsEnum.FINISH_STATUS.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO)
                    :batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndFinishAndDeleted(batchWorkDetail.getWorkSheet().getId(),childStepIdList, ConstantsEnum.FINISH_STATUS.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
            if(!CollectionUtils.isEmpty(batchWorkDetails)){
                batchWorkDetails.forEach(batchWorkDetail1 -> batchWorkDetail1.setFinish(ConstantsEnum.UNFINISHED_STATUS.getCategoryName()).setDeleted(net.airuima.constant.Constants.LONG_ZERO));
                batchWorkDetailRepository.saveAll(batchWorkDetails);
            }
        }
        return batchWorkDetail;
    }

    /**
     * 更新容情详情生产数据
     *
     * @param snWorkDetail SN工作详情
     * @return ContainerDetail
     */
    public ContainerDetail rollBackContainerDetail(SnWorkDetail snWorkDetail, WsStep currWsStep) {
        ContainerDetail containerDetail = snWorkDetail.getContainerDetail();
        containerDetail.setInputNumber(containerDetail.getInputNumber() - Constants.INT_ONE);
        if (snWorkDetail.getResult() == Constants.INT_ZERO) {
            containerDetail.setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() - Constants.INT_ONE);
        } else {
            processAfterStep(snWorkDetail, currWsStep, containerDetail);
        }
        List<PreContainerDetailInfo> preContainerDetailInfoList = containerDetail.getPreContainerDetailInfoList();
        if (!CollectionUtils.isEmpty(preContainerDetailInfoList)) {
            preContainerDetailInfoList.forEach(preContainerDetailInfo -> {
                ContainerDetail preContainerDetail = containerDetailRepository.findByIdAndDeleted(preContainerDetailInfo.getPreContainerDetailId(), Constants.LONG_ZERO);
                preContainerDetail.setTransferNumber(preContainerDetail.getTransferNumber() + Constants.INT_ONE).setUnbindTime(null).setStatus(ConstantsEnum.BINDING.getCategoryName());
                containerDetailRepository.save(containerDetail);
            });
        }
        return containerDetailRepository.save(containerDetail);
    }

    /**
     * 如果下个工序请求模式是单支的话则不修改待流转数
     *
     * @param snWorkDetail    单支工序生产详情
     * @param currWsStep      生产工单定制工序
     * @param containerDetail 容器生产详情
     */
    private void processAfterStep(SnWorkDetail snWorkDetail, WsStep currWsStep, ContainerDetail containerDetail) {
        boolean afterStepRequestModeIsSn = false;
        if (StringUtils.isNotBlank(currWsStep.getAfterStepId())) {
            List<Long> afterSteps = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            List<WsStep> afterWsStepList = null;
            if (null != snWorkDetail.getSubWorkSheet()) {
                afterWsStepList = wsStepRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(snWorkDetail.getSubWorkSheet().getId(), afterSteps, Constants.LONG_ZERO);
            }
            if (CollectionUtils.isEmpty(afterWsStepList)) {
                afterWsStepList = wsStepRepository.findByWorkSheetIdAndStepIdInAndDeleted(null != snWorkDetail.getSubWorkSheet() ? snWorkDetail.getSubWorkSheet().getWorkSheet().getId() : snWorkDetail.getWorkSheet().getId(), afterSteps, Constants.LONG_ZERO);
            }
            afterStepRequestModeIsSn = afterWsStepList.stream().anyMatch(wsStep -> wsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName());
        }
        //如果下个工序请求模式是单支的话则不修改待流转数
        containerDetail.setQualifiedNumber(containerDetail.getQualifiedNumber() - Constants.INT_ONE)
                .setTransferNumber(!afterStepRequestModeIsSn ? containerDetail.getTransferNumber() - Constants.INT_ONE : containerDetail.getTransferNumber());
    }

    /**
     * 回退物料数量
     *
     * @param snWorkDetail SN详情
     */
    public void returnSnWorkDetailMaterialBatch(SnWorkDetail snWorkDetail) {
        List<SnWorkDetailMaterialBatch> snWorkDetailMaterialBatchList = snWorkDetailMaterialBatchRepository.findBySnWorkDetailIdAndDeleted(snWorkDetail.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(snWorkDetailMaterialBatchList)) {
            return;
        }
        Map<Integer, List<SnWorkDetailMaterialBatch>> snWorkDetailMaterialBatchGroup = snWorkDetailMaterialBatchList.stream().collect(Collectors.groupingBy(SnWorkDetailMaterialBatch::getType));
        snWorkDetailMaterialBatchGroup.forEach((type, snWorkDetailMaterialBatches) -> {
            //工单层级退料
            if (type == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(snWorkDetailMaterialBatches)) {
                //工单+物料+批次 进行分组
                snWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(
                        snWorkDetailMaterialBatch -> (snWorkDetail.getSubWorkSheet() != null ? snWorkDetail.getSubWorkSheet().getWorkSheet().getId() : snWorkDetail.getWorkSheet().getId())
                                + "-" + snWorkDetailMaterialBatch.getMaterialId()
                                + "-" + snWorkDetailMaterialBatch.getMaterialBatch())).forEach((key, snWorkDetailMaterialBatchTempList) -> {
                    //退料数量
                    double sum = snWorkDetailMaterialBatchTempList.stream().mapToDouble(SnWorkDetailMaterialBatch::getNumber).sum();
                    SnWorkDetailMaterialBatch snWorkDetailMaterialBatch = snWorkDetailMaterialBatchTempList.get(Constants.INT_ZERO);
                    this.abatementContainerDetailMaterialBatch(snWorkDetailMaterialBatch, sum);
                    this.abatementBatchWorkDetailMaterial(snWorkDetailMaterialBatch, sum);
                    batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null != snWorkDetail.getSubWorkSheet() ? snWorkDetail.getSubWorkSheet().getWorkSheet().getId() : snWorkDetail.getWorkSheet().getId(),
                            snWorkDetail.getWorkCell().getId(), snWorkDetailMaterialBatch.getMaterialId(), snWorkDetailMaterialBatch.getMaterialBatch(), type, sum));
                });
            }
            this.returnWorkCellSnWorkDetailMaterialBatch(snWorkDetail, type, snWorkDetailMaterialBatches);
        });
    }

    /**
     * 回退物料数量
     *
     * @param snWorkDetail                SN详情
     * @param type                        扣料方式
     * @param snWorkDetailMaterialBatches 单支生产详情物料批次
     */
    private void returnWorkCellSnWorkDetailMaterialBatch(SnWorkDetail snWorkDetail, Integer type, List<SnWorkDetailMaterialBatch> snWorkDetailMaterialBatches) {
        //工位层级退料
        if (type == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() && ValidateUtils.isValid(snWorkDetailMaterialBatches)) {
            //工单+工位+物料+批次 进行分组
            snWorkDetailMaterialBatches.stream().collect(Collectors.groupingBy(
                    snWorkDetailMaterialBatch -> (null != snWorkDetail.getSubWorkSheet()
                            ? snWorkDetail.getSubWorkSheet().getWorkSheet().getId()
                            : snWorkDetail.getWorkSheet().getId())
                            + "-" + snWorkDetail.getWorkCell().getId()
                            + "-" + snWorkDetailMaterialBatch.getMaterialId()
                            + "-" + snWorkDetailMaterialBatch.getMaterialBatch())).forEach((key, snWorkDetailMaterialBatchTempList) -> {
                //退料数量
                double sum = snWorkDetailMaterialBatchTempList.stream().mapToDouble(SnWorkDetailMaterialBatch::getNumber).sum();
                SnWorkDetailMaterialBatch snWorkDetailMaterialBatch = snWorkDetailMaterialBatchTempList.get(Constants.INT_ZERO);
                this.abatementContainerDetailMaterialBatch(snWorkDetailMaterialBatch, sum);
                this.abatementBatchWorkDetailMaterial(snWorkDetailMaterialBatch, sum);
                //分工单工位
                batchWorkDetailService.saveMaterialBatch(new MaterialBatchDTO(null != snWorkDetail.getSubWorkSheet() ? snWorkDetail.getSubWorkSheet().getWorkSheet().getId() : snWorkDetail.getWorkSheet().getId(),
                        snWorkDetail.getWorkCell().getId(), snWorkDetailMaterialBatch.getMaterialId(), snWorkDetailMaterialBatch.getMaterialBatch(), type, sum));
            });
        }
    }

    /**
     * 回退容器详情批次数量
     *
     * @param snWorkDetailMaterialBatch SN详情批次
     * @param number                    回退数量
     */
    public void abatementContainerDetailMaterialBatch(SnWorkDetailMaterialBatch snWorkDetailMaterialBatch, double number) {
        if (null == snWorkDetailMaterialBatch.getSnWorkDetail().getContainerDetail()) {
            return;
        }
        SnWorkDetail snWorkDetail = snWorkDetailMaterialBatch.getSnWorkDetail();
        ContainerDetail containerDetail = snWorkDetail.getContainerDetail();
        Optional<ContainerDetailMaterialBatch> containerDetailMaterialBatchOptional = Optional.empty();
        //物料存在&批次存在
        if (snWorkDetailMaterialBatch.getMaterialId() != null && StringUtils.isNotBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndMaterialIdAndBatchAndDeleted(containerDetail.getId(), snWorkDetailMaterialBatch.getMaterialId(), snWorkDetailMaterialBatch.getMaterialBatch(), Constants.LONG_ZERO);
        }
        //物料存在&批次不存在
        if (!containerDetailMaterialBatchOptional.isPresent() && snWorkDetailMaterialBatch.getMaterialId() != null && StringUtils.isBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndMaterialIdAndDeleted(containerDetail.getId(), snWorkDetailMaterialBatch.getMaterialId(), Constants.LONG_ZERO);
        }
        //物料不存在&批次存在
        if (!containerDetailMaterialBatchOptional.isPresent() && snWorkDetailMaterialBatch.getMaterialId() == null && StringUtils.isNotBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndBatchAndDeleted(containerDetail.getId(), snWorkDetailMaterialBatch.getMaterialBatch(), Constants.LONG_ZERO);
        }
        //扣减对应容器详情批次所用物料
        if (containerDetailMaterialBatchOptional.isPresent()) {
            ContainerDetailMaterialBatch containerDetailMaterialBatch = containerDetailMaterialBatchOptional.get();
            containerDetailMaterialBatch.setNumber(NumberUtils.subtract(containerDetailMaterialBatch.getNumber(), number).doubleValue());
            containerDetailMaterialBatchRepository.save(containerDetailMaterialBatch);
        }
    }

    /**
     * 回退批量详情批次数量
     *
     * @param snWorkDetailMaterialBatch SN详情批次
     * @param number                    回退数量
     */
    public void abatementBatchWorkDetailMaterial(SnWorkDetailMaterialBatch snWorkDetailMaterialBatch, double number) {
        SnWorkDetail snWorkDetail = snWorkDetailMaterialBatch.getSnWorkDetail();
        BatchWorkDetail batchWorkDetail = snWorkDetail.getSubWorkSheet() != null ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(snWorkDetail.getSubWorkSheet().getId(), snWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null)
                : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(snWorkDetail.getWorkSheet().getId(), snWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        if (null == batchWorkDetail) {
            return;
        }
        Optional<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchOptional = Optional.empty();
        //物料存在&批次存在
        if (snWorkDetailMaterialBatch.getMaterialId() != null && StringUtils.isNotBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), snWorkDetailMaterialBatch.getMaterialId(), snWorkDetailMaterialBatch.getMaterialBatch(), Constants.LONG_ZERO);
        }
        //工单批次不存在&物料存在&批次不存在
        if (!batchWorkDetailMaterialBatchOptional.isPresent() && snWorkDetailMaterialBatch.getMaterialId() != null && StringUtils.isBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndDeleted(batchWorkDetail.getId(), snWorkDetailMaterialBatch.getMaterialId(), Constants.LONG_ZERO);
        }
        //工单批次不存在&物料不存在&批次存在
        if (!batchWorkDetailMaterialBatchOptional.isPresent() && snWorkDetailMaterialBatch.getMaterialId() == null && StringUtils.isNotBlank(snWorkDetailMaterialBatch.getMaterialBatch())) {
            batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), snWorkDetailMaterialBatch.getMaterialBatch(), Constants.LONG_ZERO);
        }
        //扣减对应工单批次所用物料
        if (batchWorkDetailMaterialBatchOptional.isPresent()) {
            BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = batchWorkDetailMaterialBatchOptional.get();
            batchWorkDetailMaterialBatch.setNumber(NumberUtils.subtract(batchWorkDetailMaterialBatch.getNumber(), number).doubleValue());
            batchWorkDetailMaterialBatchRepository.save(batchWorkDetailMaterialBatch);
        }
    }

    /**
     * 验证当前sn是否能合法回退
     *
     * @param snWorkDetail sn详情
     * @return void
     * <AUTHOR>
     * @date 2022/11/18
     */
    public WsStep validDateSnWorkDetail(SnWorkDetail snWorkDetail,List<WsStep> wsSteps,boolean singleSnOnlineRepair) throws BadRequestAlertException {
        if (!singleSnOnlineRepair && null != snWorkDetail.getSubWorkSheet() && snWorkDetail.getSubWorkSheet().getQualifiedNumber() + snWorkDetail.getSubWorkSheet().getUnqualifiedNumber() == snWorkDetail.getSubWorkSheet().getNumber()) {
            throw new ResponseException("error.subWorkSheetFinished", "子工单已完成不能回退");
        }
        if (!singleSnOnlineRepair && null != snWorkDetail.getWorkSheet() && snWorkDetail.getWorkSheet().getQualifiedNumber() + snWorkDetail.getWorkSheet().getUnqualifiedNumber() == snWorkDetail.getWorkSheet().getNumber()) {
            throw new ResponseException("error.workSheetFinished", "工单已完成不能回退");
        }
        WsStep currentWsStep = wsSteps.stream().filter(wsStep -> wsStep.getStep().getId().equals(snWorkDetail.getStep().getId())).findFirst().orElseThrow(() -> new ResponseException("error.wsStepNotExist", "生产工单定制工序不存在"));
        SnWorkDetail lastSnWorkDetail = snWorkDetailRepository.findTop1BySnAndDeletedOrderByIdDesc(snWorkDetail.getSn(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.snWorkDetailNotExist", "单支工序生产详情不存在"));
        //当前sn详情存在后续工作信息
        if (!snWorkDetail.getId().equals(lastSnWorkDetail.getId())) {
            throw new ResponseException("error.nonCurrentJobDetail", "单支详情存在后续生产信息");
        }
        //当前sn已生成在线返修单不可回退
        this.validGeneratedSnRework(snWorkDetail);
        return currentWsStep;
    }

    private void validGeneratedSnRework(SnWorkDetail snWorkDetail) {
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(snWorkDetail.getSn(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.snWorkStatusNotExist", "SN生产状态不存在"));
        if (null != snWorkStatus.getSubWorkSheet() && null != snWorkDetail.getSubWorkSheet() && !snWorkStatus.getSubWorkSheet().getId().equals(snWorkDetail.getSubWorkSheet().getId())) {
            throw new ResponseException(REWORK_IS_EXIST, ERR_MSG);
        }
        if (null != snWorkStatus.getWorkSheet() && null != snWorkDetail.getWorkSheet() && !snWorkStatus.getWorkSheet().getId().equals(snWorkDetail.getWorkSheet().getId())) {
            throw new ResponseException(REWORK_IS_EXIST, ERR_MSG);
        }
        Optional<SnRework> snReworkOptional = snReworkRepository.findTop1BySnWorkStatusIdAndDeletedOrderByIdDesc(snWorkStatus.getId(), Constants.LONG_ZERO);
        if (snReworkOptional.isPresent()) {
            SnRework snRework = snReworkOptional.get();
            if (!snRework.getWsRework().getReworkWorkSheet().getId().equals(snWorkDetail.getSubWorkSheet().getWorkSheet().getId())) {
                throw new ResponseException(REWORK_IS_EXIST, ERR_MSG);
            }
        }
    }

    /**
     * 回退sn生成过程数据
     *
     * @param snWorkDetail sn详情
     * @param deleteByBatchDetail 是否为删除容器详情或者批量详情处删除SN详情=>批量详情删除时无需根据单个SN去回退工单合格数
     * <AUTHOR>
     * @date 2022/11/11
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackSnWorkDetail(SnWorkDetail snWorkDetail,boolean deleteByBatchDetail,boolean singleSnOnlineRepair) {
        //修改sn状态
        iRollbackStepServices[0].updateSnWorkStatus(snWorkDetail,deleteByBatchDetail,singleSnOnlineRepair);
        //逻辑删除SN工作详情的物料批次信息
        snWorkDetailMaterialBatchRepository.batchDeleteBySnWorkDetailId(snWorkDetail.getId());
        //逻辑删除SN工作详情的设备信息
        snWorkDetailFacilityRepository.batchDeleteBySnWorkDetailId(snWorkDetail.getId());
        //逻辑删除SN工作详情不良信息
        snUnqualifiedItemRepository.deleteAllBySnWorkDetailIdAndDeleted(snWorkDetail.getId(),Constants.LONG_ZERO);
        //逻辑删除单支详情易损件
        snWorkDetailWearingPartRepository.batchDeleteBySnWorkDetailId(snWorkDetail.getId());
    }
}
