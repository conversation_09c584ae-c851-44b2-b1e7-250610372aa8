package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.PedigreeReworkWorkFlowExcelConstants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeReworkWorkFlowRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedGroupRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品型号不良种类流程框图Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeReworkWorkFlowService extends CommonJpaService<PedigreeReworkWorkFlow> {

    private static final String PEDIGREE_REWORK_WORK_FLOW_ENTITY_GRAPH = "pedigreeReworkWorkFlowEntityGraph";

    private final PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;
    private final PedigreeRepository pedigreeRepository;
    private final CommonService commonService;

    private final PriorityElementConfigRepository priorityElementConfigRepository;
    private final WorkFlowRepository workFlowRepository;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;

    private final UnqualifiedGroupRepository unqualifiedGroupRepository;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public PedigreeReworkWorkFlowService(PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository, PedigreeRepository pedigreeRepository, CommonService commonService, PriorityElementConfigRepository priorityElementConfigRepository, WorkFlowRepository workFlowRepository, UnqualifiedGroupRepository unqualifiedGroupRepository) {
        this.pedigreeReworkWorkFlowRepository = pedigreeReworkWorkFlowRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.commonService = commonService;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.workFlowRepository = workFlowRepository;
        this.unqualifiedGroupRepository = unqualifiedGroupRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeReworkWorkFlow> find(Specification<PedigreeReworkWorkFlow> spec, Pageable pageable) {
        return pedigreeReworkWorkFlowRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_REWORK_WORK_FLOW_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeReworkWorkFlow> find(Specification<PedigreeReworkWorkFlow> spec) {
        return pedigreeReworkWorkFlowRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_REWORK_WORK_FLOW_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeReworkWorkFlow> findAll(Pageable pageable) {
        return pedigreeReworkWorkFlowRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_REWORK_WORK_FLOW_ENTITY_GRAPH));
    }


    /**
     * 通过产品谱系ID、工艺路线ID及不良组别ID删除关联关系
     *
     * @param pedigreeId         谱系ID
     * @param unqualifiedGroupId 不良组别ID
     * @param workFlowId         工艺路线ID
     * @param clientId           客户id
     * @return void
     * <AUTHOR>
     * @date 2021-05-10
     **/
    public void deleteByPedigreeIdAndUnqualifiedGroupIdAndWorkFlowIdAndClientId(Long pedigreeId, Long unqualifiedGroupId, Long workFlowId, Long clientId) {
            pedigreeReworkWorkFlowRepository.deleteByPedigreeIdAndUnqualifiedGroupIdAndWorkFlowIdAndClientId(pedigreeId, unqualifiedGroupId, workFlowId, clientId);
    }

    /**
     * 启用/禁用指定产品谱系不良种类工艺路线
     *
     * @param pedigreeReworkWorkFlowId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByPedigreeReworkWorkFlowId(Long pedigreeReworkWorkFlowId) {
        PedigreeReworkWorkFlow pedigreeReworkWorkFlow = pedigreeReworkWorkFlowRepository.findById(pedigreeReworkWorkFlowId).orElseThrow(() -> new ResponseException("error.pedigreeReworkWorkFlowNotExist", "产品型号不良种类流程框图不存在"));
        pedigreeReworkWorkFlow.setIsEnable(!pedigreeReworkWorkFlow.getIsEnable());
        pedigreeReworkWorkFlowRepository.save(pedigreeReworkWorkFlow);
    }

    /**
     * 根据产品谱系id获取产品谱系返修工艺路线
     *
     * @param pedigreeId 谱系ID
     * @return List<WorkFlow>
     */
    public List<WorkFlow> findByPedigreeIdAndClientId(Long pedigreeId, Long clientId) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findById(pedigreeId);
        if (pedigreeOptional.isPresent()) {
            return commonService.findPedigreeReworkWorkFlows(pedigreeOptional.get(), clientId,Boolean.FALSE);
        }
        return Lists.newArrayList();
    }

    /**
     * 保存产品谱系不良组别返修工艺路线
     *
     * @param entity 产品谱系不良组别返修工艺路线
     */
    public BaseResultDTO saveInstance(PedigreeReworkWorkFlow entity) {
        //如果是新增
        PriorityElementConfig pedigreeReworkWorkFlowPriority = getPedigreeReworkWorkFlowPriority(entity.getPedigree().getId(),entity.getClientId());
        if (Objects.isNull(entity.getPriorityElementConfig()) && Objects.isNull(pedigreeReworkWorkFlowPriority)) {
            return new BaseResultDTO(net.airuima.constant.Constants.KO, "没有匹配的组合条件", PedigreeReworkWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        PriorityElementConfig priorityElementConfig = Objects.nonNull(entity.getPriorityElementConfig()) ? priorityElementConfigRepository.findByIdAndDeleted(entity.getPriorityElementConfig().getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null):pedigreeReworkWorkFlowPriority;
        if (Objects.isNull(priorityElementConfig)) {
            return new BaseResultDTO(net.airuima.constant.Constants.KO, "没有匹配的组合条件", PedigreeReworkWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        entity.setPriorityElementConfig(priorityElementConfig);
        BaseResultDTO baseResultDTO = checkPedigreeReworkWorkFlow(entity);
        if (Objects.nonNull(baseResultDTO)) {
            return baseResultDTO;
        }
        entity.setDeleted(net.airuima.constant.Constants.LONG_ZERO);
        pedigreeReworkWorkFlowRepository.save(entity);
        return new BaseResultDTO(net.airuima.constant.Constants.OK, entity);
    }

    /**
     * 校验产品谱系不良组别返修工艺路线是否已经存在
     *
     * @param pedigreeReworkWorkFlow 产品谱系不良组别返修工艺路线
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    private BaseResultDTO checkPedigreeReworkWorkFlow(PedigreeReworkWorkFlow pedigreeReworkWorkFlow) {
        //客户代码
        Long clientId = null != pedigreeReworkWorkFlow.getClientId() ? pedigreeReworkWorkFlow.getClientId():null;
        // 产品谱系
        Long pedigreeId = null != pedigreeReworkWorkFlow.getPedigree() ? pedigreeReworkWorkFlow.getPedigree().getId() : null;
        // 工艺路线
        Long workFlowId = null != pedigreeReworkWorkFlow.getWorkFlow() ? pedigreeReworkWorkFlow.getWorkFlow().getId() : null;
        // 不良项目类别Id
        Long unqualifiedGroupId = null != pedigreeReworkWorkFlow.getUnqualifiedGroup() ? pedigreeReworkWorkFlow.getUnqualifiedGroup().getId() : null;
        // 获取产品谱系工艺路线
        PedigreeReworkWorkFlow queryPedigreeReworkWorkFlow = pedigreeReworkWorkFlowRepository.findByPedigreeIdAndWorkFlowIdAndUnqualifiedGroupIdAndClientIdAndDeleted(pedigreeId,workFlowId,unqualifiedGroupId,clientId,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeReworkWorkFlow.getId()) && Objects.nonNull(queryPedigreeReworkWorkFlow)) {
            return new BaseResultDTO(Constants.KO, "产品谱系不良组别返修工艺路线已存在", StringUtils.uncapitalize(PedigreeReworkWorkFlow.class.getSimpleName()), "PedigreeReworkWorkFlowExists");
        }
        if(Objects.nonNull(pedigreeReworkWorkFlow.getId()) && Objects.nonNull(queryPedigreeReworkWorkFlow) && !queryPedigreeReworkWorkFlow.getId().equals(pedigreeReworkWorkFlow.getId())){
            return new BaseResultDTO(Constants.KO, "产品谱系不良组别返修工艺路线已存在", StringUtils.uncapitalize(PedigreeReworkWorkFlow.class.getSimpleName()), "PedigreeReworkWorkFlowExists");
        }
        return null;
    }

    /**
     * 产品谱系返修工艺路线导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object> 行数据
     */
    public List<Map<String, Object>> importPedigreeReworkWorkFlowExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(PedigreeReworkWorkFlowExcelConstants.PEDIGREE_CODE));
            // 产品谱系
            Pedigree pedigree = null;
            if (!StringUtils.isEmpty(pedigreeCode) && !"null".equals(pedigreeCode)) {
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO);
                if (pedigreeOptional.isPresent()) {
                    pedigree = pedigreeOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工艺路线编码并转换为对象
            String workFlowCode = String.valueOf(row.get(PedigreeReworkWorkFlowExcelConstants.WORK_FLOW_CODE));
            WorkFlow workFlow = null;
            if (!StringUtils.isEmpty(workFlowCode) && !"null".equals(workFlowCode)) {
                Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(workFlowCode, Constants.LONG_ZERO);
                if (workFlowOptional.isPresent()) {
                    workFlow = workFlowOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            if (Objects.isNull(workFlow)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取不良种类并转换为对象
            String unqualifiedGroupCode = String.valueOf(row.get(PedigreeReworkWorkFlowExcelConstants.UNQUALIFIED_GROUP_CODE));
            UnqualifiedGroup unqualifiedGroup = null;
            if (!StringUtils.isEmpty(unqualifiedGroupCode) && !"null".equals(unqualifiedGroupCode)) {
                Optional<UnqualifiedGroup> unqualifiedGroupOptional = unqualifiedGroupRepository.findByCodeAndDeleted(unqualifiedGroupCode, Constants.LONG_ZERO);
                if (unqualifiedGroupOptional.isPresent()) {
                    unqualifiedGroup = unqualifiedGroupOptional.get();
                } else {
                    row.put("错误信息", "导入Excel失败,数据有误, 原因不良种类不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            //获取客户代码
            String clientCode = String.valueOf(row.get((PedigreeReworkWorkFlowExcelConstants.CLIENT_CODE)));
            ClientDTO clientDto = StringUtils.isEmpty(clientCode) || "null".equals(clientCode) ? null : rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO);
            if ((!StringUtils.isEmpty(clientCode) && !"null".equals(clientCode)) && (clientDto == null || clientDto.getId() == null)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因客户编码不存在");
                illegalDataList.add(row);
                return;
            }
            // 客户id
            Long clientId = Optional.ofNullable(clientDto).map(ClientDTO::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
            //不良种类
            Long unqualifiedGroupId = Optional.ofNullable(unqualifiedGroup).map(UnqualifiedGroup:: getId).orElse(null);
            // 获取是否启用
            Boolean isEnable = String.valueOf(row.get(PedigreeReworkWorkFlowExcelConstants.IS_ENABLE)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 查询不良配置是否已经存在
            PedigreeReworkWorkFlow queryPedigreeReworkWorkFlow = pedigreeReworkWorkFlowRepository.findByPedigreeIdAndWorkFlowIdAndUnqualifiedGroupIdAndClientIdAndDeleted(pedigreeId,workFlowId,unqualifiedGroupId,clientId,Constants.LONG_ZERO).orElse(null);
            // 获取不良条件优先级配置
            PriorityElementConfig pedigreeReworkWorkFlowPriority = getPedigreeReworkWorkFlowPriority(pedigreeId,clientId);
            if (pedigreeReworkWorkFlowPriority == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 该组合条件优先级未配置");
                illegalDataList.add(row);
                return;
            }
            // 保存配置
            savePedigreeReworkWorkFlow(pedigree, workFlow, isEnable, queryPedigreeReworkWorkFlow, pedigreeReworkWorkFlowPriority,
                    clientId, unqualifiedGroup);
        });
        return illegalDataList;
    }

    /**
     * 保存产品谱系返修工艺路线配置
     *
     * @param pedigree                      产品谱系
     * @param workFlow                      工艺路线
     * @param isEnable                      是否启用
     * @param queryPedigreeReworkWorkFlow   查询的返修工艺路线配置
     * @param pedigreeReworkWorkFlowPriority 条件优先级
     * @param clientId                      客户id
     * @param unqualifiedGroup              不良组别
     */
    private void savePedigreeReworkWorkFlow(Pedigree pedigree, WorkFlow workFlow, Boolean isEnable, PedigreeReworkWorkFlow queryPedigreeReworkWorkFlow,
                                          PriorityElementConfig pedigreeReworkWorkFlowPriority, Long clientId, UnqualifiedGroup unqualifiedGroup) {
        if (Objects.nonNull(queryPedigreeReworkWorkFlow)) {
            queryPedigreeReworkWorkFlow.setIsEnable(isEnable);
            pedigreeReworkWorkFlowRepository.save(queryPedigreeReworkWorkFlow);
        } else {
            PedigreeReworkWorkFlow pedigreeReworkWorkFlow = new PedigreeReworkWorkFlow();
            pedigreeReworkWorkFlow
                    .setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setUnqualifiedGroup(unqualifiedGroup)
                    .setPriorityElementConfig(pedigreeReworkWorkFlowPriority)
                    .setClientId(clientId)
                    .setIsEnable(isEnable);
            pedigreeReworkWorkFlow.setDeleted(Constants.LONG_ZERO);
            pedigreeReworkWorkFlowRepository.save(pedigreeReworkWorkFlow);
        }
    }


    /**
     * 获取产品谱系返修工艺路线条件优先级配置
     *
     * @param pedigreeId 产品谱系id
     * @param clientId   客户id
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    private PriorityElementConfig getPedigreeReworkWorkFlowPriority(Long pedigreeId, Long clientId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_SEVEN,combination);
    }



}
