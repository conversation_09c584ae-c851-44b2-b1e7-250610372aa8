package net.airuima.rbase.service.procedure.aps.plugin.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.SaleOrderDetail;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsSaleOrderDetail;
import net.airuima.rbase.dto.aps.SaleOrderDetailInfo;
import net.airuima.rbase.dto.aps.WorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.procedure.aps.*;
import net.airuima.rbase.service.procedure.aps.plugin.ISaleOrderService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 销售订单相关接口实现
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SaleOrderServiceImpl implements ISaleOrderService {

    @Autowired
    private SaleOrderDetailRepository saleOrderDetailRepository;
    @Autowired
    private WsSaleOrderDetailRepository wsSaleOrderDetailRepository;
    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CascadeWorkSheetRepository cascadeWorkSheetRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    /**
     * 生成工单销售订单号
     *
     * @param pedigree 产品谱系
     * @param workLine 产线
     * @return 工单销售订单号
     */
    @Override
    public String generateWsSaleOrderSerialNumber(Pedigree pedigree, WorkLine workLine) {
        SerialNumberDTO serialNumberDTO = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET, null, workLine.getOrganizationId());
        return rbaseSerialNumberProxy.generate(serialNumberDTO);
    }

    /**
     * 创建销售订单与工单的关联关系记录
     *
     * @param workSheet            工单信息
     * @param saleOrderDetailInfos 销售订单详情ids
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    @Override
    public void generateWsSaleOrder(WorkSheet workSheet, List<SaleOrderDetailInfo> saleOrderDetailInfos) {

        if (!ValidateUtils.isValid(saleOrderDetailInfos)) return;

        saleOrderDetailInfos = saleOrderDetailInfos.stream().distinct().collect(Collectors.toList());

        // 获取去重后的销售订单明细ID集合
        List<Long> detailIds = saleOrderDetailInfos.stream().map(SaleOrderDetailInfo::getSaleOrderDetailId)
                .distinct().collect(Collectors.toList());

        List<SaleOrderDetail> saleOrderDetails = saleOrderDetailRepository.findByIdInAndDeleted(detailIds,
                Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(saleOrderDetails)) {
            throw new ResponseException("wsSaleOrderDetailNotFind", "销售订单谱系信息不存在");
        }

        // 创建快速查找的映射表
        Map<Long, SaleOrderDetail> detailMap = saleOrderDetails.stream()
                .collect(Collectors.toMap(SaleOrderDetail::getId, Function.identity()));

        // 验证产品谱系唯一性
        Long distinctPedigrees = saleOrderDetails.stream().map(SaleOrderDetail::getPedigree).distinct()
                .count();
        if (distinctPedigrees > 1) {
            throw new ResponseException("multipleSalesOrdersMustOnePedigree", "多个销售订单下发必须同一产品谱系");
        }

        // 批量操作容器
        List<WsSaleOrderDetail> wsDetailsToSave = new ArrayList<>();
        Set<SaleOrderDetail> detailsToUpdate = new HashSet<>();
        Set<SaleOrder> ordersToUpdate = new HashSet<>();

        saleOrderDetailInfos.forEach(detailInfo -> {
            SaleOrderDetail saleOrderDetail = detailMap.get(detailInfo.getSaleOrderDetailId());
            // 创建关联关系
            WsSaleOrderDetail wsDetail = new WsSaleOrderDetail()
                    .setSaleOrderDetail(detailMap.get(detailInfo.getSaleOrderDetailId()))
                    .setWorkSheet(workSheet)
                    .setNumber(detailInfo.getNumber());
            wsDetailsToSave.add(wsDetail);

            // 更新生产数量
            saleOrderDetail.setProductionQuantity(saleOrderDetail.getProductionQuantity() + saleOrderDetail.getNumber());
            detailsToUpdate.add(saleOrderDetail);

            // 更新关联订单
            SaleOrder order = saleOrderDetail.getSaleOrder();
            order.setProductionQuantity(order.getProductionQuantity() + saleOrderDetail.getNumber());
            ordersToUpdate.add(order);
        });

        // 批量数据库操作
        wsSaleOrderDetailRepository.saveAll(wsDetailsToSave);
        saleOrderDetailRepository.saveAll(detailsToUpdate);
        saleOrderRepository.saveAll(ordersToUpdate);

        // 工单属性设置
        if (workSheet.getPriority() == Constants.INT_ZERO) {
            Integer maxPriority = saleOrderDetails.stream()
                    .map(d -> d.getSaleOrder().getPriority())
                    .max(Integer::compareTo)
                    .orElse(Constants.INT_ZERO);
            workSheet.setPriority(maxPriority);
        }
        if (Objects.isNull(workSheet.getClientId()) && saleOrderDetails.size() == 1) {
            workSheet.setClientId(saleOrderDetails.get(0).getSaleOrder().getClientId());
        }

        if (Objects.isNull(workSheet.getDeliveryDate())) {
            Optional<LocalDate> maxDate = saleOrderDetails.stream()
                    .map(d -> d.getSaleOrder().getDeliveryDate())
                    .max(LocalDate::compareTo);
            workSheet.setDeliveryDate(maxDate.orElse(null));
        }
        workSheetRepository.save(workSheet);
    }

    /**
     * 更新工单与销售订单的投产数量
     *
     * @param workSheet            工单信息
     * @param saleOrderDetailInfos 销售订单详情ids
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    @Override
    public void updateWsSaleOrder(WorkSheet workSheet, List<SaleOrderDetailInfo> saleOrderDetailInfos) {
        if (!ValidateUtils.isValid(saleOrderDetailInfos)) {
            return;
        }

        saleOrderDetailInfos = saleOrderDetailInfos.stream().distinct().toList();
        List<WsSaleOrderDetail> wsSaleOrderDetailList = wsSaleOrderDetailRepository
                .findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);

        // 修改时，若不存在则新建销售订单与工单的关联关系记录
        if (!ValidateUtils.isValid(wsSaleOrderDetailList)) {
            BeanUtil.getHighestPrecedenceBean(ISaleOrderService.class).generateWsSaleOrder(workSheet,
                    saleOrderDetailInfos);
        } else {
            List<SaleOrderDetailInfo> finalSaleOrderDetailInfos = saleOrderDetailInfos;
            List<WsSaleOrderDetail> logicDeleteWs = wsSaleOrderDetailList.stream()
                    .filter(wsSaleOrderDetail -> finalSaleOrderDetailInfos.stream()
                            .noneMatch(saleOrderDetailInfo -> Objects.equals(
                                    wsSaleOrderDetail.getSaleOrderDetail().getId(),
                                    saleOrderDetailInfo.getSaleOrderDetailId())))
                    .collect(Collectors.toList());
            // 删除修改的销售订单详情
            if (ValidateUtils.isValid(logicDeleteWs)) {
                logicDeleteWs.forEach(wsSaleOrderDetail -> {
                    SaleOrderDetail saleOrderDetail = wsSaleOrderDetail.getSaleOrderDetail();
                    saleOrderDetail.setProductionQuantity(
                            saleOrderDetail.getProductionQuantity() - wsSaleOrderDetail.getNumber());
                    saleOrderDetailRepository.save(saleOrderDetail);
                    SaleOrder saleOrder = wsSaleOrderDetail.getSaleOrderDetail().getSaleOrder();
                    saleOrder.setProductionQuantity(saleOrder.getProductionQuantity() - wsSaleOrderDetail.getNumber());
                    saleOrderRepository.save(saleOrder);
                });
                wsSaleOrderDetailRepository.logicDelete(logicDeleteWs);
            }

            finalSaleOrderDetailInfos.forEach(saleOrderDetailInfo -> {
                WsSaleOrderDetail wsSaleOrderDetail = wsSaleOrderDetailRepository
                        .findByWorkSheetIdAndSaleOrderDetailIdAndDeleted(workSheet.getId(),
                                saleOrderDetailInfo.getSaleOrderDetailId(), Constants.LONG_ZERO)
                        .orElse(new WsSaleOrderDetail());
                SaleOrderDetail saleOrderDetail = saleOrderDetailRepository
                        .findByIdAndDeleted(saleOrderDetailInfo.getSaleOrderDetailId(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("wsSaleOrderDetailNotFind", "销售订单谱系信息不存在"));
                wsSaleOrderDetail.setWorkSheet(workSheet).setSaleOrderDetail(saleOrderDetail)
                        .setNumber(saleOrderDetailInfo.getNumber());
                wsSaleOrderDetailRepository.save(wsSaleOrderDetail);
            });

        }
    }

    /**
     * 更新销售订单的完成数量
     *
     * @param workSheet 工单
     * @param number    完成数量
     */
    @Override
    public void calculateBatchSaleOrderAfterAllStepFinished(WorkSheet workSheet, int number) {
        List<WsSaleOrderDetail> wsSaleOrderDetails = wsSaleOrderDetailRepository
                .findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsSaleOrderDetails)) {
            return;
        }

        int totalNumber = wsSaleOrderDetails.stream().mapToInt(WsSaleOrderDetail::getNumber).sum();
        if (totalNumber == 0) {
            throw new ResponseException("totalNumberIsZero", "销售订单明细总数量不能为零");
        }

        List<SaleOrderDetail> detailsToUpdate = new ArrayList<>();
        Set<SaleOrder> ordersToUpdate = new LinkedHashSet<>(); // 使用Set避免重复更新订单

        for (WsSaleOrderDetail wsSaleOrderDetail : wsSaleOrderDetails) {
            // 使用BigDecimal进行精确比例分配
            BigDecimal ratio = BigDecimal.valueOf(wsSaleOrderDetail.getNumber())
                    .divide(BigDecimal.valueOf(totalNumber), 4, RoundingMode.HALF_UP);
            BigDecimal allocatedNumber = BigDecimal.valueOf(number)
                    .multiply(ratio)
                    .setScale(0, RoundingMode.HALF_UP);
            int allocated = allocatedNumber.intValue();

            // 更新销售订单明细
            SaleOrderDetail detail = wsSaleOrderDetail.getSaleOrderDetail();
            detail.setFinishNumber(detail.getFinishNumber() + allocated);
            detailsToUpdate.add(detail);

            // 更新关联销售订单
            SaleOrder order = detail.getSaleOrder();
            order.setFinishNumber(order.getFinishNumber() + allocated);
            ordersToUpdate.add(order);
        }

        // 批量保存更新
        saleOrderDetailRepository.saveAll(detailsToUpdate);
        saleOrderRepository.saveAll(ordersToUpdate);
    }

    /**
     * 获取销售订单进度
     *
     * @param workSheets      工单列表
     * @param saleOrderNumber 销售订单数量
     * @return SaleOrderProcessDTO
     */
    public SaleOrderProcessDTO getWsOrderProcessDTO(List<WorkSheet> workSheets, Integer saleOrderNumber) {
        SaleOrderProcessDTO saleOrderProcessDTO = new SaleOrderProcessDTO();

        //销售订单进度：根据成品工单及关联的半成品工单、权重进行汇总统计
        BigDecimal totalSaleOrderProcess = NumberUtils.divide(workSheets.stream().mapToInt(WorkSheet::getQualifiedNumber).sum(), saleOrderNumber, Constants.INT_TWO);
        List<WorkSheetSimpleGetDTO> workSheetSimpleGetDTOList = workSheets.stream().map(WorkSheetSimpleGetDTO::new).toList();
        //获取可能的下级工单列表
        for (WorkSheetSimpleGetDTO workSheetSimpleGetDTO : workSheetSimpleGetDTOList) {
            //工单所占销售订单中的权重
            List<WorkSheet> subordinateWorkSheetList = cascadeWorkSheetRepository.findSubordinateWorkSheetBySuperiorWorkSheetId(workSheetSimpleGetDTO.getId(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(subordinateWorkSheetList)) {
                List<WorkSheetSimpleGetDTO> subordinateWorkSheetDTOList = Lists.newArrayList();
                for (WorkSheet subordinateWorkSheet : subordinateWorkSheetList) {
                    WorkSheetSimpleGetDTO workSheetSimpleGetDto = new WorkSheetSimpleGetDTO(subordinateWorkSheet);
                    //半成品工单在成品工单中的权重
                    BigDecimal subordinateWorkSheetProcess = NumberUtils.divide(subordinateWorkSheet.getQualifiedNumber(), subordinateWorkSheet.getNumber(), Constants.INT_TWO);
                    workSheetSimpleGetDto.setProgress(subordinateWorkSheetProcess);
                    subordinateWorkSheetDTOList.add(workSheetSimpleGetDto);
                }
                BigDecimal wsWeight = NumberUtils.divide(subordinateWorkSheetList.stream().mapToInt(WorkSheet::getQualifiedNumber).sum(), workSheetSimpleGetDTO.getNumber(), Constants.INT_TWO);
                //成品工单在销售订单的进度
                workSheetSimpleGetDTO.setProgress(wsWeight)
                        .setSubordinateWorkSheetDTOList(subordinateWorkSheetDTOList);
            }
        }
        saleOrderProcessDTO.setWorkSheetSimpleGetDTOList(workSheetSimpleGetDTOList).setProgress(totalSaleOrderProcess);
        return saleOrderProcessDTO;
    }
}
