package net.airuima.rbase.service.procedure.quality;

import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendHistory;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendHistoryRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工位宽放记录Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellExtendHistoryService extends CommonJpaService<WorkCellExtendHistory> {

    @Autowired
    private WorkCellExtendHistoryRepository workCellExtendHistoryRepository;


    @Override
    @FetchMethod
    public Page<WorkCellExtendHistory> find(Specification<WorkCellExtendHistory> spec, Pageable pageable) {
        return workCellExtendHistoryRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<WorkCellExtendHistory> find(Specification<WorkCellExtendHistory> spec) {
        return workCellExtendHistoryRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<WorkCellExtendHistory> findAll(Pageable pageable) {
        return workCellExtendHistoryRepository.findAll(pageable);
    }
}
