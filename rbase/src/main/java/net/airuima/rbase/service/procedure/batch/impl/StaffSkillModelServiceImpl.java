package net.airuima.rbase.service.procedure.batch.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;
import net.airuima.rbase.dto.skill.SkillDTO;
import net.airuima.rbase.proxy.skill.RbaseSkillProxy;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.batch.StaffSkillModelService;
import net.airuima.rbase.util.ValidateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/11/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class StaffSkillModelServiceImpl implements StaffSkillModelService {
    private final Logger log = LoggerFactory.getLogger(StaffSkillModelServiceImpl.class);
    @Autowired
    private CommonService commonService;

    @Autowired
    private RbaseSkillProxy rbaseSkillProxy;

    /**
     * 验证当前待生产的工序绑定了相应的技能与员工所拥有的技能是否都匹配
     *
     * @param subWorkSheet 子工单
     * @param currWsStep   工序
     * @param stepInfo     工位
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO validStaffSkillModel(SubWorkSheet subWorkSheet, WsStep currWsStep, ClientGetStepInfoDTO.StepInfo stepInfo) {
        // 工单
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        // 产品谱系
        Pedigree pedigree = workSheet.getPedigree();
        // 工艺路线
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null!=currWsStep.getWorkFlow()?currWsStep.getWorkFlow():subWorkSheet.getWorkFlow();
        // 工序
        Step step = currWsStep.getStep();
        // 子工单的产品谱系-工艺路线-工序绑定的技能
        List<SkillDTO> stepWorkSkills = commonService.findPedigreeStepSkills(pedigree, snapshotWorkFlow, step);
        // 员工Id
        Long staffId = stepInfo.getStaffId();
        if (!ValidateUtils.isValid(stepWorkSkills)) {
            return new BaseClientDTO(Constants.OK, "工序未绑定相应的技能,放行");
        }
        // 员工技能
        List<SkillDTO> staffSkills = rbaseSkillProxy.findLegalSkillByStaffIdAndDeleted(staffId, LocalDate.now(),Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(staffSkills)) {
            return new BaseClientDTO(Constants.KO, "员工无技能");
        }
        // 员工掌握的技能匹配工序所需的技能
        Boolean match = staffSkillsMatchStepWork(staffSkills, stepWorkSkills);
        if (match) {
            return new BaseClientDTO(Constants.OK);
        } else {
            return new BaseClientDTO(Constants.KO, "(子)工单工序绑定的技能与员工所拥有的技能不匹配");
        }
    }

    /**
     * 员工掌握的技能匹配工序所需的技能
     *
     * @param staffSkills    员工技能
     * @param stepWorkSkills 工序所需的技能
     * @return 员工掌握的技能匹配工序所需的技能
     */
    private Boolean staffSkillsMatchStepWork(List<SkillDTO> staffSkills, List<SkillDTO> stepWorkSkills) {
        for (SkillDTO stepSkill : stepWorkSkills) {
            // 找出匹配的技能
            boolean anyMatch = staffSkills.stream().anyMatch(stepSkill::equals);
            // 存在有员工未掌握的工序技能
            if (!anyMatch) {
                return false;
            }
        }
        return true;
    }
}
