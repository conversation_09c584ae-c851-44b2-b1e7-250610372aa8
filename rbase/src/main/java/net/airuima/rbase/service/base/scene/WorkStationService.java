package net.airuima.rbase.service.base.scene;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkStation;
import net.airuima.rbase.repository.base.scene.WorkStationRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工站Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkStationService extends CommonJpaService<WorkStation> {
    private static final String WORK_STATION_ENTITY_GRAPH = "workStationEntityGraph";
    private final WorkStationRepository workStationRepository;

    public WorkStationService(WorkStationRepository workStationRepository) {
        this.workStationRepository = workStationRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkStation> find(Specification<WorkStation> spec, Pageable pageable) {
        return workStationRepository.findAll(spec, pageable,new NamedEntityGraph(WORK_STATION_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkStation> find(Specification<WorkStation> spec) {
        return workStationRepository.findAll(spec,new NamedEntityGraph(WORK_STATION_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkStation> findAll(Pageable pageable) {
        return workStationRepository.findAll(pageable,new NamedEntityGraph(WORK_STATION_ENTITY_GRAPH));
    }

    /**
     * 通过名称和编码模糊查询工站
     *
     * @param text 名称或编码
     * @return 工站列表
     */
    @Transactional(readOnly = true)
    public List<WorkStation> findByNameOrCode(String text) {
        return workStationRepository.findByNameOrCode(text);
    }

    /**
     * 通过线体ID获取工站列表
     * <AUTHOR>
     * @param workLineId 线体ID
     * @return List<WorkStation>
     * @date 2021-04-12
     **/
    @Transactional(readOnly = true)
    public List<WorkStation> findByWorkLineId(Long workLineId){
        return workStationRepository.findByWorkLineIdAndDeleted(workLineId, Constants.LONG_ZERO);
    }
}
