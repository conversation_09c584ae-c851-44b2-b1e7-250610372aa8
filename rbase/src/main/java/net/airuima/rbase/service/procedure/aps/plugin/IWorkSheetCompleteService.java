package net.airuima.rbase.service.procedure.aps.plugin;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;

import java.util.List;

/**
 * 工单完工上传API
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 */
@FuncDefault
public interface IWorkSheetCompleteService {

    /**
     * 工单完工上传
     * @param subWorkSheetIdList 子工单主键ID列表
     * @return java.lang.String 结果信息
     */
   @FuncInterceptor("WorkSheetCompleteUpload")
   default String subWorkSheetComplete(List<Long> subWorkSheetIdList) {
       return "工单完工上传失败";
   }
}
