package net.airuima.rbase.service.procedure.batch.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/12
 */
@FuncDefault
public interface IBatchWorkDetailService {

    /**
     * 验证后置工序是否已经录入(containerDetail == null 说明是工序验证，容器验证不需要)
     *
     * @param currWsStep      定制工序
     * @param batchWorkDetail 批量详情
     * @param containerDetail 容器详情
     * @return : net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO 被占用的容器详情
     * <AUTHOR>
     * @date 2023/4/7
     **/
    default ContainerDetailReplaceDTO validAfterStep(WsStep currWsStep, BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail) {
        return null;
    }
}
