package net.airuima.rbase.service.procedure.material;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;

/**
 * Copyright(C), 2017-2023,武汉睿码智能科技有限公司
 * 工单退料相关 Interface
 *
 * @author: rain
 * @date: 2023/12/4 11:17
 */
public interface IWsMaterialReturnService {

    /**
     * 保存工单退料记录
     *
     * @param rollBackMaterialDto 工单退料信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     */
    @FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber")
    default BaseDTO saveWsMaterialReturn(RollBackMaterialDTO rollBackMaterialDto) {
        return new BaseDTO(Constants.KO);
    }
}
