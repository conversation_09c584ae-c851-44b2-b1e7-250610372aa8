package net.airuima.rbase.service.base.wearingpart.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WearingPartCategoryEnum;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.service.base.wearingpart.IWearingPartBaseService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WearingPartBaseServiceImpl implements IWearingPartBaseService {

    @Autowired
    private WearingPartRepository wearingPartRepository;

    /**
     * 修改易损件状态
     *
     * @param id     易损件主键ID
     * @param status 易损件状态
     * <AUTHOR>
     * @date 2023/3/16
     **/
    @Override
    public void updateStatus(Long id, Integer status) {
        WearingPart wearingPart = wearingPartRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElse(null);
        if (null == wearingPart) {
            throw new ResponseException("error.WearingPartIsNotFound", "易损件不存在");
        }
        if (WearingPartCategoryEnum.AVAILABLE.getCategory() != status && WearingPartCategoryEnum.INUSE.getCategory() != status && WearingPartCategoryEnum.EXCEED.getCategory() != status && WearingPartCategoryEnum.SCRAP.getCategory() != status) {
            throw new ResponseException("error.WearingPartStatusIsNotFound", "易损件使用状态不存在");
        }
        wearingPart.setStatus(status);
        wearingPartRepository.saveAndFlush(wearingPart);
    }
}
