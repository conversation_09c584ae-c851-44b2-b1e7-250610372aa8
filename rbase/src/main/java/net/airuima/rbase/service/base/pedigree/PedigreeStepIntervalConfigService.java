package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.PedigreeStepIntervalConfigExcelConstants;
import net.airuima.rbase.constant.PedigreeStepIntervalTimeUnitEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepIntervalConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.util.ResponseException;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepIntervalConfigDTO;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 产品谱系工序间隔配置Service
 *
 * <AUTHOR>
 * @date 2022/4/11 15:06
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepIntervalConfigService extends CommonJpaService<PedigreeStepIntervalConfig> {
    private static final String PEDIGREE_STEP_INTERVAL_CONFIG_ENTITY_GRAPH = "pedigreeStepIntervalConfigEntityGraph";
    private final PedigreeRepository pedigreeRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepRepository stepRepository;
    private final PedigreeStepIntervalConfigRepository pedigreeStepIntervalConfigRepository;

    public PedigreeStepIntervalConfigService(PedigreeStepIntervalConfigRepository pedigreeStepIntervalConfigRepository,
                                             PedigreeRepository pedigreeRepository,
                                             WorkFlowRepository workFlowRepository,
                                             StepRepository stepRepository) {
        this.pedigreeStepIntervalConfigRepository = pedigreeStepIntervalConfigRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeStepIntervalConfig> find(Specification<PedigreeStepIntervalConfig> spec, Pageable pageable) {
        return pedigreeStepIntervalConfigRepository.findAll(spec, pageable, new NamedEntityGraph(PEDIGREE_STEP_INTERVAL_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<PedigreeStepIntervalConfig> find(Specification<PedigreeStepIntervalConfig> spec) {
        return pedigreeStepIntervalConfigRepository.findAll(spec, new NamedEntityGraph(PEDIGREE_STEP_INTERVAL_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeStepIntervalConfig> findAll(Pageable pageable) {
        return pedigreeStepIntervalConfigRepository.findAll(pageable, new NamedEntityGraph(PEDIGREE_STEP_INTERVAL_CONFIG_ENTITY_GRAPH));
    }

    /**
     * 批量更新工序时间间隔配置
     * @param pedigreeStepIntervalConfigDtoList 参数列表
     */
    public void batchUpdate(List<PedigreeStepIntervalConfigDTO> pedigreeStepIntervalConfigDtoList) {
        if(CollectionUtils.isEmpty(pedigreeStepIntervalConfigDtoList)){
            return;
        }
        pedigreeStepIntervalConfigDtoList.forEach(this::create);
    }

    /**
     * 新增产品谱系工序间隔配置
     *
     * @param pedigreeStepIntervalConfigDto 新增产品谱系工序间隔配置Dto
     * @return BaseDTO
     */
    public void create(PedigreeStepIntervalConfigDTO pedigreeStepIntervalConfigDto) {
        List<PedigreeStepIntervalConfigDTO.PreStepDurationDTO> preStepDurationDtoList = pedigreeStepIntervalConfigDto.getPreStepDurationDtoList();
        // 存放前置工序与时间间隔的关系不能为空
        if (!ValidateUtils.isValid(preStepDurationDtoList)) {
            throw new ResponseException("error.preStepMustNotBeNull", "前置工序不可为空");
        }
        // 验证参数的可靠性
        Pedigree pedigree = pedigreeStepIntervalConfigDto.getPedigreeId() == null ?
                null : pedigreeRepository.findByIdAndDeleted(pedigreeStepIntervalConfigDto.getPedigreeId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.pedigreeNotExist", "产品谱系不存在"));
        WorkFlow workFlow = pedigreeStepIntervalConfigDto.getWorkFlowId() == null ?
                null : workFlowRepository.findByIdAndDeleted(pedigreeStepIntervalConfigDto.getWorkFlowId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.workFlowNotExist", "工艺路线不存在"));
        Step step = stepRepository.findByIdAndDeleted(pedigreeStepIntervalConfigDto.getStepId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.stepNotExist", "工序不存在"));
        // 产品谱系id
        Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
        // 工艺路线id
        Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigs = Lists.newArrayList();
        // 验证前置工序与时间间隔的可靠
        for (PedigreeStepIntervalConfigDTO.PreStepDurationDTO preStepDurationDto : preStepDurationDtoList) {
            if (preStepDurationDto.getDuration() == null) {
                throw new ResponseException("error.durationIsNull", "间隔时长不可为空");
            }
            if (preStepDurationDto.getPreStepId() == null) {
                throw new ResponseException("error.preStepMustNotBeNull", "前置工序不可为空");
            }
            Step preStep = stepRepository.findByIdAndDeleted(preStepDurationDto.getPreStepId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.preStepNotExist", "前置工序不存在"));
            // 验证唯一性
            PedigreeStepIntervalConfig pedigreeStepIntervalConfig = pedigreeStepIntervalConfigRepository
                    .findByPedigreeIdAndWorkFlowIdAndStepIdAndPreStepIdAndDeleted(pedigreeId, workFlowId, step.getId(), preStep.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(new PedigreeStepIntervalConfig());
            pedigreeStepIntervalConfig.setPedigree(pedigree)
                    .setWorkFlow(workFlow).setStep(step)
                    .setPreStep(preStep)
                    .setDuration(preStepDurationDto.getDuration())
                    .setDurationUnit(preStepDurationDto.getDurationUnit())
                    .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
            pedigreeStepIntervalConfigs.add(pedigreeStepIntervalConfigRepository.save(pedigreeStepIntervalConfig));
        }
        if (null != pedigreeStepIntervalConfigDto.getId() && pedigreeStepIntervalConfigs.stream().noneMatch(pedigreeStepIntervalConfig -> pedigreeStepIntervalConfig.getId().equals(pedigreeStepIntervalConfigDto.getId()))) {
            pedigreeStepIntervalConfigRepository.deleteById(pedigreeStepIntervalConfigDto.getId());
        }

    }

    /**
     * 产品谱系工序间隔配置导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>> 行数据
     */
    public List<Map<String, Object>> importPedigreeStepIntervalConfigExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.PEDIGREE_CODE));
            // 产品谱系
            Pedigree pedigree = null;
            if (StringUtils.hasLength(pedigreeCode) && !"null".equals(pedigreeCode)) {
                Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, net.airuima.constant.Constants.LONG_ZERO);
                if (pedigreeOptional.isPresent()) {
                    pedigree = pedigreeOptional.get();
                    if (!pedigree.getIsEnable()) {
                        row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系已禁用");
                        illegalDataList.add(row);
                        return;
                    }
                }
            }
            if (StringUtils.hasLength(pedigreeCode) && !"null".equals(pedigreeCode) && Objects.isNull(pedigree)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因产品谱系不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取工艺路线编码并转换为对象
            String workFlowCode = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.WORK_FLOW_CODE));
            WorkFlow workFlow = null;
            if (StringUtils.hasLength(workFlowCode) && !"null".equals(workFlowCode)) {
                Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(workFlowCode, net.airuima.constant.Constants.LONG_ZERO);
                if (workFlowOptional.isPresent()) {
                    workFlow = workFlowOptional.get();
                    if (!workFlow.getIsEnable()) {
                        row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线已禁用");
                        illegalDataList.add(row);
                        return;
                    }
                }
            }
            if (StringUtils.hasLength(workFlowCode) && !"null".equals(workFlowCode) && Objects.isNull(workFlow)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因工艺路线不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取工序编码并转换为对象
            String stepCode = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.STEP_CODE));
            Step step = null;
            if (StringUtils.hasLength(stepCode) && !"null".equals(stepCode)) {
                Optional<Step> stepOptional = stepRepository.findByCodeAndDeleted(stepCode, net.airuima.constant.Constants.LONG_ZERO);
                if (stepOptional.isPresent()) {
                    step = stepOptional.get();
                    if (!step.getIsEnable()) {
                        row.put("错误信息", "导入Excel失败,数据有误, 原因工序已禁用");
                        illegalDataList.add(row);
                        return;
                    }
                }
            }
            if (Objects.isNull(step)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因工序不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取前置工序编码并转换为对象
            String preStepCode = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.PRE_STEP_CODE));
            Step preStep = null;
            if (StringUtils.hasLength(preStepCode) && !"null".equals(preStepCode)) {
                Optional<Step> preStepOptional = stepRepository.findByCodeAndDeleted(preStepCode, net.airuima.constant.Constants.LONG_ZERO);
                if (preStepOptional.isPresent()) {
                    preStep = preStepOptional.get();
                    if (!preStep.getIsEnable()) {
                        row.put("错误信息", "导入Excel失败,数据有误, 原因前置工序已禁用");
                        illegalDataList.add(row);
                        return;
                    }
                }
            }
            if (Objects.isNull(preStep)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因前置工序不存在");
                illegalDataList.add(row);
                return;
            }
            // 间隔时长
            String duration = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.DURATION));
            if (ObjectUtils.isEmpty(duration) || "null".equals(duration)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因间隔时长不存在");
                illegalDataList.add(row);
                return;
            }
            if(!ToolUtils.isValidInterval(duration)){
                row.put("错误信息", "导入Excel失败,数据有误, 原因间隔时长校验不通过,请输入有效值");
                illegalDataList.add(row);
                return;
            }
            // 单位
            String durationUnitText = String.valueOf(row.get(PedigreeStepIntervalConfigExcelConstants.DURATION_UNIT));
            if (ObjectUtils.isEmpty(durationUnitText) || "null".equals(durationUnitText)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因单位不存在");
                illegalDataList.add(row);
                return;
            }
            PedigreeStepIntervalTimeUnitEnum pedigreeStepIntervalTimeUnitEnum = PedigreeStepIntervalTimeUnitEnum.getByDescription(durationUnitText);
            if (Objects.isNull(pedigreeStepIntervalTimeUnitEnum)) {
                row.put("错误信息", "导入Excel失败,数据有误, 原因单位不存在");
                illegalDataList.add(row);
                return;
            }
            // 单位
            Integer durationUnit = pedigreeStepIntervalTimeUnitEnum.getCode();
            // 工序id
            Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
            // 前置工序id
            Long preStepId = Optional.ofNullable(preStep).map(Step::getId).orElse(null);
            // 产品谱系id
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线id
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
            // 查询配置是否已经存在
            PedigreeStepIntervalConfig queryPedigreeStepIntervalConfig = pedigreeStepIntervalConfigRepository
                    .findByPedigreeIdAndWorkFlowIdAndStepIdAndPreStepIdAndDeleted(pedigreeId, workFlowId, stepId, preStepId, net.airuima.constant.Constants.LONG_ZERO).orElse(null);
            // 保存工序间隔配置
            savePedigreeStepIntervalConfig(pedigree, workFlow, step, preStep, duration, durationUnit, queryPedigreeStepIntervalConfig);
        });
        return illegalDataList;
    }



    /**
     * 保存工序间隔配置
     *
     * @param pedigree                        产品谱系
     * @param workFlow                        工艺路线
     * @param step                            工序
     * @param preStep                         preStep
     * @param duration                        间隔时长
     * @param durationUnit                    单位
     * @param queryPedigreeStepIntervalConfig 产品谱系工序间隔配置
     */
    private void savePedigreeStepIntervalConfig(Pedigree pedigree, WorkFlow workFlow, Step step, Step preStep, String duration, Integer durationUnit, PedigreeStepIntervalConfig queryPedigreeStepIntervalConfig) {
        if (Objects.nonNull(queryPedigreeStepIntervalConfig)) {
            queryPedigreeStepIntervalConfig.setDuration(duration).setDurationUnit(durationUnit);
            pedigreeStepIntervalConfigRepository.save(queryPedigreeStepIntervalConfig);
        } else {
            PedigreeStepIntervalConfig pedigreeStepIntervalConfig = new PedigreeStepIntervalConfig();
            pedigreeStepIntervalConfig
                    .setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setStep(step)
                    .setPreStep(preStep)
                    .setDuration(duration)
                    .setDurationUnit(durationUnit)
                    .setDeleted(Constants.LONG_ZERO);
            pedigreeStepIntervalConfigRepository.save(pedigreeStepIntervalConfig);
        }
    }

    /**
     * 通过产品谱系主键ID、工序主键ID、工艺路线主键ID获取产品谱系工序间隔配置
     * @param pedigreeId 产品谱系ID
     * @param workFlowId 工艺路线ID
     * @param stepId 工序ID
     * @return  List<PedigreeStepIntervalConfig>
     */
    public List<PedigreeStepIntervalConfig> findAllByPedigreeIdAndWorkFlowIdAndStepId(Long pedigreeId, Long workFlowId, Long stepId){
        return pedigreeStepIntervalConfigRepository.findByPedigreeIdAndStepIdAndWorkFlowIdAndDeleted(pedigreeId,stepId,workFlowId, net.airuima.constant.Constants.LONG_ZERO);
    }


}
