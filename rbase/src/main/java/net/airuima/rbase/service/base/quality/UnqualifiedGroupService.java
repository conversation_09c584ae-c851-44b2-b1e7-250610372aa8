package net.airuima.rbase.service.base.quality;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.quality.UnqualifiedGroupRepository;
import net.airuima.rbase.web.rest.base.quality.dto.UnqualifiedGroupCreateDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良种类Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedGroupService extends CommonJpaService<UnqualifiedGroup> {

    private final UnqualifiedGroupRepository unqualifiedGroupRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public UnqualifiedGroupService(UnqualifiedGroupRepository unqualifiedGroupRepository) {
        this.unqualifiedGroupRepository = unqualifiedGroupRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedGroup> find(Specification<UnqualifiedGroup> spec, Pageable pageable) {
        return unqualifiedGroupRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedGroup> find(Specification<UnqualifiedGroup> spec) {
        return unqualifiedGroupRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedGroup> findAll(Pageable pageable) {
        return unqualifiedGroupRepository.findAll(pageable);
    }

    /**
     * 获取所有未删除的不良种类
     */
    public List<UnqualifiedGroup> findAllNotDelete() {
        return unqualifiedGroupRepository.findByDeleted(Constants.LONG_ZERO);
    }

    /**
     * 通过名称和编码模糊查询不良种类
     *
     * @param text 名称或编码
     * @return 不良种类列表
     */
    public List<UnqualifiedGroup> findByNameOrCode(String text) {
        return unqualifiedGroupRepository.findByNameOrCode(text);
    }

    /**
     * 新增不良种类
     *
     * @param entity
     * @return : net.airuima.rbase.domain.base.process.UnqualifiedGroup
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public UnqualifiedGroup create(UnqualifiedGroupCreateDTO entity) {
        UnqualifiedGroup unqualifiedGroup = new UnqualifiedGroup();
        //如果未手动输入编码则按照编码规则自动生成，如果手动输入则判断唯一性
        if (StringUtils.isBlank(entity.getCode())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_UNQUALIFIED_GROUP_CODE, null, null);
            unqualifiedGroup.setCode(rbaseSerialNumberProxy.generate(serialNumberDto));
        } else {
            Optional<UnqualifiedGroup> unqualifiedGroupOptional = unqualifiedGroupRepository.findByCodeAndDeleted(entity.getCode(), Constants.LONG_ZERO);
            if (unqualifiedGroupOptional.isPresent()) {
                throw new ResponseException("error.CodeIsExist", "编码已存在");
            }
            unqualifiedGroup.setCode(entity.getCode());
        }
        unqualifiedGroup.setName(entity.getName()).setIsEnable(Boolean.TRUE);
        unqualifiedGroupRepository.save(unqualifiedGroup);
        return unqualifiedGroup;
    }

    /**
     * 启用/禁用指定不良种类
     *
     * @param unqualifiedGroupId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByUnqualifiedGroupId(Long unqualifiedGroupId) {
        UnqualifiedGroup unqualifiedGroup = unqualifiedGroupRepository.findById(unqualifiedGroupId).orElseThrow(()->new ResponseException("error.unqualifiedGroupNotExist", "不良种类不存在"));
        unqualifiedGroup.setIsEnable(!unqualifiedGroup.getIsEnable());
        unqualifiedGroupRepository.save(unqualifiedGroup);
    }

}
