package net.airuima.rbase.service.base.quality;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.web.rest.base.quality.dto.UnqualifiedItemCreateDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良项目Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedItemService extends CommonJpaService<UnqualifiedItem> {

    private final UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public UnqualifiedItemService(UnqualifiedItemRepository unqualifiedItemRepository) {
        this.unqualifiedItemRepository = unqualifiedItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    
    public Page<UnqualifiedItem> find(Specification<UnqualifiedItem> spec, Pageable pageable) {
        return unqualifiedItemRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedItem> find(Specification<UnqualifiedItem> spec) {
        return unqualifiedItemRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnqualifiedItem> findAll(Pageable pageable) {
        return unqualifiedItemRepository.findAll(pageable);
    }

    /**
     * 通过不良编码或者名称获取启用的不良项目
     *
     * @param text
     * @param size
     * @return
     */
    public List<UnqualifiedItem> findByCodeOrName(String text, Integer size) {
        Page<UnqualifiedItem> page = unqualifiedItemRepository.findByNameOrCode(text, true, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     *
     * @param text 名称或编码
     * @param dealWay 处理方式
     * @param size 条数
     * @return List<UnqualifiedItem>
     */
    public List<UnqualifiedItem> findByCodeOrNameAndDealWay(String text, Integer dealWay, Integer size) {
        Page<UnqualifiedItem> page = unqualifiedItemRepository.findByNameOrCodeAndDealWay(text, dealWay, true, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }
    /**
     * 根据工序组别ID获取工序
     *
     * @param groupId
     * @return
     */
    public List<UnqualifiedItem> findByUqualifiedGroupId(Long groupId) {
        return unqualifiedItemRepository.findByUnqualifiedGroupId(groupId);
    }

    /**
     * 新增不良项目
     *
     * @param entity
     * @return : net.airuima.rbase.domain.base.process.UnqualifiedItem
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public UnqualifiedItem create(UnqualifiedItemCreateDTO entity) {
        UnqualifiedItem unqualifiedItem = new UnqualifiedItem();
        //如果未手动输入编码则按照编码规则自动生成，如果手动输入则判断唯一性
        if (StringUtils.isBlank(entity.getCode())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_UNQUALIFIED_ITEM_CODE, null, null);
            unqualifiedItem.setCode(rbaseSerialNumberProxy.generate(serialNumberDto));
        } else {
            Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository.findByCodeAndDeleted(entity.getCode(), Constants.LONG_ZERO);
            if (unqualifiedItemOptional.isPresent()) {
                throw new ResponseException("error.CodeIsExist", "编码已存在");
            }
            unqualifiedItem.setCode(entity.getCode());
        }
        unqualifiedItem.setName(entity.getName()).setIsEnable(Boolean.TRUE);
        unqualifiedItemRepository.save(unqualifiedItem);
        return unqualifiedItem;
    }

    /**
     * 启用/禁用指定不良项目
     *
     * @param unqualifiedItemId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByUnqualifiedItemId(Long unqualifiedItemId) {
        UnqualifiedItem unqualifiedItem = unqualifiedItemRepository.findById(unqualifiedItemId).orElseThrow(()->new ResponseException("error.unqualifiedItemNotExist", "不良项目不存在"));
        unqualifiedItem.setIsEnable(!unqualifiedItem.getIsEnable());
        unqualifiedItemRepository.save(unqualifiedItem);
    }
}
