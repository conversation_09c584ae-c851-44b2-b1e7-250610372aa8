package net.airuima.rbase.service.procedure.scene;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.TransferModeEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.scene.NextTodoStep;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.rworker.process.dto.RworkerToDoInfoGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerTodoInfoRequestDTO;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.scene.NextTodoStepRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 工位下个待做生产信息Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class NextTodoStepService extends CommonJpaService<NextTodoStep> {

    private final Logger log = LoggerFactory.getLogger(NextTodoStepService.class);
    private final NextTodoStepRepository nextTodoStepRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;

    public NextTodoStepService(NextTodoStepRepository nextTodoStepRepository) {
        this.nextTodoStepRepository = nextTodoStepRepository;
    }

    @Override
    public Page<NextTodoStep> find(Specification<NextTodoStep> spec, Pageable pageable) {
        return nextTodoStepRepository.findAll(spec, pageable);
    }

    @Override
    public List<NextTodoStep> find(Specification<NextTodoStep> spec) {
        return nextTodoStepRepository.findAll(spec);
    }

    @Override
    public Page<NextTodoStep> findAll(Pageable pageable) {
        return nextTodoStepRepository.findAll(pageable);
    }

    /**
     * 分页分别获取工位下个可能的待做工序生产信息清单
     *
     * @param rworkerTodoInfoRequestDTO 请求参数
     * @return net.airuima.rbase.dto.rworker.process.dto.RworkerToDoInfoGetDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    @Transactional(readOnly = true)
    public RworkerToDoInfoGetDTO findRworkerNextTodoStepInfo(RworkerTodoInfoRequestDTO rworkerTodoInfoRequestDTO) {
        RworkerToDoInfoGetDTO rworkerToDoInfoGetDTO = new RworkerToDoInfoGetDTO();
        //首先获取工位绑定的工序列表，如果没有工序绑定则直接返回
        List<Step> stepList = workCellStepRepository.findByWorkCellId(rworkerTodoInfoRequestDTO.getWorkCellId());
        if (CollectionUtils.isEmpty(stepList)) {
            rworkerToDoInfoGetDTO.setBatchTodoStepPageInfo(new RworkerToDoInfoGetDTO.BatchTodoStepPageInfo().setBatchTodoStepInfoList(new ArrayList<>()).setCountSize(Constants.LONG_ZERO).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()));
            rworkerToDoInfoGetDTO.setContainerTodoStepPageInfo(new RworkerToDoInfoGetDTO.ContainerTodoStepPageInfo().setContainerTodoStepInfoList(new ArrayList<>()).setCountSize(Constants.LONG_ZERO).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()));
            rworkerToDoInfoGetDTO.setSingleTodoStepPageInfo(new RworkerToDoInfoGetDTO.SingleTodoStepPageInfo().setSingleTodoStepInfoList(new ArrayList<>()).setCountSize(Constants.LONG_ZERO).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()));
            return rworkerToDoInfoGetDTO;
        }
        List<Long> workCellStepIdList = stepList.stream().map(Step::getId).toList();
        Long pedigreeId = rworkerTodoInfoRequestDTO.getPedigreeId();
        Integer type = rworkerTodoInfoRequestDTO.getType();
        Integer category = rworkerTodoInfoRequestDTO.getCategory();
        //批量待做工序查询条件
        Specification<NextTodoStep> batchRquerySpecification = null;
        //容器待做工序查询条件
        Specification<NextTodoStep> containerRquerySpecification = null;
        //单支待做工序查询条件
        Specification<NextTodoStep> snRquerySpecification = null;
        //如果请求类型为空则添加所有类型待做工序的查询条件
        if (Objects.isNull(type)) {
            batchRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName());
            containerRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName());
            snRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.SN_REQUEST_MODE.getCategoryName());
        } else if (type == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            //如果请求类型为批量下交则添加批量下交待做工序的查询条件
            batchRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName());
        } else if (type == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            //如果请求类型为容器下交则添加容器下交待做工序的查询条件
            containerRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName());
        } else if (type == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            //如果请求类型为单支下交则添加单支下交待做工序的查询条件
            snRquerySpecification = this.findRworkerNextTodoStepInfoQuerySpecification(workCellStepIdList, pedigreeId, category, ConstantsEnum.SN_REQUEST_MODE.getCategoryName());
        }
        //如果批量待做工序查询条件不为空则查询批量待做工序
        if (Objects.nonNull(batchRquerySpecification)) {
            Page<NextTodoStep> batchTodoInfoPage = this.find(batchRquerySpecification, PageRequest.of(rworkerTodoInfoRequestDTO.getCurrentPage(), rworkerTodoInfoRequestDTO.getPageSize()));
            List<NextTodoStep> batchTodoInfoList = Optional.ofNullable(batchTodoInfoPage).map(Slice::getContent).orElse(new ArrayList<>());
            RworkerToDoInfoGetDTO.BatchTodoStepPageInfo batchTodoStepPageInfo = new RworkerToDoInfoGetDTO.BatchTodoStepPageInfo();
            List<RworkerToDoInfoGetDTO.BatchTodoStepInfo> batchTodoStepInfoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(batchTodoInfoList)) {
                batchTodoStepPageInfo.setBatchTodoStepInfoList(batchTodoStepInfoList).setCountSize(Constants.LONG_ZERO).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            } else {
                batchTodoInfoList.forEach(batchTodoStep -> {
                    batchTodoStepInfoList.add(new RworkerToDoInfoGetDTO.BatchTodoStepInfo(batchTodoStep));
                });
                batchTodoStepPageInfo.setBatchTodoStepInfoList(batchTodoStepInfoList).setCountSize(batchTodoInfoPage.getTotalElements()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            }
            rworkerToDoInfoGetDTO.setBatchTodoStepPageInfo(batchTodoStepPageInfo);
        }
        //如果容器待做工序查询条件不为空则查询容器待做工序
        if (Objects.nonNull(containerRquerySpecification)) {
            Page<NextTodoStep> containerTodoInfoPage = this.find(containerRquerySpecification, PageRequest.of(rworkerTodoInfoRequestDTO.getCurrentPage(), rworkerTodoInfoRequestDTO.getPageSize()));
            List<NextTodoStep> containerTodoInfoList = Optional.ofNullable(containerTodoInfoPage).map(Slice::getContent).orElse(new ArrayList<>());
            RworkerToDoInfoGetDTO.ContainerTodoStepPageInfo containerTodoStepPageInfo = new RworkerToDoInfoGetDTO.ContainerTodoStepPageInfo();
            List<RworkerToDoInfoGetDTO.ContainerTodoStepInfo> containerTodoStepInfoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(containerTodoInfoList)) {
                containerTodoStepPageInfo.setContainerTodoStepInfoList(containerTodoStepInfoList).setCountSize(Constants.LONG_ZERO).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            } else {
                containerTodoInfoList.forEach(containerTodoStep -> {
                    containerTodoStepInfoList.add(new RworkerToDoInfoGetDTO.ContainerTodoStepInfo(containerTodoStep));
                });
                containerTodoStepPageInfo.setContainerTodoStepInfoList(containerTodoStepInfoList).setCountSize(containerTodoInfoPage.getTotalElements()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            }
            rworkerToDoInfoGetDTO.setContainerTodoStepPageInfo(containerTodoStepPageInfo);
        }
        //如果单支待做工序查询条件不为空则查询单支待做工序
        if (Objects.nonNull(snRquerySpecification)) {
            Page<NextTodoStep> snTodoInfoPage = this.find(snRquerySpecification, PageRequest.of(rworkerTodoInfoRequestDTO.getCurrentPage(), rworkerTodoInfoRequestDTO.getPageSize()));
            List<NextTodoStep> snTodoInfoList = Optional.ofNullable(snTodoInfoPage).map(Slice::getContent).orElse(new ArrayList<>());
            RworkerToDoInfoGetDTO.SingleTodoStepPageInfo singleTodoStepPageInfo = new RworkerToDoInfoGetDTO.SingleTodoStepPageInfo();
            List<RworkerToDoInfoGetDTO.SingleTodoStepInfo> singleTodoStepInfoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(snTodoInfoList)) {
                singleTodoStepPageInfo.setSingleTodoStepInfoList(singleTodoStepInfoList).setCountSize(Constants.LONG_ZERO).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            } else {
                snTodoInfoList.forEach(singleTodoStep -> {
                    singleTodoStepInfoList.add(new RworkerToDoInfoGetDTO.SingleTodoStepInfo(singleTodoStep));
                });
                singleTodoStepPageInfo.setSingleTodoStepInfoList(singleTodoStepInfoList).setCountSize(snTodoInfoPage.getTotalElements()).setPageSize(rworkerTodoInfoRequestDTO.getPageSize()).setCurrentPage(rworkerTodoInfoRequestDTO.getCurrentPage());
            }
            rworkerToDoInfoGetDTO.setSingleTodoStepPageInfo(singleTodoStepPageInfo);
        }
        return rworkerToDoInfoGetDTO;
    }

    /**
     * 根据工位绑定的工序ID列表、产品谱系ID、工单类型、请求类型构建待做工序查询条件
     *
     * @param stepIdList 工位绑定的工序ID列表
     * @param pedigreeId 产品谱系ID
     * @param category   工单类型
     * @param type       请求类型
     * @return org.springframework.data.jpa.domain.Specification<net.airuima.rbase.domain.procedure.scene.NextTodoStep>
     * <AUTHOR>
     * @since 1.8.1
     */
    private Specification<NextTodoStep> findRworkerNextTodoStepInfoQuerySpecification(List<Long> stepIdList, Long pedigreeId, Integer category, Integer type) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            CriteriaBuilder.In<Long> stepPredicate = criteriaBuilder.in(root.get("step").get("id"));
            for (Long id : stepIdList) {
                stepPredicate.value(id);
            }
            predicateList.add(stepPredicate);
            predicateList.add(criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO));
            predicateList.add(criteriaBuilder.greaterThan(root.get("number"), Constants.INT_ZERO));
            //产品谱系ID参数不为空则增加产品谱系ID参数
            if (Objects.nonNull(pedigreeId)) {
                predicateList.add(criteriaBuilder.equal(root.get("workSheet").get("pedigree").get("id"), pedigreeId));
            }
            //工单类型参数不为空则增加工单类型参数
            if (Objects.nonNull(category)) {
                predicateList.add(criteriaBuilder.equal(root.get("workSheet").get("category"), category));
            }
            //如果请求类型为批量则增加批量待做工序的查询条件
            if (Objects.nonNull(type) && type == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
                predicateList.add(criteriaBuilder.isNull(root.get("containerCode")));
                predicateList.add(criteriaBuilder.isNull(root.get("sn")));
                return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
            }
            //如果请求类型为容器则增加容器待做工序的查询条件
            if (Objects.nonNull(type) && type == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                predicateList.add(criteriaBuilder.isNotNull(root.get("containerCode")));
                predicateList.add(criteriaBuilder.isNull(root.get("sn")));
                return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
            }
            //如果请求类型为单支则增加单支待做工序的查询条件
            if (Objects.nonNull(type) && type == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
                predicateList.add(criteriaBuilder.isNotNull(root.get("sn")));
                predicateList.add(criteriaBuilder.isNull(root.get("containerCode")));
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
    }

    /**
     * 纯批量下交或者分单时更新下个可能的待做工序信息
     *
     * @param productWorkSheetId 投产粒度工单ID（主要用于分布式锁）
     * @param workSheet          工单
     * @param subWorkSheet       子工单
     * @param wsStepList         工单工序快照
     * @param batchWorkDetail    批量详情
     * <AUTHOR>
     * @since 1.8.1
     */
    @Klock(keys = {"#productWorkSheetId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public void updateNextTodoStepInfoWhenBatchStep(Long productWorkSheetId, WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, BatchWorkDetail batchWorkDetail, boolean rollBack) {
        try {
            // 处理下单或分单场景
            if (Objects.isNull(batchWorkDetail)) {
                handleNewSubWorkSheetOrWorkSheet(workSheet, subWorkSheet, wsStepList);
                return;
            }
            // 处理批量工序下交场景
            handleBatchStepTransfer(workSheet, subWorkSheet, wsStepList, batchWorkDetail, rollBack);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理下单或分单场景
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param wsStepList   工单工序快照列表
     * <AUTHOR>
     * @since 1.8.1
     */
    private void handleNewSubWorkSheetOrWorkSheet(WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList) {
        // 获取所有没有前置工序的工序（即第一级工序）
        List<WsStep> firstWsStepList = wsStepList.stream()
                .filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId()))
                .toList();
        if (Objects.nonNull(subWorkSheet)) {
            nextTodoStepRepository.deleteBySubWorkSheetId(subWorkSheet.getId());
        } else {
            nextTodoStepRepository.deleteByWorkSheetId(workSheet.getId());
        }
        // 更新第一级工序的待做信息
        int orderNumber = Objects.nonNull(subWorkSheet) ? subWorkSheet.getNumber() : workSheet.getNumber();
        this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, firstWsStepList, orderNumber, Boolean.TRUE, Boolean.FALSE);
    }

    /**
     * 处理批量工序下交场景
     *
     * @param workSheet       工单
     * @param subWorkSheet    子工单
     * @param wsStepList      工单工序快照列表
     * @param batchWorkDetail 批量详情
     * <AUTHOR>
     * @since 1.8.1
     */
    private void handleBatchStepTransfer(WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, BatchWorkDetail batchWorkDetail, boolean rollBack) {
        // 查找当前工序
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(batchWorkDetail.getStep().getId())).findFirst().get();
        // 更新当前工序及相关工序的待做数据
        updateRelatedNextTodoSteps(workSheet, subWorkSheet, wsStepList, currWsStep, rollBack);
        // 如果当前工序没有后置工序，直接返回
        if (StringUtils.isBlank(currWsStep.getAfterStepId())) {
            if (Objects.nonNull(subWorkSheet)) {
                nextTodoStepRepository.deleteBySubWorkSheetId(subWorkSheet.getId());
            } else {
                nextTodoStepRepository.deleteByWorkSheetId(workSheet.getId());
            }
            return;
        }
        // 更新后置工序的待做信息
        updateNextLevelStepsTodoInfo(workSheet, subWorkSheet, wsStepList, currWsStep, batchWorkDetail, rollBack);
    }


    /**
     * 删除当前工序及相关工序的待做数据
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param wsStepList   工单工序快照列表
     * @param currWsStep   当前工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void updateRelatedNextTodoSteps(WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, WsStep currWsStep, boolean rollBack) {
        if (currWsStep.getTransferType() == TransferModeEnum.INDEPENDENT.getValue()) {
            // 独立工序只删除当前待工序数据
            updateNextTodoStepForSingleStep(workSheet, subWorkSheet, currWsStep, rollBack);
        } else {
            // 非独立工序删除同层级所有待做工序数据
            updateNextTodoStepForParallelSteps(workSheet, subWorkSheet, wsStepList, currWsStep, rollBack);
        }
    }


    /**
     * 删除单个工序的待做数据
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param currWsStep   当前工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void updateNextTodoStepForSingleStep(WorkSheet workSheet, SubWorkSheet subWorkSheet, WsStep currWsStep, boolean rollBack) {
        if (Objects.nonNull(subWorkSheet)) {
            if (!rollBack) {
                nextTodoStepRepository.deleteBySubWorkSheetIdAndStepId(subWorkSheet.getId(), currWsStep.getStep().getId());
            } else {
                nextTodoStepRepository.recoveryBySubWorkSheetIdAndStepId(subWorkSheet.getId(), currWsStep.getStep().getId());
            }
        } else {
            if (!rollBack) {
                nextTodoStepRepository.deleteByWorkSheetIdAndStepId(workSheet.getId(), currWsStep.getStep().getId());
            } else {
                nextTodoStepRepository.recoveryByWorkSheetIdAndStepId(workSheet.getId(), currWsStep.getStep().getId());
            }
        }
    }

    /**
     * 删除并行工序的待做数据
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param wsStepList   工单工序快照
     * @param currWsStep   当前工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void updateNextTodoStepForParallelSteps(WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, WsStep currWsStep, boolean rollBack) {
        // 获取同层级并行工序
        List<WsStep> parallelWsStepList = commonService.findSamePreStepParallelWsSteps(wsStepList, currWsStep);
        if (CollectionUtils.isEmpty(parallelWsStepList)) {
            parallelWsStepList = new ArrayList<>();
        }
        // 添加当前工序
        parallelWsStepList.add(currWsStep);
        // 提取工序ID列表
        List<Long> stepIds = parallelWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
        // 批量删除待做数据
        if (Objects.nonNull(subWorkSheet)) {
            if (!rollBack) {
                nextTodoStepRepository.deleteBySubWorkSheetIdAndStepIdIn(subWorkSheet.getId(), stepIds);
            } else {
                nextTodoStepRepository.recoveryBySubWorkSheetIdAndStepIdIn(subWorkSheet.getId(), stepIds);
            }
        } else {
            if (!rollBack) {
                nextTodoStepRepository.deleteByWorkSheetIdAndStepIdIn(workSheet.getId(), stepIds);
            } else {
                nextTodoStepRepository.recoverByWorkSheetIdAndStepIdIn(workSheet.getId(), stepIds);
            }
        }
    }

    /**
     * 更新后置工序的待做信息
     */
    private void updateNextLevelStepsTodoInfo(WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, WsStep currWsStep, BatchWorkDetail batchWorkDetail, boolean rollBack) {

        // 获取后置工序列表
        List<WsStep> childFirstLevelWsStepList = wsStepList.stream().filter(wsStep -> currWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).toList();
        //后置工序的流转方式(0:连续流转;1:独立流转;2:分拆流转)
        int transferMode = childFirstLevelWsStepList.get(Constants.INT_ZERO).getTransferType();
        if (childFirstLevelWsStepList.size() > Constants.INT_ONE) {
            // 更新后置工序的待做信息，投产数为当前工序的待流转数
            this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, childFirstLevelWsStepList, batchWorkDetail.getTransferNumber(), Boolean.TRUE, rollBack);
        }else {
            //获取后置工序相同的同层级并行工序列表
            List<WsStep> sameAfterStepParallelWsStepList = commonService.findSameAfterStepParallelWsSteps(wsStepList, currWsStep);
            transferMode = CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) ? transferMode : sameAfterStepParallelWsStepList.get(Constants.INT_ZERO).getTransferType();
            //1，如果后置工序的前置工序(除自身外)只有一个的话那么直接更新后置工序的待投产信息即可
            if (CollectionUtils.isEmpty(sameAfterStepParallelWsStepList)) {
                // 更新后置工序的待做信息，投产数为当前工序的待流转数
                this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, childFirstLevelWsStepList, batchWorkDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                return;
            }
            List<Long> otherSameAfterStepParallelStepIdList = sameAfterStepParallelWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
            List<BatchWorkDetail> batchWorkDetailList = Objects.nonNull(subWorkSheet)
                    ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), otherSameAfterStepParallelStepIdList, Constants.LONG_ZERO)
                    : batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), otherSameAfterStepParallelStepIdList, Constants.LONG_ZERO);
            boolean allStepFinish = batchWorkDetailList.size() == sameAfterStepParallelWsStepList.size();
            //并行的独立工序那么后置工序是前置工序里最小待流转数进行往下流转
            if (transferMode == TransferModeEnum.INDEPENDENT.getValue() && allStepFinish){
                batchWorkDetailList.add(batchWorkDetail);
                BatchWorkDetail minTransferNumberBatchWorkDetail = batchWorkDetailList.stream().min(Comparator.comparing(BatchWorkDetail::getTransferNumber)).get();
                if(minTransferNumberBatchWorkDetail.getTransferNumber() == Constants.INT_ZERO){
                    return;
                }
                List<SnWorkDetail> snWorkDetailList = Objects.nonNull(subWorkSheet) ? snWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(),otherSameAfterStepParallelStepIdList,Constants.LONG_ZERO)
                        : snWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(),otherSameAfterStepParallelStepIdList,Constants.LONG_ZERO);
                if(!CollectionUtils.isEmpty(snWorkDetailList)){
                    snWorkDetailList.forEach(snWorkDetail -> {
                        if (snWorkDetail.getResult() != Constants.INT_ZERO) {
                            this.updateNextTodoInfo(workSheet, subWorkSheet, Objects.nonNull(snWorkDetail.getContainerDetail())?snWorkDetail.getContainerDetail().getContainerCode():null, snWorkDetail.getSn(), childFirstLevelWsStepList,
                                    snWorkDetail.getResult(), true, rollBack);
                        }
                    });
                    return;
                }
                this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, childFirstLevelWsStepList, batchWorkDetailList.stream().min(Comparator.comparing(BatchWorkDetail::getTransferNumber)).get().getTransferNumber(), Boolean.TRUE, rollBack);
                return;
            }
            //连续的并行工序都完成了那么后置工序取最后一个完成的工序流转数进行往下流转，如果没完成则更新连续的并行工序的待做工序
            if(transferMode == TransferModeEnum.CONTINUOUS.getValue() && allStepFinish){
                batchWorkDetailList.add(batchWorkDetail);
                this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, childFirstLevelWsStepList, batchWorkDetailList.stream().max(Comparator.comparing(BatchWorkDetail::getId)).get().getTransferNumber(), Boolean.TRUE, rollBack);
                return;
            }else if(transferMode == TransferModeEnum.CONTINUOUS.getValue()){
                List<WsStep> nextWsStepList = CollectionUtils.isEmpty(batchWorkDetailList) ? sameAfterStepParallelWsStepList:sameAfterStepParallelWsStepList.stream().filter(wsStep -> batchWorkDetailList.stream().noneMatch(batchWorkDetail1 -> batchWorkDetail1.getStep().getId().equals(wsStep.getStep().getId()))).toList();
                this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, nextWsStepList, batchWorkDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                return;
            }
            //分拆的并行工序那么后置工序取所有工序流转数之和进行往下流转
            if(transferMode == TransferModeEnum.SPLIT.getValue() && allStepFinish){
                batchWorkDetailList.add(batchWorkDetail);
                this.updateNextTodoInfo(workSheet, subWorkSheet, null, null, childFirstLevelWsStepList, batchWorkDetailList.stream().map(BatchWorkDetail::getTransferNumber).reduce(Constants.INT_ZERO, Integer::sum), Boolean.TRUE, rollBack);
            }
        }
    }


    /**
     * 容器下交时更新待下个可能的做工序信息
     *
     * @param productWorkSheetId 投产粒度工单ID（主要用于分布式锁）
     * @param workSheet          工单
     * @param subWorkSheet       子工单
     * @param wsStepList         工单工序快照
     * @param containerDetail    容器详情
     * @param rollBack           是否回退工序
     * <AUTHOR>
     * @since 1.8.1
     */
    @Klock(keys = {"#productWorkSheetId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public void updateNextTodoStepInfoWhenContainerStep(Long productWorkSheetId, WorkSheet workSheet, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, ContainerDetail containerDetail, boolean rollBack) {
        try {
            WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(containerDetail.getBatchWorkDetail().getStep().getId())).findFirst().orElse(null);
            if (Objects.isNull(currWsStep)) {
                return;
            }
            //获取前置工序相同的同层级并行工序列表
            List<WsStep> parallelWsStepList = commonService.findSamePreStepParallelWsSteps(wsStepList, currWsStep);
            parallelWsStepList = CollectionUtils.isEmpty(parallelWsStepList) ? new ArrayList<>() : parallelWsStepList;
            if (!CollectionUtils.isEmpty(parallelWsStepList)) {
                int transferMode = parallelWsStepList.get(Constants.INT_ZERO).getTransferType();
                //如果并行工序是非独立流转的那么都得修改并行工序的容器待做数据及对应的批量工序待做数据
                if (transferMode != TransferModeEnum.INDEPENDENT.getValue()) {
                    this.reduceNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, parallelWsStepList, containerDetail.getInputNumber(), Boolean.TRUE, rollBack);
                }
            }
            //无论如何都会去更新当前工序的容器待做数据及对应的批量工序待做数据
            this.reduceNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, List.of(currWsStep), containerDetail.getInputNumber(), Boolean.TRUE, rollBack);
            //如果当前生产完的工序存在后置工序那么后置工序的投产数默认为前置工序的待流转数
            if (StringUtils.isBlank(currWsStep.getAfterStepId())) {
                nextTodoStepRepository.deleteByContainerCode(containerDetail.getContainerCode());
                return;
            }
            //常规后置工序ID列表
            List<Long> customAfterStepIdList = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).toList();
            List<ContainerDetail> nextContainerWorkDetailList = Objects.nonNull(subWorkSheet) ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdInAndDeleted(subWorkSheet.getId(), customAfterStepIdList, Constants.LONG_ZERO)
                    : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndDeleted(workSheet.getId(), customAfterStepIdList, Constants.LONG_ZERO);
            //常规后置工序快照列表
            List<WsStep> childFirstLevelWsStepList = wsStepList.stream().filter(wsStep -> currWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).toList();
            //后置工序的流转方式(0:连续流转;1:独立流转;2:分拆流转)
            int transferMode = childFirstLevelWsStepList.get(Constants.INT_ZERO).getTransferType();
            if (customAfterStepIdList.size() > Constants.INT_ONE) {
                //如果后置工序一个都没做或者是后置工序为非连续流转的工序，那么更新所有后置工序的待做数量
                if (CollectionUtils.isEmpty(nextContainerWorkDetailList) || transferMode != TransferModeEnum.CONTINUOUS.getValue()) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, childFirstLevelWsStepList, containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                } else {
                    //如果后置工序是连续流转的那么更新后置工序最先做的工序待做数量
                    ContainerDetail latestParallelContainerDetail = nextContainerWorkDetailList.stream().min(Comparator.comparing(ContainerDetail::getId)).get();
                    this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(),
                            null, childFirstLevelWsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(latestParallelContainerDetail.getBatchWorkDetail().getStep().getId())).toList(),
                            containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                }
            } else {
                //如果后置工序只有一个时需要考虑并行工序的情况
                List<WsStep> sameAfterStepParallelWsStepList = commonService.findSameAfterStepParallelWsSteps(wsStepList, currWsStep);
                transferMode = CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) ? transferMode : sameAfterStepParallelWsStepList.get(Constants.INT_ZERO).getTransferType();
                //1，如果后置工序的前置工序(除自身外)只有一个的话那么直接更新后置工序的待投产信息即可
                if (CollectionUtils.isEmpty(sameAfterStepParallelWsStepList)) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, childFirstLevelWsStepList, containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                    return;
                }
                //2. 如果后置工序的前置工序(除自身外)有多个且工序流转模式是分拆流转那么直接更新后置工序的待投产信息即可
                if(transferMode == TransferModeEnum.SPLIT.getValue()){
                    this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, childFirstLevelWsStepList, containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                    return;
                }
                List<Long> afterParallelStepIdList = sameAfterStepParallelWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
                nextContainerWorkDetailList = CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) ? null : Objects.nonNull(subWorkSheet) ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdInAndDeleted(subWorkSheet.getId(), afterParallelStepIdList, Constants.LONG_ZERO)
                        : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndDeleted(workSheet.getId(), afterParallelStepIdList, Constants.LONG_ZERO);
                //3.如果后置工序的前置工序(除自身外)有多个且工序流转模式是连续流转且后置工序的前置工序(除自身外)的其他工序都未投产时，那么更新后置工序的(除自身外)前置工序的待投产工序信息
                if (transferMode == TransferModeEnum.CONTINUOUS.getValue() && CollectionUtils.isEmpty(nextContainerWorkDetailList)) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, sameAfterStepParallelWsStepList, containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                }
                //4. 如果后置工序的前置工序(除自身外)有多个且工序流转模式是连续流转且后置工序的前置工序(除自身外)的其他工序存在投产时，那么更新后置工序的(除自身外)前置工序最先投产的工序的待投产工序信息
                else if (transferMode == TransferModeEnum.CONTINUOUS.getValue()) {
                    List<ContainerDetail> currContainerCompletedParallelContainerDetailList = nextContainerWorkDetailList.stream().filter(nextContainerWorkDetail -> nextContainerWorkDetail.getContainerCode().equals(containerDetail.getContainerCode()) && afterParallelStepIdList.contains(nextContainerWorkDetail.getBatchWorkDetail().getStep().getId())).toList();
                    if (!CollectionUtils.isEmpty(currContainerCompletedParallelContainerDetailList) && currContainerCompletedParallelContainerDetailList.size() == afterParallelStepIdList.size()) {
                        this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null, childFirstLevelWsStepList, containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                    } else {
                        ContainerDetail latestParallelContainerDetail = nextContainerWorkDetailList.stream().min(Comparator.comparing(ContainerDetail::getId)).get();
                        if (latestParallelContainerDetail.getId() > containerDetail.getId()) {
                            this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail.getContainerCode(), null,
                                    sameAfterStepParallelWsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(latestParallelContainerDetail.getBatchWorkDetail().getStep().getId())).toList(),
                                    containerDetail.getTransferNumber(), Boolean.TRUE, rollBack);
                        }
                    }
                }
                //5.如果是独立的工序必须要等并行的独立工序都做完才能更新后置工序待投产信息（要取独立并行工序里待流转数最小的工序对应的容器进行往下流转）
                else if (transferMode == TransferModeEnum.INDEPENDENT.getValue() && !CollectionUtils.isEmpty(nextContainerWorkDetailList)
                        && nextContainerWorkDetailList.stream().anyMatch(nextContainerDetail -> nextContainerDetail.getBatchWorkDetail().getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName())) {
                    List<ContainerDetail> finalNextContainerWorkDetailList = nextContainerWorkDetailList;
                    nextContainerWorkDetailList.stream().min(Comparator.comparing(containerDetail1 -> containerDetail1.getBatchWorkDetail().getTransferNumber())).ifPresent(latestParallelContainerDetail -> {
                        List<WsStep> minTransferNumbereWsStepList = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(latestParallelContainerDetail.getBatchWorkDetail().getStep().getId())).toList();
                        finalNextContainerWorkDetailList.stream().filter(nextContainerWorkDetail -> nextContainerWorkDetail.getBatchWorkDetail().getId().equals(latestParallelContainerDetail.getBatchWorkDetail().getId())).forEach(containerDetail1 -> {
                            this.updateNextTodoInfo(workSheet, subWorkSheet, containerDetail1.getContainerCode(), null, minTransferNumbereWsStepList, containerDetail1.getTransferNumber(), Boolean.TRUE, rollBack);
                        });
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 单支下交时更新待下个可能的做工序信息
     *
     * @param productWorkSheetId 投产粒度工单ID（主要用于分布式锁）
     * @param workSheet          工单
     * @param subWorkSheet       子工单
     * @param wsStepList         工序列表
     * @param snWorkDetail       工序SN
     * @param reworkTime         重工次数
     * @param singleOnlineRepair 是否单支在线返工
     * @param rollBack           是否回退工序
     * <AUTHOR>
     * @since 1.8.1
     */
    @Klock(keys = {"#productWorkSheetId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public void updateNextStepTodoInfoWhenSingleStep(Long productWorkSheetId, WorkSheet workSheet, SubWorkSheet subWorkSheet,
                                                     List<WsStep> wsStepList, SnWorkDetail snWorkDetail, int reworkTime, boolean singleOnlineRepair, boolean rollBack) {
        try {
            WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(snWorkDetail.getStep().getId())).findFirst().orElse(null);
            if (Objects.isNull(currWsStep)) {
                return;
            }
            String currContainerCode = Objects.nonNull(snWorkDetail.getContainerDetail()) ? snWorkDetail.getContainerDetail().getContainerCode() : null;
            //获取前置工序相同的同层级并行工序列表
            List<WsStep> parallelWsStepList = commonService.findSamePreStepParallelWsSteps(wsStepList, currWsStep);
            parallelWsStepList = CollectionUtils.isEmpty(parallelWsStepList) ? new ArrayList<>() : parallelWsStepList;
            if (!CollectionUtils.isEmpty(parallelWsStepList)) {
                int transferMode = parallelWsStepList.get(Constants.INT_ZERO).getTransferType();
                //如果并行工序是非独立流转的那么都得修改并行工序的容器待做数据及对应的批量工序待做数据
                if (transferMode != TransferModeEnum.INDEPENDENT.getValue()) {
                    this.reduceNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), parallelWsStepList, Constants.INT_ONE, !singleOnlineRepair, rollBack);
                }
            }
            //无论如何都会去更新当前工序的容器待做数据及对应的批量工序待做数据
            this.reduceNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), List.of(currWsStep), Constants.INT_ONE, !singleOnlineRepair, rollBack);
            //如果当前生产完的工序存在后置工序那么后置工序的投产数默认为前置工序的待流转数
            if (StringUtils.isBlank(currWsStep.getAfterStepId())) {
                nextTodoStepRepository.deleteBySn(snWorkDetail.getSn());
                return;
            }
            //如果当前SN不合格了则直接返回
            if (snWorkDetail.getResult() == Constants.INT_ZERO && currWsStep.getTransferType() != TransferModeEnum.INDEPENDENT.getValue()) {
                return;
            }
            //常规后置工序ID列表
            List<Long> customAfterStepIdList = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).toList();
            //常规后置工序快照列表
            List<WsStep> childFirstLevelWsStepList = wsStepList.stream().filter(wsStep -> currWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).toList();
            //后置工序的流转方式(0:连续流转;1:独立流转;2:分拆流转)
            int transferMode = childFirstLevelWsStepList.get(Constants.INT_ZERO).getTransferType();
            List<SnWorkDetail> nextSnWorkDetailList = Objects.nonNull(subWorkSheet) ? snWorkDetailRepository.findBySnAndSubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(snWorkDetail.getSn(), subWorkSheet.getId(), customAfterStepIdList, reworkTime, Constants.LONG_ZERO)
                    : snWorkDetailRepository.findBySnAndWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(snWorkDetail.getSn(), workSheet.getId(), customAfterStepIdList, reworkTime, Constants.LONG_ZERO);

            if (customAfterStepIdList.size() > Constants.INT_ONE) {
                //如果后置工序一个都没做或者是后置工序为非连续流转的工序，那么更新所有后置工序的待做数量
                if (CollectionUtils.isEmpty(nextSnWorkDetailList) || transferMode != TransferModeEnum.CONTINUOUS.getValue()) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), childFirstLevelWsStepList, Constants.INT_ONE, !singleOnlineRepair, rollBack);
                } else {
                    //如果后置工序是连续流转的那么更新后置工序最先做的工序待做数量
                    SnWorkDetail latestParallelSnWorkDetail = nextSnWorkDetailList.stream().min(Comparator.comparing(SnWorkDetail::getId)).get();
                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode,
                            snWorkDetail.getSn(), childFirstLevelWsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(latestParallelSnWorkDetail.getStep().getId())).toList(),
                            Constants.INT_ONE, !singleOnlineRepair, rollBack);
                }
            } else {
                //如果后置工序只有一个时需要考虑并行工序的情况
                List<WsStep> sameAfterStepParallelWsStepList = commonService.findSameAfterStepParallelWsSteps(wsStepList, currWsStep);
                transferMode = CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) ? transferMode : sameAfterStepParallelWsStepList.get(Constants.INT_ZERO).getTransferType();
                //1，如果后置工序的前置工序(除自身外)没有同层级的其他并行工序的话那么直接更新后置工序的待投产信息即可
                if (CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) && snWorkDetail.getResult() != Constants.INT_ZERO) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), childFirstLevelWsStepList, Constants.INT_ONE, !singleOnlineRepair, rollBack);
                    return;
                }
                //2. 如果后置工序的前置工序(除自身外)有多个且工序流转模式是分拆流转那么直接更新后置工序的待投产信息即可
                if (transferMode == TransferModeEnum.SPLIT.getValue() && snWorkDetail.getResult() != Constants.INT_ZERO) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), childFirstLevelWsStepList, Constants.INT_ONE, !singleOnlineRepair, rollBack);
                    return;
                }
                List<Long> afterParallelStepIdList = new ArrayList<>(sameAfterStepParallelWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList());
                List<SnWorkDetail> otherParallelSnWorkDetailList = CollectionUtils.isEmpty(sameAfterStepParallelWsStepList) ? null : Objects.nonNull(subWorkSheet) ? snWorkDetailRepository.findBySnAndSubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(snWorkDetail.getSn(), subWorkSheet.getId(), afterParallelStepIdList, reworkTime, Constants.LONG_ZERO)
                        : snWorkDetailRepository.findBySnAndWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(snWorkDetail.getSn(), workSheet.getId(), afterParallelStepIdList, reworkTime, Constants.LONG_ZERO);
                //3.如果后置工序的前置工序(除自身外)有多个且工序流转模式是连续流转且后置工序的前置工序(除自身外)的其他工序都未投产时，那么更新后置工序的(除自身外)前置工序的待投产工序信息
                if (transferMode == TransferModeEnum.CONTINUOUS.getValue() && CollectionUtils.isEmpty(otherParallelSnWorkDetailList) && snWorkDetail.getResult() != Constants.INT_ZERO) {
                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), sameAfterStepParallelWsStepList, Constants.INT_ONE, !singleOnlineRepair, rollBack);
                }
                //4. 如果后置工序的前置工序(除自身外)有多个且工序流转模式是连续流转且后置工序的前置工序(除自身外)的其他工序存在投产时，那么更新后置工序的(除自身外)前置工序最先投产的工序的待投产工序信息
                else if (transferMode == TransferModeEnum.CONTINUOUS.getValue()) {
                    List<SnWorkDetail> currSnCompletedParallelContainerDetailList = otherParallelSnWorkDetailList.stream().filter(nextSnWorkDetail -> nextSnWorkDetail.getSn().equals(snWorkDetail.getSn()) && afterParallelStepIdList.contains(nextSnWorkDetail.getStep().getId())).toList();
                    if (!CollectionUtils.isEmpty(currSnCompletedParallelContainerDetailList) && currSnCompletedParallelContainerDetailList.size() == afterParallelStepIdList.size()) {
                        this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), childFirstLevelWsStepList, Constants.INT_ONE, Boolean.TRUE, rollBack);
                    } else {
                        SnWorkDetail latestParallelSnWorkDetail = otherParallelSnWorkDetailList.stream().min(Comparator.comparing(SnWorkDetail::getId)).get();
                        if (latestParallelSnWorkDetail.getId() > snWorkDetail.getId() && snWorkDetail.getResult() != Constants.INT_ZERO) {
                            this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(),
                                    sameAfterStepParallelWsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(latestParallelSnWorkDetail.getStep().getId())).toList(),
                                    Constants.INT_ONE, !singleOnlineRepair, rollBack);
                        }
                    }
                }
                //5.如果是独立的工序必须要等并行的独立工序都做完才能更新后置工序待投产信息（要取独立并行工序里待流转数最小的工序对应的容器进行往下流转）
                else if (transferMode == TransferModeEnum.INDEPENDENT.getValue()) {
                    if (!singleOnlineRepair) {
                        afterParallelStepIdList.add(currWsStep.getStep().getId());
                        List<BatchWorkDetail> parallelBatchWorkDetailList = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), afterParallelStepIdList, Constants.LONG_ZERO)
                                : batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), afterParallelStepIdList, Constants.LONG_ZERO);
                        if (!CollectionUtils.isEmpty(parallelBatchWorkDetailList) && parallelBatchWorkDetailList.size() == afterParallelStepIdList.size()
                                && parallelBatchWorkDetailList.stream().allMatch(batchWorkDetail -> batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName())) {
                            BatchWorkDetail minTransferNumberBatchWorkDetail = parallelBatchWorkDetailList.stream().min(Comparator.comparing(BatchWorkDetail::getTransferNumber)).get();
                            List<SnWorkDetail> transferNextSnWorkDetailList = Objects.nonNull(subWorkSheet) ? snWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(subWorkSheet.getId(), Collections.singletonList(minTransferNumberBatchWorkDetail.getStep().getId()), snWorkDetail.getReworkTime(), Constants.LONG_ZERO)
                                    : snWorkDetailRepository.findByWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(workSheet.getId(), Collections.singletonList(minTransferNumberBatchWorkDetail.getStep().getId()), snWorkDetail.getReworkTime(), Constants.LONG_ZERO);
                            if(CollectionUtils.isEmpty(transferNextSnWorkDetailList)){
                                transferNextSnWorkDetailList = Objects.nonNull(subWorkSheet) ? snWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(subWorkSheet.getId(), afterParallelStepIdList, snWorkDetail.getReworkTime(), Constants.LONG_ZERO)
                                        : snWorkDetailRepository.findByWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(workSheet.getId(), afterParallelStepIdList, snWorkDetail.getReworkTime(), Constants.LONG_ZERO);
                            }
                            transferNextSnWorkDetailList.forEach(transferNextSnWorkDetail -> {
                                if (snWorkDetail.getResult() != Constants.INT_ZERO) {
                                    this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, transferNextSnWorkDetail.getSn(), childFirstLevelWsStepList,
                                            transferNextSnWorkDetail.getResult(), true, rollBack);
                                }
                            });
                        }
                    }
                    //如果是纯单支在线返工那么只需要判断当前SN在多个独立工序上是否都生产过
                    else if (!CollectionUtils.isEmpty(otherParallelSnWorkDetailList) && otherParallelSnWorkDetailList.size() == afterParallelStepIdList.size() && snWorkDetail.getResult() != Constants.INT_ZERO) {
                        this.updateNextTodoInfo(workSheet, subWorkSheet, currContainerCode, snWorkDetail.getSn(), childFirstLevelWsStepList, snWorkDetail.getResult(), false, rollBack);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新下个可能的待做工序信息
     *
     * @param workSheet             工单
     * @param subWorkSheet          子工单
     * @param containerCode         容器编码
     * @param sn                    SN
     * @param mayNextTodoWsStepList 可能的待做工序列表
     * @param number                数量
     * @param updateBatchTodoStep   是否更新批量待做工序
     * @param rollBack              是否回退工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void updateNextTodoInfo(WorkSheet workSheet, SubWorkSheet subWorkSheet, String containerCode, String sn, List<WsStep> mayNextTodoWsStepList, int number, boolean updateBatchTodoStep, boolean rollBack) {
        mayNextTodoWsStepList.forEach(mayNextTodoWsStep -> {
            if (updateBatchTodoStep && (StringUtils.isNotBlank(sn) || StringUtils.isNotBlank(containerCode))) {
                NextTodoStep nextBatchTodoStep = Objects.nonNull(subWorkSheet) ?
                        nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndContainerCodeIsNullAndSnIsNullAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndContainerCodeIsNullAndSnIsNullAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), Constants.LONG_ZERO);
                nextBatchTodoStep = Objects.nonNull(nextBatchTodoStep) ? nextBatchTodoStep : new NextTodoStep();
                this.plusNextTodoInfo(nextBatchTodoStep, workSheet, subWorkSheet, null, null, mayNextTodoWsStep, number, rollBack);
            }
            if (StringUtils.isNotBlank(containerCode)) {
                NextTodoStep nextContainerTodoStep = Objects.nonNull(subWorkSheet)
                        ? nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndContainerCodeAndSnIsNullAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), containerCode, Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndContainerCodeAndSnIsNullAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), containerCode, Constants.LONG_ZERO);
                nextContainerTodoStep = Objects.nonNull(nextContainerTodoStep) ? nextContainerTodoStep : new NextTodoStep();
                this.plusNextTodoInfo(nextContainerTodoStep, workSheet, subWorkSheet, containerCode, null, mayNextTodoWsStep, number, rollBack);
            }
            if (StringUtils.isNotBlank(sn)) {
                NextTodoStep nextContainerTodoStep = Objects.nonNull(subWorkSheet) ?
                        nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndSnAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), sn, Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndSnAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), sn, Constants.LONG_ZERO);
                nextContainerTodoStep = Objects.nonNull(nextContainerTodoStep) ? nextContainerTodoStep : new NextTodoStep();
                this.plusNextTodoInfo(nextContainerTodoStep, workSheet, subWorkSheet, null, sn, mayNextTodoWsStep, number, rollBack);
            }
            if (StringUtils.isBlank(containerCode) && StringUtils.isBlank(sn)) {
                this.plusNextTodoInfo(new NextTodoStep(), workSheet, subWorkSheet, containerCode, sn, mayNextTodoWsStep, number, Boolean.FALSE);
            }
        });
    }

    /**
     * 增加下个可能的待做工序信息
     *
     * @param nextTodoStep   待做工序信息
     * @param workSheet      工单
     * @param subWorkSheet   子工单
     * @param containerCode  容器编码
     * @param sn             SN
     * @param nextTodoWsStep 待做工序
     * @param number         数量
     * @param rollBack       是否回退工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void plusNextTodoInfo(NextTodoStep nextTodoStep, WorkSheet workSheet, SubWorkSheet subWorkSheet, String containerCode, String sn, WsStep nextTodoWsStep, int number, boolean rollBack) {
        if (rollBack && Objects.isNull(nextTodoStep.getId())) {
            return;
        }
        nextTodoStep.setWorkSheet(workSheet)
                .setSubWorkSheet(subWorkSheet)
                .setContainerCode(containerCode)
                .setSn(sn)
                .setStep(nextTodoWsStep.getStep())
                .setDeleted(Constants.LONG_ZERO);
        if (rollBack) {
            nextTodoStep.setNumber(nextTodoStep.getNumber() - number);
        } else {
            nextTodoStep.setNumber(nextTodoStep.getNumber() + number);
        }
        nextTodoStepRepository.save(nextTodoStep);
    }

    /**
     * 减少下个可能的待做工序信息
     *
     * @param workSheet             工单
     * @param subWorkSheet          子工单
     * @param containerCode         容器编码
     * @param sn                    SN
     * @param mayNextTodoWsStepList 可能的待做工序列表
     * @param number                数量
     * @param updateBatchTodoStep   是否更新批量待做工序
     * @param rollBack              是否回退工序
     * <AUTHOR>
     * @since 1.8.1
     */
    private void reduceNextTodoInfo(WorkSheet workSheet, SubWorkSheet subWorkSheet, String containerCode, String sn, List<WsStep> mayNextTodoWsStepList, int number, boolean updateBatchTodoStep, boolean rollBack) {
        mayNextTodoWsStepList.forEach(mayNextTodoWsStep -> {
            //1.容器下交或者SN下交都需要更新批量的待做数据
            if (updateBatchTodoStep && (StringUtils.isNotBlank(sn) || StringUtils.isNotBlank(containerCode))) {
                NextTodoStep nextBatchTodoStep = Objects.nonNull(subWorkSheet) ?
                        nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndContainerCodeIsNullAndSnIsNullAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndContainerCodeIsNullAndSnIsNullAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), Constants.LONG_ZERO);
                if (Objects.nonNull(nextBatchTodoStep)) {
                    if (!rollBack) {
                        nextBatchTodoStep.setNumber(nextBatchTodoStep.getNumber() - number);
                    } else {
                        nextBatchTodoStep.setNumber(nextBatchTodoStep.getNumber() + number);
                    }
                    if (nextBatchTodoStep.getNumber() <= Constants.INT_ZERO) {
                        nextBatchTodoStep.setDeleted(Constants.LONG_ZERO);
                    }
                    nextTodoStepRepository.save(nextBatchTodoStep);
                }
            }
            //2.容器下交或者SN下交都需要更新容器的待做数据
            if (StringUtils.isNotBlank(containerCode)) {
                NextTodoStep nextContainerTodoStep = Objects.nonNull(subWorkSheet)
                        ? nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndContainerCodeAndSnIsNullAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), containerCode, Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndContainerCodeAndSnIsNullAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), containerCode, Constants.LONG_ZERO);
                if (Objects.nonNull(nextContainerTodoStep)) {
                    if (!rollBack) {
                        nextContainerTodoStep.setNumber(nextContainerTodoStep.getNumber() - number);
                    } else {
                        nextContainerTodoStep.setNumber(nextContainerTodoStep.getNumber() + number);
                    }
                    nextTodoStepRepository.save(nextContainerTodoStep);
                }
            }
            //3.SN下交都需要更新SN的待做数据
            if (StringUtils.isNotBlank(sn)) {
                NextTodoStep nextContainerTodoStep = Objects.nonNull(subWorkSheet) ?
                        nextTodoStepRepository.findBySubWorkSheetIdAndStepIdAndSnAndDeleted(subWorkSheet.getId(), mayNextTodoWsStep.getStep().getId(), sn, Constants.LONG_ZERO)
                        : nextTodoStepRepository.findByWorkSheetIdAndStepIdAndSnAndDeleted(workSheet.getId(), mayNextTodoWsStep.getStep().getId(), sn, Constants.LONG_ZERO);
                if (Objects.nonNull(nextContainerTodoStep)) {
                    if (!rollBack) {
                        nextContainerTodoStep.setNumber(nextContainerTodoStep.getNumber() - number);
                    } else {
                        nextContainerTodoStep.setNumber(nextContainerTodoStep.getNumber() + number);
                    }
                    nextTodoStepRepository.save(nextContainerTodoStep);
                }
            }
        });
    }
}
