package net.airuima.rbase.service.base.pedigree;

import net.airuima.rbase.domain.base.pedigree.PedigreeStepFppConfig;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepFppConfigRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 产品谱系工序FPP配置Service
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepFppConfigService extends CommonJpaService<PedigreeStepFppConfig> {
    private final PedigreeStepFppConfigRepository pedigreeStepFppConfigRepository;

    public PedigreeStepFppConfigService(PedigreeStepFppConfigRepository pedigreeStepFppConfigRepository) {
        this.pedigreeStepFppConfigRepository = pedigreeStepFppConfigRepository;
    }

    @Override
    public Page<PedigreeStepFppConfig> find(Specification<PedigreeStepFppConfig> spec, Pageable pageable) {
        return pedigreeStepFppConfigRepository.findAll(spec,pageable);
    }

    @Override
    public List<PedigreeStepFppConfig> find(Specification<PedigreeStepFppConfig> spec) {
        return pedigreeStepFppConfigRepository.findAll(spec);
    }

    @Override
    public Page<PedigreeStepFppConfig> findAll(Pageable pageable) {
        return pedigreeStepFppConfigRepository.findAll(pageable);
    }

}
