package net.airuima.rbase.service.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.constant.WorkSheetCategoryEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterial;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.material.CheckMaterialDetailDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWmsWorkSheetMaterialBatchDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialBatchDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单领料明细表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsMaterialBatchService extends CommonJpaService<WsMaterialBatch> {
    private static final String WS_MATERIAL_BATCH_ENTITY_GRAPH = "wsMaterialBatchEntityGraph";
    private final WsMaterialBatchRepository wsMaterialBatchRepository;

    private final WorkSheetRepository workSheetRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    private final WsCheckMaterialDetailService wsCheckMaterialDetailService;
    @Autowired
    private PIWipLedgerService wipLedgerServices;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private WsCheckMaterialRepository wsCheckMaterialRepository;
    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;
    @Autowired
    private IWsCheckMaterialDetailService[] wsCheckMaterialDetailServices;

    public WsMaterialBatchService(WsMaterialBatchRepository wsMaterialBatchRepository, WorkSheetRepository workSheetRepository, WsCheckMaterialDetailService wsCheckMaterialDetailService) {
        this.wsMaterialBatchRepository = wsMaterialBatchRepository;
        this.workSheetRepository = workSheetRepository;
        this.wsCheckMaterialDetailService = wsCheckMaterialDetailService;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsMaterialBatch> find(Specification<WsMaterialBatch> spec, Pageable pageable) {
        return wsMaterialBatchRepository.findAll(spec, pageable, new NamedEntityGraph(WS_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsMaterialBatch> find(Specification<WsMaterialBatch> spec) {
        return wsMaterialBatchRepository.findAll(spec, new NamedEntityGraph(WS_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsMaterialBatch> findAll(Pageable pageable) {
        return wsMaterialBatchRepository.findAll(pageable, new NamedEntityGraph(WS_MATERIAL_BATCH_ENTITY_GRAPH));
    }

    @Override
    public <S extends WsMaterialBatch> S save(S entity) {
        entity.setWorkSheet(workSheetRepository.getReferenceById(entity.getWorkSheet().getId()));
        return super.save(entity);
    }

    /**
     * 同步工单领料批次数据 （目前仅sap使用）
     * operate:0:领料;1:退料
     *
     * @param syncWorkSheetMaterialBatchDtoList 上传的工单物料批次信息
     * @return List<SapBaseDTO>
     * <AUTHOR>
     * @date 2021-06-04
     **/
    public List<SyncResultDTO> workSheetMaterialBatchSync(List<SyncWorkSheetMaterialBatchDTO> syncWorkSheetMaterialBatchDtoList) {
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncWorkSheetMaterialBatchDtoList)) {
            syncWorkSheetMaterialBatchDtoList.forEach(syncWorkSheetMaterialBatchDto -> {
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(syncWorkSheetMaterialBatchDto.getSerialNumber(), Constants.LONG_ZERO);
                if (!workSheetOptional.isPresent()) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialBatchDto.getId(), Constants.INT_TWO, "工单不存在"));
                    return;
                }
                MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(syncWorkSheetMaterialBatchDto.getMaterialCode(), Constants.LONG_ZERO).orElse(null);
                if (null == materialDto) {
                    syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialBatchDto.getId(), Constants.INT_TWO, "物料不存在"));
                    return;
                }
                WsMaterialBatch wsMaterialBatch = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheetOptional.get().getId(), materialDto.getId(), syncWorkSheetMaterialBatchDto.getMaterialBatch(), Constants.LONG_ZERO).orElse(new WsMaterialBatch());
                //若为退料需要验证批次剩余数量是否足够扣减
                if (syncWorkSheetMaterialBatchDto.getOperate() == Constants.INT_ONE) {
                    if (null == wsMaterialBatch.getId()) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialBatchDto.getId(), Constants.INT_TWO, "领料记录不存在"));
                        return;
                    }
                    if (wsMaterialBatch.getLeftNumber() < syncWorkSheetMaterialBatchDto.getNumber()) {
                        syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialBatchDto.getId(), Constants.INT_TWO, "物料批次剩余数量不够扣减"));
                        return;
                    }
                }
                // 处理线边仓工单库存、新增台账
                wipLedgerServices.processWarehouseAndSaveLedger(workSheetOptional.get().getWorkLine(), wsMaterialBatch, syncWorkSheetMaterialBatchDto.getNumber(), syncWorkSheetMaterialBatchDto.getOperate() == Constants.INT_ZERO ? WipLedgerOperationEnum.WIP_LEDGER_OPERATION_STORE.getCategory() : WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory(), null);
                //领料时需要更新领料总数和剩余总数，退料时需要扣减剩余总数
                if (syncWorkSheetMaterialBatchDto.getOperate() == Constants.INT_ZERO) {
                    wsMaterialBatch.setWorkSheet(workSheetOptional.get()).setMaterialId(materialDto.getId()).setBatch(syncWorkSheetMaterialBatchDto.getMaterialBatch()).setLeftNumber(wsMaterialBatch.getLeftNumber() + syncWorkSheetMaterialBatchDto.getNumber()).setNumber(wsMaterialBatch.getNumber() + syncWorkSheetMaterialBatchDto.getNumber()).setDeleted(Constants.LONG_ZERO);
                } else {
                    wsMaterialBatch.setLeftNumber(wsMaterialBatch.getLeftNumber() - syncWorkSheetMaterialBatchDto.getNumber());
                }
                wsMaterialBatchRepository.save(wsMaterialBatch);
                syncResultDtoList.add(new SyncResultDTO(syncWorkSheetMaterialBatchDto.getId(), Constants.INT_ONE, ""));
            });
        }
        return syncResultDtoList;
    }


    /**
     * 通过工单id 获取对应的工单领料记录
     *
     * @param wsId 工单id
     * @return List<WsMaterialBatch>
     * <AUTHOR>
     * @date 2022/3/25
     */
    @Transactional(readOnly = true)
    public List<WsMaterialBatch> findByWsMaterialBatch(Long wsId, Long materialId) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()) {
                wsId = subWorkSheetOptional.get().getWorkSheet().getId();
            }
        }
        workSheetOptional = workSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            return Lists.newArrayList();
        }
        if (workSheetOptional.get().getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
            wsId = wsReworkOptional.isPresent() ? wsReworkOptional.get().getOriginalWorkSheet().getId() : wsId;
        }
        if (null != materialId) {
            return wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndDeleted(wsId, materialId, Constants.LONG_ZERO);
        }
        return wsMaterialBatchRepository.findByWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
    }

    /**
     * 通过工单id、物料id获取对应的工单领料记录
     *
     * @param wsId       工单id
     * @param materialId 物料id
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch>
     * <AUTHOR>
     * @date 2023/3/25
     */
    @Transactional(readOnly = true)
    public List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndDeleted(Long wsId, Long materialId) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()) {
                wsId = subWorkSheetOptional.get().getWorkSheet().getId();
            }
        }
        workSheetOptional = workSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            return Lists.newArrayList();
        }
        if (workSheetOptional.get().getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
            wsId = wsReworkOptional.isPresent() ? wsReworkOptional.get().getOriginalWorkSheet().getId() : wsId;
        }
        return wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndDeleted(wsId, materialId, Constants.LONG_ZERO);
    }

    /**
     * 同步工单物料库存
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncWmsWorkSheetMaterialBatch(SyncWmsWorkSheetMaterialBatchDTO syncWmsWorkSheetMaterialBatchDTOList) {
        String result = rbaseSysCodeProxy.findByCode(Constants.KEY_AUTO_CHECK_MATERIAL);
        boolean autoCheckMaterial = StringUtils.isNotBlank(result) && Boolean.parseBoolean(result);
        Optional<WorkSheet> workSheet = workSheetRepository.findBySerialNumberAndDeleted(syncWmsWorkSheetMaterialBatchDTOList.getSerialNumber(), Constants.LONG_ZERO);
        WsCheckMaterial wsCheckMaterial = new WsCheckMaterial().setCode(UUID.randomUUID().toString().substring(Constants.INT_ZERO, Constants.INT_EIGHT)).setWorkSheet(workSheet.orElse(null)).setCheckDate(LocalDateTime.now()).setType(workSheet.filter(ws -> ws.getCategory() != WorkSheetCategoryEnum.NORMAL_CATEGORY.getCategory()).map(ws -> Constants.INT_ONE).orElse(Constants.INT_ZERO)).setStatus(Constants.INT_ZERO);
        wsCheckMaterial = wsCheckMaterialRepository.save(wsCheckMaterial);
        List<WsCheckMaterialDetail> list = new ArrayList<>();
        for (SyncWmsWorkSheetMaterialBatchDTO.MaterialDetail detail : syncWmsWorkSheetMaterialBatchDTOList.getDetails()) {
            WsCheckMaterialDetail wsCheckMaterialDetail = new WsCheckMaterialDetail().setWsCheckMaterial(wsCheckMaterial).setWorkSheet(workSheet.orElse(null)).setMaterialId(detail.getMaterialId()).setBatch(detail.getLot()).setCheckedNumber(Constants.DOUBLE_ZERRO).setUncheckNumber(detail.getNumber().doubleValue());
            list.add(wsCheckMaterialDetail);
        }
        List<WsCheckMaterialDetail> detailList = wsCheckMaterialDetailRepository.saveAll(list);
        List<CheckMaterialDetailDTO> dtoList = detailList.stream().map(wsCheckMaterialDetail -> new CheckMaterialDetailDTO().setId(wsCheckMaterialDetail.getId()).setNum(wsCheckMaterialDetail.getUncheckNumber())).toList();
        if (autoCheckMaterial) {
            wsCheckMaterialDetailServices[0].saveCheckMaterialDetail(dtoList);
        }
    }

    /**
     * 通过物料批次获取领料记录
     */
    @Transactional(readOnly = true)
    public List<WsMaterialBatch> findByMaterialBatch(String materialBatch) {
        return wsMaterialBatchRepository.findByBatchAndDeleted(materialBatch, Constants.LONG_ZERO);
    }
}
