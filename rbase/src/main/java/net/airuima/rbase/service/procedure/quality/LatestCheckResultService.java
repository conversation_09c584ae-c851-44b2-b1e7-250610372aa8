package net.airuima.rbase.service.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.LatestCheckResult;
import net.airuima.rbase.repository.procedure.quality.LatestCheckResultRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测结果最新状态Service
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class LatestCheckResultService extends CommonJpaService<LatestCheckResult> {
    private static final String LATEST_CHECK_RESULT_ENTITY_GRAPH = "latestCheckResultEntityGraph";
    private final LatestCheckResultRepository latestCheckResultRepository;

    public LatestCheckResultService(LatestCheckResultRepository latestCheckResultRepository) {
        this.latestCheckResultRepository = latestCheckResultRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<LatestCheckResult> find(Specification<LatestCheckResult> spec, Pageable pageable) {
        return latestCheckResultRepository.findAll(spec,pageable,new NamedEntityGraph(LATEST_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<LatestCheckResult> find(Specification<LatestCheckResult> spec) {
        return latestCheckResultRepository.findAll(spec,new NamedEntityGraph(LATEST_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<LatestCheckResult> findAll(Pageable pageable) {
        return latestCheckResultRepository.findAll(pageable,new NamedEntityGraph(LATEST_CHECK_RESULT_ENTITY_GRAPH));
    }

}
