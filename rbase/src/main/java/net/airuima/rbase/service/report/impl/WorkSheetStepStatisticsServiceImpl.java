package net.airuima.rbase.service.report.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.ProductionPlan;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.report.PedigreeStepStatistics;
import net.airuima.rbase.domain.procedure.report.WorkSheetStepStatistics;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepPassRateRepository;
import net.airuima.rbase.repository.procedure.aps.ProductionPlanRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.report.PedigreeStepStatisticsRepository;
import net.airuima.rbase.repository.procedure.report.WorkSheetStepStatisticsRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsRequestDTO;
import net.airuima.rbase.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WorkSheetStepStatisticsServiceImpl implements IWorkSheetStepStatisticsService {

    @Autowired
    private WorkSheetStepStatisticsRepository workSheetStepStatisticsRepository;
    @Autowired
    private PedigreeStepStatisticsRepository pedigreeStepStatisticsRepository;
    @Autowired
    private PedigreeStepPassRateRepository pedigreeStepPassRateRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ProductionPlanRepository productionPlanRepository;


    /**
     * 自动分单、新增工单或修改工单时初始化更新生产在制看板数据
     *
     * @param workSheet           工单
     * @param subWorkSheetList    子工单列表
     * @param subWsProductionMode 是否子工单投产
     */
    @Override
    public void initWorkSheetStepStatisticsInfo(WorkSheet workSheet, List<SubWorkSheet> subWorkSheetList, boolean subWsProductionMode) {
        //工序生产在制看板目前只针对正常单
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wsStepList)) {
            return;
        }
        List<WsStep> firstWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).toList();
        if (CollectionUtils.isEmpty(firstWsStepList)) {
            return;
        }
        //若最小投产粒度为子工单，则按照子工单进行更新生产看板数据
        if (subWsProductionMode && CollectionUtils.isNotEmpty(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> firstWsStepList.forEach(wsStep -> {
                WorkSheetStepStatistics workSheetStepStatistics = workSheetStepStatisticsRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new WorkSheetStepStatistics());
                PedigreeStepStatistics pedigreeStepStatistics = pedigreeStepStatisticsRepository.findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(workSheet.getPedigree().getId(), workSheet.getWorkLine().getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new PedigreeStepStatistics());
                pedigreeStepStatistics.setPedigree(workSheet.getPedigree()).setWorkLine(workSheet.getWorkLine()).setStep(wsStep.getStep()).setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() - workSheetStepStatistics.getOnlineNumber() + (int) Math.ceil(NumberUtils.multiply(subWorkSheet.getNumber(), wsStep.getInputRate()).doubleValue())).setDeleted(Constants.LONG_ZERO);
                pedigreeStepStatistics = pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
                workSheetStepStatistics.setSubWorkSheet(subWorkSheet).setStep(wsStep.getStep()).setOnlineNumber((int) Math.ceil(NumberUtils.multiply(subWorkSheet.getNumber(), wsStep.getInputRate()).doubleValue())).setPedigreeStepStatistics(pedigreeStepStatistics).setDeleted(Constants.LONG_ZERO);
                workSheetStepStatisticsRepository.save(workSheetStepStatistics);
            }));
            return;
        }
        //若最小投产粒度为工单，则按照工单进行更新生产看板数据
        if (!subWsProductionMode) {
            wsStepList.forEach(wsStep -> {
                WorkSheetStepStatistics workSheetStepStatistics = workSheetStepStatisticsRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new WorkSheetStepStatistics());
                PedigreeStepStatistics pedigreeStepStatistics = pedigreeStepStatisticsRepository.findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(workSheet.getPedigree().getId(), workSheet.getWorkLine().getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new PedigreeStepStatistics());
                pedigreeStepStatistics.setPedigree(workSheet.getPedigree()).setWorkLine(workSheet.getWorkLine()).setStep(wsStep.getStep()).setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() - workSheetStepStatistics.getOnlineNumber() + (int) Math.ceil(NumberUtils.multiply(workSheet.getNumber(), wsStep.getInputRate()).doubleValue())).setDeleted(Constants.LONG_ZERO);
                pedigreeStepStatistics = pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
                workSheetStepStatistics.setWorkSheet(workSheet).setPedigreeStepStatistics(pedigreeStepStatistics).setStep(wsStep.getStep()).setOnlineNumber((int) Math.ceil(NumberUtils.multiply(workSheet.getNumber(), wsStep.getInputRate()).doubleValue())).setDeleted(Constants.LONG_ZERO);
                workSheetStepStatisticsRepository.save(workSheetStepStatistics);
            });
        }
    }

    /**
     * 工单完成、异常结单或者取消时需要删除在线看板与工单相关的信息
     *
     * @param workSheet           工单
     * @param subWorkSheetList    子工单列表
     * @param subWsProductionMode 投产粒度
     */
    @Override
    public void deleteWorkSheetStepStatisticsInfo(WorkSheet workSheet, List<SubWorkSheet> subWorkSheetList, boolean subWsProductionMode) {
        //工序生产在制看板目前只针对正常单
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        if (subWsProductionMode && CollectionUtils.isNotEmpty(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> {
                List<WorkSheetStepStatistics> workSheetStepStatisticsList = workSheetStepStatisticsRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
                if (CollectionUtils.isNotEmpty(workSheetStepStatisticsList)) {
                    workSheetStepStatisticsList.forEach(workSheetStepStatistics -> {
                        PedigreeStepStatistics pedigreeStepStatistics = workSheetStepStatistics.getPedigreeStepStatistics();
                        pedigreeStepStatistics.setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() - workSheetStepStatistics.getOnlineNumber());
                        pedigreeStepStatistics.setInputNumber(pedigreeStepStatistics.getInputNumber() - workSheetStepStatistics.getInputNumber());
                        pedigreeStepStatistics.setQualifiedNumber(pedigreeStepStatistics.getQualifiedNumber() - workSheetStepStatistics.getQualifiedNumber());
                        pedigreeStepStatistics.setUnqualifiedNumber(pedigreeStepStatistics.getUnqualifiedNumber() - workSheetStepStatistics.getUnqualifiedNumber());
                        pedigreeStepStatistics.setTransferNumber(pedigreeStepStatistics.getTransferNumber() - workSheetStepStatistics.getTransferNumber());
                        pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
                    });
                    workSheetStepStatisticsRepository.deleteAllInBatch(workSheetStepStatisticsList);
                }
            });
            return;
        }
        if (!subWsProductionMode) {
            List<WorkSheetStepStatistics> workSheetStepStatisticsList = workSheetStepStatisticsRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (CollectionUtils.isNotEmpty(workSheetStepStatisticsList)) {
                workSheetStepStatisticsList.forEach(workSheetStepStatistics -> {
                    PedigreeStepStatistics pedigreeStepStatistics = workSheetStepStatistics.getPedigreeStepStatistics();
                    pedigreeStepStatistics.setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() - workSheetStepStatistics.getOnlineNumber());
                    pedigreeStepStatistics.setInputNumber(pedigreeStepStatistics.getInputNumber() - workSheetStepStatistics.getInputNumber());
                    pedigreeStepStatistics.setQualifiedNumber(pedigreeStepStatistics.getQualifiedNumber() - workSheetStepStatistics.getQualifiedNumber());
                    pedigreeStepStatistics.setUnqualifiedNumber(pedigreeStepStatistics.getUnqualifiedNumber() - workSheetStepStatistics.getUnqualifiedNumber());
                    pedigreeStepStatistics.setTransferNumber(pedigreeStepStatistics.getTransferNumber() - workSheetStepStatistics.getTransferNumber());
                    pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
                });
                workSheetStepStatisticsRepository.deleteAllInBatch(workSheetStepStatisticsList);
            }
        }
    }

    /**
     * 保存生产数据或者回退生产数据时更新当前生产工序在制看板及更新后置工序生产看板
     *
     * @param workSheet         工单
     * @param subWorkSheet      子工单
     * @param step              工序
     * @param inputNumber       投入数
     * @param qualifiedNumber   合格数
     * @param unqualifiedNumber 不合格数
     * @param transferNumber    流转数
     * <AUTHOR>
     * @date 2023/12/19
     */
    @Override
    public void updateWorkSheetStepStatisticsInfo(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step, List<WsStep> wsStepList, int inputNumber, int qualifiedNumber, int unqualifiedNumber, int transferNumber) {
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        //如果当前工序没有在制数据则不更新
        WorkSheetStepStatistics currWorkSheetStepStatistics = Objects.nonNull(subWorkSheet) ? workSheetStepStatisticsRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null) : workSheetStepStatisticsRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(currWorkSheetStepStatistics)) {
            return;
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            return;
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(step.getId())).findFirst().orElse(null);
        if (Objects.isNull(currWsStep)) {
            return;
        }
        //获取后置工序列表
        List<WsStep> nextWsStepList = StringUtils.isNotBlank(currWsStep.getAfterStepId()) ? wsStepList.stream().filter(wsStep -> currWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).toList() : new ArrayList<>();
        //更新当前工序的在制数据
        currWorkSheetStepStatistics.setTransferNumber(currWorkSheetStepStatistics.getTransferNumber() + transferNumber)
                .setInputNumber(currWorkSheetStepStatistics.getInputNumber() + inputNumber)
                .setQualifiedNumber(currWorkSheetStepStatistics.getQualifiedNumber() + qualifiedNumber)
                .setUnqualifiedNumber(currWorkSheetStepStatistics.getUnqualifiedNumber() + unqualifiedNumber)
                .setOnlineNumber(currWorkSheetStepStatistics.getOnlineNumber() - inputNumber);
        if (currWorkSheetStepStatistics.getOnlineNumber() < 0) {
            currWorkSheetStepStatistics.setOnlineNumber(Constants.INT_ZERO);
        }
        workSheetStepStatisticsRepository.save(currWorkSheetStepStatistics);
        PedigreeStepStatistics currPedigreeStepStatistics = currWorkSheetStepStatistics.getPedigreeStepStatistics();
        if (Objects.isNull(currPedigreeStepStatistics)) {
            currPedigreeStepStatistics = pedigreeStepStatisticsRepository.findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(workSheet.getPedigree().getId(), workSheet.getWorkLine().getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        }
        if (Objects.nonNull(currPedigreeStepStatistics)) {
            if (Objects.isNull(currWorkSheetStepStatistics.getPedigreeStepStatistics())) {
                currWorkSheetStepStatistics.setPedigreeStepStatistics(currPedigreeStepStatistics);
                workSheetStepStatisticsRepository.save(currWorkSheetStepStatistics);
            }
            currPedigreeStepStatistics.setTransferNumber(currPedigreeStepStatistics.getTransferNumber() + transferNumber)
                    .setInputNumber(currPedigreeStepStatistics.getInputNumber() + inputNumber)
                    .setQualifiedNumber(currPedigreeStepStatistics.getQualifiedNumber() + qualifiedNumber)
                    .setUnqualifiedNumber(currPedigreeStepStatistics.getUnqualifiedNumber() + unqualifiedNumber)
                    .setOnlineNumber(currPedigreeStepStatistics.getOnlineNumber() - inputNumber);
            if (currPedigreeStepStatistics.getOnlineNumber() < 0) {
                currPedigreeStepStatistics.setOnlineNumber(Constants.INT_ZERO);
            }
            pedigreeStepStatisticsRepository.save(currPedigreeStepStatistics);
        }
        //更新下个工序的在制数据
        if (CollectionUtils.isNotEmpty(nextWsStepList)) {
            nextWsStepList.forEach(wsStep -> {
                int nextOnlineNumber = (int) Math.ceil(NumberUtils.multiply(transferNumber, wsStep.getInputRate()).doubleValue());
                WorkSheetStepStatistics workSheetStepStatistics = Objects.nonNull(subWorkSheet) ? workSheetStepStatisticsRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new WorkSheetStepStatistics()) : workSheetStepStatisticsRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new WorkSheetStepStatistics());
                workSheetStepStatistics.setStep(wsStep.getStep()).setWorkSheet(Objects.nonNull(subWorkSheet) ? null : workSheet).setSubWorkSheet(subWorkSheet).setOnlineNumber(workSheetStepStatistics.getOnlineNumber() + nextOnlineNumber).setDeleted(Constants.LONG_ZERO);
                WorkLine workLine = Objects.nonNull(subWorkSheet) && Objects.nonNull(subWorkSheet.getWorkLine()) ? subWorkSheet.getWorkLine() : workSheet.getWorkLine();
                PedigreeStepStatistics pedigreeStepStatistics = Objects.nonNull(workSheetStepStatistics.getId()) ? workSheetStepStatistics.getPedigreeStepStatistics() : pedigreeStepStatisticsRepository.findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(workSheet.getPedigree().getId(), workLine.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new PedigreeStepStatistics());
                pedigreeStepStatistics.setStep(wsStep.getStep()).setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() + nextOnlineNumber).setPedigree(workSheet.getPedigree()).setWorkLine(workLine).setDeleted(Constants.LONG_ZERO);
                pedigreeStepStatistics = pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
                workSheetStepStatistics.setPedigreeStepStatistics(pedigreeStepStatistics);
                workSheetStepStatisticsRepository.save(workSheetStepStatistics);
            });
        }
    }

    /**
     * 工单转工艺时更新在制看板数据
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param originStep   原始工序
     * @param targetStep   转换后工序
     */
    @Override
    public void updateWorkSheetStepStatisticsWhenConvertWorkFlow(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step originStep, Step targetStep) {
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        //如果当前工序没有在制数据则不更新
        WorkSheetStepStatistics currWorkSheetStepStatistics = Objects.nonNull(subWorkSheet) ? workSheetStepStatisticsRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), originStep.getId(), Constants.LONG_ZERO).orElse(null) : workSheetStepStatisticsRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), originStep.getId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(currWorkSheetStepStatistics)) {
            return;
        }
        currWorkSheetStepStatistics.setStep(targetStep);
        PedigreeStepStatistics currPedigreeStepStatistics = currWorkSheetStepStatistics.getPedigreeStepStatistics();
        currPedigreeStepStatistics.setOnlineNumber(currPedigreeStepStatistics.getOnlineNumber() - currWorkSheetStepStatistics.getOnlineNumber());
        pedigreeStepStatisticsRepository.save(currPedigreeStepStatistics);
        PedigreeStepStatistics pedigreeStepStatistics = pedigreeStepStatisticsRepository.findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(currPedigreeStepStatistics.getPedigree().getId(), currPedigreeStepStatistics.getWorkLine().getId(), targetStep.getId(), Constants.LONG_ZERO).orElse(new PedigreeStepStatistics());
        pedigreeStepStatistics.setOnlineNumber(pedigreeStepStatistics.getOnlineNumber() + currWorkSheetStepStatistics.getOnlineNumber())
                .setPedigree(currPedigreeStepStatistics.getPedigree()).setWorkLine(currPedigreeStepStatistics.getWorkLine()).setStep(targetStep).setDeleted(Constants.LONG_ZERO);
        pedigreeStepStatistics = pedigreeStepStatisticsRepository.save(pedigreeStepStatistics);
        currWorkSheetStepStatistics.setPedigreeStepStatistics(pedigreeStepStatistics);
        workSheetStepStatisticsRepository.save(currWorkSheetStepStatistics);
    }

    /**
     * 获取产品在制看板数据
     *
     * @param onlineProductStatisticsRequestDTO 请求参数
     * @return net.airuima.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    @Override
    public OnlineProductStatisticsResultDTO findOnlineProductStatisticsInfo(OnlineProductStatisticsRequestDTO onlineProductStatisticsRequestDTO) {
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        //是否通过工单获取在制看板
        boolean pedigreeProductionMode = Boolean.TRUE;
        List<WorkSheetStepStatistics> workSheetStepStatisticsList;
        if (subWsProductionMode) {
            workSheetStepStatisticsList = workSheetStepStatisticsRepository.findReportInfoByConditionWhenSubWorkSheetMode(onlineProductStatisticsRequestDTO);
        } else {
            workSheetStepStatisticsList = workSheetStepStatisticsRepository.findReportInfoByConditionWhenWorkSheetMode(onlineProductStatisticsRequestDTO);
        }
        if (CollectionUtils.isEmpty(workSheetStepStatisticsList)) {
            return null;
        }
        if (Objects.nonNull(onlineProductStatisticsRequestDTO.getWorkSheetId())) {
            WorkSheet workSheet = subWsProductionMode ? workSheetStepStatisticsList.get(Constants.INT_ZERO).getSubWorkSheet().getWorkSheet() : workSheetStepStatisticsList.get(Constants.INT_ZERO).getWorkSheet();
            onlineProductStatisticsRequestDTO.setWorkLineId(workSheet.getWorkLine().getId()).setPedigreeId(workSheet.getPedigree().getId());
            pedigreeProductionMode = Boolean.FALSE;
        }
        //获取生产计划列表
        List<ProductionPlan> productionPlanList = productionPlanRepository.findByCondition(onlineProductStatisticsRequestDTO, LocalDate.now());
        //获取工序目标良率列表
        List<PedigreeStepPassRate> pedigreeStepPassRateList = pedigreeStepPassRateRepository.findByCondition(onlineProductStatisticsRequestDTO.getPedigreeId(), null, Constants.LONG_ZERO);
        //获取工序在制看板数据
        OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO = null;
        if (onlineProductStatisticsRequestDTO.getCategory() == Constants.INT_ZERO) {
            onlineProductStatisticsResultDTO = pedigreeProductionMode ?
                    findOnlineProductStepStatisticsInfo(workSheetStepStatisticsList, productionPlanList, pedigreeStepPassRateList, subWsProductionMode) :
                    findOnlineProductStepStatisticsInfoByOrder(workSheetStepStatisticsList, productionPlanList, pedigreeStepPassRateList, subWsProductionMode);
        } else {
            //获取工序组在制看板数据
            onlineProductStatisticsResultDTO = pedigreeProductionMode ? findOnlineProductStepGroupStatisticsInfo(workSheetStepStatisticsList, productionPlanList, pedigreeStepPassRateList, subWsProductionMode) :
                    findOnlineProductStepGroupStatisticsInfoByOrder(workSheetStepStatisticsList, productionPlanList, pedigreeStepPassRateList, subWsProductionMode);

        }
        // 重新构建产品生产在制看板结果数据DTO
        buildOnlineProductStatisticsResultDTO(onlineProductStatisticsResultDTO, subWsProductionMode);
        return onlineProductStatisticsResultDTO;
    }

    /**
     * 重新构建产品生产在制看板结果数据DTO
     *
     * @param onlineProductStatisticsResultDTO 产品生产在制看板结果数据DTO
     * @param subWsProductionMode              投产模式
     */
    public void buildOnlineProductStatisticsResultDTO(OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO, boolean subWsProductionMode) {
        if (subWsProductionMode && onlineProductStatisticsResultDTO != null && CollectionUtils.isNotEmpty(onlineProductStatisticsResultDTO.getPedigreeStepOnlineStatisticsDtoList())) {
            onlineProductStatisticsResultDTO.getPedigreeStepOnlineStatisticsDtoList().forEach(pedigreeStepOnlineStatisticsDTO -> {
                if (CollectionUtils.isNotEmpty(pedigreeStepOnlineStatisticsDTO.getWorksheetStepOnlineStatisticsDtoList())) {
                    // 按工单号分组（按"-"前段分组）
                    Map<String, List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO>> workSheetGroupMap =
                            pedigreeStepOnlineStatisticsDTO.getWorksheetStepOnlineStatisticsDtoList().stream()
                                    .collect(Collectors.groupingBy(dto -> {
                                        String serialNumber = dto.getSerialNumber();
                                        if (StringUtils.isNotBlank(serialNumber) && serialNumber.contains("-")) {
                                            return serialNumber.substring(0, serialNumber.lastIndexOf("-"));
                                        }
                                        return serialNumber;
                                    }));

                    // 重新构建工单列表
                    List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO> newWorksheetList = new ArrayList<>();
                    workSheetGroupMap.forEach((baseWorkSheetNo, worksheetList) -> {
                        OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                        worksheetStepOnlineStatisticsDTO.setSerialNumber(baseWorkSheetNo);
                        List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.SubWorksheetStepOnlineStatisticsDTO> statisticsDTOS = worksheetList.stream().map(value -> {
                            OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.SubWorksheetStepOnlineStatisticsDTO subWorksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.SubWorksheetStepOnlineStatisticsDTO();
                            subWorksheetStepOnlineStatisticsDTO.setSerialNumber(value.getSerialNumber());
                            subWorksheetStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(value.getStepOnlineStatisticsDtoList());
                            return subWorksheetStepOnlineStatisticsDTO;
                        }).toList();
                        worksheetStepOnlineStatisticsDTO.setSubWorksheetStepOnlineStatisticsDtoList(statisticsDTOS);
                        newWorksheetList.add(worksheetStepOnlineStatisticsDTO);
                    });
                    pedigreeStepOnlineStatisticsDTO.setWorksheetStepOnlineStatisticsDtoList(newWorksheetList);
                }
            });
        }
    }

    /**
     * 获取工序在制看板数据
     *
     * @param workSheetStepStatisticsList 工单工序生产在制数据列表
     * @param subWsProductionMode         生产模式
     * @return net.airuima.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    @Override
    public OnlineProductStatisticsResultDTO findOnlineProductStepStatisticsInfo(List<WorkSheetStepStatistics> workSheetStepStatisticsList, List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList, boolean subWsProductionMode) {
        OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO = new OnlineProductStatisticsResultDTO();
        Set<SubWorkSheet> subWorkSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getSubWorkSheet).collect(Collectors.toSet());
        Set<WorkSheet> workSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getWorkSheet).collect(Collectors.toSet());
        int allExpiredWorkSheetSize = subWsProductionMode ? subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size() :
                workSheetSet.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
        //总的在制工单/子工单个数及逾期个数赋值
        onlineProductStatisticsResultDTO.setOnlineWorkSheetNumber(subWsProductionMode ? subWorkSheetSet.size() : workSheetSet.size()).setOnlineExpiredWorkSheetNumber(allExpiredWorkSheetSize);
        //获取产品谱系工序在制数据列表以及按照工序分组来组装工序总的在制统计数据
        Set<PedigreeStepStatistics> pedigreeStepStatisticsSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getPedigreeStepStatistics).sorted(Comparator.comparingLong(PedigreeStepStatistics::getId)).collect(Collectors.toCollection(LinkedHashSet::new));
        Map<Step, List<PedigreeStepStatistics>> pedigreeStepStatisticsStepGroup = pedigreeStepStatisticsSet.stream().collect(Collectors.groupingBy(PedigreeStepStatistics::getStep, LinkedHashMap::new, Collectors.toList()));
        List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDTOS = new ArrayList<>();
        Map<String, String> stepGroupMap = new HashMap<>();
        pedigreeStepStatisticsStepGroup.forEach((step, pedigreeStepStatisticsList) -> {
            OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO stepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
            int onlineNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getOnlineNumber).sum();
            int inputNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getInputNumber).sum();
            int qualifiedNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getQualifiedNumber).sum();
            int unqualifiedNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getUnqualifiedNumber).sum();
            int transferNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getTransferNumber).sum();
            stepOnlineStatisticsDTO.setCode(step.getCode()).setName(step.getName())
                    .setInputNumber(inputNumber).setOnlineNumber(onlineNumber).setTransferNumber(transferNumber)
                    .setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setTargetPlanNumber(Constants.INT_ZERO);
            if (Objects.nonNull(step.getStepGroup())) {
                stepGroupMap.put(step.getCode(), step.getStepGroup().getCode());
            }
            //获取工序组生产计划
            if (CollectionUtils.isNotEmpty(productionPlanList) && Objects.nonNull(step.getStepGroup())) {
                stepOnlineStatisticsDTO.setTargetPlanNumber(productionPlanList.stream().filter(productionPlan -> productionPlan.getStepGroup().getCode().equals(step.getStepGroup().getCode())).toList().stream().mapToInt(ProductionPlan::getPlanNumber).sum());
            }
            //获取工序组目标良率
            if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList) && Objects.nonNull(step.getStepGroup())) {
                pedigreeStepPassRateList.stream().filter(pedigreeStepPassRate -> pedigreeStepPassRate.getStepGroup().getCode().equals(step.getStepGroup().getCode())).findFirst().ifPresent(pedigreeStepPassRate -> {
                    stepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                });
            }
            stepOnlineStatisticsDTOS.add(stepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);
        //按照产品谱系进行分组来组装产品谱系工序总的在制统计数据
        Map<Pedigree, List<PedigreeStepStatistics>> pedigreeStepStatisticsPedigreeGroup = pedigreeStepStatisticsSet.stream().collect(Collectors.groupingBy(PedigreeStepStatistics::getPedigree, LinkedHashMap::new, Collectors.toList()));
        Map<Pedigree, List<WorkSheetStepStatistics>> workSheetStepStatisticsPedigreeGroup = workSheetStepStatisticsList.stream().collect(Collectors.groupingBy(workSheetStepStatistics -> workSheetStepStatistics.getPedigreeStepStatistics().getPedigree(), LinkedHashMap::new, Collectors.toList()));
        List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDTOList = new ArrayList<>();
        pedigreeStepStatisticsPedigreeGroup.forEach((pedigree, pedigreeStepStatisticsList) -> {
            OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO pedigreeStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO();
            pedigreeStepOnlineStatisticsDTO.setCode(pedigree.getCode()).setName(pedigree.getName()).setSpecification(pedigree.getSpecification());
            //产品谱系工序在制统计数据对应的工单工序在制统计数据列表
            List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDTOList = new ArrayList<>();
            int pedigreeWorkSheetSize;
            int pedigreeExpiredWorkSheetSize;
            //获取产品谱系工序在制统计数据中对应的在制工单/子工单总数及逾期总数，同时初始化对应工单/子工单列表
            if (subWsProductionMode) {
                List<SubWorkSheet> pedigreeSubWorkSheetList = subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getWorkSheet().getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeSubWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeSubWorkSheetList.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeSubWorkSheetList.forEach(subWorkSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(subWorkSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            } else {
                List<WorkSheet> pedigreeWorkSheetList = workSheetSet.stream().filter(workSheet -> workSheet.getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeWorkSheetList.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeWorkSheetList.forEach(workSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(workSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            }
            //组装产品谱系对应的在制工序数据
            pedigreeStepOnlineStatisticsDTO.setOnlineWorkSheetNumber(pedigreeWorkSheetSize).setOnlineExpiredWorkSheetNumber(pedigreeExpiredWorkSheetSize);
            List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList = new ArrayList<>();
            stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO onlinePedigreeStepStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                onlinePedigreeStepStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                List<PedigreeStepStatistics> stepMatchedList = pedigreeStepStatisticsList.stream().filter(pedigreeStepStatistics -> pedigreeStepStatistics.getStep().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                Integer onlineNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getOnlineNumber).sum() : null;
                Integer inputNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getInputNumber).sum() : null;
                Integer qualifiedNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getQualifiedNumber).sum() : null;
                Integer unqualifiedNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getUnqualifiedNumber).sum() : null;
                Integer transferNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getTransferNumber).sum() : null;
                onlinePedigreeStepStatisticsDTO.setOnlineNumber(onlineNumber).setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                //获取工序组生产计划
                if (CollectionUtils.isNotEmpty(productionPlanList) && stepGroupMap.containsKey(onlinePedigreeStepStatisticsDTO.getCode())) {
                    for (ProductionPlan productionPlan : productionPlanList) {
                        if (productionPlan.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && productionPlan.getStepGroup().getCode().equals(stepGroupMap.get(onlinePedigreeStepStatisticsDTO.getCode()))) {
                            onlinePedigreeStepStatisticsDTO.setTargetPlanNumber(productionPlan.getPlanNumber());
                            break;
                        }
                    }
                }
                //获取工序组目标良率
                if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList) && stepGroupMap.containsKey(onlinePedigreeStepStatisticsDTO.getCode())) {
                    for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                        if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(stepGroupMap.get(onlinePedigreeStepStatisticsDTO.getCode()))) {
                            onlinePedigreeStepStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                            break;
                        }
                    }
                }
                stepOnlineStatisticsDtoList.add(onlinePedigreeStepStatisticsDTO);
            });
            pedigreeStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDtoList);
            //组装产品谱系对应的工单/子工单在制工序数据
            List<WorkSheetStepStatistics> matchedWorkSheetStepStatisticsList = workSheetStepStatisticsPedigreeGroup.get(pedigree);
            worksheetStepOnlineStatisticsDTOList.forEach(worksheetStepOnlineStatisticsDTO -> {
                List<WorkSheetStepStatistics> matchedSerialNumberWorkSheetStepStatisticsList = matchedWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> subWsProductionMode ? workSheetStepStatistics.getSubWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber()) : workSheetStepStatistics.getWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber())).toList();
                List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> workSheetStepOnlineStatisticsDtoList = new ArrayList<>();
                stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                    OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO workSheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                    workSheetStepOnlineStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                    List<WorkSheetStepStatistics> matchedStepList = matchedSerialNumberWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> workSheetStepStatistics.getStep().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                    Integer onlineNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum() : null;
                    Integer inputNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum() : null;
                    Integer qualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum() : null;
                    Integer unqualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum() : null;
                    Integer transferNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum() : null;
                    workSheetStepOnlineStatisticsDTO.setOnlineNumber(onlineNumber).setUnqualifiedNumber(unqualifiedNumber).setQualifiedNumber(qualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                    //获取工序组目标良率
                    if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList) && stepGroupMap.containsKey(stepOnlineStatisticsDTO.getCode())) {
                        for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                            if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(stepGroupMap.get(stepOnlineStatisticsDTO.getCode()))) {
                                workSheetStepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                                break;
                            }
                        }
                    }
                    workSheetStepOnlineStatisticsDtoList.add(workSheetStepOnlineStatisticsDTO);
                });
                worksheetStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(workSheetStepOnlineStatisticsDtoList);
            });
            pedigreeStepOnlineStatisticsDTO.setWorksheetStepOnlineStatisticsDtoList(worksheetStepOnlineStatisticsDTOList);
            pedigreeStepOnlineStatisticsDTOList.add(pedigreeStepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setPedigreeStepOnlineStatisticsDtoList(pedigreeStepOnlineStatisticsDTOList);
        return onlineProductStatisticsResultDTO;
    }

    /**
     * 工单在制状态质量看板 工序
     *
     * @param workSheetStepStatisticsList 工单在制状态列表
     * @param productionPlanList          产品谱系计划产能
     * @param pedigreeStepPassRateList    产品谱系工序目标良率
     * @param subWsProductionMode         投产模式
     * @return OnlineProductStatisticsResultDTO
     */
    public OnlineProductStatisticsResultDTO findOnlineProductStepStatisticsInfoByOrder(List<WorkSheetStepStatistics> workSheetStepStatisticsList, List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList, boolean subWsProductionMode) {

        OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO = new OnlineProductStatisticsResultDTO();
        Set<SubWorkSheet> subWorkSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getSubWorkSheet).collect(Collectors.toSet());
        Set<WorkSheet> workSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getWorkSheet).collect(Collectors.toSet());
        int allExpiredWorkSheetSize = subWsProductionMode ? subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size() :
                workSheetSet.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
        //总的在制工单/子工单个数及逾期个数赋值
        onlineProductStatisticsResultDTO.setOnlineWorkSheetNumber(subWsProductionMode ? subWorkSheetSet.size() : workSheetSet.size()).setOnlineExpiredWorkSheetNumber(allExpiredWorkSheetSize);
        //通过（子）工单对应的 的生产数据pedigree 进行分组
        Map<Step, List<WorkSheetStepStatistics>> workSheetStepStatisticsStepGroup = workSheetStepStatisticsList.stream().collect(Collectors.groupingBy(WorkSheetStepStatistics::getStep));
        List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDTOS = new ArrayList<>();
        Map<String, String> stepGroupMap = new HashMap<>();
        workSheetStepStatisticsStepGroup.forEach((step, workSheetStepStatisticsStepList) -> {
            OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO stepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
            int onlineNumber = workSheetStepStatisticsStepList.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum();
            int inputNumber = workSheetStepStatisticsStepList.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum();
            int qualifiedNumber = workSheetStepStatisticsStepList.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum();
            int unqualifiedNumber = workSheetStepStatisticsStepList.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum();
            int transferNumber = workSheetStepStatisticsStepList.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum();
            stepOnlineStatisticsDTO.setCode(step.getCode()).setName(step.getName())
                    .setInputNumber(inputNumber).setOnlineNumber(onlineNumber).setTransferNumber(transferNumber)
                    .setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setTargetPlanNumber(Constants.INT_ZERO);
            if (Objects.nonNull(step.getStepGroup())) {
                stepGroupMap.put(step.getCode(), step.getStepGroup().getCode());
            }
            //获取工序组生产计划
            if (CollectionUtils.isNotEmpty(productionPlanList) && Objects.nonNull(step.getStepGroup())) {
                stepOnlineStatisticsDTO.setTargetPlanNumber(productionPlanList.stream().filter(productionPlan -> productionPlan.getStepGroup().getCode().equals(step.getStepGroup().getCode())).toList().stream().mapToInt(ProductionPlan::getPlanNumber).sum());
            }
            //获取工序组目标良率
            if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList) && Objects.nonNull(step.getStepGroup())) {
                pedigreeStepPassRateList.stream().filter(pedigreeStepPassRate -> pedigreeStepPassRate.getStepGroup().getCode().equals(step.getStepGroup().getCode())).findFirst().ifPresent(pedigreeStepPassRate -> {
                    stepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                });
            }
            stepOnlineStatisticsDTOS.add(stepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);

        //按照产品谱系进行分组来组装产品谱系工序总的在制统计数据
        Map<Pedigree, List<WorkSheetStepStatistics>> workSheetStepStatisticsPedigreeGroup = workSheetStepStatisticsList.stream().collect(Collectors.groupingBy(workSheetStepStatistics -> workSheetStepStatistics.getPedigreeStepStatistics().getPedigree(), LinkedHashMap::new, Collectors.toList()));
        List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDTOList = new ArrayList<>();
        workSheetStepStatisticsPedigreeGroup.forEach((pedigree, workSheetStepStatisticsPedigreeList) -> {
            OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO pedigreeStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO();
            pedigreeStepOnlineStatisticsDTO.setCode(pedigree.getCode()).setName(pedigree.getName()).setSpecification(pedigree.getSpecification());
            //产品谱系工序在制统计数据对应的工单工序在制统计数据列表
            List<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDTOList = new ArrayList<>();
            int pedigreeWorkSheetSize;
            int pedigreeExpiredWorkSheetSize;

            //获取产品谱系工序在制统计数据中对应的在制工单/子工单总数及逾期总数，同时初始化对应工单/子工单列表
            if (subWsProductionMode) {
                List<SubWorkSheet> pedigreeSubWorkSheetList = subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getWorkSheet().getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeSubWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeSubWorkSheetList.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeSubWorkSheetList.forEach(subWorkSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(subWorkSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            } else {
                List<WorkSheet> pedigreeWorkSheetList = workSheetSet.stream().filter(workSheet -> workSheet.getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeWorkSheetList.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeWorkSheetList.forEach(workSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(workSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            }
            //组装产品谱系对应的在制工序数据
            pedigreeStepOnlineStatisticsDTO.setOnlineWorkSheetNumber(pedigreeWorkSheetSize).setOnlineExpiredWorkSheetNumber(pedigreeExpiredWorkSheetSize);

            pedigreeStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);
            //组装产品谱系对应的工单/子工单在制工序数据
            List<WorkSheetStepStatistics> matchedWorkSheetStepStatisticsList = workSheetStepStatisticsPedigreeGroup.get(pedigree);
            worksheetStepOnlineStatisticsDTOList.forEach(worksheetStepOnlineStatisticsDTO -> {
                List<WorkSheetStepStatistics> matchedSerialNumberWorkSheetStepStatisticsList = matchedWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> subWsProductionMode ? workSheetStepStatistics.getSubWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber()) : workSheetStepStatistics.getWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber())).toList();
                List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> workSheetStepOnlineStatisticsDtoList = new ArrayList<>();
                stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                    OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO workSheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                    workSheetStepOnlineStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                    List<WorkSheetStepStatistics> matchedStepList = matchedSerialNumberWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> workSheetStepStatistics.getStep().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                    Integer onlineNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum() : null;
                    Integer inputNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum() : null;
                    Integer qualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum() : null;
                    Integer unqualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum() : null;
                    Integer transferNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum() : null;
                    workSheetStepOnlineStatisticsDTO.setOnlineNumber(onlineNumber).setUnqualifiedNumber(unqualifiedNumber).setQualifiedNumber(qualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                    //获取工序组目标良率
                    if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList) && stepGroupMap.containsKey(stepOnlineStatisticsDTO.getCode())) {
                        for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                            if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(stepGroupMap.get(stepOnlineStatisticsDTO.getCode()))) {
                                workSheetStepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                                break;
                            }
                        }
                    }
                    workSheetStepOnlineStatisticsDtoList.add(workSheetStepOnlineStatisticsDTO);
                });
                worksheetStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(workSheetStepOnlineStatisticsDtoList);
            });
            pedigreeStepOnlineStatisticsDTO.setWorksheetStepOnlineStatisticsDtoList(worksheetStepOnlineStatisticsDTOList);
            pedigreeStepOnlineStatisticsDTOList.add(pedigreeStepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setPedigreeStepOnlineStatisticsDtoList(pedigreeStepOnlineStatisticsDTOList);
        return onlineProductStatisticsResultDTO;
    }


    /**
     * 获取工序组别在制看板数据
     *
     * @param workSheetStepStatisticsList 工单工序生产在制数据列表
     * @param subWsProductionMode         生产模式
     * @return net.airuima.web.rest.report.dto.onlinestatistics.OnlineProductStatisticsResultDTO 在制看板数据
     */
    @Override
    public OnlineProductStatisticsResultDTO findOnlineProductStepGroupStatisticsInfo(List<WorkSheetStepStatistics> workSheetStepStatisticsList, List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList, boolean subWsProductionMode) {
        OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO = new OnlineProductStatisticsResultDTO();
        //获取产品谱系工序在制数据列表以及按照工序分组来组装工序总的在制统计数据
        LinkedHashSet<PedigreeStepStatistics> pedigreeStepStatisticsSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getPedigreeStepStatistics).collect(Collectors.toCollection(LinkedHashSet::new));
        pedigreeStepStatisticsSet = pedigreeStepStatisticsSet.stream().filter(pedigreeStepStatistics -> Objects.nonNull(pedigreeStepStatistics.getStep().getStepGroup())).collect(Collectors.toCollection(LinkedHashSet::new));
        if (CollectionUtils.isEmpty(pedigreeStepStatisticsSet)) {
            return onlineProductStatisticsResultDTO;
        }
        Set<SubWorkSheet> subWorkSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getSubWorkSheet).collect(Collectors.toSet());
        Set<WorkSheet> workSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getWorkSheet).collect(Collectors.toSet());
        int allExpiredWorkSheetSize = subWsProductionMode ? subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size() :
                workSheetSet.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
        //总的在制工单/子工单个数及逾期个数赋值
        onlineProductStatisticsResultDTO.setOnlineWorkSheetNumber(subWsProductionMode ? subWorkSheetSet.size() : workSheetSet.size()).setOnlineExpiredWorkSheetNumber(allExpiredWorkSheetSize);
        //按照工序组分组
        LinkedHashMap<StepGroup, List<PedigreeStepStatistics>> pedigreeStepStatisticsStepGroup = pedigreeStepStatisticsSet.stream().collect(Collectors.groupingBy(pedigreeStepStatistics -> pedigreeStepStatistics.getStep().getStepGroup(), LinkedHashMap::new, Collectors.toList()));
        LinkedList<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDTOS = new LinkedList<>();
        pedigreeStepStatisticsStepGroup.forEach((stepGroup, pedigreeStepStatisticsList) -> {
            OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO stepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
            int onlineNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getOnlineNumber).sum();
            int inputNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getInputNumber).sum();
            int qualifiedNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getQualifiedNumber).sum();
            int unqualifiedNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getUnqualifiedNumber).sum();
            int transferNumber = pedigreeStepStatisticsList.stream().mapToInt(PedigreeStepStatistics::getTransferNumber).sum();
            stepOnlineStatisticsDTO.setCode(stepGroup.getCode()).setName(stepGroup.getName())
                    .setInputNumber(inputNumber).setOnlineNumber(onlineNumber).setTransferNumber(transferNumber)
                    .setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setTargetPlanNumber(Constants.INT_ZERO);
            //获取工序组生产计划
            if (CollectionUtils.isNotEmpty(productionPlanList)) {
                stepOnlineStatisticsDTO.setTargetPlanNumber(productionPlanList.stream().filter(productionPlan -> productionPlan.getStepGroup().getCode().equals(stepGroup.getCode())).toList().stream().mapToInt(ProductionPlan::getPlanNumber).sum());
            }
            //获取工序组目标良率
            if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList)) {
                pedigreeStepPassRateList.stream().filter(pedigreeStepPassRate -> pedigreeStepPassRate.getStepGroup().getCode().equals(stepGroup.getCode())).findFirst().ifPresent(pedigreeStepPassRate -> {
                    stepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                });
            }
            stepOnlineStatisticsDTOS.add(stepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);
        //按照产品谱系进行分组来组装产品谱系工序组总的在制统计数据
        LinkedHashMap<Pedigree, List<PedigreeStepStatistics>> pedigreeStepStatisticsPedigreeGroup = pedigreeStepStatisticsSet.stream().collect(Collectors.groupingBy(PedigreeStepStatistics::getPedigree, LinkedHashMap::new, Collectors.toList()));
        LinkedHashMap<Pedigree, List<WorkSheetStepStatistics>> workSheetStepStatisticsPedigreeGroup = workSheetStepStatisticsList.stream().collect(Collectors.groupingBy(workSheetStepStatistics -> workSheetStepStatistics.getPedigreeStepStatistics().getPedigree(), LinkedHashMap::new, Collectors.toList()));
        LinkedList<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDTOList = new LinkedList<>();
        pedigreeStepStatisticsPedigreeGroup.forEach((pedigree, pedigreeStepStatisticsList) -> {
            OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO pedigreeStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO();
            pedigreeStepOnlineStatisticsDTO.setCode(pedigree.getCode()).setName(pedigree.getName()).setSpecification(pedigree.getSpecification());
            //产品谱系工序在制统计数据对应的工单工序在制统计数据列表
            LinkedList<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDTOList = new LinkedList<>();
            int pedigreeWorkSheetSize;
            int pedigreeExpiredWorkSheetSize;
            //获取产品谱系工序在制统计数据中对应的在制工单/子工单总数及逾期总数，同时初始化对应工单/子工单列表
            if (subWsProductionMode) {
                List<SubWorkSheet> pedigreeSubWorkSheetList = subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getWorkSheet().getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeSubWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeSubWorkSheetList.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeSubWorkSheetList.forEach(subWorkSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(subWorkSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            } else {
                List<WorkSheet> pedigreeWorkSheetList = workSheetSet.stream().filter(workSheet -> workSheet.getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeWorkSheetList.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeWorkSheetList.forEach(workSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(workSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            }
            //组装产品谱系对应的在制工序数据
            pedigreeStepOnlineStatisticsDTO.setOnlineWorkSheetNumber(pedigreeWorkSheetSize).setOnlineExpiredWorkSheetNumber(pedigreeExpiredWorkSheetSize);
            List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDtoList = new ArrayList<>();
            stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO onlinePedigreeStepStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                onlinePedigreeStepStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                List<PedigreeStepStatistics> stepMatchedList = pedigreeStepStatisticsList.stream().filter(pedigreeStepStatistics -> Objects.nonNull(pedigreeStepStatistics.getStep().getStepGroup()) && pedigreeStepStatistics.getStep().getStepGroup().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                Integer onlineNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getOnlineNumber).sum() : null;
                Integer inputNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getInputNumber).sum() : null;
                Integer qualifiedNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getQualifiedNumber).sum() : null;
                Integer unqualifiedNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getUnqualifiedNumber).sum() : null;
                Integer transferNumber = CollectionUtils.isNotEmpty(stepMatchedList) ? stepMatchedList.stream().mapToInt(PedigreeStepStatistics::getTransferNumber).sum() : null;
                onlinePedigreeStepStatisticsDTO.setOnlineNumber(onlineNumber).setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                //获取工序组生产计划
                if (CollectionUtils.isNotEmpty(productionPlanList)) {
                    for (ProductionPlan productionPlan : productionPlanList) {
                        if (productionPlan.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && productionPlan.getStepGroup().getCode().equals(onlinePedigreeStepStatisticsDTO.getCode())) {
                            onlinePedigreeStepStatisticsDTO.setTargetPlanNumber(productionPlan.getPlanNumber());
                            break;
                        }
                    }
                }
                //获取工序组目标良率
                if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList)) {
                    for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                        if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(onlinePedigreeStepStatisticsDTO.getCode())) {
                            onlinePedigreeStepStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                            break;
                        }
                    }
                }
                stepOnlineStatisticsDtoList.add(onlinePedigreeStepStatisticsDTO);
            });
            pedigreeStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDtoList);
            //组装产品谱系对应的工单/子工单在制工序数据
            List<WorkSheetStepStatistics> matchedWorkSheetStepStatisticsList = workSheetStepStatisticsPedigreeGroup.get(pedigree);
            worksheetStepOnlineStatisticsDTOList.forEach(worksheetStepOnlineStatisticsDTO -> {
                List<WorkSheetStepStatistics> matchedSerialNumberWorkSheetStepStatisticsList = matchedWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> subWsProductionMode ? workSheetStepStatistics.getSubWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber()) : workSheetStepStatistics.getWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber())).toList();
                List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> workSheetStepOnlineStatisticsDtoList = new ArrayList<>();
                stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                    OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO workSheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                    workSheetStepOnlineStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                    List<WorkSheetStepStatistics> matchedStepList = matchedSerialNumberWorkSheetStepStatisticsList.stream().filter(workSheetStepStatistics -> Objects.nonNull(workSheetStepStatistics.getStep().getStepGroup()) && workSheetStepStatistics.getStep().getStepGroup().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                    Integer onlineNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum() : null;
                    Integer inputNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum() : null;
                    Integer qualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum() : null;
                    Integer unqualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum() : null;
                    Integer transferNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum() : null;
                    workSheetStepOnlineStatisticsDTO.setOnlineNumber(onlineNumber).setUnqualifiedNumber(unqualifiedNumber).setQualifiedNumber(qualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                    //获取工序组目标良率
                    if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList)) {
                        for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                            if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(stepOnlineStatisticsDTO.getCode())) {
                                workSheetStepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                                break;
                            }
                        }
                    }
                    workSheetStepOnlineStatisticsDtoList.add(workSheetStepOnlineStatisticsDTO);
                });
                worksheetStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(workSheetStepOnlineStatisticsDtoList);
            });
            pedigreeStepOnlineStatisticsDTO.setWorksheetStepOnlineStatisticsDtoList(worksheetStepOnlineStatisticsDTOList);
            pedigreeStepOnlineStatisticsDTOList.add(pedigreeStepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setPedigreeStepOnlineStatisticsDtoList(pedigreeStepOnlineStatisticsDTOList);
        return onlineProductStatisticsResultDTO;
    }

    /**
     * 工单在制状态质量看板 工序组
     *
     * @param workSheetStepStatisticsList 工单在制状态列表
     * @param productionPlanList          产品谱系计划产能
     * @param pedigreeStepPassRateList    产品谱系工序目标良率
     * @param subWsProductionMode         投产模式
     * @return OnlineProductStatisticsResultDTO
     */
    public OnlineProductStatisticsResultDTO findOnlineProductStepGroupStatisticsInfoByOrder(List<WorkSheetStepStatistics> workSheetStepStatisticsList, List<ProductionPlan> productionPlanList, List<PedigreeStepPassRate> pedigreeStepPassRateList, boolean subWsProductionMode) {
        OnlineProductStatisticsResultDTO onlineProductStatisticsResultDTO = new OnlineProductStatisticsResultDTO();
        LinkedHashSet<WorkSheetStepStatistics> workSheetStepStatisticsLinkedHashSet = workSheetStepStatisticsList.stream().filter(workSheetStepStatistic -> Objects.nonNull(workSheetStepStatistic.getStep().getStepGroup())).collect(Collectors.toCollection(LinkedHashSet::new));
        if (CollectionUtils.isEmpty(workSheetStepStatisticsLinkedHashSet)) {
            return onlineProductStatisticsResultDTO;
        }
        Set<SubWorkSheet> subWorkSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getSubWorkSheet).collect(Collectors.toSet());
        Set<WorkSheet> workSheetSet = workSheetStepStatisticsList.stream().map(WorkSheetStepStatistics::getWorkSheet).collect(Collectors.toSet());
        int allExpiredWorkSheetSize = subWsProductionMode ? subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size() :
                workSheetSet.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
        //总的在制工单/子工单个数及逾期个数赋值
        onlineProductStatisticsResultDTO.setOnlineWorkSheetNumber(subWsProductionMode ? subWorkSheetSet.size() : workSheetSet.size()).setOnlineExpiredWorkSheetNumber(allExpiredWorkSheetSize);
        LinkedHashMap<StepGroup, List<WorkSheetStepStatistics>> workSheetStepStatisticsStepGroup = workSheetStepStatisticsLinkedHashSet.stream().collect(Collectors.groupingBy(entity -> entity.getStep().getStepGroup(), LinkedHashMap::new, Collectors.toList()));
        LinkedList<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> stepOnlineStatisticsDTOS = new LinkedList<>();
        workSheetStepStatisticsStepGroup.forEach((stepGroup, workSheetStepStatistics) -> {

            OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO stepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
            int onlineNumber = workSheetStepStatistics.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum();
            int inputNumber = workSheetStepStatistics.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum();
            int qualifiedNumber = workSheetStepStatistics.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum();
            int unqualifiedNumber = workSheetStepStatistics.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum();
            int transferNumber = workSheetStepStatistics.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum();
            stepOnlineStatisticsDTO.setCode(stepGroup.getCode()).setName(stepGroup.getName())
                    .setInputNumber(inputNumber).setOnlineNumber(onlineNumber).setTransferNumber(transferNumber)
                    .setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(unqualifiedNumber).setTargetPlanNumber(Constants.INT_ZERO);

            //获取工序组生产计划
            if (CollectionUtils.isNotEmpty(productionPlanList)) {
                stepOnlineStatisticsDTO.setTargetPlanNumber(productionPlanList.stream().filter(productionPlan -> productionPlan.getStepGroup().getCode().equals(stepGroup.getCode())).toList().stream().mapToInt(ProductionPlan::getPlanNumber).sum());
            }
            //获取工序组目标良率
            if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList)) {
                pedigreeStepPassRateList.stream().filter(pedigreeStepPassRate -> pedigreeStepPassRate.getStepGroup().getCode().equals(stepGroup.getCode())).findFirst().ifPresent(pedigreeStepPassRate -> {
                    stepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                });
            }
            stepOnlineStatisticsDTOS.add(stepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);

        LinkedHashMap<Pedigree, List<WorkSheetStepStatistics>> workSheetStepStatisticsPedigreeGroup = workSheetStepStatisticsList.stream().collect(Collectors.groupingBy(workSheetStepStatistics -> workSheetStepStatistics.getPedigreeStepStatistics().getPedigree(), LinkedHashMap::new, Collectors.toList()));
        LinkedList<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO> pedigreeStepOnlineStatisticsDTOList = new LinkedList<>();
        workSheetStepStatisticsPedigreeGroup.forEach((pedigree, workSheetStepStatistics) -> {

            OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO pedigreeStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO();
            pedigreeStepOnlineStatisticsDTO.setCode(pedigree.getCode()).setName(pedigree.getName()).setSpecification(pedigree.getSpecification());
//产品谱系工序在制统计数据对应的工单工序在制统计数据列表
            LinkedList<OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO> worksheetStepOnlineStatisticsDTOList = new LinkedList<>();
            int pedigreeWorkSheetSize;
            int pedigreeExpiredWorkSheetSize;
            //获取产品谱系工序在制统计数据中对应的在制工单/子工单总数及逾期总数，同时初始化对应工单/子工单列表
            if (subWsProductionMode) {
                List<SubWorkSheet> pedigreeSubWorkSheetList = subWorkSheetSet.stream().filter(subWorkSheet -> subWorkSheet.getWorkSheet().getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeSubWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeSubWorkSheetList.stream().filter(subWorkSheet -> subWorkSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeSubWorkSheetList.forEach(subWorkSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(subWorkSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            } else {
                List<WorkSheet> pedigreeWorkSheetList = workSheetSet.stream().filter(workSheet -> workSheet.getPedigree().getId().equals(pedigree.getId())).toList();
                pedigreeWorkSheetSize = pedigreeWorkSheetList.size();
                pedigreeExpiredWorkSheetSize = pedigreeWorkSheetList.stream().filter(workSheet -> workSheet.getPlanEndDate().isBefore(LocalDateTime.now())).toList().size();
                pedigreeWorkSheetList.forEach(workSheet -> {
                    OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO worksheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.PedigreeStepOnlineStatisticsDTO.WorksheetStepOnlineStatisticsDTO();
                    worksheetStepOnlineStatisticsDTO.setSerialNumber(workSheet.getSerialNumber());
                    worksheetStepOnlineStatisticsDTOList.add(worksheetStepOnlineStatisticsDTO);
                });
            }
            //组装产品谱系对应的在制工序数据
            pedigreeStepOnlineStatisticsDTO.setOnlineWorkSheetNumber(pedigreeWorkSheetSize).setOnlineExpiredWorkSheetNumber(pedigreeExpiredWorkSheetSize);
            pedigreeStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(stepOnlineStatisticsDTOS);
            //组装产品谱系对应的工单/子工单在制工序数据
            List<WorkSheetStepStatistics> matchedWorkSheetStepStatisticsList = workSheetStepStatisticsPedigreeGroup.get(pedigree);
            worksheetStepOnlineStatisticsDTOList.forEach(worksheetStepOnlineStatisticsDTO -> {
                List<WorkSheetStepStatistics> matchedSerialNumberWorkSheetStepStatisticsList = matchedWorkSheetStepStatisticsList.stream().filter(entity -> subWsProductionMode ? entity.getSubWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber()) : entity.getWorkSheet().getSerialNumber().equals(worksheetStepOnlineStatisticsDTO.getSerialNumber())).toList();
                List<OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO> workSheetStepOnlineStatisticsDtoList = new ArrayList<>();
                stepOnlineStatisticsDTOS.forEach(stepOnlineStatisticsDTO -> {
                    OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO workSheetStepOnlineStatisticsDTO = new OnlineProductStatisticsResultDTO.StepOnlineStatisticsDTO();
                    workSheetStepOnlineStatisticsDTO.setCode(stepOnlineStatisticsDTO.getCode()).setName(stepOnlineStatisticsDTO.getName());
                    List<WorkSheetStepStatistics> matchedStepList = matchedSerialNumberWorkSheetStepStatisticsList.stream().filter(entity -> Objects.nonNull(entity.getStep().getStepGroup()) && entity.getStep().getStepGroup().getCode().equals(stepOnlineStatisticsDTO.getCode())).toList();
                    Integer onlineNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getOnlineNumber).sum() : null;
                    Integer inputNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getInputNumber).sum() : null;
                    Integer qualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getQualifiedNumber).sum() : null;
                    Integer unqualifiedNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getUnqualifiedNumber).sum() : null;
                    Integer transferNumber = CollectionUtils.isNotEmpty(matchedStepList) ? matchedStepList.stream().mapToInt(WorkSheetStepStatistics::getTransferNumber).sum() : null;
                    workSheetStepOnlineStatisticsDTO.setOnlineNumber(onlineNumber).setUnqualifiedNumber(unqualifiedNumber).setQualifiedNumber(qualifiedNumber).setInputNumber(inputNumber).setTransferNumber(transferNumber).setTargetPlanNumber(Constants.INT_ZERO);
                    //获取工序组目标良率
                    if (CollectionUtils.isNotEmpty(pedigreeStepPassRateList)) {
                        for (PedigreeStepPassRate pedigreeStepPassRate : pedigreeStepPassRateList) {
                            if (pedigreeStepPassRate.getPedigree().getCode().equals(pedigreeStepOnlineStatisticsDTO.getCode()) && pedigreeStepPassRate.getStepGroup().getCode().equals(stepOnlineStatisticsDTO.getCode())) {
                                workSheetStepOnlineStatisticsDTO.setTargetPassRate(pedigreeStepPassRate.getTargetPassRate());
                                break;
                            }
                        }
                    }
                    workSheetStepOnlineStatisticsDtoList.add(workSheetStepOnlineStatisticsDTO);
                });
                worksheetStepOnlineStatisticsDTO.setStepOnlineStatisticsDtoList(workSheetStepOnlineStatisticsDtoList);
            });
            pedigreeStepOnlineStatisticsDTO.setWorksheetStepOnlineStatisticsDtoList(worksheetStepOnlineStatisticsDTOList);
            pedigreeStepOnlineStatisticsDTOList.add(pedigreeStepOnlineStatisticsDTO);
        });
        onlineProductStatisticsResultDTO.setPedigreeStepOnlineStatisticsDtoList(pedigreeStepOnlineStatisticsDTOList);
        return onlineProductStatisticsResultDTO;
    }
}