package net.airuima.rbase.service.procedure.batch;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/11/18
 */
@FuncDefault
public interface StaffSkillModelService {
    /**
     * 验证当前待生产的工序绑定了相应的技能与员工所拥有的技能是否都匹配
     * @param subWorkSheet 子工单
     * @param currWsStep  生产工单定制工序
     * @param stepInfo 工序
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor("StaffSkillControl")
    default BaseClientDTO validStaffSkillModel(SubWorkSheet subWorkSheet, WsStep currWsStep, ClientGetStepInfoDTO.StepInfo stepInfo) {
        return new BaseClientDTO(Constants.OK);
    }
}
