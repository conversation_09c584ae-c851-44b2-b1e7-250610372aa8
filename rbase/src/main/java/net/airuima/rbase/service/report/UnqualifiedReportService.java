package net.airuima.rbase.service.report;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ReportTimeRangeEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.web.rest.report.dto.*;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良报表Service
 *
 * <AUTHOR>
 * @date 2021-3-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UnqualifiedReportService {


    private final StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;

    private final ContainerDetailRepository containerDetailRepository;

    private final SnWorkDetailRepository snWorkDetailRepository;

    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;
    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private CommonService commonService;

    /**
     * 记录时间
     */
    private final String RECORD_TIME = "recordTime";

    /**
     * 员工产量
     */
    private final String STAFF_PERFORM = "staffPerform";

    /**
     * 工单
     */
    private final String WORK_SHEET = "workSheet";

    /**
     * 子工单
     */
    private final String SUB_WORK_SHEET = "subWorkSheet";

    public UnqualifiedReportService(StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository, ContainerDetailRepository containerDetailRepository, SnWorkDetailRepository snWorkDetailRepository) {
        this.staffPerformUnqualifiedItemRepository = staffPerformUnqualifiedItemRepository;
        this.containerDetailRepository = containerDetailRepository;
        this.snWorkDetailRepository = snWorkDetailRepository;
    }

    /**
     * 不良统计报表统计图表
     *
     * @param requestDto 不良统计报表请求参数
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportChartResultDTO>不良统计报表统计图形返回结果
     */
    @Transactional(readOnly = true)
    public UnqualifiedReportChartResultDTO getUnqualifiedReportChart(UnqualifiedReportRequestDTO requestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(requestDto);
        // 产品谱系id
        Long pedigreeId = requestDto.getPedigreeId();
        //产线 id
        Long workLineId = requestDto.getWorkLineId();
        //开始时间
        LocalDateTime startDateTime = requestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = requestDto.getEndDate();
        List<Long> pedigreeIdList = null;
        if(Objects.nonNull(pedigreeId)){
            pedigreeIdList = commonService.getMinLevelPedigreeIds(pedigreeRepository.getReferenceById(pedigreeId));
        }
        pedigreeIdList = CollectionUtils.isEmpty(pedigreeIdList)?null:pedigreeIdList;
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        //工单号
        String serialNumber = StringUtils.isNotBlank(requestDto.getSerialNumber())?requestDto.getSerialNumber():null;
        //获取统计不良图形
        return getUnqualifiedReportChartResultDTO(serialNumber,pedigreeIdList, workLineId, startDateTime, endDateTime, subWsProductionMode);
    }

    /**
     * 获取统计不良图形
     *
     * @param pedigreeId    产品谱系id
     * @param workLineId    产线 id
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportChartResultDTO 不良图形统计数据
     */
    private UnqualifiedReportChartResultDTO getUnqualifiedReportChartResultDTO(String serialNumber,List<Long> pedigreeIdList, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Boolean subWsProductionMode) {
        UnqualifiedReportChartResultDTO unqualifiedReportChartResultDto = new UnqualifiedReportChartResultDTO();
        //获取工单维度统计不良图表数据
        if (Boolean.FALSE.equals(subWsProductionMode)) {
            unqualifiedReportChartResultDto = getUnqualifiedReportWorkSheetChartData(serialNumber,pedigreeIdList, workLineId, startDateTime, endDateTime);
        }
        //获取子工单维度统计不良图表数据
        if (Boolean.TRUE.equals(subWsProductionMode)) {
            unqualifiedReportChartResultDto = getUnqualifiedSubWorkSheetChartData(serialNumber,pedigreeIdList, workLineId, startDateTime, endDateTime);
        }
        return unqualifiedReportChartResultDto;
    }

    /**
     * 获取工单维度统计不良图表数据
     *
     * @param pedigreeId    产品谱系id
     * @param workLineId    产线 id
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportChartResultDTO 工单维度统计不良图表数据
     */
    private UnqualifiedReportChartResultDTO getUnqualifiedReportWorkSheetChartData(String serialNumber,List<Long> pedigreeIdList, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        UnqualifiedReportChartResultDTO unqualifiedReportChartResultDto = new UnqualifiedReportChartResultDTO();
        List<StepUnqualifiedDTO> stepUnqualifiedList;
        // 不良工序条形图数据
        stepUnqualifiedList = staffPerformUnqualifiedItemRepository.findWorkSheetStepUnqualifiedChart(startDateTime, endDateTime, workLineId, pedigreeIdList,serialNumber, Constants.LONG_ZERO);
        //获取指定范围内按照不良项目及日期分组的不良统计数据
        List<UnqualifiedResultDTO> unqualifiedItemGroupByDateList = staffPerformUnqualifiedItemRepository.findWorkSheetUnqualifiedItemGroupByUnqualifiedItemAndRecordDate(startDateTime, endDateTime, workLineId, pedigreeIdList,serialNumber, Constants.LONG_ZERO);
        // 不良比率环图数据
        Map<Long, List<UnqualifiedResultDTO>> groupMap = unqualifiedItemGroupByDateList.stream().collect(Collectors.groupingBy(UnqualifiedResultDTO::getId));
        //不良比率环图数据
        List<UnqualifiedResultDTO> unqualifiedRateChartResultList = new ArrayList<>();
        groupMap.forEach((id, list) -> {
            UnqualifiedResultDTO unqualifiedResultDTO = list.get(Constants.INT_ZERO);
            unqualifiedResultDTO.setNumber(list.stream().map(UnqualifiedResultDTO::getNumber).mapToLong(Long::longValue).sum());
            unqualifiedRateChartResultList.add(unqualifiedResultDTO);
        });
        //不良总数
        Long unqualifiedCountNumber = unqualifiedRateChartResultList.stream().map(UnqualifiedResultDTO::getNumber).mapToLong(Long::longValue).sum();
        List<UnqualifiedRateDTO> unqualifiedRateList = unqualifiedRateChartResultList.stream().map(u -> convertUnqualifiedQueryResultToChart(u, unqualifiedCountNumber)).collect(Collectors.toList());
        //取Top10的数据
        unqualifiedRateList = convertToTopChart(unqualifiedRateList);
        unqualifiedRateList = unqualifiedRateList.stream().sorted(Comparator.comparing(UnqualifiedRateDTO::getNumber).reversed()).toList();
        // 设置不良图形统计数据
        unqualifiedReportChartResultDto.setNumber(unqualifiedCountNumber).setStepUnqualifiedList(stepUnqualifiedList).setUnqualifiedRateList(unqualifiedRateList);
        // 设置不良推移图数据
        unqualifiedReportChartResultDto.setUnqualifiedItemRunChartDtoList(this.convertToRunChart(unqualifiedItemGroupByDateList, unqualifiedRateList.stream().filter(unqualifiedRateDTO -> Objects.nonNull(unqualifiedRateDTO.getId())).toList()));
        return unqualifiedReportChartResultDto;
    }

    /**
     * 获取按照日期分布的不良推移图数据
     *
     * @param unqualifiedItemGroupByDateList 按照日期、不良项目分组的统计信息列表
     * @param unqualifiedRateList            TOP10不良项目列表
     * @return List<UnqualifiedReportChartResultDTO.UnqualifiedItemRunChartDTO> 处理后的推移图数据
     */
    private List<UnqualifiedReportChartResultDTO.UnqualifiedItemRunChartDTO> convertToRunChart(List<UnqualifiedResultDTO> unqualifiedItemGroupByDateList, List<UnqualifiedRateDTO> unqualifiedRateList) {
        Set<LocalDate> recordDateList = unqualifiedItemGroupByDateList.stream().map(UnqualifiedResultDTO::getRecordDate).collect(Collectors.toSet());
        List<UnqualifiedReportChartResultDTO.UnqualifiedItemRunChartDTO> unqualifiedItemRunChartDtoList = new ArrayList<>();
        recordDateList.stream().sorted(Comparator.naturalOrder()).forEach(recordDate -> {
            UnqualifiedReportChartResultDTO.UnqualifiedItemRunChartDTO unqualifiedItemRunChartDTO = new UnqualifiedReportChartResultDTO.UnqualifiedItemRunChartDTO();
            unqualifiedItemRunChartDTO.setRecordDate(recordDate);
            List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> unqualifiedItemNumberInfoList = new ArrayList<>();
            unqualifiedRateList.forEach(unqualifiedRateDTO -> {
                UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo unqualifiedItemNumberInfo = new UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo();
                UnqualifiedStatisticsDTO.UnqualifiedItemInfo unqualifiedItemInfo = new UnqualifiedStatisticsDTO.UnqualifiedItemInfo();
                unqualifiedItemInfo.setId(unqualifiedRateDTO.getId()).setCode(unqualifiedRateDTO.getCode()).setName(unqualifiedRateDTO.getName());
                unqualifiedItemNumberInfo.setUnqualifiedItemInfo(unqualifiedItemInfo).setNumber(Constants.LONG_ZERO);
                unqualifiedItemNumberInfoList.add(unqualifiedItemNumberInfo);
            });
            unqualifiedItemRunChartDTO.setUnqualifiedItemNumberInfoList(unqualifiedItemNumberInfoList);
            unqualifiedItemRunChartDtoList.add(unqualifiedItemRunChartDTO);
        });
        unqualifiedItemRunChartDtoList.forEach(unqualifiedItemRunChartDTO -> {
            List<UnqualifiedResultDTO> recordDateMatchedList = unqualifiedItemGroupByDateList.stream().filter(unqualifiedResultDTO -> unqualifiedResultDTO.getRecordDate().isEqual(unqualifiedItemRunChartDTO.getRecordDate())).toList();
            recordDateMatchedList.forEach(unqualifiedResultDTO -> {
                unqualifiedItemRunChartDTO.getUnqualifiedItemNumberInfoList().forEach(unqualifiedItemNumberInfo -> {
                    if (unqualifiedItemNumberInfo.getUnqualifiedItemInfo().getId().equals(unqualifiedResultDTO.getId())) {
                        unqualifiedItemNumberInfo.setNumber(unqualifiedResultDTO.getNumber());
                    }
                });
            });
        });
        return unqualifiedItemRunChartDtoList;
    }

    /**
     * 转换为比率为指定数量的Top的饼图
     *
     * @param unqualifiedRateList 不良比率环图数据
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.UnqualifiedRateDTO> 不良比率环图数据
     */
    private List<UnqualifiedRateDTO> convertToTopChart(List<UnqualifiedRateDTO> unqualifiedRateList) {
        if (!CollectionUtils.isEmpty(unqualifiedRateList) && unqualifiedRateList.size() > Constants.INT_TEN) {
            // 根据rate从大到小排序
            unqualifiedRateList.sort((o1, o2) -> Double.compare(o2.getRate(), o1.getRate()));
            // 取出前10个数据之外的所有rate并累加
            double otherRate = unqualifiedRateList.subList(Constants.INT_TEN, unqualifiedRateList.size())
                    .stream()
                    .mapToDouble(UnqualifiedRateDTO::getRate)
                    .sum();
            long otherNumber = unqualifiedRateList.subList(Constants.INT_TEN, unqualifiedRateList.size())
                    .stream()
                    .mapToLong(UnqualifiedRateDTO::getNumber)
                    .sum();
            // 清除用于累加的数据，保留前10个数据
            unqualifiedRateList = unqualifiedRateList.subList(Constants.INT_ZERO, Constants.INT_TEN);
            // 创建新的DTO对象并设置name为Other，rate为累加的值
            UnqualifiedRateDTO otherDTO = new UnqualifiedRateDTO();
            // Other 前端做国际化
            otherDTO.setName("Other");
            otherDTO.setRate(otherRate).setNumber(otherNumber);
            // 将新对象添加到列表中
            unqualifiedRateList.add(otherDTO);
        }
        return unqualifiedRateList;
    }


    /**
     * 获取子工单维度统计不良图表数据
     *
     * @param pedigreeId    产品谱系id
     * @param workLineId    产线 id
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportChartResultDTO 子工单维度统计不良图表数据
     */
    private UnqualifiedReportChartResultDTO getUnqualifiedSubWorkSheetChartData(String serialNumber,List<Long> pedigreeIdList, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        UnqualifiedReportChartResultDTO unqualifiedReportChartResultDto = new UnqualifiedReportChartResultDTO();
        // 不良工序条形图数据
        List<StepUnqualifiedDTO> stepUnqualifiedList = staffPerformUnqualifiedItemRepository.findSubWorkSheetStepUnqualifiedChart(startDateTime, endDateTime, workLineId, pedigreeIdList,serialNumber, Constants.LONG_ZERO);
        //获取指定范围内按照不良项目及日期分组的不良统计数据
        List<UnqualifiedResultDTO> unqualifiedItemGroupByDateList = staffPerformUnqualifiedItemRepository.findSubWorkSheetUnqualifiedItemGroupByUnqualifiedItemAndRecordDate(startDateTime, endDateTime, workLineId, pedigreeIdList,serialNumber, Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedResultDTO>> groupMap = unqualifiedItemGroupByDateList.stream().collect(Collectors.groupingBy(UnqualifiedResultDTO::getId));
        //不良比率环图数据
        List<UnqualifiedResultDTO> unqualifiedRateChartResultList = new ArrayList<>();
        groupMap.forEach((id, list) -> {
            UnqualifiedResultDTO unqualifiedResultDTO = list.get(Constants.INT_ZERO);
            unqualifiedResultDTO.setNumber(list.stream().map(UnqualifiedResultDTO::getNumber).mapToLong(Long::longValue).sum());
            unqualifiedRateChartResultList.add(unqualifiedResultDTO);
        });
        //不良总数
        Long unqualifiedCountNumber = unqualifiedRateChartResultList.stream().map(UnqualifiedResultDTO::getNumber).mapToLong(Long::longValue).sum();
        List<UnqualifiedRateDTO> unqualifiedRateList = unqualifiedRateChartResultList.stream().map(u -> convertUnqualifiedQueryResultToChart(u, unqualifiedCountNumber)).collect(Collectors.toList());
        //取Top10的数据
        unqualifiedRateList = convertToTopChart(unqualifiedRateList);
        unqualifiedRateList = unqualifiedRateList.stream().sorted(Comparator.comparing(UnqualifiedRateDTO::getNumber).reversed()).toList();
        // 设置不良图形统计数据
        unqualifiedReportChartResultDto.setNumber(unqualifiedCountNumber).setStepUnqualifiedList(stepUnqualifiedList).setUnqualifiedRateList(unqualifiedRateList);
        // 设置不良推移图数据
        unqualifiedReportChartResultDto.setUnqualifiedItemRunChartDtoList(this.convertToRunChart(unqualifiedItemGroupByDateList, unqualifiedRateList.stream().filter(unqualifiedRateDTO -> Objects.nonNull(unqualifiedRateDTO.getId())).toList()));
        return unqualifiedReportChartResultDto;
    }

    /**
     * 转换不良比率查询结果为环图数据
     *
     * @param queryResultDto         不良比率查询结果
     * @param unqualifiedCountNumber 不良总数
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedRateDTO 环形图表统计数据
     */
    private UnqualifiedRateDTO convertUnqualifiedQueryResultToChart(UnqualifiedResultDTO queryResultDto, Long unqualifiedCountNumber) {
        UnqualifiedRateDTO unqualifiedRateDTO = new UnqualifiedRateDTO();
        double rate = 0d;
        // 计算不良项目所占比率
        if (Constants.LONG_ZERO != unqualifiedCountNumber) {
            rate = BigDecimal.valueOf((float) (queryResultDto.getNumber() * 100) / unqualifiedCountNumber).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        unqualifiedRateDTO.setId(queryResultDto.getId()).setCode(queryResultDto.getCode()).setName(queryResultDto.getName()).setNumber(queryResultDto.getNumber()).setRate(rate);
        return unqualifiedRateDTO;
    }


    /**
     * 不良报表表格数据查询
     *
     * @param requestDto 不良报表查询参数
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportTableResultDTO 不良报表表格数据查询结果
     */
    @Transactional(readOnly = true)
    public UnqualifiedReportTableResultDTO getUnqualifiedReportTable(UnqualifiedReportRequestDTO requestDto) {
        //设置开始时间和结束时间
        parseTimeFinishTimeCategory(requestDto);
        // 产品谱系id
        Long pedigreeId = requestDto.getPedigreeId();
        // 产线id
        Long workLineId = requestDto.getWorkLineId();
        List<Long> pedigreeIdList = null;
        if(Objects.nonNull(pedigreeId)){
            pedigreeIdList = commonService.getMinLevelPedigreeIds(pedigreeRepository.getReferenceById(pedigreeId));
        }
        pedigreeIdList = CollectionUtils.isEmpty(pedigreeIdList)?null:pedigreeIdList;
        // 查询开始时间
        LocalDateTime startDateTime = requestDto.getStartDate();
        //查询结束时间
        LocalDateTime endDateTime = requestDto.getEndDate();
        // 是否导出
        Boolean exportStatus = requestDto.getExportStatus();
        //当前页
        Integer currentPage = requestDto.getCurrentPage();
        // 分页大小
        Integer pageSize = requestDto.getPageSize();
        String serialNumber = StringUtils.isNotBlank(requestDto.getSerialNumber())?requestDto.getSerialNumber():null;
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        //获取工单报表表格数据
        return getUnqualifiedReportTableResultDTO(serialNumber,pedigreeIdList, workLineId, startDateTime, endDateTime, exportStatus, currentPage, pageSize, subWsProductionMode);
    }

    /**
     * 获取子工单报表表格数据
     *
     * @param pedigreeId    设备谱系id
     * @param workLineId    生产线id
     * @param startDateTime 查询开始时间
     * @param endDateTime   查询结束时间
     * @param exportStatus  是否导出
     * @param currentPage   当前页
     * @param pageSize      页数
     * @return net.airuima.rbase.web.rest.report.dto.UnqualifiedReportTableResultDTO 报表返回结果表格数据
     */
    private UnqualifiedReportTableResultDTO getUnqualifiedReportTableResultDTO(String serialNumber,List<Long> pedigreeIdList, Long workLineId, LocalDateTime startDateTime, LocalDateTime endDateTime, Boolean exportStatus, Integer currentPage, Integer pageSize, Boolean subWsProductionMode) {
        UnqualifiedReportTableResultDTO reportTableResultDto = new UnqualifiedReportTableResultDTO();
        // 分页查询
        Specification<StaffPerformUnqualifiedItem> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            //逻辑删除
            Predicate deletedPredicate = criteriaBuilder.equal(root.get("deleted"), Constants.LONG_ZERO);
            predicateList.add(deletedPredicate);
            //开始时间和结束时间筛选
            if ( endDateTime != null) {
                predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class), endDateTime));
            }
            if (startDateTime != null) {
                predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get(RECORD_TIME).as(LocalDateTime.class), startDateTime));
            }
            //子工单投产
            if (subWsProductionMode) {

                // 产品谱系筛选
                if (!CollectionUtils.isEmpty(pedigreeIdList)) {
                    CriteriaBuilder.In<Long> pedigreePredicate  = criteriaBuilder.in(root.get(STAFF_PERFORM).get(SUB_WORK_SHEET).get(WORK_SHEET).get("pedigree").get("id"));
                    for (Long id : pedigreeIdList) {
                        pedigreePredicate.value(id);
                    }
                    predicateList.add(pedigreePredicate);
                }
                // 产线筛选
                if (workLineId != null) {
                    Predicate workLinePredicate = criteriaBuilder.equal(root.get(STAFF_PERFORM).get(SUB_WORK_SHEET).get("workLine").get("id"), workLineId);
                    predicateList.add(workLinePredicate);
                }
                if(StringUtils.isNotBlank(serialNumber)){
                    Predicate workLinePredicate =  criteriaBuilder.like(root.get(STAFF_PERFORM).get(SUB_WORK_SHEET).get("serialNumber").as(String.class), serialNumber+"%");;
                    predicateList.add(workLinePredicate);
                }
                Predicate subWorkSheetPredicate = criteriaBuilder.isNotNull(root.get(STAFF_PERFORM).get("subWorkSheet"));
                predicateList.add(subWorkSheetPredicate);
            }
            //工单投产
            if (!subWsProductionMode) {
                // 产品谱系筛选
                if (!CollectionUtils.isEmpty(pedigreeIdList)) {
                    CriteriaBuilder.In<Long> pedigreePredicate  = criteriaBuilder.in(root.get("pedigree").get("id"));
                    for (Long id : pedigreeIdList) {
                        pedigreePredicate.value(id);
                    }
                    predicateList.add(pedigreePredicate);
                }
                // 产线筛选
                if (workLineId != null) {
                    Predicate workLinePredicate = criteriaBuilder.equal(root.get(STAFF_PERFORM).get(WORK_SHEET).get("workLine").get("id"), workLineId);
                    predicateList.add(workLinePredicate);
                }
                if(StringUtils.isNotBlank(serialNumber)){
                    Predicate workLinePredicate =  criteriaBuilder.like(root.get(STAFF_PERFORM).get(WORK_SHEET).get("serialNumber").as(String.class), serialNumber+"%");;
                    predicateList.add(workLinePredicate);
                }
                Predicate subWorkSheetPredicate = criteriaBuilder.isNull(root.get(STAFF_PERFORM).get("subWorkSheet"));
                predicateList.add(subWorkSheetPredicate);
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        };
        List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItemList;
        Page<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItemPage = null;
        //导出时 导出全部数据
        if (Boolean.TRUE.equals(exportStatus)) {
            staffPerformUnqualifiedItemList = staffPerformUnqualifiedItemRepository.findAll(specification,new NamedEntityGraph("staffPerformUnqualifiedItemEntityGraph"));
        } else {
            // 分页查询
            staffPerformUnqualifiedItemPage = staffPerformUnqualifiedItemRepository.findAll(specification, PageRequest.of(currentPage, pageSize),new NamedEntityGraph("staffPerformUnqualifiedItemEntityGraph"));
            staffPerformUnqualifiedItemList = Optional.ofNullable(staffPerformUnqualifiedItemPage).map(Slice::getContent).orElse(Lists.newArrayList());
        }
        //设置子工单表格分页数据
        reportTableResultDto.setCurrentPage(currentPage);
        reportTableResultDto.setPageSize(pageSize);
        reportTableResultDto.setCountSize(Boolean.TRUE.equals(exportStatus) ? Optional.ofNullable(staffPerformUnqualifiedItemList).map(s -> Long.valueOf(s.size())).orElse(0L) : Optional.ofNullable(staffPerformUnqualifiedItemPage).map(Page::getTotalElements).orElse(0L));
        // 设置表格数据
        List<UnqualifiedReportTableItemDTO> reportTableItemDTOList = convertUnqualifiedQueryToTable(staffPerformUnqualifiedItemList, subWsProductionMode);
        reportTableResultDto.setUnqualifiedReportTableItemDtoList(reportTableItemDTOList);
        return reportTableResultDto;
    }

    /**
     * 转换员工不良明细为不良报表表格
     *
     * @param staffPerformUnqualifiedItemList 员工不良明细 集合
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.UnqualifiedReportTableItemDTO>  不良报表表格数据
     */
    private List<UnqualifiedReportTableItemDTO> convertUnqualifiedQueryToTable(List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItemList, Boolean subWsProductionMode) {
        List<UnqualifiedReportTableItemDTO> unitPerformUnqualifiedItemList = Lists.newArrayList();
        staffPerformUnqualifiedItemList.forEach(s -> {
            UnqualifiedReportTableItemDTO unqualifiedReportTableItemDto = new UnqualifiedReportTableItemDTO();
            //员工产量
            StaffPerform staffPerform = Optional.ofNullable(s).map(StaffPerformUnqualifiedItem::getStaffPerform).orElse(null);
            // 投产类型工单
            WorkSheet workSheet = null;
            SubWorkSheet subWorkSheet = Optional.ofNullable(staffPerform).map(StaffPerform::getSubWorkSheet).orElse(null);
            if (!subWsProductionMode) {
                workSheet = Optional.ofNullable(staffPerform).map(StaffPerform::getWorkSheet).orElse(null);
            }
            // 投产类型子工单
            if (subWsProductionMode) {
                workSheet = Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getWorkSheet).orElse(null);
            }
            // 不良项目
            UnqualifiedItem unqualifiedItem = Optional.ofNullable(s).map(StaffPerformUnqualifiedItem::getUnqualifiedItem).orElse(null);
            // 产品谱系
            Pedigree pedigree = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).orElse(null);
            //容器生产详情
            ContainerDetail containerDetail = Optional.ofNullable(staffPerform).map(StaffPerform::getContainerDetailId).flatMap(containerDetailRepository::findById).orElse(null);
            //单支工序生产详情
            SnWorkDetail snWorkDetail = Optional.ofNullable(staffPerform).map(StaffPerform::getSnWorkDetailId).flatMap(snWorkDetailRepository::findById).orElse(null);
            //设置不良看板表格数据
            unqualifiedReportTableItemDto.setRecordTime(Optional.ofNullable(s).map(StaffPerformUnqualifiedItem::getRecordTime).orElse(null))
                    .setWorkSheetSerialNumber(Optional.ofNullable(workSheet).map(WorkSheet::getSerialNumber).orElse(null))
                    .setSubWorkSheetSerialNumber(Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getSerialNumber).orElse(null))
                    .setUnqualifiedItemCode(Optional.ofNullable(unqualifiedItem).map(UnqualifiedItem::getCode).orElse(null))
                    .setUnqualifiedItemName(Optional.ofNullable(unqualifiedItem).map(UnqualifiedItem::getName).orElse(null))
                    .setPedigreeCode(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse(null))
                    .setPedigreeName(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse(null))
                    .setSpecification(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse(null))
                    .setWorkLineName(Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(WorkLine::getName).orElse(null))
                    .setStepName(Optional.ofNullable(staffPerform).map(StaffPerform::getStep).map(Step::getName).orElse(null))
                    .setStepCode(Optional.ofNullable(staffPerform).map(StaffPerform::getStep).map(Step::getCode).orElse(null))
                    .setContainerName(Optional.ofNullable(containerDetail).map(ContainerDetail::getContainer).map(Container::getName).orElse(null))
                    .setContainerCode(Optional.ofNullable(containerDetail).map(ContainerDetail::getContainer).map(Container::getCode).orElse(null))
                    .setSn(Optional.ofNullable(snWorkDetail).map(SnWorkDetail::getSn).orElse(null))
                    .setNumber(Optional.ofNullable(s).map(StaffPerformUnqualifiedItem::getNumber).orElse(null));
            unitPerformUnqualifiedItemList.add(unqualifiedReportTableItemDto);
        });
        return unitPerformUnqualifiedItemList;
    }


    /**
     * 计划完成时期解析
     *
     * @param reportRequestDto 不良报表请求参数
     */
    private void parseTimeFinishTimeCategory(UnqualifiedReportRequestDTO reportRequestDto) {
        // 计划完工时间类型
        Integer planFinishTimeCategory = reportRequestDto.getPlanFinishTimeCategory();
        //开始时间
        LocalDateTime startDateTime = reportRequestDto.getStartDate();
        //结束时间
        LocalDateTime endDateTime = reportRequestDto.getEndDate();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime) && Objects.nonNull(planFinishTimeCategory)) {
            //今天
            if (ReportTimeRangeEnum.TODAY.getCategory() == planFinishTimeCategory) {
                reportRequestDto.setStartDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                reportRequestDto.setEndDate(LocalDateTime.now());
            }
            //本周
            if (ReportTimeRangeEnum.WEEK.getCategory() == planFinishTimeCategory) {
                LocalDate now = LocalDate.now();
                LocalDateTime weekStart = LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN);
                reportRequestDto.setStartDate(weekStart);
                reportRequestDto.setEndDate(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
            }
            //本月
            if (ReportTimeRangeEnum.MONTH.getCategory() == planFinishTimeCategory) {
                LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                reportRequestDto.setStartDate(monthStart);
                reportRequestDto.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            }
        }
    }
}
