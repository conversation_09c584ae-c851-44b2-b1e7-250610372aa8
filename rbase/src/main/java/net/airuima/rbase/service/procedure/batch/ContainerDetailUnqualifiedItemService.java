package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailUnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情不良明细表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailUnqualifiedItemService extends CommonJpaService<ContainerDetailUnqualifiedItem> {
    private static final String CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH = "containerDetailUnqualifiedItemEntityGraph";
    private final ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;

    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;

    public ContainerDetailUnqualifiedItemService(
            ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository) {
        this.containerDetailUnqualifiedItemRepository = containerDetailUnqualifiedItemRepository;
    }

    /**
     * 根据规格和分页查询容器工作详情不良明细
     *
     * @param spec     查询规格
     * @param pageable 分页参数
     * @return 分页的容器工作详情不良明细数据
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    @FetchMethod
    public Page<ContainerDetailUnqualifiedItem> find(Specification<ContainerDetailUnqualifiedItem> spec,
                                                     Pageable pageable) {
        Page<ContainerDetailUnqualifiedItem> page = containerDetailUnqualifiedItemRepository.findAll(spec, pageable,
                new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));
        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

    /**
     * 根据规格查询容器工作详情不良明细列表
     *
     * @param spec 查询规格
     * @return 容器工作详情不良明细列表
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    @FetchMethod
    public List<ContainerDetailUnqualifiedItem> find(Specification<ContainerDetailUnqualifiedItem> spec) {
        return containerDetailUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    /**
     * 分页查询所有容器工作详情不良明细
     *
     * @param pageable 分页参数
     * @return 分页的容器工作详情不良明细数据
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    @FetchMethod
    public Page<ContainerDetailUnqualifiedItem> findAll(Pageable pageable) {
        Page<ContainerDetailUnqualifiedItem> page = containerDetailUnqualifiedItemRepository.findAll(pageable,
                new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));

        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

    /**
     * 根据容器详情ID获取不良项目信息
     *
     * @param containerDetailId 容器详情ID
     * @return List<ContainerDetailUnqualifiedItem>
     * <AUTHOR>
     * @date 2021-04-15
     **/
    @Transactional(readOnly = true)
    public List<ContainerDetailUnqualifiedItem> findByContainerDetailId(Long containerDetailId) {
        return containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetailId,
                Constants.LONG_ZERO);
    }

}
