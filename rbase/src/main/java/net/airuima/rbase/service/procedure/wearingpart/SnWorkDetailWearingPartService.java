package net.airuima.rbase.service.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;
import net.airuima.rbase.repository.procedure.wearingpart.SnWorkDetailWearingPartRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *单支生产详情易损件Service
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnWorkDetailWearingPartService extends CommonJpaService<SnWorkDetailWearingPart> {
    private static final String SN_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH = "snWorkDetailWearingPartEntityGraph";
    @Autowired
    private SnWorkDetailWearingPartRepository snWorkDetailWearingPartRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnWorkDetailWearingPart> find(Specification<SnWorkDetailWearingPart> spec, Pageable pageable) {
        return snWorkDetailWearingPartRepository.findAll(spec,pageable,new NamedEntityGraph(SN_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SnWorkDetailWearingPart> find(Specification<SnWorkDetailWearingPart> spec) {
        return snWorkDetailWearingPartRepository.findAll(spec,new NamedEntityGraph(SN_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnWorkDetailWearingPart> findAll(Pageable pageable) {
        return snWorkDetailWearingPartRepository.findAll(pageable,new NamedEntityGraph(SN_WORK_DETAIL_WEARING_PART_ENTITY_GRAPH));
    }

    @Override
    public SnWorkDetailWearingPart save(SnWorkDetailWearingPart entity) {
        return super.save(entity);
    }
}
