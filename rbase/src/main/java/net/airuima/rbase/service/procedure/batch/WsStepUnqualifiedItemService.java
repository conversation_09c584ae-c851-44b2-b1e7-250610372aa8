package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.dto.batch.ReworkGetSubWsDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseIMaintainServiceProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.reinspect.IStepReinspectService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.batch.dto.WsStepUnqualifiedItemDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工序不良项目统计Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsStepUnqualifiedItemService extends CommonJpaService<WsStepUnqualifiedItem> {
    private static final String WS_STEP_UNQUALIFIED_ITEM_ENTITY_GRAPH = "wsStepUnqualifiedItemEntityGraph";
    private final WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    private final WsStepRepository wsStepRepository;
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetRepository workSheetRepository;
    private final CommonService commonService;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private RbaseIMaintainServiceProxy rbaseIMaintainServiceProxy;
    @Autowired
    private IStepReinspectService[] stepReinspectServices;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;

    public WsStepUnqualifiedItemService(BatchWorkDetailRepository batchWorkDetailRepository,WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository,
                                        WsStepRepository wsStepRepository,
                                        SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository, CommonService commonService) {
        this.wsStepUnqualifiedItemRepository = wsStepUnqualifiedItemRepository;
        this.wsStepRepository = wsStepRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.commonService = commonService;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<WsStepUnqualifiedItem> find(Specification<WsStepUnqualifiedItem> spec, Pageable pageable) {
        Page<WsStepUnqualifiedItem> page = wsStepUnqualifiedItemRepository.findAll(spec, pageable,
                new NamedEntityGraph(WS_STEP_UNQUALIFIED_ITEM_ENTITY_GRAPH));
        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(net.airuima.util.ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

    @Override
    @FetchMethod
    public List<WsStepUnqualifiedItem> find(Specification<WsStepUnqualifiedItem> spec) {
        return wsStepUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(WS_STEP_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<WsStepUnqualifiedItem> findAll(Pageable pageable) {
        Page<WsStepUnqualifiedItem> page = wsStepUnqualifiedItemRepository.findAll(pageable,
                new NamedEntityGraph(WS_STEP_UNQUALIFIED_ITEM_ENTITY_GRAPH));

        Optional.ofNullable(page)
                .map(Page::getContent)
                .filter(net.airuima.util.ValidateUtils::isValid)
                .ifPresent(content -> content
                        .forEach(item -> item.setDocumentDTOList(rbaseDocumentProxy.getByRecordId(item.getId()))));
        return page;
    }

    /**
     * 根据投产工单id及工序id获取不良信息
     *
     * @param productWorkSheetId 投产工单ID
     * @param stepId         工序ID
     * @return List<WsStepUnqualifiedItem>
     * <AUTHOR>
     * @date 2021-04-15
     **/
    @Transactional(readOnly = true)
    public List<WsStepUnqualifiedItemDTO> findByProductWorkSheetIdAndStepId(Long productWorkSheetId, Long stepId) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isPresent()){
            List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList =  wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheetOptional.get().getId(), stepId, Constants.LONG_ZERO);
            return CollectionUtils.isEmpty(wsStepUnqualifiedItemList) ? new ArrayList<>(): MapperUtils.mapAll(wsStepUnqualifiedItemList, net.airuima.rbase.web.rest.procedure.batch.dto.WsStepUnqualifiedItemDTO.class);
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        if(workSheetOptional.isPresent()){
            List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheetOptional.get().getId(), stepId, Constants.LONG_ZERO);
            return CollectionUtils.isEmpty(wsStepUnqualifiedItemList) ? new ArrayList<>(): MapperUtils.mapAll(wsStepUnqualifiedItemList, net.airuima.rbase.web.rest.procedure.batch.dto.WsStepUnqualifiedItemDTO.class);
        }
        return new ArrayList<>();
    }


    /**
     * 通过总工单ID获取可以生成返工的所有不良相关信息
     * @param workSheetId 工单id
     * @param stepOrWsLevel 工序Or子工单层级
     * <AUTHOR>
     * @date  2021/12/17
     * @return WsStepUnqualifiedItemDTO
     */
    @Transactional(readOnly = true)
    public ReworkGetSubWsDTO findSubWorkSheetUnqualifiedInfo(Long workSheetId, Integer stepOrWsLevel){
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        if(workSheetOptional.isEmpty()){
            throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
        }
        // 判断总工单的状态
        WorkSheet ws = workSheetOptional.get();
        if (ws.getStatus() == Constants.PAUSE){
            throw new ResponseException("error.WorkSheetPause", "工单已暂停");
        }
        //(获取子工单)根据配置来判断获取不良生成返工的规则，isStepOrWsLevel：isStepLevel(子工单工序完成层级)：0;isWsLevel(子工单完成层级):1;
        List<SubWorkSheet> subWorkSheetList = new ArrayList<>();
        if (stepOrWsLevel != null && ConstantsEnum.IS_STEP_LEVEL.getCategoryName() == stepOrWsLevel){
            subWorkSheetList.addAll(subWorkSheetRepository.findByWorkSheetIdAndDeletedAndActualStartDateIsNotNull(ws.getId(),Constants.LONG_ZERO));
        }
        if (stepOrWsLevel == null || ConstantsEnum.IS_WS_LEVEL.getCategoryName() == stepOrWsLevel){
            subWorkSheetList.addAll(subWorkSheetRepository.findByWorkSheetIdAndStatusGreaterThanEqualAndDeleted(workSheetId, Constants.FINISH, Constants.LONG_ZERO));
        }
        if (!ValidateUtils.isValid(subWorkSheetList) && ConstantsEnum.IS_WS_LEVEL.getCategoryName() == stepOrWsLevel) {
            throw new ResponseException("error.notExistFinishedSubWorkSheet", "不存在已完成的子工单");
        }
        if (!ValidateUtils.isValid(subWorkSheetList) && ConstantsEnum.IS_STEP_LEVEL.getCategoryName() == stepOrWsLevel) {
            throw new ResponseException("error.notExistFinishedStepSubWorkSheet", "不存在已完成工序的子工单");
        }
        //通过总工单ID获取定制工序列表(TODO:目前产品并没有转工艺的过程，工艺快照都是按照总工单来，之后有此功能先按子工单的工艺快照，没有则按照总工单的工艺快照)
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        //工单的投产工序管控模式全为单支管控模式则直接返回提醒走单支在线返修(当前工单为单支在线返修模式)
        if (wsStepList.stream().allMatch(wsStep -> wsStep.getControlMode() == ConstantsEnum.SN_CONTROL_MODE.getCategoryName())) {
            throw new ResponseException("error.illegalReworkWay", "单支请直接在线返工");
        }
        //查找非单支管控模式工序
        List<WsStep> firstWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).toList();
        //分组：单支与批量管控模式:批量管控模式工序列表(TODO:目前未完成管控为单支的模式)
        Set<Step> stepList = firstWsStepList.stream().map(WsStep::getStep).collect(Collectors.toSet());
        firstWsStepList.forEach(wsStep -> {
            if (StringUtils.isNotBlank(wsStep.getAfterStepId())) {
                findNotSnControlStep(wsStepList, wsStep, stepList);
            }
        });
        //获取到每个子工单对应工序的工作详情为完成状态的
        Map<SubWorkSheet, List<WsStep>> subWorkSheetListMap = new HashMap<>();
        subWorkSheetList.forEach(subWorkSheet -> {
            List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndFinishAndDeleted(subWorkSheet.getId(),stepList.stream().map(Step::getId).collect(Collectors.toList()),ConstantsEnum.FINISH_STATUS.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
            if (net.airuima.util.ValidateUtils.isValid(batchWorkDetailList)){
                List<Long> stepIds = batchWorkDetailList.stream().map(BatchWorkDetail::getStep).toList().stream().map(Step::getId).toList();
                subWorkSheetListMap.put(subWorkSheet, wsStepList.stream().filter(wsStep -> stepIds.contains(wsStep.getStep().getId())).collect(Collectors.toList()));
            }
        });

        ReworkGetSubWsDTO reworkGetDTO = new ReworkGetSubWsDTO();
        reworkGetDTO.setWorkSheetId(workSheetId);
        //获取已完成工序子工单可生成返工的不良组别汇总数据信息
        subWorkSheetListMap.forEach((subWorkSheet,wsSteps)->{
            rbaseIMaintainServiceProxy.validReWorkMaintain(subWorkSheet.getId(),wsSteps.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()));
            stepReinspectServices[0].validReWorkStepReinspect(subWorkSheet.getId(),workSheetId,wsSteps.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()));
            List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdInAndUnqualifiedItemDealWayAndDeleted(subWorkSheet.getId(), wsSteps.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()),ConstantsEnum.DEAL_WAY_ONLINE_REPAIR.getCategoryName(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsStepUnqualifiedItemList)){
                wsStepUnqualifiedItemList = wsStepUnqualifiedItemList.stream().filter(wsStepUnqualifiedItem -> (wsStepUnqualifiedItem.getNumber() - wsStepUnqualifiedItem.getRepairCount()) > 0).collect(Collectors.toList());
            }
            if (ValidateUtils.isValid(wsStepUnqualifiedItemList)){
                this.processByUnqualifiedGroup(wsStepUnqualifiedItemList, ws, subWorkSheet, reworkGetDTO);
                this.processByStep(wsStepUnqualifiedItemList, ws, subWorkSheet, reworkGetDTO);
            }
        });
        if(!ValidateUtils.isValid(reworkGetDTO.getUnqualifiedGroupDtoList()) && !ValidateUtils.isValid(reworkGetDTO.getSubWsStepDtoList())){
            throw new ResponseException("error.UnqualifiedGroupEmpty", "当前工单无法生成返工单，请检查工单状态或是否有不良项目");
        }
        return reworkGetDTO;
    }

    /**
     * 按照不良工序
     * @param wsStepUnqualifiedItemList 工单id
     * @param ws 工单
     * @param subWorkSheet 子工单
     * @param reworkGetDto 返修不良相关DTO
     * <AUTHOR>
     * @date  2022/12/30
     */
    private void processByStep(List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList, WorkSheet ws, SubWorkSheet subWorkSheet, ReworkGetSubWsDTO reworkGetDto) {
        wsStepUnqualifiedItemList = wsStepUnqualifiedItemList.stream().filter(wsStepUnqualifiedItem -> wsStepUnqualifiedItem.getStep() != null).collect(Collectors.toList());
        Map<Step, List<WsStepUnqualifiedItem>> stepListMap = wsStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(WsStepUnqualifiedItem::getStep));
        stepListMap.forEach((step, wsStepUnqualifiedItems) -> {
            AtomicBoolean findSame = new AtomicBoolean(Constants.FALSE);
            //不良数量
            Integer unqualifiedNumber = wsStepUnqualifiedItems.stream().map(wsStepUnqualifiedItem -> wsStepUnqualifiedItem.getNumber()-wsStepUnqualifiedItem.getRepairCount()).reduce(Integer::sum).orElse(Constants.INT_ZERO);
            //不良项目工序列表
            List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO> unqualifiedItemStepDtoList = wsStepUnqualifiedItems.stream().map(ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO::new).collect(Collectors.toList());
            //累加相同的不良工序的数量，以及子工单对应的不良数量
            if (reworkGetDto.getSubWsStepDtoList() == null){
                reworkGetDto.setSubWsStepDtoList(new ArrayList<>());
            }
            reworkGetDto.getSubWsStepDtoList().forEach(subStepDTO -> {
                if (subStepDTO.getStepId().equals(step.getId())) {
                    findSame.set(true);
                    subStepDTO.setNumber(subStepDTO.getNumber() + unqualifiedNumber);
                    subStepDTO.getSubStepInfoDtoList().add(new ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), unqualifiedNumber,unqualifiedItemStepDtoList));
                }
            });
            Set<WorkFlow> workFlows = new HashSet<>();
            List<Long> unqualifiedItemIds = wsStepUnqualifiedItems.stream().map(WsStepUnqualifiedItem::getUnqualifiedItem).map(UnqualifiedItem::getUnqualifiedGroup).map(UnqualifiedGroup::getId).collect(Collectors.toList());
            if (ValidateUtils.isValid(unqualifiedItemIds)){
                unqualifiedItemIds.forEach(id -> {
                    Set<WorkFlow> workFlowSet = commonService.findReworkWorkFlowByPedigreeAndUnqualifiedGroupIdAndClientId(ws.getPedigree(), id, ws.getClientId());
                    if (workFlowSet == null) {
                        return;
                    }
                    workFlows.addAll(workFlowSet);
                });
            }
            if (!findSame.get()){
                ReworkGetSubWsDTO.UnqualifiedItemStepDTO subStepDto = new ReworkGetSubWsDTO.UnqualifiedItemStepDTO();
                List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO> subWsUnqualifiedGroupInfoDtoList = Lists.newArrayList();
                subWsUnqualifiedGroupInfoDtoList.add(new ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), unqualifiedNumber,unqualifiedItemStepDtoList));
                subStepDto.setStepId(step.getId())
                        .setStepCode(step.getCode())
                        .setStepName(step.getName())
                        .setNumber(unqualifiedNumber)
                        .setSubStepInfoDtoList(subWsUnqualifiedGroupInfoDtoList)
                        .setWorkFlow(workFlows);
                reworkGetDto.getSubWsStepDtoList().add(subStepDto);
            }
        });
        // 过滤不良数量为0的数据
        reworkGetDto.setSubWsStepDtoList(reworkGetDto.getSubWsStepDtoList().stream().filter(unqualifiedItemStepDto -> unqualifiedItemStepDto.getNumber() > 0).collect(Collectors.toList()));
    }

    /**
     * 按照不良工序
     * @param wsStepUnqualifiedItemList 工单id
     * @param ws 工单
     * @param subWorkSheet 子工单
     * @param reworkGetDto 返修不良相关DTO
     * <AUTHOR>
     * @date  2021/12/17
     */
    private void processByUnqualifiedGroup(List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList, WorkSheet ws, SubWorkSheet subWorkSheet, ReworkGetSubWsDTO reworkGetDto) {
        //过滤掉没有不良组别的不良项
        wsStepUnqualifiedItemList = wsStepUnqualifiedItemList.stream().filter(wsStepUnqualifiedItem -> wsStepUnqualifiedItem.getUnqualifiedItem().getUnqualifiedGroup() != null).collect(Collectors.toList());
        Map<UnqualifiedGroup, List<WsStepUnqualifiedItem>> unqualifiedGroupListMap = wsStepUnqualifiedItemList.stream().collect(Collectors.groupingBy(wsStepUnqualifiedItem -> wsStepUnqualifiedItem.getUnqualifiedItem().getUnqualifiedGroup()));
        unqualifiedGroupListMap.forEach((unqualifiedGroup, wsStepUnqualifiedItems) -> {
            AtomicBoolean findSame = new AtomicBoolean(Constants.FALSE);
            //不良数量
            Integer unqualifiedNumber = wsStepUnqualifiedItems.stream().map(wsStepUnqualifiedItem -> wsStepUnqualifiedItem.getNumber()-wsStepUnqualifiedItem.getRepairCount()).reduce(Integer::sum).orElse(Constants.INT_ZERO);
            //不良项目工序列表
            List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO> unqualifiedItemStepDtoList = wsStepUnqualifiedItems.stream().map(ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO::new).collect(Collectors.toList());
            //累加相同的不良组别的数量，以及子工单对应的不良数量
            if (reworkGetDto.getUnqualifiedGroupDtoList() == null){
                reworkGetDto.setUnqualifiedGroupDtoList(new ArrayList<>());
            }
            reworkGetDto.getUnqualifiedGroupDtoList().forEach(reworkGetSubWsUnqualifiedGroupDTO -> {
                if (reworkGetSubWsUnqualifiedGroupDTO.getUnqualifiedGroupId().equals(unqualifiedGroup.getId())) {
                    findSame.set(true);
                    reworkGetSubWsUnqualifiedGroupDTO.setNumber(reworkGetSubWsUnqualifiedGroupDTO.getNumber() + unqualifiedNumber);
                    reworkGetSubWsUnqualifiedGroupDTO.getSubWsUnqualifiedGroupInfoDtoList().add(new ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), unqualifiedNumber,unqualifiedItemStepDtoList));
                }
            });
            if (!findSame.get()){
                ReworkGetSubWsDTO.UnqualifiedGroupDTO reworkGetSubWsUnqualifiedGroupDto = new ReworkGetSubWsDTO.UnqualifiedGroupDTO();
                List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO> subWsUnqualifiedGroupInfoDtoList = Lists.newArrayList();
                subWsUnqualifiedGroupInfoDtoList.add(new ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), unqualifiedNumber,unqualifiedItemStepDtoList));
                reworkGetSubWsUnqualifiedGroupDto.setUnqualifiedGroupId(unqualifiedGroup.getId())
                        .setUnqualifiedGroupCode(unqualifiedGroup.getCode())
                        .setUnqualifiedGroupName(unqualifiedGroup.getName())
                        .setNumber(unqualifiedNumber)
                        .setSubWsUnqualifiedGroupInfoDtoList(subWsUnqualifiedGroupInfoDtoList);
                reworkGetDto.getUnqualifiedGroupDtoList().add(reworkGetSubWsUnqualifiedGroupDto);
            }
        });
        // 过滤不良数量为0的数据
        reworkGetDto.setUnqualifiedGroupDtoList(reworkGetDto.getUnqualifiedGroupDtoList().stream().filter(unqualifiedGroupDto -> unqualifiedGroupDto.getNumber() > 0).collect(Collectors.toList()));
        //添加返修工艺路线（通过产品谱系和不良组别获取，需要配置，没有配置则为空）
        reworkGetDto.getUnqualifiedGroupDtoList().forEach(reworkGetSubWsUnqualifiedGroupDto->reworkGetSubWsUnqualifiedGroupDto.setWorkFlow(commonService.findReworkWorkFlowByPedigreeAndUnqualifiedGroupIdAndClientId(ws.getPedigree(), reworkGetSubWsUnqualifiedGroupDto.getUnqualifiedGroupId(),ws.getClientId())));
    }


    /**
     * 递归获取定制工序的各个分支上第一个单支管控模式工序以上的各个批量管控模式的工序列表
     *
     * @param wsStepList 定制工序列表
     * @param currWsStep 当前定制工序
     * @param stepList   批量管控模式的工序列表
     * <AUTHOR>
     * @date 2021-04-29
     **/
    public void findNotSnControlStep(List<WsStep> wsStepList, WsStep currWsStep, Set<Step> stepList) {
        List<WsStep> wsSteps = wsStepList.stream().filter(wsStep -> currWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString()) && wsStep.getControlMode() != ConstantsEnum.SN_CONTROL_MODE.getCategoryName()).collect(Collectors.toList());
        if (ValidateUtils.isValid(wsSteps)){
            wsSteps.forEach(wsStep -> {
                stepList.add(wsStep.getStep());
                if (StringUtils.isNotBlank(wsStep.getAfterStepId())) {
                    findNotSnControlStep(wsStepList, wsStep, stepList);
                }
            });
        }
    }

}
