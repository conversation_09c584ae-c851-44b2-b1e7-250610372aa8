package net.airuima.rbase.service.base.quality;

import net.airuima.rbase.domain.base.quality.OnlineReworkRule;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修工序不良现象规则Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OnlineReworkRuleService extends CommonJpaService<OnlineReworkRule> {

    private final OnlineReworkRuleRepository onlineReworkRuleRepository;

    public OnlineReworkRuleService(OnlineReworkRuleRepository onlineReworkRuleRepository) {
        this.onlineReworkRuleRepository = onlineReworkRuleRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OnlineReworkRule> find(Specification<OnlineReworkRule> spec, Pageable pageable) {
        return onlineReworkRuleRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<OnlineReworkRule> find(Specification<OnlineReworkRule> spec) {
        return onlineReworkRuleRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OnlineReworkRule> findAll(Pageable pageable) {
        return onlineReworkRuleRepository.findAll(pageable);
    }

}
