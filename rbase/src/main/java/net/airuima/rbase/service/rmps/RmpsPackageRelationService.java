package net.airuima.rbase.service.rmps;

import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailMaterialBatchRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO;
import net.airuima.rbase.web.rest.rmps.dto.SpecifyContentRelationDTO;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class RmpsPackageRelationService {

    private final BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    private final ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;

    public RmpsPackageRelationService(BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository, ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository) {
        this.batchWorkDetailMaterialBatchRepository = batchWorkDetailMaterialBatchRepository;
        this.containerDetailMaterialBatchRepository = containerDetailMaterialBatchRepository;
    }

    /**
     * 获取（子）工单 指定标签参数
     *
     * @param specifyContentRelationDto 请求标签参数
     * @return List<PackRelationDataDTO>
     */
    public List<PackRelationDataDTO> findSpecifyContentRelationData(SpecifyContentRelationDTO specifyContentRelationDto) {

        boolean subWsProductionMode = commonService.subWsProductionMode();

        MaterialDTO materialDto = Objects.isNull(specifyContentRelationDto.getMaterialCode()) ? null : rbaseMaterialProxy.findByCodeAndDeleted(specifyContentRelationDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
        Long materialId = Objects.isNull(materialDto) || Objects.isNull(materialDto.getId()) ? null : materialDto.getId();
        List<PackRelationDataDTO> packRelationDataDTOList;

        if (specifyContentRelationDto.getSubWsProductionMode() || subWsProductionMode) {
            List<String> subWsSerialNumbers = Lists.newArrayList();
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(specifyContentRelationDto.getSerialNumber(), Constants.LONG_ZERO);
            if (workSheetOptional.isEmpty()) {
                subWsSerialNumbers.add(specifyContentRelationDto.getSerialNumber());
            } else {
                List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheetOptional.get().getId(), Constants.LONG_ZERO);
                if (!ValidateUtils.isValid(subWorkSheets)) {
                    subWsSerialNumbers.add(specifyContentRelationDto.getSerialNumber());
                } else {
                    subWsSerialNumbers.addAll(subWorkSheets.stream().map(SubWorkSheet::getSerialNumber).toList());
                }
            }
            if(StringUtils.isNotBlank(specifyContentRelationDto.getSn())){
                packRelationDataDTOList = snWorkDetailMaterialBatchRepository.findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(subWsSerialNumbers, materialId, specifyContentRelationDto.getMaterialBatch(), specifyContentRelationDto.getSn());
            } else if (StringUtils.isNotBlank(specifyContentRelationDto.getContainerCode())) {
                packRelationDataDTOList = containerDetailMaterialBatchRepository.findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(subWsSerialNumbers, materialId, specifyContentRelationDto.getMaterialBatch(), specifyContentRelationDto.getContainerCode());
            } else {
                packRelationDataDTOList = batchWorkDetailMaterialBatchRepository.findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(subWsSerialNumbers, materialId, specifyContentRelationDto.getMaterialBatch());
            }
        } else {
            if(StringUtils.isNotBlank(specifyContentRelationDto.getSn())){
                packRelationDataDTOList = snWorkDetailMaterialBatchRepository.findPackRelationDataByWsAndMaterialIdAndMaterialBatch(specifyContentRelationDto.getSerialNumber(), materialId, specifyContentRelationDto.getMaterialBatch(), specifyContentRelationDto.getSn());
            }else if (StringUtils.isNotBlank(specifyContentRelationDto.getContainerCode())) {
                packRelationDataDTOList = containerDetailMaterialBatchRepository.findPackRelationDataByWsAndMaterialIdAndMaterialBatch(specifyContentRelationDto.getSerialNumber(), materialId, specifyContentRelationDto.getMaterialBatch(), specifyContentRelationDto.getContainerCode());
            } else {
                packRelationDataDTOList = batchWorkDetailMaterialBatchRepository.findPackRelationDataByWsAndMaterialIdAndMaterialBatch(specifyContentRelationDto.getSerialNumber(), materialId, specifyContentRelationDto.getMaterialBatch());
            }
        }
        packRelationDataDTOList = ValidateUtils.isValid(packRelationDataDTOList) ? packRelationDataDTOList.stream().peek(packRelationDataDTO -> packRelationDataDTO.setMaterialName(packRelationDataDTO.getMaterialDto().getName()).setMaterialCode(packRelationDataDTO.getMaterialDto().getCode())).collect(Collectors.toList()) : null;
        if (ValidateUtils.isValid(packRelationDataDTOList) && ValidateUtils.isValid(specifyContentRelationDto.getMaterialCode())) {
            packRelationDataDTOList = packRelationDataDTOList.stream().filter(packRelationDataDTO -> Objects.equals(specifyContentRelationDto.getMaterialCode(), packRelationDataDTO.getMaterialCode())).collect(Collectors.toList());
        }
        return packRelationDataDTOList;
    }
}
