package net.airuima.rbase.service.procedure.flowable.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.service.procedure.flowable.IFlowableCallBackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class FlowableCallBackServiceImpl implements IFlowableCallBackService {

    @Autowired
    private WorkSheetService workSheetService;

    /**
     * 工作流连线事件
     *
     * @param processRunTimeEventDto 流程节点完成返回信息DTO
     */
    @Override
    public void flowableSequenceListener(ProcessRunTimeEventDTO processRunTimeEventDto) {
        if (Constants.FLOWABLE_WORKSHEET_PROCESS_KEY.equals(processRunTimeEventDto.getProcessDefinitionKey())) {
            workSheetService.flowableSequenceListener(processRunTimeEventDto);
        }
    }
}
