package net.airuima.rbase.service.base.scene.api;

import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.scene.WorkLineDTO;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@FuncInterceptor("WorkStation")
public interface IWorkStationService {

    /**
     * 通过生产线主键ID获取工站及对应工位信息
     * @param workLineId 生产线主键ID
     * @return java.util.List<net.airuima.rbase.dto.scene.WorkLineDTO.WorkStationInfo> 工站工位信息
     */
    default List<WorkLineDTO.WorkStationInfo> findWorStationWorkCellInfo(Long workLineId){
        return Lists.newArrayList();
    }
}
