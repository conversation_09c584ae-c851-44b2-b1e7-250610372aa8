package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailFacilityRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器设备生产详情Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailFacilityService extends CommonJpaService<ContainerDetailFacility> {
    private static final String CONTAINER_DETAIL_EQUIPMENT_ENTITY_GRAPH = "containerDetailEquipmentEntityGraph";
    private final ContainerDetailFacilityRepository containerDetailFacilityRepository;

    public ContainerDetailFacilityService(ContainerDetailFacilityRepository containerDetailFacilityRepository) {
        this.containerDetailFacilityRepository = containerDetailFacilityRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailFacility> find(Specification<ContainerDetailFacility> spec, Pageable pageable) {
        return containerDetailFacilityRepository.findAll(spec, pageable,new NamedEntityGraph(CONTAINER_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<ContainerDetailFacility> find(Specification<ContainerDetailFacility> spec) {
        return containerDetailFacilityRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailFacility> findAll(Pageable pageable) {
        return containerDetailFacilityRepository.findAll(pageable,new NamedEntityGraph(CONTAINER_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

}
