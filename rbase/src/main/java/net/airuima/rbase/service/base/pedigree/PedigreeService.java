package net.airuima.rbase.service.base.pedigree;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.domain.base.AuditSfIdEntity;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.AuthorityConstants;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.FuncKeyConstants;
import net.airuima.rbase.constant.PedigreeConstants;
import net.airuima.rbase.domain.base.pedigree.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.dto.pedigree.PedigreeConfigOptionDTO;
import net.airuima.rbase.dto.pedigree.PedigreeDTO;
import net.airuima.rbase.dto.pedigree.PedigreeDetailDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.dto.sync.SyncMaterialDTO;
import net.airuima.rbase.dto.sync.SyncPedigreeDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialUnitProxy;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.dynamicdata.RbaseStepDynamicDataProxy;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.*;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeModelCloneDTO;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeTypeDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Order(-1)
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeService extends CommonJpaService<Pedigree> implements PedigreeInterface {

    private final String PEDIGREE_ENTITY_GRAPH = "pedigreeEntityGraph";
    private final PedigreeRepository pedigreeRepository;
    @Autowired
    private PedigreeConfigRepository pedigreeConfigRepository;
    @Autowired
    private PedigreeStepRepository pedigreeStepRepository;
    @Autowired
    private PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository;
    @Autowired
    private PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository;
    @Autowired
    private PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository;
    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private PedigreeWorkFlowRepository pedigreeWorkFlowRepository;
    @Autowired
    private PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;
    @Autowired
    private RbaseMaterialUnitProxy rbaseMaterialUnitProxy;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;

    @Autowired
    private PedigreeStepUnqualifiedItemService pedigreeStepUnqualifiedItemService;

    @Autowired
    private PedigreeStepIntervalConfigRepository pedigreeStepIntervalConfigRepository;

    @Autowired
    private PedigreeSnReuseConfigRepository pedigreeSnReuseConfigRepository;

    @Autowired
    private RbaseStepDynamicDataProxy rbaseStepDynamicDataProxy;

    @Autowired
    private PedigreeStepPassRateRepository pedigreeStepPassRateRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;


    public PedigreeService(PedigreeRepository pedigreeRepository) {
        this.pedigreeRepository = pedigreeRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<Pedigree> find(Specification<Pedigree> spec, Pageable pageable) {
        return pedigreeRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<Pedigree> find(Specification<Pedigree> spec) {
        return pedigreeRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<Pedigree> findAll(Pageable pageable) {
        return pedigreeRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_ENTITY_GRAPH));
    }

    /**
     * 通过名称或编码和产品谱系类型和查询数量和是否启用查询产品谱系列表
     * @param text 名称或编码
     * @param type 产品谱系类型
     * @param size 查询数量
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @Transactional(readOnly = true)
    @Override
    public List<Pedigree> findByNameOrCode(String text, Integer type, Integer size, Boolean isEnable) {
        List<Pedigree> list;
        if (null == type) {
            list = Optional.ofNullable(pedigreeRepository
                    .findByNameOrCode(StringUtils.isNotBlank(text) ? text : null, null != isEnable ? isEnable : Boolean.TRUE, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
        } else {
            list = Optional.ofNullable(pedigreeRepository.findByTypeAndNameOrCode(StringUtils.isNotBlank(text) ? text : null, type, null != isEnable ? isEnable : Boolean.TRUE, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
        }
        return list;
    }

    /**
     * 通过产品谱系类型和是否启用查找产品谱系列表
     * @param type 产品谱系类型
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @Transactional(readOnly = true)
    @Override
    public List<Pedigree> findByType(Integer type, Boolean isEnable) {
        List<Pedigree> list = pedigreeRepository.findByTypeAndDeleted(type, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(list)) {
            return null != isEnable ? list.stream().filter(pedigree -> isEnable == pedigree.getIsEnable()).collect(Collectors.toList()) : list;
        }
        return list;
    }


    /**
     * 新增产品谱系
     * @param pedigreeDto 产品谱系/产品谱系配置/物料DT
     */
    @Override
    public ResponseEntity<Void> createInstance(PedigreeDTO pedigreeDto) {
        ModelMapper modelMapper = new ModelMapper();
        //保存产品谱系
        Pedigree pedigree = modelMapper.map(pedigreeDto, Pedigree.class);
        //父级产品谱系
        Optional<Pedigree> parentPedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeDto.getParent().getId(), Constants.LONG_ZERO);
        parentPedigreeOptional.ifPresent(pedigree::setParent);
        //如果当前产品谱系层级是最大层级,且没有选择物料,新增该产品谱系需要对应的新增物料信息
        if (Boolean.TRUE.equals(pedigreeDto.getIsLastLevel()) && pedigreeDto.getMaterial() == null) {
            //调用bom服务，生成物料
            MaterialDTO materialDto = new MaterialDTO()
                    .setCode(pedigree.getCode())
                    .setName(pedigree.getName())
                    .setDisplay(true)
                    .setIsEnable(true)
                    .setSpecification(pedigree.getSpecification());
            materialDto = rbaseBomProxy.saveInstance(materialDto);
            pedigree.setMaterialId(materialDto.getId());
        }
        this.save(pedigree);
        //保存产品谱系配置
        PedigreeConfig pedigreeConfig = modelMapper.map(pedigreeDto, PedigreeConfig.class);
        pedigreeConfig.setPedigree(pedigree);
        pedigreeConfig.setDeleted(Constants.LONG_ZERO);
        pedigreeConfigRepository.save(pedigreeConfig);
        return ResponseEntity.ok().headers(HeaderUtil.createdAlert(StringUtils.uncapitalize(Pedigree.class.getSimpleName()), pedigree.getId().toString())).build();
    }

    /**
     * 修改产品谱系，可能需要修改对应产品谱系配置和对应物料相关数据
     *
     * @param pedigreeDto 产品谱系/产品谱系配置/物料DTO
     */
    @Override
    public ResponseEntity<Void> updateInstance(PedigreeDTO pedigreeDto) {
        try {
            ModelMapper modelMapper = new ModelMapper();
            //修改产品谱系
            Pedigree pedigree = modelMapper.map(pedigreeDto, Pedigree.class);
            //父级产品谱系
            Optional<Pedigree> parentPedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeDto.getParent().getId(), Constants.LONG_ZERO);
            parentPedigreeOptional.ifPresent(pedigree::setParent);
            pedigree.setDeleted(Constants.LONG_ZERO);
            if (null != pedigree.getId()) {
                pedigree = this.updateInstance(pedigree).getBody();
            }
            //修改产品谱系配置
            Optional<PedigreeConfig> pedigreeConfigOptional = pedigreeConfigRepository.findByPedigreeIdAndDeleted(pedigree.getId(), Constants.LONG_ZERO);
            pedigreeConfigOptional.ifPresent(pedigreeConfig -> {
                pedigreeConfig.setSplitNumber(pedigreeDto.getSplitNumber())
                        .setIsCustomWorkFlow(pedigreeDto.getIsCustomWorkFlow())
                        .setPlanFinishDay(pedigreeDto.getPlanFinishDay())
                        .setQualifiedRate(pedigreeDto.getQualifiedRate())
                        .setLastModifiedDate(Instant.now());
                pedigreeConfig.setDeleted(Constants.LONG_ZERO);
                pedigreeConfigRepository.save(pedigreeConfig);
            });
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(Pedigree.class.getSimpleName()), pedigree.getId().toString())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(Pedigree.class.getSimpleName()), "updateFailure", e.toString())).build();
        }

    }

    /**
     * 修改产品谱系
     * @param entity 产品谱系
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.Pedigree>  产品谱系
     */
    @Override
    public ResponseEntity<Pedigree> updateInstance(Pedigree entity) {
        Pedigree oldInstance = pedigreeRepository.getReferenceById(entity.getId());
        List<Pedigree> parentList = Lists.newArrayList();
        parentList.add(oldInstance);
        if (!entity.getIsEnable() == oldInstance.getIsEnable()) {
            List<Pedigree> childList = Lists.newArrayList();
            this.findChildPedigree(parentList, childList);
            if (ValidateUtils.isValid(childList)) {
                childList.forEach(pedigree -> {
                    pedigree.setIsEnable(entity.getIsEnable());
                    this.save(pedigree);
                });
            }
        }
        this.save(entity);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(Pedigree.class.getSimpleName()), entity.getId().toString())).body(entity);
    }

    /**
     * 递归获取产品谱系的所有子节点
     *
     * @param parentList 父级节点列表
     * @param childList  子级节点列表
     * @return void
     * <AUTHOR>
     * @date 2021-06-10
     **/
    @Transactional(readOnly = true)
    public void findChildPedigree(List<Pedigree> parentList, List<Pedigree> childList) {
        for (Pedigree pedigree : parentList) {
            List<Pedigree> childPedigrees = pedigreeRepository.findByParentIdAndDeleted(pedigree.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(childPedigrees)) {
                childList.addAll(childPedigrees);
                this.findChildPedigree(childPedigrees, childList);
            }
        }
    }

    /**
     * 获取产品谱系树形展示数据
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @Transactional(readOnly = true)
    @Override
    public List<Pedigree> getTreeData(Boolean isEnable) {
        List<Pedigree> pedigreeList = Optional.ofNullable(pedigreeRepository.findByDeleted(0L)).orElse(new ArrayList<>());
        Map<Long, List<Pedigree>> parentIdPedigreeList = pedigreeList.stream().collect(Collectors.groupingBy(p -> p.getParent() == null ? -1L : p.getParent().getId()));
        pedigreeList.forEach(p -> {
            p.setParent(null);
            p.setChildPedigreeList(parentIdPedigreeList.get(p.getId()));
        });
        List<Pedigree> list = parentIdPedigreeList.get(-1L);
        return null != isEnable && ValidateUtils.isValid(list) ? list.stream().filter(pedigree -> isEnable == pedigree.getIsEnable()).collect(Collectors.toList()) : list;
    }

    /**
     * 获取产品谱系工序所有配置项
     * @param clientId 客户主键Id
     * @param pedigreeId 产品谱系主键Id
     * @param workFlowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @return net.airuima.dto.pedigree.PedigreeConfigOptionDTO  产品谱系工序所有配置项
     */
    @Transactional(readOnly = true)
    @Override
    public PedigreeConfigOptionDTO findPedigreeConfigOption(Long clientId, Long pedigreeId, Long workFlowId, Long stepId) {
        Optional<PedigreeStep> pedigreeStep = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndEnableAndDeleted(clientId, pedigreeId, workFlowId, stepId, Boolean.TRUE, Constants.LONG_ZERO);
        PedigreeStepSpecification pedigreeStepSpecification = commonService.findPedigreeStepSpecification(pedigreeId, workFlowId, stepId, clientId);
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRules = commonService.findPedigreeStepMaterialRule(clientId, pedigreeId, workFlowId, stepId);
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItems = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId, workFlowId, stepId, clientId, Constants.LONG_ZERO);
        //获取检查规则
        Step step = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.stepNotExist", "生产工序不存在"));
        List<PedigreeStepCheckRule> pedigreeStepCheckRules = pedigreeStepCheckRuleRepository.findAllStandardByElementOrderByPriorityAndPedigreeNoCategory(Collections.singletonList(pedigreeId), null, null, ObjectUtils.isEmpty(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), workFlowId, clientId, null, Constants.LONG_ZERO);
        List<PedigreeStepCheckItem> pedigreeStepCheckItems = new ArrayList<PedigreeStepCheckItem>();
        if (!CollectionUtils.isEmpty(pedigreeStepCheckRules)) {
            List<Long> pedigreeStepCheckRuleIdList = pedigreeStepCheckRules.stream().map(AuditSfIdEntity::getId).collect(Collectors.toList());
            pedigreeStepCheckItems = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdInAndDeleted(pedigreeStepCheckRuleIdList, Constants.LONG_ZERO);
        }
        //查询文件集合
        if (!ObjectUtils.isEmpty(pedigreeStepSpecification)) {
            List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(pedigreeStepSpecification.getId());
            if (!CollectionUtils.isEmpty(documentDTOList)) {
                pedigreeStepSpecification.setDocumentDTOList(documentDTOList);
            }
        }

        return new PedigreeConfigOptionDTO()
                .setPedigreeStep(pedigreeStep.orElse(null))
                .setPedigreeStepSpecification(pedigreeStepSpecification)
                .setPedigreeStepMaterialRuleList(pedigreeStepMaterialRules)
                .setPedigreeStepUnqualifiedItemList(pedigreeStepUnqualifiedItems)
                .setPedigreeStepCheckItems(pedigreeStepCheckItems)
                .setPedigreeStepCheckRules(pedigreeStepCheckRules);
    }

    /**
     * 导入产品谱系数据
     * @param list 数据
     * @param maxLevel 最高层级
     * @param productLevel 生产层级
     * @return java.util.List<java.util.Map<String, Object>>  结果数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Map<String, Object>> importPedigree(List<Map<String, Object>> list, Integer maxLevel, Integer productLevel) {
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        list.forEach(map -> {
            //产品谱系名称
            String name = Optional.ofNullable(map.get("产品谱系名称")).map(Object::toString).orElse(null);
            if (StringUtils.isBlank(name)) {
                map.put("错误信息", "产品谱系名称字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //产品谱系编码
            String code = Optional.ofNullable(map.get("产品谱系编码")).map(Object::toString).orElse(null);
            if (StringUtils.isBlank(code)) {
                map.put("错误信息", "产品谱系编码字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //产品谱系型号
            String specification = Optional.ofNullable(map.get("产品规格型号")).map(Object::toString).orElse(null);
            //父级编码
            String parentCode = Optional.ofNullable(map.get("父级谱系编码")).map(Object::toString).orElse(null);
            //物料编码
            String materialCode = Optional.ofNullable(map.get("物料编码")).map(Object::toString).orElse(null);

            //目标成品率
            Double qualifiedRate = Optional.ofNullable(map.get("目标成品率")).map(object -> Double.valueOf(object.toString())).orElse(null);
            if (null == qualifiedRate) {
                map.put("错误信息", "目标成品率字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //是否检查工单核料
            Boolean isCheckReceiveMaterial = Optional.ofNullable(map.get("是否检查工单核料")).map(object -> object.toString().equals("是")).orElse(null);
            if (null == isCheckReceiveMaterial) {
                map.put("错误信息", "是否检查工单核料字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //计划完成天数
            Integer planFinishDay = Optional.ofNullable(map.get("计划完成天数")).map(object -> Integer.valueOf(object.toString())).orElse(null);
            if (null == planFinishDay) {
                map.put("错误信息", "计划完成天数字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //分单数量
            Integer splitNumber = Optional.ofNullable(map.get("分单数量")).map(object -> Integer.valueOf(object.toString())).orElse(null);
            if (null == splitNumber) {
                map.put("错误信息", "分单数量字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //是否复用SN
            Boolean isReUseSn = Optional.ofNullable(map.get("是否复用SN")).map(object -> "是".equals(object.toString())).orElse(null);
            if (null == isReUseSn) {
                map.put("错误信息", "是否复用SN字段不能为空!");
                illegalDataList.add(map);
                return;
            }
            //是否启用
            Boolean isEnable = Optional.ofNullable(map.get("是否启用")).map(object -> "是".equals(object.toString())).orElse(true);
            Pedigree parentPedigree = null;
            if (ValidateUtils.isValid(parentCode)) {
                parentPedigree = pedigreeRepository.findByCodeAndDeleted(parentCode, Constants.LONG_ZERO).orElse(null);
                if (null == parentPedigree) {
                    map.put("错误信息", "父级谱系编码父级谱系不存在!");
                    illegalDataList.add(map);
                    return;
                }
                if (!parentPedigree.getIsEnable()) {
                    map.put("错误信息", "父级谱系已禁用!");
                    illegalDataList.add(map);
                    return;
                }
            }
            //获得当前产品谱系的类型
            Integer pedigreeType = null;
            if (parentPedigree != null) {
                pedigreeType = parentPedigree.getType() + Constants.INT_ONE;
            } else {
                pedigreeType = maxLevel;
            }
            //查找或新建产品谱系对应的物料
            MaterialDTO materialDto = null;
            if (pedigreeType.equals(productLevel) && StringUtils.isBlank(materialCode)) {
                materialDto = rbaseMaterialProxy.findByCodeAndDeleted(code,Constants.LONG_ZERO).orElse(null);
                if (null == materialDto || null == materialDto.getId()) {
                    map.put("错误信息", "物料编码字段不能为空!");
                    illegalDataList.add(map);
                    return;
                }
                if (!materialDto.getIsEnable()) {
                    map.put("错误信息", "物料已禁用!");
                    illegalDataList.add(map);
                    return;
                }
            }
            Pedigree pedigree = pedigreeRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO).orElse(new Pedigree());
            if (null != materialDto && null != materialDto.getId()) {
                Pedigree instance = pedigreeRepository.findByMaterialIdAndDeleted(materialDto.getId(), Constants.LONG_ZERO).orElse(null);
                if (null != instance && !instance.getId().equals(pedigree.getId())) {
                    map.put("错误信息", "物料编码已存在!");
                    illegalDataList.add(map);
                    return;
                }
            }
            PedigreeConfig pedigreeConfig = null != pedigree.getId() ? pedigreeConfigRepository.findByPedigreeIdAndDeleted(pedigree.getId(), Constants.LONG_ZERO).orElse(new PedigreeConfig()) : new PedigreeConfig();
            //保存产品谱系
            pedigree.setName(name).setCode(code).setIsEnable(true).setParent(parentPedigree).setSpecification(specification).setType(pedigreeType)
                    .setMaterialId(null != materialDto && null != materialDto.getId() ? materialDto.getId() : null).setIsEnable(isEnable);
            pedigree = pedigreeRepository.save(pedigree);
            //保存产品谱系对应配置
            pedigreeConfig.setPedigree(pedigree).setIsCheckReceiveMaterial(isCheckReceiveMaterial)
                    .setPlanFinishDay(planFinishDay).setSplitNumber(splitNumber).setIsReuseSN(isReUseSn).setQualifiedRate(qualifiedRate);
            pedigreeConfigRepository.save(pedigreeConfig);
        });
        return illegalDataList;
    }

    /**
     * 同步物料数据  当物料为半成品和成品时需要更新产品谱系
     * @param syncMaterialDtoList 上传的物料参数集合
     * @return java.util.List<net.airuima.dto.sync.SyncResultDTO> 同步物料结果集合
     */
    @Override
    public List<SyncResultDTO> syncMaterial(List<SyncMaterialDTO> syncMaterialDtoList) {
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncMaterialDtoList)) {
            this.updateMaterialInfo(syncMaterialDtoList, syncResultDtoList);
        }
        return syncResultDtoList;
    }

    /**
     * 同步物料信息，当物料为半成品和成品时需要更新产品谱系
     *
     * @param syncMaterialDtoList 上传的物料参数
     * @return syncResultDtoList 同步结果信息DTO
     * @return maxLevel 生产层级
     **/
    private void updateMaterialInfo(List<SyncMaterialDTO> syncMaterialDtoList, List<SyncResultDTO> syncResultDtoList) {
        syncMaterialDtoList.forEach(syncMaterialDto -> {
            MaterialDTO materialDto = MapperUtils.map(syncMaterialDto, MaterialDTO.class);
            materialDto.setIsEnable(syncMaterialDto.getOperate() != Constants.INT_TWO && syncMaterialDto.getOperate() != Constants.INT_THREE)
                    .setCategory(syncMaterialDto.getMaterialGroup());
            materialDto.setId(null);
            // 获取物料的单位
            if (StringUtils.isNotBlank(syncMaterialDto.getUnit())) {
                MeteringUnitDTO meteringUnitDto = rbaseMaterialUnitProxy.findByCodeAndDeleted(syncMaterialDto.getUnit(),Constants.LONG_ZERO);
                if (meteringUnitDto != null) {
                    materialDto.setMeteringUnit(meteringUnitDto);
                }
            }
            rbaseBomProxy.saveInstance(materialDto);
            syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(), Constants.INT_ONE, ""));
        });
    }

    /**
     * 根据产品谱系ID查找产品谱系详情
     * @param pedigreeId 产品谱系主键ID
     * @param clientId 客户主键ID
     * @param category 工艺路线类型
     * @return net.airuima.dto.pedigree.PedigreeDetailDTO 产品谱系详情
     */
    @Transactional(readOnly = true)
    @Override
    public PedigreeDetailDTO findByPedigree(Long pedigreeId, Long clientId, Integer category) {
        PedigreeDetailDTO pedigreeDetailDTO = new PedigreeDetailDTO();
        // 根据产品谱系id查询工艺路线
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findById(pedigreeId);
        pedigreeOptional.ifPresent(pedigree -> {
            List<WorkFlowDTO> workFlowDTOList = Lists.newArrayList();
            List<StepDTO> stepDTOList = Lists.newArrayList();
            List<StepGroup> stepGroupList = Lists.newArrayList();
            List<WorkFlow> allWorkFlows = Lists.newArrayList();
            if (Objects.isNull(category) || category == Constants.INT_ZERO || category == Constants.INT_TWO) {
                List<WorkFlow> workFlows = commonService.findPedigreeWorkFlowAndClientId(pedigree, clientId, Boolean.TRUE);
                if(!CollectionUtils.isEmpty(workFlows)) {
                    workFlows = workFlows.stream().filter(WorkFlow::getIsEnable).toList();
                }
                allWorkFlows.addAll(workFlows);
            }
            if (Objects.isNull(category) || category == Constants.INT_ONE) {
                List<WorkFlow> reWorkFlows = commonService.findPedigreeReworkWorkFlows(pedigree, clientId, Boolean.TRUE);
                if(!CollectionUtils.isEmpty(reWorkFlows)) {
                    reWorkFlows = reWorkFlows.stream().filter(WorkFlow::getIsEnable).toList();
                }
                allWorkFlows.addAll(reWorkFlows);
            }
            for (WorkFlow workFlow : allWorkFlows) {
                // 根据工艺路线id查询工序列表
                WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
                workFlowDTOList.add(workFlowStepService.findByWorkFlowId(workFlow.getId()));
                List<StepDTO> stepDtoList = workFlowDTO.getStepDtoList();
                stepDTOList.addAll(workFlowDTO.getStepDtoList());
                stepGroupList.addAll(stepDtoList.stream().map(StepDTO::getStepGroup).filter(Objects::nonNull).toList());
            }
            pedigreeDetailDTO.setPedigree(pedigree)
                    .setStepDTOList(stepDTOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StepDTO::getId))), ArrayList::new)))
                    .setStepGroupsList(stepGroupList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StepGroup::getId))), ArrayList::new)))
                    .setWorkFlowDTOList(workFlowDTOList.stream().filter(workFlowDTO -> !ObjectUtils.isEmpty(workFlowDTO.getId()) && Boolean.TRUE.equals(workFlowDTO.getIsEnable())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WorkFlowDTO::getId))), ArrayList::new)));
        });
        return pedigreeDetailDTO;
    }

    /**
     * 同步产品谱系数据
     * @param pedigreeList 同步产品谱系信息列表
     * @return java.util.List<net.airuima.dto.sync.SyncResultDTO> 同步产品谱系结果列表
     */
    @Override
    public List<SyncResultDTO> syncPedigree(List<SyncPedigreeDTO> pedigreeList) {
        //返回同步信息
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();

        if (!ValidateUtils.isValid(pedigreeList)) {
            return syncResultDtoList;
        }

        //优先处理产品种类 没有父级
        List<SyncPedigreeDTO> pedigreeDtoZeroList = pedigreeList.stream().filter(pedigreeDto -> ObjectUtils.isEmpty(pedigreeDto.getParentCode()) && ObjectUtils.isEmpty(pedigreeDto.getMaterialCode()))
                .collect(Collectors.toList());
        if (ValidateUtils.isValid(pedigreeDtoZeroList)) {
            processPedigreeZero(pedigreeDtoZeroList, syncResultDtoList);
        }
        //其次产品系列 有父级，没有对应的物料
        List<SyncPedigreeDTO> pedigreeDtoOneList = pedigreeList.stream().filter(pedigreeDto -> !ObjectUtils.isEmpty(pedigreeDto.getParentCode()) && ObjectUtils.isEmpty(pedigreeDto.getMaterialCode()))
                .collect(Collectors.toList());
        if (ValidateUtils.isValid(pedigreeDtoOneList)) {
            processPedigreeOne(pedigreeDtoOneList, syncResultDtoList);
        }

        //最后产品代码 有父级，有对应的物料
        List<SyncPedigreeDTO> pedigreeDtoTwoList = pedigreeList.stream().filter(pedigreeDto -> !ObjectUtils.isEmpty(pedigreeDto.getParentCode()) && !ObjectUtils.isEmpty(pedigreeDto.getMaterialCode()))
                .collect(Collectors.toList());
        if (ValidateUtils.isValid(pedigreeDtoTwoList)) {
            processPedigreeTwo(pedigreeDtoTwoList, syncResultDtoList);
        }
        return syncResultDtoList;
    }

    /**
     * 产品种类同步处理
     *
     * @param pedigreeDtoZeroList 产品种类列表
     * @param syncResultDtoList   同步返回结果信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/30
     */
    public void processPedigreeZero(List<SyncPedigreeDTO> pedigreeDtoZeroList, List<SyncResultDTO> syncResultDtoList) {
        if (!ValidateUtils.isValid(pedigreeDtoZeroList)) {
            return;
        }
        pedigreeDtoZeroList.forEach(pedigreeDto -> {
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeDto.getCode(), Constants.LONG_ZERO);
            if (validPedigreeSync(pedigreeOptional, pedigreeDto, syncResultDtoList)) {
                updatePedigreeSync(pedigreeDto, pedigreeOptional, null, null, syncResultDtoList);
            }
        });
    }

    /**
     * 产品系列同步处理
     *
     * @param pedigreeDtoOneList 产品系列列表
     * @param syncResultDtoList  同步返回结果信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/30
     */
    public void processPedigreeOne(List<SyncPedigreeDTO> pedigreeDtoOneList, List<SyncResultDTO> syncResultDtoList) {
        if (!ValidateUtils.isValid(pedigreeDtoOneList)) {
            return;
        }
        pedigreeDtoOneList.forEach(pedigreeDto -> {
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeDto.getCode(), Constants.LONG_ZERO);
            if (validPedigreeSync(pedigreeOptional, pedigreeDto, syncResultDtoList)) {
                Optional<Pedigree> parentPedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeDto.getParentCode(), Constants.LONG_ZERO);
                if (parentPedigreeOptional.isEmpty()) {
                    syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_TWO, "产品谱系对应的父级不存在：" + pedigreeDto.getCode()));
                }
                updatePedigreeSync(pedigreeDto, pedigreeOptional, parentPedigreeOptional, null, syncResultDtoList);
            }
        });
    }

    /**
     * 产品代码同步处理
     *
     * @param pedigreeDtoTwoList 产品代码列表
     * @param syncResultDtoList  同步返回结果信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/30
     */
    public void processPedigreeTwo(List<SyncPedigreeDTO> pedigreeDtoTwoList, List<SyncResultDTO> syncResultDtoList) {
        if (!ValidateUtils.isValid(pedigreeDtoTwoList)) {
            return;
        }
        pedigreeDtoTwoList.forEach(pedigreeDto -> {
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeDto.getCode(), Constants.LONG_ZERO);
            if (validPedigreeSync(pedigreeOptional, pedigreeDto, syncResultDtoList)) {
                Optional<Pedigree> parentPedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeDto.getParentCode(), Constants.LONG_ZERO);
                if (parentPedigreeOptional.isEmpty()) {
                    syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_TWO, "产品谱系对应的父级不存在：" + pedigreeDto.getParentCode()));
                }
                MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(pedigreeDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
                if (ObjectUtils.isEmpty(materialDto)) {
                    syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_TWO, "产品谱系对应的物料不存在：" + pedigreeDto.getMaterialCode()));
                }
                updatePedigreeSync(pedigreeDto, pedigreeOptional, parentPedigreeOptional, materialDto, syncResultDtoList);
            }
        });

    }

    /**
     * 通用验证同步产品谱系状态
     *
     * @param pedigreeOptional  当前产品谱系
     * @param pedigreeDto       同步产品谱系
     * @param syncResultDtoList 返回同步信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/6/30
     */
    public Boolean validPedigreeSync(Optional<Pedigree> pedigreeOptional, SyncPedigreeDTO pedigreeDto, List<SyncResultDTO> syncResultDtoList) {
        if (pedigreeOptional.isPresent() && pedigreeDto.getOperate() == Constants.INT_ZERO) {
            syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_TWO, "产品谱系已存在：" + pedigreeDto.getCode()));
            return Boolean.FALSE;
        }
        if (pedigreeOptional.isEmpty() && pedigreeDto.getOperate() != Constants.INT_ZERO) {
            syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_TWO, "产品谱系不存在：" + pedigreeDto.getCode()));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 同步产品谱系
     *
     * @param pedigreeDto            同步产品谱系
     * @param pedigreeOptional       当前产品谱系
     * @param parentPedigreeOptional 父级产品谱系
     * @param materialDto            物料
     * @param syncResultDtoList      返回同步信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/30
     */
    public void updatePedigreeSync(SyncPedigreeDTO pedigreeDto, Optional<Pedigree> pedigreeOptional, Optional<Pedigree> parentPedigreeOptional, MaterialDTO materialDto, List<SyncResultDTO> syncResultDtoList) {
        //新增修改产品谱系
        if (pedigreeDto.getOperate() == Constants.INT_ZERO || pedigreeDto.getOperate() == Constants.INT_ONE) {
            Pedigree pedigree = pedigreeDto.getOperate() == Constants.INT_ZERO ? new Pedigree() : pedigreeOptional.get();
            pedigree.setName(pedigreeDto.getName())
                    .setCode(pedigreeDto.getCode())
                    .setIsEnable(Constants.TRUE)
                    .setSpecification(pedigreeDto.getSpecification())
                    .setType(Constants.INT_ZERO);
            if (!ObjectUtils.isEmpty(parentPedigreeOptional) && parentPedigreeOptional.isPresent()) {
                pedigree.setParent(parentPedigreeOptional.get()).setType(Constants.INT_ONE);
            }
            if (!ObjectUtils.isEmpty(materialDto)) {
                // 获取最低级型号
                pedigree.setMaterialId(materialDto.getId())
                        .setType(commonService.getPedigreeMinLevel());
            }
            pedigreeRepository.save(pedigree);
        }
        //删除产品谱系
        if (pedigreeDto.getOperate() == Constants.INT_TWO) {
            Pedigree pedigree = pedigreeOptional.get();
            pedigree.setDeleted(pedigree.getId());
            pedigreeRepository.save(pedigree);
        }
        //添同步成功标记
        syncResultDtoList.add(new SyncResultDTO(pedigreeDto.getId(), Constants.INT_ONE, ""));
    }

    /**
     * 产品谱系导入
     *
     * @param file excel文件
     */
    @Override
    public void importPedigreeExcel(MultipartFile file) {
        //查找数据字典配置的产品谱系层级信息
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_PEDIGREE_LEVEL,Constants.LONG_ZERO).orElse(null);
        String config = dictionaryDTO.getData();
        // 解析产品谱系类型配置
        List<PedigreeTypeDTO> pedigreeTypes = parsePedigreeConfig(config);
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        // 对每一行数据进行处理
        rowList.forEach(i -> {
            processRow(i, pedigreeTypes);
        });
    }

    /**
     * 解析产品谱系配置
     *
     * @param config 产品谱系配置
     * @return java.util.List<net.airuima.web.rest.base.pedigree.dto.PedigreeTypeDTO> 品谱系配置集合
     */
    @Override
    public List<PedigreeTypeDTO> parsePedigreeConfig(String config) {
        return JSON.parseObject(config, new TypeReference<List<PedigreeTypeDTO>>() {
        });
    }


    /**
     * 处理Excel每一行的数据
     *
     * @param row 行数据
     */
    private void processRow(Map<String, Object> row, List<PedigreeTypeDTO> pedigreeTypes) {
        //excel当前数据行号
        Integer rowNumber = (Integer) row.get("excelRowNum");
        String rowNumberText = rowNumber != null ? String.valueOf(rowNumber) : "末尾";
        // 获取产品谱系
        Pedigree pedigree = getPedigreeFromRow(row, rowNumberText, pedigreeTypes);
        // 校验产品谱系
        validatePedigree(pedigree, rowNumberText, pedigreeTypes);
        pedigreeRepository.save(pedigree);
    }


    /**
     * 解析Excel数据为产品谱系
     *
     * @param row           行数据
     * @param rowNumberText 行号
     * @param pedigreeTypes 产品谱系配置集合
     * @return net.airuima.domain.base.pedigree.Pedigree 产品谱系
     */
    private Pedigree getPedigreeFromRow(Map<String, Object> row, String rowNumberText, List<PedigreeTypeDTO> pedigreeTypes) {
        // 获取产品谱系编码并转换为对象
        String pedigreeCode = String.valueOf(row.get(PedigreeConstants.PEDIGREE_CODE));
        if (StringUtils.isEmpty(pedigreeCode) || "null".equals(pedigreeCode)) {
            throw new ResponseException("error.PedigreeCodeEmptyError", "导入Excel失败, 第" + rowNumberText + "行数据有误, 产品谱系编码不能为空");
        }
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO);
        Pedigree pedigree = pedigreeOptional.orElse(new Pedigree());
        // 当前类型
        String currentPedigreeType = String.valueOf(row.get(PedigreeConstants.PEDIGREE_TYPE));
        // 获取产品谱系名称
        String pedigreeName = String.valueOf(row.get(PedigreeConstants.PEDIGREE_NAME));
        // 获取产品规格型号
        String specification = String.valueOf(row.get(PedigreeConstants.SPECIFICATION));
        specification = "null".equals(specification) ? null : specification;
        // 获取是否启用
        String isEnableText = String.valueOf(row.get(PedigreeConstants.IS_ENABLE));
        Boolean isEnable = "是".equals(isEnableText) ? Boolean.TRUE : Boolean.FALSE;
        // 获取父级谱系编码
        String parentPedigreeCode = String.valueOf(row.get(PedigreeConstants.PARENT_PEDIGREE_CODE));
        // 获取关联物料编码
        String materialCode = String.valueOf(row.get(PedigreeConstants.MATERIAL_CODE));
        MaterialDTO materialDto = null;
        if (!StringUtils.isEmpty(materialCode) && !"null".equals(materialCode)) {
            materialDto = getMaterialInfo(rowNumberText, materialCode);
        }
        // 父级对象
        Pedigree parentPedigree = null;
        if (!StringUtils.isEmpty(parentPedigreeCode) && !"null".equals(parentPedigreeCode)) {
            parentPedigree = pedigreeRepository.findByCodeAndDeleted(parentPedigreeCode, Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("error.PedigreeErrorParentPedigreeNotFoundError","原因父级谱系不存在"));
        }

        // 已存在 直接覆盖
        if (pedigreeOptional.isPresent()) {
            // 不能修改已存在产品谱系的层级
            PedigreeTypeDTO pedigreeTypeEnum = findPedigreeTypeByKey(pedigreeOptional.get().getType(), pedigreeTypes, rowNumberText);
            if (!pedigreeTypeEnum.getValue().equals(currentPedigreeType)) {
                throw new ResponseException("error.PedigreeNotAllowUpdateTypeError", "导入Excel失败, 第" + rowNumberText + "行数据有误, 不能修改已存在产品谱系的层级");
            }
        } else {
            pedigree.setType(findPedigreeTypeByName(currentPedigreeType, pedigreeTypes, rowNumberText).getKey());
        }
        // 设置产品谱系相关属性
        pedigree.setName(pedigreeName)
                .setCode(pedigreeCode)
                .setParent(parentPedigree)
                .setIsEnable(isEnable)
                .setSpecification(specification)
                .setMaterialId(Optional.ofNullable(materialDto).map(MaterialDTO::getId).orElse(null));
        return pedigree;
    }

    /**
     * 根据产品谱系类型名称查找产品谱系类型
     *
     * @param name          产品谱系类型名称
     * @param pedigreeTypes 产品谱系类型配置集合
     * @param rowNumberText 行号
     * @return net.airuima.web.rest.base.pedigree.dto.PedigreeTypeDTO 产品谱系类型
     */
    private PedigreeTypeDTO findPedigreeTypeByName(String name, List<PedigreeTypeDTO> pedigreeTypes, String rowNumberText) {
        return pedigreeTypes.stream()
                .filter(type -> type.getValue().equals(name))
                .findFirst()
                .orElseThrow(() -> new ResponseException("error.PedigreeTypeErrorNotFound", "产品谱系类型不存在"));
    }

    /**
     * 根据产品谱系key查找产品谱系类型
     *
     * @param key           产品谱系类型
     * @param pedigreeTypes 产品谱系类型配置集合
     * @param rowNumberText 行号
     * @return net.airuima.web.rest.base.pedigree.dto.PedigreeTypeDTO 产品谱系类型
     */
    private PedigreeTypeDTO findPedigreeTypeByKey(int key, List<PedigreeTypeDTO> pedigreeTypes, String rowNumberText) {
        return pedigreeTypes.stream()
                .filter(type -> type.getKey() == key)
                .findFirst()
                .orElseThrow(() -> new ResponseException("error.PedigreeTypeErrorNotFound","产品谱系类型不存在"));
    }

    /**
     * 获取物料信息
     *
     * @param rowNumberText 行号
     * @param materialCode  物料编码
     * @return net.airuima.dto.bom.MaterialDTO 物料信息
     */
    private MaterialDTO getMaterialInfo(String rowNumberText, String materialCode) {
        MaterialDTO materialDto;
        materialDto = rbaseMaterialProxy.findByCodeAndDeleted(materialCode,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(materialDto)) {
            throw new ResponseException("error.PedigreeErrorMaterialNotFoundError", "导入Excel失败, 第" + rowNumberText + "行数据有误, 原因关联物料不存在");
        }
        return materialDto;
    }

    /**
     * 校验产品谱系
     *
     * @param pedigree      产品谱系
     * @param rowNumber     行号
     * @param pedigreeTypes 产品谱系类型配置
     */
    @Override
    public void validatePedigree(Pedigree pedigree, String rowNumber, List<PedigreeTypeDTO> pedigreeTypes) {
        PedigreeTypeDTO pedigreeType = findPedigreeTypeByKey(pedigree.getType(), pedigreeTypes, rowNumber);
        // 获取最大和最小的key值
        int maxKey = pedigreeTypes.stream().mapToInt(PedigreeTypeDTO::getKey).max().orElse(Integer.MIN_VALUE);
        int minKey = pedigreeTypes.stream().mapToInt(PedigreeTypeDTO::getKey).min().orElse(Integer.MAX_VALUE);

        // 如果是产品层级
        if (pedigreeType.getKey() == maxKey) {
            // 需要有父级，有规格型号，有关联物料
            if (pedigree.getParent() == null) {
                throw new ResponseException("error.PedigreeParentError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，父级谱系不能为空");
            }
            if (pedigree.getMaterialId() == null) {
                throw new ResponseException("error.PedigreeMaterialError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，关联物料不能为空");
            }
        } else if (pedigreeType.getKey() == minKey) {
            // key值最小的产品谱系类型，无父级，无规格型号，无关联物料
            if (pedigree.getParent() != null) {
                throw new ResponseException("error.PedigreeParentNotEmptyError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，父级谱系必须为空");
            }
            if (pedigree.getMaterialId() != null) {
                throw new ResponseException("error.PedigreeMaterialNotEmptyError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，关联物料必须为空");
            }
        } else {
            // key值不是最大也不是最小的产品谱系类型，有父级，无规格型号，无关联物料
            if (pedigree.getParent() == null) {
                throw new ResponseException("error.PedigreeParentError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，父级谱系不能为空");
            }
            if (pedigree.getMaterialId() != null) {
                throw new ResponseException("error.PedigreeMaterialNotEmptyError", "导入Excel失败, 第" + rowNumber + "行数据有误, 产品谱系校验不通过，关联物料必须为空");
            }
        }

    }

    /**
     * 通过产品谱系编码和是否启用查询对应产品谱系
     *
     * @param code     产品谱系编码
     * @param isEnable 是否启用
     * @return net.airuima.domain.base.pedigree.Pedigree 产品谱系
     */
    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Pedigree findByCode(String code, Boolean isEnable) {
        Pedigree pedigree = pedigreeRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO).orElse(null);
        if (Objects.nonNull(pedigree) && Objects.nonNull(isEnable)) {
            return pedigree.getIsEnable() == isEnable ? pedigree : null;
        }
        return pedigree;
    }

    /**
     * 克隆产品谱系模型
     * @param pedigreeModelCloneDto 产品谱系模型克隆参数
     * @return net.airuima.dto.base.BaseResultDTO<net.airuima.domain.base.pedigree.Pedigree> 产品谱系
     */
    @Override
    public BaseResultDTO<Pedigree> pedigreeModelClone(PedigreeModelCloneDTO pedigreeModelCloneDto) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(pedigreeModelCloneDto.getCode(), Constants.LONG_ZERO);
        if (pedigreeOptional.isPresent()) {
            return new BaseResultDTO(Constants.KO, "产品谱系编码已存在", Pedigree.class.getSimpleName(), "error.pedigreeCodeExists");
        }
        // 待克隆的产品谱系id
        Long clonePedigreeId = pedigreeModelCloneDto.getClonePedigreeId();
        MaterialDTO materialDto = rbaseMaterialProxy.findByIdAndDeleted(pedigreeModelCloneDto.getMaterialId(),Constants.LONG_ZERO);
        if (Objects.isNull(materialDto) || Objects.isNull(materialDto.getId())) {
            return new BaseResultDTO(Constants.KO, "物料不存在", Pedigree.class.getSimpleName(), "error.cloneMaterialNotFound");
        }
        // 克隆产生的产品谱系
        Pedigree pedigree = new Pedigree();
        BeanUtils.copyProperties(pedigreeModelCloneDto, pedigree);
        pedigreeRepository.save(pedigree);
        pedigree.setMaterialDto(materialDto);
        Pedigree originPedigree = pedigreeRepository.getReferenceById(clonePedigreeId);
        //克隆产品谱系物料清单
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.BOMINFO_CREATE) && null != originPedigree.getMaterialId() && !originPedigree.getMaterialId().equals(pedigreeModelCloneDto.getMaterialId())) {
            rbaseBomProxy.clone(originPedigree.getMaterialId(), pedigreeModelCloneDto.getMaterialId());
        }
        //克隆产品谱系正常工艺路线
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREEWORKFLOW_CREATE)) {
            List<PedigreeWorkFlow> pedigreeWorkFlowList = pedigreeWorkFlowRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeWorkFlowList)) {
                List<PedigreeWorkFlow> clonePedigreeWorkFlowList = pedigreeWorkFlowList.stream()
                        .map(i -> {
                            PedigreeWorkFlow pedigreeWorkFlow = new PedigreeWorkFlow();
                            BeanUtils.copyProperties(i, pedigreeWorkFlow);
                            pedigreeWorkFlow.setPedigree(pedigree).setId(null);
                            pedigreeWorkFlow.setDeleted(Constants.LONG_ZERO);
                            return pedigreeWorkFlow;
                        }).toList();
                pedigreeWorkFlowRepository.saveAll(clonePedigreeWorkFlowList);
            }
        }
        //克隆产品谱系返工工艺路线
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREEREWORKWORKFLOW_CREATE)) {
            List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository
                    .findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeReworkWorkFlowList)) {
                List<PedigreeReworkWorkFlow> clonePedigreeReworkWorkFlowList = pedigreeReworkWorkFlowList.stream().map(i -> {
                    PedigreeReworkWorkFlow pedigreeReworkWorkFlow = new PedigreeReworkWorkFlow();
                    BeanUtils.copyProperties(i, pedigreeReworkWorkFlow);
                    pedigreeReworkWorkFlow.setPedigree(pedigree).setId(null);
                    pedigreeReworkWorkFlow.setDeleted(Constants.LONG_ZERO);
                    return pedigreeReworkWorkFlow;
                }).toList();
                pedigreeReworkWorkFlowRepository.saveAll(clonePedigreeReworkWorkFlowList);
            }
        }
        // 克隆工序配置
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEP_CREATE)) {
            List<PedigreeStep> pedigreeStepList = pedigreeStepRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepList)) {
                List<PedigreeStep> clonePedigreeStepList = pedigreeStepList.stream().map(i -> {
                    PedigreeStep pedigreeStep = new PedigreeStep();
                    BeanUtils.copyProperties(i, pedigreeStep);
                    pedigreeStep.setPedigree(pedigree).setId(null);
                    pedigreeStep.setDeleted(Constants.LONG_ZERO);
                    return pedigreeStep;
                }).toList();
                pedigreeStepRepository.saveAll(clonePedigreeStepList);
            }
        }
        //克隆工序指标
        if (FuncKeyUtil.checkApi(FuncKeyConstants.ESOP) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEPSPECIFICATION_CREATE)) {
            List<PedigreeStepSpecification> pedigreeStepSpecificationList = pedigreeStepSpecificationRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepSpecificationList)) {
                pedigreeStepSpecificationList.forEach(i -> {
                    PedigreeStepSpecification pedigreeStepSpecification = new PedigreeStepSpecification();
                    BeanUtils.copyProperties(i, pedigreeStepSpecification);
                    pedigreeStepSpecification.setPedigree(pedigree).setId(null);
                    pedigreeStepSpecification = pedigreeStepSpecificationRepository.save(pedigreeStepSpecification);
                    List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(i.getId());
                    if (!CollectionUtils.isEmpty(documentDTOList)) {
                        DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO().setServiceName(StringUtils.upperCase("rmes")).setRecordId(pedigreeStepSpecification.getId()).setDocumentList(documentDTOList.stream().map(DocumentRelationDTO.Document::new).toList());
                        rbaseDocumentProxy.relation(documentRelationDTO);
                    }
                });
            }
        }
        //克隆工序上料规则
        if (FuncKeyUtil.checkApi(FuncKeyConstants.PEDIGREE_STEP_MATERIAL_RULE_KEY) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEPMATERIALRULE_CREATE)) {
            List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = pedigreeStepMaterialRuleRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepMaterialRuleList)) {
                List<PedigreeStepMaterialRule> clonePedigreeStepMaterialRuleList = pedigreeStepMaterialRuleList.stream().map(i -> {
                    PedigreeStepMaterialRule pedigreeStepMaterialRule = new PedigreeStepMaterialRule();
                    BeanUtils.copyProperties(i, pedigreeStepMaterialRule);
                    pedigreeStepMaterialRule.setPedigree(pedigree).setId(null);
                    pedigreeStepMaterialRule.setDeleted(Constants.LONG_ZERO);
                    return pedigreeStepMaterialRule;
                }).toList();
                pedigreeStepMaterialRuleRepository.saveAll(clonePedigreeStepMaterialRuleList);
            }
        }
        // 克隆工序间隔配置
        if (FuncKeyUtil.checkApi(FuncKeyConstants.STEP_INTERVAL) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEPINTERVALCONFIG_CREATE)) {
            List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList = pedigreeStepIntervalConfigRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepIntervalConfigList)) {
                List<PedigreeStepIntervalConfig> clonePedigreeStepIntervalConfigList = pedigreeStepIntervalConfigList.stream().map(i -> {
                    PedigreeStepIntervalConfig pedigreeStepIntervalConfig = new PedigreeStepIntervalConfig();
                    BeanUtils.copyProperties(i, pedigreeStepIntervalConfig);
                    pedigreeStepIntervalConfig.setPedigree(pedigree).setId(null);
                    pedigreeStepIntervalConfig.setDeleted(Constants.LONG_ZERO);
                    return pedigreeStepIntervalConfig;
                }).toList();
                pedigreeStepIntervalConfigRepository.saveAll(clonePedigreeStepIntervalConfigList);
            }
        }
        // 克隆工序不良项目
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEPUNQUALIFIEDITEM_CREATE)) {
            List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemRepository
                    .findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemList)) {
                List<PedigreeStepUnqualifiedItem> clonePedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemList.stream().map(i -> {
                    PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = new PedigreeStepUnqualifiedItem();
                    BeanUtils.copyProperties(i, pedigreeStepUnqualifiedItem);
                    pedigreeStepUnqualifiedItem.setPedigree(pedigree).setId(null);
                    pedigreeStepUnqualifiedItem.setDeleted(Constants.LONG_ZERO);
                    return pedigreeStepUnqualifiedItem;
                }).toList();
                pedigreeStepUnqualifiedItemRepository.saveAll(clonePedigreeStepUnqualifiedItemList);
            }
        }
        //克隆工序目标良率配置
        if (FuncKeyUtil.checkApi(FuncKeyConstants.PEDIGREE_OLINE_QUALITY_REPORT) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESTEPPASSRATE_CREATE)) {
            List<PedigreeStepPassRate> pedigreeStepPassRateList = pedigreeStepPassRateRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepPassRateList)) {
                List<PedigreeStepPassRate> clonePedigreeStepPassRateList = pedigreeStepPassRateList.stream().map(i -> {
                    PedigreeStepPassRate pedigreeStepPassRate = new PedigreeStepPassRate();
                    BeanUtils.copyProperties(i, pedigreeStepPassRate);
                    pedigreeStepPassRate.setPedigree(pedigree).setId(null);
                    pedigreeStepPassRate.setDeleted(Constants.LONG_ZERO);
                    return pedigreeStepPassRate;
                }).toList();
                pedigreeStepPassRateRepository.saveAll(clonePedigreeStepPassRateList);
            }
        }
        // 克隆生产配置
        if (SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREECONFIG_CREATE)) {
            PedigreeConfig pedigreeConfig = pedigreeConfigRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(pedigreeConfig)) {
                PedigreeConfig clonePedigreeConfig = new PedigreeConfig();
                BeanUtils.copyProperties(pedigreeConfig, clonePedigreeConfig);
                clonePedigreeConfig.setPedigree(pedigree).setId(null);
                clonePedigreeConfig.setDeleted(Constants.LONG_ZERO);
                pedigreeConfigRepository.save(clonePedigreeConfig);
            }
        }
        // 克隆单支复用配置
        if (FuncKeyUtil.checkApi(FuncKeyConstants.SN_REUSE) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.PEDIGREESNREUSECONFIG_CREATE)) {
            List<PedigreeSnReuseConfig> pedigreeSnReuseConfigList = pedigreeSnReuseConfigRepository.findByPedigreeIdAndDeleted(clonePedigreeId, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeSnReuseConfigList)) {
                List<PedigreeSnReuseConfig> clonePedigreeSnReuseConfigList = pedigreeSnReuseConfigList.stream().map(i -> {
                    PedigreeSnReuseConfig pedigreeSnReuseConfig = new PedigreeSnReuseConfig();
                    BeanUtils.copyProperties(i, pedigreeSnReuseConfig);
                    pedigreeSnReuseConfig.setPedigree(pedigree).setId(null);
                    pedigreeSnReuseConfig.setDeleted(Constants.LONG_ZERO);
                    return pedigreeSnReuseConfig;
                }).toList();
                pedigreeSnReuseConfigRepository.saveAll(clonePedigreeSnReuseConfigList);
            }
        }
        // 克隆动态数据
        if (FuncKeyUtil.checkApi(FuncKeyConstants.STEP_DYNAMIC_DATA) && SecurityUtils.isCurrentUserInRole(AuthorityConstants.STEPDYNAMICDATA_CREATE)) {
            rbaseStepDynamicDataProxy.cloneStepDynamicData(clonePedigreeId, pedigree);
        }
        return new BaseResultDTO(Constants.OK, pedigree);
    }

}
