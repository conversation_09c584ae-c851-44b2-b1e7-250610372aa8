package net.airuima.rbase.service.procedure.material.impl;

import net.airuima.constant.Constants;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterial;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.dto.material.CheckMaterialDetailDTO;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.service.procedure.material.IWsCheckMaterialDetailService;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WsCheckMaterialDetailServiceImpl implements IWsCheckMaterialDetailService {

    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;

    @Autowired
    private PIWipLedgerService wipLedgerServices;
    @Autowired
    private WsCheckMaterialRepository wsCheckMaterialRepository;

    /**
     * 保存工单核料数量信息
     *
     * @param checkMaterialDetailDtoList 核料数量
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCheckMaterialDetail(List<CheckMaterialDetailDTO> checkMaterialDetailDtoList) {
        Map<Long, WsCheckMaterial> wsCheckMaterialGroup = new HashMap<>();
        checkMaterialDetailDtoList.forEach(checkMaterialDetailDto -> wsCheckMaterialDetailRepository.findByIdAndDeleted(checkMaterialDetailDto.getId(), Constants.LONG_ZERO).ifPresent(wsCheckMaterialDetail -> {
            if (!wsCheckMaterialGroup.containsKey(wsCheckMaterialDetail.getWsCheckMaterial().getId())) {
                wsCheckMaterialGroup.put(wsCheckMaterialDetail.getWsCheckMaterial().getId(), wsCheckMaterialDetail.getWsCheckMaterial());
            }
            //更新核料数量和未核料数量
            wsCheckMaterialDetail.setCheckedNumber(NumberUtils.add(wsCheckMaterialDetail.getCheckedNumber(), checkMaterialDetailDto.getNum()).doubleValue())
                    .setUncheckNumber(NumberUtils.subtract(wsCheckMaterialDetail.getUncheckNumber(), checkMaterialDetailDto.getNum()).doubleValue());
            if(wsCheckMaterialDetail.getUncheckNumber()<Constants.INT_ZERO){
                throw new ResponseException("error.unCheckedNumberError", "未核料数量异常,请勿重复提交");
            }
            wsCheckMaterialDetailRepository.save(wsCheckMaterialDetail);
            //更新领料数量
            WsMaterialBatch wsMaterialBatch = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(
                    wsCheckMaterialDetail.getWorkSheet().getId(), wsCheckMaterialDetail.getMaterialId(), wsCheckMaterialDetail.getBatch(), Constants.LONG_ZERO).orElseGet(WsMaterialBatch::new);
            if (wsMaterialBatch.getId() == null) {
                wsMaterialBatch.setWorkSheet(wsCheckMaterialDetail.getWorkSheet())
                        .setMaterialId(wsCheckMaterialDetail.getMaterialId())
                        .setBatch(wsCheckMaterialDetail.getBatch())
                        .setSupplierId(wsCheckMaterialDetail.getSupplierId());
            }
            // 处理线边仓工单库存、新增台账
            wipLedgerServices.processWarehouseAndSaveLedger(wsCheckMaterialDetail.getWorkSheet().getWorkLine(),wsMaterialBatch,checkMaterialDetailDto.getNum(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_STORE.getCategory(),null);
            wsMaterialBatch.setNumber(NumberUtils.add(wsMaterialBatch.getNumber(), checkMaterialDetailDto.getNum()).doubleValue())
                    .setLeftNumber(NumberUtils.add(wsMaterialBatch.getLeftNumber(), checkMaterialDetailDto.getNum()).doubleValue());
            wsMaterialBatchRepository.save(wsMaterialBatch);
        }));
        //更新核料状态数据
        if(ValidateUtils.isValid(wsCheckMaterialGroup)) {
            wsCheckMaterialGroup.forEach((wsCheckMaterialId, wsCheckMaterial) -> {
                List<WsCheckMaterialDetail> checkMaterialDetailList = wsCheckMaterialDetailRepository.findByWsCheckMaterialIdAndDeleted(wsCheckMaterialId, Constants.LONG_ZERO);
                if (ValidateUtils.isValid(checkMaterialDetailList)) {
                    double sumCheckedNumber = checkMaterialDetailList.stream().mapToDouble(WsCheckMaterialDetail::getCheckedNumber).sum();
                    double sumUncheckNumber = checkMaterialDetailList.stream().mapToDouble(WsCheckMaterialDetail::getUncheckNumber).sum();
                    //已经核过物料
                    if (sumCheckedNumber > 0) {
                        wsCheckMaterial.setCheckDate(LocalDateTime.now());
                        //部分核料
                        if (sumUncheckNumber > 0) {
                            wsCheckMaterial.setStatus(Constants.INT_ONE);
                        } else {//完全核料
                            wsCheckMaterial.setStatus(Constants.INT_TWO);
                        }
                        wsCheckMaterialRepository.save(wsCheckMaterial);
                    }
                }
            });
        }
    }
}
