package net.airuima.rbase.service.report.api;

import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.web.rest.report.dto.ReverseTraceReportRequestDTO;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.Map;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface ITraceReportService {


    /**
     * 组装生产过程正向追溯导出DTO集合
     *
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号）
     * @param serialNumber 单据号
     *
     **/
    default void workSheetTraceReportExport(Integer category, String serialNumber, String excelType, HttpServletResponse response){

    }


    /**
     * 生成过程正向追溯（工序详情）-导出
     *
     * @param wsId ID
     * @param category     单据类型（0:订单号，1:工单号，2:子工单号，3：容器号，4：SN）
     * @param serialNumber 单据号
     **/
    default void exportStepDetail(Long wsId, int category, String serialNumber,String excelType, HttpServletResponse response) {

    }


    /**
     * 反向追溯详情 物料-设备-sn 导出
     *
     * @param reverseTraceReportRequestDto 反向追溯请求详情
     */
    default void exportReverseTraceReport(ReverseTraceReportRequestDTO reverseTraceReportRequestDto,HttpServletResponse response){

    }
}
