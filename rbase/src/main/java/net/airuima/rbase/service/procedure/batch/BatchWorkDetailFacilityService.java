package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailFacilityRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情设备Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BatchWorkDetailFacilityService extends CommonJpaService<BatchWorkDetailFacility> {
    private static final String BATCH_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH = "batchWorkDetailEquipmentEntityGraph";
    private final BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;

    public BatchWorkDetailFacilityService(BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository) {
        this.batchWorkDetailFacilityRepository = batchWorkDetailFacilityRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    
    public Page<BatchWorkDetailFacility> find(Specification<BatchWorkDetailFacility> spec, Pageable pageable) {
        return batchWorkDetailFacilityRepository.findAll(spec, pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<BatchWorkDetailFacility> find(Specification<BatchWorkDetailFacility> spec) {
        return batchWorkDetailFacilityRepository.findAll(spec,new NamedEntityGraph(BATCH_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<BatchWorkDetailFacility> findAll(Pageable pageable) {
        return batchWorkDetailFacilityRepository.findAll(pageable,new NamedEntityGraph(BATCH_WORK_DETAIL_EQUIPMENT_ENTITY_GRAPH));
    }

}
