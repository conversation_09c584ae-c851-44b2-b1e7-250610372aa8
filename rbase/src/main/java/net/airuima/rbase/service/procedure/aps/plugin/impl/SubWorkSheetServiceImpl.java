package net.airuima.rbase.service.procedure.aps.plugin.impl;

import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.plugin.ISubWorkSheetService;
import net.airuima.util.BeanUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SubWorkSheetServiceImpl implements ISubWorkSheetService {

    /**
     * 子工单中途结单
     *
     * @param subWorkSheetDto 结单信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     * <AUTHOR>
     * @date 2023/09/12
     */
    @Override
    public BaseDTO forceFinishSubWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        SubWorkSheetService subWorkSheetService = BeanUtil.getBean(SubWorkSheetService.class);
        return subWorkSheetService.forceFinishSubWorkSheet(subWorkSheetDto);
    }

    /**
     * 工单中途结单
     *
     * @param subWorkSheetDto 结单信息
     * @return net.airuima.rbase.dto.base.BaseDTO 结果信息
     * <AUTHOR>
     * @date 2023/09/12
     */
    @Override
    public BaseDTO forceFinishWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        SubWorkSheetService subWorkSheetService = BeanUtil.getBean(SubWorkSheetService.class);
        return subWorkSheetService.forceFinishWorkSheet(subWorkSheetDto);
    }
}
