package net.airuima.rbase.service.procedure.batch;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
@FuncDefault
public interface WsStepWorkCellModeService {

    /**
     * 验证当前（子）工单工位，是否符合工单工序指派的工位
     * @param subWorkSheet 子工单
     * @param step 工序
     * @param workCell  工位
     * <AUTHOR>
     * @date  2022/8/11
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor("SpecificStepCell")
    default BaseClientDTO validWsStepWorkCell(SubWorkSheet subWorkSheet, Step step, WorkCell workCell){
        return new BaseClientDTO(Constants.OK);
    }

}
