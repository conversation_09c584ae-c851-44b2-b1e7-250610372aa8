package net.airuima.rbase.service.base.process;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WorkFlowConvertConfigExcelConstants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowConvertConfig;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowConvertConfigRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowConvertConfigBatchDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线配置Service
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFlowConvertConfigService extends CommonJpaService<WorkFlowConvertConfig> {

    private final String WORK_FLOW_CONVERT_CONFIG_GRAPH = "convertWorkflowEntityGraph";
    private final Logger log = LoggerFactory.getLogger(WorkFlowConvertConfigService.class);

    private final WorkFlowConvertConfigRepository workFlowConvertConfigRepository;

    private final PriorityElementConfigRepository priorityElementConfigRepository;

    private final PedigreeRepository pedigreeRepository;

    private final WorkFlowRepository workFlowRepository;

    private final StepRepository stepRepository;

    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    public WorkFlowConvertConfigService(WorkFlowConvertConfigRepository workFlowConvertConfigRepository, PriorityElementConfigRepository priorityElementConfigRepository,
                                        PedigreeRepository pedigreeRepository, WorkFlowRepository workFlowRepository, StepRepository stepRepository) {
        this.workFlowConvertConfigRepository = workFlowConvertConfigRepository;
        this.priorityElementConfigRepository = priorityElementConfigRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkFlowConvertConfig> find(Specification<WorkFlowConvertConfig> spec, Pageable pageable) {
        return workFlowConvertConfigRepository.findAll(spec, pageable,new NamedEntityGraph(WORK_FLOW_CONVERT_CONFIG_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WorkFlowConvertConfig> find(Specification<WorkFlowConvertConfig> spec) {
        return workFlowConvertConfigRepository.findAll(spec,new NamedEntityGraph(WORK_FLOW_CONVERT_CONFIG_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkFlowConvertConfig> findAll(Pageable pageable) {
        return workFlowConvertConfigRepository.findAll(pageable,new NamedEntityGraph(WORK_FLOW_CONVERT_CONFIG_GRAPH));
    }

    /**
     * 保存转工艺路线配置
     *
     * @param entity 转工艺路线配置
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    public BaseResultDTO saveInstance(WorkFlowConvertConfig entity) {
        Long id = entity.getId();
        if (Objects.isNull(entity.getPriorityElementConfig())) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", WorkFlowConvertConfig.class.getSimpleName(), "error.noMatchCombination");
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(entity.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (!priorityElementConfigOptional.isPresent()) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", WorkFlowConvertConfig.class.getSimpleName(), "error.noMatchCombination");
        }
        BaseResultDTO baseResultDTO = checkWorkFlowConvertConfig(entity);
        if (Objects.nonNull(baseResultDTO)) {
            return baseResultDTO;
        }
        entity.setPriorityElementConfig(priorityElementConfigOptional.get());
        entity.setDeleted(Constants.LONG_ZERO);
        workFlowConvertConfigRepository.save(entity);
        return new BaseResultDTO(Constants.OK, entity);
    }

    /**
     * 校验转工艺路线配置已经存在
     *
     * @param workFlowConvertConfig 转工艺路线配置
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    private BaseResultDTO checkWorkFlowConvertConfig(WorkFlowConvertConfig workFlowConvertConfig) {
        //客户id
        Long clientId = null != workFlowConvertConfig.getClientId() ? workFlowConvertConfig.getClientId() : null;
        // 产品谱系
        Long pedigreeId = null != workFlowConvertConfig.getPedigree() ? workFlowConvertConfig.getPedigree().getId() : null;
        // 旧的工艺路线
        Long originWorkFlowId = null != workFlowConvertConfig.getOriginWorkFlow() ? workFlowConvertConfig.getOriginWorkFlow().getId() : null;
        // 工序id
        Long stepId = null != workFlowConvertConfig.getStep() ? workFlowConvertConfig.getStep().getId() : null;

        Long targetWorkFlowId = null != workFlowConvertConfig.getTargetWorkFlow() ? workFlowConvertConfig.getTargetWorkFlow().getId() : null;
        // 获取转工艺路线配置
        WorkFlowConvertConfig queryWorkFlowConvertConfig = workFlowConvertConfigRepository.findByPedigreeIdAndOriginWorkFlowIdAndClientIdAndStepIdAndTargetWorkFlowIdAndDeleted(pedigreeId,originWorkFlowId,clientId,stepId,targetWorkFlowId,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(workFlowConvertConfig.getId()) && Objects.nonNull(queryWorkFlowConvertConfig)) {
            return new BaseResultDTO(Constants.KO, "转工艺路线配置已存在", StringUtils.uncapitalize(WorkFlowConvertConfig.class.getSimpleName()), "error.workFlowConvertConfigExists");
        }
        if(Objects.nonNull(workFlowConvertConfig.getId()) && Objects.nonNull(queryWorkFlowConvertConfig) && !queryWorkFlowConvertConfig.getId().equals(workFlowConvertConfig.getId())){
            return new BaseResultDTO(Constants.KO, "转工艺路线配置已存在", StringUtils.uncapitalize(WorkFlowConvertConfig.class.getSimpleName()), "error.workFlowConvertConfigExists");
        }
        return null;
    }


    /**
     * 工序配置导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object> 行数据
     */
    public List<Map<String, Object>> importWorkFlowConvertConfigExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            // 获取产品谱系编码并转换为对象
            String pedigreeCode = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.PEDIGREE_CODE));
            Pedigree pedigree = null;
            if (StringUtils.hasLength(pedigreeCode) && !"null".equals(pedigreeCode)) {
                pedigree = pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO).orElse(null);
                if (pedigree == null) {
                    row.put("错误信息", "导入Excel失败, 数据有误, 原因产品谱系不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取原始工艺路线编码并转换为对象
            String originWorkFlowCode = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.ORIGIN_WORK_FLOW_CODE));
            WorkFlow originWorkFlow = null;
            if (StringUtils.hasLength(originWorkFlowCode) && !"null".equals(originWorkFlowCode)) {
                originWorkFlow = workFlowRepository.findByCodeAndDeleted(originWorkFlowCode, Constants.LONG_ZERO).orElse(null);
                if (originWorkFlow == null) {
                    row.put("错误信息", "导入Excel失败, 数据有误, 原因原始工艺路线不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取工序编码并转换为对象
            String stepCode = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.STEP_CODE));
            Step step = null;
            if (StringUtils.hasLength(stepCode) && !"null".equals(stepCode)) {
                step = stepRepository.findByCodeAndDeleted(stepCode, Constants.LONG_ZERO).orElse(null);
                if (step == null) {
                    row.put("错误信息", "导入Excel失败, 数据有误, 原因工序不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取客户编码并转换为对象
            String clientCode = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.CLIENT_CODE));
            ClientDTO clientDTO = null;
            if (StringUtils.hasLength(clientCode) && !"null".equals(clientCode)) {
                clientDTO = rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO);
                if (clientDTO == null) {
                    row.put("错误信息", "导入Excel失败, 数据有误, 原因客户不存在");
                    illegalDataList.add(row);
                    return;
                }
            }
            // 获取目标工艺路线编码并转换为对象
            String targetWorkFlowCode = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.TARGET_WORK_FLOW_CODE));
            if (!StringUtils.hasLength(targetWorkFlowCode) || "null".equals(targetWorkFlowCode)) {
                row.put("错误信息", "导入Excel失败, 数据有误, 原因目标工艺路线不存在");
                illegalDataList.add(row);
                return;
            }
            WorkFlow targetWorkFlow = workFlowRepository.findByCodeAndDeleted(targetWorkFlowCode, Constants.LONG_ZERO).orElse(null);
            if (targetWorkFlow == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 原因目标工艺路线不存在");
                illegalDataList.add(row);
                return;
            }
            // 获取是否启用
            Boolean isEnable = String.valueOf(row.get(WorkFlowConvertConfigExcelConstants.IS_ENABLE)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
            // 工序id
            Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 原始工艺路线
            Long originWorkFlowId = Optional.ofNullable(originWorkFlow).map(WorkFlow::getId).orElse(null);
            // 客户id
            Long clientId = Optional.ofNullable(clientDTO).map(ClientDTO::getId).orElse(null);
            // 查询配置是否存在
            WorkFlowConvertConfig existingConfig = workFlowConvertConfigRepository.findByPedigreeIdAndOriginWorkFlowIdAndClientIdAndStepIdAndTargetWorkFlowIdAndDeleted(pedigreeId,originWorkFlowId,clientId,stepId,targetWorkFlow.getId(),Constants.LONG_ZERO).orElse(null);
            // 获取条件优先级
            PriorityElementConfig priorityElementConfig = getWorkFlowConvertConfigPriority(pedigreeId, originWorkFlowId, clientId, stepId);
            if (priorityElementConfig == null) {
                row.put("错误信息", "导入Excel失败, 数据有误, 该组合条件优先级未配置");
                illegalDataList.add(row);
                return;
            }
            // 保存转工艺路线配置
            saveWorkFlowConvertConfig(pedigree, originWorkFlow, targetWorkFlow, step, clientId, isEnable, existingConfig, priorityElementConfig);
        });
        return illegalDataList;
    }

    /**
     * 保存转工艺路线配置
     *
     * @param pedigree                   产品谱系
     * @param originWorkFlow             原始工艺路线
     * @param targetWorkFlow             目标工艺路线
     * @param step                       工序
     * @param clientId                   客户id
     * @param isEnable                   是否启用
     * @param queryWorkFlowConvertConfig 查询转工艺路线配置
     * @param priorityElementConfig      优先级
     */
    private void saveWorkFlowConvertConfig(Pedigree pedigree, WorkFlow originWorkFlow, WorkFlow targetWorkFlow, Step step, Long clientId, Boolean isEnable, WorkFlowConvertConfig queryWorkFlowConvertConfig, PriorityElementConfig priorityElementConfig) {
        // 存在则覆盖 否则更新
        if (Objects.nonNull(queryWorkFlowConvertConfig)) {
            queryWorkFlowConvertConfig.setPedigree(pedigree)
                    .setOriginWorkFlow(originWorkFlow)
                    .setTargetWorkFlow(targetWorkFlow)
                    .setStep(step)
                    .setClientId(clientId)
                    .setPriorityElementConfig(priorityElementConfig)
                    .setEnable(isEnable);
            workFlowConvertConfigRepository.save(queryWorkFlowConvertConfig);
        } else {
            WorkFlowConvertConfig workFlowConvertConfig = new WorkFlowConvertConfig();
            workFlowConvertConfig
                    .setPedigree(pedigree)
                    .setOriginWorkFlow(originWorkFlow)
                    .setTargetWorkFlow(targetWorkFlow)
                    .setStep(step)
                    .setClientId(clientId)
                    .setPriorityElementConfig(priorityElementConfig)
                    .setEnable(isEnable);
            workFlowConvertConfigRepository.save(workFlowConvertConfig);
        }
    }

    /**
     * 获取转工艺路线配置条件优先级配置
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param clientId   客户ID
     * @param stepId     工序ID
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    public PriorityElementConfig getWorkFlowConvertConfigPriority(Long pedigreeId, Long workFlowId, Long clientId, Long stepId) {
        List<Integer> combination = Lists.newArrayList();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(workFlowId)) {
            combination.add(Constants.WORKFLOW_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        if (Objects.nonNull(stepId)) {
            combination.add(Constants.STEP_ELEMENT);
        }
        if (CollectionUtils.isEmpty(combination)) {
            return null;
        }
        // 校验是否已有相同条件
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_SIX,combination);
    }

    /**
     * 查询已配置的转工艺路线
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param clientId   客户id
     * @param stepId     工序id
     * @param keyword    模糊查询字符串
     * @return java.util.Set<net.airuima.rbase.domain.base.process.WorkFlow> 转工艺路线集合
     */
    @Transactional(readOnly = true)
    public List<WorkFlow> findWorkFlow(Long pedigreeId, Long workFlowId, Long clientId, Long stepId, String keyword) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        Pedigree pedigree = pedigreeRepository.getReferenceById(pedigreeId);
        commonService.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<WorkFlowConvertConfig> workFlowConvertConfigList = workFlowConvertConfigRepository.findByPedigreeIdInAndOriginWorkFlowIdAndClientIdAndStepIdAndEnableAndDeleted(pedigreeIdList,workFlowId,clientId,stepId,Boolean.TRUE,Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(workFlowConvertConfigList)) {
            return Lists.newArrayList();
        }
        List<WorkFlowConvertConfig> workFlowConvertConfigListSort = new ArrayList<>();
        //1. 获取优先级排序最高集合
        WorkFlowConvertConfig workFlowConvertConfig = workFlowConvertConfigList.stream().min(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).orElseThrow(() -> new ResponseException("error.workFlowConvertConfigNotExist", "工序转工艺配置不存在"));
        workFlowConvertConfigList = workFlowConvertConfigList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == workFlowConvertConfig.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (workFlowConvertConfigList.size() == Constants.INT_ONE) {
            List<WorkFlow> workFlowList =  workFlowConvertConfigList.stream().map(WorkFlowConvertConfig::getTargetWorkFlow).distinct().toList();
            if(StringUtils.hasLength(keyword)){
                return workFlowList.stream().filter(workFlow -> workFlow.getName().contains(keyword)||workFlow.getCode().contains(keyword)).collect(Collectors.toList());
            }
            return workFlowList;
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<WorkFlowConvertConfig> workFlowConvertConfigListPedigreeNull = workFlowConvertConfigList.stream().filter(i -> Objects.isNull(i.getPedigree())).toList();
        List<WorkFlowConvertConfig> workFlowConvertConfigListPedigreeNotNull = workFlowConvertConfigList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(workFlowConvertConfigListPedigreeNull)) {
            workFlowConvertConfigListSort.addAll(workFlowConvertConfigListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(workFlowConvertConfigListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            workFlowConvertConfigListPedigreeNotNull = workFlowConvertConfigListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            workFlowConvertConfigListSort.addAll(workFlowConvertConfigListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(workFlowConvertConfigListSort) && !StringUtils.hasLength(keyword)) {
            return workFlowConvertConfigListSort.stream().map(WorkFlowConvertConfig::getTargetWorkFlow).distinct().collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(workFlowConvertConfigListSort) && StringUtils.hasLength(keyword)) {
            return workFlowConvertConfigListSort.stream().map(WorkFlowConvertConfig::getTargetWorkFlow).distinct().toList().stream().filter(workFlow -> workFlow.getName().contains(keyword)||workFlow.getCode().contains(keyword)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 根据id查找转工艺路线配置
     *
     * @param id 主键
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 工艺路线配置
     */
    @Transactional(readOnly = true)
    @FetchMethod
    public Optional<WorkFlowConvertConfig> findById(Long id) {
        return workFlowConvertConfigRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
    }

    /**
     * 删除转工艺配置
     *
     * @param id 转工艺配置id
     */
    public void deleteEntity(Long id) {
        workFlowConvertConfigRepository.deleteByWorkFlowConvertConfigIdAndDeleted(id);
    }


    /**
     * 批量新增转工艺配置
     *
     * @param workFlowConvertConfigBatchDto 工艺配置参数
     * @return java.util.List<java.util.List < net.airuima.rbase.domain.base.process.WorkFlowConvertConfig>> 工艺配置集合
     */
    public BaseResultDTO<List<WorkFlowConvertConfig>> batchCreate(WorkFlowConvertConfigBatchDTO workFlowConvertConfigBatchDto) {
        // 获取产品谱系
        Pedigree pedigree = Objects.isNull(workFlowConvertConfigBatchDto.getPedigreeId()) ? null : pedigreeRepository.findByIdAndDeleted(workFlowConvertConfigBatchDto.getPedigreeId(), Constants.LONG_ZERO).orElse(null);
        // 获取原来工艺路线
        WorkFlow originWorkFlow = Objects.isNull(workFlowConvertConfigBatchDto.getOriginWorkFlowId()) ? null : workFlowRepository.findByIdAndDeleted(workFlowConvertConfigBatchDto.getOriginWorkFlowId(), Constants.LONG_ZERO).orElse(null);
        // 获取工序
        Step step = Objects.isNull(workFlowConvertConfigBatchDto.getStepId()) ? null : stepRepository.findByIdAndDeleted(workFlowConvertConfigBatchDto.getStepId(), Constants.LONG_ZERO).orElse(null);
        // 条件优先级
        PriorityElementConfig priorityElementConfig = Objects.isNull(workFlowConvertConfigBatchDto.getPriorityElementConfigId()) ? null : priorityElementConfigRepository
                .findByIdAndDeleted(workFlowConvertConfigBatchDto.getPriorityElementConfigId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(priorityElementConfig)) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", WorkFlowConvertConfig.class.getSimpleName(), "NoMatchCombination");
        }
        // 客户
        Long clientId = workFlowConvertConfigBatchDto.getClientId();
        // 是否启用
        Boolean enable = workFlowConvertConfigBatchDto.getEnable();
        // 目标工艺路线
        List<WorkFlow> targetWorkFlowList = workFlowConvertConfigBatchDto.getTargetWorkFlowId().stream()
                .map(i -> workFlowRepository.findByIdAndDeleted(i, Constants.LONG_ZERO).orElse(null)).filter(Objects::nonNull).collect(Collectors.toList());
        List<WorkFlowConvertConfig> saveWorkFlowConvertConfigList = Lists.newArrayList();
        for (int i = 0; i < targetWorkFlowList.size(); i++) {
            // 设置转工艺配置
            WorkFlowConvertConfig workFlowConvertConfig = new WorkFlowConvertConfig();
            workFlowConvertConfig.setPedigree(pedigree).setOriginWorkFlow(originWorkFlow)
                    .setStep(step).setTargetWorkFlow(targetWorkFlowList.get(i)).setClientId(clientId).setEnable(enable)
                    .setPriorityElementConfig(priorityElementConfig).setDeleted(Constants.LONG_ZERO);
            // 校验转工艺配置
            BaseResultDTO baseResultDTO = checkWorkFlowConvertConfig(workFlowConvertConfig);
            if (Objects.nonNull(baseResultDTO)) {
                return baseResultDTO;
            } else {
                saveWorkFlowConvertConfigList.add(workFlowConvertConfig);
            }
        }
        // 保存转工艺配置
        List<WorkFlowConvertConfig> resultList = workFlowConvertConfigRepository.saveAll(saveWorkFlowConvertConfigList);
        return new BaseResultDTO(Constants.OK, resultList);
    }
}


