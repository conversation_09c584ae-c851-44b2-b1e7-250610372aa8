package net.airuima.rbase.service.procedure.quality;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.constant.enums.MaintainHistorySourceEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.quality.*;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.quality.MrbApplicantRequestDTO;
import net.airuima.rbase.dto.quality.MrbProcessResultDTO;
import net.airuima.rbase.dto.quality.SnUnqualifiedDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.downgrade.RbaseDownGradeProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.proxy.qms.RbaseDefectCheckItemProxy;
import net.airuima.rbase.proxy.rqms.RbaseMrbProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.quality.*;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.common.StatisticsDataCommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.quality.api.ICheckHistoryService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.quality.dto.CheckHistoryDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.CheckHistoryExportDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.InspectionResultDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.BeanUtil;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.SecurityUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史Service
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class CheckHistoryService extends CommonJpaService<CheckHistory> implements ICheckHistoryService {
    private static final String CHECK_HISTORY_ENTITY_GRAPH = "checkHistoryEntityGraph";
    private final CheckHistoryRepository checkHistoryRepository;

    @Autowired
    private CheckHistoryDetailRepository checkHistoryDetailRepository;

    @Autowired
    private CheckHistoryItemSnapshotRepository checkHistoryItemSnapshotRepository;
    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private InspectUnqualifiedRepository inspectUnqualifiedRepository;
    @Autowired
    private InspectUnqualifiedDetailRepository inspectUnqualifiedDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;
    @Autowired
    private RbaseMrbProxy rbaseMrbProxy;
    @Autowired
    private StatisticsDataCommonService statisticsDataCommonService;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    @Autowired
    private RbaseDefectCheckItemProxy defectCheckItemRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private InspectTaskDetailRepository inspectTaskDetailRepository;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private RbaseDownGradeProxy rbaseDownGradeProxy;


    public CheckHistoryService(CheckHistoryRepository checkHistoryRepository) {
        this.checkHistoryRepository = checkHistoryRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistory> find(Specification<CheckHistory> spec, Pageable pageable) {
        Page<CheckHistory> page = checkHistoryRepository.findAll(spec, pageable, new NamedEntityGraph(CHECK_HISTORY_ENTITY_GRAPH));
        if (page != null && !CollectionUtils.isEmpty(page.getContent())) {
            enrichWithDocuments(page.getContent());
        }
        return page;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CheckHistory> find(Specification<CheckHistory> spec) {
        List<CheckHistory> list = checkHistoryRepository.findAll(spec, new NamedEntityGraph(CHECK_HISTORY_ENTITY_GRAPH));
        if (!CollectionUtils.isEmpty(list)) {
            enrichWithDocuments(list);
        }
        return list;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CheckHistory> findAll(Pageable pageable) {
        Page<CheckHistory> page = checkHistoryRepository.findAll(pageable, new NamedEntityGraph(CHECK_HISTORY_ENTITY_GRAPH));
        if (page != null && !CollectionUtils.isEmpty(page.getContent())) {
            enrichWithDocuments(page.getContent());
        }
        return page;
    }

    /**
     * 为CheckHistory列表批量添加文档关联信息
     *
     * @param checkHistories 检验历史列表
     */
    private void enrichWithDocuments(List<CheckHistory> checkHistories) {
        if (CollectionUtils.isEmpty(checkHistories)) {
            return;
        }
        // 批量获取文档关联
        List<Long> recordIds = checkHistories.stream()
                .map(CheckHistory::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recordIds)) {
            // 假设rbaseDocumentProxy有批量获取的方法，如果没有，可以循环调用
            Map<Long, List<DocumentDTO>> documentMap = rbaseDocumentProxy.getDocumentRelationMap(recordIds);
            if (CollectionUtils.isEmpty(documentMap)) {
                return;
            }
            Map<Long, CheckHistory> checkHistoryMap =
                    checkHistories.stream().collect(Collectors.toMap(CheckHistory::getId, Function.identity()));

            // 将文档关联信息设置到对应的CheckHistory对象
            documentMap.forEach((id, documents) -> {
                CheckHistory checkHistory = checkHistoryMap.get(id);
                if (checkHistory != null) {
                    checkHistory.setDocumentDTOList(documents);
                }
            });
        }
    }


    /**
     * 根据检测类型、项目类型、工位查询检验过程数据
     *
     * @param category   检测类型(首检0/巡检1)
     * @param varietyId  项目类型id
     * @param workCellId 工位id
     * @return net.airuima.rbase.web.rest.procedure.quality.dto.CheckHistoryVO检验过程数据VO对象
     */
    @Transactional(readOnly = true)
    public CheckHistoryDTO processDetail(Integer category, Long varietyId, Long workCellId) {
        // 定义返回对象
        CheckHistoryDTO result = new CheckHistoryDTO();
        // 查询最新的一条记录
        CheckHistory checkHistory = checkHistoryRepository.findTopOneByCategoryAndVarietyIdAndWorkCellIdAndDeletedOrderByCreatedDateDesc(category, varietyId, workCellId, Constants.LONG_ZERO);
        if (!ObjectUtils.isEmpty(checkHistory)) {
            // 获取明细
            List<CheckHistoryDetail> checkHistoryDetailList = checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(checkHistory.getId(), Boolean.TRUE,Constants.LONG_ZERO);
            if (ValidateUtils.isValid(checkHistoryDetailList)) {
                Map<String, List<CheckHistoryDetail>> snMap = checkHistoryDetailList.stream().collect(Collectors.groupingBy(CheckHistoryDetail::getSn));
                if (ValidateUtils.isValid(snMap)) {
                    List<CheckHistoryDTO.CheckHistoryDetailSnInfo> checkHistoryDetailSnInfoList = new ArrayList<>();
                    for (String sn : snMap.keySet()) {
                        List<CheckHistoryDTO.CheckItemInfo> checkItemInfoList = new ArrayList<>();
                        List<CheckHistoryDetail> checkHistoryDetails = snMap.get(sn);
                        checkHistoryDetails.forEach(detail -> {
                            // 创建检测项目
                            CheckHistoryDTO.CheckItemInfo checkItemInfo = new CheckHistoryDTO.CheckItemInfo();
                            BeanUtils.copyProperties(detail.getCheckItem(), checkItemInfo);
                            // 创建检测明细
                            CheckHistoryDTO.CheckHistoryDetailInfo checkHistoryDetailInfo = new CheckHistoryDTO.CheckHistoryDetailInfo();
                            BeanUtils.copyProperties(detail, checkHistoryDetailInfo);
                            //添加关联文件
                            List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(detail.getId());
                            if (!CollectionUtils.isEmpty(documentDTOList)) {
                                checkHistoryDetailInfo.setDocumentDTOList(documentDTOList);
                            }
                            checkItemInfo.setCheckHistoryDetail(checkHistoryDetailInfo);
                            // 添加检测项
                            checkItemInfoList.add(checkItemInfo);
                        });
                        checkHistoryDetailSnInfoList.add(new CheckHistoryDTO.CheckHistoryDetailSnInfo(sn, checkItemInfoList));
                    }
                    result.setCheckHistoryDetailList(checkHistoryDetailSnInfoList);
                }
            }
            // 获取条件明细
            List<CheckHistoryItemSnapshot> checkHistoryItemSnapshotList = checkHistoryItemSnapshotRepository.findByCheckHistoryIdAndDeleted(checkHistory.getId(), Constants.LONG_ZERO);
            // 设置返回值
            result.setCheckHistory(checkHistory).setCheckHistoryItemSnapshotList(checkHistoryItemSnapshotList);
        }
        // 返回数据
        return result;
    }

    /**
     * 根据历史记录id查询检测过程数据
     *
     * @param historyId 历史记录id
     * @return net.airuima.rbase.web.rest.procedure.quality.dto.CheckHistoryVO返回测过程数据
     */
    @Transactional(readOnly = true)
    public CheckHistoryDTO detailByHistoryId(Long historyId) {
        // 定义返回对象
        CheckHistoryDTO result = new CheckHistoryDTO();
        // 获取检测历史
        CheckHistory checkHistory = this.findByIdAndDeleted(historyId, Constants.LONG_ZERO);
        // 获取明细
        List<CheckHistoryDetail> checkHistoryDetailList = checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(historyId, Boolean.TRUE,Constants.LONG_ZERO);
        if (ValidateUtils.isValid(checkHistoryDetailList)) {
            Map<String, List<CheckHistoryDetail>> snMap = checkHistoryDetailList.stream().collect(Collectors.groupingBy(CheckHistoryDetail::getSn));
            if (ValidateUtils.isValid(snMap)) {
                List<CheckHistoryDTO.CheckHistoryDetailSnInfo> checkHistoryDetailSnInfoList = new ArrayList<>();
                for (String sn : snMap.keySet()) {
                    List<CheckHistoryDTO.CheckItemInfo> checkItemInfoList = new ArrayList<>();
                    List<CheckHistoryDetail> checkHistoryDetails = snMap.get(sn);
                    checkHistoryDetails.forEach(detail -> {
                        // 创建检测项目
                        CheckHistoryDTO.CheckItemInfo checkItemInfo = new CheckHistoryDTO.CheckItemInfo();
                        BeanUtils.copyProperties(detail.getCheckItem(), checkItemInfo);
                        // 创建检测明细
                        CheckHistoryDTO.CheckHistoryDetailInfo checkHistoryDetailInfo = new CheckHistoryDTO.CheckHistoryDetailInfo();
                        BeanUtils.copyProperties(detail, checkHistoryDetailInfo);
                        //添加关联文件
                        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(detail.getId());
                        if (!CollectionUtils.isEmpty(documentDTOList)) {
                            checkHistoryDetailInfo.setDocumentDTOList(documentDTOList);
                        }
                        checkItemInfo.setCheckHistoryDetail(checkHistoryDetailInfo);
                        // 添加检测项
                        checkItemInfoList.add(checkItemInfo);
                    });
                    checkHistoryDetailSnInfoList.add(new CheckHistoryDTO.CheckHistoryDetailSnInfo(sn, checkItemInfoList));
                }
                result.setCheckHistoryDetailList(checkHistoryDetailSnInfoList);
            }
        } else {
            return result.setCheckHistory(checkHistory);
        }
        boolean subWsProductionMode = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet());
        List<SnWorkDetail> snWorkDetailList = new ArrayList<>();
        // 获取当前报检SN详情列表
        if (!ObjectUtils.isEmpty(checkHistory.getContainerCode()) && !checkHistory.getVirtual()) {
            // 获取容器详情
            Optional<ContainerDetail> containerDetailOptional = subWsProductionMode
                    ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO)
                    : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);

            ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));
            // 获取SN工作详情列表
            snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        } else if (!checkHistory.getVirtual()) {
            Optional<BatchWorkDetail> batchWorkDetailOptional = subWsProductionMode ?
                    batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                    batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
            BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "当前工单批次不存在"));
            // 获取SN工作详情列表
            snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        }
        if (!CollectionUtils.isEmpty(snWorkDetailList)) {
            result.setFullInspectSnList(snWorkDetailList.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).toList().stream().map(SnWorkDetail::getSn).toList());
        }
        // 获取条件明细
        List<CheckHistoryItemSnapshot> checkHistoryItemSnapshotList = checkHistoryItemSnapshotRepository.findByCheckHistoryIdAndDeleted(historyId, Constants.LONG_ZERO);
        // 设置返回值
        return result.setCheckHistoryItemSnapshotList(checkHistoryItemSnapshotList).setCheckHistory(checkHistory);
    }

    /**
     * 首检 巡检 更新检测处理结果
     *
     * @param inspectionResultDto 下交Dto
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public void firstInspectionResult(InspectionResultDTO inspectionResultDto) {
        LatestCheckResult latestCheckResult = latestCheckResultRepository.findByIdAndDeleted(inspectionResultDto.getCheckHistoryId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.latestCheckResultIsNotExist", "最新检测结果不存在"));
        if (latestCheckResult.getStatus()) {
            throw new ResponseException("error.AlreadyDone", "最新检测结果已处理");
        }
        CheckHistory checkHistory = checkHistoryRepository.findTop1ByWorkCellIdAndCategoryAndVarietyIdAndDeletedOrderByIdDesc(latestCheckResult.getWorkCell().getId(), latestCheckResult.getCategory(), latestCheckResult.getVarietyId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.checkHistoryIsNotExist", "检测历史记录不存在"));

        //修改最新检测结果处理方式以及检测历史处理方式
        latestCheckResult.setDealWay(inspectionResultDto.getDealWay()).setResult(inspectionResultDto.getDealWay() == Constants.INT_THREE ? Boolean.TRUE : latestCheckResult.getResult()).setStatus(Boolean.TRUE);
        checkHistory.setDealWay(inspectionResultDto.getDealWay()).setStatus(Boolean.TRUE);

        latestCheckResultRepository.save(latestCheckResult);
        checkHistoryRepository.save(checkHistory);
    }

    /**
     * 抽检 终检 更新检测处理结果
     *
     * @param inspectionResultDto 下交Dto
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public void inspectionResult(InspectionResultDTO inspectionResultDto) {
        CheckHistory checkHistory = checkHistoryRepository.findByIdAndDeleted(inspectionResultDto.getCheckHistoryId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.checkHistoryIsNotExist", "检测历史记录不存在"));
        if (checkHistory.getStatus()) {
            throw new ResponseException("error.AlreadyDone", "检测结果已处理");
        }
        boolean subWsProductionMode = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet());
        WorkSheet workSheet = ObjectUtils.isEmpty(checkHistory.getWorkSheet()) ? checkHistory.getSubWorkSheet().getWorkSheet() : checkHistory.getWorkSheet();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        List<WsStep> wsStepList = null;
        if (Objects.nonNull(subWorkSheet)) {
            wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        Optional<WsStep> wsStepOptional = subWsProductionMode ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        if (wsStepOptional.isEmpty()) {
            wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        }
        if (wsStepOptional.isEmpty()) {
            throw new ResponseException("error.wsStepNotFound", "投产工单工序快照不存在");
        }
        List<CheckHistoryDetail> checkHistoryDetails = checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(checkHistory.getId(), Boolean.TRUE,Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(checkHistoryDetails)) {
            throw new ResponseException("checkHistoryDetailsEmpty", "检测记录详情不存在");
        }
        //发起MRB评审（目前只针对抽检和终检）
        if (DetectionModeEnum.MRB.getCode() == inspectionResultDto.getDealWay()) {
            if (checkHistory.getCategory() <= Constants.INT_ONE) {
                throw new ResponseException("error.dealWayError", "当前检测不可以发起MRB评审方式进行处理");
            }
            BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).applicantMrb(checkHistory, checkHistoryDetails, inspectionResultDto.getReason());
            return;
        }
        //放行
        if (DetectionModeEnum.RELEASED.getCode() == inspectionResultDto.getDealWay()) {
            // 直接将其放行不合格sn，只生产一条不良品记录
            BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).addReleasedInspectUnqualified(checkHistoryDetails, wsStepList);
        }
        //维修分析 -（单只：将不合格的sn进行维修分析，合格放行；批量：全部进入维修分析）
        if (DetectionModeEnum.MAIN_TAIN.getCode() == inspectionResultDto.getDealWay()) {
            // 判断SN真实性：一个真实全部真实，反之一样
            boolean virtual = checkHistoryDetails.get(Constants.INT_ZERO).getVirtual();
            // 如果存在容器，则进行容器维修分析处理，否则进行工单器维修分析处理
            if (!ObjectUtils.isEmpty(checkHistory.getContainerCode())) {
                BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).handleContainerMaintain(subWsProductionMode, virtual, checkHistory, checkHistoryDetails, wsStepList);
            } else {
                BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).handleWorkSheetMaintain(subWsProductionMode, virtual, checkHistory, checkHistoryDetails, wsStepList);
            }
        }
        //单只批退
        if (DetectionModeEnum.BATCH_REJECTED.getCode() == inspectionResultDto.getDealWay()) {
            boolean virtual = checkHistoryDetails.get(Constants.INT_ZERO).getVirtual();
            //批量是不能进行，单只批退的
            if (virtual) {
                throw new ResponseException("snBatchRejected", "单只才能进行批退");
            }
            // 如果存在容器，则进行容器维修分析处理，否则进行工单器维修分析处理
            if (!ObjectUtils.isEmpty(checkHistory.getContainerCode())) {
                BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).handleContainerBatchRejected(subWsProductionMode, virtual, checkHistory, checkHistoryDetails);
            } else {
                BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).handleWorkSheetBatchRejected(subWsProductionMode, virtual, checkHistory, checkHistoryDetails);
            }
        }
        //重检
        if (DetectionModeEnum.RECHECK.getCode() == inspectionResultDto.getDealWay()) {
            BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).handleRecheckHistory(checkHistory);
        }
        checkHistory.setDealWay(inspectionResultDto.getDealWay()).setStatus(Boolean.TRUE);
        checkHistoryRepository.save(checkHistory);
    }

    /**
     * 发送MRB评审请求
     *
     * @param checkHistory           检验历史
     * @param checkHistoryDetailList 明细
     * @param reason                 发起原因
     */
    @Override
    public void applicantMrb(CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetailList, String reason) {
        WorkSheet workSheet = Objects.nonNull(checkHistory.getSubWorkSheet()) ? checkHistory.getSubWorkSheet().getWorkSheet() : checkHistory.getWorkSheet();
        AtomicReference<StaffDTO> staffDTO = new AtomicReference<>();
        Optional<String> loginNameOptional = SecurityUtils.getCurrentUserLogin();
        if (loginNameOptional.isPresent()) {
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(loginNameOptional.get());
            if (Objects.nonNull(userDTO) && Objects.nonNull(userDTO.getStaffDTO()) && Objects.nonNull(userDTO.getStaffDTO().getId())) {
                staffDTO.set(MapperUtils.map(userDTO.getStaffDTO(), StaffDTO.class));
            } else {
                staffDTO.set(checkHistory.getOperatorDto());
            }
        } else {
            staffDTO.set(checkHistory.getOperatorDto());
        }
        MrbApplicantRequestDTO mrbApplicantRequestDTO = new MrbApplicantRequestDTO(checkHistory, workSheet, staffDTO.get());
        mrbApplicantRequestDTO.setReason(reason);
        MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO mrbApplicantRequestDetailDTO = new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO();
        mrbApplicantRequestDetailDTO.setPopulation(checkHistory.getInspectNumber())
                .setSample(checkHistory.getNumber())
                .setUnqualifiedNumber(checkHistory.getUnqualifiedNumber());
        List<MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo> inspectInfoList = new ArrayList<>();

        Map<String, List<CheckHistoryDetail>> detailGroup = checkHistoryDetailList.stream().collect(Collectors.groupingBy(CheckHistoryDetail::getSn));
        detailGroup.forEach((sn, checkHistoryDetails) -> {
            MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo inspectInfo = new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo();
            inspectInfo.setSn(sn);
            List<String> defects = new ArrayList<>();
            List<MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo> inspectItemInfoList = new ArrayList<>();
            checkHistoryDetails.forEach(checkHistoryDetail -> {
                if (Objects.nonNull(checkHistoryDetail.getDefect())) {
                    defects.add(checkHistoryDetail.getDefect().getName());
                }
                MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo inspectItemInfo = new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo();
                inspectItemInfo.setCode(checkHistoryDetail.getCheckItem().getCode())
                        .setName(checkHistoryDetail.getCheckItem().getName())
                        .setCheckData(checkHistoryDetail.getCheckData())
                        .setResult(checkHistoryDetail.getResult())
                        .setQualifiedRange(checkHistoryDetail.getQualifiedRange());
                inspectItemInfoList.add(inspectItemInfo);
            });
            inspectInfo.setDefectList(defects).setInspectItemInfoList(inspectItemInfoList);
            inspectInfoList.add(inspectInfo);
        });
        mrbApplicantRequestDetailDTO.setInspectInfoList(inspectInfoList);
        mrbApplicantRequestDTO.setSourceInspectInfo(mrbApplicantRequestDetailDTO);
        rbaseMrbProxy.custom(mrbApplicantRequestDTO);
        checkHistory.setDealWay(Constants.INT_FIVE).setDeleted(Constants.LONG_ZERO);
        checkHistoryRepository.save(checkHistory);
    }

    /**
     * 单支检测记录处理
     *
     * @param checkHistoryDetails 检测详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/5/15
     */
    public void snInspectUnqualified(boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet.getWorkSheet()) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedList = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();
        List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snUnqualifiedList.stream().map(SnUnqualifiedDTO::getSn).collect(Collectors.toList()), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(snWorkStatuses) || snWorkStatuses.size() != snUnqualifiedList.size()) {
            throw new ResponseException("error.snStatusError", "sn状态异常");
        }
        Map<String, SnWorkStatus> snWorkStatusMap = snWorkStatuses.stream()
                .collect(Collectors.toMap(SnWorkStatus::getSn, Function.identity()));
        List<MaintainHistoryDTO> maintainHistoryList = Lists.newArrayList();
        List<SnUnqualifiedItem> snUnqualifiedItemList = Lists.newArrayList();
        //添加维修分析记录&添加sn不良项目记录
        snUnqualifiedList.forEach(snUnqualifiedDto -> {
            //添加维修分析记录
            if ((release && snUnqualifiedDto.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) || !release) {
                MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
                maintainHistory.setNumber(Constants.INT_ONE)
                        .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                        .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                        .setStep(checkHistory.getStep())
                        .setSubWorkSheet(checkHistory.getSubWorkSheet())
                        .setWorkSheet(checkHistory.getWorkSheet())
                        .setSnWorkStatus(snWorkStatusMap.get(snUnqualifiedDto.getSn()))
                        .setContainerDetail(maintainHistory.getSnWorkStatus().getLatestSnWorkDetail().getContainerDetail())
                        .setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                        .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                //添加维修分析来源
                maintainHistory.setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()))
                        .setCheckHistoryId(checkHistory.getId());
                maintainHistoryList.add(maintainHistory);
            }

            //添加sn不良记录
            SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
            snUnqualifiedItem.setSn(snUnqualifiedDto.getSn()).setSnWorkDetail(snWorkStatusMap.get(snUnqualifiedDto.getSn()).getLatestSnWorkDetail())
                    .setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                    .setStep(checkHistory.getStep())
                    .setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setWorkSheet(checkHistory.getWorkSheet());
            snUnqualifiedItemList.add(snUnqualifiedItem);
        });

        //处理sn状态，修改sn详情
        List<SnWorkStatus> unqualifiedSnWorkStatus = Lists.newArrayList();
        List<SnWorkDetail> unqualifiedSnWorkDetails = Lists.newArrayList();
        snUnqualifiedList.forEach(snUnqualifiedDto -> {
            SnWorkStatus snWorkStatus = snWorkStatusMap.get(snUnqualifiedDto.getSn());
            snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE).setLatestUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem()).setLatestReworkSnWorkDetail(snWorkStatus.getLatestSnWorkDetail()).setIsUpdateBatchWorkDetail(Boolean.TRUE);
            if (release && snUnqualifiedDto.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_SCRAP.getCategoryName()) {
                snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            } else if (release && snUnqualifiedDto.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName()) {
                dealWithUnqualifiedSnWhenOnlineRework(snWorkStatus);
            } else if ((release && snUnqualifiedDto.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) || !release) {
                snWorkStatus.setStatus(SnWorkStatusEnum.MAINTAIN.getStatus());
                //批退时需要更新子工单和工单不合格数，因为在维修分析放行时会减去不合格数
                if (null != subWorkSheet && null != subWorkSheet.getId()) {
                    subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + net.airuima.constant.Constants.INT_ONE);
                    if (subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber()) {
                        subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                        subWorkSheet.setActualEndDate(LocalDateTime.now());
                    }
                    subWorkSheetRepository.save(subWorkSheet);
                }
                if (null != workSheet && null != workSheet.getId()) {
                    workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + net.airuima.constant.Constants.INT_ONE);
                    if (workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber()) {
                        workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                        workSheet.setActualEndDate(LocalDateTime.now());
                    }
                    workSheetRepository.save(workSheet);
                }
            }
            //修改sn详情为不合格
            SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
            latestSnWorkDetail.setResult(Constants.INT_ZERO).setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem());
            StaffPerform staffPerform = staffPerformRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
            if (null != staffPerform) {
                staffPerformRepository.save(staffPerform.setQualifiedNumber(net.airuima.constant.Constants.INT_ZERO).setUnqualifiedNumber(net.airuima.constant.Constants.INT_ONE));
                List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItems = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndDeleted(staffPerform.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (CollectionUtils.isEmpty(staffPerformUnqualifiedItems)) {
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem().setStaffPerform(staffPerform)
                            .setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                            .setNumber(net.airuima.constant.Constants.INT_ONE).setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime());
                    staffPerformUnqualifiedItem.setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                }
            }
            unqualifiedSnWorkStatus.add(snWorkStatus);
            unqualifiedSnWorkDetails.add(latestSnWorkDetail);
        });
        //保存sn相关数据
        snWorkStatusRepository.saveAll(unqualifiedSnWorkStatus);
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.saveAll(unqualifiedSnWorkDetails);
        snUnqualifiedItemRepository.saveAll(snUnqualifiedItemList);
        if (!CollectionUtils.isEmpty(maintainHistoryList)) {
            rbaseMaintainHistoryProxy.batchSaveInstance(maintainHistoryList);
        }
        //更新Rworker下个可能的待做工序信息
        snWorkDetailList.forEach(snWorkDetail -> {
            if (CollectionUtils.isEmpty(maintainHistoryList) || !maintainHistoryList.stream().noneMatch(maintainHistory -> maintainHistory.getSnWorkStatus().getSn().equals(snWorkDetail.getSn()))) {
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
            }
        });
    }


    /**
     * 单只批退处理 （默认全部进入维修分析，不看不良项目处理方式）
     *
     * @param checkHistory        检测记录
     * @param snWorkDetails       sn详情
     * @param checkHistoryDetails 检测历史详情
     * @param unqualifiedItem     不良项目
     */
    public List<SnWorkDetail> snBatchRejectInspectUnqualified(CheckHistory checkHistory, List<SnWorkDetail> snWorkDetails, List<CheckHistoryDetail> checkHistoryDetails, UnqualifiedItem unqualifiedItem) {

        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet.getWorkSheet()) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();

        List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snWorkDetails.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(snWorkStatuses)) {
            throw new ResponseException("error.snStatusError", "sn状态不存在");
        }

        Map<String, CheckHistoryDetail> unqualifiedCheckHistoryDetailMap = new HashMap<>();
        checkHistoryDetails.stream().filter(checkHistoryDetail -> !checkHistoryDetail.getResult())
                .collect(Collectors.groupingBy(CheckHistoryDetail::getSn)).forEach((key, values) -> {
                    unqualifiedCheckHistoryDetailMap.put(key, values.get(Constants.INT_ZERO));
                });

        Map<String, SnWorkStatus> snWorkStatusMap = snWorkStatuses.stream()
                .collect(Collectors.toMap(SnWorkStatus::getSn, Function.identity()));
        List<MaintainHistoryDTO> maintainHistoryList = Lists.newArrayList();
        List<SnUnqualifiedItem> snUnqualifiedItemList = Lists.newArrayList();
        //添加维修分析记录&添加sn不良项目记录
        snWorkDetails.forEach(snWorkDetail -> {
            CheckHistoryDetail checkHistoryDetail = unqualifiedCheckHistoryDetailMap.get(snWorkDetail.getSn());

            MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
            maintainHistory.setNumber(Constants.INT_ONE)
                    .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                    .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                    .setStep(checkHistory.getStep())
                    .setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setWorkSheet(checkHistory.getWorkSheet())
                    .setSnWorkStatus(snWorkStatusMap.get(snWorkDetail.getSn()))
                    .setContainerDetail(maintainHistory.getSnWorkStatus().getLatestSnWorkDetail().getContainerDetail())
                    .setUnqualifiedItem(Objects.nonNull(checkHistoryDetail) ? checkHistoryDetail.getUnqualifiedItem() : unqualifiedItem)
                    .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);

            //增加维修分析来源
            maintainHistory.setCheckHistoryId(checkHistory.getId())
                    .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
            maintainHistoryList.add(maintainHistory);

            //添加sn不良记录
            SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
            snUnqualifiedItem.setSn(snWorkDetail.getSn()).setSnWorkDetail(snWorkStatusMap.get(snWorkDetail.getSn()).getLatestSnWorkDetail())
                    .setUnqualifiedItem(maintainHistory.getUnqualifiedItem())
                    .setStep(checkHistory.getStep())
                    .setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setWorkSheet(checkHistory.getWorkSheet());
            snUnqualifiedItemList.add(snUnqualifiedItem);
        });

        //处理sn状态，修改sn详情
        List<SnWorkStatus> unqualifiedSnWorkStatus = Lists.newArrayList();
        List<SnWorkDetail> unqualifiedSnWorkDetails = Lists.newArrayList();
        snWorkDetails.forEach(unqualifiedSnWorkDetail -> {

            CheckHistoryDetail checkHistoryDetail = unqualifiedCheckHistoryDetailMap.get(unqualifiedSnWorkDetail.getSn());


            SnWorkStatus snWorkStatus = snWorkStatusMap.get(unqualifiedSnWorkDetail.getSn());
            snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE)
                    .setLatestUnqualifiedItem(Objects.nonNull(checkHistoryDetail) ? checkHistoryDetail.getUnqualifiedItem() : unqualifiedItem)
                    .setLatestReworkSnWorkDetail(unqualifiedSnWorkDetail).setIsUpdateBatchWorkDetail(Boolean.TRUE);

            snWorkStatus.setStatus(SnWorkStatusEnum.MAINTAIN.getStatus());
            //批退时需要更新子工单和工单不合格数，因为在维修分析放行时会减去不合格数
            if (Objects.nonNull(subWorkSheet)) {
                subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + Constants.INT_ONE);
                if (subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber()) {
                    subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                    subWorkSheet.setActualEndDate(LocalDateTime.now());
                }
                subWorkSheetRepository.save(subWorkSheet);
            }
            if (Objects.nonNull(workSheet)) {
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + Constants.INT_ONE);
                if (workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber()) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                    workSheet.setActualEndDate(LocalDateTime.now());
                }
                workSheetRepository.save(workSheet);
            }

            //修改sn详情为不合格
            unqualifiedSnWorkDetail.setResult(Constants.INT_ZERO).setUnqualifiedItem(snWorkStatus.getLatestUnqualifiedItem());
            StaffPerform staffPerform = staffPerformRepository.findBySnWorkDetailIdAndDeleted(unqualifiedSnWorkDetail.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
            if (null != staffPerform) {
                staffPerformRepository.save(staffPerform.setQualifiedNumber(net.airuima.constant.Constants.INT_ZERO).setUnqualifiedNumber(net.airuima.constant.Constants.INT_ONE));
                List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItems = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndDeleted(staffPerform.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (CollectionUtils.isEmpty(staffPerformUnqualifiedItems)) {
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem().setStaffPerform(staffPerform)
                            .setUnqualifiedItem(unqualifiedSnWorkDetail.getUnqualifiedItem())
                            .setNumber(net.airuima.constant.Constants.INT_ONE).setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime());
                    staffPerformUnqualifiedItem.setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                }
            }
            unqualifiedSnWorkStatus.add(snWorkStatus);
            unqualifiedSnWorkDetails.add(unqualifiedSnWorkDetail);
        });
        //保存sn相关数据
        snWorkStatusRepository.saveAll(unqualifiedSnWorkStatus);
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.saveAll(unqualifiedSnWorkDetails);
        snUnqualifiedItemRepository.saveAll(snUnqualifiedItemList);
        if (!CollectionUtils.isEmpty(maintainHistoryList)) {
            rbaseMaintainHistoryProxy.batchSaveInstance(maintainHistoryList);
        }
        return snWorkDetailList;
    }

    /**
     * 容器检测记录处理
     *
     * @param checkHistoryDetails 检测详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/5/15
     */
    public void containerInspectUnqualified(boolean virtual, boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedDtos = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();
        Optional<ContainerDetail> containerDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO) :
                containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);
        ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));

        //保存容器不良信息
        ContainerDetail finalContainerDetail = containerDetail;
        snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(finalContainerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            containerDetailUnqualifiedItem.setContainerDetail(finalContainerDetail).setUnqualifiedItem(unqualifiedItem).setNumber(containerDetailUnqualifiedItem.getNumber() + snUnqualifieds.size()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
        }));

        //调整容器详情信息
        if (snUnqualifiedDtos.size() == containerDetail.getTransferNumber()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
        } else {
            containerDetail.setTransferNumber(containerDetail.getTransferNumber() > Constants.INT_ZERO ? containerDetail.getTransferNumber() - snUnqualifiedDtos.size() : Constants.INT_ZERO)
                    .setQualifiedNumber(containerDetail.getQualifiedNumber() - snUnqualifiedDtos.size())
                    .setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + snUnqualifiedDtos.size());
        }
        //如果是虚拟SN  & 存在维修分析类型的不良则全部开出
        SnUnqualifiedDTO snUnqualifiedDto = snUnqualifiedDtos.stream().filter(snUnqualifiedDTO -> snUnqualifiedDTO.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        boolean existMatain = Boolean.FALSE;
        if ((virtual && Objects.nonNull(snUnqualifiedDto) && release) || (virtual && !release)) {
            MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
            maintainHistory.setNumber(containerDetail.getInputNumber())
                    .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                    .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                    .setStep(containerDetail.getBatchWorkDetail().getStep())
                    .setSubWorkSheet(containerDetail.getBatchWorkDetail().getSubWorkSheet())
                    .setWorkSheet(containerDetail.getBatchWorkDetail().getWorkSheet())
                    .setContainerDetail(containerDetail)
                    .setUnqualifiedItem(Objects.nonNull(snUnqualifiedDto) ? snUnqualifiedDto.getUnqualifiedItem() : snUnqualifiedDtos.get(Constants.INT_ZERO).getUnqualifiedItem())
                    .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            //增加维修分析来源
            maintainHistory.setCheckHistoryId(checkHistory.getId())
                    .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
            rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
            existMatain = Boolean.TRUE;
        }
        //如果是虚拟的SN则需要以容器工序不良更新员工产量及不良数据
        if (virtual) {
            List<StaffPerform> staffPerforms = staffPerformRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(staffPerforms)) {
                staffPerforms.forEach(staffPerform -> {
                    staffPerform = staffPerformRepository.save(staffPerform.setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + snUnqualifiedDtos.size()).setQualifiedNumber(staffPerform.getQualifiedNumber() - snUnqualifiedDtos.size()));
                    StaffPerform finalStaffPerform = staffPerform;
                    snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
                        StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(finalStaffPerform.getId(), unqualifiedItem.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                        staffPerformUnqualifiedItem.setStaffPerform(finalStaffPerform).setUnqualifiedItem(unqualifiedItem)
                                .setRecordDate(finalStaffPerform.getRecordDate()).setRecordTime(finalStaffPerform.getRecordTime())
                                .setNumber(staffPerformUnqualifiedItem.getNumber() + snUnqualifieds.size())
                                .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }));
                });
            }
        }
        containerDetail = containerDetailRepository.save(containerDetail);
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //更新Rworker下个可能的的待做工序信息
        if (virtual && !existMatain) {
            nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail, Boolean.FALSE);
        }
    }

    /**
     * 单只批退-容器详情-不良记录处理
     *
     * @param containerDetail 容器详情
     * @param snWorkDetails   sn详情列表
     * @return ContainerDetail
     * @since 1.8.1
     */
    public ContainerDetail containerBatchRejectInspectUnqualified(ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetails) {

        //保存容器不良信息
        ContainerDetail finalContainerDetail = containerDetail;
        snWorkDetails.stream().collect(Collectors.groupingBy(SnWorkDetail::getUnqualifiedItem)).forEach(((unqualifiedItem, snWorkDetailList) -> {
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(finalContainerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            containerDetailUnqualifiedItem.setContainerDetail(finalContainerDetail).setUnqualifiedItem(unqualifiedItem).setNumber(containerDetailUnqualifiedItem.getNumber() + snWorkDetailList.size()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
        }));

        //调整容器详情信息
        if (snWorkDetails.size() == containerDetail.getTransferNumber()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
        } else {
            containerDetail.setTransferNumber(containerDetail.getTransferNumber() > Constants.INT_ZERO ? containerDetail.getTransferNumber() - snWorkDetails.size() : Constants.INT_ZERO)
                    .setQualifiedNumber(containerDetail.getQualifiedNumber() - snWorkDetails.size())
                    .setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + snWorkDetails.size());
        }
        return containerDetailRepository.save(containerDetail);
    }

    /**
     * 批次检测记录处理
     *
     * @param checkHistoryDetails 检测详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/5/15
     */
    public void batchWorkDetailInspectUnqualified(boolean virtual, boolean existContainer, boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();

        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedDtos = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();

        //获取批次信息
        Optional<BatchWorkDetail> batchWorkDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "批次详情不存在"));
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //保存批次不良详情
        snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach((key, value) -> {
            Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                    wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO) :
                    wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO);
            WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.orElse(new WsStepUnqualifiedItem());
            wsStepUnqualifiedItem.setWorkSheet(checkHistory.getWorkSheet()).setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setStep(checkHistory.getStep()).setUnqualifiedItem(key).setNumber(wsStepUnqualifiedItem.getNumber() + value.size())
                    .setFlag(ObjectUtils.isEmpty(wsStepUnqualifiedItem.getFlag()) ? Boolean.FALSE : wsStepUnqualifiedItem.getFlag())
                    .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
            if (Objects.isNull(wsStepUnqualifiedItem.getOperatorId())) {
                wsStepUnqualifiedItem.setOperatorId(checkHistory.getOperatorId());
            }
            wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
        });
        //如果是虚拟SN & 不存在容器 & 存在维修分析类型的不良则全部开出
        SnUnqualifiedDTO snUnqualifiedDto = snUnqualifiedDtos.stream().filter(snUnqualifiedDTO -> snUnqualifiedDTO.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        boolean existMaintainAnalyse = Boolean.FALSE;
        if ((virtual && !existContainer && Objects.nonNull(snUnqualifiedDto) && release) || (virtual && !release)) {
            MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
            maintainHistory.setNumber(batchWorkDetail.getInputNumber())
                    .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                    .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                    .setStep(batchWorkDetail.getStep())
                    .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                    .setWorkSheet(batchWorkDetail.getWorkSheet())
                    .setUnqualifiedItem(Objects.nonNull(snUnqualifiedDto) ? snUnqualifiedDto.getUnqualifiedItem() : snUnqualifiedDtos.get(Constants.INT_ZERO).getUnqualifiedItem())
                    .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            //增加维修分析来源
            maintainHistory.setCheckHistoryId(checkHistory.getId())
                    .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
            rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
            existMaintainAnalyse = Boolean.TRUE;
        }
        //虚拟SN以及不存在容器时以批量工序开出来更新员工产量及不良数据
        if (virtual && !existContainer) {
            List<StaffPerform> staffPerformList = staffPerformRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(staffPerformList)) {
                staffPerformList.forEach(staffPerform -> {
                    staffPerform = staffPerformRepository.save(staffPerform.setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + snUnqualifiedDtos.size()).setQualifiedNumber(staffPerform.getQualifiedNumber() - snUnqualifiedDtos.size()));
                    StaffPerform finalStaffPerform = staffPerform;
                    snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
                        StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(finalStaffPerform.getId(), unqualifiedItem.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                        staffPerformUnqualifiedItem.setStaffPerform(finalStaffPerform)
                                .setRecordDate(finalStaffPerform.getRecordDate()).setRecordTime(finalStaffPerform.getRecordTime())
                                .setUnqualifiedItem(unqualifiedItem).setNumber(staffPerformUnqualifiedItem.getNumber() + snUnqualifieds.size())
                                .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }));
                });
            }
        }
        //原始合格数
        int originQualifiedNumber = batchWorkDetail.getQualifiedNumber();
        //原始不合格数
        int originUnqualifiedNumber = batchWorkDetail.getUnqualifiedNumber();
        //原始待流转数
        int originTransferNumber = batchWorkDetail.getTransferNumber();
        //修改批次详情信息
        if (snUnqualifiedDtos.size() == batchWorkDetail.getTransferNumber()) {
            //todo: 可能需要后续补充
            batchWorkDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(batchWorkDetail.getInputNumber());
        } else {
            batchWorkDetail.setTransferNumber(batchWorkDetail.getTransferNumber() - snUnqualifiedDtos.size())
                    .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - snUnqualifiedDtos.size())
                    .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + snUnqualifiedDtos.size());
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        //更新Rworker下个可能的待做工序信息
        if (virtual && !existContainer && !existMaintainAnalyse) {
            nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail, Boolean.FALSE);
        }
        //若为最后一个工序则可能需要更新工单数据
        updateWorkSheetReleaseWhenLastStep(batchWorkDetail, originQualifiedNumber, originUnqualifiedNumber, originTransferNumber);
    }

    /**
     * 单只批退-更新批次详情
     *
     * @param checkHistory     检测历史
     * @param snWorkDetailList sn详情列表
     * @since 1.8.1
     */
    public void batchWorkDetailRejectInspectUnqualified(CheckHistory checkHistory, List<SnWorkDetail> snWorkDetailList) {

        //获取批次信息
        BatchWorkDetail batchWorkDetail = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("batchWorkDetailEmpty", "批次详情不存在")) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("batchWorkDetailEmpty", "批次详情不存在"));
        //保存批次不良详情
        snWorkDetailList.stream().collect(Collectors.groupingBy(SnWorkDetail::getUnqualifiedItem)).forEach((key, values) -> {
            Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                    wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO) :
                    wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO);
            WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.orElse(new WsStepUnqualifiedItem());
            wsStepUnqualifiedItem.setWorkSheet(checkHistory.getWorkSheet()).setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setStep(checkHistory.getStep()).setUnqualifiedItem(key).setNumber(wsStepUnqualifiedItem.getNumber() + values.size())
                    .setFlag(ObjectUtils.isEmpty(wsStepUnqualifiedItem.getFlag()) ? Boolean.FALSE : wsStepUnqualifiedItem.getFlag())
                    .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
            if (Objects.isNull(wsStepUnqualifiedItem.getOperatorId())) {
                wsStepUnqualifiedItem.setOperatorId(checkHistory.getOperatorId());
            }
            wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
        });

        //原始合格数
        int originQualifiedNumber = batchWorkDetail.getQualifiedNumber();
        //原始不合格数
        int originUnqualifiedNumber = batchWorkDetail.getUnqualifiedNumber();
        //原始待流转数
        int originTransferNumber = batchWorkDetail.getTransferNumber();
        //修改批次详情信息
        if (snWorkDetailList.size() == batchWorkDetail.getTransferNumber()) {
            batchWorkDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(batchWorkDetail.getInputNumber());
        } else {
            batchWorkDetail.setTransferNumber(batchWorkDetail.getTransferNumber() - snWorkDetailList.size())
                    .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - snWorkDetailList.size())
                    .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + snWorkDetailList.size());
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        //若为最后一个工序则可能需要更新工单数据
        updateWorkSheetReleaseWhenLastStep(batchWorkDetail, originQualifiedNumber, originUnqualifiedNumber, originTransferNumber);
    }

    /**
     * 放行若为最后一个工序或者中间工序流转数为0则可能需要更新工单数据
     * TODO:这里需要重新判断子工单/工单完成状态更新相关数据，需要判断终检、末检、任务是否还有
     *
     * @param batchWorkDetail         更新后的批量详情
     * @param originQualifiedNumber   原始详情合格数
     * @param originUnqualifiedNumber 原始详情不合格数
     * @param originTransferNumber    原始详情待流转数
     * <AUTHOR>
     * @date 2023/10/9
     */
    public void updateWorkSheetReleaseWhenLastStep(BatchWorkDetail batchWorkDetail, int originQualifiedNumber, int originUnqualifiedNumber, int originTransferNumber) {
        if (batchWorkDetail.getFinish() != ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            return;
        }
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = null != subWorkSheet ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        WsStep wsStep = null;
        if (null != subWorkSheet) {
            wsStep = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        }
        if (null == wsStep) {
            wsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        }
        if (null == wsStep) {
            return;
        }
        boolean isLastStep = StringUtils.isBlank(wsStep.getAfterStepId());
        //验证是否存在待检(抽检、终检、末检)任务未完成、维修分析未完成、复检未完成
        boolean todoInspectTask = validateTodoInspectTaskAndMaintainTaskAndOnlineRework(subWorkSheet, workSheet);
        if (isLastStep || batchWorkDetail.getTransferNumber() == Constants.INT_ZERO) {
            if (null != subWorkSheet && isLastStep) {
                subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() - originQualifiedNumber + batchWorkDetail.getQualifiedNumber())
                        .setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() - originUnqualifiedNumber + batchWorkDetail.getUnqualifiedNumber());
                if (!todoInspectTask) {
                    subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                            .setActualEndDate(LocalDateTime.now());
                }
                subWorkSheetRepository.save(subWorkSheet);
            }
            if (null != subWorkSheet && !isLastStep) {
                subWorkSheet.setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber());
                if (!todoInspectTask) {
                    subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()).setActualEndDate(LocalDateTime.now());
                }
                subWorkSheetRepository.save(subWorkSheet);
            }
            //判断是否存在返工单未完成
            boolean reworkSheetNotFinished = validateReworkSheetNotFinished(Objects.nonNull(subWorkSheet), workSheet);
            if (isLastStep) {
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - originQualifiedNumber + batchWorkDetail.getQualifiedNumber())
                        .setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - originUnqualifiedNumber + batchWorkDetail.getUnqualifiedNumber());
                if (!todoInspectTask && !reworkSheetNotFinished) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now());
                }
                workSheetRepository.save(workSheet);
            }
            if (!isLastStep) {
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + batchWorkDetail.getUnqualifiedNumber());
                if (workSheet.getNumber() == (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber()) && !todoInspectTask && !reworkSheetNotFinished) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()).setActualEndDate(LocalDateTime.now());
                    workSheetRepository.save(workSheet);
                }
                workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), Constants.INT_ZERO, batchWorkDetail.getUnqualifiedNumber(), OperationEnum.ADD);
            }
            //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
            if (workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()) {
                rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(workSheet);
            }
            if (null != subWorkSheet) {
                //更新工单的子工单个数以及完成个数(包括正常、异常)
                subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
            }
            //若详情对应的工单为返工单还要更新原始正常单的数据
            if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() && isLastStep && !todoInspectTask && !reworkSheetNotFinished) {
                Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
                wsReworkOptional.ifPresent(wsRework -> {
                    WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
                    originWorkSheet.setQualifiedNumber(originWorkSheet.getQualifiedNumber() - originQualifiedNumber + batchWorkDetail.getQualifiedNumber())
                            .setUnqualifiedNumber(originWorkSheet.getUnqualifiedNumber() + originQualifiedNumber - batchWorkDetail.getQualifiedNumber())
                            .setReworkQualifiedNumber(originWorkSheet.getReworkQualifiedNumber() - originQualifiedNumber + batchWorkDetail.getQualifiedNumber());
                    //判断工单所有子工单及返工单是否都已完成，若已完成则更新工单完成状态及日期
                    if (!validateReworkSheetNotFinished(Objects.nonNull(subWorkSheet), originWorkSheet) && (originWorkSheet.getQualifiedNumber() + originWorkSheet.getUnqualifiedNumber()) == originWorkSheet.getNumber()) {
                        workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now());
                    }
                    workSheetRepository.save(originWorkSheet);
                    //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
                    if (originWorkSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()) {
                        rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(originWorkSheet);
                    }
                });
            }
        }
        //更新相关统计报表
        if (isLastStep) {
            //更新工单统计表
            workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), batchWorkDetail.getQualifiedNumber()-originQualifiedNumber, batchWorkDetail.getUnqualifiedNumber()- originUnqualifiedNumber, OperationEnum.ADD);
            //更新生产计划统计数据
            statisticsDataCommonService.updateProductionPlanStatistics(subWorkSheet, workSheet, batchWorkDetail.getQualifiedNumber() - originQualifiedNumber, OperationEnum.ADD);
        }
        //更新在制看板数据
        if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()) {
            statisticsDataCommonService.updateWorkSheetStepStatisticsInfo(subWorkSheet, workSheet, batchWorkDetail.getStep(), Constants.INT_ZERO,
                    batchWorkDetail.getQualifiedNumber() - originQualifiedNumber, batchWorkDetail.getUnqualifiedNumber() - originUnqualifiedNumber, batchWorkDetail.getTransferNumber() - originTransferNumber);
        }
        //更新工序组生产计划数据
        statisticsDataCommonService.updateStepGroupProductionPlanInfo(subWorkSheet, workSheet, batchWorkDetail.getStep(), batchWorkDetail.getQualifiedNumber() - originQualifiedNumber, OperationEnum.ADD);
    }

    /**
     * 判断是否存在质检任务未处理、质检结果未处理、维系分析未处理
     *
     * @param subWorkSheet
     * @param workSheet
     * @return
     */
    public boolean validateTodoInspectTaskAndMaintainTaskAndOnlineRework(SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //判断是否存在待质检(抽检、终检、末检)分析的任务
        long todoTaskCount = Objects.nonNull(subWorkSheet) ?
                inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO) :
                inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return Boolean.TRUE;
        }
        //判断是否存在待质检(抽检、终检、末检)历史未处理
        todoTaskCount = Objects.nonNull(subWorkSheet) ? checkHistoryRepository.countBySubWorkSheetIdAndCategoryInAndDealWayAndDeleted(subWorkSheet.getId(), Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_ZERO, Constants.LONG_ZERO) :
                checkHistoryRepository.countByWorkSheetIdAndCategoryInAndDealWayAndDeleted(workSheet.getId(), Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.INT_ZERO, Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return Boolean.TRUE;
        }
        //判断是否存在待维修分析的任务
        todoTaskCount = Objects.nonNull(subWorkSheet) ? rbaseMaintainHistoryProxy.countBySubWorkSheetIdAndStatusLessThanAndDeleted(subWorkSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO)
                : rbaseMaintainHistoryProxy.countByWorkSheetIdAndStatusLessThanAndDeleted(workSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return Boolean.TRUE;
        }
        //判断是否存在不良复检未处理
        todoTaskCount = Objects.nonNull(subWorkSheet) ? stepReinspectRepository.countBySubWorkSheetIdAndStatusAndDeleted(subWorkSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO)
                : stepReinspectRepository.countByWorkSheetIdAndStatusAndDeleted(workSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (todoTaskCount > Constants.LONG_ZERO) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 判断是否存在未完成返工单
     *
     * @param subWsProductMode 子工单
     * @param workSheet        工单
     * @return boolean 是否存在未完成的返工单
     */
    public boolean validateReworkSheetNotFinished(boolean subWsProductMode, WorkSheet workSheet) {
        //若非返工单的工单判断是否完成时需要判断是否存在有未完成的返工单在投产
        boolean existReWorkSheetNotFinished = Boolean.FALSE;
        if (subWsProductMode && workSheet.getGenerateSubWsStatus() != Constants.INT_TWO) {
            return Boolean.TRUE;
        }
        if (workSheet.getCategory() != ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()) {
            //查询返工单是否都已完成
            List<WsRework> wsReworks = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            existReWorkSheetNotFinished = (!CollectionUtils.isEmpty(wsReworks) && wsReworks.stream().anyMatch(wsRework -> wsRework.getReworkWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && wsRework.getReworkWorkSheet().getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()));
        }
        //若非返工单的工单判断是否完成时需要判断是否存在未完成的子工单在投产
        if (!existReWorkSheetNotFinished) {
            List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            existReWorkSheetNotFinished = !CollectionUtils.isEmpty(subWorkSheetList) && subWorkSheetList.stream().anyMatch(subWorkSheetTemp -> subWorkSheetTemp.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && subWorkSheetTemp.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
        }
        return existReWorkSheetNotFinished;
    }

    /**
     * 验证（子）是否存在待做工序
     *
     * @param subWsProductMode 投产模式
     * @param workSheet        工单
     * @param subWorkSheet     子工单
     * @return 是否存在待做工序
     */
    public Boolean validateTodoStep(boolean subWsProductMode, WorkSheet workSheet, SubWorkSheet subWorkSheet) {
        List<BatchWorkDetail> todoBatchWorkDetailList = subWsProductMode ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndFinishAndDeleted(subWorkSheet.getId(), Constants.INT_ZERO, Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndFinishAndDeleted(workSheet.getId(), Constants.INT_ZERO, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(todoBatchWorkDetailList)) {
            return Boolean.TRUE;
        }
        Optional<BatchWorkDetail> lastBatchWorkDetailOptional = subWsProductMode ?
                batchWorkDetailRepository.findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(subWorkSheet.getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findTop1ByWorkSheetIdAndDeletedOrderByIdDesc(workSheet.getId(), Constants.LONG_ZERO);
        if (lastBatchWorkDetailOptional.isPresent()) {
            BatchWorkDetail batchWorkDetail = lastBatchWorkDetailOptional.get();
            WsStep nextWsStep = commonService.getNextWsStep(batchWorkDetail.getWorkSheet(), batchWorkDetail.getSubWorkSheet(), batchWorkDetail.getStep());
            //已做工序都完成，获取最后一道工序查看 是否还存在下道工序可作
            if (Objects.nonNull(nextWsStep) && batchWorkDetail.getQualifiedNumber() != Constants.INT_ZERO) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;

    }


    /**
     * 放行 - 有效单支进入维修分析，虚拟sn则放行直接回显生产数据
     *
     * @param checkHistoryDetails 检测历史明细列表
     */
    public void addReleasedInspectUnqualified(List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        // 如果检测历史明细列表为空，则直接返回
        if (!ValidateUtils.isValid(checkHistoryDetails)) {
            return;
        }
        //是否为虚拟sn
        boolean virtual = checkHistoryDetails.get(Constants.INT_ZERO).getVirtual();
        // 过滤出 存在不良sn
        List<String> snList = checkHistoryDetails.stream()
                .filter(checkHistoryDetail -> !checkHistoryDetail.getResult())
                .toList().stream().map(CheckHistoryDetail::getSn).distinct().toList();
        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        //投产粒度工单ID
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //没有不合格项时更新工单状态
        if (!ValidateUtils.isValid(snList)) {
            this.updateWorkSheetStatus(Objects.nonNull(subWorkSheet), subWorkSheet, workSheet);
            //更新工作台下个待做工序清单信息
            if (!virtual) {
                List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(checkHistoryDetails.stream().map(CheckHistoryDetail::getSn).distinct().toList(), Constants.LONG_ZERO);
                List<SnWorkDetail> latestSnWorkDetailList = snWorkStatusList.stream().map(SnWorkStatus::getLatestSnWorkDetail).toList();
                latestSnWorkDetailList.forEach(snWorkDetail -> {
                    nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
                });
            } else if (ValidateUtils.isValid(checkHistory.getContainerCode())) {
                Optional<ContainerDetail> containerDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                        containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO) :
                        containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);
                ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));
                nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail, Boolean.FALSE);
            } else {
                Optional<BatchWorkDetail> batchWorkDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                        batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
                BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "批次详情不存在"));
                nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail, Boolean.FALSE);
            }
            return;
        }
        //通过不良sn 获取对应的检测详情数据
        checkHistoryDetails = checkHistoryDetails.stream()
                .filter(checkHistoryDetail -> snList.contains(checkHistoryDetail.getSn())).collect(Collectors.toList());
        checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        if (checkHistoryDetails.stream().anyMatch(checkHistoryDetail -> Objects.isNull(checkHistoryDetail.getUnqualifiedItem()))) {
            if (checkHistory.getCategory() == Constants.INSPECT_FQC_CATEGORY) {
                throw new ResponseException("error.unqualifiedItemIsNull", "终检检测项目不合格必须选择不良项目");
            } else if (checkHistory.getCategory() == Constants.INSPECT_PQC_CATEGORY) {
                throw new ResponseException("error.unqualifiedItemIsNull", "抽检检测项目不合格必须选择不良项目");
            }
        }
        //处理不良sn
        if (!virtual) {
            snInspectUnqualified(Boolean.TRUE, checkHistoryDetails, wsStepList);
            List<String> allSnList = checkHistoryDetails.stream().map(CheckHistoryDetail::getSn).distinct().collect(Collectors.toList());
            allSnList.removeAll(snList);
            //更新剩余合格的SN的rworker的下个可能的待做工序信息
            if (!CollectionUtils.isEmpty(allSnList)) {
                List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(allSnList, Constants.LONG_ZERO);
                List<SnWorkDetail> latestSnWorkDetailList = snWorkStatusList.stream().map(SnWorkStatus::getLatestSnWorkDetail).toList();
                latestSnWorkDetailList.forEach(snWorkDetail -> {
                    nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
                });
            }
        }
        //容器
        if (ValidateUtils.isValid(checkHistory.getContainerCode())) {
            containerInspectUnqualified(virtual, Boolean.TRUE, checkHistoryDetails, wsStepList);
        }
        //批量
        batchWorkDetailInspectUnqualified(virtual, ValidateUtils.isValid(checkHistory.getContainerCode()), Boolean.TRUE, checkHistoryDetails, wsStepList);
    }

    /**
     * 更新子工单及工单完成状态
     *
     * @param subWsProductionMode 生产模式
     * @param subWorkSheet        子工单
     * @param workSheet           工单
     */
    public void updateWorkSheetStatus(boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        if (subWsProductionMode) {
            if ((subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber()) != subWorkSheet.getNumber()) {
                return;
            }
            //验证是否存在待检(抽检、终检、末检)任务未完成、维修分析未完成、复检未完成
            boolean todoInspectTask = this.validateTodoInspectTaskAndMaintainTaskAndOnlineRework(subWorkSheet, subWorkSheet.getWorkSheet());
            if (todoInspectTask) {
                return;
            }
            //验证是否存在待做工序
            Boolean todoStep = validateTodoStep(subWsProductionMode, workSheet, subWorkSheet);
            if (todoStep) {
                return;
            }
            if (subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                subWorkSheetRepository.save(subWorkSheet);
            }
            if ((workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber()) != workSheet.getNumber()) {
                return;
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
                return;
            }
            //判断是否存在返工单未完成
            boolean reworkSheetNotFinished = this.validateReworkSheetNotFinished(Objects.nonNull(subWorkSheet), subWorkSheet.getWorkSheet());
            if (reworkSheetNotFinished) {
                return;
            }
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            workSheetRepository.save(workSheet);
        } else {
            if ((workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber()) != workSheet.getNumber()) {
                return;
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
                return;
            }
            //验证是否存在待检(抽检、终检、末检)任务未完成、维修分析未完成、复检未完成
            boolean todoInspectTask = this.validateTodoInspectTaskAndMaintainTaskAndOnlineRework(null, workSheet);
            if (todoInspectTask) {
                return;
            }
            //验证是否存在待做工序
            Boolean todoStep = validateTodoStep(subWsProductionMode, workSheet, subWorkSheet);
            if (todoStep) {
                return;
            }
            //判断是否存在返工单未完成
            boolean reworkSheetNotFinished = this.validateReworkSheetNotFinished(Boolean.FALSE, subWorkSheet.getWorkSheet());
            if (reworkSheetNotFinished) {
                return;
            }
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            workSheetRepository.save(workSheet);
        }
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() && workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            wsReworkOptional.ifPresent(wsRework -> {
                WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
                //判断工单所有子工单及返工单是否都已完成，若已完成则更新工单完成状态及日期
                if (!this.validateReworkSheetNotFinished(Objects.nonNull(subWorkSheet), originWorkSheet) && (originWorkSheet.getQualifiedNumber() + originWorkSheet.getUnqualifiedNumber()) == originWorkSheet.getNumber()) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                }
                workSheetRepository.save(originWorkSheet);
            });
        }
    }

    /**
     * 容器器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    public void handleContainerMaintain(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        // 获取容器详情
        Optional<ContainerDetail> containerDetailOptional = subWsProductionMode
                ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO)
                : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);

        ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));
        // 获取SN工作详情列表
        List<SnWorkDetail> snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).addMaintainInspectUnqualified(containerDetail, null, snWorkDetailList, checkHistoryDetails, wsStepList);
    }

    /**
     * 工单器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    public void handleWorkSheetMaintain(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        Optional<BatchWorkDetail> batchWorkDetailOptional = subWsProductionMode ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "当前工单批次不存在"));
        // 获取SN工作详情列表
        List<SnWorkDetail> snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        //维修分析处理
        BeanUtil.getHighestPrecedenceBean(ICheckHistoryService.class).addMaintainInspectUnqualified(null, batchWorkDetail, snWorkDetailList, checkHistoryDetails, wsStepList);
    }

    /**
     * 单只批退-容器器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    public void handleContainerBatchRejected(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails) {
        // 获取容器详情
        Optional<ContainerDetail> containerDetailOptional = subWsProductionMode
                ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO)
                : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);

        ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("containerDetailEmpty", "容器详情不存在"));
        // 获取SN工作详情列表
        List<SnWorkDetail> snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        //单只批退-sn-容器-工序详情处理
        addBatchRejectInspectUnqualified(containerDetail, snWorkDetailList, checkHistoryDetails);
    }

    /**
     * 单只批退-工单器维修分析处理
     *
     * @param subWsProductionMode 检测力度
     * @param virtual             真实虚拟
     * @param checkHistory        检测历史
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     */
    public void handleWorkSheetBatchRejected(boolean subWsProductionMode, boolean virtual, CheckHistory checkHistory, List<CheckHistoryDetail> checkHistoryDetails) {
        Optional<BatchWorkDetail> batchWorkDetailOptional = subWsProductionMode ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("containerDetailEmpty", "当前工单批次不存在"));
        // 获取SN工作详情列表
        List<SnWorkDetail> snWorkDetailList = getAllCheckSnWorkDetailList(checkHistory);
        //单只批退-sn-容器-工序详情处理
        addBatchRejectInspectUnqualified(null, snWorkDetailList, checkHistoryDetails);
    }

    /**
     * 重检
     *
     * @param checkHistory 检测历史
     */
    public void handleRecheckHistory(CheckHistory checkHistory) {
        if (Objects.isNull(checkHistory.getInspectTaskId())) {
            throw new ResponseException("inspectTaskEmpty", "未获取到来源质检任务，不能重检");
        }
        InspectTask inspectTask = inspectTaskRepository.findByIdAndDeleted(checkHistory.getInspectTaskId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("inspectTaskEmpty", "未获取到来源质检任务，不能重检"));

        InspectTask newInspectTask = new InspectTask();
        BeanUtils.copyProperties(inspectTask, newInspectTask, "id");
        newInspectTask.setStatus(Boolean.FALSE).setTodoInspectedTime(LocalDateTime.now())
                .setCache(null);
        newInspectTask = inspectTaskRepository.save(newInspectTask);

        List<String> sns = inspectTaskDetailRepository.findSnListByInspectTaskIdAndDeleted(inspectTask.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(sns)) {
            InspectTask finalNewInspectTask = newInspectTask;
            List<InspectTaskDetail> inspectTaskDetails = sns.stream().map(sn -> new InspectTaskDetail().setInspectTask(finalNewInspectTask).setSn(sn))
                    .toList();
            inspectTaskDetailRepository.saveAll(inspectTaskDetails);
        }
    }

    /**
     * 获取SN工作详情列表
     *
     * @param  checkHistory   检查历史
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetail>
     */
    private List<SnWorkDetail> getAllCheckSnWorkDetailList(CheckHistory checkHistory) {
        if (!checkHistory.getVirtual()) {
            // 直接从检查历史数据获取检测历史详情数据SN列表
            List<String> allCheckSnList = checkHistoryDetailRepository.findByCheckHistoryIdAndDeleted(
                    checkHistory.getId(), Constants.LONG_ZERO);

            if (!ValidateUtils.isValid(allCheckSnList)) {
                throw new ResponseException("error.checkHistorySnNotFound",
                        "检查历史编号[" + checkHistory.getSerialNumber() + "]下未找到任何SN记录");
            }

            List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(
                    allCheckSnList, Constants.LONG_ZERO);

            if (!ValidateUtils.isValid(snWorkStatuses)) {
                throw new ResponseException("error.snStatusNotFound",
                        "检查历史编号[" + checkHistory.getSerialNumber() + "]中的SN未找到对应的工作状态记录");
            }

            List<SnWorkDetail> snWorkDetails = snWorkStatuses.stream()
                    .map(SnWorkStatus::getLatestSnWorkDetail)
                    .filter(snWorkDetail -> snWorkDetail.getStep().getId().equals(checkHistory.getStep().getId()))
                    .toList();

            if (!ValidateUtils.isValid(snWorkDetails)) {
                throw new ResponseException("error.snStepMismatch",
                        "检查历史工序[" + checkHistory.getStep().getName() + "]与SN记录中的工作详情工序不匹配");
            }
            return snWorkDetails;
        }
        return Collections.emptyList();
    }

    /**
     * 维修分析处理
     *
     * @param containerDetail     批退数量
     * @param snWorkDetailList    sn详情列表
     * @param checkHistoryDetails 检测历史详情列表
     * @return void
     * <AUTHOR>
     * @Date 2023/5/8
     */
    public void addMaintainInspectUnqualified(ContainerDetail containerDetail, BatchWorkDetail batchWorkDetail, List<SnWorkDetail> snWorkDetailList, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        if (!ValidateUtils.isValid(checkHistoryDetails)) {
            return;
        }
        // 过滤出 存在不良sn
        List<String> snList = checkHistoryDetails.stream()
                .filter(checkHistoryDetail -> !checkHistoryDetail.getResult())
                .toList().stream().map(CheckHistoryDetail::getSn).distinct().toList();
        if (ValidateUtils.isValid(snList)) {
            checkHistoryDetails = checkHistoryDetails.stream()
                    .filter(checkHistoryDetail -> snList.stream().anyMatch(sn -> sn.equals(checkHistoryDetail.getSn()))).collect(Collectors.toList());
        }
        List<SnWorkDetail> qualifiedSnWorkDetailList = null;
        if (!CollectionUtils.isEmpty(snWorkDetailList)) {
            qualifiedSnWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> !snList.contains(snWorkDetail.getSn())).collect(Collectors.toList());
        }
        CheckHistoryDetail checkHistoryDetail = checkHistoryDetails.get(Constants.INT_ZERO);
        //是否为虚拟sn
        boolean virtual = checkHistoryDetail.getVirtual();
        CheckHistory checkHistory = checkHistoryDetail.getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet.getWorkSheet()) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        if (!CollectionUtils.isEmpty(qualifiedSnWorkDetailList)) {
            qualifiedSnWorkDetailList.forEach(snWorkDetail -> {
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
            });
        }
        //真实sn，添加开出不良维修记录
        if (!virtual && ValidateUtils.isValid(checkHistoryDetails)) {
            snInspectUnqualified(Boolean.FALSE, checkHistoryDetails, wsStepList);
            if (null != containerDetail) {
                containerInspectUnqualified(virtual, Boolean.FALSE, checkHistoryDetails, wsStepList);
            }
            batchWorkDetailInspectUnqualified(virtual, null != containerDetail, Boolean.FALSE, checkHistoryDetails, wsStepList);
        }
        //真实sn，添加未开出不良维修记录，但进行批退了的sn
        if (!virtual && ValidateUtils.isValid(snWorkDetailList)) {
            //只开出不合格的SN到维修分析
            snWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> snList.contains(snWorkDetail.getSn())).toList();
            List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(snWorkStatuses) || snWorkStatuses.size() != snWorkDetailList.size()) {
                throw new ResponseException("error.snStatusError", "sn状态异常");
            }
            Map<String, SnWorkStatus> snWorkStatusMap = snWorkStatuses.stream()
                    .collect(Collectors.toMap(SnWorkStatus::getSn, Function.identity()));

            List<MaintainHistoryDTO> maintainHistoryList = Lists.newArrayList();

            snWorkDetailList.stream().filter(snWorkDetail -> snList.stream().noneMatch(sn -> sn.equals(snWorkDetail.getSn())))
                    .forEach(snWorkDetail -> {
                        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
                        maintainHistory.setNumber(Constants.INT_ONE)
                                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                                .setStep(checkHistory.getStep())
                                .setSubWorkSheet(checkHistory.getSubWorkSheet())
                                .setWorkSheet(checkHistory.getWorkSheet())
                                .setSnWorkStatus(snWorkStatusMap.get(snWorkDetail.getSn()))
                                .setContainerDetail(snWorkDetail.getContainerDetail())
                                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        //增加维修分析来源
                        maintainHistory.setCheckHistoryId(checkHistory.getId())
                                .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
                        maintainHistoryList.add(maintainHistory);
                    });
            rbaseMaintainHistoryProxy.batchSaveInstance(maintainHistoryList);

            //修改sn状态为待分析
            List<SnWorkStatus> snWorkStatusList = maintainHistoryList.stream().map(MaintainHistoryDTO::getSnWorkStatus).map(snWorkStatus -> snWorkStatus.setStatus(SnWorkStatusEnum.MAINTAIN.getStatus()).setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE)).collect(Collectors.toList());
            snWorkStatusRepository.saveAll(snWorkStatusList);
        } else {
            saveMaintainHistory(containerDetail, batchWorkDetail, checkHistoryDetails, checkHistory);
        }

        //解绑容器
        if (!ObjectUtils.isEmpty(containerDetail) && containerDetail.getTransferNumber() == Constants.INT_ZERO && containerDetail.getStatus() == ConstantsEnum.BINDING.getCategoryName()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
            containerDetailRepository.save(containerDetail);
        }
    }

    /**
     * 单只批退-sn-容器-工序详情处理
     *
     * @param containerDetail     容器详情
     * @param snWorkDetailList    sn详情
     * @param checkHistoryDetails 检测历史
     * @since 1.8.1
     */
    public void addBatchRejectInspectUnqualified(ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList, List<CheckHistoryDetail> checkHistoryDetails) {
        if (!ValidateUtils.isValid(snWorkDetailList)) {
            throw new ResponseException("error.snNotExist", "检测sn详情记录不存在");
        }

        //只处理工序合格的进行批退，不合格的在工序下交时已经处理
        snWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE)
                .collect(Collectors.toList());

        //获取默认的第一个检测结果中的不良项目
        UnqualifiedItem unqualifiedItem = checkHistoryDetails.stream()
                .filter(checkHistoryDetail -> !checkHistoryDetail.getResult())
                .map(CheckHistoryDetail::getUnqualifiedItem).findFirst().orElseThrow(() ->
                        new ResponseException("checkHistoryDetailUnqualifiedItemEmpty", "质检结果详情中没有项目不能进行单只批退"));

        CheckHistoryDetail checkHistoryDetail = checkHistoryDetails.get(Constants.INT_ZERO);
        //是否为虚拟sn
        CheckHistory checkHistory = checkHistoryDetail.getCheckHistory();

        //单子批退处理
        snWorkDetailList = snBatchRejectInspectUnqualified(checkHistory, snWorkDetailList, checkHistoryDetails, unqualifiedItem);

        //容器批退处理
        if (Objects.nonNull(containerDetail)) {
            containerBatchRejectInspectUnqualified(containerDetail, snWorkDetailList);
        }
        //工单批退处理
        batchWorkDetailRejectInspectUnqualified(checkHistory, snWorkDetailList);

        //解绑容器
        if (!ObjectUtils.isEmpty(containerDetail) && containerDetail.getTransferNumber() == Constants.INT_ZERO && containerDetail.getStatus() == ConstantsEnum.BINDING.getCategoryName()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
            containerDetailRepository.save(containerDetail);
        }
    }

    /**
     * 保存维修历史
     *
     * @param containerDetail     容器生产详情
     * @param batchWorkDetail     批量工序生产详情
     * @param checkHistoryDetails 检测历史明细
     * @param checkHistory        检测历史
     */
    private void saveMaintainHistory(ContainerDetail containerDetail, BatchWorkDetail batchWorkDetail, List<CheckHistoryDetail> checkHistoryDetails, CheckHistory checkHistory) {
        Optional<CheckHistoryDetail> checkHistoryDetailOptional = checkHistoryDetails.stream().filter(checkHistoryDetail1 -> !ObjectUtils.isEmpty(checkHistoryDetail1.getUnqualifiedItem()) && (checkHistoryDetail1.getUnqualifiedItem().getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName())).findFirst();
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(checkHistory.getStep())
                .setSubWorkSheet(checkHistory.getSubWorkSheet())
                .setWorkSheet(checkHistory.getWorkSheet())
                .setUnqualifiedItem(checkHistoryDetailOptional.map(CheckHistoryDetail::getUnqualifiedItem).orElse(null))
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        if (!ObjectUtils.isEmpty(containerDetail)) {
            maintainHistory.setNumber(containerDetail.getQualifiedNumber()).setContainerDetail(containerDetail);
        } else if (!ObjectUtils.isEmpty(batchWorkDetail)) {
            maintainHistory.setNumber(batchWorkDetail.getQualifiedNumber());
        }
        //增加维修分析来源
        maintainHistory.setCheckHistoryId(checkHistory.getId())
                .setSource(MaintainHistorySourceEnum.fromCheckHistoryCategory(checkHistory.getCategory()));
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        //批量
        if (ValidateUtils.isValid(checkHistoryDetails)) {
            checkHistoryDetails.stream().filter(ch -> !ObjectUtils.isEmpty(ch.getUnqualifiedItem())).collect(Collectors.groupingBy(CheckHistoryDetail::getUnqualifiedItem))
                    .forEach(((unqualifiedItem, checkHistoryDetailsList) -> {
                        //容器生产不良
                        if (!ObjectUtils.isEmpty(containerDetail)) {
                            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
                            containerDetailUnqualifiedItem.setContainerDetail(containerDetail).setUnqualifiedItem(unqualifiedItem).setNumber(containerDetailUnqualifiedItem.getNumber() + checkHistoryDetailsList.size()).setDeleted(Constants.LONG_ZERO);
                            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                        }
                        Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO) :
                                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO);
                        WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.orElse(new WsStepUnqualifiedItem());
                        wsStepUnqualifiedItem.setWorkSheet(checkHistory.getWorkSheet()).setSubWorkSheet(checkHistory.getSubWorkSheet())
                                .setStep(checkHistory.getStep()).setUnqualifiedItem(unqualifiedItem).setNumber(wsStepUnqualifiedItem.getNumber() + checkHistoryDetailsList.size())
                                .setFlag(ObjectUtils.isEmpty(wsStepUnqualifiedItem.getFlag()) ? Boolean.FALSE : wsStepUnqualifiedItem.getFlag())
                                .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
                        if (Objects.isNull(wsStepUnqualifiedItem.getOperatorId())) {
                            wsStepUnqualifiedItem.setOperatorId(checkHistory.getOperatorId());
                        }
                        wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);

                    }));
        }
    }

    /**
     * MRB评审完成时需要保存结果数据
     *
     * @param serialNumber            IQC单据号
     * @param mrbProcessResultDTOList MRB处理结果
     */
    public void mrbProcessCheckRecordResult(String serialNumber, List<MrbProcessResultDTO> mrbProcessResultDTOList) {
        checkHistoryRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).ifPresent(history -> {
            history.setProcessMrbUnqualifiedItem(Boolean.TRUE).setMrbProcessResultInfoList(mrbProcessResultDTOList).setDeleted(Constants.LONG_ZERO);
            this.save(history);
        });
    }

    /**
     * MRB取消评审时候更新处理状态为待处理
     *
     * @param serialNumber 检测流水号
     */
    public void mrbUpdateCheckRecordStatusWhenCanceled(String serialNumber) {
        checkHistoryRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).ifPresent(history -> {
            history.setProcessMrbUnqualifiedItem(Boolean.FALSE).setDealWay(Constants.INT_ZERO).setDeleted(Constants.LONG_ZERO);
            this.save(history);
        });
    }

    /**
     * 根据MRB结果处理不良
     *
     * @param id                   检验历史ID
     * @param processResultDTOList 处理结果
     */
    public void processUnqualified(Long id, List<MrbProcessResultDTO> processResultDTOList) {
        CheckHistory checkHistory = checkHistoryRepository.getReferenceById(id);
        checkHistory.setMrbProcessResultInfoList(processResultDTOList).setStatus(Boolean.TRUE).setProcessMrbUnqualifiedItem(Boolean.FALSE);
        checkHistoryRepository.save(checkHistory);
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId();
        List<WsStep> wsStepList = commonService.findBatchWsStep(workSheet, subWorkSheet);
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(checkHistory.getStep().getId())).findFirst().orElse(null);
        List<MrbProcessResultDTO> dealProcessResultDTOList = processResultDTOList.stream().filter(processResult -> processResult.getResult() >= Constants.INT_THREE).toList();
        //是否产生了新的质检任务
        AtomicBoolean generateTask = new AtomicBoolean(false);
        //质检SN均为真实SN,只有返工、报废、全检的才需要处理
        if (!checkHistory.getVirtual()) {
            WsStep finalCurrWsStep = currWsStep;
            dealProcessResultDTOList.forEach(processResult -> {
                if (this.processWhenSn(checkHistory, finalCurrWsStep, processResult)) {
                    generateTask.set(true);
                }
            });
            //没有产生新的任务则更新Rworker下个可能的SN待做工序信息
            if (!generateTask.get()) {
                List<CheckHistoryDetail> checkHistoryDetails = checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(checkHistory.getId(), Boolean.TRUE,Constants.LONG_ZERO);
                if (CollectionUtils.isEmpty(checkHistoryDetails)) {
                    return;
                }
                List<String> snList = checkHistoryDetails.stream().map(CheckHistoryDetail::getSn).collect(Collectors.toSet()).stream().toList();
                List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(snList, Constants.LONG_ZERO);
                snWorkStatusList.forEach(snWorkStatus -> {
                    nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkStatus.getLatestSnWorkDetail(), snWorkStatus.getLatestSnWorkDetail().getReworkTime(), Boolean.FALSE, Boolean.FALSE);
                });
            }
            return;
        }
        //容器批量质检
        if (StringUtils.isNotBlank(checkHistory.getContainerCode())) {
            WsStep finalCurrWsStep = currWsStep;
            dealProcessResultDTOList.forEach(processResult -> {
                if (this.processWhenContainer(checkHistory, finalCurrWsStep, processResult, Boolean.TRUE, Boolean.TRUE)) {
                    generateTask.set(true);
                }
            });
            //没有产生新的任务则更新Rworker下个可能的容器待做工序信息
            if (!generateTask.get()) {
                ContainerDetail containerDetail = Objects.nonNull(subWorkSheet)
                        ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null)
                        : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null);
                nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail, Boolean.FALSE);
            }
        } else {
            WsStep finalCurrWsStep = currWsStep;
            dealProcessResultDTOList.forEach(processResult -> {
                if (this.processWhenBatch(checkHistory, finalCurrWsStep, processResult, Boolean.TRUE, Boolean.TRUE)) {
                    generateTask.set(true);
                }
            });
            //没有产生新的任务则更新Rworker下个可能的批量待做工序信息
            if (!generateTask.get()) {
                BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet)
                        ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO).orElse(null) :
                        batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO).orElse(null);
                nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail, Boolean.FALSE);
            }
        }
        if (Objects.nonNull(currWsStep) && StringUtils.isBlank(currWsStep.getAfterStepId())) {
            //更新工单完成状态
            this.updateWorkSheetStatus(Objects.nonNull(subWorkSheet), subWorkSheet, workSheet);
        }
    }

    /**
     * 单支模式时处理检测数据
     *
     * @param checkHistory  检测历史
     * @param currWsStep    当前工序快照
     * @param processResult 处理结果
     * @return 是否生成了新的质检任务
     */
    public boolean processWhenSn(CheckHistory checkHistory, WsStep currWsStep, MrbProcessResultDTO processResult) {
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        AtomicBoolean existSingleSnOnlineRepair = new AtomicBoolean(false);
        AtomicBoolean generateInspectTask = new AtomicBoolean(false);
        //报废或者返工
        if (processResult.getResult() == Constants.INT_FIVE || processResult.getResult() == Constants.INT_THREE) {
            processResult.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(unqualifiedItemInfo.getSnList(), Constants.LONG_ZERO);
                snWorkStatusList.forEach(snWorkStatus -> {
                    SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
                    //是否为单支返工
                    boolean singleSnOnlineRepair = Boolean.FALSE;
                    snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE);
                    if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(latestSnWorkDetail.getSubWorkSheet().getId())) {
                        if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                            singleSnOnlineRepair = Boolean.TRUE;
                            existSingleSnOnlineRepair.set(true);
                        }
                    }
                    if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(latestSnWorkDetail.getWorkSheet().getId())) {
                        if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                            singleSnOnlineRepair = Boolean.TRUE;
                            existSingleSnOnlineRepair.set(true);
                        }
                    }
                    UnqualifiedItem targetUnqualifiedItem = unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getUnqualifiedItemId());
                    if (processResult.getResult() == Constants.INT_THREE) {
                        //若不良项目没有不良组别则sn生产状态改为报废
                        if (null == targetUnqualifiedItem.getUnqualifiedGroup()) {
                            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
                        } else {
                            //若没有配置单支不良在线返工规则则也认为报废
                            WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(workSheet.getPedigree(), targetUnqualifiedItem.getUnqualifiedGroup().getId(), workSheet.getClientId());
                            if (null == reWorkFlow) {
                                snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
                            } else {
                                snWorkStatus.setWorkFlow(reWorkFlow).setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus()).setReworkStartDate(null == snWorkStatus.getReworkStartDate() ? LocalDateTime.now() : snWorkStatus.getReworkStartDate());
                            }
                        }
                    } else {
                        snWorkStatus.setLatestReworkSnWorkDetail(latestSnWorkDetail)
                                .setLatestUnqualifiedItem(latestSnWorkDetail.getUnqualifiedItem()).setStatus(SnWorkStatusEnum.SCRAP.getStatus()).setEndDate(LocalDateTime.now());
                    }
                    latestSnWorkDetail.setResult(Constants.INT_ZERO).setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId()));
                    snWorkDetailRepository.save(latestSnWorkDetail);
                    snWorkStatusRepository.save(snWorkStatus);
                    SnUnqualifiedItem snUnqualifiedItem = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(new SnUnqualifiedItem());
                    snUnqualifiedItem.setSubWorkSheet(subWorkSheet).setWorkSheet(Objects.nonNull(subWorkSheet) ? null : workSheet).setSn(snWorkStatus.getSn()).setStep(latestSnWorkDetail.getStep()).setSnWorkDetail(latestSnWorkDetail).setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId())).setDeleted(Constants.LONG_ZERO);
                    snUnqualifiedItemRepository.save(snUnqualifiedItem);
                    //更新员工产量表信息
                    StaffPerform staffPerform = staffPerformRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
                    if (Objects.nonNull(staffPerform)) {
                        staffPerform.setUnqualifiedNumber(Constants.INT_ONE).setQualifiedNumber(Constants.INT_ZERO).setInputNumber(Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
                        staffPerform = staffPerformRepository.save(staffPerform);
                        StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                        staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId()))
                                .setNumber(Constants.INT_ONE).setRecordDate(LocalDate.now()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }
                    //如果是单支在线返工的最后一个工序则需要更新工单的合格数
                    if (StringUtils.isBlank(currWsStep.getAfterStepId()) && singleSnOnlineRepair) {
                        statisticsDataCommonService.updateWorkSheetCompleteInfo(null, workSheet, -Constants.INT_ONE, Constants.INT_ONE, Constants.INT_ONE, Boolean.TRUE);
                        statisticsDataCommonService.updateStepGroupProductionPlanInfo(subWorkSheet, workSheet, latestSnWorkDetail.getStep(), Constants.INT_ONE, OperationEnum.MINUS);
                        statisticsDataCommonService.updateProductionPlanStatistics(subWorkSheet, workSheet, Constants.INT_ONE, OperationEnum.MINUS);
                    }
                });
            });
        }
        //全检
        else if (processResult.getResult() == Constants.INT_FOUR) {
            InspectTask inspectTask = new InspectTask(checkHistory);
            inspectTask.setInspectParameterInfo(this.getFullInspectParameterInfo(checkHistory, processResult)).setDeleted(Constants.LONG_ZERO);
            inspectTaskRepository.save(inspectTask);
            generateInspectTask.set(Boolean.TRUE);
        }
        if (StringUtils.isNotBlank(checkHistory.getContainerCode())) {
            this.processWhenContainer(checkHistory, currWsStep, processResult, Boolean.FALSE, Boolean.FALSE);
        } else if (!existSingleSnOnlineRepair.get()) {
            this.processWhenBatch(checkHistory, currWsStep, processResult, Boolean.FALSE, Boolean.FALSE);
        }
        return generateInspectTask.get();
    }

    /**
     * 容器模式时处理检测数据
     *
     * @param checkHistory       检测历史
     * @param currWsStep         当前工序快照
     * @param processResult      处理结果
     * @param generateInspetTask 是否生成任务
     * @param updateStaffPerform 是否更新员工产量
     * @return 是否生成了新的质检任务
     */
    public boolean processWhenContainer(CheckHistory checkHistory, WsStep currWsStep, MrbProcessResultDTO processResult, boolean generateInspetTask, boolean updateStaffPerform) {
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        ContainerDetail containerDetail = Objects.nonNull(subWorkSheet)
                ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null)
                : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(containerDetail)) {
            throw new ResponseException("error.containerDetailNotExist", "容器生产详情不存在");
        }
        StaffPerform staffPerform;
        if (updateStaffPerform) {
            List<StaffPerform> staffPerformList = staffPerformRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
            staffPerform = !CollectionUtils.isEmpty(staffPerformList) ? staffPerformList.get(Constants.INT_ZERO) : null;
        } else {
            staffPerform = null;
        }
        AtomicBoolean generateInspectTask = new AtomicBoolean(false);
        //报废或者返工时更新容器详情及可能的产量数据
        if (processResult.getResult() == Constants.INT_FIVE || processResult.getResult() == Constants.INT_THREE) {
            processResult.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                containerDetail.setQualifiedNumber(containerDetail.getQualifiedNumber() - processResult.getNumber())
                        .setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + processResult.getNumber())
                        .setTransferNumber(containerDetail.getTransferNumber() - processResult.getNumber())
                        .setDeleted(Constants.LONG_ZERO);
                containerDetailRepository.save(containerDetail);
                ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
                containerDetailUnqualifiedItem.setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId())).setContainerDetail(containerDetail).setNumber(containerDetailUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber()).setDeleted(Constants.LONG_ZERO);
                containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                if (updateStaffPerform && Objects.nonNull(staffPerform)) {
                    staffPerform.setQualifiedNumber(staffPerform.getQualifiedNumber() - processResult.getNumber()).setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + processResult.getNumber());
                    staffPerformRepository.save(staffPerform);
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(null);
                    if (Objects.nonNull(staffPerformUnqualifiedItem)) {
                        staffPerformUnqualifiedItem.setNumber(staffPerformUnqualifiedItem.getNumber() + processResult.getNumber()).setDeleted(staffPerformUnqualifiedItem.getNumber() == Constants.INT_ZERO ? staffPerformUnqualifiedItem.getId() : Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }
                }
            });
        }
        //全检生成全检任务
        else if (generateInspetTask && processResult.getResult() == Constants.INT_FOUR) {
            InspectTask inspectTask = new InspectTask(checkHistory);
            inspectTask.setInspectParameterInfo(this.getFullInspectParameterInfo(checkHistory, processResult)).setDeleted(Constants.LONG_ZERO);
            inspectTaskRepository.save(inspectTask);
            generateInspectTask.set(Boolean.TRUE);
        }
        //更新容器详情对应的批量相关数据
        this.processWhenBatch(checkHistory, currWsStep, processResult, Boolean.FALSE, Boolean.FALSE);
        return generateInspectTask.get();
    }


    /**
     * 批量模式时处理检测数据
     *
     * @param checkHistory       检测历史
     * @param currWsStep         当前工序快照
     * @param processResult      处理结果
     * @param generateInspetTask 是否生成任务
     * @param updateStaffPerform 是否更新员工产量
     * @return 是否生成了新的质检任务
     */
    public boolean processWhenBatch(CheckHistory checkHistory, WsStep currWsStep, MrbProcessResultDTO processResult, boolean generateInspetTask, boolean updateStaffPerform) {
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet)
                ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO).orElse(null) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(batchWorkDetail)) {
            throw new ResponseException("error.batchWorkDetailNotExist", "批量生产详情不存在");
        }
        StaffPerform staffPerform;
        if (updateStaffPerform) {
            List<StaffPerform> staffPerformList = staffPerformRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
            staffPerform = !CollectionUtils.isEmpty(staffPerformList) ? staffPerformList.get(Constants.INT_ZERO) : null;
        } else {
            staffPerform = null;
        }
        //报废或者返工时更新批量详情及可能的产量数据
        if (processResult.getResult() == Constants.INT_FIVE || processResult.getResult() == Constants.INT_THREE) {
            processResult.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                batchWorkDetail.setTransferNumber(batchWorkDetail.getTransferNumber() - processResult.getNumber())
                        .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + processResult.getNumber())
                        .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - processResult.getNumber())
                        .setDeleted(Constants.LONG_ZERO);
                batchWorkDetailRepository.save(batchWorkDetail);
                WsStepUnqualifiedItem wsStepUnqualifiedItem = Objects.nonNull(subWorkSheet) ? wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), checkHistory.getStep().getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem())
                        : wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), checkHistory.getStep().getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
                wsStepUnqualifiedItem.setSubWorkSheet(subWorkSheet).setWorkSheet(Objects.isNull(subWorkSheet) ? workSheet : null)
                        .setStep(checkHistory.getStep())
                        .setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId()))
                        .setNumber(wsStepUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber())
                        .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
                if (Objects.isNull(wsStepUnqualifiedItem.getOperatorId())) {
                    wsStepUnqualifiedItem.setOperatorId(checkHistory.getOperatorId());
                }
                wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                //最后一个工序则需要更新(子)工单相关合格数信息
                if (StringUtils.isBlank(currWsStep.getAfterStepId())) {
                    statisticsDataCommonService.updateWorkSheetCompleteInfo(subWorkSheet, workSheet, -unqualifiedItemInfo.getNumber(), unqualifiedItemInfo.getNumber(), -processResult.getNumber(), Boolean.FALSE);
                }
                //更新在制看板数据
                if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()) {
                    statisticsDataCommonService.updateWorkSheetStepStatisticsInfo(subWorkSheet, workSheet, checkHistory.getStep(), Constants.INT_ZERO, -processResult.getNumber(), processResult.getNumber(), -processResult.getNumber());
                }
                //更新工序组生产计划数据
                statisticsDataCommonService.updateStepGroupProductionPlanInfo(subWorkSheet, workSheet, checkHistory.getStep(), processResult.getNumber(), OperationEnum.MINUS);
                //更新生产计划统计数据
                statisticsDataCommonService.updateProductionPlanStatistics(subWorkSheet, workSheet, processResult.getNumber(), OperationEnum.MINUS);
                if (updateStaffPerform && Objects.nonNull(staffPerform)) {
                    staffPerform.setQualifiedNumber(staffPerform.getQualifiedNumber() - processResult.getNumber()).setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + processResult.getNumber());
                    staffPerformRepository.save(staffPerform);
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(staffPerform.getId(), unqualifiedItemInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                    staffPerformUnqualifiedItem.setStaffPerform(staffPerform)
                            .setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemInfo.getUnqualifiedItemId()))
                            .setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime())
                            .setNumber(staffPerformUnqualifiedItem.getNumber() + processResult.getNumber()).setDeleted(staffPerformUnqualifiedItem.getNumber() == Constants.INT_ZERO
                                    ? staffPerformUnqualifiedItem.getId() : Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                }
            });
        }
        //全检生成全检任务
        else if (generateInspetTask && processResult.getResult() == Constants.INT_FOUR) {
            InspectTask inspectTask = new InspectTask(checkHistory);
            inspectTask.setInspectParameterInfo(this.getFullInspectParameterInfo(checkHistory, processResult)).setDeleted(Constants.LONG_ZERO);
            inspectTaskRepository.save(inspectTask);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * 获取全检参数
     *
     * @param checkHistory  检测历史
     * @param processResult 不良处理结果
     * @return RworkerQualityInspectionPlanDTO
     */
    private RworkerQualityInspectionPlanDTO getFullInspectParameterInfo(CheckHistory checkHistory, MrbProcessResultDTO processResult) {
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.getReferenceById(processResult.getFullInspectInfo().getId());
        RworkerQualityInspectionPlanDTO rworkerQualityInspectionPlanDto = new RworkerQualityInspectionPlanDTO(pedigreeStepCheckRule);
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        rworkerQualityInspectionPlanDto.setProductWorkSheetId(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId())
                .setNumber(processResult.getNumber())
                .getSampleCaseDto()
                .setNumber(processResult.getNumber());
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            List<RworkerQualityInspectionPlanDTO.CheckItemDTO> checkItems = pedigreeStepCheckItemList.stream().map(pedigreeStepCheckItem -> new RworkerQualityInspectionPlanDTO.CheckItemDTO(pedigreeStepCheckItem, processResult.getNumber())).collect(Collectors.toList());
            checkItems = checkItems.stream().map(checkItem -> {
                //添加检测项目关联的文件
                List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(checkItem.getId());
                Optional.ofNullable(documentDTOList).ifPresent(checkItem::setDocumentDtos);
                //添加检测项目关联的缺陷原因
                List<DefectDTO> defectList = defectCheckItemRepository.findByCheckItemId(checkItem.getId());
                Optional.ofNullable(defectList).ifPresent(checkItem::setDefects);
                return checkItem;
            }).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setCheckItems(checkItems);
        }
        rworkerQualityInspectionPlanDto.setSnInfoList(CollectionUtils.isEmpty(processResult.getFullInspectInfo().getSnList()) ? new ArrayList<>() : processResult.getFullInspectInfo().getSnList());
        //添加不良项目
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, checkHistory.getStep());
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(workSheet.getPedigree(), workFlow.getId(), checkHistory.getStep().getId(), workSheet.getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            //批量管控 添加非维修分析处理方式不良项目  单支 添加非在线返修处理方式不良项目
            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = ObjectUtils.isEmpty(rworkerQualityInspectionPlanDto.getSnInfoList()) ?
                    unqualifiedItems.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName())
                            .map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList()) :
                    unqualifiedItems.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName())
                            .map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setUnqualifiedItems(unqualifiedItemDtos);
        }
        return rworkerQualityInspectionPlanDto;
    }

    /**
     * 根据工序检验历史ID获取与检验历史条件一致的指定抽检方案的质检方案列表
     *
     * @param sampleCaseCategory 检验方案类型
     * @param id                 检验历史ID
     * @return List<PedigreeStepCheckRule>
     */
    public List<PedigreeStepCheckRule> findStepSpecifiedCheckRule(Long id, int sampleCaseCategory) {
        CheckHistory checkHistory = checkHistoryRepository.getReferenceById(id);
        SubWorkSheet subWorkSheet = Objects.nonNull(checkHistory.getSubWorkSheet()) ? checkHistory.getSubWorkSheet() : null;
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Pedigree pedigree = workSheet.getPedigree();
        Step step = checkHistory.getStep();
        int category = checkHistory.getCategory();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(workSheet.getPedigree());
        //获取定制工序中工艺路线
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        // 查出规则集合
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleList = pedigreeStepCheckRuleRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), ObjectUtils.isEmpty(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), workFlow.getId(), workSheet.getClientId(), Objects.nonNull(checkHistory.getWorkCell()) ? checkHistory.getWorkCell().getId() : null, category, checkHistory.getVarietyId(), Constants.LONG_ZERO);
        //过滤项目类型
        if (!ObjectUtils.isEmpty(checkHistory.getVarietyId()) && ValidateUtils.isValid(pedigreeStepCheckRuleList)) {
            pedigreeStepCheckRuleList = pedigreeStepCheckRuleList.stream().filter(pedigreeStepCheckRule -> !ObjectUtils.isEmpty(pedigreeStepCheckRule.getVarietyId()))
                    .filter(pedigreeStepCheckRule -> pedigreeStepCheckRule.getVarietyId().equals(checkHistory.getVarietyId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(pedigreeStepCheckRuleList)) {
            return new ArrayList<>();
        }
        pedigreeStepCheckRuleList = pedigreeStepCheckRuleList.stream().filter(pedigreeStepCheckRule -> pedigreeStepCheckRule.getSampleCase().getCategory() == sampleCaseCategory).toList();
        if (CollectionUtils.isEmpty(pedigreeStepCheckRuleList)) {
            return new ArrayList<>();
        }
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleListSort = commonService.getPedigreeStepCheckRuleListSort(pedigreeStepCheckRuleList);
        return pedigreeStepCheckRuleListSort;
    }


    /**
     * 检验历史数据导出
     *
     * @param checkHistoryList 检验历史列表
     * @param exportDTO        参数
     * @param response
     * @throws IOException
     */
    public void exporExcel(List<CheckHistory> checkHistoryList, ExportDTO exportDTO, HttpServletResponse response) throws IOException {
        List<CheckHistoryExportDTO> checkHistoryExportDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(checkHistoryList)) {
            checkHistoryList.forEach(checkHistory -> {
                CheckHistoryExportDTO checkHistoryExportDTO = new CheckHistoryExportDTO(checkHistory);
                List<CheckHistoryDetail> checkHistoryDetailList = checkHistoryDetailRepository.findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(checkHistory.getId(), Boolean.TRUE,Constants.LONG_ZERO);
                List<CheckHistoryExportDTO.CheckDetailInfo> checkDetailInfoList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(checkHistoryDetailList)) {
                    checkHistoryDetailList.stream().collect(Collectors.groupingBy(CheckHistoryDetail::getSn)).forEach((sn, detailList) -> {
                        CheckHistoryExportDTO.CheckDetailInfo checkDetailInfo = new CheckHistoryExportDTO.CheckDetailInfo();
                        checkDetailInfo.setSn(sn);
                        List<CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo> checkItemInfoList = new ArrayList<>();
                        detailList.forEach(detail -> {
                            CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo checkItemInfo = new CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo();
                            checkItemInfo.setCheckItemName(detail.getCheckItem().getName()).setCheckData(detail.getCheckData())
                                    .setResult(detail.getResult()).setDefectName(Objects.nonNull(detail.getDefect()) ? detail.getDefect().getName() : null);
                            List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(detail.getId());
                            if (!CollectionUtils.isEmpty(documentDTOList)) {
                                List<CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo.DefectImagesInfo> defectImages = new ArrayList<>();
                                for (DocumentDTO documentDTO : documentDTOList) {
                                    Path path = Paths.get(documentDTO.getPath());
                                    try {
                                        FileInputStream inStream = new FileInputStream(path.toFile());
                                        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
                                        byte[] buffer = new byte[10240];
                                        int len = 0;
                                        while ((len = inStream.read(buffer)) != -1) {
                                            outStream.write(buffer, 0, len);
                                        }
                                        inStream.close();
                                        CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo.DefectImagesInfo defectImagesInfo = new CheckHistoryExportDTO.CheckDetailInfo.CheckItemInfo.DefectImagesInfo();
                                        defectImagesInfo.setDefectImage(outStream.toByteArray());
                                        defectImages.add(defectImagesInfo);
                                    } catch (FileNotFoundException e) {
                                        throw new RuntimeException(e);
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                                checkItemInfo.setDefectImages(defectImages);
                            }
                            checkItemInfoList.add(checkItemInfo);
                        });
                        checkDetailInfo.setCheckItemInfos(checkItemInfoList);
                        checkDetailInfoList.add(checkDetailInfo);
                    });
                }
                checkHistoryExportDTO.setCheckDetailInfoList(checkDetailInfoList);
                checkHistoryExportDTOList.add(checkHistoryExportDTO);
            });
        }
        ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
        if (StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")) {
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
            exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
        }
        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls") ? ".xls" : ".xlsx";
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, CheckHistoryExportDTO.class, checkHistoryExportDTOList);
        String fileName = URLEncoder.encode(exportDTO.getExcelTitle() + prefix, StandardCharsets.UTF_8);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        response.setHeader("message", "export!");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.close();
    }

    /**
     * 不良项目处理类型为在线返修时的处理逻辑
     *
     * @param snWorkStatus sn生产状态
     */
    private void dealWithUnqualifiedSnWhenOnlineRework(SnWorkStatus snWorkStatus) {
        //若不良项目没有不良组别则sn生产状态改为报废
        if (null == snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup()) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        //若没有配置单支不良在线返工规则则也认为报废
        WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(snWorkStatus.getWorkSheet().getPedigree(), snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup().getId(), snWorkStatus.getWorkSheet().getClientId());
        if (null == reWorkFlow) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        snWorkStatus.setWorkFlow(reWorkFlow).setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus()).setReworkStartDate(null == snWorkStatus.getReworkStartDate() ? LocalDateTime.now() : snWorkStatus.getReworkStartDate());
    }

    /**
     * 回退验证质检历史是否存在未处理的抽检终检历史任务
     *
     * @param subWorkSheet 子工单信息
     * @param workSheet    工单信息
     * @param step         工序信息
     */
    public void validRollBackCheckHistory(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step) {

        long todoCheckHistoryNumber = Objects.nonNull(subWorkSheet) ?
                checkHistoryRepository.countBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(subWorkSheet.getId(),
                        step.getId(), Arrays.asList(WorkCellStartCheckEnum.LAST_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory()),
                        Constants.INT_ZERO, Constants.LONG_ZERO) :
                checkHistoryRepository.countByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(workSheet.getId(),
                        step.getId(), Arrays.asList(WorkCellStartCheckEnum.LAST_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory()),
                        Constants.INT_ZERO, Constants.LONG_ZERO);
        if (todoCheckHistoryNumber > Constants.LONG_ZERO) {
            throw new ResponseException("error.todoCheckHistoryExist", "存在待检历史任务处理，无法回退");
        }
    }

    /**
     * 检验历史绑定文件
     *
     * @param id         记录id
     * @param documentId 文件id
     */
    public void bindCheckDocument(Long id, Long documentId) {
        checkHistoryRepository.findByIdAndDeleted(id, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.checkHistoryNotExist", "质量检验历史不存在"));
        DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO();
        documentRelationDTO.setServiceName("RBASE").setModeName("checkHistory").setRecordId(id).setDocumentList(List.of(new DocumentRelationDTO.Document().setDocumentId(documentId)));
        rbaseDocumentProxy.relation(documentRelationDTO);
    }
}
