package net.airuima.rbase.util;

import net.airuima.rbase.constant.Constants;
import org.springframework.util.ObjectUtils;

import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 时间转换工具类
 *
 * <AUTHOR>
 * @date 2021/8/25
 */
public class DateUtils {
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    private static final int ONE_DAY_TIME = 86400;

    private DateUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "天");
        }
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        return sb.toString();
    }

    /**
     * 计算两个时间差保留两位小数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return double
     * <AUTHOR>
     */
    public static double LocalDateTimeHourMinus(LocalDateTime startTime, LocalDateTime endTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instantStart = startTime.atZone(zone).toInstant();
        Instant instantEnd = endTime.atZone(zone).toInstant();

        Date dateStart = Date.from(instantStart);
        Date dateEnd = Date.from(instantEnd);

        Calendar instance = Calendar.getInstance();
        instance.setTime(dateStart);
        long timeInMillis1 = instance.getTimeInMillis();
        Calendar instance2 = Calendar.getInstance();
        instance2.setTime(dateEnd);
        long timeInMillis2 = instance2.getTimeInMillis();

        double hours = (timeInMillis2 - timeInMillis1) / 1000 / 60 / 60.00;
        DecimalFormat df = new DecimalFormat("##.##");
        String dff = df.format(hours);
        return Double.parseDouble(dff);
    }

    /**
     * 计算两个时间差保留两位小数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return double
     * <AUTHOR>
     */
    public static long LocalDateTimeSeconds(LocalDateTime startTime, LocalDateTime endTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instantStart = startTime.atZone(zone).toInstant();
        Instant instantEnd = endTime.atZone(zone).toInstant();

        Date dateStart = Date.from(instantStart);
        Date dateEnd = Date.from(instantEnd);

        Calendar instance = Calendar.getInstance();
        instance.setTime(dateStart);
        long timeInMillis1 = instance.getTimeInMillis();
        Calendar instance2 = Calendar.getInstance();
        instance2.setTime(dateEnd);
        long timeInMillis2 = instance2.getTimeInMillis();

        return Long.parseLong(String.valueOf(timeInMillis2 - timeInMillis1)) / 1000;
    }

    /**
     * 秒转换时分秒
     *
     * @param seconds 秒
     * @return : java.lang.String
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public static String minutesToHMS(int seconds) {
        int minutes = 60;
        int hours = 60 * 60;
        int convertHours = seconds / hours;
        int convertMinutese = seconds % hours / minutes;
        int convertSeconds = seconds % hours % minutes % minutes;
        return timeFormat(convertHours, convertMinutese, convertSeconds);
    }

    /**
     * 格式化时间
     *
     * @param params
     * @return
     */
    public static String timeFormat(int... params) {
        StringBuilder sbd = new StringBuilder();
        for (int i = 0, length = params.length; i < length; i++) {
            if (params[i] < 10) {
                sbd.append("0").append(params[i]).append(":");
            } else {
                sbd.append(params[i]).append(":");
            }
        }
        return sbd.deleteCharAt(sbd.length() - 1).toString();
    }

    /**
     * 返回指定时间 向后推迟 numbers * unit 的时间
     *
     * @param start   开始时间
     * @param numbers 推迟数量
     * @param unit    推迟单位 0:小时，1:天，2:周，3:月，4:年
     * @return 最终时间
     */
    public static LocalDateTime delayDate(LocalDateTime start, int numbers, int unit) {
        int unitCalendar = 0;
        if (Constants.INT_ZERO == unit) {
            unitCalendar = Calendar.HOUR;
        } else if (Constants.INT_ONE == unit) {
            unitCalendar = Calendar.DATE;
        } else if (Constants.INT_TWO == unit) {
            unitCalendar = Calendar.WEEK_OF_YEAR;
        } else if (Constants.INT_THREE == unit) {
            unitCalendar = Calendar.MONTH;
        } else if (Constants.INT_FOUR == unit) {
            unitCalendar = Calendar.YEAR;
        }

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(start.atZone(ZoneId.systemDefault()).toInstant()));
        c.add(unitCalendar, numbers);
        return c.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime 转 字符串，指定日期格式
     *
     * @param localDateTime 日期
     * @param pattern       日期格式
     * @return : String
     * <AUTHOR>
     * @date 2022/7/15
     **/
    public static String format(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        String timeStr = formatter.format(localDateTime.atZone(DEFAULT_ZONE_ID));
        return timeStr;
    }

    /**
     * 获取一天的工作总时长秒数
     *
     * @param begin 开始时间
     * @param end   结束时间
     * @return 返回当天工作时间秒数
     */
    public static int getWorkTime(LocalTime begin, LocalTime end) {
        if (ObjectUtils.isEmpty(begin)) {
            begin = LocalTime.of(0, 0, 0);
        }
        if (ObjectUtils.isEmpty(end)) {
            end = LocalTime.of(23, 59, 59);
        }

        if (begin.isAfter(end)) {
            // 时间隔日了
            return (ONE_DAY_TIME - begin.toSecondOfDay() + end.toSecondOfDay());
        } else {
            // 时间还是当日时间
            return (end.toSecondOfDay() - begin.toSecondOfDay());
        }

    }

    /**
     * 判断指定的时间范围和目标时间范围是否重合
     *
     * @param begin       指定时间开始
     * @param end         指定时间结束
     * @param targetBegin 目标时间开始
     * @param targetEnd   目标时间结束
     * @return 重合返回true，不重合返回false
     */
    public static boolean predicateTimeRepeat(LocalTime begin, LocalTime end, LocalTime targetBegin, LocalTime targetEnd) {
        if (begin.isAfter(end) && targetBegin.isAfter(targetEnd)) {
            return true;
        } else if (begin.isAfter(end) && targetBegin.isBefore(targetEnd)) {
            if (end.isBefore(targetBegin) && begin.isAfter(targetEnd)) {
                return false;
            } else {
                return true;
            }
        } else if (begin.isBefore(end) && targetBegin.isAfter(targetEnd)) {
            if (end.isBefore(targetBegin) && begin.isAfter(targetEnd)) {
                return false;
            } else {
                return true;
            }
        } else {
            if (targetEnd.isBefore(begin)) {
                return false;
            } else if (end.isBefore(targetBegin)) {
                return false;
            } else if (begin.isAfter(targetBegin) && begin.isBefore(targetEnd)) {
                return true;
            } else if (begin.isBefore(targetBegin) && end.isAfter(targetEnd)) {
                return true;
            } else if (end.isAfter(targetBegin) && end.isBefore(targetEnd)) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 判断时间是否在指定的工作时间范围内(包含开始和结束时间)
     *
     * @param workTimeStart 每日工作开始时间
     * @param workTimeEnd   每日工作结束时间
     * @param start         开始时间
     * @param end           结束时间
     * @return 在指定的时间范围内则返回true，否则返回false
     */
    public static boolean predicateTimeInWorkTime(LocalTime workTimeStart, LocalTime workTimeEnd, LocalTime start, LocalTime end) {
        if (ObjectUtils.isEmpty(workTimeStart)) {
            workTimeStart = LocalTime.of(0, 0, 0);
        }
        if (ObjectUtils.isEmpty(workTimeEnd)) {
            workTimeEnd = LocalTime.of(23, 59, 59);
        }
        // 工作时间跨日
        if (workTimeStart.isAfter(workTimeEnd)) {
            // 判断时间跨日
            if (start.isAfter(end)) {
                if ((workTimeStart.isBefore(start) || workTimeStart.equals(start)) && (workTimeEnd.isAfter(end) || workTimeEnd.equals(end))) {
                    return true;
                } else {
                    return false;
                }
            } else {
                if ((workTimeStart.isBefore(start) || workTimeStart.equals(start)) || (workTimeEnd.isAfter(end) || workTimeEnd.equals(end))) {
                    return true;
                } else {
                    return false;
                }
            }
        } else {
            // 判断时间跨日
            if (start.isAfter(end)) {
                return false;
            } else {
                if ((workTimeStart.isBefore(start) || workTimeStart.equals(start)) && (workTimeEnd.isAfter(end) || workTimeEnd.equals(end))) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }

    /**
     * 计算两个多段时间之间的交叉时间
     *
     * @param sourceTimeList List<List<StartTime + EndTime>>
     * @param targetTimeList List<List<StartTime + EndTime>>
     * @return : Integer
     * <AUTHOR>
     * @date 2022/12/8
     **/
    public static Integer timeOfCrossingByWorkTimeList(List<List<LocalTime>> sourceTimeList, List<List<LocalTime>> targetTimeList) {
        Integer times = Constants.INT_ZERO;
        for (List<LocalTime> sourceTimeSubList : sourceTimeList) {
            LocalTime sourceStartTime = sourceTimeSubList.get(0);
            LocalTime sourceEndTime = sourceTimeSubList.get(1);
            for (List<LocalTime> targetTimeSubList : targetTimeList) {
                LocalTime targetStartTime = targetTimeSubList.get(0);
                LocalTime targetEndTime = targetTimeSubList.get(1);

                times += timeOfCrossingByWorkTimes(sourceStartTime, sourceEndTime, targetStartTime, targetEndTime);
            }
        }
        return times;
    }

    /**
     * 计算两段时间之间的交叉时间
     *
     * @param begin       原始开始时间
     * @param end         原始结束时间
     * @param targetBegin 目标开始时间
     * @param targetEnd   目标结束时间
     * @return : java.lang.Integer
     * <AUTHOR>
     * @date 2022/12/8
     **/
    public static Integer timeOfCrossingByWorkTimes(LocalTime begin, LocalTime end, LocalTime targetBegin, LocalTime targetEnd) {
        //如果原始时间跨天+目标时间跨天
        if (begin.isAfter(end) && targetBegin.isAfter(targetEnd)) {
            return getWorkTime((begin.isAfter(targetBegin) ? begin : targetBegin), (end.isAfter(targetEnd) ? targetEnd : end));
        } else if (begin.isBefore(end) && targetBegin.isBefore(targetEnd)) {
            //如果原始时间不跨天+目标时间不跨天
            //原始结束时间在目标开始时间之前,或者原始开始时间在目标结束时间之后,则不重合
            if (end.isBefore(targetBegin) || targetEnd.isBefore(begin)) {
                return Constants.INT_ZERO;
            }
            return getWorkTime((begin.isAfter(targetBegin) ? begin : targetBegin), (end.isAfter(targetEnd) ? targetEnd : end));
        } else if (begin.isAfter(end) && targetBegin.isBefore(targetEnd)) {
            //如果原始时间跨天+目标时间不跨天
            //原始开始在目标结束时间之后,则不重合
            if (begin.isAfter(targetEnd)) {
                return Constants.INT_ZERO;
            }
            return getWorkTime((begin.isAfter(targetBegin) ? begin : targetBegin), targetEnd);
        } else if (begin.isBefore(end) && targetBegin.isAfter(targetEnd)) {
            //如果原始时间不跨天+目标时间跨天
            //目标开始时间在原始结束时间之后,则不重合
            if (targetBegin.isAfter(end)) {
                return Constants.INT_ZERO;
            }
            return getWorkTime((begin.isAfter(targetBegin) ? begin : targetBegin), end);
        }
        return null;
    }

    /**
     * LocalDateTime转LocalTime
     *
     * @param localDateTime
     * @return : java.time.LocalTime
     * <AUTHOR>
     * @date 2022/12/8
     **/
    public static LocalTime localDateTimeToLocalTime(LocalDateTime localDateTime) {
        LocalTime createTime = LocalTime.of(localDateTime.getHour(), localDateTime.getMinute(), localDateTime.getSecond());
        return createTime;
    }

    /**
     * 判断now是否在start和end之间
     *
     * @param now
     * @param startAndEndList
     * @return : java.lang.Boolean
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public static Boolean localTimeBetween(LocalTime now, List<List<LocalTime>> startAndEndList) {
        return startAndEndList.stream().anyMatch(i -> {
            LocalTime start = i.get(0);
            LocalTime end = i.get(1);
            //未跨天
            if (start.isBefore(end)) {
                return (start.isBefore(now) || start.equals(now)) && (now.isBefore(end) || now.equals(end));
            } else if (start.isAfter(end)) {
                return !((end.isBefore(now)) && (now.isBefore(start)));
            } else {
                return now.equals(start);
            }
        });
    }

    /**
     * 生产开始查询时间和结束查询时间之间所有日期
     *
     * @param startDateTime 开始查询时间
     * @param endDateTime   结束查询时间
     * @param pattern   格式样式
     * @return java.util.List<java.lang.String> 日期集合
     */
    public static List<String> getDatesBetween(LocalDateTime startDateTime, LocalDateTime endDateTime,String pattern) {
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        for (LocalDateTime date = startDateTime; !date.isAfter(endDateTime); date = date.plusDays(1)) {
            dates.add(date.format(formatter));
        }
        return dates;
    }
}
