package net.airuima.rbase.util;

import net.airuima.rbase.constant.Constants;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.util.Base64;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public class Base64Util {

    private Base64Util(){}

    /**
     * 根据文件地址获取对应的文件并将其转码为base64
     * @param filePath 文件路径
     * <AUTHOR>
     * @date  2021/12/16
     * @return java.lang.String
     */
    public static String transformBase64(String filePath){
        byte[] fileData = null;
        try ( FileInputStream fileInputStream = new FileInputStream(filePath) ){
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[Constants.INT_TWO_HUNDRED];
            int rc = Constants.INT_ZERO;
            while ((rc = fileInputStream.read(buff, Constants.INT_ZERO,Constants.INT_TWO_HUNDRED)) > 0){
                swapStream.write(buff, Constants.INT_ZERO, rc);
            }
            fileData = swapStream.toByteArray();
            return Base64.getEncoder().encodeToString(fileData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
