package net.airuima.rbase.util;

import cn.afterturn.easypoi.entity.vo.NormalExcelConstants;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import net.airuima.dto.ExportParamDTO;
import net.airuima.util.ResponseException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.airuima.util.ExcelUtil.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * Excel相关工具类
 *
 * <AUTHOR>
 * @date 2023/07/13
 */
public class ExcelUtils {

    private static final String SHEET_TITTLE = "title";
    /**
     * 解析excel文件数据
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < String, Object>> excel 行数据集合
     */
    public static List<Map<String, Object>> parseExcel(MultipartFile file) {
        //设置解析表头
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        List<Map<String, Object>> rowList;
        try {
            rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, importParams);
        } catch (Exception e) {
            throw new ResponseException("error.ParseExcelError", "解析excel失败");
        }
        //校验数据是否为空
        if (CollectionUtils.isEmpty(rowList)) {
            throw new ResponseException("error.ParseExcelEmptyError", "excel数据为空");
        }
        return rowList;
    }

    /**
     * 处理Excel数据
     * @param dataList 数据计划
     * @param exportParamDTOList 导出参数
     * @param result 结果
     */
    public static void dealExcelFilterData(List<?> dataList, List<ExportParamDTO> exportParamDTOList, List<Map<String, String>> result) {
        Map<String, List<ExportParamDTO>> stringListMap = (Map)exportParamDTOList.stream().collect(Collectors.groupingBy(ExportParamDTO::getFieldName));
        dataList.forEach((t) -> {
            try {
                Map<String, String> tMap = new LinkedHashMap();
                Field[] fields = getAllField(t);
                Field[] fieldArray = fields;
                int length = fields.length;
                for(int i = 0; i < length; ++i) {
                    Field field = fieldArray[i];
                    field.setAccessible(true);
                    ExcelEntity excelEntity = (ExcelEntity)field.getAnnotation(ExcelEntity.class);
                    // 是导出参数类包含的数据
                    if (stringListMap.get(field.getName()) != null) {
                        boolean isSplitFrame = isContainFrame(((ExportParamDTO) ((List) stringListMap.get(field.getName())).get(0)).getLabel());
                        String realLabel = dealLabel(((ExportParamDTO) ((List) stringListMap.get(field.getName())).get(0)).getLabel());
                        String realValue = dealFieldValue(((ExportParamDTO) ((List) stringListMap.get(field.getName())).get(0)).getLabel(), field.get(t) != null ? field.get(t).toString() : (isSplitFrame ? "0" : ""));
                        tMap.put(realLabel, realValue);
                    }
                    if(excelEntity != null){
                        // 导出数据为对象的属性情况
                        Map<String, List<ExportParamDTO>> excelEntityListMap = (Map) exportParamDTOList.stream().filter((exportParamDTO) -> {
                            return exportParamDTO.getFieldName().contains(".");
                        }).collect(Collectors.groupingBy(ExportParamDTO::getFieldName));
                        if (excelEntityListMap != null && !excelEntityListMap.isEmpty()) {
                            excelEntityListMap.forEach((column, exportParamDTOS) -> {
                                try {
                                    String[] columns = column.split("\\.");
                                    findChildObjectFieldValue(t, field, columns, 0, stringListMap, tMap);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            });
                        }
                    }
                }

                if (!tMap.isEmpty()) {
                    result.add(tMap);
                }
            } catch (Exception var15) {
                var15.printStackTrace();
            }

        });
    }


    /**
     * 组装sheet数据
     * @param sheetName  sheet名字
     * @param typeClazz 待导出数据class
     * @param data        待导出数据
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2023/10/27
     */
    public static Map<String, Object> createSheetParams(String sheetName,Class<?> typeClazz,Object data){
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(sheetName);
        Map<String, Object> exportMap = new HashMap<>();
        exportMap.put(SHEET_TITTLE, exportParams);
        exportMap.put(NormalExcelConstants.CLASS, typeClazz);
        exportMap.put(NormalExcelConstants.DATA_LIST, data);
        return exportMap;
    }

}
