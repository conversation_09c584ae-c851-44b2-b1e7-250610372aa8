package net.airuima.rbase.util;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 浮点型数据操作工具类
 *
 * <AUTHOR>
 * @date 2021-01-18
 */
public class NumberUtils {
    private NumberUtils() {

    }

    /**
     * int型数值和double型数值相加
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal add(int number1, double number2) {
        BigDecimal b1 = new BigDecimal(Integer.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.add(b2);
    }

    /**
     * 两个double型数值相加
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal add(double number1, double number2) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.add(b2);
    }

    /**
     * int型数值减去double型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal subtract(int number1, double number2) {
        BigDecimal b1 = new BigDecimal(Integer.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.subtract(b2);
    }

    /**
     * String型数值减去String型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal subtract(String number1, String number2) {
        BigDecimal b1 = new BigDecimal(number1);
        BigDecimal b2 = new BigDecimal(number2);
        return b1.subtract(b2);
    }

    /**
     * double型数值减去int型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal subtract(double number1, int number2) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Integer.toString(number2));
        return b1.subtract(b2);
    }

    /**
     * double型数值减去double型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal subtract(double number1, double number2) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.subtract(b2);
    }

    /**
     * int型数值乘以double型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal multiply(int number1, double number2) {
        BigDecimal b1 = new BigDecimal(Integer.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.multiply(b2);
    }

    /**
     * double型数值乘以int型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal multiply(double number1, int number2) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Integer.toString(number2));
        return b1.multiply(b2);
    }

    /**
     * double型数值乘以double型数值
     *
     * @param number1
     * @param number2
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal multiply(double number1, double number2) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.multiply(b2);
    }

    /**
     * int型数值除以int型数值,并保留一定位数
     *
     * @param number1
     * @param number2
     * @param precision
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal divide(int number1, int number2, int precision) {
        BigDecimal b1 = new BigDecimal(Integer.toString(number1));
        BigDecimal b2 = new BigDecimal(Integer.toString(number2));
        return b1.divide(b2, precision, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * String型数值除以String型数值,并保留一定位数
     *
     * @param number1
     * @param number2
     * @param precision
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal divide(String number1, String number2, int precision) {
        BigDecimal b1 = new BigDecimal(number1);
        BigDecimal b2 = new BigDecimal(number2);
        return b1.divide(b2, precision, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * int型数值除以double型数值,并保留一定位数
     *
     * @param number1
     * @param number2
     * @param precision
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal divide(int number1, double number2, int precision) {
        BigDecimal b1 = new BigDecimal(Integer.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.divide(b2, precision, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * double型数值除以int型数值,并保留一定位数
     *
     * @param number1
     * @param number2
     * @param precision
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal divide(double number1, int number2, int precision) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Integer.toString(number2));
        return b1.divide(b2, precision, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * double型数值除以double型数值,并保留一定位数
     *
     * @param number1
     * @param number2
     * @param precision
     * @return BigDecimal
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static BigDecimal divide(double number1, double number2, int precision) {
        BigDecimal b1 = new BigDecimal(Double.toString(number1));
        BigDecimal b2 = new BigDecimal(Double.toString(number2));
        return b1.divide(b2, precision, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 对double类型的数值保留指定位数的小数。
     * 该方法舍入模式：向“最接近的”数字舍入，如果与两个相邻数字的距离相等，则为向上舍入的舍入模式。
     * 注意：如果精度要求比较精确请使用 keepPrecision(String number, int precision)方法
     *
     * @param number
     * @param precision
     * @return double
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static double keepPrecision(double number, int precision) {
        BigDecimal bg = BigDecimal.valueOf(number);
        return bg.setScale(precision, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 格式化为指定位小数的数字,返回未使用科学计数法表示的具有指定位数的字符串。
     * 该方法舍入模式：向“最接近的”数字舍入，如果与两个相邻数字的距离相等，则为向上舍入的舍入模式。
     *
     * @param number
     * @param precision
     * @return String
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static String keepPrecision(String number, int precision) {
        BigDecimal bg = new BigDecimal(number);
        return bg.setScale(precision, BigDecimal.ROUND_HALF_UP).toPlainString();
    }

    /**
     * 淾平均值
     *
     * @param bigDecimalList 数值集合
     * @return BigDecimal
     */
    public static BigDecimal avg(List<BigDecimal> bigDecimalList, int scale) {
        BigDecimal addAll = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : bigDecimalList) {
            addAll = add(addAll.doubleValue(), bigDecimal.doubleValue());
        }
        return divide(addAll.doubleValue(), bigDecimalList.size(), scale);
    }

    /**
     * 比较大小
     *
     * @param v1 被比较数
     * @param v2 比较数
     * @return 如果v1 大于v2 则 返回true 否则false
     */
    public static boolean compare(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        int bj = b1.compareTo(b2);
        boolean res;
        if (bj > 0)
            res = true;
        else
            res = false;
        return res;
    }

    /**
     * 转换百分比
     * @param number 待转换数量
     * @param precision 保留几位小数
     * @return String
     */
    public static String percentNumber(BigDecimal number,int precision){
        NumberFormat nt = NumberFormat.getPercentInstance();
        nt.setMinimumFractionDigits(precision);
        return nt.format(number.doubleValue());
    }

    /**
     * 转换百分比
     * @param number 待转换数量
     * @param precision 保留几位小数
     * @return String
     */
    public static String percentNumber(double number,int precision){
        NumberFormat nt = NumberFormat.getPercentInstance();
        nt.setMinimumFractionDigits(precision);
        return nt.format(number);
    }
}
