package net.airuima.rbase.util;

import net.airuima.util.ResponseData;
import org.springframework.http.ResponseEntity;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * feign结果处理utils
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
public class ResponseDataUtils {

    /**
     * 获取feign响应数据
     *
     * @param responseEntity feign响应
     */
    public static <T> T get(ResponseEntity<ResponseData<T>> responseEntity) {
        if (responseEntity != null && responseEntity.getBody() != null) {
            ResponseData<T> responseData = responseEntity.getBody();
            if (responseData != null && responseData.getData() != null) {
                return responseData.getData();
            }
        }
        return null;
    }
}
