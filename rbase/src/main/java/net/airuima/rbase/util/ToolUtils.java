package net.airuima.rbase.util;

import net.airuima.constant.Constants;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
public class ToolUtils {
    private ToolUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 验证开闭区间本身是否合规
     * @param range
     * @return
     */
    public static boolean validateQualifiedRange(String range){
        String leftChar = range.split(",")[0].trim();
        String rightChar = range.split(",")[1].trim();
        double leftNumber = 0 ;
        double rightNumber = 0;
        boolean leftNumberIsNull = Boolean.FALSE;
        boolean rightNumberIsNull = Boolean.FALSE;
        if (leftChar.contains("(") && leftChar.length() > 1) {
            leftNumber = Double.parseDouble(leftChar.substring(leftChar.indexOf("(") + 1));
        } else if (leftChar.contains("[") && leftChar.length() > 1) {
            leftNumber = Double.parseDouble(leftChar.substring(leftChar.indexOf("[") + 1));
        }else {
            leftNumberIsNull = Boolean.TRUE;
        }
        if (rightChar.contains(")") && rightChar.length() > 1) {
            rightNumber = Double.parseDouble(rightChar.substring(0, rightChar.indexOf(")")));
        } else if (rightChar.contains("]") && rightChar.length() > 1) {
            rightNumber = Double.parseDouble(rightChar.substring(0, rightChar.indexOf("]")));
        }else {
            rightNumberIsNull= Boolean.TRUE;
        }
        if(leftNumberIsNull && rightNumberIsNull){
            return Boolean.FALSE;
        }
        if(leftNumberIsNull || rightNumberIsNull){
            return Boolean.TRUE;
        }
        return rightNumber>=leftNumber;
    }

    /**
     * 判断目标值是否在区间内
     *
     * @param range   区间范围 如[num1,num2)。 "["包含端点值, ")"不包含端点值
     * @param compare 目标值
     * @return boolean
     */
    public static boolean compareInterval(String range, String compare) {
        boolean left = true;
        boolean right = true;
        String leftChar = range.split(",")[0].trim();
        String rightChar = range.split(",")[1].trim();
        if (leftChar.contains("(") && leftChar.length() > 1) {
            double leftNum = Double.parseDouble(leftChar.substring(leftChar.indexOf("(") + 1));
            if (leftNum >= Double.parseDouble(compare)) {
                left = false;
            }
        } else if (leftChar.contains("[") && leftChar.length() > 1) {
            double leftNum = Double.parseDouble(leftChar.substring(leftChar.indexOf("[") + 1));
            if (leftNum > Double.parseDouble(compare)) {
                left = false;
            }
        }
        if (left) {
            if (rightChar.contains(")") && rightChar.length() > 1) {
                double rightNum = Double.parseDouble(rightChar.substring(0, rightChar.indexOf(")")));
                if (rightNum <= Double.parseDouble(compare)) {
                    right = false;
                }
            } else if (rightChar.contains("]") && rightChar.length() > 1) {
                double rightNum = Double.parseDouble(rightChar.substring(0, rightChar.indexOf("]")));
                if (rightNum < Double.parseDouble(compare)) {
                    right = false;
                }
            }
        } else {
            right = false;
        }
        return left && right;
    }

    /**
     * 带系数判断目标值是否在区间内
     *
     * @param range   区间范围 如[num1,num2)。 "["包含端点值, ")"不包含端点值
     * @param compare 目标值
     * @param coefficient 系数
     * @return boolean
     */
    public static boolean compareIntervalWithUnit(String range, String compare,int coefficient) {
        boolean left = true;
        boolean right = true;
        String leftChar = range.split(",")[0].trim();
        String rightChar = range.split(",")[1].trim();
        if (leftChar.contains("(") && leftChar.length() > 1) {
            double leftNum = Double.parseDouble(leftChar.substring(leftChar.indexOf("(") + 1))*coefficient;
            if (leftNum >= Double.parseDouble(compare)) {
                left = false;
            }
        } else if (leftChar.contains("[") && leftChar.length() > 1) {
            double leftNum = Double.parseDouble(leftChar.substring(leftChar.indexOf("[") + 1))*coefficient;
            if (leftNum > Double.parseDouble(compare)) {
                left = false;
            }
        }
        if (left) {
            if (rightChar.contains(")") && rightChar.length() > 1) {
                double rightNum = Double.parseDouble(rightChar.substring(0, rightChar.indexOf(")")))*coefficient;
                if (rightNum <= Double.parseDouble(compare)) {
                    right = false;
                }
            } else if (rightChar.contains("]") && rightChar.length() > 1) {
                double rightNum = Double.parseDouble(rightChar.substring(0, rightChar.indexOf("]")))*coefficient;
                if (rightNum < Double.parseDouble(compare)) {
                    right = false;
                }
            }
        } else {
            right = false;
        }
        return left && right;
    }

    public static String leftData(String range){
        String leftChar = range.split(",")[0];
        if (leftChar.contains("(") && leftChar.length() > 1) {
            return leftChar.substring(leftChar.indexOf("(") + 1);
        }
        if (leftChar.contains("[") && leftChar.length() > 1) {
            return leftChar.substring(leftChar.indexOf("[") + 1);
        }
        return null;
    }

    public static String rightData(String range){
        String rightChar = range.split(",")[1];
        if (rightChar.contains(")") && rightChar.length() > 1) {
            return rightChar.substring(0, rightChar.indexOf(")"));

        }
        if (rightChar.contains("]") && rightChar.length() > 1) {
            return rightChar.substring(0, rightChar.indexOf("]"));
        }
        return null;
    }

    /**
     * 产生新的32位uuid
     *
     * @return uuid
     */
    public static String generateUuId() {
        UUID uuid = UUID.randomUUID();
        String uniqueUuid = uuid.toString().toUpperCase().replace("-", "");
        return uniqueUuid;
    }


    /**
     * 通过周期单位及周期数值获取天数
     *
     * @param cycleUnit 周期单位(0:年;1:月;2:周;3:天)
     * @param period    周期数值
     * @return Long
     */
    public static Long getCycleDays(int cycleUnit, int period) {
        if (cycleUnit == Constants.INT_ZERO) {
            return Long.parseLong(String.valueOf(365 * period));
        }
        if (cycleUnit == Constants.INT_ONE) {
            return Long.parseLong(String.valueOf(30 * period));
        }
        if (cycleUnit == Constants.INT_TWO) {
            return Long.parseLong(String.valueOf(7 * period));
        }
        if (cycleUnit == Constants.INT_THREE) {
            return Long.parseLong(String.valueOf(period));
        }
        return Constants.LONG_ZERO;
    }

    /**
     * 校验区间格式的字符串是否有效。
     * 有效格式为: [a,b) 或 (a,b] 或 (a,b) 或 [a,b]
     *
     * @param intervalStr 要校验的区间字符串
     * @return 如果字符串是有效的区间格式，则返回 true，否则返回 false。
     */
    public static boolean isValidInterval(String intervalStr) {
        if (intervalStr == null || intervalStr.trim().isEmpty()) {
            return false;
        }
        // 正则表达式用于匹配区间格式
        String regex = "^\\s*([\\[(])\\s*(-?\\d*\\.?\\d*|\\+?\\d*\\.?\\d*)?\\s*,\\s*(-?\\d*\\.?\\d*|\\+?\\d*\\.?\\d*)?\\s*([\\])])\\s*$";
        if (!intervalStr.matches(regex)) {
            return false;
        }
        // 提取数字
        String[] parts = intervalStr.substring(1, intervalStr.length() - 1).split(",", -1);
        String startStr = parts.length > 0 ? parts[0].trim() : null;
        String endStr = parts.length > 1 ? parts[1].trim() : null;
        // 处理小端点
        Double start = null;
        if (startStr != null && !startStr.isEmpty()) {
            start = Double.parseDouble(startStr);
        }
        // 处理大端点
        Double end = null;
        if (endStr != null && !endStr.isEmpty()) {
            end = Double.parseDouble(endStr);
        }
        // 校验端点值 如果都是有限值，开始值应小于结束值
        if (start != null && end != null && start >= end) {
            return false;
        }
        //  "(,)"不允许
        if (start == null && end == null) {
            return false;
        }
        return true;
    }

    /**
     * 获取对象中为空的参数名
     *
     * @param source
     * @return : java.lang.String[]
     * <AUTHOR>
     * @date 2022/7/14
     **/
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }


    /**
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static  <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> concurrentHashMap = new ConcurrentHashMap<>();
        return t -> concurrentHashMap.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
