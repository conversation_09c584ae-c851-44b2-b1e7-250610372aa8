package net.airuima.rbase.util;

import java.util.regex.Pattern;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 验证通配符url路径与请求url是否匹配
 * <AUTHOR>
 * @date 2021-06-12
 */
public final class RegexpUtils {
    private RegexpUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     *
     * <AUTHOR>
     * @param patternRule 路由表达式
     * @param requestMappingUrl   requestMapping的url
     * @return boolean
     * @date 2021-06-12
     **/
    public static boolean isMatchUrl(String patternRule,String requestMappingUrl){
        String regPath = getRegPath(patternRule);
        return Pattern.compile(regPath).matcher(requestMappingUrl).matches();
    }

    /**
     *
     * <AUTHOR>
     * @param path 通配符组成的路径
     * @return String
     * @date 2021-06-12
     **/
    public static String getRegPath(String path) {
        char[] chars = path.toCharArray();
        int len = chars.length;
        StringBuilder sb = new StringBuilder();
        boolean preX = false;
        for (int i = 0; i < len; i++) {
            if (chars[i] == '*') {
                if (preX) {
                    sb.append(".*");
                    preX = false;
                } else if (i + 1 == len) {
                    sb.append("[^/]*");
                } else {
                    preX = true;
                }
            } else {
                if (preX) {
                    sb.append("[^/]*");
                    preX = false;
                }
                if (chars[i] == '?') {
                    sb.append('.');
                } else {
                    sb.append(chars[i]);
                }
            }
        }
        return sb.toString();
    }
}
