package net.airuima.rbase.repository.base.pedigree;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良现象Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeStepUnqualifiedItemRepository extends LogicDeleteableRepository<PedigreeStepUnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepUnqualifiedItem>, EntityGraphJpaRepository<PedigreeStepUnqualifiedItem, Long> {

    /**
     * 根据主键id和删除标记查询产品谱系工序不良项目
     * @param id 主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良项目
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where p.id = ?1 and p.deleted = ?2")
    Optional<PedigreeStepUnqualifiedItem> findByIdAndDeleted(Long id, Long deleted);



    /**
     * 根据产品谱系主键id工艺路线主键id工序主键id和客户主键id查询产品谱系工序不良现象
     *
     * @param pedigreeId 产品谱系工序主键Id
     * @param workFlowId 工艺路线主键ID
     * @param stepId     工序主键id
     * @param clientId 客户主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where p.pedigree.id = ?1 and p.workFlow.id = ?2 and p.step.id = ?3 and (p.clientId is null or p.clientId = ?4) and p.deleted = ?5")
    List<PedigreeStepUnqualifiedItem> findByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long clientId, Long deleted);



    /**
     * 根据产品谱系主键id集合 工艺路线 主键id 工序主键id 客户主键id 条件对象 启用标记 删除标记 返回匹配的工序不良配置
     * @param pedigreeIds 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param clientId 客户主键id
     * @param target 条件对象
     * @param enable 启用标记
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where (p.pedigree is null or p.pedigree.id in (?1)) and ( p.workFlow is null or p.workFlow.id = ?2) and ( p.step is null or p.step.id = ?3) and ( p.clientId is null or p.clientId = ?4) and p.priorityElementConfig.target = ?5 and p.isEnable = ?6 and p.deleted = ?7")
    List<PedigreeStepUnqualifiedItem> findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndPriorityElementConfigTargetAndIsEnableAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId, Long clientId, Integer target, boolean enable, Long deleted);


    /**
     * 查找适用于所有产品谱系和所有工艺路线的工序不良项目配置
     * @param stepId 工序主键id
     * @param isEnable 是否启用
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where p.pedigree is null and p.workFlow is null and p.step.id = ?1 and p.clientId is null and p.isEnable = ?2 and p.deleted = ?3")
    List<PedigreeStepUnqualifiedItem> findByPedigreeIdNullAndWorkFlowIdNullAndStepIdAndClientIdNullAndIsEnableAndDeleted(Long stepId, boolean isEnable, Long deleted);

    /**
     * 根据主键id和删除标识删除
     * @param pedigreeStepUnqualifiedItemId 工序不良配置ID
     */
    @Modifying
    @Query("UPDATE PedigreeStepUnqualifiedItem psui SET psui.deleted=psui.id WHERE psui.id=?1 AND psui.deleted=0")
    void deleteByPedigreeStepUnqualifiedItemIdAndDeleted(Long pedigreeStepUnqualifiedItemId);

    /***
     * 根据优先级主键id查找工序不良配置
     * @param priorityId 优先级主键id
     * @param deleted 删除标记
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem 产品谱系工序不良
     */
    @DataFilter(isSkip = true)
    PedigreeStepUnqualifiedItem findTop1ByPriorityElementConfigIdAndDeleted(Long priorityId, Long deleted);

    /**
     * 根据产品谱系主键id集合和工艺路线主键id和工序主键id和是否启用和删除标记查询 产品谱系工序不良列表列表
     * @param pedigreeIds 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param clientId 客户主键id
     * @param isEnable 是否启用
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良列表列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p " +
            "where (p.pedigree.id is null or p.pedigree.id in (?1)) " +
            "and ( p.workFlow.id is null or p.workFlow.id = ?2) " +
            "and (p.step.id is null or p.step.id = ?3) " +
            "and ( p.clientId is null or p.clientId = ?4) " +
            "and p.isEnable = ?5 " +
            "and p.deleted = ?6")
    List<PedigreeStepUnqualifiedItem> findByPedigreeIdInAndWorkFlowIdAndStepIdAndAndClientIdAndIsEnableAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId, Long clientId, boolean isEnable, Long deleted);


    /**
     * 获取产品谱系工序不良列表列表唯一记录
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param clientId 客户主键id
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良
     * <AUTHOR>
     * @date 2023/12/7
     */
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where ((?1 is null and  p.pedigree.id is null) or p.pedigree.id=?1) and ( (?2 is null and  p.workFlow.id is null) or p.workFlow.id = ?2) and ((?3 is null and  p.step.id is null) or p.step.id = ?3) and ( (?4 is null and  p.clientId is null) or p.clientId = ?4) and  ( (?5 is null and  p.unqualifiedItem.id is null) or p.unqualifiedItem.id = ?5) and p.deleted = ?6")
    Optional<PedigreeStepUnqualifiedItem> findByPedigreeIdAndWorkFlowIdAndStepIdAndAndClientIdAndUnqualifiedItemIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long clientId,Long unqualifiedItemId , Long deleted);

    /**
     * 查询产品谱系的工序不良项目配置
     * @param pedigreeId 产品谱系主键id
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem> 产品谱系工序不良列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepUnqualifiedItem p where p.pedigree.id = ?1 and p.deleted = ?2")
    List<PedigreeStepUnqualifiedItem> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);



}
