package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情设备Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SnWorkDetailFacilityRepository extends LogicDeleteableRepository<SnWorkDetailFacility>,
        EntityGraphJpaSpecificationExecutor<SnWorkDetailFacility>, EntityGraphJpaRepository<SnWorkDetailFacility, Long> {

    /**
     * 通过设备主键Id及日期获取详情数据 分页
     * <AUTHOR>
     * @param equipmentId 设备主键ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted  逻辑删除
     * @param pageable  分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility> 单支生产详情设备分页
     * @date 2021-03-24
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<SnWorkDetailFacility> findByFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long equipmentId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 根据SN工作详情ID删除设备信息
     * <AUTHOR>
     * @param snWorkDetailId    SN详情ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update SnWorkDetailFacility sdmb set sdmb.deleted=sdmb.id where sdmb.snWorkDetail.id=?1")
    void batchDeleteBySnWorkDetailId(Long snWorkDetailId);

    /**
     * 根据sn详情主键ID+删除标识，查询sn设备集合
     *
     * @param snWorkDetailId SN详情主键ID
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility> 单支生产详情设备列表
     * <AUTHOR>
     * @date 2023/6/9
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailFacility> findBySnWorkDetailIdAndDeleted(Long snWorkDetailId, Long deleted);

    /**
     *  根据 容器详情主键id 与设备主键 id 获取 容器绑定的 sn设备使用信息
     * @param containerDetailId 容器详情id
     * @param facilityId 设备 主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility> 单支生产详情设备列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailFacility> findBySnWorkDetailContainerDetailIdAndFacilityIdAndDeleted(Long containerDetailId, Long facilityId, Long deleted);

    /**
     * 根据 子工单主键id 工序主键id 设备主键id 获取sn 使用 设备详情列表
     * @param subWorkSheetId 子工单主键id
     * @param stepId 工序主键id
     * @param facilityId 设备主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility> 单支生产详情设备列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailFacility> findBySnWorkDetailSubWorkSheetIdAndSnWorkDetailStepIdAndFacilityIdAndDeleted(Long subWorkSheetId,Long stepId,Long facilityId,Long deleted);

    /**
     * 根据 工单id 工序id 设备主键id 获取sn 使用 设备详情列表
     * @param workSheetId 工单主键id
     * @param stepId 工序主键id
     * @param facilityId 设备主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailFacility> 单支生产详情设备列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailFacility> findBySnWorkDetailWorkSheetIdAndSnWorkDetailStepIdAndFacilityIdAndDeleted(Long workSheetId,Long stepId,Long facilityId,Long deleted);

}
