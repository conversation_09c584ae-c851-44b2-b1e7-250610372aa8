package net.airuima.rbase.repository.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 待检任务表Repository
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Repository
public interface InspectTaskRepository extends LogicDeleteableRepository<InspectTask>,
        EntityGraphJpaSpecificationExecutor<InspectTask>, EntityGraphJpaRepository<InspectTask, Long> {


    /**
     * 获取 待检任务
     *
     * @param status  是否处理
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务列表
     * <AUTHOR>
     * @date 2023/5/5
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<InspectTask> findByStatusAndDeleted(Boolean status, Long deleted);


    /**
     * 通过工位主键ID，检查类型,项目类型，是否处理获取 唯一待检任务
     *
     * @param workCellId 工位主键ID
     * @param category   检查类型
     * @param varietyId  项目类型
     * @param status     是否处理
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/5
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<InspectTask> findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(Long workCellId, Integer category, Long varietyId, Boolean status, Long deleted);

    /**
     * 通过 子工单主键ID + 工序主键ID + 检测项目主键ID + 处理状态 + 检测类型 + 容器编码 获取待检任务
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主主键ID
     * @param status         处理状态
     * @param category       检测类型
     * @param containerCode  容器编码
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/6
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<InspectTask> findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(Long subWorkSheetId, Long stepId, Boolean status, Integer category, String containerCode, Long deleted);

    /**
     * 通过 工单主键ID + 工序主键ID + 检测项目主键ID + 处理状态 + 检测类型 + 容器编码 获取待检任务
     *
     * @param workSheetId   子工单主键ID
     * @param stepId        工序主主键ID
     * @param status        处理状态
     * @param category      检测类型
     * @param containerCode 容器编码
     * @param deleted       逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/6
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<InspectTask> findByWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(Long workSheetId, Long stepId, Boolean status, Integer category, String containerCode, Long deleted);


    /**
     * 通过 子工单主键ID + 工序主键ID + 检测项目主键ID + 处理状态 + 检测类型 + sn 获取待检任务
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主主键ID
     * @param status         处理状态
     * @param category       检测类型
     * @param sn             sn
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/6
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("""
                    select itd.inspectTask from InspectTaskDetail itd where itd.inspectTask.subWorkSheet.id = ?1 and itd.inspectTask.step.id = ?2 and itd.inspectTask.status = ?3 and 
                    itd.inspectTask.category = ?4 and itd.sn = ?5 and itd.deleted = ?6 and itd.inspectTask.deleted = ?6
            """)
    InspectTask findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(Long subWorkSheetId, Long stepId, Boolean status, Integer category, String sn, Long deleted);


    /**
     * 通过 工单主键id + 工序主键id + 检测项目主键id + 处理状态 + 检测类型 + sn 获取待检任务
     *
     * @param workSheetId 工单主键id
     * @param stepId      工序主键id
     * @param status      处理状态
     * @param category    检测类型
     * @param sn          sn
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/6
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("""
                    select itd.inspectTask from InspectTaskDetail itd where itd.inspectTask.workSheet.id = ?1 and itd.inspectTask.step.id = ?2 and itd.inspectTask.status = ?3 and 
                    itd.inspectTask.category = ?4 and itd.sn = ?5 and itd.deleted = ?6 and itd.inspectTask.deleted = ?6
            """)
    InspectTask findByWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(Long workSheetId, Long stepId, Boolean status, Integer category, String sn, Long deleted);


    /**
     * 通过工位获取待做的指定类型的待检任务列表
     *
     * @param workCellId        工位id
     * @param status            处理状态
     * @param categories        检测类型
     * @param todoInspectedTime 待检时间
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务
     * <AUTHOR>
     * @date 2023/5/6
     */
    Long countByWorkCellIdAndStatusAndCategoryInAndTodoInspectedTimeLessThanEqualAndDeleted(Long workCellId, Boolean status, List<Integer> categories, LocalDateTime todoInspectedTime, Long deleted);


    /**
     * 子工单请求时获取待检 指定类型的待检任务
     *
     * @param subWorkSheetId 子工单id
     * @param status         待检状态
     * @param category       任务类型
     * @param deleted        逻辑删除
     * @return 待检任务个数
     */
    Long countBySubWorkSheetIdAndContainerCodeIsNullAndSnIsNullAndStatusAndCategoryInAndDeleted(Long subWorkSheetId, Boolean status, List<Integer> category, Long deleted);


    /**
     * 工单请求时获取待检 指定类型的待检任务
     *
     * @param workSheetId 工单id
     * @param status      待检状态
     * @param category    任务类型
     * @param deleted     逻辑删除
     * @return 待检任务个数
     */
    Long countByWorkSheetIdAndContainerCodeIsNullAndSnIsNullAndStatusAndCategoryInAndDeleted(Long workSheetId, Boolean status, List<Integer> category, Long deleted);


    /**
     * 通过子工单主键ID及工序主键ID删除所有任务
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主键ID
     */
    @Modifying
    @Query("delete from InspectTask where  subWorkSheet.id=?1 and step.id=?2")
    void deleteBySubWorkSheetIdAndStepId(Long subWorkSheetId, Long stepId);


    /**
     * 通过工单主键ID及工序主键ID删除所有任务
     *
     * @param workSheetId 工单主键ID
     * @param stepId      工序主键ID
     */
    @Modifying
    @Query("delete from InspectTask where  workSheet.id=?1 and step.id=?2")
    void deleteByWorkSheetIdAndStepId(Long workSheetId, Long stepId);

    /**
     * 通过子工单主键ID及工序主键ID、容器编码删除所有任务
     *
     * @param subWorkSheetId 子工单主键主键ID
     * @param stepId         工序主键主键ID
     * @param containerCode  容器编码
     */
    @Modifying
    @Query("delete from InspectTask where  subWorkSheet.id=?1 and step.id=?2 and containerCode=?3")
    void deleteBySubWorkSheetIdAndStepIdAndContainerCode(Long subWorkSheetId, Long stepId, String containerCode);

    /**
     * 通过工单主键ID及工序主键ID、容器编码删除所有任务
     *
     * @param workSheetId   工单主键ID
     * @param stepId        工序主键ID
     * @param containerCode 容器编码
     */
    @Modifying
    @Query("delete from InspectTask where  workSheet.id=?1 and step.id=?2 and containerCode=?3")
    void deleteByWorkSheetIdAndStepIdAndContainerCode(Long workSheetId, Long stepId, String containerCode);

    /**
     * 通过id 获取待检任务
     *
     * @param id      任务id
     * @param deleted 逻辑删除
     * @return Optional<InspectTask>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<InspectTask> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 获取子工单（不同类型） 未完成待检任务的个数
     *
     * @param subWsId     子工单id
     * @param status      是否检测完成
     * @param categoryIds 任务类型列表
     * @param deleted     逻辑删除
     * @return Long
     */
    Long countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(Long subWsId, Boolean status, List<Integer> categoryIds, Long deleted);

    /**
     * 获取工单（不同类型） 未完成待检任务的个数
     *
     * @param wsId        工单id
     * @param status      是否检测完成
     * @param categoryIds 任务类型列表
     * @param deleted     逻辑删除
     * @return Long
     */
    Long countByWorkSheetIdAndStatusAndCategoryInAndDeleted(Long wsId, Boolean status, List<Integer> categoryIds, Long deleted);

    /**
     * 更新缓存
     *
     * @param id    任务ID
     * @param cache 缓存
     */
    @Modifying
    @Query("update InspectTask set cache=?2 where id=?1")
    void updateInspectTaskCache(Long id, String cache);

    /**
     * 获取工单（不同类型） 工序 未完成待检任务的个数
     *
     * @param wsId        工单id
     * @param stepId      工序id
     * @param status      是否检测完成
     * @param categoryIds 任务类型列表
     * @param deleted     逻辑删除
     * @return Long
     */
    Long countByWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(Long wsId, Long stepId, Boolean status, List<Integer> categoryIds, Long deleted);

    /**
     * 获取子工单（不同类型） 工序 未完成待检任务的个数
     *
     * @param subWsId     子工单id
     * @param stepId      工序id
     * @param status      是否检测完成
     * @param categoryIds 任务类型列表
     * @param deleted     逻辑删除
     * @return Long
     */
    Long countBySubWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(Long subWsId, Long stepId, Boolean status, List<Integer> categoryIds, Long deleted);


    /**
     * 容器编码 任务类型及状态查询任务的个数
     *
     * @param containerCode 容器编码
     * @param status        状态
     * @param categoryIds   任务类型
     * @param deleted       逻辑删除
     * @return 容器编码 任务类型及状态查询任务的个数
     */
    Long countByContainerCodeAndStatusAndCategoryInAndDeleted(String containerCode, Boolean status, List<Integer> categoryIds, Long deleted);


    /**
     * 子工单 + 工序 + sns + 状态 + 类型 获取 待检任务个数
     *
     * @param subWsId     子工单id
     * @param stepId      工序
     * @param sns         sns
     * @param status      状态
     * @param categoryIds 类型
     * @param deleted     逻辑删除
     * @return 待检任务个数
     */
    @Query("""
                    select count(itd.id) from InspectTaskDetail itd where itd.inspectTask.subWorkSheet.id = ?1 and itd.inspectTask.step.id = ?2 and itd.inspectTask.status = ?4 and 
                    itd.inspectTask.category in ?5 and itd.sn in ?3 and itd.deleted = ?6 and itd.inspectTask.deleted = ?6
            """)
    Long countBySubWorkSheetIdAndStepIdAndSnInAndStatusAndCategoryInAndDeleted(Long subWsId, Long stepId, List<String> sns, Boolean status, List<Integer> categoryIds, Long deleted);


    /**
     * 工单 + 工序 + sns + 状态 + 类型 获取 待检任务个数
     *
     * @param wsId        工单id
     * @param stepId      工序
     * @param sns         sns
     * @param status      状态
     * @param categoryIds 类型
     * @param deleted     逻辑删除
     * @return 待检任务个数
     */
    @Query("""
                    select count(itd.id) from InspectTaskDetail itd where itd.inspectTask.workSheet.id = ?1 and itd.inspectTask.step.id = ?2 and itd.inspectTask.status = ?4 and 
                    itd.inspectTask.category in ?5 and itd.sn in ?3 and itd.deleted = ?6 and itd.inspectTask.deleted = ?6
            """)
    Long countByWorkSheetIdAndStepIdAndSnInAndStatusAndCategoryInAndDeleted(Long wsId, Long stepId, List<String> sns, Boolean status, List<Integer> categoryIds, Long deleted);


    /**
     * 通过工位主键ID、检查类型、是否处理获取 待检任务列表
     *
     * @param workCellId 工位主键ID
     * @param category   质检类型
     * @param status     是否处理
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.InspectTask> 待检任务列表
     */
    @FetchMethod
    List<InspectTask> findByWorkCellIdAndCategoryAndStatusAndDeleted(Long workCellId, Integer category, Boolean status, Long deleted);


    /**
     * 获取sn的待检任务
     *
     * @param sn          sn
     * @param status      待检状态
     * @param categoryIds 待检任务类型
     * @param deleted     逻辑删除
     * @return sn的待检任务个数
     */
    @Query("""
                    select count(itd.id) from InspectTaskDetail itd where  itd.inspectTask.status = ?2 and 
                    itd.inspectTask.category in ?3 and itd.sn = ?1 and itd.deleted = ?4 and itd.inspectTask.deleted = ?4
            """)
    Long countBySnAndStatusAndCategoryInAndDeleted(String sn, Boolean status, List<Integer> categoryIds, Long deleted);
}
