package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * 产品谱系工序间隔配置Repository
 *
 * <AUTHOR>
 * @date 2022/4/11 15:05
 */
public interface PedigreeStepIntervalConfigRepository extends LogicDeleteableRepository<PedigreeStepIntervalConfig>, EntityGraphJpaSpecificationExecutor<PedigreeStepIntervalConfig>, EntityGraphJpaRepository<PedigreeStepIntervalConfig, Long> {

    /**
     * 通过产品谱系主键ID、工序主键ID、工艺路线主键ID获取产品谱系工序间隔配置
     *
     * @param pedigreeId 产品谱系主键ID
     * @param stepId     工序主键ID
     * @param workFlowId 工艺路线主键ID
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig> 产品谱系工序间隔时长配置列表
     */
    @DataFilter(isSkip = true)
    List<PedigreeStepIntervalConfig> findByPedigreeIdAndStepIdAndWorkFlowIdAndDeleted(Long pedigreeId, Long stepId, Long workFlowId, Long deleted);

    /**
     * 通过产品谱系主键ID列表、工序主键ID、工艺路线主键ID获取产品谱系工序间隔配置列表
     *
     * @param pedigreeIds 产品谱系主键ID列表
     * @param stepId     工序主键ID
     * @param workFlowId 工艺路线主键ID
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig> 产品谱系工序间隔时长配置列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepIntervalConfigEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select p from PedigreeStepIntervalConfig p " +
            "where p.step.id = ?3 and (p.pedigree is null or p.pedigree.id in (?1)) " +
            "and (p.workFlow is null or p.workFlow.id = ?2) " +
            "and p.deleted = ?4")
    List<PedigreeStepIntervalConfig> findByPedigreeIdInAndWorkFlowIdAndStepIdAndDeleted(List<Long> pedigreeIds, Long workFlowId,Long stepId,  Long deleted);

    /**
     * 通过产品谱系、工艺路线、当前工序、前置工序、逻辑删除获取产品谱系工序间隔配置
     *
     * @param pedigreeId 产品谱系
     * @param workFlowId 工艺路线
     * @param stepId 当前工序
     * @param perStep 前置工序
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig> 产品谱系工序间隔配置
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeStepIntervalConfig p " +
            " where p.step.id = ?3 and p.preStep.id = ?4 and (case when ?1 is null then p.pedigree.id is null else p.pedigree.id = ?1 end) " +
            " and (case when ?2 is null then p.workFlow.id is null else p.workFlow.id = ?2 end) " +
            " and p.deleted = ?5")
    Optional<PedigreeStepIntervalConfig> findByPedigreeIdAndWorkFlowIdAndStepIdAndPreStepIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long perStep, Long deleted);

    /**
     * 通过产品谱系主键ID获取产品谱系工序间隔配置列表
     * @param pedigreeId 产品谱系主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig> 产品谱系工序间隔配置列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeStepIntervalConfig p where p.pedigree.id = ?1 and p.deleted = ?2")
    List<PedigreeStepIntervalConfig> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);


}
