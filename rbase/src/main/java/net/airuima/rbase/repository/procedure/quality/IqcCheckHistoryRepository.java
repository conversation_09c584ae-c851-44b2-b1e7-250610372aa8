package net.airuima.rbase.repository.procedure.quality;

import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验Repository
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Repository
public interface IqcCheckHistoryRepository extends LogicDeleteableRepository<IqcCheckHistory>,
        JpaSpecificationExecutor<IqcCheckHistory>, JpaRepository<IqcCheckHistory, Long> {

    /**
     * 根据编码和删除标记查询来料检验
     *
     * @param serialNumber 编码
     * @param deleted      删除标记
     * @return java.util.Optional<net.airuima.domain.base.quality.IqcCheckHistory> 来料检验
     */
    @Query("select i from IqcCheckHistory i where i.serialNumber = ?1 and i.deleted = ?2")
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<IqcCheckHistory> findBySerialNumberAndDeleted(String serialNumber, Long deleted);

    /**
     * 根据ID和删除标记查询来料检验
     *
     * @param id      来料检ID
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.domain.base.quality.IqcCheckHistory> 来料检验
     */
    @Query("select i from IqcCheckHistory i where i.id = ?1 and i.deleted = ?2")
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<IqcCheckHistory> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 根据状态和删除标记查询来料检验
     *
     * @param status  状态
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.base.quality.IqcCheckHistory> 来料检验列表
     */
    @Query("select i from IqcCheckHistory i where i.status = ?1 and i.deleted = ?2")
    @DataFilter(isSkip = true)
    @FetchMethod
    List<IqcCheckHistory> findByStatusAndDeleted(Integer status, Long deleted);

    /**
     * 更新缓存
     * @param id 主键
     * @param cache 缓存内容
     */
    @Modifying
    @Query("update IqcCheckHistory set cache=?2 where id=?1")
    void updateCache(Long id,String cache);

    /**
     * 根据物料id和删除标记查询来料检验
     *
     * @param materialId  物料id
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.base.quality.IqcCheckHistory> 来料检验列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<IqcCheckHistory> findByMaterialIdAndDeleted(Long materialId, Long deleted);
}
