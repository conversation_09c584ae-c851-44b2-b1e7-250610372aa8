package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.InspectTaskDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 检测任务详情记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public interface InspectTaskDetailRepository extends LogicDeleteableRepository<InspectTaskDetail>,
        EntityGraphJpaSpecificationExecutor<InspectTaskDetail>, EntityGraphJpaRepository<InspectTaskDetail, Long> {


    /**
     * 通过检测任务主键ID获取明细列表
     *
     * @param inspectTaskId 检测任务主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<String> 检测任务详情Sn列表
     */
    @Query("""
               select it.sn from InspectTaskDetail it where it.inspectTask.id = ?1 and it.deleted = ?2
            """)
    List<String> findSnListByInspectTaskIdAndDeleted(Long inspectTaskId, Long deleted);

}
