package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修单关联正常单Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WsReworkRepository extends LogicDeleteableRepository<WsRework>,
        EntityGraphJpaSpecificationExecutor<WsRework>, EntityGraphJpaRepository<WsRework, Long> {

    /**
     * 通过在线返修总工单主键ID获取与原始工单关联关系
     *
     * @param reworkWorkSheetId 在线返修单主键ID
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.aps.WsRework> 返工单关联正常单
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    Optional<WsRework> findByReworkWorkSheetIdAndDeleted(Long reworkWorkSheetId, Long deleted);

    /**
     *
     * 通过原始工单主键ID获取返工单列表
     * @param originWorkSheetId 原始工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.aps.WsRework> 返工单关联正常单列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsRework> findByOriginalWorkSheetIdAndDeleted(Long originWorkSheetId,Long deleted );

    /**
     * 通过原始工单主键id数组查找在线返修单记录
     *
     * @param idList  原始工单主键id数组
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.aps.WsRework> 返工单关联正常单列表
     */
    @DataFilter(isSkip = true)
    List<WsRework> findByOriginalWorkSheetIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     *
     * 通过原始工单主键ID与逻辑删除标志获取最新返工单记录
     * @param originWorkSheetId 原始工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.aps.WsRework> 返工单关联正常单
     */
    @DataFilter(isSkip = true)
    Optional<WsRework> findTop1ByOriginalWorkSheetIdAndDeletedOrderByIdDesc(Long originWorkSheetId,Long deleted);

}
