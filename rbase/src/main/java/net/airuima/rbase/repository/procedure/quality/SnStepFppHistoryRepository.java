package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.SnStepFppHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * 单支FPP历史Repository
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface SnStepFppHistoryRepository  extends LogicDeleteableRepository<SnStepFppHistory>,
        EntityGraphJpaSpecificationExecutor<SnStepFppHistory>, EntityGraphJpaRepository<SnStepFppHistory, Long> {
}
