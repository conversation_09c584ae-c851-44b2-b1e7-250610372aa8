package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良项目预警标准Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface UnqualifiedItemWarningStandardRepository extends LogicDeleteableRepository<UnqualifiedItemWarningStandard>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedItemWarningStandard>, EntityGraphJpaRepository<UnqualifiedItemWarningStandard, Long> {

    /**
     * 根据优先级配置id查询是否有关联的不良预警规则
     * <AUTHOR>
     * @date 2022/11/4 17:14
     * @param priorityElementConfigId 优先级配置id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard 不良项目预警标准
     */
    @DataFilter(isSkip = true)
    UnqualifiedItemWarningStandard findTop1ByPriorityElementConfigIdAndDeleted(Long priorityElementConfigId, Long deleted);


    /**
     * 返回匹配的生产良率预警标准集合，满足“预警条件”+“条件对象”，并根据优先级升序+产品谱系类型降序
     *
     * @param pedigreeIdList  产品谱系主键id
     * @param workSheetId 工单主键id
     * @param category    工单类型
     * @param stepGroupId 工序组主键id
     * @param stepId      工序主键id
     * @param workFlowId  工艺流程主键id
     * @param clientId    客户主键id
     * @param unqualifiedItemId    客户主键id
     * @param unqualifiedGroupId    客户主键id
     * @param target      条件对象
     * @param deleted      条件对象
     * @return java.util.List<net.airuima.rbase.domain.base.quality.StepWarningStandard> 不良项目预警标准列表
     * <AUTHOR>
     * @date 2022/10/18
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select a from UnqualifiedItemWarningStandard a  " +
            "where (a.pedigree is null or a.pedigree.id in (?1)) " +
            "and (a.workSheet is null or a.workSheet.id = ?2) " +
            "and (a.workSheetCategory is null or a.workSheetCategory = ?3) " +
            "and (a.stepGroup is null or a.stepGroup.id = ?4) " +
            "and (a.step is null or a.step.id = ?5) " +
            "and (a.workFlow is null or a.workFlow.id = ?6) " +
            "and (a.clientId is null or a.clientId = ?7) " +
            "and (a.unqualifiedItem is null or a.unqualifiedItem.id = ?8) " +
            "and (a.unqualifiedGroup is null or a.unqualifiedGroup.id = ?9) " +
            "and a.priorityElementConfig.target = ?10 and a.deleted = ?11" )
    List<UnqualifiedItemWarningStandard> findAllStandardByElementOrderByPriorityAndPedigree(List<Long> pedigreeIdList, Long workSheetId, Integer category, Long stepGroupId,
                                                                                 Long stepId, Long workFlowId, Long clientId, Long unqualifiedItemId, Long unqualifiedGroupId,
                                                                                 Integer target, Long deleted);
    /**
     * 根据产品谱系id,工序主键id,工艺路线主键id,工序组主键id,工单主键id,客户主键id,工单类型,不良种类主键id,不良项目主键id,查询不良预警规则
     * <AUTHOR>
     * @date 2022/11/1
     * @param pedigreeId 产品谱系主键id
     * @param stepId 工序主键id
     * @param workFlowId 工艺路线主键id
     * @param stepGroupId 工序组主键id
     * @param workSheetId 工单主键id
     * @param clientId 客户主键id
     * @param workSheetCategory 工单类型
     * @param unqualifiedGroupId 不良种类主键id
     * @param unqualifiedItemId 不良项目主键id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard 不良项目预警标准
     */
    @DataFilter(isSkip = true)
    @Query("select p from UnqualifiedItemWarningStandard p where ((coalesce(?1, null) is null and  p.pedigree.id is null) or p.pedigree.id=?1) " +
            "and ( (coalesce(?3, null) is null and  p.workFlow.id is null) or p.workFlow.id = ?3) " +
            "and ((coalesce(?2, null) is null and  p.step.id is null) or p.step.id = ?2) " +
            "and ( (coalesce(?4, null) is null and  p.stepGroup.id is null) or p.stepGroup.id = ?4) " +
            "and  ( (coalesce(?5, null) is null and  p.workSheet.id is null) or p.workSheet.id = ?5) " +
            "and  ( (coalesce(?6, null) is null and  p.clientId is null) or p.clientId = ?6) " +
            "and  ( (coalesce(?7, null) is null and  p.workSheetCategory is null) or p.workSheetCategory = ?7) " +
            "and  ( (coalesce(?8, null) is null and  p.unqualifiedGroup.id is null) or p.unqualifiedGroup.id = ?8)  " +
            "and  ( (coalesce(?9, null) is null and  p.unqualifiedItem.id is null) or p.unqualifiedItem.id = ?9)   and p.deleted = ?10")
    UnqualifiedItemWarningStandard findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndUnqualifiedGroupIdAndUnqualifiedItemIdAndDeleted(
            Long pedigreeId, Long stepId, Long workFlowId, Long stepGroupId, Long workSheetId, Long clientId, Integer workSheetCategory,Long unqualifiedGroupId,Long unqualifiedItemId, Long deleted);

    /**
     * 根据id查询不良预警规则
     * <AUTHOR>
     * @date 2022/11/1
     * @param id 不良预警主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard> 不良项目预警标准
     */
    @DataFilter(isSkip = true)
    Optional<UnqualifiedItemWarningStandard> findByIdAndDeleted(Long id, Long deleted);

}
