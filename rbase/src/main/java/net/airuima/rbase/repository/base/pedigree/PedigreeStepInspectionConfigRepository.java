package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepInspectionConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 工序检查配置Repository
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface PedigreeStepInspectionConfigRepository extends LogicDeleteableRepository<PedigreeStepInspectionConfig>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepInspectionConfig>, EntityGraphJpaRepository<PedigreeStepInspectionConfig, Long> {

    /**
     * 根据id和逻辑删除字段查询
     *
     * @param id      主键id
     * @param deleted 逻辑删除字段
     * @return PedigreeStepInspectionConfig 工序检查配置
     * <AUTHOR>
     * @since 1.8.1
     */
    Optional<PedigreeStepInspectionConfig> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 根据工序配置id和检查类型查询
     *
     * @param pedigreeStepId 工序配置id
     * @param inspectionType 检查类型
     * @param deleted        逻辑删除字段
     * @return PedigreeStepInspectionConfig 工序检查配置
     * <AUTHOR>
     * @since 1.8.1
     */
    Optional<PedigreeStepInspectionConfig> findByPedigreeStepIdAndInspectionTypeAndDeleted(Long pedigreeStepId, Integer inspectionType, Long deleted);

    /**
     * 物理删除工序配置id
     *
     * @param pedigreeStepId 工序配置id
     * <AUTHOR>
     * @since 1.8.1
     */
    @Modifying
    @Query("delete PedigreeStepInspectionConfig pi  where pi.pedigreeStep.id = ?1")
    void deleteByPedigreeStepId(Long pedigreeStepId);

    /**
     * 逻辑删除工序配置id
     *
     * @param pedigreeStepId 工序配置id
     * <AUTHOR>
     * @since 1.8.1
     */
    @Modifying
    @Query("update PedigreeStepInspectionConfig pi  set pi.deleted = pi.id where pi.pedigreeStep.id = ?1 and pi.deleted = 0")
    void logicDeleteByPedigreeStepId(Long pedigreeStepId);

    /**
     * 根据工序配置id查询
     *
     * @param pedigreeStepId 工序配置id
     * @param deleted        逻辑删除字段
     * @return List<PedigreeStepInspectionConfig> 工序检查配置
     * <AUTHOR>
     * @since 1.8.1
     */
    List<PedigreeStepInspectionConfig> findByPedigreeStepIdAndDeleted(Long pedigreeStepId, Long deleted);

    /**
     * 根据id 获取全部工序配置列表
     *
     * @param ids     ids
     * @param deleted 逻辑删除
     * @return List<PedigreeStepInspectionConfig> 工序检查配置
     * <AUTHOR>
     * @since 1.8.1
     */
    List<PedigreeStepInspectionConfig> findByIdInAndDeleted(List<Long> ids, Long deleted);
}
