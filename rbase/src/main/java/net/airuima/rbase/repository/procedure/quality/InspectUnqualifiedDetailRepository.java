package net.airuima.rbase.repository.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualifiedDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良管理记录详情表Repository
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Repository
public interface InspectUnqualifiedDetailRepository extends LogicDeleteableRepository<InspectUnqualifiedDetail>,
        EntityGraphJpaSpecificationExecutor<InspectUnqualifiedDetail>, EntityGraphJpaRepository<InspectUnqualifiedDetail, Long> {

    /**
     * 根据管理记录主键id、删除标识获取明细信息列表
     * @param inspectUnqualifiedId      不良管理记录主键id
     * @param deleted                   删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.InspectUnqualifiedDetail>详细列表信息列表
     */
    @DataFilter(isSkip = true)
    @Query("select iud from InspectUnqualifiedDetail iud where iud.inspectUnqualified.id = ?1 and iud.deleted = ?2 ")
    List<InspectUnqualifiedDetail> findByInspectUnqualifiedIdAndDeleted(Long inspectUnqualifiedId, Long deleted);
}
