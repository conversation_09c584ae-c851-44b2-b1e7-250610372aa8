package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 工位宽放历史
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface WorkCellExtendHistoryRepository extends LogicDeleteableRepository<WorkCellExtendHistory>,
        EntityGraphJpaSpecificationExecutor<WorkCellExtendHistory>, EntityGraphJpaRepository<WorkCellExtendHistory, Long> {

    /**
     * 根据工位主键ID和检测类型获取最新数据
     *
     * @param workCellId 工位主键ID
     * @param category   检测类型
     * @param varietyId  项目类型主键id
     * @param deleted    逻辑删除
     * @return 检测结果最新状态
     */
    Optional<WorkCellExtendHistory> findByWorkCellIdAndCategoryAndVarietyIdAndResultAndDeleted(Long workCellId, int category, Long varietyId, Boolean result, Long deleted);

    /**
     * 根据工位主键ID和检测类型获取最新数据
     *
     * @param workCellId 工位主键ID
     * @param result     是否已处理
     * @param deleted    逻辑删除
     * @return 检测结果最新状态
     */
    List<WorkCellExtendHistory> findByWorkCellIdAndResultAndDeleted(Long workCellId, Boolean result, Long deleted);

}
