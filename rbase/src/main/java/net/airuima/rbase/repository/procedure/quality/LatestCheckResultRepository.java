package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.LatestCheckResult;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测结果最新状态Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface LatestCheckResultRepository extends LogicDeleteableRepository<LatestCheckResult>,
        EntityGraphJpaSpecificationExecutor<LatestCheckResult>, EntityGraphJpaRepository<LatestCheckResult, Long> {

    /**
     * 根据工位主键ID和检测类型删除数据
     * <AUTHOR>
     * @param workCellId 工位主键ID
     * @param category   类型
     * @return void
     * @date 2021-03-23
     **/
    void deleteByWorkCellIdAndCategory(Long workCellId,Integer category);

    /**
     * 根据工位 + 检测类型 + 项目类型获取最新数据
     * <AUTHOR>
     * @param workCellId 工位主键ID
     * @param category 检测类型
     * @param variety 项目类型
     * @param deleted   逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.LatestCheckResult> 检测结果最新状态
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<LatestCheckResult> findByWorkCellIdAndCategoryAndVarietyAndDeleted(Long workCellId, Integer category,Integer variety, Long deleted);

    /**
     * 根据工位 + 检测类型 + 项目类型获取最新数据
     * <AUTHOR>
     * @param workCellId 工位主键ID
     * @param category 检测类型
     * @param varietyId 项目类型主键id
     * @param deleted   逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.LatestCheckResult> 检测结果最新状态
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<LatestCheckResult> findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(Long workCellId, Integer category,Long varietyId, Long deleted);

    /**
     * 根据工位 + 检测类型获取最新数据集合
     * <AUTHOR>
     * @date 2022/11/23 15:40
     * @param workCellId 工位主键ID
     * @param category 类型
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.LatestCheckResult> 检测结果最新状态列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<LatestCheckResult> findByWorkCellIdAndCategoryAndDeleted(Long workCellId, Integer category,Long deleted);

    /**
     * 根据主键id 获取最新检测记录信息
      * @param id 最新检测记录主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @Date  2023/5/8
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.LatestCheckResult> 检测结果最新状态
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<LatestCheckResult> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 获取即将到期的待检任务
     * @param compareDate 比较日期
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.LatestCheckResult> 检测结果最新状态列表
     * <AUTHOR>
     * @date 2023/12/12
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<LatestCheckResult> findByNextCheckDateNotNullAndNextCheckDateBeforeAndDeleted(LocalDateTime compareDate,Long deleted);
}
