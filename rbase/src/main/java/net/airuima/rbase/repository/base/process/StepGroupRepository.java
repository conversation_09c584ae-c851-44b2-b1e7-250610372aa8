package net.airuima.rbase.repository.base.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组别Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface StepGroupRepository extends LogicDeleteableRepository<StepGroup>,
        EntityGraphJpaSpecificationExecutor<StepGroup>, EntityGraphJpaRepository<StepGroup, Long> {
    /**
     * 根据编码查询工序组
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.StepGroup>  工序组
     * <AUTHOR>
     * @date 2022/10/27
     */
    Optional<StepGroup> findByCodeAndDeleted(String code, Long deleted);


    /**
     * 获取所有工序组别
     *
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.process.StepGroup>  工序组列表
     */
    List<StepGroup> findByDeleted(Long deleted);

    /**
     * 根据主键ID和删除标记查找工序组
     * @param Id 主键ID
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.StepGroup>  工序组
     */
    Optional<StepGroup> findByIdAndDeleted(Long Id,Long deleted);

    /**
     * 根据工序组名称或编码模糊查询工序组
     * <AUTHOR>
     * @date 2022/11/1
     * @param text 名称或编码
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<StepGroup> 工序组分页
     */
    @Query("select sg from StepGroup sg where (sg.name like concat('%',?1,'%') or sg.code like concat('%',?1,'%')) and sg.isEnable=true and sg.deleted=0L order by sg.createdDate desc")
    Page<StepGroup> findByNameOrCode(String text, Pageable pageable);


}
