package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialReturn;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工单工位退料Repository
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Repository
public interface WsWorkCellMaterialReturnRepository extends LogicDeleteableRepository<WsWorkCellMaterialReturn>,
        EntityGraphJpaSpecificationExecutor<WsWorkCellMaterialReturn>, EntityGraphJpaRepository<WsWorkCellMaterialReturn, Long> {
}
