package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序指标Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeStepSpecificationRepository extends LogicDeleteableRepository<PedigreeStepSpecification>, EntityGraphJpaSpecificationExecutor<PedigreeStepSpecification>, EntityGraphJpaRepository<PedigreeStepSpecification, Long> {

    /**
     * 通过产品谱系id查询产品谱系工序指标
     *
     * @param pedigreeId 产品谱系id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeStepSpecification> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);


    /**
     * 根据产品谱系主键ID和工艺路线主键ID和工序主键ID获取产品谱系工序指标
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线ID
     * @param stepId     工序id
     * @param deleted    逻辑删除字段
     * @return java.util.Optional<net.airuima.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepSpecificationEntityGraph", type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    Optional<PedigreeStepSpecification> findByPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long deleted);

    /**
     * 根据产品谱系主键ID列表和工艺路线主键ID和工序主键ID获取产品谱系工序指标
     *
     * @param pedigreeIds 产品谱系id列表
     * @param workFlowId  工艺路线ID
     * @param stepId      工序id
     * @param deleted     逻辑删除字段
     * @return java.util.List<net.airuima.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeStepSpecification p where  p.step.id = ?3 and p.deleted = ?5 and (p.pedigree.id is null or p.pedigree.id in(?1)) and (p.workFlow.id is null or p.workFlow.id = ?2)  and  (p.clientId is null or p.clientId=?4) ")
    List<PedigreeStepSpecification> findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId, Long clientId, Long deleted);


    /**
     * 根据主键ID和删除标识查询
     *
     * @param id      产品谱系工序指标主键ID
     * @param deleted 删除标识
     * @return net.airuima.domain.base.pedigree.PedigreeStepSpecification 产品谱系工序指标
     * <AUTHOR>
     * @date 2022/8/23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    PedigreeStepSpecification findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据产品谱系id和工艺路线id和工序id和客户id和删除标记查找谱系工序指标
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param stepId     工序id
     * @param clientId   客户id
     * @param deleted    删除标识
     * @return java.util.Optional<net.airuima.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标
     */
    @FetchMethod
    @Query("""
            select p from PedigreeStepSpecification p
            where p.step.id = ?3 and ((?1 is null and p.pedigree.id is null) or p.pedigree.id = ?1) and ((?2 is null and p.workFlow.id is null) or p.workFlow.id = ?2)  and  ((?4 is null and p.clientId is null) or (p.clientId = ?4))  and p.deleted = ?5""")
    Optional<PedigreeStepSpecification> findInfoByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long clientId, Long deleted);


    /**
     * 根据优先级配置id查询是否有关联产品谱系工序指标
     *
     * @param priorityElementConfigId 优先级配置id
     * @param deleted                 删除标记
     * @return net.airuima.domain.base.pedigree.PedigreeStepSpecification 产品谱系工序指标
     */
    @FetchMethod
    PedigreeStepSpecification findTop1ByPriorityElementConfigIdAndDeleted(Long priorityElementConfigId, Long deleted);
}
