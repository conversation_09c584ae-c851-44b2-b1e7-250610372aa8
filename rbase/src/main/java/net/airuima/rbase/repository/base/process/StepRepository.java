package net.airuima.rbase.repository.base.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工序Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface StepRepository extends LogicDeleteableRepository<Step>,
        EntityGraphJpaSpecificationExecutor<Step>, EntityGraphJpaRepository<Step, Long> {

    /**
     * 根据工序编码或者名称获取启用的工序列表
     *
     * @param text     编码或者名称
     * @param isEnable 是否启用
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.process.Step> 工序分页
     */
    @Query("select step from  Step  step where (" +
            "(coalesce(?1, null) is null or step.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or step.name like concat('%',?1,'%')))" +
            "and step.isEnable=?2 and step.deleted=0L")
    Page<Step> findByNameOrCode(String text, Boolean isEnable, Pageable pageable);


    /**
     * 根据工序编码或者名称，是否启用、类型列表获取启用的工序列表
     * @param text  编码或者名称
     * @param isEnable  是否启用
     * @param categoryList 工序类型列表
     * @param pageable     分页参数
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.process.Step> 工序分页
     * <AUTHOR>
     * @date 2023/10/13
     */
    @Query("select step from  Step  step where (" +
            "(coalesce(?1, null) is null or step.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or step.name like concat('%',?1,'%')))" +
            "and step.isEnable=?2 and step.category in(?3) and step.deleted=0L")
    Page<Step> findByNameOrCodeAndCategories(String text, Boolean isEnable, List<Integer> categoryList,Pageable pageable);

    /**
     * 通过工序主键id与删除字段查询工序
     *
     * @param id     工序主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.Step> 工序
     */
    Optional<Step> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过工序主键id字段查询工序
     *
     * @param id 工序主键id
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.Step> 工序
     * @throws
     * <AUTHOR>
     * @date 2021-01-11
     **/
    Optional<Step> findById(Long id);

    /**
     * 通过工序编码查询唯一工序
     *
     * @param code 工序编码
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.Step> 工序
     */
    @Query("from Step where code = ?1 and deleted = 0L")
    Optional<Step> findByCode(String code);

    /**
     * 通过工序主键ID集合获取工序信息
     *
     * @param stepIdList 工序主键ID集合
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    List<Step> findByIdInAndDeleted(List<Long> stepIdList, Long deleted);

    /**
     * 通过编码列表 获取工序信息
     * @param codes 工序编码列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/9
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    List<Step> findByCodeInAndDeleted(List<String> codes,Long deleted);


    /**
     * 通过编码删除工序
     * @param code 工序编码
     */
    @Modifying
    @Query("update Step set deleted = id where code = ?1 and deleted = 0")
    void deleteByCode(String code);


    /**
     * 通过工序编码和删除标记查询工序
     * @param code 工序编码
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.Step> 工序
     */
    @Query("select s from Step s where s.code = ?1 and s.deleted = ?2")
    Optional<Step> findByCodeAndDeleted(String code, Long deleted);



}
