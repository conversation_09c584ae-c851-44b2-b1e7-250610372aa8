package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序上料规则Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeStepMaterialRuleRepository extends LogicDeleteableRepository<PedigreeStepMaterialRule>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepMaterialRule>, EntityGraphJpaRepository<PedigreeStepMaterialRule, Long> {

    /**
     * 根据产品谱系主键id查询产品谱系工序上料规则列表
     *
     * @param pedigreeId 产品谱系主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule> 产品谱系工序上料规则列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeStepMaterialRule> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    /**
     * 通过产品谱系主键id和工序主键d查询产品谱系工序上料规则列表
     *
     * @param pedigreeIds 产品谱系主键ids
     * @param workFlowId  工艺路线主键ID
     * @param stepId      工序主键id
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule> 产品谱系工序上料规则列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepMaterialRuleEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select p from PedigreeStepMaterialRule p where (p.pedigree.id is null or p.pedigree.id in(?1)) " +
            "and  (p.workFlow.id is null or p.workFlow.id= ?2 ) " +
            "and (p.step.id is null or p.step.id=?3) " +
            "and (p.clientId is null or p.clientId = ?4)  and p.enable = true and p.deleted = ?5")
    List<PedigreeStepMaterialRule> findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId,Long clientId, Long deleted);


    /**
     * 通过产品谱系主键id和工序主键d查询产品谱系工序上料规则列表
     *
     * @param pedigreeIds 产品谱系主键ids
     * @param workFlowId  工艺路线主键ID
     * @param stepId      工序主键id
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.base.pedigree.PedigreeStepMaterialRule> 产品谱系工序上料规则列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepMaterialRuleEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select p from PedigreeStepMaterialRule p where ((coalesce(?1, null) is null and p.pedigree.id is null ) or p.pedigree.id = ?1) " +
            "and  ((coalesce(?2, null) is null and p.workFlow.id is null) or p.workFlow.id= ?2 ) " +
            "and ((coalesce(?3, null) is null and p.step.id is null) or p.step.id=?3) " +
            "and ((coalesce(?4, null) is null and p.clientId is null) or p.clientId = ?4)  and p.deleted = ?5")
    List<PedigreeStepMaterialRule> findAllByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId,Long clientId, Long deleted);


    /**
     * 根据产品谱系主键ID删除产品谱系工序上料规则
     * @param pedigreeId 产品谱系主键ID
     */
    @Modifying
    @Query("delete from PedigreeStepMaterialRule where pedigree.id=?1")
    void physicallyDeletedByPedigreeId(Long pedigreeId);

    /**
     * 通过主键id获取产品谱系工序上料规则
     *
     * @param id      产品谱系工序上料规则主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule> 产品谱系工序上料规则
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<PedigreeStepMaterialRule> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过客户谱系工艺路线工序物料 获取唯一记录
     *
     * @param clientId   客户代码主键id
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId     工序主键id
     * @param materialId 物料主键id
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule> 产品谱系工序上料规则
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStepMaterialRule p where ((coalesce(?2, null) is null and  p.pedigree is null) or p.pedigree.id=?2) and ( (coalesce(?3, null) is null and  p.workFlow is null) or p.workFlow.id = ?3) and ((coalesce(?4, null) is null and  p.step.id is null) or p.step.id = ?4) and (( coalesce(?1, null) is null and  p.clientId is null) or p.clientId = ?1) and  ( (coalesce(?5, null) is null and  p.materialId is null) or p.materialId = ?5) and p.deleted = ?6")
    Optional<PedigreeStepMaterialRule> findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndMaterialIdAndDeleted(Long clientId, Long pedigreeId, Long workFlowId, Long stepId, Long materialId, Long deleted);

    /**
     * 根据产品谱系工序上料规则主键ID删除上料规则
     * @param id 产品谱系工序上料规则主键ID
     */
    @Modifying
    @Query("update PedigreeStepMaterialRule set deleted=id where id=?1")
    void deleteById(Long id);

}
