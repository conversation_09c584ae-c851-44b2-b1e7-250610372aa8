package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.StepIntervalEvent;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序间隔异常Repository
 * <AUTHOR>
 */
@Repository
public interface StepIntervalEventRepository extends LogicDeleteableRepository<StepIntervalEvent>,
        EntityGraphJpaSpecificationExecutor<StepIntervalEvent>, EntityGraphJpaRepository<StepIntervalEvent, Long> {

    /**
     * 通过子工单ID，工序ID及逻辑删除获取异常列表
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId, Long stepId, Long deleted);

    /**
     * 通过工单ID，工序ID及逻辑删除获取异常列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId, Long stepId, Long deleted);


    /**
     * 通过子工单ID，工序ID、SN及逻辑删除获取异常列表
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param sn sn
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findBySubWorkSheetIdAndStepIdAndSnAndDeleted(Long subWorkSheetId, Long stepId,String sn, Long deleted);


    /**
     * 通过子工单ID，工序ID、SN列表及逻辑删除获取异常列表
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param sns sn列表
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findBySubWorkSheetIdAndStepIdAndSnInAndDeleted(Long subWorkSheetId, Long stepId,List<String> sns, Long deleted);


    /**
     * 通过工单ID，工序ID、SN及逻辑删除获取异常列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param sn sn
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findByWorkSheetIdAndStepIdAndSnAndDeleted(Long workSheetId, Long stepId,String sn, Long deleted);


    /**
     * 通过工单ID，工序ID、SN列表及逻辑删除获取异常列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param sns sn列表
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent> 异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findByWorkSheetIdAndStepIdAndSnInAndDeleted(Long workSheetId, Long stepId,List<String> sns, Long deleted);


    /**
     * 通过子工单ID，工序ID、容器编码及逻辑删除获取异常列表
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param containerId 容器ID
     * @param deleted      逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent>异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findBySubWorkSheetIdAndStepIdAndRequestContainerIdAndDeleted(Long subWorkSheetId, Long stepId,Long containerId, Long deleted);

    /**
     * 通过子工单ID，工序ID、容器ID列表及逻辑删除获取异常列表
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param containerIdList 容器ID列表
     * @param deleted      逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent>异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findBySubWorkSheetIdAndStepIdAndRequestContainerIdInAndDeleted(Long subWorkSheetId, Long stepId,List<Long> containerIdList, Long deleted);

    /**
     * 通过工单ID，工序ID、容器编码及逻辑删除获取异常列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param containerId 容器ID
     * @param deleted      逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent>异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findByWorkSheetIdAndStepIdAndRequestContainerIdAndDeleted(Long workSheetId, Long stepId,Long containerId, Long deleted);


    /**
     * 通过工单ID，工序ID、容器ID列表及逻辑删除获取异常列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param containerIdList 容器ID列表
     * @param deleted      逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.StepIntervalEvent>异常列表
     * <AUTHOR>
     */
    List<StepIntervalEvent> findByWorkSheetIdAndStepIdAndRequestContainerIdInAndDeleted(Long subWorkSheetId, Long stepId,List<Long> containerIdList, Long deleted);
}
