package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.web.rest.report.dto.*;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.PedigreeStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.ProductionCycleStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityLineChartDataDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityOrganizationQueryDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityQueryDTO;
import net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO;
import net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产总工单Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkSheetRepository extends LogicDeleteableRepository<WorkSheet>,
        EntityGraphJpaSpecificationExecutor<WorkSheet>, EntityGraphJpaRepository<WorkSheet, Long> {


    /**
     * 根据总工单号获取记录
     *
     * @param serialNumber 工单号
     * @param deleted      删除标志
     * @return java.util.Optional<net.airuima.domain.procedure.aps.WorkSheet> 总工单号
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Optional<WorkSheet> findBySerialNumberAndDeleted(String serialNumber, Long deleted);


    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select sw from WorkSheet sw where sw.serialNumber=?1 and sw.deleted=?2")
    Optional<WorkSheet> findBySerialNumberAndDeletedWhenDataFilter(String serialNumber, Long deleted);

    /**
     * 通过主键id查找总工单
     *
     * @param id      总工单主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.aps.WorkSheet> 总工单号
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WorkSheet> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过主键id列表查找工单
     *
     * @param ids      工单主键id列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 总工单列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WorkSheet> findByIdInAndDeleted(List<Long> ids, Long deleted);

    /**
     * 通过工单号获取总工单列表
     *
     * @param workSheetNoList 工单号
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 总工单列表
     */
    @DataFilter(isSkip = true)
    @Query("from WorkSheet where serialNumber in ?1 and deleted = 0L")
    @FetchMethod
    List<WorkSheet> findBySerialNumberList(List<String> workSheetNoList);

    /**
     * 通过总工单号模糊查询总工单列表
     *
     * @param text     总工单号
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.aps.WorkSheet> 总工单分页
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @Query("select ws from WorkSheet ws where ws.serialNumber like concat('%',?1,'%') and ws.deleted=0L")
    @EntityGraph("workSheetEntityGraph")
    @FetchMethod
    Page<WorkSheet> findPageBySerialNumber(String text, Pageable pageable);

    /**
     * 通过工单号修改工单状态
     *
     * @param serialNumber 工单号
     * @param status       工单状态
     * <AUTHOR>
     * @date 2022/9/27
     **/
    @Modifying
    @Query("update WorkSheet ws set ws.status = ?2 where ws.serialNumber = ?1")
    void updateStatusBySerialNumber(String serialNumber, int status);

    /**
     * 通过生产线主键ID、状态列表及逻辑删除获取工单列表
     *
     * @param workLineId 生产线主键ID
     * @param statuses   状态列表
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 总工单列表
     */
    @DataFilter(isSkip = true)
    List<WorkSheet> findByWorkLineIdAndStatusInAndDeleted(Long workLineId, List<Integer> statuses, Long deleted);

    /**
     * 通过状态列表及逻辑删除获取工单列表
     */
    @DataFilter(isSkip = true)
    List<WorkSheet> findByStatusInAndDeletedOrderByIdDesc(List<Integer> statusList, Long deleted);

    /**
     * 通过产线主键ID、0:已下单;1:投产中 2:已暂停 状态获取正常 工单
     * <AUTHOR>
     * @param workLineId 生产线ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 总工单列表
     * @date 2021-06-18
     **/
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select ws from WorkSheet ws where ws.workLine.id = ?1 and ws.status in (0,1,2) and ws.category = 1 and ws.deleted = ?2 order by ws.planEndDate asc ")
    Page<WorkSheet> findByWorkLineIdOrderByPlanEndDateAsc(Long workLineId, Long deleted, Pageable pageable);


    /**
     * 产品分类日产量增长趋势(工单投产)
     *
     * @param delay30DayTime 前30天日期
     * @param workLineId     生产线主键ID
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.String>> 增长趋势
     * <AUTHOR>
     * @date 2022/12/29
     **/
    @DataFilter(isSkip = true)
    @Query("select (case when p.type = 0 then p.id " +
            "when p.type = 1 then (select p2.id from Pedigree p2 where p2.id = p.parent.id) " +
            "when p.type = 2 then (select p4.parent.id from Pedigree p4 where p4.id = p.parent.id) else 0 end) as pedigreeId, " +
            "TO_CHAR(ws.actualEndDate, 'YYYY-MM-DD') as time, " +
            "sws.unqualifiedNumber as sun, sws.qualifiedNumber as sqn, ws.unqualifiedNumber as wun, ws.qualifiedNumber as wqn " +
            "from SubWorkSheet sws " +
            "left join WorkSheet ws on sws.workSheet.id = ws.id " +
            "left join Pedigree p on ws.pedigree.id = p.id " +
            "where ws.deleted = 0l and ws.workLine.id = (case when (?2 is not null) then ?2 else ws.workLine.id end) " +
            "and (case when p.type = 0 then p.id " +
            "when p.type = 1 then (select p2.id from Pedigree p2 where p2.id = p.parent.id) " +
            "when p.type = 2 then (select p4.parent.id from Pedigree p4 where p4.id = p.parent.id) else 0 end) is not null " +
            "and ws.actualEndDate > ?1 ")
    List<Map<String, String>> productCategoryDailyOutputGrowthTrendByWorkSheet(LocalDateTime delay30DayTime, Long workLineId);


    /**
     * 产品分类日产量增长趋势(子工单投产)
     *
     * @param delay30DayTime 前30天日期
     * @param workLineId     生产线主键ID
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.String>> 增长趋势
     * <AUTHOR>
     * @date 2022/12/29
     **/
    @DataFilter(isSkip = true)
    @Query("select (case when p.type = 0 then p.id " +
            "when p.type = 1 then (select p2.id from Pedigree p2 where p2.id = p.parent.id) " +
            "when p.type = 2 then (select p4.parent.id from Pedigree p4 where p4.id = p.parent.id) else 0 end) as pedigreeId, " +
            "TO_CHAR(sws.actualEndDate, 'YYYY-MM-DD') as time, " +
            "sws.unqualifiedNumber as sun, sws.qualifiedNumber as sqn, ws.unqualifiedNumber as wun, ws.qualifiedNumber as wqn " +
            "from SubWorkSheet sws " +
            "left join WorkSheet ws on sws.workSheet.id = ws.id " +
            "left join Pedigree p on ws.pedigree.id = p.id " +
            "where ws.deleted = 0l and sws.workLine.id = (case when (?2 is not null) then ?2 else sws.workLine.id end) " +
            "and (case when p.type = 0 then p.id " +
            "when p.type = 1 then (select p2.id from Pedigree p2 where p2.id = p.parent.id) " +
            "when p.type = 2 then (select p4.parent.id from Pedigree p4 where p4.id = p.parent.id) else 0 end) is not null " +
            "and sws.actualEndDate > ?1 ")
    List<Map<String, String>> productCategoryDailyOutputGrowthTrendBySubWorkSheet(LocalDateTime delay30DayTime, Long workLineId);

    /**
     * 通过订单主键id获取对应的工单列表
     * @param saleOrderId 订单主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 总工单列表
     * <AUTHOR>
     * @date  2023/3/16
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WorkSheet> findBySaleOrderIdAndDeleted(Long saleOrderId,Long deleted);


    /**
     * 工单数量查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.lang.Integer 工单数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(s.id) from WorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.category=1")
    Integer countWorkSheetNumber(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 工单数量查询(取消除外)
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系id
     * @return java.lang.Integer 工单数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(s.id) from WorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.category=1 and s.status!=-2")
    Integer countWorkSheetNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 按照工单状态分组获取查询条件下的工单状态的统计个数
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系id
     * @return java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 统计个数
     * <AUTHOR>
     * @date 2023/10/24
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(s.status,count(s.id)) from WorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.category=1 group by s.status")
    List<WsStatusNumberDTO> countStatusNumberGroupByStatus(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 按照日期分组获取查询条件下的工单状态的统计个数
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系id
     * @param statuses 状态列表
     * @return  java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 统计个数
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(DATE_FORMAT(s.planEndDate,'%m-%d'),count(s.id)) from  WorkSheet  s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.status in(?6) and s.deleted=0L and s.category=1 group by DATE_FORMAT(s.planEndDate,'%m-%d')")
    List<WsStatusNumberDTO> countStatusNumberGroupByPlanEndDate(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, List<Integer> statuses);


    /**
     * 按照日期分组获取查询条件下的工单逾期状态的统计个数
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param pedigreeId     产品谱系主键id
     * @param compareDate 比较逾期日期
     * @param statuses  状态列表
     * @return java.util.List<net.airuima.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO> 工单逾期状态的统计个数
     * <AUTHOR>
     * @date 2023/10/24
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.worksheetstatus.WsStatusNumberDTO(DATE_FORMAT(s.planEndDate,'%m-%d'),count(s.id)) from  WorkSheet  s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.planEndDate<?6 and s.status in(?7)  and s.deleted=0L and s.category=1 group by DATE_FORMAT(s.planEndDate,'%m-%d')")
    List<WsStatusNumberDTO> countExpiredNumberGroupByPlanEndDate(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, LocalDateTime compareDate,List<Integer> statuses);


    /**
     * 计划完成数量查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.lang.Integer 计划完成数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select sum(s.number) from WorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.category=1 and s.status <> -2")
    Integer countWorkSheetPlanNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 实际完成数量查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.lang.Integer 实际完成数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select sum(s.qualifiedNumber + s.unqualifiedNumber) from WorkSheet s where 1=1 and ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.category=1  and s.status <> -2")
    Integer countWorkSheetActualFinishNumberIgnoreCancel(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 查询按部门分组数据
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @param deleted           删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.OrganizationChartResultDTO> 部门分组完成数据
     */
    @DataFilter(isSkip = true)
    @Query(value = "select new net.airuima.rbase.web.rest.report.dto.OrganizationChartResultDTO(s.organizationId, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.number))  from WorkSheet s  where ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status <> -2 group by s.organizationId")
    @FetchMethod
    List<OrganizationChartResultDTO> findOrganizationChart(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 查询按产线分组完成数据
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @param deleted           删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.WorkLineFinishInfoChartResultDTO> 产线分组完成数据
     */
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkLineFinishInfoChartResultDTO(wl.name,sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.number))  from WorkSheet s inner JOIN WorkLine wl ON wl.id = s.workLine.id where ( ?1 is null or s.planEndDate >= ?1  ) and ( ?2 is null or s.planEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status <> -2 group by wl.name ")
    List<WorkLineFinishInfoChartResultDTO> findWorkLineChart(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 合格率数量查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return net.airuima.web.rest.report.dto.QualifiedRateNumberQueryDTO 合格率数量查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.QualifiedRateNumberQueryDTO(count(s.id),sum(s.number),sum(s.qualifiedNumber),sum(s.unqualifiedNumber),sum(s.reworkQualifiedNumber)) " +
            "from WorkSheet s where 1=1 and ( coalesce(?1, null) is null or s.actualEndDate >= ?1  ) and ( coalesce(?2, null) is null or s.actualEndDate < ?2 )  and  (coalesce(?3, null) is null or s.workLine.id = ?3 ) " +
            "and  ( coalesce(?4, null) is null or s.pedigree.id in (?4) )  and (coalesce(?5, null) is null or s.serialNumber like concat(?5,'%')) and s.deleted = ?6 and s.status in (3,5) and s.category = 1")
    QualifiedRateNumberQueryDTO countWorkSheetQualifiedRateNumber(LocalDateTime startTime, LocalDateTime endTime, Long workLineId, List<Long> pedigreeId,String serialNumber, Long deleted);


    /**
     * 合格率图形查询结果
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.util.List<net.airuima.web.rest.report.dto.QualifiedChartQueryDataDTO>  合格率图形查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.QualifiedChartQueryDataDTO(sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.qualifiedNumber),sum(s.reworkQualifiedNumber)" +
            ",s.pedigree.name,DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')) " +
            "from WorkSheet s  where 1=1 and ( coalesce(?1, null) is null or s.actualEndDate >= ?1  ) and ( coalesce(?2, null) is null or s.actualEndDate < ?2 ) " +
            " and  ( coalesce(?3, null) is null or s.workLine.id = ?3 ) and  ( coalesce(?4, null) is null or s.pedigree.id in (?4) )  and (coalesce(?5, null) is null or s.serialNumber like concat(?5,'%')) and s.deleted = ?6 and s.status in (3,5) and s.category = 1 and s.pedigree.name is not null and date(s.actualEndDate) is not null group by s.pedigree.id, DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')")
    List<QualifiedChartQueryDataDTO> getQualifiedChartData(LocalDateTime startTime, LocalDateTime endTime, Long workLineId, List<Long> pedigreeId,String serialNumber, Long deleted);


    /**
     * 产量数量统计查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityQueryDTO 产量数量统计查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityQueryDTO(count(s.id),sum(s.number),sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.qualifiedNumber) ) from WorkSheet s where s.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5)")
    ProductionCapacityQueryDTO findProductionCapacityNumberData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按部门统计查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityOrganizationQueryDTO 产量按部门统计查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityOrganizationQueryDTO(s.organizationId,sum(s.qualifiedNumber + s.unqualifiedNumber)) from WorkSheet s where s.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) group by s.organizationId")
    @FetchMethod
    List<ProductionCapacityOrganizationQueryDTO> findProductionCapacityOrganizationData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按产线统计查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityWorkLineDTO 产量按产线统计查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityWorkLineDTO(s.workLine.name,sum(s.qualifiedNumber + s.unqualifiedNumber)) from WorkSheet s where s.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) group by s.workLine.id")
    List<ProductionCapacityWorkLineDTO> findProductionCapacityWorkLineData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);


    /**
     * 产量按产品型号统计查询
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param organizationId    部门主键id
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return net.airuima.web.rest.report.dto.ProductionCapacityWorkLineDTO 产量按产品型号统计查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.production.ProductionCapacityLineChartDataDTO(s.pedigree.name,sum(s.qualifiedNumber + s.unqualifiedNumber),DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')) from WorkSheet s where s.category=1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 ) and ( ?3 is null or  s.organizationId = ?3 ) and  ( ?4 is null or s.workLine.id = ?4 ) and  ( ?5 is null or s.pedigree.id = ?5 ) and s.deleted = ?6 and s.status in (3,5) and date(s.actualEndDate) is not null group by s.pedigree.id, DATE_FORMAT(s.actualEndDate,'%Y-%m-%d') order by DATE_FORMAT(s.actualEndDate,'%Y-%m-%d') asc ")
    List<ProductionCapacityLineChartDataDTO> findProductionCapacityLineChartData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId, Long pedigreeId, Long deleted);

    /**
     * 根据生产线 以及 工单状态 获取当天的 工单信息
     * @param workLineId 生产线产线主键ID
     * @param startDate 工单状态
     * @param endDate 结束时间
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.WorkOrderDTO>  工单信息
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkOrderDTO(ws.serialNumber,ws.status,ws.pedigree.code,ws.pedigree.name," +
            "ws.pedigree.specification,ws.organizationId,ws.createdDate,ws.planStartDate,ws.planEndDate,ws.actualStartDate,ws.actualEndDate,ws.number,ws.qualifiedNumber) " +
            "from WorkSheet ws where ws.workLine.id = ?1 and ws.status in (3,5) and ws.deleted = 0 and ws.actualEndDate between ?2 and ?3")
    Page<WorkOrderDTO> findByWorkLineAndStatusAndDateNow(Long workLineId, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);


    /**
     * 根据生产线 以及 工单状态 获取当天的 工单总数
     * @param workLineId 生产线产线主键ID
     * @param startDate 工单状态
     * @param endDate 结束时间
     * @return  java.lang.Long  工单总数
     */
    @DataFilter(isSkip = true)
    @Query("select count(ws.id) from WorkSheet ws where ws.workLine.id = ?1 and ws.status in (3,5) and ws.deleted = 0 and ws.actualEndDate between ?2 and ?3")
    Long countByWorkLineAndStatusAndDateNow(Long workLineId,LocalDateTime startDate,LocalDateTime endDate);

    /**
     * 根据部门id 产线id 以及当前时间，获取计划时间小于当前时间的 产线统计
     *
     * @param organizationId 部门id
     * @param workLineId     产线id
     * @param nowDateTime    当前时间
     * @return java.util.List<net.airuima.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO> 产线统计
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO(ws.organizationId,ws.workLine,count(ws.id),sum(ws.number)) from WorkSheet ws where ws.status in (0,1) and ( ?1 is null or  ws.organizationId = ?1 ) and  ws.category > -1 and ( ?2 is null or ws.workLine.id = ?2 ) and ws.planEndDate < ?3 and ws.deleted = 0 group by ws.workLine.id")
    List<WorkLineStatisticsDTO> countByNowBeforeAndStatus(Long organizationId, Long workLineId,LocalDateTime nowDateTime);

    /**
     * 通过部门主键id 产线主键id 获取以当前开始结束时间 范围时间内 工单今日已完成数
     *
     * @param organizationId 部门主键id
     * @param workLineId     产线主键id
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @return java.util.List<net.airuima.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO> 产线统计
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkLineStatisticsDTO(ws.organizationId,ws.workLine,sum(ws.qualifiedNumber +ws.unqualifiedNumber),sum(ws.qualifiedNumber),sum(ws.unqualifiedNumber),sum(ws.reworkQualifiedNumber)) from WorkSheet ws where ws.deleted = 0 and  ws.category > -1 and ( ?1 is null or  ws.organizationId = ?1 ) and  ( ?2 is null or ws.workLine.id = ?2 ) and ws.status = 3 and ws.actualEndDate between ?3 and ?4 group by ws.workLine.id")
    List<WorkLineStatisticsDTO> countByNowAndFinishNumber(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 获取当前车间下所有投产中、已暂停、已下单的子工单总数
     *
     * @param status         工单状态列表
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @return java.lang.Long  子工单总数
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByStatusInAndOrganizationIdAndDeletedAndCategoryGreaterThan(List<Integer> status,Long organizationId,Long deleted,Integer category);

    /**
     * 子工单中，计划完工日期<当前日期的 逾期数量
     *
     * @param nowDateTime    当前时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态列表
     * @return java.lang.Long  数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateBeforeAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime nowDateTime,Long organizationId,Long deleted,List<Integer> status,Integer category);

    /**
     * 通过 部门id 获取指定开始结束时间内 工单状态为投产中、已暂停、已下单 今日计划完成
     *
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态列表
     * @return java.lang.Long  数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateBetweenAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime startDateTime,LocalDateTime endDateTime,Long organizationId,Long deleted,List<Integer> status,Integer category);

    /**
     * 获取 未来计划完成总数：计划完工日期>当前日期的
     *
     * @param nowDateTime    当前时间
     * @param organizationId 部门主键id
     * @param deleted        逻辑删除
     * @param status         工单状态
     * @return java.lang.Long  数量
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Long countByPlanEndDateAfterAndOrganizationIdAndDeletedAndStatusInAndCategoryGreaterThan(LocalDateTime nowDateTime,Long organizationId,Long deleted,List<Integer> status,Integer category);


    /**
     * 合格率图形查询结果
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return java.util.List<net.airuima.web.rest.report.dto.QualifiedChartQueryDataDTO>  合格率图形查询结果
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.PedigreeStatisticsDTO(s.pedigree.name,DATE_FORMAT(s.actualEndDate,'%m-%d'),sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.qualifiedNumber),sum(s.reworkQualifiedNumber)) from WorkSheet s  where 1=1 and s.category > -1 and ( ?1 is null or s.actualEndDate >= ?1  ) and ( ?2 is null or s.actualEndDate < ?2 )  and  ( ?3 is null or s.organizationId = ?3 )  and ( ?4 is null or s.workLine.id = ?4 ) and s.deleted = ?5 and s.status in (3,5)  and s.pedigree.name is not null and date(s.actualEndDate) is not null group by s.pedigree.id, DATE_FORMAT(s.actualEndDate,'%Y-%m-%d')")
    List<PedigreeStatisticsDTO> getPedigreeStatisticsChartData(LocalDateTime startTime, LocalDateTime endTime, Long organizationId, Long workLineId,Long deleted);

    /**
     * 产品谱系平均使用时长
     *
     * @param organizationId 部门主键id
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @return java.util.List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> 平均使用时长
     */
    @Query(value = "SELECT pe.id as pedigreeId,pe.`code` as pedigreeCode,pe.`name` as pedigreeName, " +
            " CASE" +
            "        WHEN COUNT(ws.id) > 0 THEN ROUND(ROUND(SUM(TIMESTAMPDIFF(SECOND, ws.actual_start_date, ws.actual_end_date)) / 60.0, 4) / sum(ws.number), 1)" +
            "        ELSE 0 " +
            "    END AS averageUsageDuration FROM " +
            " procedure_work_sheet ws " +
            " INNER JOIN base_pedigree pe on pe.id = ws.pedigree_id" +
            " WHERE " +
            " ws.deleted = 0 " +
            " AND ws.organization_id = ?1 " +
            " AND ws.`status` = 3 " +
            " AND ws.category > -1 " +
            " AND ws.actual_end_date IS NOT NULL" +
            " AND ws.actual_end_date >= ?2 " +
            " and ws.actual_end_date < ?3 " +
            " GROUP BY " +
            " ws.pedigree_id ",nativeQuery = true)
    List<ProductionCycleStatisticsDTO.PedigreeAverageTimeConsumptionInfo> findByPedigreeAverageTimeConsumptionInfo(Long organizationId, LocalDateTime startDateTime, LocalDateTime endDateTime);


    /**
     * 通过车间（产线） 获取 今日工单排产 及今日之前的 未完成工单
     *
     * @param workLineId    产线主键ID
     * @param endDateTime   结束时间
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 工单列表
     */
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<WorkSheet> findByStatusInAndWorkLineIdAndPlanEndDateBeforeAndPlanEndDateIsNotNullAndDeletedAndCategoryGreaterThanOrderByPlanEndDateDesc(List<Integer> status,Long workLineId, LocalDateTime endDateTime,Long deleted,Integer category);

    @DataFilter(isSkip = true)
    @EntityGraph(value = "workSheetEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Override
    WorkSheet getReferenceById(Long aLong);
}
