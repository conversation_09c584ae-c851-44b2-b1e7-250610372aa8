package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistoryDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工序检查配置历史详情Repository
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface PedigreeStepInspectionHistoryDetailRepository extends LogicDeleteableRepository<PedigreeStepInspectionHistoryDetail>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepInspectionHistoryDetail>, EntityGraphJpaRepository<PedigreeStepInspectionHistoryDetail, Long> {

    /**
     * 通过工序检查配置历史id获取检查详情
     *
     * @param historyId 检查历史id'
     * @param deleted   逻辑删除
     * @return List<PedigreeStepInspectionHistoryDetail>
     * <AUTHOR>
     * @since 1.8.1
     */
    List<PedigreeStepInspectionHistoryDetail> findByPedigreeStepInspectionHistoryIdAndDeleted(Long historyId, Long deleted);


    /**
     * 获取子工单 工序 处理方式 以及处理状态的 工序检查详情列表
     *
     * @param subWorkSheetId   子工单id
     * @param stepId           工序id
     * @param inspectionResult 处理方式
     * @param status           处理状态
     * @param deleted          逻辑删除
     * @return List<PedigreeStepInspectionHistoryDetail>
     */
    @FetchMethod
    @Query("""
                    select psihd from PedigreeStepInspectionHistoryDetail psihd where psihd.pedigreeStepInspectionHistory.subWorkSheet.id = ?1 
                    and psihd.pedigreeStepInspectionHistory.step.id = ?2 and  psihd.pedigreeStepInspectionHistory.inspectionResult = ?3 
                    and psihd.status = ?4 and psihd.deleted = ?5
            
            """)
    List<PedigreeStepInspectionHistoryDetail> findBySubWorkSheetIdAndStepIdAndInspectionResultAndStatusAndDeleted(Long subWorkSheetId, Long stepId, Integer inspectionResult, Integer status, Long deleted);

    /**
     * 获取工单 工序 处理方式 以及处理状态的 工序检查详情列表
     *
     * @param workSheetId   工单id
     * @param stepId           工序id
     * @param inspectionResult 处理方式
     * @param status           处理状态
     * @param deleted          逻辑删除
     * @return List<PedigreeStepInspectionHistoryDetail>
     */
    @FetchMethod
    @Query("""
                    select psihd from PedigreeStepInspectionHistoryDetail psihd where psihd.pedigreeStepInspectionHistory.workSheet.id = ?1 
                    and psihd.pedigreeStepInspectionHistory.step.id = ?2 and  psihd.pedigreeStepInspectionHistory.inspectionResult = ?3 
                    and psihd.status = ?4 and psihd.deleted = ?5
            
            """)
    List<PedigreeStepInspectionHistoryDetail> findByWorkSheetIdAndStepIdAndInspectionResultAndStatusAndDeleted(Long workSheetId, Long stepId, Integer inspectionResult, Integer status, Long deleted);

}
