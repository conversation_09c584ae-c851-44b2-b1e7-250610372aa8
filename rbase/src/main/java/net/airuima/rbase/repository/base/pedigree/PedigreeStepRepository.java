package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序配置Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeStepRepository extends LogicDeleteableRepository<PedigreeStep>,
        EntityGraphJpaSpecificationExecutor<PedigreeStep>, EntityGraphJpaRepository<PedigreeStep, Long> {

    /**
     * 根据主键id和删除标记查询工序配置
     *
     * @param id      主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<PedigreeStep> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 通过产品谱系主键id和客户主键id查询产品谱系工序配置
     *
     * @param pedigreeId 产品谱系主键id
     * @param deleted    逻辑删除
     * @param clientId   客户主键id
     * @return ava.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @DataFilter(isSkip = true)
    @Query("select ps.step from PedigreeStep ps where ((?1 is null and ps.pedigree is null) or ps.pedigree.id = ?1)  and ps.deleted = ?2 and ((?3 is null and ps.clientId is null) or ps.clientId = ?3) ")
    List<Step> findStepByPedigreeIdAndDeletedAndClientId(Long pedigreeId, Long deleted, Long clientId);

    /**
     * 模糊查询产品谱系和客户主键id绑定工序
     *
     * @param pedigreeId 产品谱系主键id
     * @param keyword    工序名称或编码
     * @param clientId   客户id
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @DataFilter(isSkip = true)
    @Query("select ps.step from PedigreeStep ps where ps.deleted = 0L and ((?1 is null and ps.pedigree is null) or (ps.pedigree.id = ?1))  and (ps.step.name like concat('%',?2,'%') or ps.step.code like concat('%',?2,'%')) and ((?3 is null and ps.clientId is null) or (ps.clientId = ?3)) ")
    List<Step> findStepByPedigreeIdAndKeywordAndClientId(Long pedigreeId, String keyword, Long clientId);


    /**
     * 根据客户产品谱系工艺路线获取多个工序的配置列表
     *
     * @param clientId   客户主键id
     * @param pedigreeId 产品主键id
     * @param workFlowId 工艺路线id
     * @param enable     是否启用
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStep p where ((?1 is null and p.clientId is null) or (p.clientId = ?1)) and ((?2 is null and p.pedigree is null) or (p.pedigree.id = ?2)) and ((?3 is null and p.workFlow.id is null) or (p.workFlow.id = ?3)) and p.enable = ?4 and p.deleted = ?5")
    List<PedigreeStep> findByClientIdAndPedigreeIdAndWorkFlowIdAndEnableAndDeleted(Long clientId, Long pedigreeId, Long workFlowId, Boolean enable, Long deleted);


    /**
     * 获取 产品谱系主键id列表 工艺路线工序 对应的 全部工序配置
     *
     * @param pedigreeIds 产品谱系主键id列表
     * @param workFlowId  工艺路线主键id
     * @param stepId      工序主键id
     * @param clientId    客户主键ID
     * @param enable      是否启用
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepEntityGraph", type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select p from PedigreeStep p where (p.pedigree is null or p.pedigree.id in (?1)) and (p.workFlow is null or p.workFlow.id = ?2) and ( p.step is null or p.step.id = ?3) and (p.clientId is null or p.clientId=?4) and p.enable = ?5 and p.deleted = ?6")
    List<PedigreeStep> findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndEnableAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId, Long clientId, Boolean enable, Long deleted);


    /**
     * 根据客户主键id 产品谱系主键id集合 工艺路线主键 id 工序主键id   删除标记 返回匹配的工序配置
     *
     * @param clientId   客户主键id
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId     工序主键id
     * @param deleted    删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStep p where ((coalesce(?1, null) is null and p.clientId is null) or (p.clientId = ?1)) and ((coalesce(?2, null) is null and p.pedigree is null) or (p.pedigree.id = ?2)) and ((coalesce(?3, null) is null and p.workFlow.id is null) or (p.workFlow.id = ?3)) and ((coalesce(?4, null) is null and p.step.id is null) or (p.step.id = ?4)) and p.deleted = ?5 ")
    Optional<PedigreeStep> findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(Long clientId, Long pedigreeId, Long workFlowId, Long stepId, Long deleted);


    /**
     * 根据客户主键id 产品谱系主键id集合 工艺路线主键id 工序主键id   是否启用 删除标记 返回匹配的工序配置
     *
     * @param clientId   客户主键id
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId     工序主键id
     * @param enable     是否启用
     * @param deleted    删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeStep p where ((coalesce(?1, null) is null and p.clientId is null) or (p.clientId = ?1)) and ((coalesce(?2, null) is null and p.pedigree is null) or (p.pedigree.id = ?2)) and ((coalesce(?3, null) is null and p.workFlow.id is null) or (p.workFlow.id = ?3)) and ((coalesce(?4, null) is null and p.step.id is null) or (p.step.id = ?4)) and p.enable = ?5 and p.deleted = ?6 ")
    Optional<PedigreeStep> findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndEnableAndDeleted(Long clientId, Long pedigreeId, Long workFlowId, Long stepId, Boolean enable, Long deleted);

    /**
     * 根据主键id和删除标识删除
     *
     * @param pedigreeStepId 工序配置主键ID
     */
    @Modifying
    @Query("UPDATE PedigreeStep ps SET ps.deleted=ps.id WHERE ps.id=?1 AND ps.deleted=0")
    void deleteByPedigreeStepIdAndDeleted(Long pedigreeStepId);


    /**
     * 根据优先级主键id查询工序配置
     *
     * @param priorityId 优先级主键id
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep 工序配置列表
     */
    PedigreeStep findTop1ByPriorityElementConfigIdAndDeleted(Long priorityId, Long deleted);

    /**
     * 根据条件查询工序配置列表
     *
     * @param specification 查询条件
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep 工序配置列表
     */
    @Override
    @FetchMethod
    List<PedigreeStep> findAll(Specification<PedigreeStep> specification);

    /**
     * 查询产品谱系的工序配置
     *
     * @param pedigreeId 产品谱系主键id
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep 工序配置列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeStep p where p.pedigree.id = ?1 and p.deleted = ?2")
    List<PedigreeStep> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    /**
     * 查询工序推移图工艺路线
     *
     * @param pedigreeId 产品谱系
     * @param enable     是否启用
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.domain.base.process.WorkFlow> 工艺路线列表
     */
    @Query("""
            select p.workFlow from PedigreeStep p
            where p.pedigree.id = ?1 and p.clientId is null and p.enable = ?2 and p.deleted = ?3""")
    List<WorkFlow> findByPedigreeIdAndEnableAndDeleted(Long pedigreeId, boolean enable, Long deleted);



    /**
     * 根据工艺路线ID和工序ID列表删除工序配置
     * @param workFlowId 工艺路线ID
     * @param stepIds 工序ID列表
     *
     */
    @Modifying
    @Query("update PedigreeStep p set p.deleted=p.id where p.workFlow.id=?1 and p.step.id in(?2) and p.pedigree.id is null and p.clientId is null and p.deleted=0L")
    void logicDeleteByOnlyWorkFlowIdAndStepIdIn(Long workFlowId,List<Long> stepIds);



}
