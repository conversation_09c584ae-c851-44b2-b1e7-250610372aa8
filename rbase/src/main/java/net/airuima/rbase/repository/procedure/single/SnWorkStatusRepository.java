package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.client.ClientStepSubWsInfoDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产状态Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SnWorkStatusRepository extends LogicDeleteableRepository<SnWorkStatus>,
        EntityGraphJpaSpecificationExecutor<SnWorkStatus>, EntityGraphJpaRepository<SnWorkStatus, Long> {

    /**
     * 通过子工单主键id查找SN生产状态
     *
     * @param subWorkSheetId 子工单主键id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);


    /**
     * 通过工单主键id查找SN生产状态
     *
     * @param workSheetId 工单主键id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findByWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 根据子工单主键id 获取指定的sn 以及状态
     * @param subWorkSheetId 子工单主键id
     * @return java.util.List<ClientStepSubWsInfoDTO.SnInfo> SN信息
     */
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.dto.client.ClientStepSubWsInfoDTO$SnInfo(s.sn,s.status) from SnWorkStatus s where s.subWorkSheet.id = ?1 and s.deleted = 0")
    List<ClientStepSubWsInfoDTO.SnInfo> findSnBySubWorkSheetId(Long subWorkSheetId);


    /**
     * 通过子工单和状态查找SN生产状态
     *
     * @param subWorkSheetId 子工单主键id
     * @param status         状态
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "snWorkStatusEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<SnWorkStatus> findBySubWorkSheetIdAndStatusAndDeleted(Long subWorkSheetId, Integer status, Long deleted);

    /**
     *
     * 通过工单ID和状态查找SN生产状态列表
     * @param workSheetId 工单ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> SN生产状态列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "snWorkStatusEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<SnWorkStatus> findByWorkSheetIdAndStatusAndDeleted(Long workSheetId,Integer status, Long deleted);

    /**
     * 通过SN数组获取SN的状态
     *
     * @param snList  SN列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态列表
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @EntityGraph(value = "snWorkStatusEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findBySnInAndDeleted(List<String> snList, Long deleted);

    /**
     * 通过SN查找SN生产状态
     *
     * @param sn      SN
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<SnWorkStatus> findBySnAndDeleted(String sn, Long deleted);


    /**
     * 通过YSN查找SN生产状态
     *
     * @param ysn      ysn
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<SnWorkStatus> findByYsnAndDeleted(String ysn,Long deleted);

    /**
     *
     * 通过sn工作详情主键ID列表及SN生产状态获取SN生产状态数据列表
     * @param snWorkDetailIdList  sn工作详情ID列表
     * @param status SN生产状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> SN生产状态列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findByLatestSnWorkDetailIdInAndStatusAndDeleted(List<Long> snWorkDetailIdList,Integer status,Long deleted);

    /**
     *
     * 通过sn工作详情主键ID列表、SN生产状态列表及子工单ID获取SN生产状态数据列表
     * @param snWorkDetailIdList  sn工作详情ID列表
     * @param subWorkSheetId 子工单ID
     * @param statusList SN状态列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> SN生产状态列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findByLatestSnWorkDetailIdInAndSubWorkSheetIdAndStatusInAndDeleted(List<Long> snWorkDetailIdList,Long subWorkSheetId,List<Integer> statusList,Long deleted);

    /**
     *
     * 通过sn工作详情主键ID列表、SN生产状态列表及工单ID获取SN生产状态数据列表
     * @param snWorkDetailIdList  sn工作详情ID列表
     * @param workSheetId 工单ID
     * @param statusList SN状态列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> SN生产状态列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findByLatestSnWorkDetailIdInAndWorkSheetIdAndStatusInAndDeleted(List<Long> snWorkDetailIdList,Long workSheetId,List<Integer> statusList,Long deleted);

    /**
     * 通过子工单主键ID、SN状态、最新不良项目主键ID列表获取SN生产状态列表
     *
     * @param subWsId            子工单主键ID
     * @param status             生产状态
     * @param unqualifiedItemIds 不良项目主键ID列表
     * @param deleted            删除标识
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkStatus> 单支生产状态列表
     * <AUTHOR>
     * @date 2021-01-15
     **/
    @DataFilter(isSkip = true)
    List<SnWorkStatus> findBySubWorkSheetIdAndStatusAndLatestUnqualifiedItemIdInAndDeleted(Long subWsId, Integer status, List<Long> unqualifiedItemIds, Long deleted);

    /**
     * 修改SN对应绑定工单
     *
     * @param newSubWorkSheetId 新子工单主键id
     * @param oldSubWorkSheetId 旧子工单主键id
     */
    @Modifying
    @Query("UPDATE SnWorkStatus ss SET ss.subWorkSheet.id=?1 WHERE ss.subWorkSheet.id=?2 AND ss.deleted=0")
    void updateSnSubWorkSheet(Long newSubWorkSheetId, Long oldSubWorkSheetId);

    /**
     * 修改SN对应状态
     *
     * @param subWorkSheetId
     */
    @Modifying
    @Query("UPDATE SnWorkStatus ss SET ss.status=1 WHERE ss.subWorkSheet.id=?1 AND ss.deleted=0")
    void updateSnStatus(Long subWorkSheetId);

}
