package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.SnStepFppStatus;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 单支FPP状态Repository
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface SnStepFppStatusRepository extends LogicDeleteableRepository<SnStepFppStatus>,
        EntityGraphJpaSpecificationExecutor<SnStepFppStatus>, EntityGraphJpaRepository<SnStepFppStatus, Long> {

    /**
     * 通过SN列表获取单支FPP状态列表
     * @param snList SN列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.quality.SnStepFppStatus> 单支FPP状态列表
     */
    List<SnStepFppStatus> findBySnInAndDeleted(List<String> snList,Long deleted);

    /**
     * 通过SN获取单支FPP状态
     * @param sn SN
     * @param deleted 逻辑删除
     * @return net.airuima.domain.procedure.quality.SnStepFppStatus 单支FPP状态
     */
    SnStepFppStatus findBySnAndDeleted(String sn,Long deleted);

    /**
     * 通过SN逻辑删除SN-FPP状态
     * @param sn
     * <AUTHOR>
     * @since 1.8.1
     */
    @Modifying
    @Query("update SnStepFppStatus set deleted=id where sn=?1")
    void deleteBySn(String sn);
}
