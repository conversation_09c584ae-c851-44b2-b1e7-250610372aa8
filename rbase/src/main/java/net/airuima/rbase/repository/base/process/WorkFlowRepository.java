package net.airuima.rbase.repository.base.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程框图Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkFlowRepository extends LogicDeleteableRepository<WorkFlow>,
        EntityGraphJpaSpecificationExecutor<WorkFlow>, EntityGraphJpaRepository<WorkFlow, Long> {

    /**
     * 根据名称或者编码获取工艺路线
     *
     * @param text     名称或编码
     * @param pageable 分页条件
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线分页
     */
    @Query("select wf from WorkFlow wf where (" +
            "(coalesce(?1, null) is null or wf.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or wf.name like concat('%',?1,'%')))" +
            "and wf.isEnable=?2 and wf.deleted=0L")
    Page<WorkFlow> findByNameOrCode(String text,Boolean enable, Pageable pageable);

    /**
     * 通过主键id查找唯一的流程框图
     *
     * @param id      工艺路线主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     */
    Optional<WorkFlow> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过编码查询唯一框图
     *
     * @param code 流程框图编码
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     */
    @Query("from WorkFlow where code = ?1 and deleted = 0L")
    Optional<WorkFlow> findByCode(String code);

    /**
     * 通过工艺路线编码列表 获取工艺路线
     * @param workFlowCodes 工艺路线编码列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2021/12/15
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线列表
     */
    List<WorkFlow> findByCodeInAndDeleted(List<String> workFlowCodes,Long deleted);

    /**
     * 根据（名称/编码）和 工艺路线类型 获取工艺路线
     *
     * @param categoryList 工艺路线类型
     * @param text         名称或编码
     * @param pageable     分页条件
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线分页
     */
    @Query("select wf from WorkFlow wf where (" +
            "(coalesce(?1, null) is null or wf.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or wf.name like concat('%',?1,'%')))" +
            "and wf.category in (?2) and wf.isEnable=?3 and  wf.deleted=0L")
    Page<WorkFlow> findByCodeOrNameAndCategoryList(String text, List<Integer> categoryList,Boolean enable, PageRequest pageable);

    /**
     * 通过工艺路线编码获取工艺路线
     * <AUTHOR>
     * @date 2022/10/26
     * @param workFlowCode 工艺路线编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     */
    Optional<WorkFlow> findByCodeAndDeleted(String workFlowCode,Long deleted);
}
