package net.airuima.rbase.repository.procedure.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.process.WorkflowConvertHistory;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺历史Repository
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Repository
public interface WorkflowConvertHistoryRepository extends LogicDeleteableRepository<WorkflowConvertHistory>,
        EntityGraphJpaSpecificationExecutor<WorkflowConvertHistory>, EntityGraphJpaRepository<WorkflowConvertHistory, Long> {
    /**
     * 通过子工单获取转工艺历史记录列表
     *
     * @param subWorkSheetId 子工单主键id
     * @param deleted        删除标记
     * @return java.util.List<net.airuima.rbase.domain.procedure.process.WorkflowConvertHistory> 转工艺历史记录列表
     */
    @DataFilter(isSkip = true)
    @Query("select w from WorkflowConvertHistory w where w.subWorkSheet.id = ?1 and w.deleted = ?2")
    @FetchMethod
    List<WorkflowConvertHistory> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);


}
