package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.AreaWorkCell;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 区域工位Repository
 *
 * <AUTHOR>
 * @date 2023/4/13 9:58
 **/
@Repository
public interface AreaWorkCellRepository extends LogicDeleteableRepository<AreaWorkCell>,
        EntityGraphJpaSpecificationExecutor<AreaWorkCell>, EntityGraphJpaRepository<AreaWorkCell, Long> {

    /**
     * 通过工位获取唯一记录
     *
     * @param workCellId 工位主键id
     * @param deleted    逻辑删除
     * @param isEnable   是否启用(0:禁用;1:启用)
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.AreaWorkCell> 区域工位
     */
    @DataFilter(isSkip = true)
    Optional<AreaWorkCell> findByWorkCellIdAndIsEnableAndDeleted(Long workCellId, boolean isEnable, Long deleted);

    /**
     * 通过区域获取区域工位列表
     *
     * @param areaId  区域Id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.AreaWorkCell> 区域工位列表
     */
    @DataFilter(isSkip = true)
    List<AreaWorkCell> findByAreaIdAndDeleted(Long areaId, Long deleted);

    /**
     * 删除该区域绑定的所有工位信息
     *
     * @param areaId 区域主键Id
     */
    @Modifying
    @Query("delete from AreaWorkCell where area.id = ?1")
    void deleteByAreaId(Long areaId);

    /**
     * 通过工位列表获取区域工位信息
     *
     * @param workCellIds 工位列表
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.AreaWorkCell> 区域工位列表
     */
    @DataFilter(isSkip = true)
    List<AreaWorkCell> findByWorkCellIdInAndDeleted(List<Long> workCellIds, Long deleted);


    /**
     * 模糊查询区域绑定的工位信息
     *
     * @param areaId  区域主键id
     * @param keyword 工位名称或编码
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.scene.AreaWorkCell> 区域工位分页
     */
    @DataFilter(isSkip = true)
    @Query("select awc.workCell from AreaWorkCell awc where awc.deleted = 0 and awc.area.id = ?1 and (awc.workCell.name like concat('%',?2,'%') or awc.workCell.code like concat('%',?2,'%') )")
    Page<WorkCell> findByAreaIdAndKeyword(Long areaId, String keyword, Pageable pageable);

    /**
     * 查询区域绑定的工位信息
     *
     * @param areaId 区域主键id
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.scene.AreaWorkCell> 区域工位分页
     */
    @DataFilter(isSkip = true)
    @Query("select awc.workCell from AreaWorkCell awc where awc.area.id = ?1 and awc.deleted = 0L")
    Page<WorkCell> findByAreaId(Long areaId, Pageable pageable);
}
