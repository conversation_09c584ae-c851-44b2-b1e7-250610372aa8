package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerRepository extends LogicDeleteableRepository<Container>,
        EntityGraphJpaSpecificationExecutor<Container>, EntityGraphJpaRepository<Container, Long> {

    /**
     * 根据容器号获取容器信息
     *
     * @param code    容器号
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.batch.Container> 容器
     * @throws
     * <AUTHOR>
     * @date 2021-01-12
     **/
    Optional<Container> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 通过子工单的主键id列表、状态来修改容器的状态
     *
     * @param status 是否占用(0:否;1:是) 0,false;1,true
     * @param ids 子工单集合
     * @param deleted  逻辑删除
     */
    @Modifying(clearAutomatically = true)
    @Query("update Container set status = ?1 where id in ?2 and deleted = ?3")
    void updateStatus(boolean status, List<Long> ids, Long deleted);


    /**
     * 根据容器编码获取容器信息
     * @param codes 容器编码list
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/11
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.Container> 容器列表
     */
    List<Container> findByCodeInAndDeleted(List<String> codes,Long deleted);
}
