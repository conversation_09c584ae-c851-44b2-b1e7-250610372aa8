package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.PedigreeStepInspectionHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * 工序检查历史记录Repository
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface PedigreeStepInspectionHistoryRepository extends LogicDeleteableRepository<PedigreeStepInspectionHistory>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepInspectionHistory>, EntityGraphJpaRepository<PedigreeStepInspectionHistory, Long> {


}
