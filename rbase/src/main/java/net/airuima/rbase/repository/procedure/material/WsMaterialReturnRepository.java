package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工单退料Repository
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Repository
public interface WsMaterialReturnRepository extends LogicDeleteableRepository<WsMaterialReturn>,
        EntityGraphJpaSpecificationExecutor<WsMaterialReturn>, EntityGraphJpaRepository<WsMaterialReturn, Long> {

    /**
     * 通过物料id查找退料详情
     *
     * @param materialId      物料id
     * @param deleted  逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialReturn> 退料详情
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialReturn> findByMaterialIdAndDeleted(Long materialId, Long deleted);
}
