package net.airuima.rbase.repository.procedure.aps;

import net.airuima.rbase.domain.procedure.aps.PlannedOrder;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 计划下单记录
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface PlannedOrderRepository extends LogicDeleteableRepository<PlannedOrder>,
        JpaSpecificationExecutor<PlannedOrder>, JpaRepository<PlannedOrder, Long> {

    /**
     * 通过销售订单详情获取指定的状态的 计划下单记录列表
     *
     * @param saleOrderDetailIds 销售订单详情ids
     * @param status             状态：0待下单，1已下单
     * @param deleted            逻辑删除
     * @return List<PlannedOrder>
     */
    List<PlannedOrder> findBySaleOrderDetailIdInAndStatusAndDeleted(List<Long> saleOrderDetailIds, Integer status, Long deleted);

    /**
     * 修改计划下单状态
     * @param status 状态
     * @param ids 记录ids
     * @param deleted 逻辑删除
     */
    @Modifying
    @Query("update PlannedOrder set status = ?1 where id in ?2 and deleted = ?3")
    void updateStatus(Integer status, List<Long> ids, Long deleted);
}
