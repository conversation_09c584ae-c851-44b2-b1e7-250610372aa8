package net.airuima.rbase.repository.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.InspectUnqualified;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良品管理记录表Repository
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Repository
public interface InspectUnqualifiedRepository extends LogicDeleteableRepository<InspectUnqualified>,
        EntityGraphJpaSpecificationExecutor<InspectUnqualified>, EntityGraphJpaRepository<InspectUnqualified, Long> {

    /**
     * 通过不良品记录主键id 获取不良品信息
     * @param id 不良品记录主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.InspectUnqualified> 不良品管理记录
     * <AUTHOR>
     * @date  2023/5/9
     */
    @DataFilter(isSkip = true)
    Optional<InspectUnqualified> findByIdAndDeleted(Long id,Long deleted);

}
