package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemCause;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良现象原因关系Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface UnqualifiedItemCauseRepository extends LogicDeleteableRepository<UnqualifiedItemCause>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedItemCause>, EntityGraphJpaRepository<UnqualifiedItemCause, Long> {

    /**
     * 删除该不良现象的所有不良原因关系
     *
     * @param unqualifiedItemId 不良现象主键ID
     */
    @Modifying
    @Query("delete from UnqualifiedItemCause where unqualifiedItem.id=?1")
    void deleteByUnqualifiedItemId(Long unqualifiedItemId);

    /**
     * 通过不良项目主键id数组查询对应不良原因
     *
     * @param unqualifiedItemIdList 不良项目主键ID数组
     * @param deleted               逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItemCause> 不良现象原因列表
     */
    List<UnqualifiedItemCause> findByUnqualifiedItemIdInAndDeleted(List<Long> unqualifiedItemIdList, Long deleted);

    /**
     * 通过不良项目主键id和不良原因主键id查找对应的绑定关系
     *
     * @param unqualifiedItemId  不良项目主键id
     * @param unqualifiedCauseId 不良原因主键id
     * @param deleted            逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.UnqualifiedItemCause> 不良现象原因
     */
    Optional<UnqualifiedItemCause> findByUnqualifiedItemIdAndUnqualifiedCauseIdAndDeleted(Long unqualifiedItemId, Long unqualifiedCauseId, Long deleted);

    /**
     * 根据不良原因主键ID删除不良项目与之绑定关系
     * <AUTHOR>
     * @param unqualifiedCauseId 不良原因主键ID
     * @param deleted     逻辑删除
     * @return void
     * @date 2021-04-13
     **/
    @Modifying
    @Query("update UnqualifiedItemCause set deleted=id where unqualifiedCause.id=?1 and deleted=?2")
    void deleteByUnqualifiedCauseIdAndDeleted(Long unqualifiedCauseId,Long deleted);

}
