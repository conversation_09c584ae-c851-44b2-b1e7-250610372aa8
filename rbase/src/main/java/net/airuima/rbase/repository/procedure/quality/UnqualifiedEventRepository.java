package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 预警停线事件表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface UnqualifiedEventRepository extends LogicDeleteableRepository<UnqualifiedEvent>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedEvent>, EntityGraphJpaRepository<UnqualifiedEvent, Long> {

    /**
     * 通过子工单主键ID、工序主键ID获取预警记录
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param stepId 工序主键ID
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent> 预警停线事件记录
     * @date 2021-05-22
     **/
    @DataFilter(isSkip = true)
    List<UnqualifiedEvent> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId,Long stepId,Long deleted);

    /**
     *
     *
     * 通过子工单主键id、工序主键id、是否处理状态获取预警记录
     * @param subWorkSheetId 子工单
     * @param stepId 工序
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent> 未处理的预警停线记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<UnqualifiedEvent> findBySubWorkSheetIdAndStepIdAndStatusNotAndDeleted(Long subWorkSheetId,Long stepId,int status,Long deleted);

    /**
     * 通过工单主键ID、工序主键id获取预警记录
     * @param workSheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent> 预警停线事件记录
     */
    @DataFilter(isSkip = true)
    List<UnqualifiedEvent> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId,Long stepId,Long deleted);

    /**
     *
     * 通过事件序列号获取预警停线记录
     * @param serialNumber 事件序列号
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent> 预警停线事件记录
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<UnqualifiedEvent> findBySerialNumberAndDeleted(String serialNumber,Long deleted);

    /**
     * 通过工单号id获取预警停线记录
     *
     * @param workSheetId 工单号id
     * @param deleted 逻辑删除
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<UnqualifiedEvent> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);
}
