package net.airuima.rbase.repository.base.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.wearingpart.WearingPartGroup;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface WearingPartGroupRepository extends LogicDeleteableRepository<WearingPartGroup>,
        EntityGraphJpaSpecificationExecutor<WearingPartGroup>, EntityGraphJpaRepository<WearingPartGroup, Long> {

    /**
     * 通过 主键id 获取类型
     * @param id 类型主键
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.wearingpart.WearingPartGroup> 易损件种类
     */
    Optional<WearingPartGroup> findByIdAndDeleted(Long id,Long deleted);

    /**
     *  易损件类型名称或者编码获取易损件类型列表
     * @param text  nameOrCode
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.wearingpart.WearingPartGroup> 易损件种类分页
     */
    @Query("select wearingPartGroup from WearingPartGroup wearingPartGroup where wearingPartGroup.deleted = 0L and wearingPartGroup.isEnable=true and (wearingPartGroup.name like concat('%',?1,'%') or wearingPartGroup.code like concat('%',?1,'%'))")
    Page<WearingPartGroup> findByCodeOrName(String text, Pageable pageable);
}
