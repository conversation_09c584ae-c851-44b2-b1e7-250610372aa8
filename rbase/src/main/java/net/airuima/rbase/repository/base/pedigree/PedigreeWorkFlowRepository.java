package net.airuima.rbase.repository.base.pedigree;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系流程框图Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeWorkFlowRepository extends LogicDeleteableRepository<PedigreeWorkFlow>,
        EntityGraphJpaSpecificationExecutor<PedigreeWorkFlow>, EntityGraphJpaRepository<PedigreeWorkFlow, Long> {

    /**
     * 根据主键和删除标记查询产品谱系工艺路线
     *
     * @param id      主键
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeWorkFlow p where p.id = ?1 and p.deleted = ?2")
    Optional<PedigreeWorkFlow> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 据产品谱系主键id和客户主键id查询产品谱系流程图
     * @param pedigreeId 产品谱系ID
     * @param clientId 客户ID
     * @param deleted 逻辑删除
     * @return List<WorkFlow>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p.workFlow from PedigreeWorkFlow p where ((coalesce(?1, null) is null and p.pedigree.id is null ) or p.pedigree.id = ?1) and ((coalesce(?2, null) is null and p.clientId is null ) or p.clientId = ?2) and p.deleted = ?3")
    List<WorkFlow> findByPedigreeIdAndClientIdAndDeleted(Long pedigreeId, Long clientId, Long deleted);

    /**
     * 模糊查询产品谱系绑定流程框图
     *
     * @param pedigreeId 产品谱系主键id
     * @param keyword    流程框图编码或名称
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线列表
     */
    @DataFilter(isSkip = true)
    @Query("select pw.workFlow from PedigreeWorkFlow pw where pw.deleted = 0L and pw.pedigree.id = ?1 and (pw.workFlow.name like concat('%',?2,'%') or pw.workFlow.code like concat('%',?2,'%') ) and ( pw.clientId is null or pw.clientId = ?3) ")
    List<WorkFlow> findWorkFlowByPedigreeIdAndKeywordAndClientId(Long pedigreeId, String keyword, Long clientId);

    /**
     * 根据产品谱系主键id和客户主键id查询产品谱系流程图
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId   客户主键id
     * @param isEnable   是否启用
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeWorkFlow p where p.pedigree.id = ?1 and ( p.clientId is null or p.clientId = ?2)  and p.isEnable = ?3 and p.deleted = ?4")
    @FetchMethod
    List<PedigreeWorkFlow> findByPedigreeIdAndClientIdAndIsEnableAndDeleted(Long pedigreeId, Long clientId, boolean isEnable, Long deleted);

    /**
     * 根据产品谱系主键id和客户主键id查询产品谱系流程图
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId   客户主键id
     * @param isEnable   是否启用
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeWorkFlow p where p.pedigree.id = ?1 and (p.clientId is null or p.clientId = ?2)  and p.isEnable = ?3 and p.workFlow.isEnable=?3 and p.deleted = ?4 ")
    @FetchMethod
    List<PedigreeWorkFlow> findAllByPedigreeIdAndClientIdAndIsEnableAndDeleted(Long pedigreeId, Long clientId, boolean isEnable, Long deleted);


    /**
     * 根据产品谱系主键id和客户主键id查询工艺路线
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId   客户主键id
     * @param isEnable   是否启用
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线列表
     */
    @DataFilter(isSkip = true)
    @Query("select p.workFlow from PedigreeWorkFlow p where p.pedigree.id = ?1 and (p.clientId is null or p.clientId = ?2)  and p.isEnable = ?3 and p.deleted = ?4")
    List<WorkFlow> findWorkFlowByPedigreeIdAndClientIdAndIsEnableAndDeleted(Long pedigreeId, Long clientId, boolean isEnable, Long deleted);

    /**
     * 通过工艺路线主键ID获取产品谱系列表 客户主键id空情况
     *
     * @param workFlowId 工艺路线主键ID
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     * <AUTHOR>
     * @date 2021-05-06
     **/
    @Query("select pw.pedigree from PedigreeWorkFlow pw where pw.workFlow.id = ?1  and pw.deleted = ?2 ")
    List<Pedigree> findPedigreeByWorkFlowIdAndDeleted(Long workFlowId, Long deleted);


    /**
     * 通过谱系主键ID和工艺路线主键ID和客户主键id逻辑删除绑定关系
     *
     * @param pedigreeId 谱系主键ID
     * @param workFlowId 工艺路线主键ID
     * @param clientId   客户主键id
     * @return void
     **/
    @Modifying
    @Query("update PedigreeWorkFlow pwf set pwf.deleted=pwf.id where pwf.pedigree.id=?1 and  pwf.workFlow.id=?2 and (pwf.clientId is null or pwf.clientId = ?3)  ")
    void deleteByPedigreeIdAndWorkFlowIdAndClientId(Long pedigreeId, Long workFlowId, Long clientId);




    /**
     * 根据 产品谱系Id 工艺路线ID 和客户id 产品谱系工艺路线
     *
     * @param pedigreeId 产品谱系Id
     * @param workFlowId 工艺路线ID
     * @param clientId 客户id
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeWorkFlow  p where ((?1 is null and  p.pedigree.id is null) or p.pedigree.id=?1) and ((?2 is null and  p.workFlow.id is null) or p.workFlow.id = ?2) and ( (?3 is null and  p.clientId is null) or p.clientId = ?3) and p.deleted=?4")
    Optional<PedigreeWorkFlow> findByPedigreeIdAndWorkFlowIdAndClientIdAndDeleted(Long pedigreeId, Long workFlowId, Long clientId, Long deleted);

    /**
     * 通过产品谱系ID列表及启用获取数据列表
     * @param pedigreeIdList 产品谱系ID列表
     * @param enable 是否启用
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeWorkFlow p where p.pedigree.id in(?1) and  (p.clientId is null or p.clientId=?2) and  p.isEnable = ?3 and p.deleted = ?4")
    List<PedigreeWorkFlow> findByPedigreeIdInAndClientIdAndIsEnableAndDeleted(List<Long> pedigreeIdList,Long clientId,Boolean enable,Long deleted);

    /**
     * 通过产品谱系id和删除标记查询产品谱系工艺路线
     * @param pedigreeId 产品谱系Id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow> 产品谱系工艺路线列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeWorkFlow p where p.pedigree.id = ?1 and p.deleted = ?2")
    List<PedigreeWorkFlow> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);




}
