package net.airuima.rbase.repository.base.pedigree;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * SN复用配置Repository
 * <AUTHOR>
 * @date 2021-02-23
 */
@Repository
public interface PedigreeSnReuseConfigRepository extends LogicDeleteableRepository<PedigreeSnReuseConfig>,
        EntityGraphJpaSpecificationExecutor<PedigreeSnReuseConfig>, EntityGraphJpaRepository<PedigreeSnReuseConfig,Long> {

    /**
     * 通过产品谱系主键ID和删除标记获取产品谱系SN复用配置列表
     * <AUTHOR>
     * @param pedigreeId 产品谱系主键ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig> 产品谱系SN复用配置
     * @date 2021-03-19
     **/
    @DataFilter(isSkip = true)
    List<PedigreeSnReuseConfig> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    /**
     * 根据产品谱系主键ID列表和删除标记查找产品谱系SN复用配置列表
     * @param pedigreeIds 产品谱系主键ID列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig> 产品谱系SN复用配置
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeSnReuseConfigEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<PedigreeSnReuseConfig> findByPedigreeIdInAndDeleted(List<Long> pedigreeIds, Long deleted);
}
