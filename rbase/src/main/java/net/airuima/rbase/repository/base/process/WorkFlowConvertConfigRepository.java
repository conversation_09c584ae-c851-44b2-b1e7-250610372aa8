package net.airuima.rbase.repository.base.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.process.WorkFlowConvertConfig;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线配置Repository
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Repository
public interface WorkFlowConvertConfigRepository extends LogicDeleteableRepository<WorkFlowConvertConfig>,
        EntityGraphJpaSpecificationExecutor<WorkFlowConvertConfig>, EntityGraphJpaRepository<WorkFlowConvertConfig, Long> {

    /**
     * 根据主键id 和删除标记查找转工艺路线配置
     *
     * @param id      主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 转工艺路线配置
     */
    @DataFilter(isSkip = true)
    @Query("select w from WorkFlowConvertConfig w where w.id = ?1 and w.deleted = ?2")
    Optional<WorkFlowConvertConfig> findByIdAndDeleted(Long id, Long deleted);



    /**
     * 根据产品谱系主键id和工艺路线主键id和客户主键id和工序主键id和关键字和是否启用和删除标记查询转工艺配置
     *
     * @param pedigreeId       产品谱系主键id
     * @param originWorkFlowId 工艺路线主键id
     * @param clientId         客户主键id
     * @param stepId           工序主键id
     * @param deleted          删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.process.WorkFlow> 转工艺路线配置
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select w from WorkFlowConvertConfig w where ( (coalesce(?1, null) is null and  w.pedigree.id is null) or w.pedigree.id = ?1) and ( (coalesce(?2, null) is null and  w.originWorkFlow.id is null) or w.originWorkFlow.id = ?2) and ( (coalesce(?3, null) is null and  w.clientId is null) or w.clientId = ?3) and  ( (coalesce(?4, null) is null and  w.step.id is null) or w.step.id = ?4 ) and ( (coalesce(?5, null) is null and  w.targetWorkFlow.id is null) or w.targetWorkFlow.id = ?5)  and w.deleted = ?6")
    Optional<WorkFlowConvertConfig> findByPedigreeIdAndOriginWorkFlowIdAndClientIdAndStepIdAndTargetWorkFlowIdAndDeleted(Long pedigreeId, Long originWorkFlowId, Long clientId, Long stepId, Long targetWorkFlowId, Long deleted);


    /**
     * 根据产品谱系主键id列表和工艺路线主键id和客户主键id和工序主键id和关键字和是否启用和删除标记查询转工艺配置
     *
     * @param pedigreeIds       产品谱系主键id
     * @param originWorkFlowId 工艺路线主键id
     * @param clientId         客户主键id
     * @param stepId           工序主键id
     * @param enable           启用
     * @param deleted          删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 查询转工艺配置集合
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select w from WorkFlowConvertConfig w where ( w.pedigree.id is null or w.pedigree.id in(?1) )  and ( w.originWorkFlow.id is null or w.originWorkFlow.id = ?2) and ( w.clientId is null or w.clientId = ?3) and  ( w.step.id is null or w.step.id = ?4) and w.enable = ?5 and w.deleted = ?6")
    List<WorkFlowConvertConfig> findByPedigreeIdInAndOriginWorkFlowIdAndClientIdAndStepIdAndEnableAndDeleted(List<Long> pedigreeIds, Long originWorkFlowId, Long clientId, Long stepId, Boolean enable, Long deleted);



    /**
     * 根据转工艺配置主键id和删除标识删除
     *
     * @param workFlowConvertConfigId 转工艺配置主键ID
     */
    @Modifying
    @Query("UPDATE WorkFlowConvertConfig w SET w.deleted=w.id WHERE w.id=?1 AND w.deleted = 0")
    void deleteByWorkFlowConvertConfigIdAndDeleted(Long workFlowConvertConfigId);
}
