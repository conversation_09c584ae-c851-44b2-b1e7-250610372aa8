package net.airuima.rbase.repository.base.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Repository
public interface WearingPartExchangeRepository extends LogicDeleteableRepository<WearingPartExchange>,
        EntityGraphJpaSpecificationExecutor<WearingPartExchange>, EntityGraphJpaRepository<WearingPartExchange, Long> {

    /**
     * 根据替换易损件类型查询替代关系
     *
     * @param exchangeGroupId 替换易损件类型主键id
     * @param deleted 逻辑删除标志
     * <AUTHOR>
     * @date 2022/10/28
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartExchange> 易损件种类替代列表
     */
    List<WearingPartExchange> findByExchangeWearingPartGroupIdAndDeleted(Long exchangeGroupId, Long deleted);

    /**
     * 根据易损件类型查询可替代的易损件类型
     *
     * @param originGroupId 易损件类型主键id
     * @param deleted 逻辑删除标志
     * <AUTHOR>
     * @date 2022/10/28
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartExchange> 易损件种类替代列表
     */
    List<WearingPartExchange> findByOriginWearingPartGroupIdAndDeleted(Long originGroupId, Long deleted);

    /**
     * 根据易损件类型查询可替代的易损件类型
     *
     * @param originGroupIds 易损件类型主键id集合
     * @param deleted 逻辑删除标志
     * <AUTHOR>
     * @date 2022/10/28
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPartExchange> 易损件种类替代列表
     */
    List<WearingPartExchange> findByOriginWearingPartGroupIdInAndDeleted(List<Long> originGroupIds, Long deleted);

    /**
     * 根据易损件类型、替换易损件类型查询关联关系
     *
     * @param originGroupId 易损件类型主键id
     * @param exchangeGroupId 替换易损件类型主键id
     * @param deleted 逻辑删除标志
     * <AUTHOR>
     * @date 2022/10/28
     * @return net.airuima.rbase.domain.base.wearingpart.WearingPartExchange 易损件种类替代
     */
    WearingPartExchange findByOriginWearingPartGroupIdAndExchangeWearingPartGroupIdAndDeleted(Long originGroupId, Long exchangeGroupId, Long deleted);
}
