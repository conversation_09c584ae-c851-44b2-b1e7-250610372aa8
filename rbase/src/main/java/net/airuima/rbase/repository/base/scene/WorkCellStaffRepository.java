package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStaff;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位员工关系Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkCellStaffRepository extends LogicDeleteableRepository<WorkCellStaff>,
        EntityGraphJpaSpecificationExecutor<WorkCellStaff>, EntityGraphJpaRepository<WorkCellStaff, Long> {
    /**
     * 删除该员工绑定所有工位信息
     *
     * @param staffId 员工主键id
     */
    @Modifying
    @Query("delete from WorkCellStaff where staffId = ?1")
    void deleteByStaffId(Long staffId);

    /**
     * 根据员工主键id查询绑定工位信息
     *
     * @param staffId 员工主键id
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 绑定工位列表
     */
    @Query("select wcs.workCell from WorkCellStaff wcs where wcs.deleted=0 and wcs.staffId=?1")
    @EntityGraph(value = "workCellEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WorkCell> findWorkCellByStaffId(Long staffId);

    /**
     * 通过工位主键ID、员工主键ID获取关联关系
     * @param workCellId 工位主键ID
     * @param staffId 员工主键ID
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.scene.WorkCellStaff 工位员工数据
     */
    @FetchMethod
    WorkCellStaff findByWorkCellIdAndStaffIdAndDeleted(Long workCellId,Long staffId,Long deleted);

    /**
     * 模糊查询员工绑定工位信息
     *
     * @param staffId 员工主键id
     * @param keyword 工位名称或编码
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 绑定工位
     */
    @Query("select ws.workCell from WorkCellStaff ws where ws.deleted=0 and ws.staffId = ?1 and (ws.workCell.name like concat('%',?2,'%') or ws.workCell.code like concat('%',?2,'%'))")
    @FetchMethod
    List<WorkCell> findWorkCellByStaffIdAndKeyword(Long staffId, String keyword);

    /**
     * 根据员工主键ID 查询员工工位
     *
     * @param staffId 员工主键ID
     * @return  java.util.List<net.airuima.rbase.domain.base.scene.WorkCellStaff> 工位员工数据列表
     * <AUTHOR>
     * @date 2022/12/16
     **/
    @DataFilter(isSkip = true)
    @Query("select s from WorkCellStaff s where s.staffId = ?1 and s.deleted = 0l")
    List<WorkCellStaff> findWorkCellStaffByStaffId(Long staffId);
}
