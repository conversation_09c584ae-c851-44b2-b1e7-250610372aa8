package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支工序生产详情Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SnWorkDetailRepository extends LogicDeleteableRepository<SnWorkDetail>,
        EntityGraphJpaSpecificationExecutor<SnWorkDetail>, EntityGraphJpaRepository<SnWorkDetail, Long> {

    /**
     * 通过子工单主键id和SN查找SN详情
     *
     * @param subWsId 子工单主键id
     * @param sn      SN
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetail> findBySubWorkSheetIdAndSnAndDeleted(Long subWsId, String sn, Long deleted);

    /**
     * 通过容器详情主键ID和删除标识查询SN详情
     *
     * @param containerDetailId 容器详情主键ID
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findByContainerDetailIdAndDeleted(Long containerDetailId, Long deleted);


    @DataFilter(isSkip = true)
    SnWorkDetail findTop1BySubWorkSheetIdAndSnAndStepIdInAndReworkTimeAndDeleted(Long subWsId, String sn, List<Long> stepIds, Integer reworkTime, Long deleted);

    @DataFilter(isSkip = true)
    SnWorkDetail findTop1ByWorkSheetIdAndSnAndStepIdInAndReworkTimeAndDeleted(Long wsId, String sn, List<Long> stepIds, Integer reworkTime, Long deleted);

    /**
     *
     * 通过容器详情主键ID和SN获取唯一SN详情数据
     * @param containerDetailId 容器详情主键ID
     * @param sn SN
     * @param deleted 落基删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findByContainerDetailIdAndSnAndDeleted(Long containerDetailId,String sn, Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID、SN及返修次数获取唯一SN工作详情
     * <AUTHOR>
     * @param subWsId 子工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn
     * @param reworkTime 返修次数
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     * @date 2021-03-16
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findBySubWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(Long subWsId, Long stepId, String sn, Integer reworkTime,Long deleted);



    /**
     * 通过子工单主键ID、工序主键ID、SN及返修次数获取唯一SN工作详情
     * <AUTHOR>
     * @param subWsId 子工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn
     * @param reworkTime 返修次数
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findBySnAndSubWorkSheetIdAndStepIdAndReworkTimeAndDeleted(String sn,Long subWsId, Long stepId, Integer reworkTime,Long deleted);

    /**
     * 通过子工单主键ID、SN及返修次数获取SN工作详情列表
     * @param sn SN
     * @param subWsId 子工单主键ID
     * @param reworkTime 返修次数
     * @param deleted  逻辑删除
     * @return  List<SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySnAndSubWorkSheetIdAndReworkTimeAndDeleted(String sn,Long subWsId, Integer reworkTime,Long deleted);

    /**
     * 通过工单主键ID、SN及返修次数获取SN工作详情列表
     * @param sn SN
     * @param wsId 工单主键ID
     * @param reworkTime 返修次数
     * @param deleted  逻辑删除
     * @return List<SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySnAndWorkSheetIdAndReworkTimeAndDeleted(String sn,Long wsId, Integer reworkTime,Long deleted);


    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySnAndDeleted(String sn,Long deleted);

    /**
     * 根据SN列表、子工单ID、工序ID及逻辑删除获取每个SN最新的详情记录列表
     * @param snList SN列表
     * @param subWsId 子工单ID
     * @param stepId 工序ID
     * @param deleted 逻辑删除
     * @return SN详情列表
     */
    @Query("select sd from SnWorkDetail sd where sd.id in(select max(swd.id) from SnWorkDetail  swd where swd.sn in(?1) and swd.subWorkSheet.id=?2 and swd.step.id=?3 and swd.deleted=?4 group by swd.sn)")
    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findLatestStepWorkDetailBySnInAndSubWorkSheetIdAndStepIdAndDeleted(List<String> snList,Long subWsId, Long stepId,Long deleted);


    /**
     * 根据SN列表、工单ID、工序ID及逻辑删除获取每个SN最新的详情记录列表
     * @param snList SN列表
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param deleted 逻辑删除
     * @return SN详情列表
     */
    @Query("select sd from SnWorkDetail sd where sd.id in(select max(swd.id) from SnWorkDetail  swd where swd.sn in(?1) and swd.workSheet.id=?2 and swd.step.id=?3 and swd.deleted=?4 group by swd.sn)")
    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findLatestStepWorkDetailBySnInAndWorkSheetIdAndStepIdAndDeleted(List<String> snList,Long workSheetId, Long stepId,Long deleted);

    /**
     *
     * 通过工单主键ID、工序主键ID、SN及返修次数获取唯一SN工作详情
     * @param worksheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn编码
     * @param reworkTime 返修次数
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> SN工作详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findByWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(Long worksheetId, Long stepId, String sn, Integer reworkTime,Long deleted);


    /**
     *
     * 通过工单主键ID、工序主键ID、SN及返修次数获取唯一SN工作详情
     * @param worksheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn编码
     * @param reworkTime 返修次数
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> SN工作详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findBySnAndWorkSheetIdAndStepIdAndReworkTimeAndDeleted(String sn,Long worksheetId, Long stepId, Integer reworkTime,Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID获取第一个生产记录
     * <AUTHOR>
     * @param subWsId 子工单主键ID
     * @param stepId 工序主键ID
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     * @date 2021-05-23
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySubWorkSheetIdAndStepIdAndDeleted(Long subWsId, Long stepId,Long deleted);

    /**
     * 通过工单主键ID、工序主键ID获取第一个生产记录
     * <AUTHOR>
     * @param wsId 工单主键ID
     * @param stepId 工序主键ID
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     * @date 2021-05-23
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1ByWorkSheetIdAndStepIdAndDeleted(Long wsId, Long stepId,Long deleted);

    /**
     *
     * 通过子工单主键ID、工序主键ID、SN获取第一个生产记录
     * @param subWsId 子工工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySubWorkSheetIdAndStepIdAndSnAndDeletedOrderByIdDesc(Long subWsId, Long stepId,String sn,Long deleted);

    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySnAndSubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(String sn,Long subWsId, List<Long> stepIds,Integer reworkTime,Long deleted);

    @DataFilter(isSkip = true)
    List<SnWorkDetail>  findBySnAndWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(String sn,Long subWsId,List<Long> stepIds,Integer reworkTime,Long deleted);

    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySubWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(Long subWsId, List<Long> stepId,Integer reworkTime,Long deleted);

    @DataFilter(isSkip = true)
    List<SnWorkDetail> findByWorkSheetIdAndStepIdInAndReworkTimeAndDeleted(Long subWsId, List<Long> stepId,Integer reworkTime,Long deleted);

    /**
     *
     * 通过工单主键ID、工序主键ID、SN获取第一个生产记录
     * @param workSheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param sn sn
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1ByWorkSheetIdAndStepIdAndSnAndDeletedOrderByIdDesc(Long workSheetId, Long stepId,String sn,Long deleted);

    /**
     * 获取最新的一条sn的工作详情
     * @param sn sn编码
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/30
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<SnWorkDetail> findTop1BySnAndDeletedOrderByIdDesc(String sn,Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID、获取SN工作详情列表
     * <AUTHOR>
     * @param subWsId 子工单主键ID
     * @param stepId 工序主键ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    List<SnWorkDetail> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWsId, Long stepId,Long deleted);


    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select swd from SnWorkDetail  swd where  swd.subWorkSheet.id=?1 and swd.step.id=?2 and swd.deleted=?3")
    List<SnWorkDetail> findBySubWorkSheetIdAndStepIdAndDeletedWhenDataFilter(Long subWsId, Long stepId,Long deleted);

    /**
     * 通过工单主键ID、工序ID获取SN工作详情列表
     * @param wsId 工单主键ID
     * @param stepId 工序
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetail> findByWorkSheetIdAndStepIdAndDeleted(Long wsId, Long stepId,Long deleted);


    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select swd from SnWorkDetail  swd where  swd.workSheet.id=?1 and swd.step.id=?2 and swd.deleted=?3")
    List<SnWorkDetail> findByWorkSheetIdAndStepIdAndDeletedWhenDataFilter(Long subWsId, Long stepId,Long deleted);


    /**
     * 通过子工单主键ID、SN、ID获取比当前ID小的工作详情
     * <AUTHOR>
     * @param subWsId 子工单主键ID
     * @param sn SN
     * @param id 主键ID
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySubWorkSheetIdAndSnAndIdLessThanAndDeletedOrderByIdDesc(Long subWsId, String sn, Long id, long deleted);

    /**
     * 通过工单主键ID、SN、ID获取比当前ID小的工作详情
     * <AUTHOR>
     * @param wsId 工单主键ID
     * @param sn SN
     * @param id 主键主键ID
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1ByWorkSheetIdAndSnAndIdLessThanAndDeletedOrderByIdDesc(Long wsId, String sn, Long id, long deleted);

    /**
     * 通过子工单主键ID和工序主键ID删除sn详情
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param stepId     工序主键ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update SnWorkDetail sd set sd.deleted=sd.id where sd.subWorkSheet.id=?1 and sd.step.id=?2")
    void batchDeleteBySubWorkSheetIdAndStepId(Long subWorkSheetId,Long stepId);

    /**
     * 通过工单主键ID和工序主键ID删除sn详情
     * <AUTHOR>
     * @param subWorkSheetId 工单主键ID
     * @param stepId     工序主键ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update SnWorkDetail sd set sd.deleted=sd.id where sd.workSheet.id=?1 and sd.step.id=?2")
    void batchDeleteByWorkSheetIdAndStepId(Long subWorkSheetId,Long stepId);

    /**
     * 通过容器详情主键ID删除SN工作详情
     * <AUTHOR>
     * @param containerDetailId     容器想起主键ID
     * @return void
     * @date 2021-08-27
     **/
    @Modifying
    @Query("update SnWorkDetail  sd set sd.deleted=sd.id where sd.containerDetail.id=?1")
    void batchDeleteByContainerDetailId(Long containerDetailId);

    /**
     * 通过容器详情列表 获取sn详情列表信息
     * @param containerDetailIds 容器详情id主键列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/9/29
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findByContainerDetailIdInAndDeleted(List<Long> containerDetailIds,Long deleted);

    /**
     * 通过子工单主键id以及工序ids 获取sn详情列表
     * @param subWorkSheetId 子工单主键id
     * @param stepIds 工序id主键列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/9/29
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findBySubWorkSheetIdAndStepIdInAndDeleted(Long subWorkSheetId,List<Long> stepIds,Long deleted);

    /**
     * 通过工单主键id以及工序ids 获取sn详情列表
     * @param workSheetId 工单主键id
     * @param stepIds 工序id主键列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetail> findByWorkSheetIdAndStepIdInAndDeleted(Long workSheetId,List<Long> stepIds,Long deleted);

    /**
     * 通过子工单id,sn 获取最新一条sn详情
     * @param subWsId 子工单主键ID
     * @param sn sn编码
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/7
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySubWorkSheetIdAndSnAndDeletedOrderByIdDesc(Long subWsId,String sn,Long deleted);

    /**
     * 根据子工单主键id查找第一条单支工序生产详情
     * @param subWsId 子工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySubWorkSheetIdAndDeletedOrderByIdAsc(Long subWsId,Long deleted);

    /**
     * 根据子工单主键id查找第一条单支工序生产详情
     * @param subWsId 子工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1ByWorkSheetIdAndDeletedOrderByIdAsc(Long subWsId,Long deleted);

    /**
     *
     * 通过工单ID,SN获取最新生产记录
     * @param workSheetId 工单主键ID
     * @param sn sn
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1ByWorkSheetIdAndSnAndDeletedOrderByIdDesc(Long workSheetId,String sn,Long deleted);

    /**
     * 通过SN、不合格状态，ID获取比当前ID小的工作详情列表
     * @param sn sn
     * @param result  不合格状态
     * @param snWorkDetailId 当前sn详情主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/9
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySnAndResultAndIdLessThanAndDeletedOrderByIdDesc(String sn,int result,Long snWorkDetailId,Long deleted);

    /**
     * 通过SN、ID获取比当前ID小的工作详情列表
     * @param sn sn
     * @param snWorkDetailId 当前sn详情主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/9
     * @return java.util.Optional<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetail> findTop1BySnAndIdLessThanAndDeletedOrderByIdDesc(String sn,Long snWorkDetailId,Long deleted);

    /**
     * 更换sn详情 对应的原始sn
     * @param originalSn 原始sn
     * @param replaceSn 替换sn
     * <AUTHOR>
     * @date  2023/2/23
     */
    @Modifying
    @Query("update SnWorkDetail swd set swd.sn = ?2  where swd.sn = ?1")
    void snWorkDetailReplaceSn(String originalSn,String replaceSn);

    /**
     * 通过sn+删除标识查询工单集合
     *
     * @param sn      sn
     * @param deleted 删除标识
     * @return : java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 工单列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select distinct c.workSheet from SnWorkDetail c where c.sn = ?1 and c.deleted = ?2")
    List<WorkSheet> findWorkSheetBySnAndDeleted(String sn, Long deleted);

    /**
     * 通过sn+删除标识查询子工单集合
     *
     * @param sn      sn
     * @param deleted 删除标识
     * @return : java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @EntityGraph(value = "snWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select distinct c.subWorkSheet from SnWorkDetail c where c.sn = ?1 and c.deleted = ?2")
    List<SubWorkSheet> findSubWorkSheetBySnAndDeleted(String sn, Long deleted);

    /**
     * 通过子工单主键ID+删除标识查询sn工单详情
     *
     * @param subWorkSheetId 子工单主键ID
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetail> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 通过工单主键ID+删除标识查询sn工单详情
     *
     * @param workSheetId 工单主键ID
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 单支工序生产详情列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetail> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 根据子工单 和工序集合和返修次数和删除标记查找 sn生产详情
     * @param subWorkSheetId 子工单主键id
     * @param stepIdList 工序主键id集合
     * @param reworkTime 返修次数
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> sn生产详情集合
     */
    @DataFilter(isSkip = true)
    @Query("""
            select s from SnWorkDetail s
            where s.subWorkSheet.id = ?1 and s.step.id in ?2 and s.reworkTime = ?3 and s.deleted = ?4 and s.sn = ?5""")
    List<SnWorkDetail> findBySubWorkSheetIdAndStepIdInAndReworkTimeAndSnAndDeleted(Long subWorkSheetId, List<Long> stepIdList, int reworkTime, Long deleted,String sn);


    /**
     * 根据工单 和工序集合和返修次数和删除标记查找 sn生产详情
     * @param workSheetId 工单主键id
     * @param stepIdList 工序主键id集合
     * @param reworkTime 返修次数
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> sn生产详情集合
     */
    @DataFilter(isSkip = true)
    @Query("""
            select s from SnWorkDetail s
            where s.workSheet.id = ?1 and s.step.id in ?2 and s.reworkTime = ?3 and s.deleted = ?4 and s.sn = ?5""")
    List<SnWorkDetail> findByWorkSheetIdAndStepIdInAndReworkTimeAndSnAndDeleted(Long workSheetId, List<Long> stepIdList, int reworkTime, Long deleted,String sn);

    /**
     * 用目标SN替换原始SN
     * @param originSn 原始SN
     * @param targetSn 目标SN
     */
    @Modifying
    @Query("update SnWorkDetail s set s.sn=?2 where s.sn=?1 and s.deleted=0")
    void exchangeSn(String originSn,String targetSn);

    /**
     * 通过sn获取在一定范围内使用该sn的工单
     *
     * @param sn sn
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> sn生产详情集合
     */
    @DataFilter(isSkip = true)
    @Query("select s from SnWorkDetail s where s.sn = ?1 and s.startDate >= ?2 and s.endDate <= ?3 and s.deleted = 0L")
    List<SnWorkDetail> findBySnAndStartDateAndEndDate(String sn, LocalDateTime startDate, LocalDateTime endDate);
}