package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeRepository extends LogicDeleteableRepository<Pedigree>,
        EntityGraphJpaSpecificationExecutor<Pedigree>, EntityGraphJpaRepository<Pedigree, Long> {


    /**
     * 通过产品谱系主键Id查询唯一的产品谱系记录
     *
     * @param id      产品谱系主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<Pedigree> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过产品谱系编码查询唯一的产品谱系记录
     *
     * @param code    产品谱系编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<Pedigree> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 通过产品谱系类型查找当前类型的产品谱系
     *
     * @param type    产品谱系类型
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @FetchMethod
    List<Pedigree> findByTypeAndDeleted(int type, Long deleted);


    /**
     * 通过父级产品谱系主键id查询下级产品谱系
     *
     * @param parentId 父级产品谱系主键id
     * @param deleted  逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @FetchMethod
    List<Pedigree> findByParentIdAndDeleted(Long parentId, Long deleted);

    /**
     * 根据产品谱系编码或者名称获取启用的列表
     *
     * @param text     产品谱系编码或者名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系分页
     */
    @FetchMethod
    @Query("select pedigree from Pedigree pedigree where (" +
            "(coalesce(?1, null) is null or pedigree.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or pedigree.name like concat('%',?1,'%')))" +
            "and pedigree.isEnable=?2 and pedigree.deleted=0L")
    Page<Pedigree> findByNameOrCode(String text, Boolean enable,Pageable pageable);

    /**
     * 根据产品谱系编码或者名称获取启用的谱系列表
     *
     * @param text     产品谱系编码或者名称
     * @param type     层级
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系分页
     */
    @FetchMethod
    @Query("select pedigree from Pedigree pedigree where pedigree.type=?2 and (" +
            "(coalesce(?1, null) is null or pedigree.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or pedigree.name like concat('%',?1,'%')))" +
            "and pedigree.isEnable=?3 and pedigree.deleted=0L")
    Page<Pedigree> findByTypeAndNameOrCode(String text, Integer type, Boolean enable,Pageable pageable);

    /**
     * 获取所有产品谱系数据
     *
     * @param deleted 逻辑删除字段
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @FetchMethod
    List<Pedigree> findByDeleted(long deleted);

    /**
     * 通过物料主键ID获取产品谱系
     * <AUTHOR>
     * @param materialId 物料主键ID
     * @param deleted     逻辑删除
     * @return  java.util.Optional<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系
     * @date 2021-06-04
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<Pedigree> findByMaterialIdAndDeleted(Long materialId,Long deleted);


    /**
     * 通过物料ID列表及删除标识查询产品谱系集合
     * @param materialIdList 物料ID列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<Pedigree> findByMaterialIdInAndDeleted(List<Long> materialIdList,Long deleted);

    /**
     * 通过产品谱系主键ID集合 + 删除标识查询产品谱系集合
     *
     * @param idList  主键ID集合
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     * <AUTHOR>
     * @date 2022/12/30
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<Pedigree> findByIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     * 根据产品谱系编码列表获取产品谱系列表
     * @param codes 产品谱系编码列表
     * @param deleted 逻辑参数
     * @return  java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<Pedigree> findByCodeInAndDeleted(List<String> codes,Long deleted);

}
