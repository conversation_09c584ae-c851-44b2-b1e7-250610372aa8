package net.airuima.rbase.repository.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.wearingpart.LatestStepWearingPart;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序最新使用易损件Repository
 *
 * <AUTHOR>
 */
@Repository
public interface LatestStepWearingPartRepository extends LogicDeleteableRepository<LatestStepWearingPart>,
        EntityGraphJpaSpecificationExecutor<LatestStepWearingPart>, EntityGraphJpaRepository<LatestStepWearingPart, Long> {


    /**
     *
     * @param pedigreeStepWearingPartGroupId 工序易损件使用规则ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.LatestStepWearingPart> 工序最新使用易损件记录列表
     * <AUTHOR>
     */
    @Query("select lp from LatestStepWearingPart lp where lp.pedigreeStepWearingPartGroup.id=?1 " +
            " and  lp.wearingPart.status!=3 and lp.deleted=?2 ")
    List<LatestStepWearingPart> findLatestStepWearingPartAndWearingPartBatch(Long pedigreeStepWearingPartGroupId, Long deleted);

    /**
     * 根据工序使用易损件规则ID列表删除数据
     * @param ruleIds 工序使用易损件规则ID列表
     *
     */
    @Query("delete from LatestStepWearingPart  lp where lp.pedigreeStepWearingPartGroup.id in(?1)")
    @Modifying
    void deleteByPedigreeStepWearingPartGroupIdIn(List<Long> ruleIds);
}
