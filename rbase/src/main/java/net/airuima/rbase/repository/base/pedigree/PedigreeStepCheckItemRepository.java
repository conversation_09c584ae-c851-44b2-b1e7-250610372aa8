
package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 产品谱系工序检测项目Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface PedigreeStepCheckItemRepository extends LogicDeleteableRepository<PedigreeStepCheckItem>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepCheckItem>, EntityGraphJpaRepository<PedigreeStepCheckItem, Long> {

    /**
     * 根据检测规则主键id,逻辑删除产品谱系工序检测项目
     * <AUTHOR>
     * @date 2022/11/3 13:10
     * @param pedigreeStepCheckRuleId 产品谱系工序检测项目主键id
     */
    @Modifying
    @Query("delete from PedigreeStepCheckItem where  pedigreeStepCheckRule.id=?1")
    void deleteByPedigreeStepCheckRuleId(Long pedigreeStepCheckRuleId);

    /**
     * 通过规则ID列表查询产品谱系工序检测项目列表
     *
     * @param pedigreeStepCheckRuleIdList 产品谱系工序检测项目列表主键ID集合
     * @param deleted                     删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem> 产品谱系工序检测项目列表
     * <AUTHOR>
     * @date 2022/11/3
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeStepCheckItem> findByPedigreeStepCheckRuleIdInAndDeleted(List<Long> pedigreeStepCheckRuleIdList, Long deleted);

    /**
     * 通过产品谱系工序检测项目列表主键ID查询产品谱系工序检测项目列表列表
     * @param pedigreeStepCheckRuleId 产品谱系工序检测项目主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem> 产品谱系工序检测项目列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeStepCheckItem> findByPedigreeStepCheckRuleIdAndDeleted(Long pedigreeStepCheckRuleId,Long deleted);

    /**
     * 通过检测规则主键id、检测项目主键id 获取产品谱系工序检测项目
     * 
     * @param pedigreeStepCheckRuleId 检测规则主键id
     * @param checkItemId 检测项目主键id
     * @param deleted 逻辑删除
     * @return Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem 产品谱系工序检测项目
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<PedigreeStepCheckItem> findByPedigreeStepCheckRuleIdAndCheckItemIdAndDeleted(Long pedigreeStepCheckRuleId, Long checkItemId, Long deleted);
}
