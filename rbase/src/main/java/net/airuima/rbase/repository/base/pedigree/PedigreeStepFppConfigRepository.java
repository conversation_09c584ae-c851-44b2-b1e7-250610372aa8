package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepFppConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 产品谱系工序FPP配置Repository
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface PedigreeStepFppConfigRepository extends LogicDeleteableRepository<PedigreeStepFppConfig>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepFppConfig>, EntityGraphJpaRepository<PedigreeStepFppConfig, Long> {

    /**
     * 根据谱系工序ID及逻辑删除标识查询谱系工序FPP配置
     * @param pedigreeStepId 谱系工序ID
     * @param deleted 删除标识
     * @return 谱系工序FPP配置
     */
    Optional<PedigreeStepFppConfig> findByPedigreeStepIdAndDeleted(Long pedigreeStepId,Long deleted);

    @Modifying
    @Query("delete PedigreeStepFppConfig pi  where pi.pedigreeStep.id = ?1")
    void deleteByPedigreeStepId(Long pedigreeStepId);
}
