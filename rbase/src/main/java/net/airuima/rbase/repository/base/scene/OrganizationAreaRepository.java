package net.airuima.rbase.repository.base.scene;

import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 部门区域表Repository
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Repository
public interface OrganizationAreaRepository extends LogicDeleteableRepository<OrganizationArea>,
        JpaSpecificationExecutor<OrganizationArea>, JpaRepository<OrganizationArea, Long> {

    /**
     * 根据部门主键ID查询部门区域列表接口
     *
     * @param organizationId 部门主键ID
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.scene.OrganizationArea> 部门区域列表
     */
    @DataFilter(isSkip = true)
    List<OrganizationArea> findByOrganizationIdAndDeleted(Long organizationId, Long deleted);

    /**
     * 通过主键id获取部门区域表信息
     *
     * @param id      部门区域主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.OrganizationArea> 部门区域
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<OrganizationArea> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过区域编码获取部门区域表信息
     *
     * @param code      区域编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.OrganizationArea> 部门区域
     */
    @DataFilter(isSkip = true)
    Optional<OrganizationArea> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 根据区域编码或者名称获取部门区域
     *
     * @param text     区域编码或者名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.scene.OrganizationArea> 部门区域分页
     */
    @Query("select oa from OrganizationArea oa where oa.deleted = 0L and (oa.name like concat('%',?1,'%') or oa.code like concat('%',?1,'%'))")
    Page<OrganizationArea> findByCodeOrName(String text, Pageable pageable);
}
