package net.airuima.rbase.repository.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface SnWorkDetailWearingPartRepository extends LogicDeleteableRepository<SnWorkDetailWearingPart>,
        EntityGraphJpaSpecificationExecutor<SnWorkDetailWearingPart>, EntityGraphJpaRepository<SnWorkDetailWearingPart, Long> {

    /**
     * 通过单sn详情主键ID、易损件主键ID获取唯一记录
     * @param snWorkDetailId sn详情主键id
     * @param wearingPartId 易损件主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件
     */
    @DataFilter(isSkip = true)
    Optional<SnWorkDetailWearingPart> findBySnWorkDetailIdAndWearingPartIdAndDeleted(Long snWorkDetailId,Long wearingPartId,Long deleted);

    /**
     *  易损件主键id 获取单支列表 分页获取，在开始和结束得范围区间内
     * @param wearingPartId 易损件主键id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted 逻辑删除
     * @param pageable 分页
     * <AUTHOR>
     * @date  2021/8/26
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件分页
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Page<SnWorkDetailWearingPart> findByWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long wearingPartId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 通过SN详情ID删除SN详情易损件
     * @param snWorkDetailId SN详情主键ID
     */
    @Modifying
    @Query("update SnWorkDetailWearingPart swp set swp.deleted=swp.id where swp.snWorkDetail.id=?1")
    void batchDeleteBySnWorkDetailId(Long snWorkDetailId);

    /**
     * 根据sn工单详情主键ID+删除标识查询sn易损件集合
     *
     * @param snWorkDetailId sn工单详情主键ID
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件列表
     * <AUTHOR>
     * @date 2023/6/9
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailWearingPart> findBySnWorkDetailIdAndDeleted(Long snWorkDetailId, Long deleted);

    /**
     *  根据容器详情主键id 以及 易损件主键id 获取 易损件的使用记录列表
     * @param containerDetailId 容器详情主键id
     * @param wearingPartId 易损件主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailWearingPart> findBySnWorkDetailContainerDetailIdAndWearingPartIdAndDeleted(Long containerDetailId,Long wearingPartId,Long deleted);

    /**
     * 根据 子工单主键id 工序主键id 易损件主键id 获取 易损件的使用记录列表
     * @param subWorkSheetId 子工单主键id
     * @param stepId 工序主键id
     * @param wearingPartId 易损件主键主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailWearingPart> findBySnWorkDetailSubWorkSheetIdAndSnWorkDetailStepIdAndWearingPartIdAndDeleted(Long subWorkSheetId,Long stepId,Long wearingPartId,Long deleted);

    /**
     * 根据 工单主键id 工序主键id 易损件主键id 获取 易损件的使用记录列表
     * @param workSheetId  工单主键id
     * @param stepId 工序主键id
     * @param wearingPartId 易损件主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart> 单支生产详情易损件列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailWearingPart> findBySnWorkDetailWorkSheetIdAndSnWorkDetailStepIdAndWearingPartIdAndDeleted(Long workSheetId,Long stepId,Long wearingPartId,Long deleted);
}
