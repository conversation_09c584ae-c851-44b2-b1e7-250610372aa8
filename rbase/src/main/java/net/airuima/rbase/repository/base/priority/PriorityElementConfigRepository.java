package net.airuima.rbase.repository.base.priority;

import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 条件优先级配置Repository
 *
 * <AUTHOR>
 * @date 2022-10-21
 */
@Repository
public interface PriorityElementConfigRepository extends LogicDeleteableRepository<PriorityElementConfig>,
        JpaSpecificationExecutor<PriorityElementConfig>, JpaRepository<PriorityElementConfig, Long> {

    /**
     * 根据对象和优先级别查询优先级配置
     * <AUTHOR>
     * @date 2022/10/21
     * @param target 对象
     * @param priority 优先级
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.priority.PriorityElementConfig> 优先级配置
     */
    PriorityElementConfig findByTargetAndPriorityAndDeleted(Integer target, Integer priority, Long deleted);


    /**
     * 根据对象查询所有优先级配置,按照优先级排序，从低到高
     * <AUTHOR>
     * @date 2022/10/25
     * @param target 对象
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.priority.PriorityElementConfig> 优先级配置列表
     */
    List<PriorityElementConfig> findAllByTargetAndDeletedOrderByPriorityDesc(Integer target, Long deleted);



    /**
     * 根据主键id和删除标记查询优先级配置
     * @param id 主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.priority.PriorityElementConfig> 优先级配置
     */
    @Query("select p from PriorityElementConfig p where p.id = ?1 and p.deleted = ?2")
    Optional<PriorityElementConfig> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 根据组合条件,对象查询优先级配置
     * <AUTHOR>
     * @date 2022/10/24
     * @param combination 组合条件
     * @param target 对象
     * @param deleted 逻辑删除
     * @return PriorityElementConfig
     */
    @Query(value = "select * from base_priority_element_config where JSON_CONTAINS(combination, ?1) and JSON_LENGTH(combination)=?2 and target = ?3 and deleted = ?4",nativeQuery = true)
    PriorityElementConfig findAllByCombinationInAndTargetAndDeleted(String combination, Integer length,Integer target, Long deleted);

    /**
     * 根据组合条件,对象查询优先级配置
     * @param combination  组合条件
     * @param length 组合长度
     * @param target 对象
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级
     */
    @Query(value = "select * from base_priority_element_config where JSON_CONTAINS(combination, ?1) and JSON_LENGTH(combination)=?2 and target = ?3 and deleted = ?4",nativeQuery = true)
    PriorityElementConfig findAllByCombinationInAndLengthAndTargetAndDeleted(String combination, Integer length,Integer target, Long deleted);





}
