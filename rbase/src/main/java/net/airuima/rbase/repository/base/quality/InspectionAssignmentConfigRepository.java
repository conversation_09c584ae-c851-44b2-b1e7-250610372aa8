package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.InspectionAssignmentConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * 质检人员设置
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface InspectionAssignmentConfigRepository extends LogicDeleteableRepository<InspectionAssignmentConfig>, EntityGraphJpaSpecificationExecutor<InspectionAssignmentConfig>, EntityGraphJpaRepository<InspectionAssignmentConfig, Long> {
}
