package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.WorkStation;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工站Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkStationRepository extends LogicDeleteableRepository<WorkStation>,
        EntityGraphJpaSpecificationExecutor<WorkStation>, EntityGraphJpaRepository<WorkStation, Long> {

    /**
     * 通过名称和编码模糊查询工站
     *
     * @param text 工站名称或编码
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkStation> 工站列表
     */
    @Query("from WorkStation where  deleted = 0L and isEnable=true and (name like concat('%',?1,'%') or code like concat('%',?1,'%') )")
    @EntityGraph("workStationEntityGraph")
    List<WorkStation> findByNameOrCode(String text);

    /**
     * 通过线体主键ID获取工站列表
     * <AUTHOR>
     * @param workLineId 线体主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkStation> 工站列表
     * @date 2021-04-12
     **/
    @DataFilter(isSkip = true)
    List<WorkStation> findByWorkLineIdAndDeleted(Long workLineId,Long deleted);

    @DataFilter(isSkip = true)
    @Query("select ws from WorkStation ws where  ws.id=?1 and ws.deleted=?2")
    Optional<WorkStation> findWorkStationByIdAndDeleted(Long id,Long deleted);
}
