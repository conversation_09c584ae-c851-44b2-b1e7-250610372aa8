package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良项目Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface UnqualifiedItemRepository extends LogicDeleteableRepository<UnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedItem>, EntityGraphJpaRepository<UnqualifiedItem, Long> {


    /**
     * 通过名称和编码模糊查询不良项目
     *
     * @param text     名称或编码
     * @param pageable 获取前多少行
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.quality.UnqualifiedIte>m 不良项目分页
     */
    @Query("select unqualifiedItem from UnqualifiedItem unqualifiedItem where unqualifiedItem.deleted = 0L " +
            "and unqualifiedItem.isEnable=?2 and (unqualifiedItem.name like concat('%',?1,'%') or unqualifiedItem.code like concat('%',?1,'%'))")
    Page<UnqualifiedItem> findByNameOrCode(String text, Boolean isEnable, Pageable pageable);

    @Query("select unqualifiedItem from UnqualifiedItem unqualifiedItem where unqualifiedItem.deleted = 0L " +
            "and unqualifiedItem.isEnable=?3 and unqualifiedItem.dealWay=?2 and (unqualifiedItem.name like concat('%',?1,'%') or unqualifiedItem.code like concat('%',?1,'%'))")
    Page<UnqualifiedItem> findByNameOrCodeAndDealWay(String text,Integer dealWay, Boolean isEnable, Pageable pageable);

    /**
     * 获取不合格种类下的所有不良现象
     *
     * @param unqualifiedGroupId 不良种类主键id
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 不良项目列表
     */
    List<UnqualifiedItem> findByUnqualifiedGroupId(Long unqualifiedGroupId);

    /**
     * 通过id查找唯一的不良项目
     *
     * @param id      不良项目主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.UnqualifiedItem>  不良项目
     */
    Optional<UnqualifiedItem> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过不良项目组别id数组查找对应不良项目
     *
     * @param unqualifiedGroupIdList 不良项目组别id数组
     * @param deleted                逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 不良项目 列表
     */
    List<UnqualifiedItem> findByUnqualifiedGroupIdInAndDeleted(List<Long> unqualifiedGroupIdList, Long deleted);

    /**
     * 通过ids获取不良项目列表
     * @param ids 不良项目主键id列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/9/28
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 不良项目 列表
     */
    List<UnqualifiedItem> findByIdInAndDeleted(List<Long> ids,Long deleted);

    /**
     * 通过编码和删除标识查询不良项目
     *
     * @param code    编码
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.UnqualifiedItem>  不良项目
     * <AUTHOR>
     * @date 2022/12/14
     **/
    Optional<UnqualifiedItem> findByCodeAndDeleted(String code, Long deleted);
}
