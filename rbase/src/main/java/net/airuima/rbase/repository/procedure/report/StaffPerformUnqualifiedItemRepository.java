package net.airuima.rbase.repository.procedure.report;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.StepUnqualifiedDTO;
import net.airuima.rbase.web.rest.report.dto.UnqualifiedResultDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Repository
public interface StaffPerformUnqualifiedItemRepository extends LogicDeleteableRepository<StaffPerformUnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<StaffPerformUnqualifiedItem>, EntityGraphJpaRepository<StaffPerformUnqualifiedItem, Long> {

    /**
     * 通过员工产量主键id获取详情中出现不良记录数据
     * @param staffPerformId 员工产量数据主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/12/5
     * @return java.util.List<net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem> 员工不良明细列表
     */
    @DataFilter(isSkip = true)
    List<StaffPerformUnqualifiedItem> findByStaffPerformIdAndDeleted(Long staffPerformId, Long deleted);

    /**
     * 通过员工产量主键id及不良ID获取详情中出现不良记录数据
     * @param staffPerformId 员工产量数据主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/12/5
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerformUnqualifiedItem> 员工不良明细列表
     */
    @DataFilter(isSkip = true)
    Optional<StaffPerformUnqualifiedItem> findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(Long staffPerformId, Long unqualifiedItemId, Long deleted);


    /**
     * 根据员工产量主键id删除对应的不良详情
     * @param staffPerformId 员工产量主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/12/5
     * @return void
     */
    @Modifying
    @Query("update StaffPerformUnqualifiedItem staffPerformUnqualifiedItem set staffPerformUnqualifiedItem.deleted = staffPerformUnqualifiedItem.id where staffPerformUnqualifiedItem.staffPerform.id = ?1 and staffPerformUnqualifiedItem.deleted = ?2")
    void deleteByStaffPerformId(Long  staffPerformId, Long deleted);


    /**
     * 工单工序不良不良图形数据
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.StepUnqualifiedDTO> 工序不良图形数据
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepUnqualifiedDTO(b.name, sum(s.number)) from StaffPerformUnqualifiedItem s left join StaffPerform p on s.staffPerform.id = p.id " +
            "left join Step  b on p.step.id = b.id where 1=1 and ( ?1 is null or s.recordTime >= ?1  ) and ( ?2 is null or s.recordTime < ?2 ) " +
            " and  ( ?3 is null or s.staffPerform.workSheet.workLine.id = ?3 ) and  ( coalesce(?4, null) is null or s.staffPerform.workSheet.pedigree.id in (?4) ) " +
            " and (coalesce(?5, null) is null or s.staffPerform.workSheet.serialNumber like  concat(?5,'%')) and s.deleted = ?6  group by b.name having sum(s.number) > 0")
    List<StepUnqualifiedDTO> findWorkSheetStepUnqualifiedChart(LocalDateTime startTime, LocalDateTime endTime, Long workLineId, List<Long> pedigreeIdList, String serialNumber,Long deleted);


    /**
     * 子工单工序不良不良图形数据
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.StepUnqualifiedDTO> 工序不良图形数据
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepUnqualifiedDTO(b.name, sum(s.number)) from StaffPerformUnqualifiedItem s left join StaffPerform p on s.staffPerform.id = p.id " +
            "left join Step  b on p.step.id = b.id where 1=1 and ( ?1 is null or s.recordTime >= ?1  ) " +
            "and ( ?2 is null or s.recordTime < ?2 )  and  ( ?3 is null or s.staffPerform.subWorkSheet.workSheet.workLine.id = ?3 ) " +
            "and  ( coalesce(?4, null) is null or s.staffPerform.subWorkSheet.workSheet.pedigree.id in (?4) ) " +
            "and (coalesce(?5, null) is null or s.staffPerform.subWorkSheet.serialNumber like  concat(?5,'%')) and s.deleted = ?6  " +
            "and s.staffPerform.subWorkSheet is not null group by b.name having sum(s.number) > 0")
    List<StepUnqualifiedDTO> findSubWorkSheetStepUnqualifiedChart(LocalDateTime startTime, LocalDateTime endTime, Long workLineId,List<Long> pedigreeIdList,  String serialNumber,Long deleted);


    /**
     * 获取子工单不良项目及日期分组后的数据明细
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.UnqualifiedResultDTO> 分组后的不良数据列表
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.UnqualifiedResultDTO(su.unqualifiedItem.id,su.unqualifiedItem.code,su.unqualifiedItem.name,sum(su.number),su.recordDate) " +
            "from StaffPerformUnqualifiedItem su where ( ?3 is null or su.staffPerform.subWorkSheet.workSheet.workLine.id = ?3 ) and (coalesce(?4, null) is null  or su.staffPerform.subWorkSheet.workSheet.pedigree.id in (?4) ) " +
            " and ( ?1 is null or su.recordTime >= ?1  ) and ( ?2 is null or su.recordTime < ?2 ) " +
            "and (coalesce(?5, null) is null or su.staffPerform.subWorkSheet.workSheet.serialNumber like  concat(?5,'%')) and su.deleted = ?6 group by su.unqualifiedItem.id,su.recordDate")
    List<UnqualifiedResultDTO> findSubWorkSheetUnqualifiedItemGroupByUnqualifiedItemAndRecordDate(LocalDateTime startTime, LocalDateTime endTime, Long workLineId, List<Long> pedigreeIdList, String serialNumber,Long deleted);


    /**
     * 获取工单不良项目及日期分组后的数据明细
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param workLineId        产线主键id
     * @param pedigreeId        产品谱系主键id
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.UnqualifiedResultDTO> 分组后的不良数据列表
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.UnqualifiedResultDTO(su.unqualifiedItem.id,su.unqualifiedItem.code,su.unqualifiedItem.name,sum(su.number),su.recordDate) " +
            "from StaffPerformUnqualifiedItem su where ( ?3 is null or su.staffPerform.workSheet.workLine.id = ?3 ) and  ( coalesce(?4, null) is null or su.staffPerform.workSheet.pedigree.id in (?4) ) " +
            " and ( ?1 is null or su.recordTime >= ?1  ) and ( ?2 is null or su.recordTime < ?2 )  and (coalesce(?5, null) is null or su.staffPerform.workSheet.serialNumber like  concat(?5,'%')) and su.deleted=?6  group by su.unqualifiedItem.id,su.recordDate")
    List<UnqualifiedResultDTO> findWorkSheetUnqualifiedItemGroupByUnqualifiedItemAndRecordDate(LocalDateTime startTime, LocalDateTime endTime, Long workLineId, List<Long> pedigreeIdList,String serialNumber,Long deleted);

    /**
     * 根据 部门主键id 产线主键id 获取指定开始结束时间内的 子工单不良项目汇总数量
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> 不良项目汇总数量
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO$UnqualifiedItemNumberInfo(st.unqualifiedItem,sum(st.number)) from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.subWorkSheet.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.subWorkSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and st.deleted = 0 group by st.unqualifiedItem.id")
    List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> findBySubWorkSheetWorkSheetOrganizationIdGroupByUnqualifiedItemId(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据 部门主键id 产线主键id 获取指定开始结束时间内的 工单不良项目汇总数量
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> 不良项目汇总数量
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO$UnqualifiedItemNumberInfo(st.unqualifiedItem,sum(st.number)) from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.workSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and st.deleted = 0 group by st.unqualifiedItem.id")
    List<UnqualifiedStatisticsDTO.UnqualifiedItemNumberInfo> findByWorkSheetOrganizationIdGroupByUnqualifiedItemId(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据 部门主键id 产线主键id 获取指定开始结束时间内的 子工单 工序对应的不良数量汇总
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> 不良数量汇总
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO$StepUnqualifiedItemNumberInfo(st.staffPerform.step,sum(st.number)) from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.subWorkSheet.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.subWorkSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and st.deleted = 0 group by st.unqualifiedItem.id,st.staffPerform.step.id")
    List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> findBySubWorkSheetStepUnqualifiedItemNumberInfo(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据 部门主键id 产线主键id 获取指定开始结束时间内的 工单 工序对应的不良数量汇总
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> 不良数量汇总
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.UnqualifiedStatisticsDTO$StepUnqualifiedItemNumberInfo(st.staffPerform.step,sum(st.number)) from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.workSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and st.deleted = 0 group by st.unqualifiedItem.id,st.staffPerform.step.id")
    List<UnqualifiedStatisticsDTO.StepUnqualifiedItemNumberInfo> findByWorkSheetStepUnqualifiedItemNumberInfo(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);

    /**
     *  根据 部门主键id 产线主键id 获取指定开始结束时间内的 子工单工位工序不良投产详情
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> 子工单工位工序不良投产详情
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO$WorkCellStepInfoUnqualifiedItem(st.staffPerform.workCell,st.staffPerform.step,st.unqualifiedItem,sum(st.number)) from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.subWorkSheet.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.subWorkSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and st.staffPerform.subWorkSheet.workSheet.category > -1 and st.deleted = 0 group by st.unqualifiedItem.id,st.staffPerform.step.id,st.staffPerform.workCell.id")
    List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> findBySubWorkSheetUnqualifiedItemNumberInfo(Long organizationId, Long workLineId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     *  根据 部门主键id 产线主键id 获取指定开始结束时间内的 工单工位工序不良投产详情
     * @param organizationId 部门主键id
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> 子工单工位工序不良投产详情
     */
    @EntityGraph(value = "staffPerformUnqualifiedItemEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO$WorkCellStepInfoUnqualifiedItem(st.staffPerform.workCell,st.staffPerform.step,st.unqualifiedItem,sum(st.number))from StaffPerformUnqualifiedItem st where (?1 is null or st.staffPerform.workSheet.organizationId = ?1) and (?2 is null or st.staffPerform.workSheet.workLine.id = ?2) and ( ?3 is null or st.recordTime >= ?3  ) and ( ?4 is null or st.recordTime < ?4 ) and   st.staffPerform.workSheet.category > -1 and st.deleted = 0 group by st.unqualifiedItem.id,st.staffPerform.step.id,st.staffPerform.workCell.id")
    List<WorkCellStepStatisticsDTO.WorkCellStepInfoUnqualifiedItem> findByWorkSheetUnqualifiedItemNumberInfo(Long organizationId,Long workLineId,LocalDateTime startTime, LocalDateTime endTime);


}
