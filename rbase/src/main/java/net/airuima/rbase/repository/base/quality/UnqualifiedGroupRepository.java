package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良种类Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface UnqualifiedGroupRepository extends LogicDeleteableRepository<UnqualifiedGroup>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedGroup>, EntityGraphJpaRepository<UnqualifiedGroup, Long> {

    /**
     * 获取所有不良组别
     *
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedGroup> 不良种类列表
     */
    List<UnqualifiedGroup> findByDeleted(Long deleted);


    /**
     * 通过名称和编码模糊查询不良种类
     *
     * @param text 不良种类名称或编码
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedGroup> 不良种类列表
     */
    @Query("from UnqualifiedGroup where deleted = 0L and (name like concat('%',?1,'%') or code like concat('%',?1,'%'))")
    List<UnqualifiedGroup> findByNameOrCode(String text);

    /**
     * 通过不良种类编码 和 删除标识查询不良种类
     *
     * @param code    不良种类编码
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.UnqualifiedGroup> 不良种类
     * <AUTHOR>
     * @date 2022/12/14
     **/
    Optional<UnqualifiedGroup> findByCodeAndDeleted(String code, Long deleted);
}
