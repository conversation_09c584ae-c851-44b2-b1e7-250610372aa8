package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工位上料表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface WsWorkCellMaterialBatchRepository extends LogicDeleteableRepository<WsWorkCellMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<WsWorkCellMaterialBatch>, EntityGraphJpaRepository<WsWorkCellMaterialBatch, Long> {

    /**
     * 通过总工单主键ID、工位主键ID，物料主键ID汇总当前工位剩余物料总数
     *
     * @param workSheetId 总工单主键ID
     * @param workCellId  工位主键ID
     * @param materialId  物料主键ID
     * @param deleted     删除标识
     * @return java.math.BigDecimal  剩余物料总数
     * <AUTHOR>
     * @date 2021-01-15
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wcb.leftNumber) from WsWorkCellMaterialBatch wcb where wcb.workSheet.id=?1 and wcb.workCell.id=?2 and wcb.materialId=?3 and wcb.deleted=?4")
    BigDecimal sumLeftNumberByWorkSheetIdAndWorkCellIdAndMaterialIdAndDeleted(Long workSheetId, Long workCellId, Long materialId, Long deleted);

    /**
     * 通过总工单主键ID、工位主键ID，物料I主键D数组汇总当前工位剩余物料总数
     *
     * @param workSheetId    总工单主键ID
     * @param workCellId     工位主键ID
     * @param materialIdList 物料主键ID列表
     * @param deleted
     * @return java.math.BigDecimal  剩余物料总数
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wcb.leftNumber) from WsWorkCellMaterialBatch wcb where wcb.workSheet.id=?1 and wcb.workCell.id=?2 and wcb.materialId in(?3) and wcb.deleted=?4")
    BigDecimal sumLeftNumberByWorkSheetIdAndWorkCellIdAndMaterialIdInAndDeleted(Long workSheetId, Long workCellId, List<Long> materialIdList, Long deleted);

    /**
     * 根据总工单主键ID，工位主键ID,物料主键ID列表获取上料信息大于0的信息
     *
     * @param workSheetId    工单主键ID
     * @param workCellId     工位主键ID
     * @param materialIdList 物料列表
     * @param greaterNumber  比较数量
     * @param deleted
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(Long workSheetId, Long workCellId, List<Long> materialIdList, Double greaterNumber, Long deleted);

    /**
     * 通过总工单主键ID获取数量大于0的工位库存记录
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param greaterNumber 比较数
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     * @date 2021-06-03
     **/
    @DataFilter(isSkip = true)
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndLeftNumberGreaterThanAndDeleted(Long workSheetId, Double greaterNumber, Long deleted);

    /**
     * 指定工位物料剩余数量大于某个值，修改为指定数量
     * @param leftNumber  剩余数量
     * @param workSheetId 工单id
     * @param materialIds 物料ids
     * @param greaterNumber 大于数量
     * @param deleted 逻辑删除
     */
    @Modifying
    @Query("update WsWorkCellMaterialBatch wc set wc.leftNumber = ?1 where wc.workSheet.id = ?2 and wc.materialId in ?3 and wc.leftNumber > ?4 and wc.deleted = ?5")
    void updateLeftNumberByWorkSheetIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(Double leftNumber,Long workSheetId,List<Long> materialIds,Double greaterNumber, Long deleted);


    /**
     * 根据总工单主键ID，工位主键ID,物料主键ID获取上料信息的信息
     *
     * @param workSheetId 工单主键ID
     * @param workCellId  工位主键ID
     * @param materialId  物料主键ID
     * @param deleted
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select wsmb from WsWorkCellMaterialBatch wsmb where wsmb.workSheet.id=?1 and wsmb.workCell.id=?2 and wsmb.materialId=?3 and wsmb.deleted=?4 and wsmb.batch is null")
    Optional<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndMaterialIdAndDeleted(Long workSheetId, Long workCellId, Long materialId, Long deleted);

    /**
     * 根据总工单主键ID，工位主键ID,物料主键ID及批次号获取上料信息的信息
     *
     * @param workSheetId 工单主键ID
     * @param workCellId  工位主键ID
     * @param materialId  物料主键ID
     * @param batch       批次号
     * @param deleted
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(Long workSheetId, Long workCellId, Long materialId, String batch, Long deleted);

    /**
     * 获取工位库存信息
     * @param id 工位库存主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/24
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WsWorkCellMaterialBatch> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 根据工单主键id 获取工单工位 对应的工位列表
     * @param wsId 工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/25
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndDeleted(Long wsId, Long deleted);

    /**
     * 根据工单以及工位 主键id 获取对应的工单工位领料列表
     * @param workSheetId 工单主键id
     * @param workCellId 工位主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/25
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndDeleted(Long workSheetId,Long workCellId,Long deleted);

    /**
     * 通过参数获取降序排列的工位库存列表
     * @param workSheetId 工单主键ID
     * @param workCellId 工位主键ID
     * @param materialIdList 物料主键ID列表
     * @param greaterNumber 比较参数
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     */
    @DataFilter(isSkip = true)
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeletedOrderByLeftNumberDesc(Long workSheetId, Long workCellId, List<Long> materialIdList, Double greaterNumber, Long deleted);


    /**
     * 通过参数获取降序排列的工位库存列表
     * @param workSheetId 工单主键ID
     * @param workCellId 工位主键ID
     * @param materialId 物料主键ID
     * @param greaterNumber 比较参数
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch> 工单工位上料信息列表
     */
    @DataFilter(isSkip = true)
    List<WsWorkCellMaterialBatch> findByWorkSheetIdAndWorkCellIdAndMaterialIdAndLeftNumberGreaterThanAndDeletedOrderByLeftNumberAsc(Long workSheetId, Long workCellId, Long materialId, Double greaterNumber, Long deleted);
}
