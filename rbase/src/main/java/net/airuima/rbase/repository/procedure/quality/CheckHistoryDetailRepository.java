package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史明细Repository
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface CheckHistoryDetailRepository extends LogicDeleteableRepository<CheckHistoryDetail>,
        EntityGraphJpaSpecificationExecutor<CheckHistoryDetail>, EntityGraphJpaRepository<CheckHistoryDetail, Long> {

    /**
     * 通过检测历史主键ID获取检测明细
     *
     * @param historyId 检测历史主键ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CheckHistoryDetail> findByCheckHistoryIdAndDisplayAndDeletedOrderBySn(Long historyId, Boolean display, Long deleted);

    /**
     * 通过子工单主键ID，处理状态、sn以及是否为虚拟SN获取质检历史明细
     *
     * @param subWorkSheetId 子工单主键ID
     * @param status         历史处理状态
     * @param sn             sn
     * @param virtual        是否为虚拟SN
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("""
                select chd from CheckHistoryDetail chd where chd.checkHistory.subWorkSheet.id  = ?1 
                    and chd.checkHistory.status = ?2 and chd.sn = ?3 and chd.virtual = ?4 and chd.deleted = ?5 
            """)
    List<CheckHistoryDetail> findByCheckHistorySubWorkSheetIdAndCheckHistoryStatusAndCheckHistoryWorkSheetIsNullAndSnAndVirtualAndDeleted(Long subWorkSheetId, Boolean status, String sn, Boolean virtual, Long deleted);

    /**
     * 通过工单主键ID，处理状态、sn以及是否为虚拟SN获取质检历史明细
     *
     * @param workSheetId 工单主键ID
     * @param status      历史处理状态
     * @param sn          SN
     * @param virtual     是否为虚拟SN
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("""
                select chd from CheckHistoryDetail chd where chd.checkHistory.workSheet.id  = ?1 
                    and chd.checkHistory.status = ?2 and chd.sn = ?3 and chd.virtual = ?4 and chd.deleted = ?5 
            """)
    List<CheckHistoryDetail> findByCheckHistoryWorkSheetIdAndCheckHistoryStatusAndCheckHistorySubWorkSheetIsNullAndSnAndVirtualAndDeleted(Long workSheetId, Boolean status, String sn, Boolean virtual, Long deleted);

    /**
     * 通过sn和类型和状态查找 检测历史
     *
     * @param sn       sn
     * @param category 类型
     * @param dealWay  状态
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Long countBySnAndCheckHistoryCategoryGreaterThanAndCheckHistoryDealWayAndDeleted(String sn, Integer category, Integer dealWay, Long deleted);


    /**
     * 通过子工单主键ID、工序主键ID、质检类型和逻辑删除获取质检历史明细
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主键ID
     * @param category       质检类型
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    List<CheckHistoryDetail> findByCheckHistorySubWorkSheetIdAndCheckHistoryStepIdAndCheckHistoryCategoryAndDeleted(Long subWorkSheetId, Long stepId, Integer category, Long deleted);

    /**
     * 通过工单主键ID、工序主键ID、质检类型和逻辑删除获取质检历史明细
     *
     * @param workSheetId 工单主键ID
     * @param stepId      工序主键ID
     * @param category    质检类型
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    List<CheckHistoryDetail> findByCheckHistoryWorkSheetIdAndCheckHistoryStepIdAndCheckHistoryCategoryAndDeleted(Long workSheetId, Long stepId, Integer category, Long deleted);


    /**
     * 获取检测历史记录中 全部的sn （包括已检测 以及未检测的）
     *
     * @param checkHistoryId 检测历史id
     * @param deleted        逻辑删除
     * @return List<String>
     */
    @Query("""
                    select chd.sn from CheckHistoryDetail  chd where chd.checkHistory.id = ?1 and chd.deleted = ?2
            """)
    @DataFilter(isSkip = true)
    List<String> findByCheckHistoryIdAndDeleted(Long checkHistoryId, Long deleted);
}
