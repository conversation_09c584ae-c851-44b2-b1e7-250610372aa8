package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.WsStepWorkCell;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
@Repository
public interface WsStepWorkCellRepository extends LogicDeleteableRepository<WsStepWorkCell>,
        EntityGraphJpaSpecificationExecutor<WsStepWorkCell>, EntityGraphJpaRepository<WsStepWorkCell, Long> {


    /**
     * 根据子工单获取对应的定制工序指定工位
     * @param subWorkSheetId 子工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/10
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位列表
     */
    @DataFilter(isSkip = true)
    List<WsStepWorkCell> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId,Long deleted);

    /**
     * 根据工单获取对应的定制工序指定工位
     * @param workSheetId 工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/10
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位列表
     */
    @DataFilter(isSkip = true)
    List<WsStepWorkCell> findByWorkSheetIdAndDeleted(Long workSheetId,Long deleted);

    /**
     * 根据子工单主键id，工序主键id，工位主键id 获取唯一的子工单工序指定工位信息
     * @param subWorkSheetId  子工单主键id
     * @param stepId 工序主键id
     * @param workCellId 工位主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/11
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位
     */
    @DataFilter(isSkip = true)
    Optional<WsStepWorkCell> findBySubWorkSheetIdAndStepIdAndWorkCellIdAndDeleted(Long subWorkSheetId, Long stepId, Long workCellId, Long deleted);

    /**
     * 根据工单主键id，工序主键id，工位主键id 获取唯一的工单工序指定工位信息
     * @param workSheetId  工单主键id
     * @param stepId 工序主键id
     * @param workCellId 工位主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/11
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位
     */
    @DataFilter(isSkip = true)
    Optional<WsStepWorkCell> findByWorkSheetIdAndStepIdAndWorkCellIdAndDeleted(Long workSheetId, Long stepId, Long workCellId, Long deleted);

    /**
     * 根据子工单工序 获取指定的工位信息
     * @param subWorkSheetId 子工单主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/12
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位列表
     */
    @DataFilter(isSkip = true)
    List<WsStepWorkCell> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId,Long stepId,Long deleted);

    /**
     * 根据工单工序 获取指定的工位信息
     * @param workSheetId 工单主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/8/12
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.WsStepWorkCell> (子)工单定制工序指定工位列表
     */
    @DataFilter(isSkip = true)
    List<WsStepWorkCell> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId,Long stepId,Long deleted);

    /**
     * 逻辑删除子工单对应的工序指定工位信息
      * @param subWorkSheetId 子工单主键id
     * <AUTHOR>
     * @date  2022/8/16
     * @return void
     */
    @Modifying
    @Query("update WsStepWorkCell set deleted = id where subWorkSheet.id = ?1 and deleted = 0")
    void logicBySubWorkSheetId(Long subWorkSheetId);
}
