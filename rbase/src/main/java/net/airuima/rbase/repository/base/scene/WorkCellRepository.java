package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkCellRepository extends LogicDeleteableRepository<WorkCell>,
        EntityGraphJpaSpecificationExecutor<WorkCell>, EntityGraphJpaRepository<WorkCell, Long> {

    /**
     * @param ids 工位主键id集合
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     */
    @Override
    @Query("from WorkCell where deleted = 0L and id in ?1")
    @FetchMethod
    @DataFilter(isSkip = true)
    List<WorkCell> findAllById(Iterable<Long> ids);

    /**
     * 通过名称和编码模糊查询工位
     *
     * @param text     工位名称或编码
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.scene.WorkCell> 工位分页
     */
    @Query("select  wc from WorkCell wc where wc.deleted = 0L and wc.isEnable=?2 and (" +
            "(coalesce(?1, null) is null or wc.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or wc.name like concat('%',?1,'%')))")
    @FetchMethod
    @EntityGraph("workCellEntityGraph")
    Page<WorkCell> findByNameOrCode(String text,Boolean enable, Pageable pageable);

    /**
     * 通过主键id查找工位
     *
     * @param id      主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.WorkCell> 工位
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WorkCell> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过IP获取工位
     * <AUTHOR>
     * @param ip 工位IP
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.WorkCell> 工位
     * @date 2021-07-23
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    @EntityGraph(value = "workCellEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    Optional<WorkCell> findByIpAndDeleted(String ip,Long deleted);

    /**
     * 通过工站ID获取工位列表
     * <AUTHOR>
     * @param workStationId 工站主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     * @date 2021-04-12
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WorkCell> findByWorkStationIdAndDeletedOrderByOrderNumberAsc(Long workStationId,Long deleted);

    /**
     * 通过生产线主键ID获取工位列表
     * <AUTHOR>
     * @param workLineId 生产线主键Id
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     * @date 2021-05-08
     **/
    @FetchMethod
    @EntityGraph(value = "workCellEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<WorkCell> findByWorkLineIdAndDeletedOrderByOrderNumberAsc(Long workLineId,Long deleted);

    /**
     * 通过工位编码获取工位记录
     * @param code 工位编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.WorkCell> 工位
     */
    @DataFilter(isSkip = true)
    Optional<WorkCell> findByCodeAndDeleted(String code,Long deleted);



    /**
     * 通过工位编码列表获取工位列表
     * @param codes 工位编码列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     */
    @DataFilter(isSkip = true)
    List<WorkCell> findByCodeInAndDeleted(List<String> codes,Long deleted);
}
