package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单领料明细表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface WsMaterialBatchRepository extends LogicDeleteableRepository<WsMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<WsMaterialBatch>, EntityGraphJpaRepository<WsMaterialBatch, Long> {
    /**
     * 查询工单批次领料明细
     *
     * @param workSheetId 工单id
     * @param batch       批次
     * @param materialId  物料ID
     * @param deleted     逻辑删除字段
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细
     */
    @DataFilter(isSkip = true)
    Optional<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(Long workSheetId, Long materialId, String batch, Long deleted);

    /**
     * 根据总工单主键ID获取剩余数量大于0的领料明细
     *
     * @param workSheetId   总工单主键ID
     * @param compareNumber 比较数量
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndLeftNumberIsGreaterThanAndDeleted(Long workSheetId, Double compareNumber, Long deleted);

    /**
     * 通过总工单ID获取领料记录条数
     *
     * @param wsId    总工单ID
     * @param deleted 删除标识
     * @return java.lang.Long  领料记录条数
     * <AUTHOR>
     * @date 2021-01-15
     **/
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndDeleted(Long wsId, Long deleted);

    /**
     * 通过总工单主键ID，物料主键ID列表获取总剩余数量
     *
     * @param wsId           总工单主键ID
     * @param materialIdList 物料主键ID列表
     * @param deleted        逻辑删除
     * @return java.math.BigDecimal  总剩余数量
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wb.leftNumber) from WsMaterialBatch wb where wb.workSheet.id=?1 and wb.materialId in(?2) and wb.deleted=?3")
    BigDecimal sumLeftNumberByWorkSheetIdAndMaterialIdInAndDeleted(Long wsId, List<Long> materialIdList, Long deleted);

    /**
     * 通过总工单主键ID，物料ID列表获取库存数据
     * <AUTHOR>
     * @param wsId 总工单主键ID
     * @param materialIdList 物料主键ID列表
     * @param compareNumber 比较数量
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     * @date 2021-03-15
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeleted(Long wsId,List<Long> materialIdList,Double compareNumber,Long deleted);

    /**
     * 更新领料和剩余数量
     *
     * @param number         领料总数
     * @param leftNumber     剩余总数
     * @param id             领料明细主键id
     * @param originalNumber 原始领料总数
     */
    @Modifying(clearAutomatically = true)
    @Query("update WsMaterialBatch set number = ?1,leftNumber = ?2 where id = ?3 and number =?4")
    void updateLeftNumber(double number, double leftNumber, Long id, double originalNumber);

    /**
     * 根据工单领料主键id获取对应entity
     * @param wsMaterialBatchId 工单领料批次主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/24
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WsMaterialBatch> findByIdAndDeleted(Long wsMaterialBatchId,Long deleted);

    /**
     * 通过 工单主键id，物料主键id，批次 获取 物料剩余数量升序（从小到大）工单库存信息
     * @param workSheetId 工单主键id
     * @param materialId 物料主键id
     * @param batch 批次
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/24
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndBatchAndDeletedOrderByLeftNumberAsc(Long workSheetId,Long materialId,String batch,Long deleted);

    /**
     * 通过 工单主键id，物料主键id， 获取 物料剩余数量升序（从小到大）工单库存信息
     * @param workSheetId 工单主键id
     * @param materialId 物料主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/24
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndDeletedOrderByLeftNumberAsc(Long workSheetId,Long materialId,Long deleted);

    /**
     *
     * @param wsId 工单主键ID
     * @param materialIdList 物料主键ID列表
     * @param compareNumber 比较数量
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     */
    @DataFilter(isSkip = true)
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeletedOrderByLeftNumberDesc(Long wsId,List<Long> materialIdList,Double compareNumber,Long deleted);


    /**
     * 通过工单主键ID及物料主键ID获取剩余库存大于0的升序列表
     * @param workSheetId 工单主键ID
     * @param materialId 物料主键ID
     * @param compareNumber 比较数量
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     */
    @DataFilter(isSkip = true)
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndLeftNumberIsGreaterThanAndDeletedOrderByLeftNumberAsc(Long workSheetId,Long materialId,Double compareNumber,Long deleted);

    /**
     * 通过工单主键id 获取对应的工单领料记录
     * @param wsId 工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/3/25
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndDeleted(Long wsId,Long deleted);

    /**
     * 通过 工单主键id，线边仓主键id，物料主键id，批次 获取工单库存信息
     * @param workSheetId 工单主键id
     * @param warehouseId 线边仓主键id
     * @param materialId 物料主键id
     * @param batch 批次
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/9/13
     * @return net.airuima.rbase.domain.procedure.material.WsMaterialBatch 工单领料明细
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    WsMaterialBatch findByWorkSheetIdAndWarehouseIdAndMaterialIdAndBatchAndDeleted(Long workSheetId,Long warehouseId,Long materialId,String batch,Long deleted);

    /**
     * 通过工单主键id、物料主键id获取对应的工单领料记录
     * @param wsId 工单主键id
     * @param materialId 物料主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch> 工单领料明细列表
     * <AUTHOR>
     * @date 2023/3/25
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsMaterialBatch> findByWorkSheetIdAndMaterialIdAndDeleted(Long wsId, Long materialId, Long deleted);

    /**
     * 通过物料ID集合获取工单领料记录
     *
     * @param materialIdList 物料ID集合
     * @param deleted 逻辑删除
     * @since 1.8.1
     */
    @FetchMethod
    List<WsMaterialBatch> findByMaterialIdInAndDeleted(List<Long> materialIdList, Long deleted);

    /**
     * 通过物料批次获取领料记录
     */
    @FetchMethod
    List<WsMaterialBatch> findByBatchAndDeleted(String batch, Long deleted);
}
