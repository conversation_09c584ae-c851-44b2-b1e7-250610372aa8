package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情不良明细表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerDetailUnqualifiedItemRepository extends LogicDeleteableRepository<ContainerDetailUnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<ContainerDetailUnqualifiedItem>, EntityGraphJpaRepository<ContainerDetailUnqualifiedItem, Long> {

    /**
     * 根据容器详情主键ID列表，不良项目主键ID列表获取所有不良总数
     *
     * @param containerDetailIds 容器详情主键ID列表
     * @param unqualifiedItemIds 不良项目主键ID列表
     * @param deleted            删除标识
     * @return java.lang.Long  不良总数
     * <AUTHOR>
     * @date 2021-01-15
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(cui.number) from ContainerDetailUnqualifiedItem cui where cui.containerDetail.id in (?1) and cui.unqualifiedItem.id in (?2) and cui.deleted=?3")
    Long findSumNumberByContainerDetailIdInAndUnqualifiedItemInAndDeleted(List<Long> containerDetailIds, List<Long> unqualifiedItemIds, Long deleted);

    /**
     *
     * 根据容器详情主键ID，不良项目主键ID列表获取所有不良总数
     * @param containerDetailId 容器详情主键ID
     * @param unqualifiedItemIds  不良项目主键ID列表
     * @param deleted  逻辑删除
     * @return java.lang.Long 不良总数
     */
    @DataFilter(isSkip = true)
    @Query("select sum(cui.number) from ContainerDetailUnqualifiedItem cui where cui.containerDetail.id =?1 and cui.unqualifiedItem.id in (?2) and cui.deleted=?3")
    Long findSumNumberByContainerDetailIdAndUnqualifiedItemInAndDeleted(Long containerDetailId, List<Long> unqualifiedItemIds, Long deleted);

    /**
     * 根据容器详情主键ID获取不良列表
     *
     * @param containerDetailId 容器详情主键ID
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem> 容器工作详情不良明细列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetailUnqualifiedItem> findByContainerDetailIdAndDeleted(Long containerDetailId, Long deleted);

    /**
     * 根据容器详情主键ID获取不良列表
     *
     * @param containerDetailId 容器详情主键ID
     * @param unqualifiedItemId 不良项目主键IDs
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem> 容器工作详情不良明细列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetailUnqualifiedItem> findByContainerDetailIdAndUnqualifiedItemIdInAndDeleted(Long containerDetailId, List<Long> unqualifiedItemId,Long deleted);

    /**
     * 根据容器详情主键ID、不良项目主键ID获取不良
     * @param containerDetailId 容器详情主键ID
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem> 容器工作详情不良明细
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetailUnqualifiedItem> findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(Long containerDetailId, Long unqualifiedItemId,Long deleted);



    /**
     * 根据容器详情主键ID列表删除不良信息
     * <AUTHOR>
     * @param containerDetailId    容器详情主键ID列表
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update ContainerDetailUnqualifiedItem cui set cui.deleted=cui.id where cui.containerDetail.id=?1")
    void batchDeleteByContainerDetailId(Long containerDetailId);

    /**
     * 根据容器详情主键id列表 获取不良列表信息
     * @param containerDetailIds 容器详情主键id列表
     * @param deleted   逻辑删除
     * <AUTHOR>
     * @date  2022/9/28
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem> 容器工作详情不良明细列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetailUnqualifiedItem> findByContainerDetailIdInAndDeleted(List<Long> containerDetailIds,Long deleted);
}
