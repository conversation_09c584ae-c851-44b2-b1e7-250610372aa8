package net.airuima.rbase.repository.base.pedigree;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品型号不良种类流程框图Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeReworkWorkFlowRepository extends LogicDeleteableRepository<PedigreeReworkWorkFlow>,
        EntityGraphJpaSpecificationExecutor<PedigreeReworkWorkFlow>, EntityGraphJpaRepository<PedigreeReworkWorkFlow, Long> {

    /**
     * 通过产品谱系主键id查询产品谱系不良种类流程框图
     *
     * @param pedigreeId 产品谱系主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<PedigreeReworkWorkFlow> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    
    /**
     * 根据产品谱系不良种类工艺路线主键id和删除标记查询产品谱系不良种类工艺路线
     *
     * @param id      产品谱系不良种类工艺路线主键
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类工艺路线
     */
    @FetchMethod
    @Query("select p from PedigreeReworkWorkFlow p where p.id = ?1 and p.deleted = ?2")
    @DataFilter(isSkip = true)
    Optional<PedigreeReworkWorkFlow> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 通过产品谱系主键id和客户主键id查询产品型号不良种类流程框图
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId   客户主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @FetchMethod
    @Query("select p from PedigreeReworkWorkFlow p where p.pedigree.id = ?1 and  ( p.clientId is null or p.clientId = ?2)  and p.deleted = ?3")
    @DataFilter(isSkip = true)
    List<PedigreeReworkWorkFlow> findByPedigreeIdAndClientIdAndDeleted(Long pedigreeId, Long clientId, Long deleted);


    /**
     * 通过产品谱系主键id和客户主键id查询产品型号不良种类流程框图
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId   客户主键id
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @FetchMethod
    @Query("select p from PedigreeReworkWorkFlow p where p.pedigree.id = ?1 and  ( p.clientId is null or p.clientId = ?2) and p.isEnable = ?3 and p.deleted = ?4")
    @DataFilter(isSkip = true)
    List<PedigreeReworkWorkFlow> findByPedigreeIdAndClientIdAndIsEnableAndDeleted(Long pedigreeId, Long clientId, boolean isEnable, Long deleted);


    /**
     * 根据产品谱系主键ID和客户主键ID和是否启用和删除标记查询产品谱系不良种类流程框图集合
     * @param pedigreeId 产品谱系主键ID
     * @param clientId 客户主键ID
     * @param isEnable 是否启用
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @FetchMethod
    @Query("select p from PedigreeReworkWorkFlow p where p.pedigree.id = ?1 and  ( ?2 is null or  p.clientId = ?2) and p.isEnable = ?3 and p.workFlow.isEnable=?3 and p.deleted = ?4")
    @DataFilter(isSkip = true)
    List<PedigreeReworkWorkFlow> findAllByPedigreeIdAndClientIdAndIsEnableAndDeleted(Long pedigreeId, Long clientId, boolean isEnable, Long deleted);

    /**
     * 模糊查询产品谱系绑定流程框图
     *
     * @param pedigreeId 产品谱系主键id
     * @param keyword    流程框图编码或名称
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select prw from PedigreeReworkWorkFlow prw where prw.deleted = 0L and prw.pedigree.id = ?1 and (prw.workFlow.name like concat('%',?2,'%') or prw.workFlow.code like concat('%',?2,'%') ) and ( prw.clientId is null or prw.clientId = ?3) ")
    List<PedigreeReworkWorkFlow> findWorkFlowByPedigreeIdAndKeywordAndClientId(Long pedigreeId, String keyword, Long clientId);


    /**
     * 通过工艺路线主键ID获取谱系列表
     *
     * @param workFlowId 工艺路线主键ID
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     * <AUTHOR>
     * @date 2021-05-06
     **/
    @FetchMethod
    List<PedigreeReworkWorkFlow> findByWorkFlowIdAndDeleted(Long workFlowId, Long deleted);

    /**
     * 通过产品谱系主键ID、工艺路线主键ID及不良组别主键ID删除关联关系
     *
     * @param pedigreeId         谱系主键ID
     * @param unqualifiedGroupId 不良组别主键ID
     * @param workFlowId         工艺路线主键ID
     * @param clientId           客户主键id
     * @return void
     * <AUTHOR>
     * @date 2021-05-10
     **/
    @Modifying
    @Query("update  PedigreeReworkWorkFlow prwf set prwf.deleted=prwf.id where prwf.pedigree.id=?1 and prwf.unqualifiedGroup.id=?2 and prwf.workFlow.id=?3 and ( prwf.clientId is null or prwf.clientId = ?4)  ")
    void deleteByPedigreeIdAndUnqualifiedGroupIdAndWorkFlowIdAndClientId(Long pedigreeId, Long unqualifiedGroupId, Long workFlowId, Long clientId);

    /**
     * 根据产品谱系主键ID获取工艺路线列表
     * @param pedigreeId 产品谱系主键ID
     * @param clientId   客户主键id
     * @param deleted    删除标志
     * @param isEnable   是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线列表
     * <AUTHOR>
     * @date 2024/1/18
     */
    @DataFilter(isSkip = true)
    @Query("select prw.workFlow from PedigreeReworkWorkFlow prw where prw.pedigree.id=?1 and prw.isEnable=?2 and  ( prw.clientId is null or prw.clientId = ?3)  and prw.deleted=?4")
    List<WorkFlow> findWorkFlowByPedigreeIdAndClientIdDeleted(Long pedigreeId, boolean isEnable, Long clientId, Long deleted);

    /**
     * 根据产品谱系主键id和工艺路线主键id和不良组别主键id和客户主键id和删除标记查找产品型号不良种类流程框图
     *
     * @param pedigreeId         产品谱系主键id
     * @param workFlowId         工艺路线主键id
     * @param unqualifiedGroupId 不良种类主键id
     * @param clientId           客户主键id
     * @param deleted            删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品型号不良种类流程框图
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select p from PedigreeReworkWorkFlow  p where ((?1 is null and  p.pedigree is null) or p.pedigree.id=?1) and ((?2 is null and  p.workFlow is null) or p.workFlow.id = ?2) and ( (?3 is null and  p.unqualifiedGroup.id is null) or p.unqualifiedGroup.id = ?3) and ( (?4 is null and  p.clientId is null) or p.clientId = ?4) and p.deleted=?5")
    Optional<PedigreeReworkWorkFlow> findByPedigreeIdAndWorkFlowIdAndUnqualifiedGroupIdAndClientIdAndDeleted(Long pedigreeId, Long workFlowId, Long unqualifiedGroupId, Long clientId, Long deleted);


    /**
     * 根据产品谱系主键id列表和是否启用和不良组别主键id和客户主键id和删除标记查找产品型号不良种类流程框图集合
     *
     * @param pedigreeId         产品谱系主键id列表
     * @param enable             是否启用
     * @param unqualifiedGroupId 不良种类主键id
     * @param clientId           客户主键id
     * @param deleted            删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeReworkWorkFlow p where p.pedigree.id in(?1) and p.unqualifiedGroup.id = ?2 and (p.clientId is null or p.clientId = ?3)  and p.isEnable = ?4 and p.deleted = ?5")
    List<PedigreeReworkWorkFlow> findByPedigreeIdInAndUnqualifiedGroupIdAndClientIdAndIsEnableAndDeleted(List<Long> pedigreeId, Long unqualifiedGroupId, Long clientId, Boolean enable, Long deleted);

    /**
     * 通过产品谱系ID列表及启用获取数据列表
     * @param pedigreeIdList 产品谱系ID列表
     * @param enable 是否启用
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良种类流程框图集合
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeReworkWorkFlow> findByPedigreeIdInAndIsEnableAndDeleted(List<Long> pedigreeIdList, Boolean enable, Long deleted);


}
