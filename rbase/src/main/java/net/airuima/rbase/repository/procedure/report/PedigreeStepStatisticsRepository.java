package net.airuima.rbase.repository.procedure.report;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.report.PedigreeStepStatistics;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产品谱系工序生产在制统计Repository
 * <AUTHOR>
 * @date 2023/12/19
 */
@Repository
public interface PedigreeStepStatisticsRepository extends LogicDeleteableRepository<PedigreeStepStatistics>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepStatistics>, EntityGraphJpaRepository<PedigreeStepStatistics, Long> {

    /**
     * 通过产品谱系主键ID、产线主键ID、工序主键ID及逻辑删除获取唯一产品谱系工序生产在制统计记录
     * @param pedigreeId 产品谱系主键ID
     * @param workLineId 产线主键ID
     * @param stepId 工序主键ID
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.report.PedigreeStepStatistics> 产品谱系工序生产在制统计
     * <AUTHOR>
     * @date 2023/12/19
     */
    @DataFilter(isSkip = true)
    Optional<PedigreeStepStatistics> findByPedigreeIdAndWorkLineIdAndStepIdAndDeleted(long pedigreeId,long workLineId,long stepId,long deleted);


    /**
     * 初始化除在制数据外的其他在线看板数据
     */
    @Modifying
    @Query("update PedigreeStepStatistics pss set pss.inputNumber=0,pss.unqualifiedNumber=0,pss.qualifiedNumber=0,pss.transferNumber=0 where  pss.deleted=0")
    void initPedigreeStepStatisticsNumber();

    /**
     * 初始化产品谱系工序在制数据
     */
    @Modifying
    @Query("update PedigreeStepStatistics pss set pss.onlineNumber=(select COALESCE(sum(sst.onlineNumber),0) from WorkSheetStepStatistics sst where sst.pedigreeStepStatistics.id=pss.id) where  pss.deleted=0")
    void initPedigreeStepStatisticsOnlineNumber();
}
