package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产详情物料批次Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SnWorkDetailMaterialBatchRepository extends LogicDeleteableRepository<SnWorkDetailMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<SnWorkDetailMaterialBatch>, EntityGraphJpaRepository<SnWorkDetailMaterialBatch, Long> {

    /**
     * 通过物料批次查询SN物料数据
     *
     * @param materialBatch 物料批次
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch> 单支生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailMaterialBatch> findByMaterialBatchAndDeleted(String materialBatch, Long deleted);

    /**
     * 通过sn工作详情主键ID及逻辑删除获取物料信息记录
     *
     * @param snWorkDetailId SN工作详情主键ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch> 单支生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<SnWorkDetailMaterialBatch> findBySnWorkDetailIdAndDeleted(Long snWorkDetailId, Long deleted);

    /**
     * 通过SN详情ID列表获取物料批次列表
     *
     * @param snWorkDetailIdList SN工作详情主键ID列表
     * @param deleted            逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch> 单支生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    List<SnWorkDetailMaterialBatch> findBySnWorkDetailIdInAndDeleted(List<Long> snWorkDetailIdList, Long deleted);

    /**
     * 根据物料批次、开始日期及结束日期查询数据
     *
     * @param materialBatch 物料批次
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param deleted       逻辑删除
     * @param pageable      分页查询
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch> 单支生产详情物料批次分页
     * <AUTHOR>
     * @date 2021-03-31
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<SnWorkDetailMaterialBatch> findByMaterialBatchAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(String materialBatch, Instant startDate, Instant endDate, Long deleted, Pageable pageable);


    /**
     * 根据SN工作详情主键ID删除物料批次信息
     *
     * @param snWorkDetailId SN详情主键ID
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update SnWorkDetailMaterialBatch sdmb set sdmb.deleted=sdmb.id where sdmb.snWorkDetail.id=?1")
    void batchDeleteBySnWorkDetailId(Long snWorkDetailId);


    /**
     * 根据容器详情主键id 物料主键id 批次 获取 sn 物料批次使用记录列表
     *
     * @param containerDetailId 容器详情主键id
     * @param materialId        物料主键id
     * @param batch             批次
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO> 物料批次使用记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO(sdmb.snWorkDetail.id,sdmb.snWorkDetail.containerDetail.containerCode,sdmb.snWorkDetail.sn,sdmb.snWorkDetail.operatorId,sdmb.snWorkDetail.workCell.code," +
            " sdmb.snWorkDetail.workCell.name,sdmb.materialId,sdmb.materialBatch,sdmb.number,sdmb.snWorkDetail.result,sdmb.supplierId) " +
            "from SnWorkDetailMaterialBatch sdmb where sdmb.snWorkDetail.containerDetail.id = ?1 and " +
            "(?3 is null or sdmb.materialBatch = ?3) and " +
            "sdmb.materialId = ?2 and sdmb.deleted = ?4")
    List<ProcessStepMaterialBatchDTO> findBySnWorkDetailContainerDetailIdAndMaterialIdAndMaterialBatchAndDeleted(Long containerDetailId, Long materialId, String batch, Long deleted);

    /**
     * 根据 子工单主键id 工序主键id 物料主键id 批次 获取 sn 物料批次使用记录列表
     *
     * @param stepId         工序主键id
     * @param subWorkSheetId 子工单主键id
     * @param materialId     物料主键id
     * @param batch          批次
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO> 物料批次使用记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO(sdmb.snWorkDetail.id,sdmb.snWorkDetail.sn,sdmb.snWorkDetail.operatorId,sdmb.snWorkDetail.workCell.code," +
            " sdmb.snWorkDetail.workCell.name,sdmb.materialId,sdmb.materialBatch,sdmb.number,sdmb.snWorkDetail.result,sdmb.supplierId) " +
            " from SnWorkDetailMaterialBatch sdmb where sdmb.snWorkDetail.step.id = ?1 and sdmb.snWorkDetail.subWorkSheet.id = ?2 and" +
            "(?4 is null or sdmb.materialBatch = ?4) and " +
            "sdmb.materialId = ?3 and sdmb.deleted = ?5")
    List<ProcessStepMaterialBatchDTO> findBySubWsStepMaterialIdAndMaterialBatchAndDeleted(Long stepId, Long subWorkSheetId, Long materialId, String batch, Long deleted);


    /**
     * 根据 工单主键id 工序主键id 物料主键id 批次 获取 sn 物料批次使用记录列表
     *
     * @param stepId     工序主键id
     * @param workSheet  工单主键id
     * @param materialId 物料主键id
     * @param batch      批次
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO> 物料批次使用记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO(sdmb.snWorkDetail.id,sdmb.snWorkDetail.sn,sdmb.snWorkDetail.operatorId,sdmb.snWorkDetail.workCell.code," +
            " sdmb.snWorkDetail.workCell.name,sdmb.materialId,sdmb.materialBatch,sdmb.number,sdmb.snWorkDetail.result,sdmb.supplierId) " +
            "from SnWorkDetailMaterialBatch sdmb where sdmb.snWorkDetail.step.id = ?1 and sdmb.snWorkDetail.workSheet.id = ?2 and" +
            "(?4 is null or sdmb.materialBatch = ?4) and " +
            "sdmb.materialId = ?3 and sdmb.deleted = ?5")
    List<ProcessStepMaterialBatchDTO> findByWsStepMaterialIdAndMaterialBatchAndDeleted(Long stepId, Long workSheet, Long materialId, String batch, Long deleted);

    /**
     * 通过物料批次号获取第一个记录
     * @param serialNumber 批次号
     * @param deleted 逻辑删除
     * @return List<SnWorkDetailMaterialBatch>
     */
    @DataFilter(isSkip = true)
    SnWorkDetailMaterialBatch findTop1ByMaterialBatchInAndDeleted(List<String> serialNumbers,Long deleted);



    /**
     * 获取子工单列表 指定SN的物料或者批次信息
     * @param serialNumber 子工单列表
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(ws.serialNumber,sub.serialNumber,sdm.materialId,sdm.materialBatch) from SnWorkDetailMaterialBatch sdm " +
            "left join SubWorkSheet sub on sub.id = sdm.snWorkDetail.subWorkSheet.id " +
            "left join WorkSheet ws on ws.id = sub.workSheet.id " +
            " where sdm.snWorkDetail.subWorkSheet.serialNumber in (?1) and (?2 is null or sdm.materialId = ?2) " +
            "and (?3 is null  or sdm.materialBatch = ?3) and sdm.snWorkDetail.sn = ?4 and sdm.deleted = 0 " +
            "group by sdm.snWorkDetail.subWorkSheet.id,sdm.materialId,sdm.materialBatch,sdm.snWorkDetail.sn")
    List<PackRelationDataDTO> findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(List<String> serialNumbers, Long materialId, String materialBatch, String sn);



    /**
     * 获取工单列表 指定SN的物料或者批次信息
     * @param serialNumber 子工单列表
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(sdm.snWorkDetail.workSheet.serialNumber,sdm.materialId,sdm.materialBatch) " +
            "from SnWorkDetailMaterialBatch sdm where sdm.snWorkDetail.workSheet.serialNumber = ?1 and (?2 is null or sdm.materialId = ?2) " +
            "and (?3 is null  or sdm.materialBatch = ?3) and sdm.snWorkDetail.sn = ?4 and sdm.deleted = 0 " +
            "group by sdm.snWorkDetail.workSheet.id,sdm.materialId,sdm.materialBatch,sdm.snWorkDetail.sn")
    List<PackRelationDataDTO> findPackRelationDataByWsAndMaterialIdAndMaterialBatch(String serialNumber, Long materialId, String materialBatch, String sn);
}
