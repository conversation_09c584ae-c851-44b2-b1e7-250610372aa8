package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史Repository
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface CheckHistoryRepository extends LogicDeleteableRepository<CheckHistory>,
        EntityGraphJpaSpecificationExecutor<CheckHistory>, EntityGraphJpaRepository<CheckHistory, Long> {
    /**
     * 根据检测类型、项目类型主键id、工位主键id、删除标识倒序查询最新一条检测历史
     *
     * @param category   检测类型
     * @param varietyId  项目类型主键id
     * @param workCellId 工位主键id
     * @param deleted    删除标识
     * @return net.airuima.rbase.domain.procedure.quality.CheckHistory 检测历史
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("""
            select c from CheckHistory c
            where c.category = ?1 and ((?2 is null and c.varietyId is null) or (c.varietyId = ?2)) and c.workCell.id = ?3 and c.deleted = ?4  ORDER BY c.id desc limit 1""")
    CheckHistory findTopOneByCategoryAndVarietyIdAndWorkCellIdAndDeletedOrderByCreatedDateDesc(Integer category, Long varietyId, Long workCellId, Long deleted);

    /**
     * 根据检测类型、项目类型主键id、工位主键id、删除标识倒序查询最新一条检测历史
     *
     * @param category   检测类型
     * @param varietyId  项目类型主键id
     * @param workCellId 工位主键id
     * @param deleted    删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkCellIdAndCategoryAndVarietyIdAndDeletedOrderByIdDesc(Long workCellId, Integer category, Long varietyId, Long deleted);

    /**
     * 通过主键id 获取检测历史记录
     *
     * @param id      检测历史主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过子工单主键ID和类型和状态查找 检测历史
     *
     * @param subWorkSheetId 子工单主键ID
     * @param category       类型
     * @param status         状态
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1BySubWorkSheetIdAndContainerCodeIsNullAndSnIsNullAndCategoryGreaterThanAndStatusAndDeleted(Long subWorkSheetId, Integer category, Boolean status, Long deleted);


    /**
     * 通过工单主键ID和类型和状态查找 检测历史
     *
     * @param workSheetId 工单主键ID
     * @param category    类型
     * @param status      状态
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkSheetIdAndContainerCodeIsNullAndSnIsNullAndCategoryGreaterThanAndStatusAndDeleted(Long workSheetId, Integer category, Boolean status, Long deleted);

    /**
     * 通过子工单主键ID和容器编码和类型和状态查找 检测历史
     *
     * @param subWorkSheetId 子工单主键id
     * @param containerCode  容器编码
     * @param category       类型
     * @param dealWay         状态
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1BySubWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(Long subWorkSheetId, String containerCode, Integer category, Integer dealWay, Long deleted);

    /**
     * 通过工单主键ID和容器编码和类型和状态查找 检测历史
     *
     * @param workSheetId   工单主键id
     * @param containerCode 容器编码
     * @param category      类型
     * @param dealWay        状态
     * @param deleted       逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(Long workSheetId, String containerCode, Integer category, Integer dealWay, Long deleted);


    /**
     * 通过sn和类型和状态查找 检测历史
     *
     * @param sn             sn
     * @param category       类型
     * @param dealWay         状态
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1BySnAndCategoryGreaterThanAndDealWayAndDeleted(String sn, Integer category, Integer dealWay, Long deleted);


    /**
     * 根据流水号和逻辑删除获取唯一记录
     *
     * @param serialNumber 流水号
     * @param deleted      逻辑删除
     * @return CheckHistory
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findBySerialNumberAndDeleted(String serialNumber, Long deleted);

    /**
     * 通过子工单ID、质检类型、状态和逻辑删除统计数量
     *
     * @param subWorkSheetId 子工单ID
     * @param category       质检类型
     * @param dealWay         处理状态
     * @param deleted        逻辑删除
     * @return 数量
     */
    long countBySubWorkSheetIdAndCategoryInAndDealWayAndDeleted(Long subWorkSheetId, List<Integer> category, Integer dealWay, Long deleted);


    /**
     * 通过工单ID、质检类型、状态和逻辑删除统计数量
     *
     * @param workSheetId 工单ID
     * @param category    质检类型
     * @param dealWay      处理状态
     * @param deleted     逻辑删除
     * @return 数量
     */
    long countByWorkSheetIdAndCategoryInAndDealWayAndDeleted(Long workSheetId, List<Integer> category, Integer dealWay, Long deleted);


    /**
     * 通过子工单id 工序id 状态 检查历史类型 处理方式 获取 检查历史记录列表
     *
     * @param subWsId  子工单id
     * @param stepId   工序id
     * @param category 检查历史类型
     * @param dealWay  处理方式
     * @param deleted  逻辑删除
     * @return 检查历史记录列表
     */
    List<CheckHistory> findBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long subWsId, Long stepId,  List<Integer> category, Integer dealWay, Long deleted);

    /**
     * 通过工单id 工序id 状态 检查历史类型 处理方式 获取 检查历史记录列表
     *
     * @param wsId     工单id
     * @param stepId   工序id
     * @param category 检查历史类型
     * @param dealWay  处理方式
     * @param deleted  逻辑删除
     * @return 检查历史记录列表
     */
    List<CheckHistory> findByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long wsId, Long stepId,  List<Integer> category, Integer dealWay, Long deleted);


    /**
     * 通过子工单id 工序id 检查历史类型 处理方式 获取 检查历史记录数量
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param categorys      检查历史类型
     * @param dealWay        处理方式
     * @param deleted        逻辑删除
     * @return 检查历史记录数量
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    long countBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long subWorkSheetId, Long stepId, List<Integer> categorys, int dealWay, Long deleted);


    /**
     * 通过工单id 工序id 检查历史类型 处理方式 获取 检查历史记录数量
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param categorys   检查历史类型
     * @param dealWay     处理方式
     * @param deleted     逻辑删除
     * @return 检查历史记录数量
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    long countByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long workSheetId, Long stepId, List<Integer> categorys, int dealWay, Long deleted);


    /**
     * 通过子工单id 工序id 质检类型 项目类型 获取第一个检查历史记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param category       质检类型
     * @param varietyId      项目类型
     * @param deleted        逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeIsNullAndSnIsNullAndDeleted(Long subWorkSheetId, Long stepId, Integer category, Long varietyId, Long deleted);

    /**
     * 通过工单id 工序id 质检类型 项目类型 获取第一个检查历史记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param category    质检类型
     * @param varietyId   项目类型
     * @param deleted     逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeIsNullAndSnIsNullAndDeleted(Long workSheetId, Long stepId, Integer category, Long varietyId, Long deleted);

    /**
     * 通过子工单id 工序id 质检类型 项目类型 容器编码 获取第一个检查历史记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param category       质检类型
     * @param varietyId      项目类型
     * @param containerCode  容器编码
     * @param deleted        逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeAndDeleted(Long subWorkSheetId, Long stepId, Integer category, Long varietyId, String containerCode, Long deleted);

    /**
     * 通过工单id 工序id 质检类型 项目类型 容器编码 获取第一个检查历史记录
     *
     * @param workSheetId   工单id
     * @param stepId        工序id
     * @param category      质检类型
     * @param varietyId     项目类型
     * @param containerCode 容器编码
     * @param deleted       逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeAndDeleted(Long workSheetId, Long stepId, Integer category, Long varietyId, String containerCode, Long deleted);


    /**
     * 通过子工单id 工序id 质检类型 项目类型 sn 获取第一个检查历史记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param category       质检类型
     * @param varietyId      项目类型
     * @param sn             sn
     * @param deleted        逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndSnAndDeleted(Long subWorkSheetId, Long stepId, Integer category, Long varietyId, String sn, Long deleted);

    /**
     * 通过工单id 工序id 质检类型 项目类型 sn 获取第一个检查历史记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param category    质检类型
     * @param varietyId   项目类型
     * @param sn          sn
     * @param deleted     逻辑删除
     * @return 检查历史记录
     */
    CheckHistory findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndSnAndDeleted(Long workSheetId, Long stepId, Integer category, Long varietyId, String sn, Long deleted);

    /**
     * 通过id列表和逻辑删除获取检查历史记录
     *
     * @param ids     ids
     * @param deleted 逻辑删除
     * @return List<CheckHistory>
     * <AUTHOR>
     * @since 1.8.1
     */
    List<CheckHistory> findByIdInAndDeleted(List<Long> ids, Long deleted);

}
