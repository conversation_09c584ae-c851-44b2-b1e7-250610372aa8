package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 生产工序良率预警标准repository
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Repository
public interface StepWarningStandardRepository extends LogicDeleteableRepository<StepWarningStandard>,
        EntityGraphJpaSpecificationExecutor<StepWarningStandard>, EntityGraphJpaRepository<StepWarningStandard, Long> {

    /**
     * 通过产品谱系、工序及配置类型获取工序合格率预警标准
     *
     * @param pedigreeId 谱系主键ID
     * @param stepId     工序主键ID
     * @param workFlowId 工艺路线主键ID
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.StepWarningStandard> 生产工序预警标准
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @DataFilter(isSkip = true)
    Optional<StepWarningStandard> findByPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(Long pedigreeId, Long workFlowId,Long stepId, Long deleted);

    /**
     * 根据优先级配置查询生产工序预警标准
     * <AUTHOR>
     * @date 2022/10/24
     * @param priorityElementConfigId 优先级配置主键Id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.quality.StepWarningStandard  生产工序预警标准
     */
    @DataFilter(isSkip = true)
    StepWarningStandard findTop1ByPriorityElementConfigIdAndDeleted(Long priorityElementConfigId, Long deleted);

    /**
     * 返回匹配的生产良率预警标准集合，满足“预警条件”+“条件对象”，并根据优先级升序+产品谱系类型降序
     *
     * @param pedigreeId  产品谱系主键id
     * @param workSheetId 工单主键id
     * @param category    工单类型
     * @param stepGroupId 工序组主键id
     * @param stepId      工序主键id
     * @param workFlowId  工艺流程主键id
     * @param clientId    客户主键id
     * @param target      条件对象
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.quality.StepWarningStandard> 生产工序预警标准列表
     * <AUTHOR>
     * @date 2022/10/18
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select a from StepWarningStandard a  " +
            "where (a.pedigree is null or a.pedigree.id in (?1)) " +
            "and (a.workSheet is null or a.workSheet.id = ?2) " +
            "and (a.workSheetCategory is null or a.workSheetCategory = ?3) " +
            "and (a.stepGroup is null or a.stepGroup.id = ?4) " +
            "and (a.step is null or a.step.id = ?5) " +
            "and (a.workFlow is null or a.workFlow.id = ?6) " +
            "and (a.clientId is null or a.clientId = ?7) " +
            "and a.priorityElementConfig.target = ?8 and a.deleted = ?9")
    List<StepWarningStandard> findAllStandardByElementOrderByPriorityAndPedigree(List<Long> pedigreeId, Long workSheetId, Integer category, Long stepGroupId, Long stepId, Long workFlowId, Long clientId, Integer target, Long deleted);

    /**
     * 根据产品谱系id,工序id,工艺路线id,工序组id,工单id,客户id,工单类型,查询预警规则
     * <AUTHOR>
     * @date 2022/10/25 10:52
     * @param pedigreeId 产品谱系主键id
     * @param stepId 工序主键id
     * @param WorkFlowId 工艺路线主键id
     * @param StepGroupId 工序组主键id
     * @param WorkSheetId 工单主键id
     * @param ClientId 客户主键id
     * @param workSheetCategory 工单类型
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.quality.StepWarningStandard> 生产工序预警标准列表
     */
    @DataFilter(isSkip = true)
    @Query("select p from StepWarningStandard p where ((coalesce(?1, null) is null and  p.pedigree.id is null) or p.pedigree.id=?1) " +
            "and ( (coalesce(?3, null) is null and  p.workFlow.id is null) or p.workFlow.id = ?3) " +
            "and ((coalesce(?2, null) is null and  p.step.id is null) or p.step.id = ?2) " +
            "and ( (coalesce(?4, null) is null and  p.stepGroup.id is null) or p.stepGroup.id = ?4) " +
            "and  ( (coalesce(?5, null) is null and  p.workSheet.id is null) or p.workSheet.id = ?5) " +
            "and  ( (coalesce(?6, null) is null and  p.clientId is null) or p.clientId = ?6) " +
            "and  ( (coalesce(?7, null) is null and  p.workSheetCategory is null) or p.workSheetCategory = ?7) and p.deleted = ?8 ")
    StepWarningStandard findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndDeleted(Long pedigreeId, Long stepId, Long WorkFlowId, Long StepGroupId, Long WorkSheetId, Long ClientId, Integer workSheetCategory, Long deleted);

    /**
     * 根据预警规则id查询预警规则
     * <AUTHOR>
     * @date 2022/10/25 18:21
     * @param id 预警规则主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.quality.StepWarningStandard> 生产工序预警标准
     */
    @DataFilter(isSkip = true)
    Optional<StepWarningStandard> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 逻辑删除生产工序预警标准
     * <AUTHOR>
     * @date 2022/10/27
     * @param id 生产工序预警标准主键id
     */
    @Modifying
    @Query(value = "update StepWarningStandard sws set sws.deleted=sws.id where sws.id=?1 and sws.deleted=0")
    void deletedById(Long id);
}
