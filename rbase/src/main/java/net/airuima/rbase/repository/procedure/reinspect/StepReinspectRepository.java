package net.airuima.rbase.repository.procedure.reinspect;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检历史Repository
 * <AUTHOR>
 */
@Repository
public interface StepReinspectRepository extends LogicDeleteableRepository<StepReinspect>,
        EntityGraphJpaSpecificationExecutor<StepReinspect>, EntityGraphJpaRepository<StepReinspect, Long> {

    /**
     * 通过子工单ID、处理状态及逻辑删除获取第一个记录
     * @param subWorkSheetId 子工单ID
     *  @param status 处理状态
     * @param deleted 逻辑删除
     * @return StepReinspect
     */
    StepReinspect findTop1BySubWorkSheetIdAndStatusAndDeleted(Long subWorkSheetId,Boolean status,Long deleted);


    /**
     * 通过工单ID、处理状态及逻辑删除获取第一个记录
     * @param workSheetId 工单ID
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return StepReinspect
     */
    StepReinspect findTop1ByWorkSheetIdAndStatusAndDeleted(Long workSheetId,Boolean status,Long deleted);


    /**
     * 通过容器ID、处理状态及逻辑删除获取第一个记录
     * @param containerId 容器ID
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return StepReinspect
     */
    StepReinspect findTop1ByContainerDetailContainerIdAndStatusAndDeleted(Long containerId,Boolean status,Long deleted);


    /**
     * 通过ID列表获取数据列表
     * @param ids ID列表
     * @return List<StepReinspect>
     */
    List<StepReinspect> findByIdInAndDeleted(Set<Long> ids,Long deleted);


    /**
     * 根据子工单ID列表、工序ID列表、处理状态及逻辑删除获取复检历史数据列表
     * @param subWorkSheetId 子工单ID
     * @param stepIdList 工序ID列表
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return List<StepReinspect>
     */
    List<StepReinspect> findBySubWorkSheetIdAndStepIdInAndStatusAndDeleted(Long subWorkSheetId,List<Long> stepIdList,Boolean status,Long deleted);


    /**
     * 根据工单ID列表、工序ID列表、处理状态及逻辑删除获取复检历史数据列表
     * @param workSheetId 工单ID
     * @param stepIdList 工序ID列表
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return List<StepReinspect>
     */
    List<StepReinspect> findByWorkSheetIdAndStepIdInAndStatusAndDeleted(Long workSheetId,List<Long> stepIdList,Boolean status,Long deleted);

    /**
     * 根据容器详情记录ID、处理状态获取数据列表
     * @param containerDetailId 容器详情ID
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return List<StepReinspect>
     */
    List<StepReinspect> findByContainerDetailIdAndStatusAndDeleted(Long containerDetailId,Boolean status,Long deleted);

    /**
     * 根据SN及处理状态获取第一个记录
     * @param sn SN
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return StepReinspect
     */
    StepReinspect findTop1BySnWorkStatusSnAndStatusAndDeleted(String sn,Boolean status,Long deleted);

    /**
     * 通过子工单ID、处理状态及逻辑删除获取数据数量
     * @param subWorkSheetId 子工单ID
     * @param status 是否完成
     * @param deleted       逻辑删除
     * @return long
     * <AUTHOR>
     */
    long countBySubWorkSheetIdAndStatusAndDeleted(long subWorkSheetId,Boolean status,Long deleted);

    /**
     * 通过工单ID、处理状态及逻辑删除获取数据数量
     * @param workSheetId 工单ID
     * @param status 是否完成
     * @param deleted       逻辑删除
     * @return long
     * <AUTHOR>
     */
    long countByWorkSheetIdAndStatusAndDeleted(long workSheetId,Boolean status,Long deleted);

    /**
     * 通过子工单ID、处理状态及逻辑删除获取数据数量
     * @param subWorkSheetId 子工单ID
     * @param stepId          工序id
     * @param status 是否完成
     * @param deleted       逻辑删除
     * @return long
     * <AUTHOR>
     */
    long countBySubWorkSheetIdAndStepIdAndStatusAndDeleted(long subWorkSheetId,long stepId,Boolean status,Long deleted);

    /**
     * 通过工单ID、处理状态及逻辑删除获取数据数量
     * @param workSheetId 工单ID
     * @param stepId 工序id
     * @param status 是否完成
     * @param deleted       逻辑删除
     * @return long
     * <AUTHOR>
     */
    long countByWorkSheetIdAndStepIdAndStatusAndDeleted(long workSheetId,long stepId,Boolean status,Long deleted);
}
