package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.UnqualifiedCause;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良原因Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface UnqualifiedCauseRepository extends LogicDeleteableRepository<UnqualifiedCause>,
        EntityGraphJpaSpecificationExecutor<UnqualifiedCause>, EntityGraphJpaRepository<UnqualifiedCause, Long> {

    /**
     * 通过名称和编码模糊查询不良原因
     *
     * @param text     不良原因名称或编码
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.quality.UnqualifiedCause> 不良原因分页
     */
    @Query("select unqualifiedCause from UnqualifiedCause unqualifiedCause where unqualifiedCause.deleted = 0L and unqualifiedCause.isEnable=true and (unqualifiedCause.name like concat('%',?1,'%') or unqualifiedCause.code like concat('%',?1,'%'))")
    Page<UnqualifiedCause> findByNameOrCode(String text, Pageable pageable);

    /**
     * 通过主键id查找对应不良原因
     *
     * @param id      不良原因主键id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.quality.UnqualifiedCause 不良原因
     */
    Optional<UnqualifiedCause> findByIdAndDeleted(Long id, Long deleted);
}
