package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterial;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface WsCheckMaterialRepository extends LogicDeleteableRepository<WsCheckMaterial>,
        EntityGraphJpaSpecificationExecutor<WsCheckMaterial>, EntityGraphJpaRepository<WsCheckMaterial, Long> {


    /**
     * 查询该凭证号的核料信息
     *
     * @param code 凭证号
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsCheckMaterial> 工单核料
     */
    @DataFilter(isSkip = true)
    @Query("from WsCheckMaterial where code = ?1 and deleted = 0L")
    @FetchMethod
    List<WsCheckMaterial> findByCode(String code);

    /**
     * 查询该凭证号与工单id的核料信息
     *
     * @param code 凭证号
     * @return java.util.Optional<net.airuima.domain.procedure.material.WsCheckMaterial> 工单核料
     */
    @DataFilter(isSkip = true)
    @Query("from WsCheckMaterial ws where ws.code = ?1 and ws.workSheet.id = ?2 and ws.deleted = 0L")
    @FetchMethod
    Optional<WsCheckMaterial> findByCodeAndWorkSheetId(String code,Long wsId);

    /**
     * 查询凭证号列表对应的核料信息
     *
     * @param codeList 凭证号列表
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterial> 工单核料列表
     */
    @DataFilter(isSkip = true)
    @Query("from WsCheckMaterial where code in ?1 and deleted = 0L")
    @FetchMethod
    List<WsCheckMaterial> findByCodeList(List<String> codeList);

    /**
     * 通过总工单主键ID获取核料单记录条数
     * 
     * @param workSheetId 工单主键ID
     * @param deleted 逻辑删除
     * @return Long
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);
}
