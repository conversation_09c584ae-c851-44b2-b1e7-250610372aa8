package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkCellStepRepository extends LogicDeleteableRepository<WorkCellStep>,
        EntityGraphJpaSpecificationExecutor<WorkCellStep>, EntityGraphJpaRepository<WorkCellStep, Long> {
    /**
     * 删除该工位绑定所有工序信息
     *
     * @param workCellId 工位id
     */
    @Modifying
    @Query("delete from WorkCellStep where workCell.id = ?1")
    void deleteByWorkCellId(Long workCellId);

    /**
     * 查询该工位绑定的工序信息
     *
     * @param workCellId 工位id
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @DataFilter(isSkip = true)
    @Query("select ws.step from WorkCellStep ws where ws.workCell.id = ?1 and ws.deleted = 0L")
    List<Step> findByWorkCellId(Long workCellId);

    /**
     * 查找工位绑定的工序
     *
     * @param workCellId 工位
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @DataFilter(isSkip = true)
    @Query("select wcs.step from WorkCellStep wcs where wcs.workCell.id=?1 AND wcs.deleted=?2")
    List<Step> findByWorkCellId(Long workCellId, Long deleted);

    /**
     * 模糊查询工位绑定的工序
     *
     * @param workCellId 工位主键id
     * @param keyword    工序名称或编码
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序列表
     */
    @DataFilter(isSkip = true)
    @Query("select ws.step from WorkCellStep ws where ws.deleted = 0 and ws.workCell.id = ?1 and (ws.step.name like concat('%',?2,'%') or ws.step.code like concat('%',?2,'%') )")
    List<Step> findByWorkCellIdAndKeyword(Long workCellId, String keyword);

    /**
     * 通过工序主键ID列表，组织架构主键ID、工位类型获取数据
     * <AUTHOR>
     * @param stepIds 工序主键ID列表
     * @param category 工位类型
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCellStep> 工位工序列表
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    List<WorkCellStep> findByStepIdInAndWorkCellCategoryAndDeleted(List<Long> stepIds,Integer category,Long deleted);

    /**
     * 通过工位主键ID查询工位工序集合
     *
     * @param workCellId 工位主键ID
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCellStep> 工位工序列表
     * <AUTHOR>
     * @date 2022/12/16
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCellStep w where w.workCell.id = ?1 and w.deleted = 0l")
    List<WorkCellStep> findWorkCellStepByWorkCellId(Long workCellId);


    /**
     * 根据工序主键id和是否启用查找工位数据
     * @param stepId 工序主键id
     * @param isEnable 是否启用
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位数据列表
     */
    @DataFilter(isSkip = true)
    @Query(value = "select w.workCell from WorkCellStep w where w.step.id = ?1 and w.isEnable = ?2 and w.deleted = 0")
    List<WorkCell> findByStepIdAndDeletedAndIsEnable(Long stepId,boolean isEnable);

    /**
     * 通过工序主键ID列表 获取数据
     * <AUTHOR>
     * @param stepId 工序主键ID列表
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCellStep> 工位工序列表
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    List<WorkCellStep> findByStepIdInAndDeletedAndIsEnable(List<Long> stepId,Long deleted,Boolean isEnable);
}
