package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.quality.OnlineReworkRule;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 在线返修工序不良现象规则Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface OnlineReworkRuleRepository extends LogicDeleteableRepository<OnlineReworkRule>,
        EntityGraphJpaSpecificationExecutor<OnlineReworkRule>, EntityGraphJpaRepository<OnlineReworkRule, Long> {

    /**
     * 通过工序主键ID获取在线返修的不良项目列表
     *
     * @param stepId  返修工序主键ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.quality.UnqualifiedItem> 不良项目列表
     * <AUTHOR>
     * @date 2021-01-15
     **/
    @Query("select rule.unqualifiedItem from OnlineReworkRule rule where rule.step.id=?1 and rule.deleted=?2")
    List<UnqualifiedItem> findUnqualifiedItemByStepIdAndDeleted(Long stepId, Long deleted);

    /**
     * 通过不良项目主键id  获取在线返修规则
     * @param unqualifiedItemId 不良项目规则
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/23
     * @return java.util.List<net.airuima.rbase.domain.base.quality.OnlineReworkRule> 在线返修工序不良现象规则列表
     */
    List<OnlineReworkRule> findByUnqualifiedItemIdAndDeleted(Long unqualifiedItemId,Long deleted);
}
