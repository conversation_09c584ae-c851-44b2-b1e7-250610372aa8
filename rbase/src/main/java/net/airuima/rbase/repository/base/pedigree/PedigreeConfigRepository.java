package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系属性Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface PedigreeConfigRepository extends LogicDeleteableRepository<PedigreeConfig>,
        EntityGraphJpaSpecificationExecutor<PedigreeConfig>, EntityGraphJpaRepository<PedigreeConfig, Long> {

    /**
     * 通过产品谱系主键id查询产品谱系属性
     *
     * @param pedigreeId 产品谱系主键id
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeConfig> 产品谱系属性
     */
    @DataFilter(isSkip = true)
    Optional<PedigreeConfig> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);

    /**
     * 通过产品谱系主键id列表查询产品谱系属性列表
     * @param pedigreeIds 产品谱系主键id列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeConfig> 产品谱系属性列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeConfigEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<PedigreeConfig> findByPedigreeIdInAndDeleted(List<Long> pedigreeIds, Long deleted);

    /**
     * 根据谱系名称或编码和是否复用SN查询产品谱系分页
     * <AUTHOR>
     * @param pedigreeCodeOrName 谱系名称或编码
     * @param isReuseSn   是否复用SN
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系分页
     * @date 2021-04-20
     **/
    @Query("select pedigreeConfig.pedigree from PedigreeConfig pedigreeConfig where (pedigreeConfig.pedigree.code like concat('%',?1,'%') or pedigreeConfig.pedigree.name like concat('%',?1,'%')) and pedigreeConfig.isReuseSN=?2 and pedigreeConfig.deleted=0L")
    Page<Pedigree> findByPedigreeCodeOrPedigreeNameAndIsReuseSn(String pedigreeCodeOrName, Boolean isReuseSn, Pageable pageable);
}
