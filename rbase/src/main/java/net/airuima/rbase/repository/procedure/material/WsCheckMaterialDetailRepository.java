package net.airuima.rbase.repository.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料明细表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface WsCheckMaterialDetailRepository extends LogicDeleteableRepository<WsCheckMaterialDetail>,
        EntityGraphJpaSpecificationExecutor<WsCheckMaterialDetail>, EntityGraphJpaRepository<WsCheckMaterialDetail, Long> {

    /**
     * 通过凭证号获取所有待核料明细信息
     *
     * @param checkMaterialCode 凭证号
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细列表
     */
    @Query("from WsCheckMaterialDetail where wsCheckMaterial.code = ?1 and wsCheckMaterial.status <>2 and deleted = 0L")
    @FetchMethod
    @DataFilter(isSkip = true)
    List<WsCheckMaterialDetail> findByWsCheckMaterialCodeAndStatus(String checkMaterialCode);

    /**
     * 更新已核料和未核料数量
     *
     * @param checkNum           核料数量
     * @param uncheckNum         未核料数量
     * @param id                 核料明细主键id
     * @param originalUncheckNum 原始未核料数量
     */
    @Modifying(clearAutomatically = true)
    @Query("update WsCheckMaterialDetail set checkedNumber = ?1,uncheckNumber = ?2 where id = ?3 and uncheckNumber = ?4")
    void updateCheckedNum(double checkNum, double uncheckNum, Long id, double originalUncheckNum);

    /**
     * 通过工单核料信息主键id获取工单核料明细
     *
     * @param checkMaterialIds 工单核料信息主键id
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细列表
     */
    @DataFilter(isSkip = true)
    @Query("from WsCheckMaterialDetail where wsCheckMaterial.id in ?1 and deleted = 0L")
    @FetchMethod
    List<WsCheckMaterialDetail> findByWsCheckMaterialIds(List<Long> checkMaterialIds);

    /**
     * 通过主键id查询工单核料详情信息
     *
     * @param id      核料详情主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细
     */
    @DataFilter(isSkip = true)
    Optional<WsCheckMaterialDetail> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过核料记录查找核料详情
     *
     * @param id      核料记录主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsCheckMaterialDetail> findByWsCheckMaterialIdAndDeleted(Long id, Long deleted);

    /**
     * 通过核料记录查找核料详情
     * @param wsCheckMaterialId 核料记录主键id
     * @param materialId 物料ID
     * @param batch 批次号
     * @param supplierId 供应商ID
     * @param deleted 逻辑删除
     * @return
     */
    Optional<WsCheckMaterialDetail> findByWsCheckMaterialIdAndMaterialIdAndBatchAndSupplierIdAndDeleted(Long wsCheckMaterialId,Long materialId,String batch,Long supplierId,Long deleted);

    /**
     * 根据核料记录主键ID删除明细
     * <AUTHOR>
     * @param wsCheckMaterialId   核料凭证主键ID
     * @return void
     * @date 2021-07-22
     **/
    @Modifying
    @Query("update WsCheckMaterialDetail set deleted=id where  wsCheckMaterial.id=?1")
    void batchDeleteWsCheckMaterialDetailByWsCheckMaterialId(Long wsCheckMaterialId);

    /**
     * 通过工单号主键，获取工单领料单信息
     * @param workSheetId 工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsCheckMaterialDetail> findByWorkSheetIdAndDeleted(Long workSheetId,Long deleted);

    /**
     * 通过物料id查找核料详情
     *
     * @param materialId      物料id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail> 工单核料明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WsCheckMaterialDetail> findByMaterialIdAndDeleted(Long materialId, Long deleted);
}
