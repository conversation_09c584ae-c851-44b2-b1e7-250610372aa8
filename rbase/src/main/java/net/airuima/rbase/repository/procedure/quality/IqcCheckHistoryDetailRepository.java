package net.airuima.rbase.repository.procedure.quality;

import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验详情Repository
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Repository
public interface IqcCheckHistoryDetailRepository extends LogicDeleteableRepository<IqcCheckHistoryDetail>,
        JpaSpecificationExecutor<IqcCheckHistoryDetail>, JpaRepository<IqcCheckHistoryDetail, Long> {

    /**
     * 根据来料检验ID和删除标识查询
     * @param id 来料检验ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail> 来料检验详情
     */
    @Query("select i from IqcCheckHistoryDetail i where i.checkHistory.id = ?1 and i.deleted = ?2")
    @DataFilter(isSkip = true)
    List<IqcCheckHistoryDetail> findByCheckHistoryIdAndDeleted(Long id, Long deleted);


}
