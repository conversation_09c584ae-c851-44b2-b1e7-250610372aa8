package net.airuima.rbase.repository.procedure.reinspect;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspectResult;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工序不良复检结果Repository
 * <AUTHOR>
 */
@Repository
public interface StepReinspectResultRepository extends LogicDeleteableRepository<StepReinspectResult>,
        EntityGraphJpaSpecificationExecutor<StepReinspectResult>, EntityGraphJpaRepository<StepReinspectResult, Long> {

    /**
     * 通过复检记录ID和逻辑删除字段获取处理结果明细数据
     * @param stepReinspectId 复检记录ID
     * @param deleted 逻辑删除
     * @return List<StepReinspectResult>
     */
    List<StepReinspectResult> findByStepReinspectIdAndDeleted(Long stepReinspectId,Long deleted);
}
