package net.airuima.rbase.repository.procedure.quality;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryItemSnapshot;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 检测历史条件详情记录表Repository
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Repository
public interface CheckHistoryItemSnapshotRepository extends LogicDeleteableRepository<CheckHistoryItemSnapshot>,
        EntityGraphJpaSpecificationExecutor<CheckHistoryItemSnapshot>, EntityGraphJpaRepository<CheckHistoryItemSnapshot, Long> {

    /**
     * 根据检测历史主键id、删除标识查询检测历史条件详情记录列表
     * @param checkHistoryId        检测历史主键id
     * @param deleted               删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryItemSnapshot>检测历史条件详情记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CheckHistoryItemSnapshot> findByCheckHistoryIdAndDeleted(Long checkHistoryId, Long deleted);
}
