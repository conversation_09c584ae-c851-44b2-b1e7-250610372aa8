package net.airuima.rbase.repository.base.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface WearingPartRepository extends LogicDeleteableRepository<WearingPart>,
        EntityGraphJpaSpecificationExecutor<WearingPart>, EntityGraphJpaRepository<WearingPart, Long> {


    /**
     * 获取易损件基础信息 通过code
     * @param code
     * @param deleted
     * @return java.util.Optional<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件
     */
    List<WearingPart> findByCodeAndDeleted(String code,Long deleted);

    /**
     * 通过易损件编码、流水号，逻辑删除获取唯一记录
     * @param code 易损件编码
     * @param serialNumber 易损件流水号
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.wearingpart.WearingPart>
     * <AUTHOR>
     */
    Optional<WearingPart> findByCodeAndSerialNumberAndDeleted(String code,String serialNumber,Long deleted);

    /**
     * 获取易损件基础信息 通过主键id
     * @param id
     * @param deleted
     * @return java.util.Optional<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件
     */
    Optional<WearingPart> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 易损件名称或编码 获取易损件信息列表
     * @param text nameOrCode
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件分页
     */
    @Query("select wearingPart from WearingPart wearingPart where wearingPart.deleted = 0L and (wearingPart.name like concat('%',?1,'%') or wearingPart.code like concat('%',?1,'%'))")
    Page<WearingPart> findByCodeOrName(String text, Pageable pageable);


    /**
     * 通过易损件 主键id更新 累计使用次数，累计重置次数
     * @param accumulateUseNumber 累计使用次数
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1 ,wp.accumulateResetNumber = ?2 where wp.id = ?3")
    void updateAccumulateUseNumberAndAccumulateResetNumberById(int accumulateUseNumber, int accumulateResetNumber,Long wearingPartId);

    /**
     * 更新，易损件使用状态 通过易损件主键id
     * @param wearingPartId 易损件主键id
     * @param status 易损件使用状态（0可用，1在用，2超期，3报废）
     */
    @Modifying
    @Query("update WearingPart wp set wp.status = ?2 where wp.id = ?1")
    void updateStatusById(Long wearingPartId,int status);

    /**
     * 更新累计使用次数，通过易损件主键id
     * @param accumulateUseNumber 累计使用次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1 where wp.id = ?2")
    void updateAccumulateUseNumberById(int accumulateUseNumber, Long wearingPartId);

    /**
     * 更新累计使用时间，以及重置次数 通过易损件主键id
     * @param accumulateUseTime 累计使用时间
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseTime = ?1,wp.accumulateResetNumber = ?2,wp.status=0 where wp.id = ?3")
    void updateAccumulateUseTimeAndAccumulateResetNumberById(int accumulateUseTime, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用时间 通过易损件主键id
     * @param accumulateUseTime 累计使用时间
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseTime = ?1 where wp.id = ?2")
    void updateAccumulateUseTimeById(Integer accumulateUseTime, Long wearingPartId);


    /**
     * 更新失效期，以及重置次数 通过易损件主键id
     *
     * @param expireDate 失效期
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.expireDate = ?1,wp.accumulateResetNumber= ?2,wp.status=0 where wp.id = ?3")
    void updateExpireDateAndAccumulateResetNumberById(LocalDateTime expireDate, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用次数、使用时长，以及重置次数 通过易损件id
     *
     * @param accumulateUseNumber 使用次数
     * @param accumulateUseTime 使用时长
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.accumulateUseTime= ?2,wp.accumulateResetNumber=?3 ,wp.status=0 where wp.id = ?4")
    void updateAccumulateUseNumberAndAccumulateUseTimeAndAccumulateResetNumberById(int accumulateUseNumber, int accumulateUseTime, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用次数、有效期，以及重置次数 通过易损件主键id
     *
     * @param accumulateUseNumber 使用次数
     * @param expireDate 失效期
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.expireDate= ?2,wp.accumulateResetNumber=?3 ,wp.status=0 where wp.id = ?4")
    void updateAccumulateUseNumberAndExpireDateAndAccumulateResetNumberById(int accumulateUseNumber, LocalDateTime expireDate, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用时长、有效期,以及重置次数 通过易损件主键id
     *
     * @param accumulateUseTime 使用时长
     * @param expireDate 失效期
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseTime = ?1,wp.expireDate= ?2,wp.accumulateResetNumber=?3 ,wp.status=0 where wp.id = ?4")
    void updateAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(int accumulateUseTime, LocalDateTime expireDate, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用次数、累计使用时长、有效期,以及重置次数 通过易损件id
     *
     * @param accumulateUseNumber 使用次数
     * @param accumulateUseTime 使用时长
     * @param expireDate 失效期
     * @param accumulateResetNumber 累计重置次数
     * @param wearingPartId 易损件主键id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.accumulateUseTime= ?2,wp.expireDate=?3,wp.accumulateResetNumber=?4 ,wp.status=0 where wp.id = ?5")
    void updateAccumulateUseNumberAndAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(int accumulateUseNumber, int accumulateUseTime, LocalDateTime expireDate, int accumulateResetNumber, Long wearingPartId);

    /**
     * 更新累计使用次数、累计使用时长、有效期,以及重置次数 通过易损件id
     *
     * @param accumulateUseNumber 使用次数
     * @param currentAccumulateUseTime 使用时长
     * @param wearingPartId 易损件id
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.accumulateUseTime= ?2 where wp.id = ?3")
    void updateAccumulateUseNumberAndAccumulateUseTimeById(int accumulateUseNumber, Integer currentAccumulateUseTime, Long wearingPartId);

    /**
     * 由于累计使用时间超期，使用易损件，为工序可以正常进行（可允许范围内超期使用时间），更新状态为超期
     * @param currentAccumulateUseTime 累计使用时间
     * @param status 易损件状态
     * @param wearingPartId 易损件id
     * <AUTHOR>
     * @date  2021/8/25
     * @return void
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseTime = ?1,wp.status = ?2 where wp.id = ?3")
    void updateAccumulateUseTimeAndStatusById(int currentAccumulateUseTime,int status,Long wearingPartId);

    /**
     *  在时间+次数的管控模式下，当时间超出时（可允许范围内超期使用时间），更新状态为超期
     * @param accumulateUseNumber 累计使用次数
     * @param currentAccumulateUseTime 累计使用时间
     * @param status 易损件状态
     * @param wearingPartId 易损件id
     * <AUTHOR>
     * @date  2021/8/25
     * @return void
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.accumulateUseTime= ?2,wp.status = ?3 where wp.id = ?4")
    void updateAccumulateUseNumberAndAccumulateUseTimeAndStatusById(int accumulateUseNumber, Integer currentAccumulateUseTime,int status, Long wearingPartId);

    /**
     *  在次数+有效期的管控模式下，可允许有效期超出使用时间，更新状态为超期
     * @param accumulateUseNumber 次数
     * @param status 状态
     * @param wearingPartId 易损件主键ID
     * <AUTHOR>
     * @date  2021/8/25
     * @return void
     */
    @Modifying
    @Query("update WearingPart wp set wp.accumulateUseNumber = ?1,wp.status = ?2 where wp.id = ?3")
    void updateAccumulateUseNumberAndStatusById(int accumulateUseNumber, int status, Long wearingPartId);

    /**
     * 通过主键ID集合查询易损件集合
     *
     * @param idList 主键ID集合
     * @param deleted 删除标记
     * @return : java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件列表
     * <AUTHOR>
     * @date 2023/3/3
     **/
    List<WearingPart> findByIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     * 通过易损件类型主键ID+删除标识，查询易损件集合
     *
     * @param wearingPartGroupId 易损件类型主键ID
     * @param deleted            删除标识
     * @return : java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件列表
     * <AUTHOR>
     * @date 2023/3/8
     **/
    List<WearingPart> findByWearingPartGroupIdAndDeleted(Long wearingPartGroupId, Long deleted);

    /**
     *
     * 通过易损件种类主键ID获取可用状态的易损件列表
     * @param wearingPartGroupId 易损件类型主键ID
     * @param status 状态(0可用，1在用，2超期，3报废)
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件列表
     */
    List<WearingPart> findByWearingPartGroupIdAndStatusLessThanAndDeleted(Long wearingPartGroupId,Integer status, Long deleted);

    /**
     * 通过易损件种类主键ID列表获取可用状态的易损件列表
     *
     * @param wearingPartGroupIdList 易损件类型主键ID集合
     * @param deleted                删除标识
     * @param status 状态(0可用，1在用，2超期，3报废)
     * @return : java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart> 易损件列表
     * <AUTHOR>
     * @date 2023/3/8
     **/
    List<WearingPart> findByWearingPartGroupIdInAndStatusLessThanAndDeleted(List<Long> wearingPartGroupIdList, Integer status,Long deleted);




}
