package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产过程产生不良记录Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface SnUnqualifiedItemRepository extends LogicDeleteableRepository<SnUnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<SnUnqualifiedItem>, EntityGraphJpaRepository<SnUnqualifiedItem, Long> {

    /**
     * 通过子工单主键ID、工序主键ID、SN删除数据
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param stepId 工序主键ID
     * @param sn     SN
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update  SnUnqualifiedItem  sui set sui.deleted=sui.id where sui.subWorkSheet.id=?1 and sui.step.id=?2 and sui.sn=?3")
    void batchDeleteBySnAndSubWorkSheetIdAndStepId(Long subWorkSheetId,Long stepId,String sn);


    /**
     * 通过工单主键ID、工序主键ID、SN删除数据
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param sn     SN
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update  SnUnqualifiedItem  sui set sui.deleted=sui.id where sui.workSheet.id=?1 and sui.step.id=?2 and sui.sn=?3")
    void batchDeleteBySnAndWorkSheetIdAndStepId(Long workSheetId,Long stepId,String sn);


    /**
     * 通过子工单主键ID、工序主键ID、SN删除数据
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param stepId 工序主键ID
     * @param sn     SN
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update  SnUnqualifiedItem  sui set sui.flag = true where sui.subWorkSheet.id=?1 and sui.step.id=?2 and sui.unqualifiedItem.id = ?3 and sui.sn=?4 ")
    void batchFlagBySnAndSubWorkSheetIdAndStepId(Long subWorkSheetId,Long stepId,Long unqualifiedItemId,String sn);


    /**
     * 通过工单主键ID、工序主键ID、SN删除数据
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param stepId 工序主键ID
     * @param sn     SN
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update  SnUnqualifiedItem  sui set sui.flag = true where sui.workSheet.id=?1 and sui.step.id=?2 and sui.unqualifiedItem.id = ?3 and sui.sn=?3")
    void batchFlagBySnAndWorkSheetIdAndStepId(Long workSheetId,Long stepId,Long unqualifiedItemId,String sn);

    /**
     * 通过子工单主键id，sn列表，获取单支生产过程产生不良记录列表
     * @param subWorkSheetId 子工单主键id
     * @param snLists sn列表
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/24
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem> 单支生产过程产生不良记录列表
     */
    @DataFilter(isSkip = true)
    List<SnUnqualifiedItem> findBySubWorkSheetIdAndSnInAndStepIdAndDeleted(Long subWorkSheetId,List<String> snLists,Long stepId,Long deleted);

    /**
     * 通过子工单主键id 不良产生工序主键id 不良项目主键id 获取对应sn 不良信息
     * @param subWorkSheetId 子工单主键id
     * @param stepId 工序主键id
     * @param unqualified 不良项目主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/25
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem> 单支生产过程产生不良记录列表
     */
    @DataFilter(isSkip = true)
    List<SnUnqualifiedItem> findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(Long subWorkSheetId,Long stepId,Long unqualified,Long deleted);

    /**
     * 通过sn 子工单，工序主键id 获取 对应sn 不良信息
     * @param snWorkDetailId sn工作详情
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/17
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem> 单支生产过程产生不良记录
     */
    Optional<SnUnqualifiedItem> findBySnWorkDetailIdAndDeleted(Long snWorkDetailId,Long deleted);

    /**
     * 根据sn生产详情主键id
     * @param snWorkDetailId 生产详情主键id
     * @param deleted 逻辑删除
     */
    @Modifying
    @Query("update SnUnqualifiedItem sui set sui.deleted=sui.id where sui.snWorkDetail.id=?1 and sui.deleted=?2")
    void deleteAllBySnWorkDetailIdAndDeleted(Long snWorkDetailId,Long deleted);



    /**
     * 更换sn详情不良项目 对应的原始sn
     * @param originalSn 原始sn
     * @param replaceSn 替换sn
     * <AUTHOR>
     * @date  2023/2/23
     */
    @Modifying
    @Query("update SnUnqualifiedItem sui set sui.sn = ?2 where sui.sn = ?1")
    void snUnqualifiedReplaceSn(String originalSn,String replaceSn);

    /**
     * 通过sn 子工单，工序主键id 获取 对应sn 不良信息
     * @param sn sn
     * @param subWorkSheetId 子工单主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/17
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem> 单支生产过程产生不良记录
     */
    @DataFilter(isSkip = true)
    Optional<SnUnqualifiedItem> findBySnAndSubWorkSheetIdAndStepIdAndDeleted(String sn,Long subWorkSheetId,Long stepId,Long deleted);

    /**
     * 通过sn 工单，工序主键id 获取 对应sn 不良信息
     * @param sn sn
     * @param workSheetId 工单主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/17
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem> 单支生产过程产生不良记录
     */
    @DataFilter(isSkip = true)
    Optional<SnUnqualifiedItem> findBySnAndWorkSheetIdAndStepIdAndDeleted(String sn,Long workSheetId,Long stepId,Long deleted);

}
