package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResultDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果明细表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface FqcCheckResultDetailRepository extends LogicDeleteableRepository<FqcCheckResultDetail>,
        EntityGraphJpaSpecificationExecutor<FqcCheckResultDetail>, EntityGraphJpaRepository<FqcCheckResultDetail, Long> {

}
