package net.airuima.rbase.repository.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Repository
public interface PedigreeStepPassRateRepository extends LogicDeleteableRepository<PedigreeStepPassRate>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepPassRate>, EntityGraphJpaRepository<PedigreeStepPassRate, Long> {

    /**
     * 通过产品谱系主键ID及逻辑删除获取工序目标良率列表
     * @param pedigreeId 产品谱系主键ID
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate> 工序目标良率列表
     * <AUTHOR>
     * @date 2024/1/10
     */
    @DataFilter(isSkip = true)
    List<PedigreeStepPassRate> findByPedigreeIdAndDeleted(Long pedigreeId,Long deleted);

    /**
     * 通过产品谱系主键ID、工序组别主键ID及逻辑删除动态参数查询工序目标良率列表
     * @param pedigreeId 产品谱系主键ID
     * @param stepGroupId 工序组别主键ID
     * @param deleted   逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepPassRate> 工序目标良率列表
     * <AUTHOR>
     * @date 2024/1/10
     */
    @EntityGraph(value = "pedigreeStepPassRateEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select ppr from PedigreeStepPassRate ppr where ppr.deleted=?3 and (?1 is null or ppr.pedigree.id=?1) and (?2 is null or ppr.stepGroup.id=?2)")
    List<PedigreeStepPassRate> findByCondition(Long pedigreeId,Long stepGroupId,Long deleted);
}
