package net.airuima.rbase.repository.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface WearingPartResetHistoryRepository extends LogicDeleteableRepository<WearingPartResetHistory>,
        EntityGraphJpaSpecificationExecutor<WearingPartResetHistory>, EntityGraphJpaRepository<WearingPartResetHistory, Long> {

    /**
     * 获取易损件重置历史
     *
     * @param wearingPartId 易损件id
     * @param deleted       逻辑删除
     */
    @FetchMethod
    List<WearingPartResetHistory> findByWearingPartIdAndDeleted(Long wearingPartId, Long deleted);
}