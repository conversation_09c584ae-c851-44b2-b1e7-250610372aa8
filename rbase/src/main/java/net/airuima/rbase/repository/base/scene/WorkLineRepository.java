package net.airuima.rbase.repository.base.scene;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产线Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkLineRepository extends LogicDeleteableRepository<WorkLine>,
        EntityGraphJpaSpecificationExecutor<WorkLine>, EntityGraphJpaRepository<WorkLine, Long> {
    /**
     * 通过名称和编码模糊查询生产线
     *
     * @param text 名称或编码
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.scene.WorkLine> 生产线列表
     */
    @Query("select workLine from WorkLine workLine where workLine.isEnable=true and workLine.deleted = 0L and (workLine.name like concat('%',?1,'%') or workLine.code like concat('%',?1,'%') )")
    @FetchMethod
    Page<WorkLine> findByNameOrCode(String text, Pageable pageable);

    /**
     * 通过组织架构ID获取生产线列表
     *
     * @param organizationId 组织架构主键ID
     * @param deleted        删除标志
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkLine> 生产线列表
     * <AUTHOR>
     * @date 2020-12-28
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WorkLine> findByOrganizationIdAndIsEnableAndDeleted(Long organizationId, Boolean isEnable, Long deleted);

    /**
     * 根据生产线ID获取数据
     *
     * @param id      生产线主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.WorkLine> 生产线
     * <AUTHOR>
     * @date 2021-04-12
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<WorkLine> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 通过名称或编码+是否开启+删除标识 查询产线
     *
     * @param nameOrCode 生产线名称或编码
     * @param isEnable   是否开启
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkLine> 生产线列表
     * <AUTHOR>
     * @date 2022/12/30
     **/
    @Query("select workLine from WorkLine workLine where (workLine.name like concat('%',?1,'%') or workLine.code like concat('%',?1,'%')) and workLine.isEnable = ?2 and workLine.deleted = ?3")
    @FetchMethod
    List<WorkLine> findByNameOrCodeAndIsEnableAndDeleted(String nameOrCode, Boolean isEnable, Long deleted);

    /**
     * 根据产线编码和删除标记查询产线
     *
     * @param code    产线编码
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.rbase.domain.base.scene.WorkLine> 生产线
     */
    @DataFilter(isSkip = true)
    @Query("select w from WorkLine w where w.code = ?1 and w.deleted = ?2")
    Optional<WorkLine> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 产线编码和删除标记查询产线
     *
     * @param codes   产线编码列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkLine> 生产线列表
     */
    @DataFilter(isSkip = true)
    List<WorkLine> findByCodeInAndDeleted(List<String> codes, Long deleted);

}
