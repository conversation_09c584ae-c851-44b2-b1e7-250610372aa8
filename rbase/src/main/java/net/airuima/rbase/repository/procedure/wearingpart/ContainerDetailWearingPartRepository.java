package net.airuima.rbase.repository.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface ContainerDetailWearingPartRepository extends LogicDeleteableRepository<ContainerDetailWearingPart>,
        EntityGraphJpaSpecificationExecutor<ContainerDetailWearingPart>, EntityGraphJpaRepository<ContainerDetailWearingPart, Long> {

    /**
     * 通过容器详情主键ID，易损件主键ID获取唯一记录
     *
     * @param containerDetailId 容器详情主键ID
     * @param wearingPartId 易损件主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart> 容器生产详情易损件
     * <AUTHOR>
     *  @date 2021-06-23
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetailWearingPart> findByContainerDetailIdAndWearingPartIdAndDeleted(Long containerDetailId, Long wearingPartId, Long deleted);

    /**
     *
     * 通过容器详情主键ID获取容器详情易损件列表
     * @param containerDetailId 容器详情主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart> 容器生产详情易损件
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetailWearingPart> findByContainerDetailIdAndDeleted(Long containerDetailId, Long deleted);

    /**
     *
     * 通过批量详情主键ID获取容器详情易损件列表
     * @param batchWorkDetailId 批量详情主键ID
     * @param deleted  逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart> 容器生产详情易损件列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetailWearingPart> findByContainerDetailBatchWorkDetailIdAndDeleted(Long batchWorkDetailId,Long deleted);

    /**
     *  易损件主键id 获取容器列表 分页获取，在开始和结束得范围区间内
     * @param wearingPartId 易损件主键id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted 逻辑删除
     * @param pageable 分页
     * <AUTHOR>
     * @date  2021/8/26
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart> 容器生产详情易损件分页
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Page<ContainerDetailWearingPart> findByWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long wearingPartId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);


    /**
     *
     * 通过容器详情主键ID删除详情易损件
     * @param containerDetailId 容器详情主键ID
     * @return void
     */
    @Modifying
    @Query("update ContainerDetailWearingPart cdw set cdw.deleted=cdw.id where cdw.containerDetail.id=?1 and cdw.deleted=0L")
    void batchDeleteByBatchWorkDetailId(Long containerDetailId);


    /**
     * 根据批次详情主键id 与 易损件主键id 获取 易损件使用详情信息
     * @param batchId 批次详情主键id
     * @param wearingPartId 易损件主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart> 容器生产详情易损件列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetailWearingPart> findByContainerDetailBatchWorkDetailIdAndWearingPartIdAndDeleted(Long batchId,Long wearingPartId,Long deleted);

    /**
     * 通过易损件编码获取在一定范围内使用该易损件的工单
     *
     * @param wearingPartCode 易损件编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 容器设备生产详情列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select c from ContainerDetailWearingPart c where c.wearingPart.code = ?1 and c.containerDetail.recordDate >= ?2 and c.containerDetail.recordDate <= ?3 and c.deleted = 0L")
    List<ContainerDetailWearingPart> findByWearingPartCodeAndStartDateAndEndDate(String wearingPartCode, LocalDateTime startDate, LocalDateTime endDate);
}