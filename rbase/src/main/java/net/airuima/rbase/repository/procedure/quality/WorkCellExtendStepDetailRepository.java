package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendStepDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 工位宽放过站记录详情
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Repository
public interface WorkCellExtendStepDetailRepository extends LogicDeleteableRepository<WorkCellExtendStepDetail>,
        EntityGraphJpaSpecificationExecutor<WorkCellExtendStepDetail>, EntityGraphJpaRepository<WorkCellExtendStepDetail, Long> {

    /**
     * 通过宽放工位修改对应的宽放记录状态
     *
     * @param extendWorkCellId 宽放工位id
     * @param status           宽放记录状态
     * @param deleted          逻辑删除
     */
    @Modifying
    @Query("""
                    update WorkCellExtendStepDetail wce set wce.status = ?2 where wce.extendWorkCell.id = ?1 and wce.deleted = ?3
            """)
    void updateStatusByExtendWorkCellId(Long extendWorkCellId, Boolean status, Long deleted);


    /**
     * （工单请求）通过工单获取 最后下交的工位放宽的工序信息
     *
     * @param workSheetId 工单id
     * @param status      是否处理状态
     * @param deleted     逻辑删除
     * @return Optional<WorkCellExtendStepDetail>
     */
    Optional<WorkCellExtendStepDetail> findTop1ByWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(Long workSheetId,
                                                                                                     Boolean status, Long deleted);

    /**
     * （工单请求）通过子工单获取 最后下交的工位放宽的工序信息
     *
     * @param subWorkSheetId 子工单id
     * @param status         是否处理状态
     * @param deleted        逻辑删除
     * @return Optional<WorkCellExtendStepDetail>
     */
    Optional<WorkCellExtendStepDetail> findTop1BySubWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(Long subWorkSheetId,
                                                                                                        Boolean status, Long deleted);

    /**
     * （容器请求）通过工单获取 最后下交的工位放宽的工序信息
     *
     * @param wsId          工单id
     * @param containerCode 容器编号
     * @param status        是否处理状态
     * @param deleted       逻辑删除
     * @return WorkCellExtendStepDetail
     */
    Optional<WorkCellExtendStepDetail> findTop1ByWorkSheetIdAndContainerDetailContainerCodeAndStatusAndDeletedOrderByRecordTimeDesc
    (Long wsId, String containerCode, Boolean status, Long deleted);

    /**
     * （容器请求）通过子工单获取 最后下交的工位放宽的工序信息
     *
     * @param subWsId       子工单id
     * @param containerCode 容器编号
     * @param status        是否处理状态
     * @param deleted       逻辑删除
     * @return WorkCellExtendStepDetail
     */
    Optional<WorkCellExtendStepDetail> findTop1BySubWorkSheetIdAndContainerDetailContainerCodeAndStatusAndDeletedOrderByRecordTimeDesc
    (Long subWsId, String containerCode, Boolean status, Long deleted);

    /**
     * （sn请求）通过工单获取 最后下交的工位放宽的工序信息
     *
     * @param wsId    工单id
     * @param sn      sn
     * @param status  是否处理状态
     * @param deleted 逻辑删除
     * @return WorkCellExtendStepDetail
     */
    Optional<WorkCellExtendStepDetail> findTop1ByWorkSheetIdAndSnWorkDetailSnAndStatusAndDeletedOrderByRecordTimeDesc
    (Long wsId, String sn, Boolean status, Long deleted);


    /**
     * （sn请求）通过子工单获取 最后下交的工位放宽的工序信息
     *
     * @param subWsId 子工单
     * @param sn      sn
     * @param status  是否处理状态
     * @param deleted 逻辑删除
     * @return WorkCellExtendStepDetail
     */
    Optional<WorkCellExtendStepDetail> findTop1BySubWorkSheetIdAndSnWorkDetailSnAndStatusAndDeletedOrderByRecordTimeDesc
    (Long subWsId, String sn, Boolean status, Long deleted);

    /**
     * 通过批量id 删除工位宽放记录
     *
     * @param batchId 批量id
     * @param deleted 逻辑删除
     */
    @Modifying
    @Query("""
                    update WorkCellExtendStepDetail wcesd set wcesd.deleted = wcesd.id where wcesd.batchWorkDetail.id = ?1 and wcesd.deleted = ?2
            """)
    void logicDeletedBatchId(Long batchId, Long deleted);

    /**
     * 通过容器详情id 删除工位宽放记录
     *
     * @param containerDetailId 容器详情id
     * @param deleted           逻辑删除
     */
    @Modifying
    @Query("""
                    update WorkCellExtendStepDetail wcesd set wcesd.deleted = wcesd.id where wcesd.containerDetail.id = ?1 and wcesd.deleted = ?2
            """)
    void logicDeletedContainerDetailId(Long containerDetailId, Long deleted);

    /**
     * 通过sn详情id 删除工位宽放记录
     *
     * @param snWorkDetailId sn详情id
     * @param deleted        逻辑删除
     */
    @Modifying
    @Query("""
                    update WorkCellExtendStepDetail wcesd set wcesd.deleted = wcesd.id where wcesd.snWorkDetail.id = ?1 and wcesd.deleted = ?2
            """)
    void logicDeletedSnWorkDetailId(Long snWorkDetailId, Long deleted);


}
