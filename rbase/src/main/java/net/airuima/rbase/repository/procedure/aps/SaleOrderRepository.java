package net.airuima.rbase.repository.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Repository
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Repository
public interface SaleOrderRepository extends LogicDeleteableRepository<SaleOrder>,
        EntityGraphJpaSpecificationExecutor<SaleOrder>, EntityGraphJpaRepository<SaleOrder, Long> {

    /**
     * 根据id查询销售订单
     * @param id 主键
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.aps.SaleOrder> 销售订单
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<SaleOrder> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据订单编号 获取订单信息
     * @param serialNumber 订单单
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.aps.SaleOrder> 销售订单
     * <AUTHOR>
     * @date  2023/3/16
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<SaleOrder> findBySerialNumberAndDeleted(String serialNumber,Long deleted);

    @FetchMethod
    @Query("select sw from SaleOrder sw where sw.serialNumber=?1 and sw.deleted=?2")
    Optional<SaleOrder> findBySerialNumberAndDeletedWhenDataFilter(String serialNumber, Long deleted);
}
