package net.airuima.rbase.repository.base.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface PedigreeStepWearingPartGroupRepository extends LogicDeleteableRepository<PedigreeStepWearingPartGroup>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepWearingPartGroup>, EntityGraphJpaRepository<PedigreeStepWearingPartGroup, Long> {

    /**
     * 通过产品谱系主键id、工艺路线主键id、工序主键id 获取产品谱系工序易损件规则列表
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup> 产品谱系工序易损件规则列表
     */
    @DataFilter(isSkip = true)
    List<PedigreeStepWearingPartGroup> findAllByPedigreeIdAndWorkFlowIdAndStepIdAndDeletedAndWorkCellIsNull(Long pedigreeId,Long workFlowId,Long stepId,Long deleted);

    /**
     * 通过产品谱系主键id、工艺路线主键id、工序主键id 工位主键id 获取产品谱系工序易损件规则列表
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param workCellId  工位主键id
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2021/9/17
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup> 产品谱系工序易损件规则列表
     */
    @DataFilter(isSkip = true)
    @Query("select ppg from PedigreeStepWearingPartGroup ppg where ((coalesce(?1, null) is null and ppg.pedigree.id is null) or ppg.pedigree.id=?1) and  ((coalesce(?2, null)is null and ppg.workFlow.id is null) or ppg.workFlow.id=?2)" +
            "  and   ((coalesce(?3, null) is null and ppg.step.id is null) or ppg.step.id=?3) and ((coalesce(?4, null)is null and ppg.workCell.id is null) or ppg.workCell.id=?4)" +
            " and ppg.wearingPartGroup.id=?5 and ppg.deleted=?6 and ppg.isEnable = ?7")
    PedigreeStepWearingPartGroup findByPedigreeIdAndWorkFlowIdAndStepIdAndWorkCellIdAndWearingPartGroupIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId,Long workCellId, Long groupId,Long deleted,Boolean isEnable);


    /**
     * 通过产品谱系主键id、工艺路线主键id、工序主键id 工位主键id 获取产品谱系工序易损件规则列表
     * @param pedigreeIds 产品谱系主键id列表
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param workCellId  工位主键id
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2021/9/17
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup> 产品谱系工序易损件规则列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepWearingPartGroupEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select ppg from PedigreeStepWearingPartGroup ppg where (ppg.pedigree.id is null or ppg.pedigree.id in(?1)) and  (ppg.workFlow.id is null or ppg.workFlow.id=?2) and  (ppg.step.id is null or ppg.step.id=?3) and (ppg.workCell is null or ppg.workCell.id=?4) and ppg.deleted=?5 and ppg.isEnable = ?6")
    List<PedigreeStepWearingPartGroup> findByPedigreeIdInAndWorkFlowIdAndStepIdAndWorkCellIdAndDeleted(List<Long> pedigreeIds, Long workFlowId, Long stepId,Long workCellId, Long deleted,Boolean isEnable);

    /**
     * 通过产品谱系主键id、工序主键id 工位主键id 获取产品谱系工序易损件规则列表
     * @param pedigreeId 产品谱系主键id
     * @param stepId 工序主键id
     * @param workCellId 工位主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/1/11
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup> 产品谱系工序易损件规则列表
     */
    @DataFilter(isSkip = true)
    List<PedigreeStepWearingPartGroup> findAllByPedigreeIdAndStepIdAndWorkCellIdAndWorkFlowIdIsNullAndDeleted(Long pedigreeId, Long stepId, Long workCellId, Long deleted);

    /**
     * 通过产品谱系主键id、工序主键id  获取产品谱系工序易损件规则列表
     * @param pedigreeId 产品谱系主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/1/11
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup> 产品谱系工序易损件规则列表
     */
    @DataFilter(isSkip = true)
    List<PedigreeStepWearingPartGroup> findAllByPedigreeIdAndStepIdAndDeletedAndWorkCellIsNullAndWorkFlowIdIsNull(Long pedigreeId, Long stepId, Long deleted);


}
