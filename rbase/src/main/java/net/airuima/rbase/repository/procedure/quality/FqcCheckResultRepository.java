package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.quality.FqcCheckResult;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * FQC检测结果表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface FqcCheckResultRepository extends LogicDeleteableRepository<FqcCheckResult>,
        EntityGraphJpaSpecificationExecutor<FqcCheckResult>, EntityGraphJpaRepository<FqcCheckResult, Long> {

    /**
     * 通过主键id查找唯一FQC检测结果
     *
     * @param id      检测结果主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.FqcCheckResult> 检测结果
     */
    @DataFilter(isSkip = true)
    Optional<FqcCheckResult> findByIdAndDeleted(Long id, Long deleted);

}
