package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 工单SN关联Repository
 * <AUTHOR>
 */
@Repository
public interface WorkSheetSnRepository extends LogicDeleteableRepository<WorkSheetSn>,
        EntityGraphJpaSpecificationExecutor<WorkSheetSn>, EntityGraphJpaRepository<WorkSheetSn, Long> {

    /**
     * 通过SN获取工单关联SN记录
     * @param sn 生产SN
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    Optional<WorkSheetSn> findBySnAndDeleted(String sn,Long deleted);

    /**
     * 通过SN列表及逻辑删除获取关联记录
     * @param snList SN列表
     * @param deleted  逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     * @date 2024/3/1
     */
    @DataFilter(isSkip = true)
    List<WorkSheetSn> findBySnInAndDeleted(List<String> snList,Long deleted);

    /**
     * 通过子工单ID及逻辑删除获取子工单SN关联列表
     * @param subWorkSheetId 子工单ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    List<WorkSheetSn> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId,Long deleted);


    /**
     * 通过子工单ID及逻辑删除获取第一个子工单SN关联数据
     * @param subWorkSheetId 子工单ID
     * @param deleted 逻辑删除
     * @return WorkSheetSn
     */
    @DataFilter(isSkip = true)
    WorkSheetSn findTop1BySubWorkSheetIdAndDeleted(Long subWorkSheetId,Long deleted);

    /**
     * 通过工单ID及逻辑删除获取工单SN关联列表
     * @param workSheetId 工单ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    List<WorkSheetSn> findByWorkSheetIdAndDeleted(Long workSheetId,Long deleted);


    /**
     * 通过SN列表、子工单ID及逻辑删除获取不属于子工单ID的关联SN
     * @param snList SN列表
     * @param subWorkSheetId 子工单ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    List<WorkSheetSn> findBySnInAndSubWorkSheetIdNotAndDeleted(List<String> snList,Long subWorkSheetId,Long deleted );

    /**
     * 通过SN列表、工单ID及逻辑删除获取不属于工单ID的关联SN
     * @param snList SN列表
     * @param workSheetId 工单ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.single.WorkSheetSn>
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    List<WorkSheetSn> findBySnInAndWorkSheetIdNotAndDeleted(List<String> snList,Long workSheetId,Long deleted );

    /**
     * 通过子工单ID删除所有关联SN
     * @param subWorkSheetId 子工单ID
     */
    @Modifying
    @Query("delete from WorkSheetSn ws  where ws.subWorkSheet.id = ?1")
    void deleteAllBySubWorkSheetId(Long subWorkSheetId);


    /**
     * 通过工单ID删除所有关联SN
     * @param workSheetId 工单ID
     */
    @Modifying
    @Query("delete from WorkSheetSn ws  where ws.workSheet.id = ?1")
    void deleteAllByWorkSheetId(Long workSheetId);
}
