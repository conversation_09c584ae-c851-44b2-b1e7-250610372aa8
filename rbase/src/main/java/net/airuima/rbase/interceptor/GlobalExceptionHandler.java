package net.airuima.rbase.interceptor;

import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.exception.BadRequestException;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 全局异常拦截器
 *
 * <AUTHOR>
 * @date 2021/11/29
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 拦截异常信息，重新设置异常数据 可用于（SAP/ERP同步数据时使用）
     * @param e 异常类信息
     * <AUTHOR>
     * @date  2021/11/29
     * @return List<SyncBaseDTO>
     */
    @ExceptionHandler(BadRequestException.class)
    public List<SyncResultDTO> badRequestException(BadRequestException e){
        List<SyncResultDTO> syncResultDtoList = new ArrayList<>();
        syncResultDtoList.add(new SyncResultDTO(e.getStatus(),e.getMessage()));
        return syncResultDtoList;
    }
}
