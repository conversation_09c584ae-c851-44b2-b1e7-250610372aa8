package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * sn生产状态信息
 *
 * <AUTHOR>
 * @date 2022/11/7
 */
public enum WearingPartCategoryEnum {
    /**
     * 易损件管控类型为0：次数
     **/
    FREQUENCY("次数", 0),
    /**
     * 易损件管控类型为1：时长
     **/
    DURATION("时长", 1),
    /**
     * 易损件管控类型为2：有效期
     **/
    VALIDITY("有效期", 2),
    /**
     * 易损件管控类型为3：时长+次数
     **/
    DURATION_FREQUENCY("时长+次数", 3),
    /**
     * 易损件管控类型为4：时长+有效期
     **/
    DURATION_VALIDITY("时长+有效期", 4),
    /**
     * 易损件管控类型为5：次数+有效期
     **/
    FREQUENCY_VALIDITY("次数+有效期", 5),
    /**
     * 易损件管控类型为6：时长+次数+有效期
     **/
    DURATION_FREQUENCY_VALIDITY("时长+次数+有效期", 6),
    /**
     * 易损件重置方式：手动
     **/
    MANUAL("手动", 0),
    /**
     * 易损件重置方式：自动
     **/
    AUTO("自动", 1),
    /**
     * 易损件状态：可用
     **/
    AVAILABLE("可用", 0),
    /**
     * 易损件状态：在用
     **/
    INUSE("在用", 1),
    /**
     * 易损件状态：超期
     **/
    EXCEED("超期", 2),
    /**
     * 易损件状态：报废
     **/
    SCRAP("报废", 3);

    private String name;
    private int category;

    WearingPartCategoryEnum() {
    }

    WearingPartCategoryEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }
}
