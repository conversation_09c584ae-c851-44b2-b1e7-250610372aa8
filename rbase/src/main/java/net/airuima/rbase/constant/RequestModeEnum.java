package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 请求模式枚举
 *
 * <AUTHOR>
 * @date 2023/08/05
 */
public enum RequestModeEnum {
    /**
     * 工单请求模式
     */
    WORK_SHEET_REQUEST_MODE("工单",0),

    /**
     * 容器请求模式
     */
    CONTAINER_REQUEST_MODE("容器",1),

    /**
     * 单支请求模式
     */
    SN_REQUEST_MODE("单支",2);

    private final String mode;
    private final int code;

    RequestModeEnum(String mode, int code) {
        this.mode = mode;
        this.code = code;
    }

    public String getMode() {
        return this.mode;
    }

    public int getCode() {
        return this.code;
    }

    public static int getCodeByMode(String mode) {
        for (RequestModeEnum rm : values()) {
            if (rm.getMode().equals(mode)) {
                return rm.getCode();
            }
        }
        throw new IllegalArgumentException("Invalid mode: " + mode);
    }
}
