package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 常量池
 *
 * <AUTHOR>
 * @date 2020/12/21
 */
public final class Constants {

    public static final boolean TRUE = true;
    public static final boolean FALSE = false;
    public static final String SYSTEM_ACCOUNT = "system";
    public static final String STATUS = "status";
    public static final String OK = "OK";
    public static final String KO = "KO";
    public static final String NG = "NG";
    public static final String STATUS_OK_COLON = "OK:";
    public static final String STATUS_KO_COLON = "KO:";
    public static final int INITIAL_CAPACITY = 16;
    public static final int NEGATIVE_ONE = -1;
    public static final int MAX_INT_DATA = *********;
    public static final int INT_ZERO = 0;
    public static final int INT_ONE = 1;
    public static final int INT_TWO = 2;
    public static final int INT_THREE = 3;
    public static final int INT_FOUR = 4;
    public static final int INT_FIVE = 5;
    public static final int INT_SIX = 6;
    public static final int INT_SEVEN = 7;
    public static final int INT_EIGHT = 8;
    public static final int INT_NINE = 9;
    public static final int INT_TEN = 10;
    public static final int INT_FOURTEEN = 14;
    public static final int INT_FIFTEEN = 15;
    public static final int INT_THIRTY = 30;
    public static final int INT_ONE_HUNDRED = 100;
    public static final int INT_ONE_HUNDRED_ONE = 101;
    public static final int INT_TWO_HUNDRED = 200;
    public static final long LONG_ZERO = 0L;
    public static final double DOUBLE_ZERRO = 0D;
    public static final double DOUBLE_ONE = 1D;
    public static final double DOUBLE_FOUR = 4D;
    public static final float FLOAT_ZERRO = 0F;
    public static final String EXCEL_XLSX = ".xlsx";
    public static final String EXCEL_XLS = ".xls";
    public static final String AUTHORITY_READ = "_READ";
    public static final String AUTHORITY_CREATE = "_CREATE";
    public static final String AUTHORITY_UPDATE = "_UPDATE";
    public static final String AUTHORITY_DELETE = "_DELETE";
    public static final String AUTHORITY_IMPORT = "_IMPORT";
    public static final String AUTHORITY_EXPORT = "_EXPORT";
    public static final String AUTHORITY_READ_DESCRIPTION = "浏览";
    public static final String AUTHORITY_CREATE_DESCRIPTION = "新建";
    public static final String AUTHORITY_UPDATE_DESCRIPTION = "修改";
    public static final String AUTHORITY_DELETE_DESCRIPTION = "删除";
    public static final String AUTHORITY_IMPORT_DESCRIPTION = "导入";
    public static final String AUTHORITY_EXPORT_DESCRIPTION = "导出";
    public static final int ERROR_ALERT = 500;

    /**
     * 时间
     **/
    public static final String TIME = "time";

    /**
     * 预警标准,停线标准最大值
     */
    public static final int WARING_STOP_RATE_NUMBER = 1;

    /**
     * 对应数据字典良率预警配置里的 不良KEY  数字字典编码: key_step_warning_standard_target
     */
    public static final int WARNING_STANDARD_TARGET_UNQUALIFIED = 1;

    /**
     * 对应数据字典不良项目KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int UNQUALIFIED_ITEM = 8;

    /**
     * 对应数据字典不良种类KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int UNQUALIFIED_GROUP = 7;

    /**
     * 对应数字字典产品谱系KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int PEDIGREE_ELEMENT = 0;

    /**
     * 对应数字字典工单KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int WORKSHEET_ELEMENT = 1;

    /**
     * 对应数字字典工单类型KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int WORKSHEET_CATEGORY_ELEMENT = 2;

    /**
     * 对应数字字典工序组KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int STEP_GROUP_ELEMENT = 3;

    /**
     * 对应数字字典工序KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int STEP_ELEMENT = 4;

    /**
     * 对应数字字典工艺路线KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int WORKFLOW_ELEMENT = 5;

    /**
     * 对应数字字典客户KEY  数字字典编码: key_step_warning_standard_condition
     */
    public static final int CLIENT_ELEMENT = 6;


    public static final int UNQUALIFIED_ITEM_ELEMENT = 8;


    public static final int UNQUALIFIED_GROUP_ELEMENT = 7;


    /**
     * 单工单投产,多工单投产系统配置
     */
    public static final String KEY_SINGLE_WS_PRODUCT = "key_single_ws_product";

    /**
     * 在线返修总工单生成工单号的KEY
     */
    public static final String KEY_SERIAL_NUMBER_ONLINE_REWORK_WORK_SHEET = "key_online_re_work_sheet_serial_number";
    /**
     * 正常工单生成工单号的KEY
     */
    public static final String KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET = "key_normal_work_sheet_serial_number";
    /**
     * 正常子工单生成工单号的KEY
     */
    public static final String KEY_SERIAL_NUMBER_NORMAL_SUB_WORK_SHEET = "key_sub_work_sheet_serial_number";
    /**
     * FQC批退返修单工单号KEY
     */
    public static final String KEY_SERIAL_NUMBER_FQC_REWORK_WORK_SHEET = "key_fqc_re_work_sheet_serial_number";
    /**
     * 工单类型对应数据字典KEY
     */
    public static final String WORK_SHEET_CATEGORY = "key_work_sheet_category";
    /**
     * 工单类型对应数据字典真实类型，目前为Int类型字段
     */
    public static final String WORK_SHEET_CATEGORY_DATA = "value";
    /**
     * 工单类型对应数据字典key
     */
    public static final String WORK_SHEET_CATEGORY_DATA_KEY = "key";
    /**
     * 工单编码对应数据字典KEY
     */
    public static final String SERIAL_NUMBER_CONFIG_CODE = "serialNumberConfigCode";

    /**
     * 生产过程批预警流水号生成KEY
     */
    public static final String UNQUALIFIED_EVENT_WARING_SERIAL_NUMBER = "key_unqualified_event_waring_serial_number";

    /**
     * 工序不良复检单号
     */
    public static final String STEP_REINSPECT_SERIAL_NUMBER = "key_step_reinspect_code";

    /**
     * 生产过程批停线流水号生成KEY
     */
    public static final String UNQUALIFIED_EVENT_STOP_SERIAL_NUMBER = "key_unqualified_event_stop_serial_number";

    /**
     * 异常事件流水号生成key
     */
    public static final String ABNORMAL_EVENT_SERIAL_NUMBER = "key_abnormal_event_serial_number";

    /**
     * 工序流水号生成key
     */
    public static final String KEY_STEP_CODE = "key_step_code";

    /**
     * 工序组流水号生成key
     */
    public static final String KEY_STEP_GROUP_CODE = "key_step_group_code";

    /**
     * 工艺路线流水号生成key
     */
    public static final String KEY_WORK_FLOW_CODE = "key_work_flow_code";

    /**
     * 不良种类流水号生成key
     */
    public static final String KEY_UNQUALIFIED_GROUP_CODE = "key_unqualified_group_code";

    /**
     * 不良项目流水号生成key
     */
    public static final String KEY_UNQUALIFIED_ITEM_CODE = "key_unqualified_item_code";

    /**
     * 只分一个单的子工单默认流水号
     */
    public static final String SUB_WORK_SHEET_SERIAL_NUMBER_FIRST = "-001";
    /**
     * 是否验证工单类型领料的KEY
     */
    public static final String KEY_CHECK_RECEIVE_MATERIAL = "key_work_sheet_category_check_material_inventory";

    /**
     * 动态数据流水号生成key
     */
    public static final String KEY_STEP_DYNAMIC_DATA_CODE = "key_step_dynamic_data_code";

    /**
     * 动态数据元数据流水号生成key
     */
    public static final String KEY_STEP_DYNAMIC_DATA_COLUMN_CODE = "key_step_dynamic_data_column_code";


    /**
     * Rworker缓存有效期(天)
     */
    public static final String KEY_RWORKER_CACHE_EXPIRE_DAY = "key_rworker_cache_expire_day";

    /**
     * 生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)
     */
    public static final String MATERIAL_INVENTORY_LEVEL = "key_material_inventory_control_level";

    /**
     * 产品谱系最后一层级(系统编码里配置的产品谱系的层级KEY)
     * 系统编码最大值为最小层级
     */
    public static final String KEY_PEDIGREE_LEVEL = "key_pedigree_level";

    /**
     * 是否开启工单审批流
     */
    public static final String KEY_FLOWABLE_WORK_SHEET = "key_flow_able_work_sheet";

    /**
     * 产成品降级生成编码的KEY
     */
    public static final String KEY_PEDIGREE_DOWN_GRADE_CODE = "key_pedigree_down_grade_code";

    /**
     * 工序动态子元数据编码KEY
     */
    public static final String KEY_STEP_DYNAMIC_DATA_SUB_COLUMN_CODE = "key_step_dynamic_data_sub_column_code";

    /**
     * 生成流水号-引用服务 的实体 organization
     */
    public static final String MODULE_CONFIG_ORGANIZATION = "organization";
    /**
     * 生成流水号-引用服务 的实体material
     */
    public static final String MODULE_CONFIG_MATERIAL = "material";
    /**
     * 生成流水号-引用服务的实体 workSheet
     */
    public static final String MODULE_CONFIG_WORKSHEET = "workSheet";


    /**
     * 异常默认处理流程 key
     */
    public static final String KEY_DEFAULT_ABNORMAL_PROCESS = "key_default_abnormal_process";


    /**
     * 异常处理流程key
     */
    public static final String FLOWABLE_ABNORMAL_DEFINITION_KEY = "AbnormalProcess";

    /**
     * 工单下单流程key
     */
    public static final String FLOWABLE_WORKSHEET_PROCESS_KEY = "WorkSheetProcess";

    /**
     * 工单下单处理流程key（放弃更换BOM）
     */
    public static final String FLOWABLE_WORKSHEET_APS_CANCEL_KEY = "apsCancel";

    /**
     * 工单下单处理流程key（放弃定制工艺流程）
     */
    public static final String FLOWABLE_WORKSHEET_PROCESS_CANCEL_KEY = "processCancel";

    /**
     * 工单下单处理流程key（同意工艺流程审批）
     */
    public static final String FLOWABLE_WORKSHEET_PROCESS_APPROVE_KEY = "processApprove";

    /**
     * 工单下单处理流程key（同意且采用默认型号流程）
     */
    public static final String FLOWABLE_WORKSHEET_BIND_DEFAULT_PROCESS_KEY = "bindDefaultProcess";

    /**
     * 设备待校准任务Code生成规则
     */
    public static final String KEY_MESSAGE_TASK_CODE = "key_message_task_code";

    /**
     * 消息系统对接设置
     */
    public static final String KEY_SET_MESSAGE = "key_set_message";

    /**
     * 员工脱岗时长验证配置
     */
    public static final String KEY_VALIDATE_OFF_JOB = "key_validate_off_job";

    /**
     * 下单审批流程配置
     */
    public static final String KEY_WORKSHEET_PROCESS = "key_worksheet_process";


    /**
     * OEE类别
     */
    public static final String KEY_OEE_CATEGORY = "key_oee_category";

    /**
     * 投产粒度
     */
    public static final String KEY_PRODUCTION_MODE = "key_production_mode";

    public static final String KEY_SN_INSPECT = "key_sn_inspect";

    /**
     * 是否需要密码登陆Rworke
     */
    public static final String KEY_PASSWORD_LOGIN_RWORKER = "key_password_login_rworker";

    /**
     * 工单审批流在flowable里定义的key
     */
    public static final String FLOW_ABLE_WORK_SHEET_PROCESS_DEFINATION_KEY = "WorkSheetProcess";

    /**
     * 质量检测类型
     */
    public static final String KEY_QUALITY_CHECK_CATEGORY = "key_quality_check_category";

    /**
     * 质量抽样方式
     */
    public static final String KEY_QUALITY_CHECK_WAY = "key_quality_check_way";

    /**
     * 质量抽样方式
     */
    public static final String KEY_QUALITY_CHECK_VARIETY = "key_quality_check_variety";

    /**
     * 返工单工序物料管控
     */
    public static final String KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL = "key_online_maintain_material_control";


    /**
     * 返修单工序物料管控
     */
    public static final String KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL = "key_offline_maintain_material_control";


    /**
     * 转工艺工序物料管控
     */
    public static final String KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL = "key_transfer_maintain_material_control";


    /**
     * 工序生产是否严格验证是否已配置上料规则
     */
    public static final String KEY_VALIDATE_MATERIAL_RULE = "key_validate_material_rule";

    /**
     * 是否系统自动核料
     */
    public static final String KEY_AUTO_CHECK_MATERIAL = "key_auto_check_material";


    /**
     * 易损件批次流水号
     */
    public static final String KEY_WEARING_PART_BATCH_CODE = "key_wearing_part_batch_code";

    public static final String KEY_FAI_SERIAL_NUMBER = "key_fai_serial_number";

    public static final String KEY_IPQC_SERIAL_NUMBER = "key_ipqc_serial_number";

    public static final String KEY_PQC_SERIAL_NUMBER = "key_pqc_serial_number";

    public static final String KEY_FQC_SERIAL_NUMBER = "key_fqc_serial_number";

    public static final String KEY_LQC_SERIAL_NUMBER = "key_lqc_serial_number";

    public static final String UNDERLINE = "_";
    public static final String SLASH = "/";
    public static final String STR_COMMA = ",";
    public static final String STR_POINT = ".";
    public static final String STR_SEMICOLON = ";";
    public static final String HYPHEN = "-";
    public static final String STR_PRAGMA = "#";
    public static final String EMPTY = "";
    public static final String LEFTPARENTHESIS = "(";
    public static final String RIGHTPARENTHESIS = ")";

    public static final String PERCENT = "%";

    /**
     * 工单状态相关常量
     */
    //已取消
    public static final int CANCEL = -2;
    //审批中
    public static final int APPROVE = -1;
    //已下单，
    public static final int CREATED = 0;
    //投产中
    public static final int PRODUCING = 1;
    //已暂停
    public static final int PAUSE = 2;
    //已完成
    public static final int FINISH = 3;
    //正常结单
    public static final int NORMAL_CLOSED = 4;
    //异常结单
    public static final int ABNORMAL_CLOSED = 5;


    /**
     * 预警事件处理意见
     */
    //预警事件暂停
    public static final int UNQUALIFIED_EVENT_RESTORE = 0;
    //预警事件恢复
    public static final int UNQUALIFIED_EVENT_PAUSE = 1;


    public static final int DAYOFSECOND = 86400;
    public static final int HOUROFSECOND = 3600;
    public static final int MINUTEOFSECOND = 60;
    public static final int EXPORTNUMBER = 3000;

    /**
     * 质量检测类型0:首检;
     */
    public static final int INSPECT_FAI_CATEGORY = 0;
    /**
     * 质量检测类型1:巡检
     */
    public static final int INSPECT_IPQC_CATEGORY = 1;
    /**
     * 质量检测类型2:末检
     */
    public static final int INSPECT_LQC_CATEGORY = 2;
    /**
     * 质量检测类型3:终检
     */
    public static final int INSPECT_FQC_CATEGORY = 3;
    /**
     * 质量检测类型4:抽检
     */
    public static final int INSPECT_PQC_CATEGORY = 4;

    /**
     * 是否全开不良
     */
    public static final String KEY_ALL_UNQUALIFIED = "key_all_unqualified";
    /**
     * 工作日历工作时间配置
     */
    public static final String KEY_WORK_DAY_TIME = "key_work_day_time";

    /**
     * 抽样方案类型
     */
    public static final String KEY_SAMPLE_CATEGORY = "key_sample_category";


    /***
     * 优先级配置key
     */
    public static final String KEY_PRIORITY_ELEMENT_CONFIG = "key_business_rule_combination";

    public static final String KEY_WORK_SHEET_STATUS = "key_work_sheet_status";

    public static final String KEY_WORK_CELL_FACILITY_CONFIG = "key_work_cell_facility_config";

    /**
     * 物料批次号生成
     */
    public static final String KEY_MATERIAL_LOT_SERIAL_NUMBER = "key_material_lot_serial_number";

    private Constants() {

    }

}
