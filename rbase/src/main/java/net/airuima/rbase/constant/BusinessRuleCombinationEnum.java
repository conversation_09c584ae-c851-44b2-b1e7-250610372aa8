package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
public enum BusinessRuleCombinationEnum {

    /**
     * 产品谱系
     */
    PEDIGREE("pedigree", 0, "产品谱系"),

    /**
     * 工单
     */
    WORKSHEET("workSheet", 1, "工单"),

    /**
     * 工单类型
     */
    WORKSHEETCATEGORY("workSheetCategory", 2, "工单类型"),

    /**
     * 工序组
     */
    STEPGROUP("stepGroup", 3, "工序组"),

    /**
     * 工序
     */
    STEP("step", 4, "工序"),

    /**
     * 工艺路线
     */
    WORKFLOW("workFlow", 5, "工艺路线"),

    /**
     * 客户
     */
    CLIENTDTO("clientDTO", 6, "客户"),

    /**
     * 不良种类
     */
    UNQUALIFIEDGROUP("unqualifiedGroup", 7, "不良种类"),

    /**
     * 不良项目
     */
    UNQUALIFIEDITEM("unqualifiedItem", 8, "不良项目"),

    /**
     * 工位
     */
    WORKCELL("workCell", 9, "工位"),

    /**
     * 物料
     */
    MATERIAL("material", 10, "物料"),

    /**
     * 物料属性
     */
    MATERIAL_ATTRIBUTE("material_attribute", 11, "物料属性"),

    /**
     * 供应商
     */
    SUPPLIER("supplier", 12, "供应商")

    ;


    /**
     * 类型编码
     */
    private String categoryCode;

    /**
     * 类型名称
     */
    private int categoryValue;

    /**
     * 说明信息
     */
    private String remark;

    BusinessRuleCombinationEnum(String categoryCode, int categoryValue, String remark) {
        this.categoryCode = categoryCode;
        this.categoryValue = categoryValue;
        this.remark = remark;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public BusinessRuleCombinationEnum setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return this;
    }

    public int getCategoryValue() {
        return categoryValue;
    }

    public BusinessRuleCombinationEnum setCategoryValue(int categoryValue) {
        this.categoryValue = categoryValue;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public BusinessRuleCombinationEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
