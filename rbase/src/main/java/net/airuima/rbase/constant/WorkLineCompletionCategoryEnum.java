package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产线产出类型国际化key
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
public enum WorkLineCompletionCategoryEnum {

    /**
     * 计划产出 Plan 实际产出 Actual
     */
    PLAN("计划产出", "Plan"),
    ACTUAL("实际产出", "Actual");

    WorkLineCompletionCategoryEnum(String name, String key) {
        this.name = name;
        this.key = key;
    }

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final String key;

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }
}
