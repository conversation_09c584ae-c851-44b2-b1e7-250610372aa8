package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产计划粒度类型
 *
 * <AUTHOR>
 * @date 2023/07/04
 */
public enum ProductionPlanCategoryEnum {

    /**
     * 0 工序组 1生产线
     */
    STEP_GROUP("工序组", 0),
    WORK_LINE("生产线", 1);


    ProductionPlanCategoryEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final int category;

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }

}
