package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产出推移图类型枚举
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
public enum StepProductCategoryEnum {

    /**
     * 工序1 工序组2
     */
    STEP("工序", 1),
    STEP_GROUP("工序组", 2),
    ;


    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final Integer category;


    StepProductCategoryEnum(String name, Integer category) {
        this.name = name;
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public Integer getCategory() {
        return category;
    }
}
