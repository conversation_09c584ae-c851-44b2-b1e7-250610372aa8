package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/4
 */
public enum WorkCellStartCheckFlagEnum {

    /**
     * 切换型号
     */
    MODEL_CHANGE("MODEL_CHANGE",0,"切换型号"),
    /**
     * 切换总工单
     */
    MAIN_WORKSHEET_CHANGE("MAIN_WORKSHEET_CHANGE",1,"切换总工单"),
    /**
     * 切换子工单
     */
    SUB_WORKSHEET_CHANGE("SUB_WORKSHEET_CHANGE",2,"切换子工单"),
    /**
     * 固定周期
     */
    FIXED_INTERVAL("FIXED_INTERVAL",3,"固定周期"),
    /**
     * 指定时间
     */
    SPECIFIC_TIME("SPECIFIC_TIME",4,"指定时间");

    private String flag;

    private int category;

    private String remark;


    WorkCellStartCheckFlagEnum(String flag, int category, String remark) {
        this.flag = flag;
        this.category = category;
        this.remark = remark;
    }

    public static String getRemark(int status){
        for (WorkCellStartCheckFlagEnum enums : WorkCellStartCheckFlagEnum.values()){
            if(enums.getCategory() == status){
                return enums.getRemark();
            }
        }
        return null;
    }

    public String getFlag() {
        return flag;
    }

    public WorkCellStartCheckFlagEnum setFlag(String flag) {
        this.flag = flag;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCellStartCheckFlagEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WorkCellStartCheckFlagEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
