package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序不良导入表头常量
 *
 * <AUTHOR>
 * @date 2023/08/05
 */
public class PedigreeStepUnqualifiedExcelConstants {

    /**
     * 产品谱系名称
     */
    public static final String PEDIGREE_NAME = "产品谱系名称";

    /**
     * 产品谱系编码
     */
    public static final String PEDIGREE_CODE = "产品谱系编码";

    /**
     * 工艺路线名称
     */
    public static final String WORK_FLOW_NAME = "工艺路线名称";

    /**
     * 工艺路线编码
     */
    public static final String WORK_FLOW_CODE = "工艺路线编码";

    /**
     * 工序名称
     */
    public static final String STEP_NAME = "工序名称";

    /**
     * 工序编码
     */
    public static final String STEP_CODE = "工序编码";

    /**
     * 不良项目名称
     */
    public static final String UNQUALIFIED_ITEM_NAME = "不良项目名称";

    /**
     * 不良项目编码
     */
    public static final String UNQUALIFIED_ITEM_CODE = "不良项目编码";

    /**
     * 是否启用(0:否;1:是)
     */
    public static final String IS_ENABLE = "是否启用";

    /**
     * 客户编码
     */
    public static final String CLIENT_CODE = "客户编码";
}
