package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序配置excel导入常量
 *
 * <AUTHOR>
 * @date 2023/08/04
 */
public class PedigreeStepExcelConstants {

    public static final String CLIENT_CODE = "客户编码";

    /**
     * 产品谱系名称
     */
    public static final String PEDIGREE_NAME = "产品谱系名称";

    /**
     * 产品谱系编码
     */
    public static final String PEDIGREE_CODE = "产品谱系编码";

    /**
     * 工艺路线名称
     */
    public static final String WORK_FLOW_NAME = "工艺路线名称";

    /**
     * 工艺路线编码
     */
    public static final String WORK_FLOW_CODE = "工艺路线编码";

    /**
     * 工序名称
     */
    public static final String STEP_NAME = "工序名称";

    /**
     * 工序编码
     */
    public static final String STEP_CODE = "工序编码";

    /**
     * 请求模式
     */
    public static final String REQUEST_MODE = "请求模式";

    /**
     * 管控模式(0:批量;1:单支)
     */
    public static final String CONTROL_MODE = "管控模式";

    /**
     * 是否管控物料(0:不管控;1:管控)
     */
    public static final String IS_CONTROL_MATERIAL = "是否管控物料";

    /**
     * 是否绑定容器(0:否;1:是)
     */
    public static final String IS_BIND_CONTAINER = "是否绑定容器";

    /**
     * 投产比例
     */
    public static final String INPUT_RATE = "投产比例";

    /**
     * 标准工时（精确到秒）
     */
    public static final String STANDARD_TIME = "标准工时";

    /**
     * 工序理论日产出数量
     */
    public static final String STANDARD_DAILY_OUTPUT = "工序理论日产出数量";

    /**
     * 是否启用(0:否;1:是)
     */
    public static final String IS_ENABLE = "是否启用";

}
