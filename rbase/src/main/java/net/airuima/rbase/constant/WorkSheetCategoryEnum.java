package net.airuima.rbase.constant;

public enum WorkSheetCategoryEnum {

    /**
     * 0: 返修单：（离线返修 可以收录 不是投产与返修中的 sn 进行投产的工单类型）
     * -1：返工单：（在线返修  维修分析产生，或者返工单界面批量创建）
     * 1：正常单：
     * 2： 外协工单
     */
    NORMAL_CATEGORY("key_normal_work_sheet_serial_number", 1, "正常单"),
    OFFLINE_CATEGORY("offline_category", 0, "返修单"),
    ONLINE_CATEGORY("key_online_re_work_sheet_serial_number", -1, "返工单"),
    OEM_CATEGORY("key_oem_work_sheet_serial_number", 2, "外协工单");


    private String key;

    private Integer category;

    private String description;

    WorkSheetCategoryEnum(String key, Integer category, String description) {
        this.key = key;
        this.category = category;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public WorkSheetCategoryEnum setKey(String key) {
        this.key = key;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public WorkSheetCategoryEnum setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public WorkSheetCategoryEnum setDescription(String description) {
        this.description = description;
        return this;
    }
}
