package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 管控模式枚举
 *
 * <AUTHOR>
 * @date 2023/08/05
 */
public enum ControlModeEnum {
    /**
     * 批量管控模式
     */
    BATCH_CONTROL_MODE("批量",0),

    /**
     * 单支管控模式
     */
    SN_CONTROL_MODE("单支",1);

    private final String mode;
    private final int code;

    ControlModeEnum(String mode, int code) {
        this.mode = mode;
        this.code = code;
    }

    public String getMode() {
        return this.mode;
    }

    public int getCode() {
        return this.code;
    }

    public static int getCodeByMode(String mode) {
        for (ControlModeEnum cm : values()) {
            if (cm.getMode().equals(mode)) {
                return cm.getCode();
            }
        }
        throw new IllegalArgumentException("Invalid mode: " + mode);
    }
}
