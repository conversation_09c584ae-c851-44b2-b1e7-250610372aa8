package net.airuima.rbase.constant;

/**
 *
 * Sn-fpp状态枚举
 * 状态(0:测试中,1：测试结果合格,2：测试结果不合格，3：超次数上限不合格)
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public enum SnStepFppStatusEnum {
    //状态(0:测试中,1：测试结果合格,2：测试结果不合格，3：超次数上限不合格)
    TESTING("TESTING",0,"FPP测试进行中"),
    PASSED("PASSED",1,"测试结果合格"),
    FAILED("FAILED",2,"测试结果不合格"),
    EXCEED_MAX_TEST_TIMES("EXCEED_MAX_TEST_TIMES",3,"超次数上限不合格");
    private String status;

    private int category;

    private String remark;

    SnStepFppStatusEnum(String status, int category, String remark) {
        this.status = status;
        this.category = category;
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public SnStepFppStatusEnum setStatus(String status) {
        this.status = status;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public SnStepFppStatusEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public SnStepFppStatusEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
