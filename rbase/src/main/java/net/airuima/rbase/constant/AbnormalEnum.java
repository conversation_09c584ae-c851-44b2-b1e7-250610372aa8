package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
public enum AbnormalEnum {

    //预警级别(-1:无;0:工位预警;1:子工单预警;2:工单预警;3:型号预警;4:设备预警;5:员工预警;6:物料预警;7:工序预警;8:产线预警;9:车间预警)

    /**
     * 待分析
     */
    ABNORMAL_TO_ANALYSE("ABNORMAL_TO_ANALYSE",-2,""),

    /**
     * 不做任何预警
     */
    NO_WARNING("NO_WARNING",-1,"无预警"),

    /**
     * 员工异常
     */
    STAFF_WARNING("STAFF_WARNING",5,"员工"),

    /**
     * 设备异常
     */
    FACILITY_WARNING("FACILITY_WARNING",4,"设备"),

    /**
     * 工位异常
     */
    WORK_CELL_WARNING("WORK_CELL_WARNING",0,"工位"),

    /**
     * 子工单异常
     */
    SUB_WORK_SHEET_WARNING("SUB_WORK_SHEET_WARNING",1,"子工单"),

    /**
     * 工单异常
     */
    WORK_SHEET_WARNING("WORK_SHEET_WARNING",2,"工单"),

    /**
     * 工序异常
     */
    STEP_WARNING("STEP_WARNING",7,"工序"),


    /**
     * 型号异常
     */
    PEDIGREE_WARNING("PEDIGREE_WARNING",3,"产品"),

    /**
     * 产线异常
     */
    WORK_LINE_WARNING("WORK_LINE_WARNING",8,"产线"),

    /**
     * 车间异常
     */
    ORGANIZATION_WARNING("ORGANIZATION_WARNING",9,"车间"),

    /**
     * 物料异常
     */
    MATERIAL_WARNING("MATERIAL_WARNING",6,"物料");

    /**
     * 类型编码
     */
    private String warningLevelCode;

    /**
     * 类型名称
     */
    private int warningLevelValue;

    /**
     * 说明
     */
    private String remark;


    AbnormalEnum(String warningLevelCode, int warningLevelValue,String remark) {
        this.warningLevelCode = warningLevelCode;
        this.warningLevelValue = warningLevelValue;
        this.remark = remark;
    }


    public static AbnormalEnum findByEnumByWarningLevelValue(int warningLevelValue){
        for (AbnormalEnum abnormalEnum : AbnormalEnum.values()){
            if(abnormalEnum.getWarningLevelValue() == warningLevelValue){
                return abnormalEnum;
            }
        }
        throw  new IllegalArgumentException("warningLevel is invalid");
    }

    public String getWarningLevelCode() {
        return warningLevelCode;
    }

    public AbnormalEnum setWarningLevelCode(String warningLevelCode) {
        this.warningLevelCode = warningLevelCode;
        return this;
    }

    public int getWarningLevelValue() {
        return warningLevelValue;
    }

    public AbnormalEnum setWarningLevelValue(int warningLevelValue) {
        this.warningLevelValue = warningLevelValue;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public AbnormalEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
