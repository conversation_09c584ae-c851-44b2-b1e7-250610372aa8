package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工位发起检测类型枚举
 * <AUTHOR>
 * @date 2023/1/31
 */
public enum WorkCellStartCheckEnum {

    /**
     * 首检
     */
    FIRST_INSPECTION("FIRST_INSPECTION",0,"首检"),

    /**
     * 巡检
     */
    IPQC_INSPECTION("IPQC_INSPECTION",1,"巡检"),

    /**
     * 末检
     */
    LQC_INSPECTION("LQC_INSPECTION",2,"末检"),

    /**
     * 抽检
     */
    SIMPLE_INSPECTION("SIMPLE_INSPECTION",4,"抽检"),

    /**
     * 终检
     */
    LAST_INSPECTION("LAST_INSPECTION",3,"终检"),

    /**
     * 来料检
     */
    IQC_INSPECTION("IQC_INSPECTION",5,"来料检"),

    /**
     * 外协入库检
     */
    OEM_INSPECTION("OEM_INSPECTION",6,"外协入库检");

    private String wcStartCheckCategory;

    private int category;

    private String remark;

    WorkCellStartCheckEnum(String wcStartCheckCategory, int category, String remark) {
        this.wcStartCheckCategory = wcStartCheckCategory;
        this.category = category;
        this.remark = remark;
    }

    public String getWcStartCheckCategory() {
        return wcStartCheckCategory;
    }

    public WorkCellStartCheckEnum setWcStartCheckCategory(String wcStartCheckCategory) {
        this.wcStartCheckCategory = wcStartCheckCategory;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCellStartCheckEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WorkCellStartCheckEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
