package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rule服务中的枚举
 * <AUTHOR>
 * @date 2023/2/14
 */
public enum RuleEnum {
    /**
     * 事件发起来源：系统发起
     */
    EVENT_SOURCE_SYSTEM("EVENT_SOURCE_SYSTEM",Constants.INT_ONE),

    /**
     * 事件发起来源：人工发起
     */
    EVENT_SOURCE_MANUAL("EVENT_SOURCE_MANUAL",Constants.INT_ZERO);


    /**
     * 类型编码
     */
    private String categoryCode;

    /**
     * 类型名称
     */
    private int categoryValue;

    /**
     * 说明信息
     */
    private String remark;

    RuleEnum(String categoryCode, int categoryValue) {
        this.categoryCode = categoryCode;
        this.categoryValue = categoryValue;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public RuleEnum setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return this;
    }

    public int getCategoryValue() {
        return categoryValue;
    }

    public RuleEnum setCategoryValue(int categoryValue) {
        this.categoryValue = categoryValue;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public RuleEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
