package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * sn生产状态信息
 * <AUTHOR>
 * @date 2022/11/7
 */
public enum SnWorkStatusEnum {
    /**
     * 待投产
     */
    TO_BE_PUT_INTO_PRODUCTION("TO_BE_PUT_INTO_PRODUCTION",0,"待投产"),
    /**
     * 投产中
     */
    PUT_INTO_PRODUCTION("PUT_INTO_PRODUCTION",1,"投产中"),
    /**
     * 待返修
     */
    TO_BE_REPAIR("TO_BE_REPAIR",2,"待返修"),
    /**
     * 返修中
     */
    IN_THE_REPAIR("IN_THE_REPAIR",3,"返修中"),
    /**
     * 合格
     */
    QUALIFIED("QUALIFIED",4,"合格"),
    /**
     * 报废
     */
    SCRAP("SCRAP",5,"报废"),
    /**
     * 维修分析
     */
    MAINTAIN("MAINTAIN",6,"维修分析"),

    /**
     * 退库
     */
    RETURN_STOCK("RETURN_STOCK",7,"退库"),

    /**
     * 复检
     */
    STEP_REINSPECT("STEP_REINSPECT",8,"复检");

    private String snWorkStatus;

    private int status;

    private String remark;

    SnWorkStatusEnum(String snWorkStatus, int status, String remark) {
        this.snWorkStatus = snWorkStatus;
        this.status = status;
        this.remark = remark;
    }

    public static String getRemark(int status){
        for (SnWorkStatusEnum value : SnWorkStatusEnum.values()) {
            if (value.getStatus() == status){
                return  value.getRemark();
            }
        }
        return null;
    }

    public String getSnWorkStatus() {
        return snWorkStatus;
    }

    public SnWorkStatusEnum setSnWorkStatus(String snWorkStatus) {
        this.snWorkStatus = snWorkStatus;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SnWorkStatusEnum setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public SnWorkStatusEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
