package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
public enum RworkerCacheEnum {
    SUB_WORK_SHEET_STEP_REQUEST(0,"子工单请求工序"),
    WORK_SHEET_STEP_REQUEST(1,"工单请求工序"),
    CONTAINER_STEP_REQUEST(2,"容器请求工序"),
    SN_STEP_REQUEST(3,"单支请求工序");

    /**
     * 请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     */
    private int category;

    /**
     * 工序对象类型
     */
    private String remark;

    RworkerCacheEnum(int category,String remark){
        this.category = category;
        this.remark=remark;
    }

    public int getCategory() {
        return category;
    }

    public RworkerCacheEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public RworkerCacheEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
