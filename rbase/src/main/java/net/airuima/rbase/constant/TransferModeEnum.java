package net.airuima.rbase.constant;

/**
 * 工序流转模式枚举类
 */
public enum TransferModeEnum {

    CONTINUOUS(0, "连续流转"),
    INDEPENDENT(1, "独立流转"),
    SPLIT(2, "分拆流转");

    private final int value;
    private final String description;

    TransferModeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    //通过value获取描述信息
    public static String getDescriptionByValue(Integer value) {
        for (TransferModeEnum mode : values()) {
            if (mode.value == value) {
                return mode.description;
            }
        }
        return null;
    }

    public static TransferModeEnum fromValue(Integer value) {
        if (value == null) return CONTINUOUS;
        for (TransferModeEnum mode : values()) {
            if (mode.value == value) {
                return mode;
            }
        }
        return CONTINUOUS;
    }
}
