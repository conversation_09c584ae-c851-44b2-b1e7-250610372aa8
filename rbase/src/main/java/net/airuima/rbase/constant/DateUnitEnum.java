package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 日期单位枚举类
 * <AUTHOR>
 * @create 2023/4/19
 */
public enum DateUnitEnum {
    /**
     * 天
     */
    DAY("天",0),
    /**
     * 月
     */
    MONTH("月",1),
    /**
     * 年
     */
    YEAR("年",2);

    private String type;

    private int status;

    DateUnitEnum(String type, int status) {
        this.type = type;
        this.status = status;
    }

    /**
     * 根据 日期类型字符串 获取对应的 枚举实体
     */
    public static DateUnitEnum getType(String type){
        if (type == null){
            return null;
        }
        for (DateUnitEnum enums : DateUnitEnum.values()){
            if (enums.getType().equals(type)){
                return enums;
            }
        }
        return null;
    }

    /**
     * 根据 日期类型数值类型 获取对应的 枚举实体
     */
    public static DateUnitEnum getStatus(int status){
        for (DateUnitEnum enums : DateUnitEnum.values()){
            if (enums.getStatus() == status){
                return enums;
            }
        }
        return null;
    }



    public String getType() {
        return type;
    }

    public DateUnitEnum setType(String type) {
        this.type = type;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public DateUnitEnum setStatus(int status) {
        this.status = status;
        return this;
    }
}
