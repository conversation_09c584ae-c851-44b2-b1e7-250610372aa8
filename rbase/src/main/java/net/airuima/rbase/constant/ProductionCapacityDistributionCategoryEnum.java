package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产量分布类型
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
public enum ProductionCapacityDistributionCategoryEnum {

    /**
     * 0 部门分布统计 1 产线分布统计
     */
    ORGANIZATION("部门分布统计", 0),
    WORK_LINE("产线分布统计", 1);;

    ProductionCapacityDistributionCategoryEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final int category;

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }


}
