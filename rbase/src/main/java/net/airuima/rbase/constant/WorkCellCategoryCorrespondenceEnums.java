package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
public enum WorkCellCategoryCorrespondenceEnums {
    /**
     * 工位首检对应规则首检
     */
    FIRST_CHECK(3,0,"工位首检对应规则首检"),
    /**
     * 工位巡检对应规则巡检
     */
    PQC_CHECK(2,1,"工位巡检对应规则巡检");

    private int workCellCategory;

    private int checkRuleCategory;

    private String note;

     WorkCellCategoryCorrespondenceEnums(int workCellCategory, int checkRuleCategory, String note) {
        this.workCellCategory = workCellCategory;
        this.checkRuleCategory = checkRuleCategory;
        this.note = note;
    }

    public static WorkCellCategoryCorrespondenceEnums getKey(int key){
        for (WorkCellCategoryCorrespondenceEnums enums : WorkCellCategoryCorrespondenceEnums.values()){
            if (enums.getWorkCellCategory() == key){
                return enums;
            }
        }
        return null;
    }

    public int getWorkCellCategory() {
        return workCellCategory;
    }

    public WorkCellCategoryCorrespondenceEnums setWorkCellCategory(int workCellCategory) {
        this.workCellCategory = workCellCategory;
        return this;
    }

    public int getCheckRuleCategory() {
        return checkRuleCategory;
    }

    public WorkCellCategoryCorrespondenceEnums setCheckRuleCategory(int checkRuleCategory) {
        this.checkRuleCategory = checkRuleCategory;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WorkCellCategoryCorrespondenceEnums setNote(String note) {
        this.note = note;
        return this;
    }
}
