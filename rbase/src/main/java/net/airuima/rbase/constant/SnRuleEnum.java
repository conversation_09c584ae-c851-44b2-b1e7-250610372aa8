package net.airuima.rbase.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * 序列号规则枚举类
 * <AUTHOR>
 * @date 2022/11/14
 */
public enum SnRuleEnum {
    /*
    正则分类
    0 以...开头(^{n}.*)
    1 以...结尾(.*{n}$)
    2 长度限制(^.{n}$)
    3 包含某个或某段字符(.*{n}.*)
    4 从第几位开始匹配字符(.{{n}}{n2}.*)
    5 全是数字([0-9]+)
    6 全是大写英文([A-Z]+)
    7 全是小写英文([a-z]+)
    8 英文或数字([0-9a-zA-Z]+)
    9 手动输入正则表达式
     */

    /**
     * 以...开头
     */
    START_WITH("0", "^{n}.*"),

    /**
     * 以...结尾
     */
    END_WITH("1", ".*{n}$"),

    /**
     * 长度限制
     */
    LENGTH_LIMIT("2", "^.{{n}}$"),

    /**
     * 包含某个或某些字符
     */
    CONTAINS_SOME_CHARACTERS("3", ".*{n}.*"),

    /**
     * 从第几位开始匹配字符
     */
    FROM_WHICH_MATCH_CHARACTERS("4", ".{{n}}{n2}.*"),

    /**
     * 全是数字
     */
    ALL_NUMBERS("5", "^[0-9]*$"),

    /**
     * 全是大写英文
     */
    ALL_CAPITAL_ENGLISH("6", "^[A-Z]+$"),

    /**
     * 全是小写英文
     */
    ALL_LOWERCASE_ENGLISH("7", "^[a-z]+$"),


    /**
     * 英文或数字
     */
    ENGLISH_OR_NUMBER("8", "^[A-Za-z0-9]+$"),

    /**
     * 手动输入正则表达式
     */
    MANUALLY_ENTER_REGULAR("9", "");

    /**
     * 分类
     */
    private String category;

    /**
     * 正则
     */
    private String regular;


    SnRuleEnum(String category, String regular) {
        this.category = category;
        this.regular = regular;
    }


    public static String findRegularByCategory(String category) {
        if (StringUtils.isBlank(category)) {
            return "";
        }
        for (SnRuleEnum snRuleEnum : SnRuleEnum.values()) {
            if (snRuleEnum.getCategory().equals(category)) {
                return snRuleEnum.getRegular();
            }
        }
        return category;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getRegular() {
        return regular;
    }

    public void setRegular(String regular) {
        this.regular = regular;
    }
}
