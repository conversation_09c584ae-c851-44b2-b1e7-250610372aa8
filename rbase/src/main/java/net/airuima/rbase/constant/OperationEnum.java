package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 运算类型枚举
 *
 * <AUTHOR>
 * @date 2023-01-05
 */
public enum OperationEnum {

    /**
     * 运算 add 加 minus减
     */
    ADD("add", "加"),
    MINUS("minus", "减"),
    ;

    OperationEnum(String category, String name) {
        this.category = category;
        this.name = name;
    }

    private final String category;
    private final String name;

    public String getCategory() {
        return category;
    }

    public String getName() {
        return name;
    }
}
