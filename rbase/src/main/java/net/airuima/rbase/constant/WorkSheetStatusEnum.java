package net.airuima.rbase.constant;

public enum WorkSheetStatusEnum {

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单，6：收货中)
     */
    CANCEL("cancel", -2, "已取消"),
    APPROVING("approving", -1, "审批中"),
    DEVOTE("devote", 0, "已下单"),
    EXECUTE("execute", 1, "投产中"),
    PAUSE("pause", 2, "暂停"),
    FINISH("finish", 3, "完成"),
    STATEMENT("statement", 4, "正常结单"),
    HALFWAY("halfway", 5, "异常结单"),
    RECEIVING("Receiving", 6, "收货中");

    private String key;

    private Integer status;

    private String description;

    WorkSheetStatusEnum(String key, Integer status, String description) {
        this.key = key;
        this.status = status;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public WorkSheetStatusEnum setKey(String key) {
        this.key = key;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public WorkSheetStatusEnum setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public WorkSheetStatusEnum setDescription(String description) {
        this.description = description;
        return this;
    }
}
