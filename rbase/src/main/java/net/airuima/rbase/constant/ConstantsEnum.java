package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/12/27
 */
public enum ConstantsEnum {

    /**
     * 工单状态 :异常结单
     */
    WORK_SHEET_STATIC_HALFWAY("WORK_SHEET_STATIC_HALFWAY",5),

    /**
     * 工单状态 :正常结单
     */
    WORK_SHEET_STATIC_STATEMENT("WORK_SHEET_STATIC_STATEMENT",4),

    /**
     * 工单状态 :生产完成
     */
    WORK_SHEET_STATIC_FINISH("WORK_SHEET_STATIC_FINISH",3),
    /**
     * 工单状态 :取消
     */
    WORK_SHEET_STATIC_CANCEL("WORK_SHEET_STATIC_CANCEL",-2),

    /**
     * 工单状态 :审批中
     */
    WORK_SHEET_STATIC_APPROVING("WORK_SHEET_STATIC_APPROVING",-1),

    /**
     * 工单状态 :暂停
     */
    WORK_SHEET_STATIC_PAUSE("WORK_SHEET_STATIC_PAUSE",2),
    /**
     * 工单状态 :投产中
     */
    WORK_SHEET_STATIC_EXECUTE("WORK_SHEET_STATIC_EXECUTE",1),

    /**
     * 单状态 :已下单
     */
    WORK_SHEET_STATIC_DEVOTE("WORK_SHEET_STATIC_DEVOTE",0),
    /**
     * 请求模式:工单请求
     */
    WORK_SHEET_REQUEST_MODE("WORK_REQUEST",0),
    /**
     * 请求模式:容器请求
     */
    CONTAINER_REQUEST_MODE("CONTAINER_REQUEST",1),
    /**
     * 请求模式:单支请求
     */
    SN_REQUEST_MODE("SN_REQUEST",2),
    /**
     * 管控模式:0:批量
     */
    BATCH_CONTROL_MODE("BATCH_CONTROL_MODE",0),
    /**
     * 管控模式:1:单支
     */
    SN_CONTROL_MODE("SN_CONTROL_MODE",1),

    /**
     * 工单类型:1:正常单
     */
    WORK_SHEET_CUSTOM_CATEGORY("WORK_SHEET_CUSTOM_CATEGORY",1),

    /**
     * 工单类型:0:返修单
     */
    WORK_SHEET_OFFLINE_CATEGORY("WORK_SHEET_OFFLINE_CATEGORY",0),

    /**
     * 工单类型:-1:返工单
     */
    WORK_SHEET_ONLINE_CATEGORY("WORK_SHEET_ONLINE_CATEGORY",-1),


    /**
     * 整型：true = 1
     */
    TRUE_INT("TRUE_INT",1),
    /**
     * 整型：false = 0
     */
    FALSE_INT("FALSE_INT",0),
    /**
     * 整型：绑定 = 1
     */
    BINDING("BINDING",1),
    /**
     * 整型：解绑 = 0
     */
    UNBIND("UNBIND",0),
    /**
     * 根据配置来判断获取不良生成在线返修单的规则，isStepOrWsLevel：isStepLevel(子工单工序完成层级)：1
     */
    IS_STEP_LEVEL("IS_STEP_LEVEL",1),
    /**
     * 根据配置来判断获取不良生成在线返修单的规则，isStepOrWsLevel：isWsLevel(子工单完成层级):0
     */
    IS_WS_LEVEL("IS_WS_LEVEL",0),
    /**
     * 处理方式(0,在线返修）
     */
    DEAL_WAY_ONLINE_REPAIR("DEAL_WAY_ONLINE_REPAIR",0),
    /**
     * 处理方式(1,流程返修）
     */
    DEAL_WAY_PROCESS_REPAIR("DEAL_WAY_PROCESS_REPAIR",1),
    /**
     * 处理方式(2,报废）
     */
    DEAL_WAY_SCRAP("DEAL_WAY_SCRAP",2),

    /**
     * 上料规则
     * 0:单支序列号
     */
    MATERIAL_RULE_SN("MATERIAL_RULE_SN",0),

    /**
     * 上料规则
     * 1:物料批次
     */
    MATERIAL_RULE_BATCH("MATERIAL_RULE_BATCH",1),

    /**
     * 物料管控层级
     * 0:不管控物料库存
     */
    MATERIAL_NOT_CONTROL_LEVEL("MATERIAL_NOT_CONTROL_LEVEL",0),
    /**
     * 物料管控层级
     * 1:总工单物料库存
     */
    MATERIAL_WORKSHEET_CONTROL_LEVEL("MATERIAL_WORKSHEET_CONTROL_LEVEL",1),
    /**
     * 物料管控层级
     * 2:工位物料库存
     */
    MATERIAL_WORK_CELL_CONTROL_LEVEL("MATERIAL_WORK_CELL_CONTROL_LEVEL",2),

    /**
     * 完成状态：1
     *
     */
    FINISH_STATUS("FINISH_STATUS",1),
    /**
     * 未完成状态：0
     *
     */
    UNFINISHED_STATUS("UNFINISHED_STATUS",0),

    /**
     *
     * 0:总工单物料库存
     */
    WS_MATERIAL_BATCH_ROLL_BACK("WS_MATERIAL_BATCH_ROLL_BACK",0),
    /**
     *
     * 1:工位物料库存
     */
    WS_WC_MATERIAL_BATCH_ROLL_BACK("WS_WC_MATERIAL_BATCH_ROLL_BACK",1),

    /**
     * 核料状态：未核料 ，0
     *
     */
    NOT_CHECK_MATERIAL("NOT_CHECK_MATERIAL",0),

    /**
     * 核料状态：部分核料 ，1
     *
     */
    SEGMENT_CHECK_MATERIAL("SEGMENT_CHECK_MATERIAL",1),

    /**
     * 核料状态：全部核料 ，2
     *
     */
    ALL_CHECK_MATERIAL("ALL_CHECK_MATERIAL",2),
    /**
     * 前缀类型：0:无前缀
     *
     */
    SERIALNUMBER_NOT_PREFIX("SERIALNUMBER_NOT_PREFIX",0),

    /**
     * 前缀类型：1:固定前缀
     *
     */
    SERIALNUMBER_FIXED_PREFIX("SERIALNUMBER_FIXED_PREFIX",1),
    /**
     * 前缀类型：2:动态前缀
     *
     */
    SERIALNUMBER_DYNAMIC_PREFIX("SERIALNUMBER_DYNAMIC_PREFIX",2),

    /**
     * 工序类型：0:普通工序
     */
    STEP_CUSTOM_CATEGORY("STEP_CUSTOM_CATEGORY",0),

    /**
     * 工序类型：1:在线调整工序
     */
    STEP_ONLINE_REWORK_CATEGORY("STEP_ONLINE_REWORK_CATEGORY",1),

    /**
     * 工序类型：2:在线预调整工序
     */
    STEP_ONLINE_PRE_REWORK_CATEGORY("STEP_ONLINE_PRE_REWORK_CATEGORY",2),

    /**
     * 工序类型：3:烘烤放入工序
     */
    STEP_BAKE_PUT_IN_CATEGORY("STEP_BAKE_PUT_IN_CATEGORY",3),

    /**
     * 工序类型：4:烘烤取出工序
     */
    STEP_BAKE_PUT_OUT_CATEGORY("STEP_BAKE_PUT_OUT_CATEGORY",4),

    /**
     * 工序类型：5:温循放入工序
     */
    STEP_CYCKE_PUT_IN_CATEGORY("STEP_CYCKE_PUT_IN_CATEGORY",5),

    /**
     * 工序类型：6:温循取出工序
     */
    STEP_CYCKE_PUT_OUT_CATEGORY("STEP_CYCKE_PUT_OUT_CATEGORY",6),

    /**
     * 工序类型：7:老化放入工序
     */
    STEP_AGING_PUT_IN_CATEGORY("STEP_AGING_PUT_IN_CATEGORY",7),

    /**
     * 工序类型：8:老化取出工序
     */
    STEP_AGING_PUT_OUT_CATEGORY("STEP_AGING_PUT_OUT_CATEGORY",8),

    /**
     * 环境检测类型 0：温湿度
     */
    ENVIROMENT_HUMITURE_INSPECT_CATEGORY("ENVIROMENT_HUMITURE_INSPECT_CATEGORY",0),

    /**
     * 环境检测类型 1：洁净度
     */
    ENVIROMENT_CLEANLINESS_INSPECT_CATEGORY("ENVIROMENT_CLEANLINESS_INSPECT_CATEGORY",1),

    /**
     * 不良项目：处理类型：维修分析
     */
    UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE("UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE",3),


    /**
     * 不良项目：处理类型：报废
     */
    UNQUALIFIEDITEM_DEALWAY_SCRAP("UNQUALIFIEDITEM_DEALWAY_SCRAP",2),


    /**
     * 不良项目：处理类型：在线调整
     */
    UNQUALIFIEDITEM_DEALWAY_ONLINE_ADJUSTMENT("UNQUALIFIEDITEM_DEALWAY_ONLINE_ADJUSTMENT",1),

    /**
     * 不良项目：处理类型：返工
     */
    UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK("UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK",0),

    /**
     * 物料类型：主料
     */
    MATERIAL_MAIN_CATEGORY("MATERIAL_MAIN_CATEGORY",1),
    /**
     * 物料类型：辅料
     */
    MATERIAL_NOT_MAIN_CATEGORY("MATERIAL_NOT_MAIN_CATEGORY",0),

    /**
     * 预警停线事件预警类型
     */
    UNQUALIFIED_EVENT_WARNING_TYPE("UNQUALIFIED_EVENT_WARNING_TYPE",0),

    /**
     * 预警停线事件停线类型
     */
    UNQUALIFIED_EVENT_STOP_TYPE("UNQUALIFIED_EVENT_STOP_TYPE",1),

    /**
     * 预警停线事件原因：工序良率不达标
     */
    UNQUALIFIED_EVENT_REASON_PASS_RATE_TYPE("UNQUALIFIED_EVENT_STOP_TYPE",1),

    /**
     * 预警停线事件原因：不良项目超标
     */
    UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE("UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE",0),

    /**
     * 设备正常运行状态：1
     */
    FACILITY_STATUS_RUNNING("FACILITY_STATUS_RUNNING",1),

    /**
     * 设备空闲状态：-1
     */
    FACILITY_STATUS_IDLE("FACILITY_STATUS_IDLE",-1);

    /**
     * 类型编码
     */
    private String categoryCode;

    /**
     * 类型名称
     */
    private int categoryName;

    ConstantsEnum(String categoryCode, int categoryName) {
        this.categoryCode = categoryCode;
        this.categoryName = categoryName;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public ConstantsEnum setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return this;
    }

    public int getCategoryName() {
        return categoryName;
    }

    public ConstantsEnum setCategoryName(int categoryName) {
        this.categoryName = categoryName;
        return this;
    }
}
