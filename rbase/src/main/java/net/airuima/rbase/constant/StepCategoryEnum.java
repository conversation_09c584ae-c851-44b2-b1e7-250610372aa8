package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工序类型枚举类
 * <AUTHOR>
 * @date 2022/11/8
 */
public enum StepCategoryEnum {
    /**
     * 正常生产工序
     */
    NORMAL_STEP("NORMAL_STEP",0,"正常生产工序"),
    /**
     *在线调整工序
     */
    ONLINE_ADJUSTMENT_STEP("ONLINE_ADJUSTMENT_STEP",1,"在线调整工序"),
    /**
     * 在线预调整工序
     */
    ONLINE_PRELIMINARY_ADJUSTMENT_STEP("ONLINE_PRELIMINARY_ADJUSTMENT_STEP",2,"在线预调整工序"),
    /**
     * 烘烤放入工序
     */
    PUT_IN_BAKE_STEP("PUT_IN_BAKE_STEP",3,"烘烤放入工序"),
    /**
     * 烘烤取出工序
     */
    PULL_OUT_BAKE_STEP("PULL_OUT_BAKE_STEP",4,"烘烤取出工序"),
    /**
     * 温循放入工序
     */
    PUT_IN_CYCLE_BAKE_STEP("PUT_IN_CYCLE_BAKE_STEP",5,"温循放入工序"),
    /**
     * 温循取出工序
     */
    PULL_OUT_CYCLE_BAKE_STEP("PULL_OUT_CYCLE_BAKE_STEP",6,"温循取出工序"),
    /**
     * 老化放入工序
     */
    PUT_IN_AGEING_STEP("PUT_IN_AGEING_STEP",7,"老化放入工序"),
    /**
     * 老化取出工序
     */
    PULL_OUT_AGEING_STEP("PULL_OUT_AGEING_STEP",8,"老化取出工序"),

    /**
     * 外协工序
     */
    OEM_STEP("OEM_STEP",9,"外协工序");

    private String stepCategory;

    private int status;

    private String remark;

    StepCategoryEnum(String stepCategory, int status, String remark) {
        this.stepCategory = stepCategory;
        this.status = status;
        this.remark = remark;
    }

    public static Boolean isBakeCycleBakeAgeingStep(int status){
        for (StepCategoryEnum enums : StepCategoryEnum.values()){
            if (enums.getStatus() == status && (status > Constants.INT_TWO)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public String getStepCategory() {
        return stepCategory;
    }

    public StepCategoryEnum setStepCategory(String stepCategory) {
        this.stepCategory = stepCategory;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public StepCategoryEnum setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public StepCategoryEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
