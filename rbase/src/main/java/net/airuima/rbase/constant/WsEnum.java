package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工单类型枚举类
 * <AUTHOR>
 * @date 2022/11/7
 */
public enum WsEnum {
    /**
     * 正常单
     */
    NORMAL_WS("NORMAL_WS",1,"正常单"),
    /**
     * 在线返修单
     */
    ONLINE_RE_WS("ONLINE_RE_WS",-1,"在线返修单"),
    /**
     * 离线返修单
     */
    OFFLINE_RE_WS("OFFLINE_RE_WS",0,"离线返修单"),
    /**
     * FQC批退单
     */
    FQC_RE_WS("FQC_RE_WS",-2,"FQC批退单");

    private String WsCategory;

    private int category;

    private String remark;

    WsEnum(String wsCategory, int category, String remark) {
        WsCategory = wsCategory;
        this.category = category;
        this.remark = remark;
    }

    public String getWsCategory() {
        return WsCategory;
    }

    public WsEnum setWsCategory(String wsCategory) {
        WsCategory = wsCategory;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WsEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WsEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
