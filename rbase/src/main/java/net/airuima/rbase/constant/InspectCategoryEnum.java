package net.airuima.rbase.constant;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "待检任务枚举类")
public enum InspectCategoryEnum {

    /**
     * 检测类型(首检0/巡检1/末检2/终检3/抽检4)
     */

    /**
     * 首检
     */
    FIRST_INSPECTION("key_fai_serial_number",0,"首检"),

    /**
     * 巡检
     */
    IPQC_INSPECTION("key_ipqc_serial_number",1,"巡检"),

    /**
     * 末检
     */
    LQC_INSPECTION("key_lqc_serial_number",2,"末检"),

    /**
     * 终检
     */
    LAST_INSPECTION("key_fqc_serial_number",3,"终检"),

    /**
     * 抽检
     */
    SIMPLE_INSPECTION("key_pqc_serial_number",4,"抽检"),

    /**
     * 来料检
     */
    IQC_INSPECTION("key_iqc_serial_number",5,"来料检"),


    /**
     * 外协入库检
     */
    OEM_INSPECTION("OEM_INSPECTION",6,"外协入库检");


    private String key;

    private int category;

    private String remark;

    InspectCategoryEnum(String key,int category, String remark) {
        this.category = category;
        this.remark = remark;
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public InspectCategoryEnum setKey(String key) {
        this.key = key;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public InspectCategoryEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public InspectCategoryEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public static String getKeyByCategory(int category) {
        for (InspectCategoryEnum ice : values()) {
            if (ice.getCategory() == category) {
                return ice.getKey();
            }
        }
        throw new IllegalArgumentException("Invalid mode: " + category);
    }
}
