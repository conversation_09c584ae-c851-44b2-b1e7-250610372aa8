package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报表类型枚举
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
public enum WorkSheetReportTypeEnum {

    /**
     * 0 工单 1子工单
     */
    WORK_SHEET("工单", 0),
    SUB_WORK_SHEET("子工单", 1);


    WorkSheetReportTypeEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final int category;

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }
}
