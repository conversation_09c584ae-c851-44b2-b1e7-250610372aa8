package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 工位类型枚举类
 * <AUTHOR>
 * @date 2023/1/31
 */
public enum WorkCellEnum {
    /**
     * 自动工位
     */
    AUTO("AUTO",0,"自动工位"),
    /**
     * 正常工位
     */
    NORMAL_WC("NORMAL_WC",1,"正常工位"),
    /**
     * PQC工位
     */
    PQC_WC("",2,"PQC工位") ,
    /**
     * 首检工位
     */
    FIRST_INSPECTION_WC("",3,"首检工位");

    private String workCellCategory;

    private int category;

    private String remark;

    WorkCellEnum(String workCellCategory, int category, String remark) {
        this.workCellCategory = workCellCategory;
        this.category = category;
        this.remark = remark;
    }

    public String getWorkCellCategory() {
        return workCellCategory;
    }

    public WorkCellEnum setWorkCellCategory(String workCellCategory) {
        this.workCellCategory = workCellCategory;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCellEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WorkCellEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
