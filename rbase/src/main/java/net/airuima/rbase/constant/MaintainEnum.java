package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 维修分析枚举类
 * <AUTHOR>
 * @date 2023/3/14
 */
public enum MaintainEnum {
    /**
     * 维修分析状态：待分析状态
     */
    WAIT_ANALYZE_STATUS("WAIT_ANALYZE_STATUS",0,"待分析状态"),

    /**
     * 维修分析状态：待维修状态
     */
    WAIT_MAINTAIN_STATUS("WAIT_MAINTAIN_STATUS",1,"待维修状态"),

    /**
     * 维修分析状态：完成状态
     */
    MAINTAIN_FINISHED_STATUS("MAINTAIN_FINISHED_STATUS",2,"完成状态"),


    /**
     * 维修分析结果：未处理
     */
    MAINTAIN_RESULT_WAIT_DEAL("MAINTAIN_RESULT_WAIT_DEAL",-1,"未处理"),

    /**
     * 维修分析结果：放行
     */
    MAINTAIN_RESULT_RELEASE("MAINTAIN_RESULT_RELEASE",2,"放行"),

    /**
     * 维修分析结果：返工
     */
    MAINTAIN_RESULT_REWORK("MAINTAIN_RESULT_REWORK",1,"返工"),

    /**
     * 维修分析结果：报废
     */
    MAINTAIN_RESULT_SCRAP("MAINTAIN_RESULT_SCRAP",0,"报废"),

    /**
     * 维修分析类型：SN
     */
    MAINTAIN_TYPE_SN("MAINTAIN_TYPE_SN",0,"SN"),
    /**
     * 维修分析类型：容器
     */
    MAINTAIN_TYPE_CONTAINER("MAINTAIN_TYPE_CONTAINER",1,"容器"),
    /**
     * 维修分析类型：工单
     */
    MAINTAIN_TYPE_WORKSHEET("MAINTAIN_TYPE_WORKSHEET",2,"工单"),
    /**
     * 返修流程：返修工艺路线
     */
    MAINTAIN_REWORK_CATEGORY_REWORK("MAINTAIN_REWORK_CATEGORY_REWORK",0,"返修工艺路线"),
    /**
     * 返修流程：原工艺路线
     */
    MAINTAIN_REWORK_CATEGORY_ORIGIN("MAINTAIN_REWORK_CATEGORY_ORIGIN",1,"原工艺路线");

    private String category;

    private int status;

    private String remark;

    MaintainEnum(String category, int status, String remark) {
        this.category = category;
        this.status = status;
        this.remark = remark;
    }

    public String getCategory() {
        return category;
    }

    public MaintainEnum setCategory(String category) {
        this.category = category;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public MaintainEnum setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public MaintainEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
