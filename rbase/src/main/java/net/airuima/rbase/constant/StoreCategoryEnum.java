package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 入库类型枚举
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
public enum StoreCategoryEnum {
    /**
     * 入库类型(0:采购入库;1:其他入库;2:委托到货入库;3:生产退料入库;4:非生产退料入库;5:销售退货入库;6:成品入库;7:调拨入库)
     */
    WAREHOUSING_TYPE_PURCHASE(0, "采购入库"),
    WAREHOUSING_TYPE_OTHER(1, "其他入库"),
    WAREHOUSING_TYPE_ENTRUST(2, "委托到货入库"),
    WAREHOUSING_TYPE_PRODUCTION_MATERIAL_RETURN(3, "生产退料入库"),
    WAREHOUSING_TYPE_NOT_PRODUCTION_MATERIAL_RETURN(4, "非生产退料入库"),
    WAREHOUSING_TYPE_SALE_MATERIAL_RETURN(5, "销售退货入库"),
    WAREHOUSING_TYPE_FINISHED_PRODUCT(6, "成品入库"),
    WAREHOUSING_TYPE_TRANSFER(7, "调拨入库"),
    ;
    private final int category;
    private final String name;

    StoreCategoryEnum(int category, String name) {
        this.category = category;
        this.name = name;
    }

    public int getCategory() {
        return category;
    }

    public String getName() {
        return name;
    }

}
