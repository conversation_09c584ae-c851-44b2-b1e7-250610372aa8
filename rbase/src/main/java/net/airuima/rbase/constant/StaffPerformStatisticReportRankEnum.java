package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计排行榜类型枚举
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
public enum StaffPerformStatisticReportRankEnum {

    /**
     * 0 员工 1 工位 2工序
     */
    STAFF("员工", 0),
    WORK_CELL("工位", 1),
    STEP("工序", 2);

    StaffPerformStatisticReportRankEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final int category;

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }
}
