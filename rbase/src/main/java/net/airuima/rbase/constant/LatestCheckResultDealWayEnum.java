package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 最新检测结果处理方式枚举
 * 用于LatestCheckResult实体中dealWay字段的枚举定义
 *
 * <AUTHOR>
 */
public enum LatestCheckResultDealWayEnum {

    /**
     * 待处理
     */
    PENDING(0, "待处理"),

    /**
     * 通过
     */
    PASS(1, "通过"),

    /**
     * 重检
     */
    RECHECK(2, "重检"),

    /**
     * 放行
     */
    RELEASE(3, "放行");

    private final int code;
    private final String description;

    LatestCheckResultDealWayEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例
     *
     * @param code 处理方式代码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static LatestCheckResultDealWayEnum getByCode(int code) {
        for (LatestCheckResultDealWayEnum dealWay : LatestCheckResultDealWayEnum.values()) {
            if (dealWay.getCode() == code) {
                return dealWay;
            }
        }
        return null;
    }
}