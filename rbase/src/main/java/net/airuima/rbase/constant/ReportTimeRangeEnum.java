package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报表时间范围类型枚举
 *
 * <AUTHOR>
 * @date 2023/06/19
 */
public enum ReportTimeRangeEnum {

    /**
     * 时间范围 0 今天 1本周 2本月
     */
    TODAY("今天", 0),
    WEEK("本周", 1),
    MONTH("本月", 2);


    ReportTimeRangeEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    /**
     * 时间范围名称
     */
    private final String name;

    /**
     * 时间范围类型 0今天 1本周 2 本月
     */
    private final int category;

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }


}

