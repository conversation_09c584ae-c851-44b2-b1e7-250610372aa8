package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系返修工艺路线导入表头
 *
 * <AUTHOR>
 * @date 2023/10/27
 */
public class PedigreeReworkWorkFlowExcelConstants {

    /**
     * 产品谱系编码
     */
    public static final String PEDIGREE_CODE = "产品谱系编码";

    /**
     * 工艺路线编码
     */
    public static final String WORK_FLOW_CODE = "工艺路线编码";


    /**
     * 客户编码
     */
    public static final String CLIENT_CODE = "客户编码";


    /**
     * 不良种类编码
     */
    public static final String UNQUALIFIED_GROUP_CODE = "不良种类编码";



    /**
     * 是否启用(0:否;1:是)
     */
    public static final String IS_ENABLE = "是否启用";
}
