package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序间隔单位枚举
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
public enum PedigreeStepIntervalTimeUnitEnum {

    /**
     * 单位(0:秒，1:分钟,2:小时,3:天)
     */
    SECONDS(0, "秒"),
    MINUTES(1, "分"),
    HOURS(2, "时"),
    DAYS(3, "天");

    private final int code;
    private final String description;

    PedigreeStepIntervalTimeUnitEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据描述获取时间单位枚举
     * @param description 描述
     * @return net.airuima.rbase.constant.PedigreeStepIntervalTimeUnitEnum 时间单位枚举
     */
    public static PedigreeStepIntervalTimeUnitEnum getByDescription(String description) {
        for (PedigreeStepIntervalTimeUnitEnum unit : values()) {
            if (unit.getDescription().equals(description)) {
                return unit;
            }
        }
        return null;
    }
}
