package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/10/20
 */
public enum StepIntervalEnum {
    SECOND(0,"秒"),
    MINUTE(1,"分"),
    HOUR(2,"时"),
    DAY(3,"天");

    private int unit;

    private String remark;

    StepIntervalEnum(int unit, String remark) {
        this.unit = unit;
        this.remark = remark;
    }

    public int getUnit() {
        return unit;
    }

    public StepIntervalEnum setUnit(int unit) {
        this.unit = unit;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public StepIntervalEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
