package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序产出推移图类型枚举
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
public enum StepProductCapacityEnum {

    /**
     * 类型 计划产出 实际产出
     */
    PLAN("计划产出","plan"),
    ACTUAL("实际产出","actual");

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型国际化key
     */
    private final String key;

    StepProductCapacityEnum(String name, String key) {
        this.name = name;
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }
}
