package net.airuima.rbase.constant.enums;

import net.airuima.rbase.constant.Constants;

/**
 * 维修历史来源枚举
 * 来源（工序不良0,抽检不良1，终检不良2，末检不良3）
 * 
 * <AUTHOR>
 * @since 1.8.1
 */
public enum MaintainHistorySourceEnum {
    
    /**
     * 工序不良
     */
    PROCESS_DEFECT("工序不良", 0),
    
    /**
     * 抽检不良
     */
    SAMPLING_DEFECT("抽检不良", 1),
    
    /**
     * 终检不良
     */
    FINAL_INSPECTION_DEFECT("终检不良", 2),
    
    /**
     * 末检不良
     */
    LAST_INSPECTION_DEFECT("末检不良", 3);
    
    /**
     * 描述
     */
    private final String description;
    
    /**
     * 来源代码
     */
    private final int code;
    
    MaintainHistorySourceEnum(String description, int code) {
        this.description = description;
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getCode() {
        return code;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 来源代码
     * @return 对应的枚举值
     */
    public static MaintainHistorySourceEnum fromCode(int code) {
        for (MaintainHistorySourceEnum source : values()) {
            if (source.getCode() == code) {
                return source;
            }
        }
        throw new IllegalArgumentException("Invalid MaintainHistorySource code: " + code);
    }

    public static int fromCheckHistoryCategory(Integer category){
        if (category == Constants.INSPECT_FQC_CATEGORY){
            return FINAL_INSPECTION_DEFECT.getCode();
        }else if (category == Constants.INSPECT_PQC_CATEGORY){
            return SAMPLING_DEFECT.getCode();
        }else if (category == Constants.INSPECT_LQC_CATEGORY){
            return LAST_INSPECTION_DEFECT.getCode();
        }
        throw new IllegalArgumentException("Invalid MaintainHistorySource category: " + category);
    }
    
    /**
     * 根据描述获取枚举
     * 
     * @param description 描述
     * @return 对应的枚举值
     */
    public static MaintainHistorySourceEnum fromDescription(String description) {
        for (MaintainHistorySourceEnum source : values()) {
            if (source.getDescription().equals(description)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Invalid MaintainHistorySource description: " + description);
    }
    
    /**
     * 判断是否为有效的来源代码
     * 
     * @param code 来源代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 判断是否为检验类不良（抽检、终检、末检）
     * 
     * @return 是否为检验类不良
     */
    public boolean isInspectionDefect() {
        return this == SAMPLING_DEFECT || 
               this == FINAL_INSPECTION_DEFECT || 
               this == LAST_INSPECTION_DEFECT;
    }
    
    /**
     * 判断是否为工序不良
     * 
     * @return 是否为工序不良
     */
    public boolean isProcessDefect() {
        return this == PROCESS_DEFECT;
    }
}
