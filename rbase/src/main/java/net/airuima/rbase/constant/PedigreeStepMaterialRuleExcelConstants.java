package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 上料规则导入常量
 *
 * <AUTHOR>
 * @date 2023/11/04
 */
public class PedigreeStepMaterialRuleExcelConstants {

    /**
     * 客户编码
     */
    public static final String CLIENT_CODE = "客户编码";

    /**
     * 产品谱系编码
     */
    public static final String PEDIGREE_CODE = "产品谱系编码";


    /**
     * 工艺路线编码
     */
    public static final String WORK_FLOW_CODE = "工艺路线编码";

    /**
     * 物料编码
     */
    public static final String MATERIAL_CODE = "物料编码";


    /**
     * 工序编码
     */
    public static final String STEP_CODE = "工序编码";

    /**
     * 扣料比例
     */
    public static final String PROPORTION = "扣料比例";

    /**
     * 是否扣数(0:否;1:是)
     */
    public static final String IS_DEDUCT = "是否扣数";

    /**
     * 是否核物料(0:否;1:是)
     */
    public static final String IS_CHECK_MATERIAL = "是否核物料";

    /**
     * 是否核物料批次(0:否;1:是)
     */
    public static final String IS_CHECK_MATERIAL_BATCH = "是否核物料批次";

    /**
     * 物料管控粒度
     */
    public static final String CONTROL_MATERIAL_GRANULARITY = "物料管控粒度";

    /**
     * 指定数量
     */
    public static final String CONTROL_SN_COUNT = "指定数量";

    /**
     * 规则
     */
    public static final String SERIAL_NUMBER_RULE = "规则";

    /**
     * 是否启用(0:否;1:是)
     */
    public static final String IS_ENABLE = "是否启用";



}
