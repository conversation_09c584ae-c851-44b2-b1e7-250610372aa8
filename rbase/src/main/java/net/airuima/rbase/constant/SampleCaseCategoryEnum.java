package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 抽样方案类型枚举
 *
 * <AUTHOR>
 * @date 2023-04-26
 */
public enum SampleCaseCategoryEnum {
    /**
     * 抽样类型:全检0/固定数量1/百分比抽样2/国标抽样3
     */
    ALL_CHECK(0, "全检"),
    SPECIFY_QUANTITY(1, "固定数量"),
    PERCENTAGE(2, "百分比抽样"),
    GBT(3, "国标抽样"),
    ;
    private final Integer category;
    private final String name;

    SampleCaseCategoryEnum(Integer category, String name) {
        this.category = category;
        this.name = name;
    }

    public Integer getCategory() {
        return category;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据名称获取对应key
     * @param name      名称
     * @return java.lang.Integer对应key值
     */
    public static Integer getCategoryKey(String name){
        for (SampleCaseCategoryEnum sampleCaseCategoryEnum : SampleCaseCategoryEnum.values()){
            if (sampleCaseCategoryEnum.getName().equals( name )){
                return sampleCaseCategoryEnum.category;
            }
        }
        return null;
    }

    /**
     * 根据key获取名称
     * @param category  key值
     * @return java.lang.String对应的名称
     */
    public static String getCategoryName(Integer category){
        for (SampleCaseCategoryEnum sampleCaseCategoryEnum : SampleCaseCategoryEnum.values()){
            if (sampleCaseCategoryEnum.getCategory().equals( category )){
                return sampleCaseCategoryEnum.name;
            }
        }
        return null;
    }
}
