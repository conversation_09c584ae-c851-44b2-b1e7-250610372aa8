package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public enum StepReinspectResultEnum {

    /**
     * 复检结果：放行
     */
    RELEASE("RELEASE",0,"放行"),

    /**
     * 复检结果：返工
     */
    REWORK("REWORK",1,"返工"),

    /**
     * 复检结果：报废
     */
    SCRAP("SCRAP",2,"报废");

    private String category;

    private int status;

    private String remark;

    StepReinspectResultEnum(String category, int status, String remark) {
        this.category = category;
        this.status = status;
        this.remark = remark;
    }

    public String getCategory() {
        return category;
    }

    public StepReinspectResultEnum setCategory(String category) {
        this.category = category;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public StepReinspectResultEnum setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public StepReinspectResultEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
