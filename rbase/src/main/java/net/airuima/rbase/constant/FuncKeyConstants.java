package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
public class FuncKeyConstants {
    /**
     * 工站功能key
     */
    public static final String WORK_STATION_FUNC_KEY = "WorkStation";

    /**
     * 子工单功能key
     */
    public static final String SUB_WORK_SHEET_FUNC_KEY = "SubWorkSheet";

    /**
     * 容器功能key
     */
    public static final String CONTAINER_FUNC_KEY = "Container";

    public static final String RMPS_FUNC_KEY = "RmpsService";

    /**
     * 单只生产控制功能key
     */
    public static final String SINGLE_FUNC_KEY = "Single";


    /**
     * 易损件控制功能key
     */
    public static final String WEARING_PART_FUNC_KEY = "WearingPart";

    /**
     * 首检功能key
     */
    public static final String FAI = "FAI";

    /**
     * 巡检功能key
     */
    public static final String IPQC = "IPQC";

    /**
     * 抽检功能key
     */
    public static final String PQC = "PQC";

    /**
     * 终检功能key
     */
    public static final String FQC = "FQC";

    /**
     * 末检功能key
     */
    public static final String LQC = "LQC";

    /**
     * web化的Rworker功能key
     */
    public static final String RWORKER_WEB = "RworkerWeb";

    /**
     * 供应商功能key
     */
    public static final String SUPPLIER = "OrgSupplier";

    /**
     * 工序指标
     */
    public static final String ESOP = "ESop";


    /**
     * 工序上料规则key
     */
    public static final String PEDIGREE_STEP_MATERIAL_RULE_KEY = "WorksheetMaterial && WsMaterialBatch";

    /**
     * 工序间隔配置
     */
    public static final String STEP_INTERVAL = "StepInterval";

    /**
     * 单支复用配置
     */
    public static final String SN_REUSE =  "SnReuse";


    /**
     * 动态数据
     */
    public static final String STEP_DYNAMIC_DATA = "StepDynamicData";

    /**
     * 任务消息
     */
    public static final String MESSAGE = "Message";

    /**
     * 在制产品质量看板
     */
    public static final String PEDIGREE_OLINE_QUALITY_REPORT= "PedigreeOnlineQualityReport";

    /**
     * 设备
     */
    public static final String FBASE = "FBase";
}
