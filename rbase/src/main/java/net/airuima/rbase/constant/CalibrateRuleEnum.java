package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *  校准规则-校准程序枚举
 * <AUTHOR>
 * @create 2023/4/19
 */
public enum CalibrateRuleEnum {

    /**
     * 内校
     */
    INTERNAL_CALIBRATE("内校",0),

    /**
     * 外校
     */
    OUTSOURCE_CALIBRATE("外校",1);

    private String type;

    private int status;

    CalibrateRuleEnum(String type, int status) {
        this.type = type;
        this.status = status;
    }

    /**
     * 根据 校准程序类型 字符串 获取对应的 枚举实体
     */
    public static CalibrateRuleEnum getType(String type){
        if (type == null){
            return null;
        }
        for (CalibrateRuleEnum enums : CalibrateRuleEnum.values()){
            if (enums.getType().equals(type)){
                return enums;
            }
        }
        return null;
    }

    /**
     * 根据 校准程序类型 数值类型 获取对应的 枚举实体
     */
    public static CalibrateRuleEnum getStatus(int status){
        for (CalibrateRuleEnum enums : CalibrateRuleEnum.values()){
            if (enums.getStatus() == status){
                return enums;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public CalibrateRuleEnum setType(String type) {
        this.type = type;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public CalibrateRuleEnum setStatus(int status) {
        this.status = status;
        return this;
    }

}
