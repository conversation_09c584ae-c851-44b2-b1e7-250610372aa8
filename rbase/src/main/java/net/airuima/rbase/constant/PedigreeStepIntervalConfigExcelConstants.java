package net.airuima.rbase.constant;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序间隔配置导入表头常量
 *
 * <AUTHOR>
 * @date 2023/08/05
 */
public class PedigreeStepIntervalConfigExcelConstants {


    /**
     * 产品谱系编码
     */
    public static final String PEDIGREE_CODE = "产品谱系编码";
    ;

    /**
     * 工艺路线编码
     */
    public static final String WORK_FLOW_CODE = "工艺路线编码";


    /**
     * 工序编码
     */
    public static final String STEP_CODE = "工序编码";

    /**
     * 前置工序编码
     */
    public static final String PRE_STEP_CODE = "前置工序编码";

    /**
     * 间隔时长
     */
    public static final String DURATION = "间隔时长";


    /**
     * 单位
     */
    public static final String DURATION_UNIT = "单位";


}
