<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="ruima (generated)" id="202402291116">
        <createTable tableName="procedure_work_sheet_sn" remarks="工单SN关联表">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sn" remarks="SN" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="sn,deleted"
                             constraintName="procedure_work_sheet_sn_unique"
                             tableName="procedure_work_sheet_sn"/>
        <createIndex tableName="procedure_work_sheet_sn" indexName="procedure_work_sheet_sn_work_sheet_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_sheet_sn" indexName="procedure_work_sheet_sn_sub_work_sheet_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_sheet_sn" indexName="procedure_work_sheet_sn_sn_index">
            <column name="sn"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>
