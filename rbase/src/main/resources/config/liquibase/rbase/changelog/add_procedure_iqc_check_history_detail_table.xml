<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202406201308-003" author="simon">
        <createTable tableName="procedure_iqc_check_history_detail" remarks="来料检验历史详情">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="check_history_id" type="bigint(20)" remarks="来料检验id">
                <constraints nullable="false"/>
            </column>
            <column name="check_item_id" type="bigint(20)" remarks="检验项目id"/>
            <column name="sn" type="varchar(255)" remarks="sn"/>
            <column name="check_data" type="varchar(50)" remarks="检测结果值(数字或OK、NG)"/>
            <column name="qualified_range" type="varchar(255)" remarks="判定标准(开闭区间或者OK)"/>
            <column name="result" type="bit(1)" remarks="结果(0:不合格;1:合格)"/>
            <column name="defect_id" type="bigint(20)" remarks="缺陷原因id"/>
            <column name="dtype" type="varchar(255)" remarks=""/>
            <column name="deleted" type="bigint(20)" defaultValueNumeric="0" remarks="逻辑删除标识"/>
            <column name="created_by" type="varchar(50)" remarks="新建人"/>
            <column name="created_date" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP" remarks="新建时间"/>
            <column name="last_modified_by" type="varchar(50)" remarks="最新修改人"/>
            <column name="last_modified_date" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP" remarks="最新修改时间"/>
            <column name="custom1" type="varchar(255)" remarks="定制字段1"/>
            <column name="custom2" type="varchar(255)" remarks="定制字段2"/>
            <column name="custom3" type="varchar(255)" remarks="定制字段3"/>
            <column name="custom4" type="varchar(255)" remarks="定制字段4"/>
            <column name="custom5" type="varchar(255)" remarks="定制字段5"/>
        </createTable>
    </changeSet>
    <changeSet id="202407021404" author="zorro">
        <createIndex tableName="procedure_iqc_check_history_detail" indexName="procedure_iqc_check_history_detail_history_id_index">
            <column name="check_history_id"/>
        </createIndex>
        <createIndex tableName="procedure_iqc_check_history_detail" indexName="procedure_iqc_check_history_detail_check_item_id_index">
            <column name="check_item_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
