<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <property name="autoIncrement" value="true"/>


    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="202404151836" author="ruima (generated)">
        <addColumn tableName="procedure_sn_work_detail">
            <column name="ysn" remarks="YSN" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="procedure_sn_work_status">
            <column name="ysn" remarks="YSN" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addUniqueConstraint columnNames="ysn, sub_work_sheet_id, step_id,rework_time, deleted" constraintName="procedure_sn_work_detail_ysn_sub_work_sheet_id_step_id_unique" tableName="procedure_sn_work_detail"/>
        <addUniqueConstraint columnNames="ysn, work_sheet_id, step_id, rework_time,deleted" constraintName="procedure_sn_work_detail_ysn_work_sheet_id_step_id_unique" tableName="procedure_sn_work_detail"/>
        <addUniqueConstraint columnNames="ysn, sub_work_sheet_id, deleted" constraintName="procedure_sn_work_status_ysn_sub_ws_unique" tableName="procedure_sn_work_status"/>
        <addUniqueConstraint columnNames="ysn, work_sheet_id, deleted" constraintName="procedure_sn_work_status_ysn_ws_unique" tableName="procedure_sn_work_status"/>
        <createIndex indexName="procedure_sn_work_detail_ysn_index" tableName="procedure_sn_work_detail">
            <column name="ysn"/>
        </createIndex>
        <createIndex indexName="procedure_sn_work_status_ysn_index" tableName="procedure_sn_work_status">
            <column name="ysn"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
