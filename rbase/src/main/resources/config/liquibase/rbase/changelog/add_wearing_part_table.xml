<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="zhuhuawu (generated)" id="1737459664375-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_pedigree_step_wearing_part_group"/>
            </not>
        </preConditions>
        <createTable remarks="产品谱系工序易损件规则表" tableName="base_pedigree_step_wearing_part_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="work_flow_id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="group_id" remarks="易损件种类id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="number" remarks="数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column name="platform_number" remarks="台位号/设备号" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable remarks="易损件表" tableName="base_wearing_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="group_id" remarks="易损件类型id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="易损件名称" type="VARCHAR(255)"/>
            <column name="code" remarks="易损件编码" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="category" remarks="易损件使用类型（0:次数，1:时长）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="5" name="max_reset_number" remarks="最大重置次数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="accumulate_reset_number" remarks="累计重置次数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="max_use_number" remarks="最大使用次数" type="INT"/>
            <column defaultValueNumeric="0" name="accumulate_use_number" remarks="累计使用次数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="max_use_time" remarks="最大使用时长(秒为单位)" type="INT"/>
            <column defaultValueNumeric="0" name="accumulate_use_time" remarks="累计使用时长(秒为单位)" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="status" remarks="易损件使用状态（0可用，1在用，2超期，3报废）"
                    type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="reset_way" remarks="易损件重置方式（0:手动，1:自动）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="expire_date" remarks="失效期" type="timestamp"/>
            <column name="serial_number" remarks="流水号" type="VARCHAR(100)"/>
        </createTable>

        <createTable remarks="易损件种类替换表" tableName="base_wearing_part_exchange">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="origin_group_id" remarks="原始易损件类型id" type="BIGINT"/>
            <column name="exchange_group_id" remarks="替换易损件类型id" type="BIGINT"/>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>

        <createTable remarks="易损件种类表" tableName="base_wearing_part_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="类型名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="类型编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="category"
                    remarks="管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）"
                    type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="max_reset_number" remarks="最大重置次数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="max_use_number" type="INT"/>
            <column name="max_use_time" type="INT"/>
            <column name="effective_day" type="INT"/>
            <column defaultValueNumeric="0" name="reset_way" remarks="重置方式(0手动，1自动）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="auto_work_record" remarks="是否自动记录易损件(false，否 true，是)"
                    type="BIT(1)"/>
            <column defaultValueNumeric="0" name="granularity" remarks="易损件粒度(0:单支序列号，1:批次号)"
                    type="TINYINT(3)"/>
        </createTable>

        <createTable remarks="批量生产详情易损件表" tableName="procedure_batch_work_detail_wearing_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="batch_work_detail_id" remarks="易损件批量生产id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="wearing_part_id" remarks="易损件id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="times" remarks="次数" type="INT"/>
            <column defaultValueNumeric="0" name="duration" remarks="时长" type="INT"/>
        </createTable>

        <createTable remarks="容器生产详情易损件表" tableName="procedure_container_detail_wearing_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="container_detail_id" remarks="容器id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="wearing_part_id" remarks="易损件id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="times" remarks="次数" type="INT"/>
            <column defaultValueNumeric="0" name="duration" remarks="时长" type="INT"/>
        </createTable>

        <createTable remarks="最新工序使用易损件信息表" tableName="procedure_latest_step_wearing_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_wearing_part_rule_id" remarks="工序易损件规则ID" type="BIGINT"/>
            <column name="wearing_part_id" remarks="易损件ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>

        <createTable remarks="单支生产详情易损件表" tableName="procedure_sn_work_detail_wearing_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn_work_detail_id" remarks="SN工作详情id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="wearing_part_id" remarks="易损件id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="times" remarks="次数" type="INT"/>
            <column defaultValueNumeric="0" name="duration" remarks="时长" type="INT"/>
        </createTable>

        <createTable remarks="易损件重置历史表" tableName="procedure_wearing_part_reset_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="wearing_part_id" remarks="易损件id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="staff_id" type="BIGINT"/>
            <column name="note" remarks="备注" type="VARCHAR(250)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="reset_number" remarks="重置次数" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, group_id, work_cell_id, deleted"
                             constraintName="base_pedigree_step_wearing_part_group_unique"
                             tableName="base_pedigree_step_wearing_part_group"/>
        <addUniqueConstraint
                columnNames="pedigree_id, work_flow_id, step_id, group_id, work_cell_id, platform_number, deleted"
                constraintName="base_pedigree_step_wearing_part_group_wc_pn_unique"
                tableName="base_pedigree_step_wearing_part_group"/>
        <addUniqueConstraint columnNames="origin_group_id, exchange_group_id, deleted"
                             constraintName="base_wearing_part_exchange_group_id_exchange_group_id_unique"
                             tableName="base_wearing_part_exchange"/>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_wearing_part_group_unique"
                             tableName="base_wearing_part_group"/>
        <addUniqueConstraint columnNames="code, serial_number, deleted" constraintName="base_wearing_part_unique"
                             tableName="base_wearing_part"/>
        <addUniqueConstraint columnNames="batch_work_detail_id, wearing_part_id, deleted"
                             constraintName="procedure_batch_work_detail_wearing_part_unique"
                             tableName="procedure_batch_work_detail_wearing_part"/>
        <addUniqueConstraint columnNames="container_detail_id, wearing_part_id, deleted"
                             constraintName="procedure_container_detail_wearing_part_unique"
                             tableName="procedure_container_detail_wearing_part"/>
        <addUniqueConstraint columnNames="sn_work_detail_id, wearing_part_id, deleted"
                             constraintName="procedure_sn_work_detail_wearing_part_unique"
                             tableName="procedure_sn_work_detail_wearing_part"/>
        <createIndex indexName="base_pedigree_step_wearing_part_group_group_id_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="group_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_wearing_part_group_multi_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column name="work_cell_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_wearing_part_group_pedigree_id_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="pedigree_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_wearing_part_group_step_id_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="step_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_wearing_part_group_wc_id_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="work_cell_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_wearing_part_group_work_flow_id_index"
                     tableName="base_pedigree_step_wearing_part_group">
            <column name="work_flow_id"/>
        </createIndex>
        <createIndex indexName="base_wearing_part_exchange_exchange_group_id_index"
                     tableName="base_wearing_part_exchange">
            <column name="exchange_group_id"/>
        </createIndex>
        <createIndex indexName="base_wearing_part_group_id_index" tableName="base_wearing_part">
            <column name="group_id"/>
        </createIndex>
        <createIndex indexName="procedure_batch_wdwp_batch_work_detail_id_index"
                     tableName="procedure_batch_work_detail_wearing_part">
            <column name="batch_work_detail_id"/>
        </createIndex>
        <createIndex indexName="procedure_batch_wdwp_wearing_part_id_index"
                     tableName="procedure_batch_work_detail_wearing_part">
            <column name="wearing_part_id"/>
        </createIndex>
        <createIndex indexName="procedure_cdwp_container_detail_id_index"
                     tableName="procedure_container_detail_wearing_part">
            <column name="container_detail_id"/>
        </createIndex>
        <createIndex indexName="procedure_cdwp_wearing_part_id_index"
                     tableName="procedure_container_detail_wearing_part">
            <column name="wearing_part_id"/>
        </createIndex>
        <createIndex indexName="procedure_latest_step_wearing_part_id_index"
                     tableName="procedure_latest_step_wearing_part">
            <column name="step_wearing_part_rule_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
        <createIndex indexName="procedure_sn_wdwp_sn_work_detail_id_index"
                     tableName="procedure_sn_work_detail_wearing_part">
            <column name="sn_work_detail_id"/>
        </createIndex>
        <createIndex indexName="procedure_sn_wdwp_wearing_part_id_index"
                     tableName="procedure_sn_work_detail_wearing_part">
            <column name="wearing_part_id"/>
        </createIndex>
        <createIndex indexName="procedure_wearing_part_reset_history_staff_id_index"
                     tableName="procedure_wearing_part_reset_history">
            <column name="staff_id"/>
        </createIndex>
        <createIndex indexName="procedure_wearing_part_reset_history_wearing_part_id_index"
                     tableName="procedure_wearing_part_reset_history">
            <column name="wearing_part_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
