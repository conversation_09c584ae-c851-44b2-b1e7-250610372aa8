<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202406201607-001" author="simon">
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="attribute_id" type="bigint(20)" remarks="物料属性id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="material_id" type="bigint(20)" remarks="物料id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="supplier_id" type="bigint(20)" remarks="供应商id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="client_id" type="bigint(20)" remarks="客户id"/>
        </addColumn>
    </changeSet>

    <changeSet id="202505151916-001" author="YangS">
        <addColumn tableName="base_work_cell_check_start_rule">
            <column defaultValueNumeric="0" name="extend_type" type="tinyint(2)" remarks="宽放类型(0:按计划宽放,1:按实际执行宽放)"/>
        </addColumn>

        <addColumn tableName="procedure_latest_check_result">
            <column defaultValueNumeric="0" name="extend_time" type="double" remarks="宽放类型(0:按计划宽放,1:按实际执行宽放)"/>
        </addColumn>
    </changeSet>

    <changeSet id="202506141916-001" author="YangS">
        <addColumn tableName="base_work_cell_check_start_rule">
            <column defaultValueNumeric="0" name="extend_rule" type="tinyint(2)" remarks="0：允许后续工序生产,1：限制当前工序生产，2：允许指定类型后续工序生产"/>
            <column name="extend_step_category" type="VARCHAR(255)" remarks="指定宽放工序类型：0,1,2,3"/>
        </addColumn>
    </changeSet>


    <changeSet id="202506141912-001" author="YangS">
        <createTable remarks="工位宽放历史" tableName="procedure_work_cell_extend_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT"/>
            <column defaultValue="0" name="category" remarks="检测类型(首检0/巡检1)" type="tinyint(2)"/>
            <column name="variety_id" remarks="项目类型ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="extend_time" type="double" remarks="宽放类型(0:按计划宽放,1:按实际执行宽放)"/>
            <column defaultValueNumeric="0" name="extend_rule" type="tinyint(2)" remarks="0：允许后续工序生产,1：限制当前工序生产，2：允许指定类型后续工序生产"/>
            <column name="extend_step_category" type="VARCHAR(255)" remarks="指定宽放工序类型：0,1,2,3"/>
            <column defaultValueBoolean="false" name="result" remarks="结果(0:未检测或者检测未通过;1:已检测且通过)" type="BIT(1)"/>
            <column name="start_time" remarks="开始时间" type="datetime"/>
            <column name="end_time" remarks="结束时间" type="datetime"/>

            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>

        <createIndex tableName="procedure_work_cell_extend_history" indexName="wced_work_sheet_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_history" indexName="wced_sub_work_sheet_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_history" indexName="wced_work_cell_id_index">
            <column name="work_cell_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_history" indexName="wced_category_index">
            <column name="category"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_history" indexName="wced_result_index">
            <column name="result"/>
        </createIndex>

        <createTable remarks="工位宽放工位过站记录详情" tableName="procedure_work_cell_extend_step_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_id" remarks="员工ID" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="BIGINT"/>
            <column name="extend_work_cell_id" remarks="宽放触发工位" type="BIGINT"/>
            <column name="work_cell_id" remarks="下交工位" type="BIGINT"/>
            <column name="step_id" remarks="工序" type="BIGINT"/>
            <column name="batch_work_detail_id" remarks="工序详情id" type="BIGINT"/>
            <column name="container_detail_id" remarks="容器详情id" type="BIGINT"/>
            <column name="sn_work_detail_id" remarks="sn详情id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="input_number" remarks="投产数" type="int"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数" type="int"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数" type="int"/>
            <column name="record_time" remarks="记录时间" type="datetime"/>
            <column defaultValueBoolean="false" name="status" remarks="状态:0 未处理，1 已处理" type="BIT(1)"/>

            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>

        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="staff_id_index">
            <column name="staff_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="sub_work_sheet_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="work_sheet_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="extend_work_cell_id_index">
            <column name="extend_work_cell_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="work_cell_id_index">
            <column name="work_cell_id"/>
        </createIndex>

        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="batch_work_detail_id_index">
            <column name="batch_work_detail_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="container_detail_id_index">
            <column name="container_detail_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="sn_work_detail_id_index">
            <column name="sn_work_detail_id"/>
        </createIndex>
        <createIndex tableName="procedure_work_cell_extend_step_detail" indexName="status_index">
            <column name="status"/>
        </createIndex>

    </changeSet>
</databaseChangeLog>
