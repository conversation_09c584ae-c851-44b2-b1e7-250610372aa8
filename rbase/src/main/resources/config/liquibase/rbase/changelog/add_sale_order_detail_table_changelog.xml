<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

   <changeSet id="202504161933-001" author="ruima">

       <addColumn tableName="procedure_sale_order">
           <column remarks="项目编码" name="item_code" type="VARCHAR(255)"/>
       </addColumn>

       <createTable tableName="rbase_procedure_sale_order_detail" remarks="销售订单详情表">
           <column autoIncrement="true" name="id" type="BIGINT">
               <constraints nullable="false" primaryKey="true"/>
           </column>
           <column name="sale_order_id" remarks="销售订单id" type="BIGINT">
               <constraints nullable="false"/>
           </column>
           <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT"/>
           <column defaultValueNumeric="0" name="number" remarks="订单数量" type="INT"/>
           <column defaultValueNumeric="0" name="production_quantity" remarks="投产数" type="INT"/>
           <column defaultValueNumeric="0" name="finish_number" remarks="完成数量" type="INT"/>
           <column name="plan_start_date" remarks="计划开工日期" type="date"/>
           <column name="plan_end_date" remarks="计划完工日期" type="date"/>

           <column defaultValueNumeric="0" name="deleted" type="BIGINT">
               <constraints nullable="false"/>
           </column>
           <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
           <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
           <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
       </createTable>

       <addUniqueConstraint tableName="rbase_procedure_sale_order_detail" columnNames="sale_order_id, pedigree_id,deleted" constraintName="sod_unique"/>

       <createIndex tableName="rbase_procedure_sale_order_detail" indexName="sale_order_id_index">
           <column name="sale_order_id"/>
       </createIndex>
       <createIndex tableName="rbase_procedure_sale_order_detail" indexName="pedigree_id_index">
           <column name="pedigree_id"/>
       </createIndex>

       <createTable tableName="rbase_procedure_ws_sale_order_detail" remarks="工单关联销售订单详情">
           <column autoIncrement="true" name="id" type="BIGINT">
               <constraints nullable="false" primaryKey="true"/>
           </column>
           <column name="sale_order_detail_id" remarks="销售订单详情id" type="BIGINT">
               <constraints nullable="false"/>
           </column>
           <column name="work_sheet_id" remarks="工单id" type="BIGINT">
               <constraints nullable="false"/>
           </column>
           <column defaultValueNumeric="0" name="number" remarks="数量" type="INT"/>

           <column defaultValueNumeric="0" name="deleted" type="BIGINT">
               <constraints nullable="false"/>
           </column>
           <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
           <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
           <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
           <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
       </createTable>

       <addUniqueConstraint columnNames="sale_order_detail_id,work_sheet_id,deleted" tableName="rbase_procedure_ws_sale_order_detail" constraintName="ws_sod_unique"/>

       <createIndex tableName="rbase_procedure_ws_sale_order_detail" indexName="ws_sod_index">
           <column name="sale_order_detail_id"/>
       </createIndex>
       <createIndex tableName="rbase_procedure_ws_sale_order_detail" indexName="ws_index">
           <column name="work_sheet_id"/>
       </createIndex>


       <sql>
           -- 将销售订单数据迁移到销售订单详情表
           INSERT INTO rbase_procedure_sale_order_detail
           (id, sale_order_id, pedigree_id, number, plan_start_date, plan_end_date, created_date, last_modified_date, created_by, last_modified_by, deleted)
           SELECT
               id, -- 使用销售订单的id作为详情表的id
               id, -- 使用销售订单的id作为sale_order_id
               pedigree_id, -- 产品谱系ID
               number, -- 订单数量
               plan_start_date, -- 计划开工日期
               plan_end_date, -- 计划结单日期
               created_date, -- 创建日期
               last_modified_date, -- 最后修改日期
               created_by, -- 创建人
               last_modified_by, -- 最后修改人
               deleted -- 删除标记
           FROM
               procedure_sale_order
           WHERE
               deleted = 0; -- 只迁移未删除的记录

           -- 将工单与销售订单关联数据写入到工单关联销售订单详情表
           INSERT INTO rbase_procedure_ws_sale_order_detail
           (id, sale_order_detail_id, work_sheet_id, number, created_date, last_modified_date, created_by, last_modified_by, deleted)
           SELECT
               ws.id, -- 生成新的UUID作为id
               sod.id, -- 销售订单详情id
               ws.id, -- 工单id
               ws.number, -- 工单数量
               ws.created_date, -- 创建日期
               ws.last_modified_date, -- 最后修改日期
               ws.created_by, -- 创建人
               ws.last_modified_by, -- 最后修改人
               ws.deleted -- 删除标记
           FROM
               procedure_work_sheet ws
                   JOIN
               rbase_procedure_sale_order_detail sod ON ws.sale_order_id = sod.sale_order_id
           WHERE
               ws.sale_order_id IS NOT NULL -- 工单有销售订单关联
             AND ws.deleted = 0; -- 只迁移未删除的工单
       </sql>
   </changeSet>

    <changeSet id="20250717-001" author="YangS">
        <createTable tableName="rbase_procedure_planned_order" remarks="计划下单记录">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints primaryKey="true"/>
            </column>
            <column name="sale_order_detail_id" remarks="销售订单详情Id" type="bigint(20)"/>
            <column name="plan_user_id" remarks="计划下单用户Id" type="bigint(20)"/>
            <column name="allocate_user_id" remarks="派单用户Id" type="bigint(20)"/>
            <column defaultValue="0" name="status" remarks="计划订单待排状态：0待分配，1：已分配;2:已取消" type="tinyint(2)"/>

            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
        </createTable>

        <createIndex tableName="rbase_procedure_planned_order" indexName="sale_order_detail_id_index">
            <column name="sale_order_detail_id"/>
        </createIndex>
        <createIndex tableName="rbase_procedure_planned_order" indexName="status_index">
            <column name="status"/>
        </createIndex>
    </changeSet>




</databaseChangeLog>
