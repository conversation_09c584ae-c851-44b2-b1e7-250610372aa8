<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="ruima (generated)" id="202407181358">
        <createTable tableName="procedure_step_reinspect" remarks="工序不良复检表">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="复检单号" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="工单Id" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单Id" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="container_detail_id" remarks="容器详情id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="sn_work_status_id" remarks="SN生产状态Id" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_cell_id" remarks="不良产生工位ID" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="不良产生工序ID" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="origin_unqualified_item_id" remarks="原始不良项目ID" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="number" remarks="不良项目数量" type="INT(5)">
                <constraints nullable="false"/>
            </column>
            <column name="sponsor_id" remarks="发起人ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="processor_id" remarks="处理人ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sponsor_date" remarks="发起时间" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="process_date" remarks="处理时间" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column defaultValueBoolean="false" name="status" remarks="处理状态(false:待处理;true:已处理)" type="BIT(1)"/>
            <column defaultValueBoolean="false" name="last_step" remarks="是否为最后一个工序" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable tableName="procedure_step_reinspect_result" remarks="工序不良复检结果表">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="result" remarks="0:放行;1:返工;2:报废" type="TINYINT(3)"/>
            <column name="step_reinspect_id" remarks="工序不良复检Id" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="target_unqualified_item_id" remarks="复检不良项ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="number" remarks="数量" type="INT(5)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注信息" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createIndex tableName="procedure_step_reinspect" indexName="procedure_step_reinspect_sub_work_sheet_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_step_reinspect" indexName="procedure_step_reinspect_work_sheet_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_step_reinspect" indexName="procedure_step_reinspect_step_id_index">
            <column name="step_id"/>
        </createIndex>
        <createIndex tableName="procedure_step_reinspect" indexName="procedure_step_reinspect_sn_work_status_id_index">
            <column name="sn_work_status_id"/>
        </createIndex>
        <createIndex tableName="procedure_step_reinspect" indexName="procedure_step_reinspect_container_detail_id_index">
            <column name="container_detail_id"/>
        </createIndex>
        <createIndex tableName="procedure_step_reinspect_result" indexName="procedure_step_reinspect_result_reinspect_id_index">
            <column name="step_reinspect_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
