<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <property name="autoIncrement" value="true"/>


    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="202404161646" author="ruima (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createIndex indexName="base_work_cell_step_facility_multi_index" tableName="base_work_cell_step_facility">
            <column name="work_cell_id"/>
            <column name="step_id"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_step_specification" indexName="base_pedigree_step_specification_client_index">
            <column name="client_id"/>
        </createIndex>
        <createIndex tableName="base_pedigree_step_interval_config" indexName="base_pedigree_step_interval_config_multi_index">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_step" indexName="base_pedigree_step_multi_index">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column name="client_id"/>
            <column name="is_enable"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_step_unqualified_item" indexName="base_pedigree_step_unqualified_item_multi_index">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column name="client_id"/>
            <column name="is_enable"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_step_material_rule" indexName="base_pedigree_step_material_rule_multi_index">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column name="client_id"/>
            <column name="enable"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_work_flow" indexName="base_pedigree_work_flow_multi_index">
            <column name="pedigree_id"/>
            <column name="client_id"/>
            <column name="is_enable"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="base_pedigree_rework_work_flow" indexName="base_pedigree_rework_work_flow_multi_index">
            <column name="pedigree_id"/>
            <column name="unqualified_group_id"/>
            <column name="client_id"/>
            <column name="is_enable"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet id="202404191949" author="zorro">
        <createIndex tableName="procedure_ws_material_batch" indexName="procedure_ws_material_batch_group_multi_index">
            <column name="ws_id"/>
            <column name="material_id"/>
            <column name="batch"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet id="202404192312" author="zhuhuawu">
        <createIndex tableName="procedure_container_detail" indexName="procedure_container_detail_multi_index">
            <column name="batch_work_detail_id"/>
            <column name="container_id"/>
            <column name="status"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
