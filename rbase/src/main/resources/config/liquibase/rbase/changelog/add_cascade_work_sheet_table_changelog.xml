<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="ruima (generated)" id="202407170910">
        <createTable tableName="procedure_cascade_work_sheet" remarks="工单级联关系表">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="superior_work_sheet_id" remarks="上级工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="subordinate_work_sheet_id" remarks="下级工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="superior_work_sheet_id,subordinate_work_sheet_id,deleted"
                             constraintName="procedure_cascade_work_sheet_unique"
                             tableName="procedure_cascade_work_sheet"/>
        <createIndex tableName="procedure_cascade_work_sheet" indexName="procedure_cascade_work_sheet_superior_work_sheet_index">
            <column name="superior_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_cascade_work_sheet" indexName="procedure_cascade_work_sheet_subordinate_work_sheet_index">
            <column name="subordinate_work_sheet_id"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>
