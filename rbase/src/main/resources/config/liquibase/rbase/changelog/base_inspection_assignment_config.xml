<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="ruima" id="create-base-inspection-assignment-config-table">
        <createTable tableName="base_inspection_assignment_config" remarks="质检人员设置">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints primaryKey="true"/>
            </column>
            <column name="inspection_type" remarks="质检类型(首检0/巡检1/抽检2/终检3/末检4/来料检5/破坏性检验6)"
                    type="tinyint(2)">
                <constraints nullable="false"/>
            </column>
            <column name="priority_element_config_id" remarks="条件优先级配置ID" type="bigint(20)"/>
            <column name="work_line_id" remarks="生产线ID" type="bigint(20)"/>
            <column name="pedigree_id" remarks="产品谱系ID" type="bigint(20)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="bigint(20)"/>
            <column name="step_id" remarks="工序ID" type="bigint(20)"/>
            <column name="material_id" remarks="物料ID" type="bigint(20)"/>
            <column name="client_id" remarks="客户ID" type="bigint(20)"/>
            <column name="supplier_id" remarks="供应商ID" type="bigint(20)"/>
            <column name="assignment_type" remarks="分配质检人员类型(按部门随机分配0/按角色随机分配1/指定人员2)"
                    type="tinyint(2)">
                <constraints nullable="false"/>
            </column>
            <column name="assignment_id" remarks="分配质检人员(部门ID/角色ID/人员ID)" type="bigint(20)"/>
            <column name="inspection_escalate" remarks="升级策略配置" type="json"/>
            <column name="enabled" remarks="是否启用" type="bit(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="bigint(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
