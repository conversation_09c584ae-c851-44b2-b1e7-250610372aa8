<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

   <changeSet id="202504072010-001" author="ruima">
       <!-- 创建表结构 -->
       <createTable tableName="rbase_base_pedigree_step_inspection_config" remarks="工序检查配置表">
           <column autoIncrement="true" name="id" type="bigint">
               <constraints nullable="false" primaryKey="true"/>
           </column>
           <!-- 外键关联 pedigree_step_id -->
           <column name="pedigree_step_id" type="bigint" remarks="产品谱系工序配置ID">
               <constraints nullable="false"/>
           </column>
           <column defaultValueNumeric="0" name="inspection_type" remarks="检查类型 (0:质量检查;1:设备检查)"
                   type="tinyint(2)">
               <constraints nullable="false"/>
           </column>
           <column defaultValueNumeric="0" name="handling_type" remarks="NG处理方式 (0:返工处理;1:重新调试)"
                   type="tinyint(2)">
               <constraints nullable="false"/>
           </column>
           <!-- 最大NG次数 -->
           <column defaultValueNumeric="0" name="max_ng_count" type="integer" remarks="最大NG次数"/>
           <!-- 超次策略 (0:人工确认放行) -->
           <column defaultValueNumeric="0" name="override_strategy" type="tinyint(2)" remarks="超次策略 (0:人工确认放行)">
               <constraints nullable="false"/>
           </column>
           <column defaultValueNumeric="0" name="deleted" type="bigint">
               <constraints nullable="false"/>
           </column>
           <column name="created_by" remarks="新建人" type="varchar(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
           <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                   type="timestamp"/>
           <column name="custom1" remarks="定制字段" type="varchar(255)"/>
           <column name="custom2" remarks="定制字段" type="varchar(255)"/>
           <column name="custom3" remarks="定制字段" type="varchar(255)"/>
           <column name="custom4" remarks="定制字段" type="varchar(255)"/>
           <column name="custom5" remarks="定制字段" type="varchar(255)"/>
       </createTable>

       <!-- 为外键字段添加索引（可选，提升查询性能） -->
       <createIndex
               tableName="rbase_base_pedigree_step_inspection_config"
               indexName="idx_pedigree_step_id_id">
           <column name="pedigree_step_id"/>
       </createIndex>

       <addUniqueConstraint tableName="rbase_base_pedigree_step_inspection_config" columnNames="pedigree_step_id,inspection_type,deleted"
                            constraintName="idx_pedigree_config_unique"/>


       <createTable tableName="rbase_procedure_pedigree_step_inspection_history" remarks="工序检查历史记录表">
           <column autoIncrement="true" name="id" type="bigint">
               <constraints nullable="false" primaryKey="true"/>
           </column>
           <column name="work_sheet_id" type="bigint" remarks="工单ID"/>
           <column name="sub_work_sheet_id" type="bigint" remarks="子工单ID"/>
           <column name="step_id" type="bigint" remarks="工序ID"/>
           <column name="container_id" type="bigint" remarks="容器ID"/>
           <column name="sn" type="varchar(255)" remarks="SN"/>
           <column name="work_cell_id" type="bigint" remarks="工位ID"/>
           <column name="staff_id" type="bigint" remarks="提出人ID"/>
           <column defaultValueNumeric="0" name="inspection_type" type="tinyint(2)" remarks="检查类型(0:质量检查;1:设备检查)"/>
           <column defaultValueNumeric="0" name="inspection_result" type="tinyint(2)" remarks="检查结果(0:不合格;1:合格)"/>
           <column name="inspection_time" type="datetime" remarks="检查时间"/>
           <column name="inspector_id" type="bigint" remarks="检查人ID"/>
           <column defaultValueNumeric="0" name="deleted" type="bigint">
               <constraints nullable="false"/>
           </column>
           <column name="created_by" remarks="新建人" type="varchar(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
           <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
           <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                   type="timestamp"/>
           <column name="custom1" remarks="定制字段" type="varchar(255)"/>
           <column name="custom2" remarks="定制字段" type="varchar(255)"/>
           <column name="custom3" remarks="定制字段" type="varchar(255)"/>
           <column name="custom4" remarks="定制字段" type="varchar(255)"/>
           <column name="custom5" remarks="定制字段" type="varchar(255)"/>

       </createTable>

       <!-- 添加索引 -->
       <createIndex indexName="idx_pedigree_step_inspection_history_sub_ws_id"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="sub_work_sheet_id"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_ws_id"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="work_sheet_id"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_container_id"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="container_id"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_step_id"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="step_id"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_sn"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="sn"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_work_cell_id"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="work_cell_id"/>
       </createIndex>

       <createIndex indexName="idx_pedigree_step_inspection_history_inspection_time"
                    tableName="rbase_procedure_pedigree_step_inspection_history">
           <column name="inspection_time"/>
       </createIndex>

   </changeSet>

    <changeSet id="202507021034-001" author="YangS">
        <dropIndex tableName="rbase_procedure_pedigree_step_inspection_history" indexName="idx_pedigree_step_inspection_history_sn"/>
        <dropIndex tableName="rbase_procedure_pedigree_step_inspection_history" indexName="idx_pedigree_step_inspection_history_container_id"/>

        <dropColumn tableName="rbase_procedure_pedigree_step_inspection_history" columnName="sn"/>
        <dropColumn tableName="rbase_procedure_pedigree_step_inspection_history" columnName="container_id"/>

        <addColumn tableName="rbase_procedure_pedigree_step_inspection_history">
            <column name="note" remarks="备注(退回原因)" type="text"/>
        </addColumn>

        <createTable tableName="rbase_procedure_pedigree_step_inspection_history_detail" remarks="工序检查历史记录详情表">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_step_inspection_history_id" type="bigint(20)" remarks="工序检查历史记录ID"/>
            <column name="container_code" type="varchar(255)" remarks="容器号"/>
            <column name="sn" type="varchar(255)" remarks="SN"/>
            <column name="result" type="tinyint(2)" remarks="Sn结果：0不合格，1合格"/>
            <column defaultValueNumeric="0" name="handle_type" type="tinyint(2)" remarks="处理方式：0退回重做，1：允许下交"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
        </createTable>

        <createIndex tableName="rbase_procedure_pedigree_step_inspection_history_detail" indexName="psihd_sn_index">
            <column name="sn"/>
        </createIndex>

        <createIndex tableName="rbase_procedure_pedigree_step_inspection_history_detail" indexName="psihd_container_code_index">
            <column name="container_code"/>
        </createIndex>

        <createIndex tableName="rbase_procedure_pedigree_step_inspection_history_detail" indexName="psihd_history_id_index">
            <column name="pedigree_step_inspection_history_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="202507030948-001" author="YangS">
        <addColumn tableName="rbase_procedure_pedigree_step_inspection_history_detail">
            <column name="status" remarks="处理状态：0待处理,1已处理" type="tinyint(2)" defaultValue="0"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
