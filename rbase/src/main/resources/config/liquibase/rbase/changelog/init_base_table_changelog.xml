<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="RMPC (generated)" id="1713521050316-1">
        <createTable remarks="区域工位" tableName="base_area_work_cell">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="area_id" remarks="部门区域ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-7">
        <createTable tableName="base_container">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="容器名称" type="VARCHAR(255)"/>
            <column name="code" remarks="容器编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="status" remarks="是否占用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-19">
        <createTable tableName="base_online_rework_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_item_id" remarks="不良现象id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-20">
        <createTable remarks="部门区域表" tableName="base_organization_area">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="organization_id" remarks="部门ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="区域编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="区域名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-21">
        <createTable tableName="base_pedigree">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" remarks="父级id" type="BIGINT"/>
            <column name="type" remarks="当前层级类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:不启用;1:启用)" type="BIT(1)"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="specification" remarks="规格型号" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-22">
        <createTable tableName="base_pedigree_config">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="split_number" remarks="分单数量" type="INT"/>
            <column name="is_custom_work_flow" remarks="是否需要重新定制流程框图(0:否;1:是)" type="BIT(1)"/>
            <column name="plan_finish_day" remarks="计划完成天数" type="INT"/>
            <column name="qualified_rate" remarks="目标成品率" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="is_check_receive_material" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="workflow_type" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="0" name="is_reuse_sn" remarks="是否复用SN(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-25">
        <createTable tableName="base_pedigree_rework_work_flow">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="work_flow_id" remarks="流程框图id" type="BIGINT"/>
            <column name="unqualified_group_id" remarks="不良项目类别Id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-26">
        <createTable remarks="产品谱系SN复用配置表" tableName="base_pedigree_sn_reuse_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="投产谱系" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="reuse_pedigree_id" remarks="复用谱系" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-27">
        <createTable tableName="base_pedigree_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="request_mode" remarks="请求模式" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="control_mode" remarks="管控模式(0:批量;1:单支)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="is_control_material" remarks="是否管控物料(0:不管控;1:管控)" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValue="0" name="is_bind_container" remarks="是否绑定容器(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column defaultValueNumeric="1.0" name="input_rate" remarks="投产比例" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="standard_time" remarks="标准工时（精确到秒）" type="DECIMAL(10, 1)"/>
            <column defaultValueNumeric="0" name="standard_daily_output" remarks="工序理论日产出数量" type="BIGINT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-28">
        <createTable tableName="base_pedigree_step_check_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="qualified_range" remarks="合格范围(数学开闭区间或者OK)" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="check_item_id" remarks="检测项目id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="pedigree_step_check_rule_id" remarks="检测规则id" type="BIGINT"/>
            <column defaultValue="1" name="control" remarks="是否管控其检查结果(0:不管控；1：管控)" type="BIT(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-29">
        <createTable tableName="base_pedigree_step_check_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="category" remarks="检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)" type="TINYINT(3)"/>
            <column name="judge_way" remarks="判定类型(0:数量;1:比例)" type="BIT(1)"/>
            <column name="base_number" remarks="检测基数(小于基数全检，大于基数按照比例抽检)" type="INT"/>
            <column name="rate" type="DOUBLE"/>
            <column name="qualified_rate" remarks="合格比例" type="DOUBLE"/>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="qualified_number" remarks="合格数量" type="INT"/>
            <column name="code" remarks="编码" type="VARCHAR(50)"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="work_sheet_category" remarks="工单类型(0:离线返修单;1:正常单;)" type="TINYINT(3)"/>
            <column name="step_group_id" remarks="工序组id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column name="variety" remarks="项目类型" type="TINYINT(3)"/>
            <column name="sample_case_id" remarks="抽检方案id" type="BIGINT"/>
            <column name="variety_id" remarks="项目类型id" type="BIGINT"/>
            <column name="name" remarks="名称" type="VARCHAR(50)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="expiry_date" remarks="有效期" type="timestamp"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-30">
        <createTable remarks="产品谱系工序间隔配置" tableName="base_pedigree_step_interval_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_flow_id" remarks="工艺路线id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="pre_step_id" remarks="前置工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="duration" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="2" name="duration_unit" remarks="单位(0:秒，1:分钟,2:小时,3:天)" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-31">
        <createTable tableName="base_pedigree_step_material_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="material_id" remarks="物料ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1.0" name="proportion" remarks="扣料比例" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_deduct" remarks="是否扣数(1:扣数;0:不扣数)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_check_material" remarks="是否核物料(1:核对物料编码;0:不核对物料编码)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_check_material_batch" remarks="是否核物料批次(1:核对物料批次;0:不核对物料批次)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column defaultValueNumeric="1" name="control_material_granularity" remarks="物料管控粒度(0:单只序列号;1:批次号)" type="INT"/>
            <column defaultValueNumeric="0" name="control_sn_count" remarks="定义需要扫码多少次序列号，可不管控或指定数量（当管控粒度为单只，进行管控，为0不进行管控）" type="INT"/>
            <column name="serial_number_rule" remarks="批次号/序列号规则（正则表达式方式）" type="JSON"/>
            <column name="client_id" remarks="客户代码" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column defaultValue="1" name="enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-32">
        <createTable remarks="产品谱系工序目标良率" tableName="base_pedigree_step_pass_rate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT"/>
            <column name="step_group_id" remarks="工序组ID" type="BIGINT"/>
            <column defaultValueNumeric="1.0" name="target_pass_rate" remarks="目标良率" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-34">
        <createTable tableName="base_pedigree_step_specification">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sop" type="VARCHAR(255)"/>
            <column name="qualification" type="LONGTEXT"/>
            <column name="pedigree_id" remarks="谱系id" type="BIGINT"/>
            <column name="step_id" remarks="工序Id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-35">
        <createTable tableName="base_pedigree_step_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="unqualified_item_id" remarks="不良id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-37">
        <createTable tableName="base_pedigree_work_flow">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" type="BIGINT"/>
            <column name="work_flow_id" remarks="流程框图id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-38">
        <createTable remarks="条件优先级配置" tableName="base_priority_element_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="target" remarks="条件对象" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="combination" remarks="条件组合，对应优先级元素[1,2,3]" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="priority" remarks="优先级(数字越小优先级越高)" type="INT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-44">
        <createTable tableName="base_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="category" remarks="工序类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="step_group_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-45">
        <createTable tableName="base_step_config">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="request_mode" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="control_mode" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="is_control_material" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="is_bind_container" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column defaultValueNumeric="1.0" name="input_rate" remarks="投产比例" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="standard_time" remarks="标准工时（精确到秒）" type="DECIMAL(10, 1)"/>
            <column defaultValueNumeric="0" name="standard_daily_output" remarks="工序理论日产出数量" type="BIGINT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-49">
        <createTable tableName="base_step_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="工序组别名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="工序组别编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-50">
        <createTable tableName="base_step_unqualified_item">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_item_id" remarks="不良id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-51">
        <createTable tableName="base_step_warning_standard">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="base_number" remarks="预警基数" type="INT"/>
            <column defaultValueNumeric="0.0" name="waring_rate" remarks="警告判断标准" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="stop_rate" remarks="停线判断标准" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="work_sheet_category" remarks="工单类型(0:离线返修单;1:正常单;)" type="TINYINT(3)"/>
            <column name="step_group_id" remarks="工序组id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-53">
        <createTable tableName="base_unqualified_cause">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-54">
        <createTable tableName="base_unqualified_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-56">
        <createTable tableName="base_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="不合格项目代码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="不合格项目名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_group_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deal_way" remarks="处理方式(0,在线返修;1,流程返修;2,报废）" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:不启用;1:启用)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-57">
        <createTable tableName="base_unqualified_item_cause">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="unqualified_item_id" remarks="不良现象ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_cause_id" remarks="不良原因ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-58">
        <createTable tableName="base_unqualified_item_warning_standard">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT"/>
            <column name="step_id" type="BIGINT"/>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="base_number" remarks="预警基数" type="INT"/>
            <column defaultValueNumeric="0.0" name="waring_rate" remarks="警告判断标准" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="stop_rate" remarks="停线判断标准" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="1" name="waring_number" remarks="预警数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="work_sheet_category" remarks="工单类型(0:离线返修单;1:正常单;)" type="TINYINT(3)"/>
            <column name="step_group_id" remarks="工序组id" type="BIGINT"/>
            <column name="work_flow_id" remarks="工艺路线id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="unqualified_group_id" remarks="不良种类id" type="BIGINT"/>
            <column defaultValue="0" name="is_fai" remarks="是否触发首检" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-64">
        <createTable tableName="base_work_cell">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="工位名" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="category" remarks="工位类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="is_connect_equipment" remarks="是否接入自动设备(0:不接入;1:接入)" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="position" remarks="工位在工站中的位置" type="TINYINT(3)"/>
            <column name="work_station_id" remarks="工站id" type="BIGINT"/>
            <column name="organization_id" remarks="组织架构id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="work_line_id" remarks="生产线ID" type="BIGINT"/>
            <column defaultValueNumeric="1" name="order_number" remarks="工位顺序号" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="ip" remarks="工位IP" type="VARCHAR(25)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-65">
        <createTable tableName="base_work_cell_check_start_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" type="BIGINT"/>
            <column name="category" remarks="检测类型(首检0/巡检1/抽检2/终检3)" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="flag" remarks="检测时机(0:切换型号;1:切换总工单;2:切换子工单;3:固定周期)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0.0" name="duration" remarks="周期间隔(H)" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0.0" name="extend_time" remarks="宽放时长(H)" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="specify_time" remarks="指定时间(例如: 11:45;12:00等多个时间段)" type="VARCHAR(255)"/>
            <column name="variety" remarks="项目类型" type="TINYINT(3)"/>
            <column name="variety_id" remarks="项目类型ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="target" remarks="发起类型(工位0/工序后1)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-66">
        <createTable tableName="base_work_cell_staff">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_id" remarks="员工id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-67">
        <createTable tableName="base_work_cell_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-68">
        <createTable tableName="base_work_cell_step_facility">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="facility_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-69">
        <createTable tableName="base_work_flow">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="框图编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="category" remarks="流程框图类型(0:正常生产流程;1:返修方案流程)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-70">
        <createTable remarks="转工艺路线配置" tableName="base_work_flow_convert_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT"/>
            <column name="origin_work_flow_id" remarks="原始工艺路线id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="target_work_flow_id" remarks="目标工艺路线id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="priority_element_config_id" remarks="条件优先级配置id" type="BIGINT"/>
            <column defaultValue="0" name="enable" remarks="状态(0:禁用;1:启用)" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-71">
        <createTable tableName="base_work_flow_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_flow_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="pre_step_id" remarks="前置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="after_step_id" remarks="后置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-72">
        <createTable tableName="base_work_line">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="线体名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organization_id" remarks="组织架构id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="线体编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="order_number" remarks="生产线顺序号" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-73">
        <createTable tableName="base_work_station">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="工站名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="work_line_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="order_number" remarks="工站顺序号" type="INT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-74">
        <createTable tableName="procedure_batch_work_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="input_number" remarks="工序投产数" type="INT"/>
            <column defaultValueNumeric="0" name="finish_number" remarks="工序实际完成数量" type="INT"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="工序合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="工序不合格数量" type="INT"/>
            <column name="transfer_number" remarks="待流转数量" type="INT"/>
            <column defaultValueNumeric="0.0" name="effect_number" remarks="有效合格数" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="work_hour" remarks="工时(工序完成耗时)" type="DOUBLE"/>
            <column name="start_date" remarks="工序开始时间" type="timestamp"/>
            <column name="end_date" remarks="完成日期" type="timestamp"/>
            <column defaultValueNumeric="0" name="finish" remarks="是否完成(1:完成;0:未完成)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="操作人" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="version" remarks="乐观锁版本" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="dynamic_data" remarks="动态数据信息" type="JSON"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-75">
        <createTable tableName="procedure_batch_work_detail_facility">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="batch_work_detail_id" remarks="工单批量生产详情" type="BIGINT"/>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-76">
        <createTable tableName="procedure_batch_work_detail_material_batch">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="material_batch" remarks="物料批次" type="VARCHAR(255)"/>
            <column name="serial" remarks="批次流水号" type="VARCHAR(255)"/>
            <column name="batch_work_detail_id" remarks="工单批次详情id" type="BIGINT"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="supplier_id" remarks="供应商id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="number" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="type" remarks="扣料方式" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-81">
        <createTable tableName="procedure_check_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="category" remarks="检测类型(首检0/巡检1/终检3/抽检4)" type="TINYINT(3)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="检测日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="result" remarks="是否合格(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="number" remarks="检测数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column name="operator_id" remarks="员工id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="variety" remarks="项目类型" type="TINYINT(3)"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="variety_id" remarks="项目类型ID" type="BIGINT"/>
            <column name="check_rule_id" remarks="质检方案id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="inspect_number" remarks="报检数量" type="INT"/>
            <column defaultValueNumeric="1" name="deal_way" remarks="检测方式(待处理0/通过1/重检2/放行3)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="container_code" remarks="容器号" type="VARCHAR(255)"/>
            <column defaultValue="0" name="status" remarks="是否处理结果(0:否;1:是)" type="BIT(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-82">
        <createTable tableName="procedure_check_history_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="SN" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="result" remarks="是否合格(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="history_id" remarks="检测历史id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="check_item_id" remarks="检测项目id" type="BIGINT"/>
            <column name="check_data" remarks="检测结果值" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="qualified_range" remarks="合格范围(开闭区间或者OK)" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="defect_id" remarks="缺陷原因id" type="BIGINT"/>
            <column defaultValue="1" name="is_virtual" remarks="是否为虚拟SN(0:否;1:是)" type="BIT(1)"/>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-83">
        <createTable remarks="检测历史条件详情记录表" tableName="procedure_check_history_item_snapshot">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="check_history_id" remarks="首检巡检历史id" type="BIGINT"/>
            <column name="variety_id" remarks="项目类型ID" type="BIGINT"/>
            <column name="check_item_id" remarks="检测项目id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="check_way" remarks="检验方法:目测0/检测仪器1" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="analyse_way" remarks="分析方法:定性0/定量1" type="TINYINT(3)"/>
            <column name="qualified_range" remarks="合格范围(数学开闭区间或者OK)" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="facility" remarks="检验设备" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-84">
        <createTable tableName="procedure_container_detail">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="batch_work_detail_id" remarks="批量工作详情id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="container_id" remarks="容器id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="staff_id" remarks="操作员工id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="container_code" remarks="容器编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="记录日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="bind_time" remarks="绑定日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="unbind_time" remarks="解绑日期" type="timestamp"/>
            <column name="status" remarks="绑定状态" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="input_number" remarks="投产数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="qualified_number" remarks="合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_number" remarks="不合格数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="transfer_number" remarks="待流转数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="pre_container_detail_info" remarks="前置容器详情id以及下交数量" type="JSON"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="pre_container_code" remarks="原容器列表" type="VARCHAR(255)"/>
            <column name="after_container_code" remarks="转换容器列表" type="VARCHAR(255)"/>
            <column name="start_time" remarks="容器开始时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="main_tain_status" remarks="0:正常态,1:待维修" type="TINYINT(3)"/>
            <column name="dynamic_data" remarks="动态数据信息" type="JSON"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-85">
        <createTable tableName="procedure_container_detail_facility">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="container_detail_id" remarks="容器详情id" type="BIGINT"/>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-86">
        <createTable tableName="procedure_container_detail_material_batch">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="container_detail_id" remarks="容器详情ID" type="BIGINT"/>
            <column name="material_id" remarks="物料ID" type="BIGINT"/>
            <column name="batch" remarks="物料批次" type="VARCHAR(50)"/>
            <column name="supplier_id" remarks="供应商id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="number" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="type" remarks="扣料方式" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-87">
        <createTable tableName="procedure_container_detail_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="container_detail_id" remarks="容器详情ID" type="BIGINT"/>
            <column name="unqualified_item_id" remarks="不良项目 ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="number" remarks="数量" type="INT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="repair_count" remarks="已返修数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="flag" remarks="标识是否已生成在线返修单0:否;1:是" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-89">
        <createTable tableName="procedure_custom_pedigree_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="pre_step_id" remarks="前置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="after_step_id" remarks="后置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="workflow_id" remarks="流程框图" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="category" remarks="工序类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="request_mode" remarks="请求模式" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="control_mode" remarks="管控模式(0:批量;1:单支)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="is_control_material" remarks="是否管控物料(0:不管控;1:管控)" type="BIT(1)"/>
            <column defaultValue="0" name="is_bind_container" remarks="是否绑定容器(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1.0" name="input_rate" remarks="投产比例" type="DOUBLE"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-93">
        <createTable tableName="procedure_fqc_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="编号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="check_staff_id" remarks="抽检人ID" type="BIGINT"/>
            <column name="work_cell_id" remarks="抽检工位" type="BIGINT"/>
            <column name="sub_ws_id" remarks="子工单id" type="BIGINT"/>
            <column name="original_sub_ws_id" remarks="原始子工单Id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0.0" name="check_number" remarks="抽检数" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0.0" name="unqualified_number" remarks="不良数" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="reason" remarks="发生原因" type="VARCHAR(255)"/>
            <column name="type" remarks="处理方式(0:批退;1:放行)" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="status" remarks="状态(0:未处理;1:已处理)" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="deal_staff_id" remarks="处理人ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deal_way" remarks="处理途径(0工位处理,1网页处理)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="result" remarks="是否合格(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="lock_ws" remarks="是否锁总工单(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-94">
        <createTable tableName="procedure_fqc_check_result_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="sn" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_check" remarks="是否为抽检SN" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="result" remarks="是否合格(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="fqc_check_result_id" remarks="fqc检测结果id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_item_id" remarks="不良现象" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-98">
        <createTable remarks="待检任务表" tableName="procedure_inspect_task">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="variety_id" remarks="项目类型id" type="BIGINT"/>
            <column name="container_code" remarks="容器号" type="VARCHAR(255)"/>
            <column defaultValue="0" name="status" remarks="处理结果:0未处理，1已处理" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="category" remarks="检测类型(首检0/巡检1/抽检2/终检3)" type="INT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-99">
        <createTable remarks="不良品管理记录表" tableName="procedure_inspect_unqualified">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="number" remarks="不良品数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="check_history_id" remarks="检测历史id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deal_result" remarks="处理结果:0待处理，1维修分析" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="deal_staff_id" remarks="处理人" type="BIGINT"/>
            <column name="deal_time" remarks="处理时间" type="timestamp"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-100">
        <createTable remarks="不良管理记录详情表" tableName="procedure_inspect_unqualified_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="inspect_unqualified_id" remarks="不良品管理记录id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="check_item_id" remarks="检验项目id" type="BIGINT"/>
            <column name="defect_id" remarks="缺陷原因id" type="BIGINT"/>
            <column name="check_data" remarks="检验数据" type="VARCHAR(255)"/>
            <column defaultValue="1" name="check_result" remarks="处理结果:0不合格，1合格" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="sn" remarks="sn" type="VARCHAR(255)"/>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT"/>
            <column defaultValue="1" name="is_virtual" remarks="是否为虚拟SN(0:否;1:是)" type="BIT(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-101">
        <createTable tableName="procedure_latest_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="category" remarks="检测类型(首检0/巡检1)" type="TINYINT(3)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="更新日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="next_check_date" type="timestamp"/>
            <column defaultValue="1" name="result" remarks="是否合格(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="variety" remarks="项目类型" type="TINYINT(3)"/>
            <column name="variety_id" remarks="项目类型ID" type="BIGINT"/>
            <column defaultValueNumeric="1" name="deal_way" remarks="处理方式(待处理0/通过1/重检2/放行3)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="status" remarks="是否处理结果(0:否;1:是)" type="BIT(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-106">
        <createTable remarks="产品谱系工序生产在制统计表" tableName="procedure_pedigree_step_statistics">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT"/>
            <column name="work_line_id" remarks="生产线ID" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="input_number" remarks="工序投产数量" type="INT"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="工序合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="工序不合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="plan_number" remarks="工序计划数量" type="INT"/>
            <column defaultValueNumeric="0" name="transfer_number" remarks="工序下交数量" type="INT"/>
            <column defaultValueNumeric="0" name="online_number" remarks="工序在线数量" type="INT"/>
            <column defaultValueNumeric="1.0" name="target_qualified_rate" remarks="工序目标良率" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-107">
        <createTable remarks="生产计划表" tableName="procedure_production_plan">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="organization_id" remarks="组织架构ID(预留扩展)" type="BIGINT"/>
            <column name="work_line_id" remarks="生产线ID" type="BIGINT"/>
            <column name="step_group_id" remarks="工序组别ID" type="BIGINT"/>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="category" remarks="计划粒度(0:工序组,1:生产线)" type="TINYINT(3)"/>
            <column defaultValue="0" name="status" remarks="计划状态(0:待确认;1:已确认)" type="BIT(1)"/>
            <column defaultValueNumeric="1" name="plan_number" remarks="计划产出" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="actual_number" remarks="实际产出" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="plan_date" remarks="计划日期" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-108">
        <createTable remarks="回退历史表" tableName="procedure_roll_back_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单" type="BIGINT"/>
            <column name="operator_id" remarks="回退操作员工id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="number" remarks="当前工序或者容器详情中的投产数量" type="INT"/>
            <column name="note" remarks="回退原因" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="work_sheet_id" remarks="工单Id" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="最小粒度合格数量" type="INT"/>
            <column name="container_code" remarks="回退容器编码" type="VARCHAR(50)"/>
            <column name="sn" remarks="回退SN" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-109">
        <createTable remarks="条件优先级配置" tableName="procedure_sale_order">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="订单号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="priority" remarks="优先级" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="category" remarks="订单类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="delivery_date" remarks="交付日期" type="date"/>
            <column name="pedigree_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="number" remarks="订单数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="production_quantity" remarks="投产数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="contract_no" remarks="合同编号" type="VARCHAR(255)"/>
            <column name="client_id" remarks="客户代码id" type="BIGINT"/>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column name="plan_start_date" remarks="计划开工日期" type="date"/>
            <column name="plan_end_date" remarks="计划完工日期" type="date"/>
            <column defaultValueNumeric="0" name="finish_number" remarks="完成数量" type="INT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-110">
        <createTable tableName="procedure_sn_rework">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="container_id" remarks="容器ID" type="BIGINT"/>
            <column name="rework_ws_id" remarks="在线返修单关联ID" type="BIGINT"/>
            <column name="sn_work_status_id" type="BIGINT"/>
            <column defaultValueNumeric="1" name="status" remarks="绑定状态(0:解绑;1:绑定)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-111">
        <createTable tableName="procedure_sn_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="投产SN" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValue="0" name="flag" remarks="标识是否已生成在线返修单0:否;1:是" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="sn_work_detail_id" remarks="sn详情id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-112">
        <createTable tableName="procedure_sn_work_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="投产SN" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" remarks="生产开始时间" type="timestamp"/>
            <column name="end_date" remarks="生产完成时间" type="timestamp"/>
            <column defaultValueNumeric="0.0" name="work_hour" remarks="生产完成耗时" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="rework_time" remarks="返修次数" type="INT"/>
            <column defaultValueNumeric="0" name="result" remarks="结果(0:不合格;1:合格)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="操作人" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="container_work_detail_id" remarks="容器详情ID" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT"/>
            <column name="dynamic_data" remarks="动态数据信息" type="JSON"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-113">
        <createTable tableName="procedure_sn_work_detail_facility">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn_work_detail_id" remarks="SN工作详情id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-114">
        <createTable tableName="procedure_sn_work_detail_material_batch">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="material_id" remarks="物料ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="supplier_id" remarks="供应商ID" type="BIGINT"/>
            <column name="material_batch" remarks="物料批次" type="VARCHAR(255)"/>
            <column name="sn_work_detail_id" remarks="SN工作详情id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="number" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="type" remarks="扣料方式" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-116">
        <createTable tableName="procedure_sn_work_status">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="投产SN" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="rework_time" remarks="返修次数" type="INT"/>
            <column defaultValueNumeric="0" name="status" remarks="生产状态: 0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废" type="INT"/>
            <column defaultValue="1" name="is_update_batch_work_detail" remarks="是否工单详情(1:更新工单详情数据;0:不更新工单详情数据)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" remarks="生产开始时间" type="timestamp"/>
            <column name="rework_start_date" remarks="返修开始时间" type="timestamp"/>
            <column name="end_date" remarks="最终完成时间" type="timestamp"/>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="latest_sn_work_detail_id" remarks="SN工作详情id" type="BIGINT"/>
            <column name="work_flow_id" remarks="流程框图id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="latest_rework_sn_work_detail_id" remarks="不合格SN详情id" type="BIGINT"/>
            <column name="latest_unqualified_item_id" remarks="最新不良项目" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-117">
        <createTable remarks="员工产量表" tableName="procedure_staff_perform">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_id" remarks="员工ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sub_work_sheet_id" type="BIGINT"/>
            <column name="work_sheet_id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="工序ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="batch_work_detail_id" remarks="批次详情id" type="BIGINT"/>
            <column name="container_detail_id" remarks="容器详情id" type="BIGINT"/>
            <column name="sn_work_detail_id" remarks="sn详情id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="input_number" remarks="投产数量" type="INT"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数量" type="INT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time" remarks="记录时间" type="timestamp"/>
            <column name="record_date" remarks="记录日期" type="timestamp"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-118">
        <createTable remarks="员工不良明细表" tableName="procedure_staff_perform_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_perform_id" remarks="员工产量ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_item_id" remarks="不良项目ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="number" remarks="不良数量" type="INT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time" remarks="记录时间" type="timestamp"/>
            <column name="record_date" remarks="记录日期" type="timestamp"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-122">
        <createTable tableName="procedure_sub_work_sheet">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_line_id" remarks="生产线id" type="BIGINT"/>
            <column name="serial_number" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="is_finish" remarks="完成状态(0:未完成;1:完成)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="version" remarks="乐观锁版本" type="BIGINT"/>
            <column name="work_flow_id" remarks="流程框图" type="BIGINT"/>
            <column defaultValueNumeric="0" name="number" remarks="投产数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="rework_qualified_number" remarks="在线返修合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="plan_start_date" remarks="计划开工日期" type="timestamp"/>
            <column name="plan_end_date" remarks="计划结单日期" type="timestamp"/>
            <column name="actual_start_date" remarks="实际开工日期" type="timestamp"/>
            <column name="actual_end_date" remarks="实际完成日期" type="timestamp"/>
            <column defaultValueNumeric="0" name="status" remarks="工单状态(0:正常态;1:暂停态;2:终止态)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="sync_status" remarks="erp上传状态(0:未上传,1：已上传)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="TEXT"/>
            <column name="statement_reason" remarks="结单原因" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="down_grade_number" remarks="降级总数" type="INT"/>
            <column name="priority" remarks="优先级" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="step_number" type="INT"/>
            <column defaultValueNumeric="0" name="step_comp_number" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-123">
        <createTable tableName="procedure_unqualified_event">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="预警单号" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="event_type" remarks="事件类型0:警告1:停线" type="TINYINT(3)"/>
            <column name="status" type="TINYINT(3)"/>
            <column name="owner_id" remarks="责任人" type="BIGINT"/>
            <column name="sub_ws_id" remarks="子工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column defaultValueNumeric="0.0" name="qualified_rate" remarks="合格率" type="DOUBLE"/>
            <column name="deal_staff_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deal_way" remarks="处理意见0:恢复生产1:暂停生产" type="TINYINT(3)"/>
            <column name="deal_time" remarks="处理时间" type="timestamp"/>
            <column name="reason" remarks="预警原因" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="reason_type" remarks="预警原因类型" type="TINYINT(3)"/>
            <column name="record_time" remarks="发生时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="unqualified_item_id" remarks="不良项目" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0.0" name="unqualified_rate" remarks="不良占有率" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不良个数" type="INT"/>
            <column name="ws_id" remarks="工单ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-132">
        <createTable remarks="员工工位状态表" tableName="procedure_work_cell_staff_status">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_id" remarks="员工ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="latest_login_time" remarks="最新登陆时间" type="timestamp"/>
            <column name="latest_logout_time" remarks="最新登出时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-133">
        <createTable tableName="procedure_work_sheet">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="工单号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="number" remarks="投产数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="rework_qualified_number" remarks="在线返修合格数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="plan_start_date" remarks="计划开工日期" type="timestamp"/>
            <column name="plan_end_date" remarks="计划结单日期" type="timestamp"/>
            <column name="actual_start_date" remarks="实际开工日期" type="timestamp"/>
            <column name="actual_end_date" remarks="实际完成日期" type="timestamp"/>
            <column defaultValueNumeric="0" name="is_finish" remarks="是否完成" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="category" remarks="工单类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="status" remarks="工单状态(0:正常态;1:暂停态;2:终止态)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column name="pedigree_id" remarks="产品谱系id(最小层级)" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_flow_id" remarks="流程框图id" type="BIGINT"/>
            <column name="organization_id" remarks="组织架构id" type="BIGINT"/>
            <column name="work_line_id" remarks="生产线id" type="BIGINT"/>
            <column name="bom_info_id" remarks="物料方案id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="version" remarks="乐观锁版本" type="BIGINT"/>
            <column name="client_id" remarks="客户ID" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="generate_sub_ws_status" remarks="子工单生成状态(0:未生成;1:部分生成;2:全部生成)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="statement_reason" remarks="结单原因" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="down_grade_number" remarks="降级总数" type="INT"/>
            <column name="process_instance_id" remarks="流程实例ID" type="VARCHAR(50)"/>
            <column name="sale_order_id" remarks="订单id" type="BIGINT"/>
            <column name="delivery_date" remarks="交付日期" type="date"/>
            <column defaultValueNumeric="0" name="priority" remarks="优先级" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="step_number" type="INT"/>
            <column defaultValueNumeric="0" name="step_comp_number" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-134">
        <createTable remarks="工单退料表" tableName="procedure_work_sheet_material_return">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="operator_id" remarks="操作人" type="BIGINT"/>
            <column name="batch" remarks="批次号" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0.0" name="number" remarks="退料数量" type="DOUBLE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time" remarks="记录日期时间" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="记录日期(方便统计)" type="timestamp"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-135">
        <createTable remarks="工单产量统计表" tableName="procedure_work_sheet_statistics">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT"/>
            <column name="input_number" remarks="投产数" type="INT"/>
            <column name="qualified_number" remarks="合格数" type="INT"/>
            <column name="unqualified_number" remarks="不合格数" type="INT"/>
            <column name="record_date" remarks="记录日期" type="date">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-136">
        <createTable remarks="工单工序生产在制统计表" tableName="procedure_work_sheet_step_statistics">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_step_statistics_id" remarks="产品谱系工序生产在制统计ID" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="input_number" remarks="工序投产数量" type="INT"/>
            <column defaultValueNumeric="0" name="qualified_number" remarks="工序合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="工序不合格数量" type="INT"/>
            <column defaultValueNumeric="0" name="transfer_number" remarks="工序下交数量" type="INT"/>
            <column defaultValueNumeric="0" name="online_number" remarks="工序在线数量" type="INT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-137">
        <createTable remarks="转工艺历史表" tableName="procedure_workflow_convert_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="origin_work_flow_id" remarks="旧工艺路线id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="origin_step_id" remarks="旧工序id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="convert_work_flow_id" remarks="新工艺路线id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="convert_step_id" remarks="新工序id" type="BIGINT"/>
            <column name="record_time" remarks="转工艺时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-138">
        <createTable tableName="procedure_ws_check_material">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="核料凭证号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ws_id" remarks="总工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="status" remarks="核料状态(0:未核料;1:部分核料;2:全部核料)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="check_date" remarks="核料时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="type" remarks="0,正常工单核料;1,在线返修单核料" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-139">
        <createTable tableName="procedure_ws_check_material_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="ws_check_material_id" remarks="工单核料Id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="ws_id" remarks="总工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" remarks="物料Id" type="BIGINT"/>
            <column name="batch" remarks="批次号" type="VARCHAR(50)"/>
            <column name="supplier_id" remarks="供应商Id" type="BIGINT"/>
            <column defaultValueNumeric="0.0" name="checked_number" remarks="已核料数量" type="DOUBLE"/>
            <column name="uncheck_number" remarks="未核料数量" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-140">
        <createTable tableName="procedure_ws_material">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0.0" name="number" remarks="投料数量" type="DOUBLE"/>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="material_id" remarks="当前物料id" type="BIGINT"/>
            <column name="origin_material_id" remarks="原始物料id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-141">
        <createTable tableName="procedure_ws_material_batch">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="ws_id" remarks="总工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="batch" remarks="批次号" type="VARCHAR(50)"/>
            <column name="material_id" remarks="物料Id" type="BIGINT"/>
            <column name="supplier_id" remarks="供应商Id" type="BIGINT"/>
            <column defaultValueNumeric="0.0" name="number" remarks="领料总数" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="left_number" remarks="剩余总数" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-142">
        <createTable tableName="procedure_ws_rework">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="original_work_sheet_id" remarks="原始工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rework_work_sheet_id" remarks="返修工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="unqualified_group_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="org_sub_work_sheet_list" type="TEXT"/>
            <column name="step_id" remarks="产生不良的工序 id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-143">
        <createTable tableName="procedure_ws_step">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="pre_step_id" remarks="前置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="after_step_id" remarks="后置工序列表，分号隔开" type="VARCHAR(255)"/>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="category" remarks="工序类型" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="request_mode" remarks="请求模式" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="control_mode" remarks="管控模式(0:批量;1:单支)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="is_control_material" remarks="是否管控物料(0:不管控;1:管控)" type="BIT(1)"/>
            <column defaultValue="0" name="is_bind_container" remarks="是否绑定容器(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1.0" name="input_rate" remarks="投产比例" type="DOUBLE"/>
            <column name="work_flow_id" remarks="工艺路线Id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-144">
        <createTable tableName="procedure_ws_step_unqualified_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="number" remarks="不良数量" type="INT"/>
            <column name="record_date" remarks="记录日期" type="date"/>
            <column defaultValue="0" name="flag" remarks="标识是否已生成在线返修单0:否;1:是" type="BIT(1)"/>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT"/>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT"/>
            <column name="unqualified_item_id" remarks="不良项目id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="repair_count" remarks="已返修数" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-145">
        <createTable tableName="procedure_ws_step_work_cell">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="step_id" remarks="工序id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="client_id" remarks="客户id" type="BIGINT"/>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-146">
        <createTable tableName="procedure_ws_work_cell_material_batch">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="ws_id" remarks="工单Id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" remarks="物料Id" type="BIGINT"/>
            <column name="batch" remarks="批次" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0.0" name="number" remarks="工位领料总数" type="DOUBLE"/>
            <column defaultValueNumeric="0.0" name="left_number" remarks="工位剩余总数" type="DOUBLE"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-147">
        <createTable remarks="工单工位退料表" tableName="procedure_ws_work_cell_material_return">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单id" type="BIGINT"/>
            <column name="work_cell_id" remarks="工位id" type="BIGINT"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="operator_id" remarks="操作人" type="BIGINT"/>
            <column name="batch" remarks="批次号" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0.0" name="number" remarks="退料数量" type="DOUBLE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time" remarks="记录日期时间" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="记录日期(方便统计)" type="timestamp"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-148">
        <addUniqueConstraint columnNames="work_cell_id, deleted" constraintName="base_area_work_cell_unique" tableName="base_area_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-165">
        <addUniqueConstraint columnNames="step_id, unqualified_item_id, deleted" constraintName="base_online_rework_rule_unique" tableName="base_online_rework_rule"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-166">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_organization_area_unique" tableName="base_organization_area"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-167">
        <addUniqueConstraint columnNames="pedigree_id, deleted" constraintName="base_pedigree_config_unique" tableName="base_pedigree_config"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-171">
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, client_id, unqualified_group_id, deleted" constraintName="base_pedigree_rework_work_flow_unique" tableName="base_pedigree_rework_work_flow"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-172">
        <addUniqueConstraint columnNames="pedigree_step_check_rule_id, check_item_id, deleted" constraintName="base_pedigree_step_check_item_unique" tableName="base_pedigree_step_check_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-173">
        <addUniqueConstraint columnNames="pedigree_id, work_sheet_id, work_sheet_category, step_group_id, step_id, work_flow_id, client_id, deleted" constraintName="base_pedigree_step_check_rule_unique" tableName="base_pedigree_step_check_rule"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-174">
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, pre_step_id, deleted" constraintName="base_pedigree_step_interval_config_unique_index" tableName="base_pedigree_step_interval_config"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-175">
        <addUniqueConstraint columnNames="client_id, pedigree_id, work_flow_id, step_id, material_id, deleted" constraintName="base_pedigree_step_material_rule_unique" tableName="base_pedigree_step_material_rule"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-177">
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, client_id, deleted" constraintName="base_pedigree_step_specification_unique" tableName="base_pedigree_step_specification"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-178">
        <addUniqueConstraint columnNames="client_id, pedigree_id, work_flow_id, step_id, deleted" constraintName="base_pedigree_step_unique" tableName="base_pedigree_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-179">
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, client_id, unqualified_item_id, deleted" constraintName="base_pedigree_step_unqualified_item_unique" tableName="base_pedigree_step_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-182">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_pedigree_unique" tableName="base_pedigree"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-183">
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, client_id, deleted" constraintName="base_pedigree_work_flow_unique" tableName="base_pedigree_work_flow"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-186">
        <addUniqueConstraint columnNames="material_id, material_batch, sn_work_detail_id, deleted" constraintName="base_sn_work_detail_material_batch_unique" tableName="procedure_sn_work_detail_material_batch"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-190">
        <addUniqueConstraint columnNames="work_flow_id, step_id, deleted" constraintName="base_step_config_unique" tableName="base_step_config"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-194">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_step_group_unique" tableName="base_step_group"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-195">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_step_unique" tableName="base_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-196">
        <addUniqueConstraint columnNames="step_id, unqualified_item_id, deleted" constraintName="base_step_unqualified_item_unique" tableName="base_step_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-197">
        <addUniqueConstraint columnNames="pedigree_id, step_id, work_flow_id, work_sheet_id, work_sheet_category, step_group_id, client_id, deleted" constraintName="base_step_warning_standard_unique" tableName="base_step_warning_standard"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-200">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_unqualified_cause_unique" tableName="base_unqualified_cause"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-201">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_unqualified_group_unique" tableName="base_unqualified_group"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-203">
        <addUniqueConstraint columnNames="unqualified_item_id, unqualified_cause_id, deleted" constraintName="base_unqualified_item_cause_unique" tableName="base_unqualified_item_cause"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-204">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_unqualified_item_unique" tableName="base_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-205">
        <addUniqueConstraint columnNames="pedigree_id, work_sheet_id, work_sheet_category, step_group_id, step_id, work_flow_id, client_id, unqualified_item_id, unqualified_group_id, deleted" constraintName="base_unqualified_item_warning_standard_unique" tableName="base_unqualified_item_warning_standard"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-212">
        <addUniqueConstraint columnNames="work_cell_id, work_flow_id, step_id, category, flag, variety_id, deleted" constraintName="base_work_cell_check_start_rule_unique" tableName="base_work_cell_check_start_rule"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-213">
        <addUniqueConstraint columnNames="ip, code, deleted" constraintName="base_work_cell_ip_code_unique_index" tableName="base_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-214">
        <addUniqueConstraint columnNames="ip, deleted" constraintName="base_work_cell_ip_unique_index" tableName="base_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-215">
        <addUniqueConstraint columnNames="staff_id, work_cell_id, deleted" constraintName="base_work_cell_staff_unique" tableName="base_work_cell_staff"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-216">
        <addUniqueConstraint columnNames="work_cell_id, step_id, deleted" constraintName="base_work_cell_step_unique" tableName="base_work_cell_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-217">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_work_cell_unique" tableName="base_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-218">
        <addUniqueConstraint columnNames="client_id, pedigree_id, origin_work_flow_id, step_id, target_work_flow_id, deleted" constraintName="base_work_flow_convert_config_unique" tableName="base_work_flow_convert_config"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-219">
        <addUniqueConstraint columnNames="work_flow_id, step_id, deleted" constraintName="base_work_flow_step_unique" tableName="base_work_flow_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-220">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_work_flow_unique" tableName="base_work_flow"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-221">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_work_line_unique" tableName="base_work_line"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-222">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_work_station_unique" tableName="base_work_station"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-223">
        <addUniqueConstraint columnNames="pedigree_id, reuse_pedigree_id, deleted" constraintName="bbase_pedigree_sn_reuse_config_unique_index" tableName="base_pedigree_sn_reuse_config"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-224">
        <addUniqueConstraint columnNames="code, deleted" constraintName="container_code_deleted_unique" tableName="base_container"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-225">
        <addUniqueConstraint columnNames="container_detail_id, unqualified_item_id, deleted" constraintName="container_detail_unqualified_item_unique" tableName="procedure_container_detail_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-226">
        <addUniqueConstraint columnNames="sn, fqc_check_result_id, deleted" constraintName="fqc_check_result_detail_unique" tableName="procedure_fqc_check_result_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-227">
        <addUniqueConstraint columnNames="code, deleted" constraintName="fqc_check_result_unique" tableName="procedure_fqc_check_result"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-228">
        <addUniqueConstraint columnNames="batch_work_detail_id, facility_id, deleted" constraintName="procedure_batch_work_detail_facility_unique_index" tableName="procedure_batch_work_detail_facility"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-229">
        <addUniqueConstraint columnNames="material_batch, batch_work_detail_id, material_id, deleted" constraintName="procedure_batch_work_detail_material_batch_unique" tableName="procedure_batch_work_detail_material_batch"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-230">
        <addUniqueConstraint columnNames="step_id, sub_work_sheet_id, deleted" constraintName="procedure_batch_work_detail_step_id_sub_work_sheet_id_unique" tableName="procedure_batch_work_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-231">
        <addUniqueConstraint columnNames="step_id, work_sheet_id, deleted" constraintName="procedure_batch_work_detail_step_id_work_sheet_id_unique" tableName="procedure_batch_work_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-234">
        <addUniqueConstraint columnNames="container_detail_id, facility_id, deleted" constraintName="procedure_container_detail_facility_unique_index" tableName="procedure_container_detail_facility"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-236">
        <addUniqueConstraint columnNames="pedigree_id, step_id, deleted" constraintName="procedure_custom_pedigree_step_unique" tableName="procedure_custom_pedigree_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-239">
        <addUniqueConstraint columnNames="work_cell_id, category, variety_id, deleted" constraintName="procedure_latest_check_result_unique" tableName="procedure_latest_check_result"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-240">
        <addUniqueConstraint columnNames="pedigree_id, work_line_id, step_id, deleted" constraintName="procedure_pedigree_step_statistics_unique" tableName="procedure_pedigree_step_statistics"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-241">
        <addUniqueConstraint columnNames="serial_number, deleted" constraintName="procedure_sale_order_unique" tableName="procedure_sale_order"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-242">
        <addUniqueConstraint columnNames="sn_work_detail_id, deleted" constraintName="procedure_sn_unqualified_item_unique_index" tableName="procedure_sn_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-243">
        <addUniqueConstraint columnNames="sn_work_detail_id, facility_id, deleted" constraintName="procedure_sn_work_detail_facility_unique_index" tableName="procedure_sn_work_detail_facility"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-244">
        <addUniqueConstraint columnNames="sn, sub_work_sheet_id, step_id, rework_time, deleted" constraintName="procedure_sn_work_detail_sn_sub_work_sheet_id_step_id_unique" tableName="procedure_sn_work_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-245">
        <addUniqueConstraint columnNames="sn, work_sheet_id, step_id, rework_time, deleted" constraintName="procedure_sn_work_detail_sn_work_sheet_id_step_id_unique" tableName="procedure_sn_work_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-247">
        <addUniqueConstraint columnNames="sn, sub_work_sheet_id, deleted" constraintName="procedure_sn_work_status_sub_ws_unique" tableName="procedure_sn_work_status"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-248">
        <addUniqueConstraint columnNames="sn, work_sheet_id, deleted" constraintName="procedure_sn_work_status_ws_unique" tableName="procedure_sn_work_status"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-249">
        <addUniqueConstraint columnNames="serial_number, deleted" constraintName="procedure_sub_work_sheet_unique" tableName="procedure_sub_work_sheet"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-250">
        <addUniqueConstraint columnNames="sub_work_sheet_id, step_id, unqualified_item_id, deleted" constraintName="procedure_sub_ws_step_unqualified_item_unique" tableName="procedure_ws_step_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-253">
        <addUniqueConstraint columnNames="staff_id, work_cell_id, deleted" constraintName="procedure_work_cell_staff_status_unique" tableName="procedure_work_cell_staff_status"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-254">
        <addUniqueConstraint columnNames="sub_work_sheet_id, step_id, deleted" constraintName="procedure_work_sheet_step_statistics_sub_ws_unique" tableName="procedure_work_sheet_step_statistics"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-255">
        <addUniqueConstraint columnNames="work_sheet_id, step_id, deleted" constraintName="procedure_work_sheet_step_statistics_ws_unique" tableName="procedure_work_sheet_step_statistics"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-256">
        <addUniqueConstraint columnNames="serial_number, deleted" constraintName="procedure_work_sheet_unique" tableName="procedure_work_sheet"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-257">
        <addUniqueConstraint columnNames="code, ws_id, deleted" constraintName="procedure_ws_check_material_unique" tableName="procedure_ws_check_material"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-258">
        <addUniqueConstraint columnNames="ws_id, warehouse_id, material_id, batch, deleted" constraintName="procedure_ws_material_batch_unique_index" tableName="procedure_ws_material_batch"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-259">
        <addUniqueConstraint columnNames="work_sheet_id, material_id, origin_material_id, deleted" constraintName="procedure_ws_material_unique" tableName="procedure_ws_material"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-260">
        <addUniqueConstraint columnNames="original_work_sheet_id, rework_work_sheet_id, deleted" constraintName="procedure_ws_rework_unique" tableName="procedure_ws_rework"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-261">
        <addUniqueConstraint columnNames="sub_work_sheet_id, step_id, deleted" constraintName="procedure_ws_step_sub_work_sheet_step_unique" tableName="procedure_ws_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-262">
        <addUniqueConstraint columnNames="work_sheet_id, step_id, unqualified_item_id, deleted" constraintName="procedure_ws_step_unqualified_item_unique" tableName="procedure_ws_step_unqualified_item"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-263">
        <addUniqueConstraint columnNames="sub_work_sheet_id, step_id, work_cell_id, deleted" constraintName="procedure_ws_step_work_cell_sub_ws_unique" tableName="procedure_ws_step_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-264">
        <addUniqueConstraint columnNames="work_sheet_id, step_id, work_cell_id, deleted" constraintName="procedure_ws_step_work_cell_ws_unique" tableName="procedure_ws_step_work_cell"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-265">
        <addUniqueConstraint columnNames="work_sheet_id, step_id, deleted" constraintName="procedure_ws_step_work_sheet_step_unique" tableName="procedure_ws_step"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-266">
        <addUniqueConstraint columnNames="ws_id, work_cell_id, material_id, batch, deleted" constraintName="procedure_ws_work_cell_material_batch_unique" tableName="procedure_ws_work_cell_material_batch"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-267">
        <addUniqueConstraint columnNames="serial_number, deleted" constraintName="unqualified_event_unique" tableName="procedure_unqualified_event"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-268">
        <addUniqueConstraint columnNames="work_cell_id, step_id, facility_id, deleted" constraintName="work_cell_step_facility_unique" tableName="base_work_cell_step_facility"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-269">
        <createIndex associatedWith="" indexName="base_area_work_cell_area_id_index" tableName="base_area_work_cell">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-270">
        <createIndex associatedWith="" indexName="base_area_work_cell_work_cell_id_index" tableName="base_area_work_cell">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-299">
        <createIndex associatedWith="" indexName="base_online_rework_rule_step_id" tableName="base_online_rework_rule">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-300">
        <createIndex associatedWith="" indexName="base_online_rework_rule_unqualified_item_id" tableName="base_online_rework_rule">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-301">
        <createIndex associatedWith="" indexName="base_organization_area_code_index" tableName="base_organization_area">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-302">
        <createIndex associatedWith="" indexName="base_organization_area_org_id_index" tableName="base_organization_area">
            <column name="organization_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-303">
        <createIndex associatedWith="" indexName="base_pedigree_code" tableName="base_pedigree">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-304">
        <createIndex associatedWith="" indexName="base_pedigree_config_pedigree_id" tableName="base_pedigree_config">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-310">
        <createIndex associatedWith="" indexName="base_pedigree_name" tableName="base_pedigree">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-311">
        <createIndex associatedWith="" indexName="base_pedigree_rework_work_flow_pedigree_id" tableName="base_pedigree_rework_work_flow">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-312">
        <createIndex associatedWith="" indexName="base_pedigree_rework_work_flow_unqualified_group_id" tableName="base_pedigree_rework_work_flow">
            <column name="unqualified_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-313">
        <createIndex associatedWith="" indexName="base_pedigree_rework_work_flow_work_flow_id" tableName="base_pedigree_rework_work_flow">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-314">
        <createIndex associatedWith="" indexName="base_pedigree_sn_reuse_config_pedigree_id_index" tableName="base_pedigree_sn_reuse_config">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-315">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_category_index" tableName="base_pedigree_step_check_rule">
            <column name="category"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-316">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_client_id_index" tableName="base_pedigree_step_check_rule">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-317">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_code_index" tableName="base_pedigree_step_check_rule">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-318">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_pedigree_id_index" tableName="base_pedigree_step_check_rule">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-319">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_priority_element_config_id_index" tableName="base_pedigree_step_check_rule">
            <column name="priority_element_config_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-320">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_step_group_id_index" tableName="base_pedigree_step_check_rule">
            <column name="step_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-321">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_step_id_index" tableName="base_pedigree_step_check_rule">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-322">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_variety_index" tableName="base_pedigree_step_check_rule">
            <column name="variety"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-323">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_work_cell_id_index" tableName="base_pedigree_step_check_rule">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-324">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_work_flow_id_index" tableName="base_pedigree_step_check_rule">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-325">
        <createIndex associatedWith="" indexName="base_pedigree_step_check_rule_work_sheet_id_index" tableName="base_pedigree_step_check_rule">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-326">
        <createIndex associatedWith="" indexName="base_pedigree_step_config_id_index" tableName="base_pedigree_step">
            <column name="priority_element_config_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-327">
        <createIndex associatedWith="" indexName="base_pedigree_step_interval_config_pedigree_id_index" tableName="base_pedigree_step_interval_config">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-328">
        <createIndex associatedWith="" indexName="base_pedigree_step_interval_config_pre_step_id_index" tableName="base_pedigree_step_interval_config">
            <column name="pre_step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-329">
        <createIndex associatedWith="" indexName="base_pedigree_step_interval_config_step_id_index" tableName="base_pedigree_step_interval_config">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-330">
        <createIndex associatedWith="" indexName="base_pedigree_step_interval_config_work_flow_id_index" tableName="base_pedigree_step_interval_config">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-331">
        <createIndex associatedWith="" indexName="base_pedigree_step_material_rule_material_id_index" tableName="base_pedigree_step_material_rule">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-332">
        <createIndex associatedWith="" indexName="base_pedigree_step_material_rule_pedgiree_id_index" tableName="base_pedigree_step_material_rule">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-333">
        <createIndex associatedWith="" indexName="base_pedigree_step_material_rule_step_id_index" tableName="base_pedigree_step_material_rule">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-334">
        <createIndex associatedWith="" indexName="base_pedigree_step_material_rule_work_flow_id_index" tableName="base_pedigree_step_material_rule">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-335">
        <createIndex associatedWith="" indexName="base_pedigree_step_pass_rate_pedigree_index" tableName="base_pedigree_step_pass_rate">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-336">
        <createIndex associatedWith="" indexName="base_pedigree_step_pass_rate_step_group_index" tableName="base_pedigree_step_pass_rate">
            <column name="step_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-337">
        <createIndex associatedWith="" indexName="base_pedigree_step_pedgiree_id_index" tableName="base_pedigree_step">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-342">
        <createIndex associatedWith="" indexName="base_pedigree_step_specification_pedgiree_id_index" tableName="base_pedigree_step_specification">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-343">
        <createIndex associatedWith="" indexName="base_pedigree_step_specification_step_id_index" tableName="base_pedigree_step_specification">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-344">
        <createIndex associatedWith="" indexName="base_pedigree_step_specification_work_flow_id_index" tableName="base_pedigree_step_specification">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-345">
        <createIndex associatedWith="" indexName="base_pedigree_step_step_id_index" tableName="base_pedigree_step">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-346">
        <createIndex associatedWith="" indexName="base_pedigree_step_unqualified_item_config_id_index" tableName="base_pedigree_step_unqualified_item">
            <column name="priority_element_config_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-347">
        <createIndex associatedWith="" indexName="base_pedigree_step_unqualified_item_pedigree_id_index" tableName="base_pedigree_step_unqualified_item">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-348">
        <createIndex associatedWith="" indexName="base_pedigree_step_unqualified_item_unqualified_item_id_index" tableName="base_pedigree_step_unqualified_item">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-349">
        <createIndex associatedWith="" indexName="base_pedigree_step_unqualified_item_work_flow_id_index" tableName="base_pedigree_step_unqualified_item">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-355">
        <createIndex associatedWith="" indexName="base_pedigree_step_work_flow_id_index" tableName="base_pedigree_step">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-357">
        <createIndex associatedWith="" indexName="base_pedigree_work_flow_pedigree_id_index" tableName="base_pedigree_work_flow">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-358">
        <createIndex associatedWith="" indexName="base_pedigree_work_flow_work_flow_id_index" tableName="base_pedigree_work_flow">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-363">
        <createIndex associatedWith="" indexName="base_sn_work_detail_material_batch_material_batch_index" tableName="procedure_sn_work_detail_material_batch">
            <column name="material_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-364">
        <createIndex associatedWith="" indexName="base_sn_work_detail_material_batch_material_index" tableName="procedure_sn_work_detail_material_batch">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-365">
        <createIndex associatedWith="" indexName="base_sn_work_detail_material_batch_sn_work_detail_id_index" tableName="procedure_sn_work_detail_material_batch">
            <column name="sn_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-370">
        <createIndex associatedWith="" indexName="base_step_code_index" tableName="base_step">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-371">
        <createIndex associatedWith="" indexName="base_step_config_step_id_index" tableName="base_step_config">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-372">
        <createIndex associatedWith="" indexName="base_step_config_work_flow_id_index" tableName="base_step_config">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-374">
        <createIndex associatedWith="" indexName="base_step_group_code_index" tableName="base_step_group">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-375">
        <createIndex associatedWith="" indexName="base_step_group_name_index" tableName="base_step_group">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-376">
        <createIndex associatedWith="" indexName="base_step_name_index" tableName="base_step">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-377">
        <createIndex associatedWith="" indexName="base_step_unqualified_item_step_id_index" tableName="base_step_unqualified_item">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-378">
        <createIndex associatedWith="" indexName="base_step_unqualified_item_unqualified_item_id_index" tableName="base_step_unqualified_item">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-379">
        <createIndex associatedWith="" indexName="base_step_waring_standard_pedigree_id_index" tableName="base_step_warning_standard">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-380">
        <createIndex associatedWith="" indexName="base_step_waring_standard_step_id_index" tableName="base_step_warning_standard">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-381">
        <createIndex associatedWith="" indexName="base_step_warning_standard_priority_element_config_id_index" tableName="base_step_warning_standard">
            <column name="priority_element_config_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-382">
        <createIndex associatedWith="" indexName="base_step_warning_standard_work_flow_id_index" tableName="base_step_warning_standard">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-388">
        <createIndex associatedWith="" indexName="base_ui_waring_standard_pedigree_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-389">
        <createIndex associatedWith="" indexName="base_ui_waring_standard_step_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-390">
        <createIndex associatedWith="" indexName="base_unqualified_cause_code_index" tableName="base_unqualified_cause">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-391">
        <createIndex associatedWith="" indexName="base_unqualified_cause_name_index" tableName="base_unqualified_cause">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-392">
        <createIndex associatedWith="" indexName="base_unqualified_group_code_index" tableName="base_unqualified_group">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-393">
        <createIndex associatedWith="" indexName="base_unqualified_group_name_index" tableName="base_unqualified_group">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-394">
        <createIndex associatedWith="" indexName="base_unqualified_item_cause_unqualified_item_id_index" tableName="base_unqualified_item_cause">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-395">
        <createIndex associatedWith="" indexName="base_unqualified_item_code_index" tableName="base_unqualified_item">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-396">
        <createIndex associatedWith="" indexName="base_unqualified_item_name_index" tableName="base_unqualified_item">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-399">
        <createIndex associatedWith="" indexName="base_warning_standard_client_id_index" tableName="base_step_warning_standard">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-400">
        <createIndex associatedWith="" indexName="base_warning_standard_client_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-401">
        <createIndex associatedWith="" indexName="base_warning_standard_step_group_id_index" tableName="base_step_warning_standard">
            <column name="step_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-402">
        <createIndex associatedWith="" indexName="base_warning_standard_step_group_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="step_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-403">
        <createIndex associatedWith="" indexName="base_warning_standard_unqualified_group_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="unqualified_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-404">
        <createIndex associatedWith="" indexName="base_warning_standard_unqualified_item_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-405">
        <createIndex associatedWith="" indexName="base_warning_standard_work_flow_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-406">
        <createIndex associatedWith="" indexName="base_warning_standard_work_sheet_id_index" tableName="base_step_warning_standard">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-407">
        <createIndex associatedWith="" indexName="base_warning_standard_work_sheet_id_index" tableName="base_unqualified_item_warning_standard">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-411">
        <createIndex associatedWith="" indexName="base_work_cell_check_start_rule_category_index" tableName="base_work_cell_check_start_rule">
            <column name="category"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-412">
        <createIndex associatedWith="" indexName="base_work_cell_check_start_rule_variety_index" tableName="base_work_cell_check_start_rule">
            <column name="variety"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-413">
        <createIndex associatedWith="" indexName="base_work_cell_check_start_rule_work_cell_id_index" tableName="base_work_cell_check_start_rule">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-414">
        <createIndex associatedWith="" indexName="base_work_cell_code_index" tableName="base_work_cell">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-415">
        <createIndex associatedWith="" indexName="base_work_cell_ip_index" tableName="base_work_cell">
            <column name="ip"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-416">
        <createIndex associatedWith="" indexName="base_work_cell_name_index" tableName="base_work_cell">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-417">
        <createIndex associatedWith="" indexName="base_work_cell_organization_id_index" tableName="base_work_cell">
            <column name="organization_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-418">
        <createIndex associatedWith="" indexName="base_work_cell_staff_staff_id_index" tableName="base_work_cell_staff">
            <column name="staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-419">
        <createIndex associatedWith="" indexName="base_work_cell_step_facility_facility_id_index" tableName="base_work_cell_step_facility">
            <column name="facility_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-420">
        <createIndex associatedWith="" indexName="base_work_cell_step_facility_step_id_index" tableName="base_work_cell_step_facility">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-421">
        <createIndex associatedWith="" indexName="base_work_cell_step_facility_work_cell_id_index" tableName="base_work_cell_step_facility">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-422">
        <createIndex associatedWith="" indexName="base_work_cell_step_work_cell_id_index" tableName="base_work_cell_step">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-423">
        <createIndex associatedWith="" indexName="base_work_flow_code_index" tableName="base_work_flow">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-424">
        <createIndex associatedWith="" indexName="base_work_flow_convert_config_client_id_index" tableName="base_work_flow_convert_config">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-425">
        <createIndex associatedWith="" indexName="base_work_flow_convert_config_origin_work_flow_id_index" tableName="base_work_flow_convert_config">
            <column name="origin_work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-426">
        <createIndex associatedWith="" indexName="base_work_flow_convert_config_pedigree_id_index" tableName="base_work_flow_convert_config">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-427">
        <createIndex associatedWith="" indexName="base_work_flow_convert_config_step_id_index" tableName="base_work_flow_convert_config">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-428">
        <createIndex associatedWith="" indexName="base_work_flow_convert_config_target_work_flow_id_index" tableName="base_work_flow_convert_config">
            <column name="target_work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-429">
        <createIndex associatedWith="" indexName="base_work_flow_name_index" tableName="base_work_flow">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-430">
        <createIndex associatedWith="" indexName="base_work_flow_step_step_id_index" tableName="base_work_flow_step">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-431">
        <createIndex associatedWith="" indexName="base_work_flow_step_work_flow_id_index" tableName="base_work_flow_step">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-432">
        <createIndex associatedWith="" indexName="base_work_line_code_index" tableName="base_work_line">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-433">
        <createIndex associatedWith="" indexName="base_work_line_name_index" tableName="base_work_line">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-434">
        <createIndex associatedWith="" indexName="base_work_line_organization_id_index" tableName="base_work_line">
            <column name="organization_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-435">
        <createIndex associatedWith="" indexName="base_work_station_code_index" tableName="base_work_station">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-436">
        <createIndex associatedWith="" indexName="base_work_station_name_index" tableName="base_work_station">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-437">
        <createIndex associatedWith="" indexName="base_work_station_work_line_id_index" tableName="base_work_station">
            <column name="work_line_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-441">
        <createIndex associatedWith="" indexName="container_code_index" tableName="base_container">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-442">
        <createIndex associatedWith="" indexName="container_detail_material_batch_batch_index" tableName="procedure_container_detail_material_batch">
            <column name="batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-443">
        <createIndex associatedWith="" indexName="container_detail_material_batch_container_detail_id_index" tableName="procedure_container_detail_material_batch">
            <column name="container_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-444">
        <createIndex associatedWith="" indexName="container_detail_material_batch_material_id_index" tableName="procedure_container_detail_material_batch">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-445">
        <createIndex associatedWith="" indexName="container_detail_material_batch_supplier_id_index" tableName="procedure_container_detail_material_batch">
            <column name="supplier_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-446">
        <createIndex associatedWith="" indexName="container_detail_unqualified_item_container_detail_id_index" tableName="procedure_container_detail_unqualified_item">
            <column name="container_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-447">
        <createIndex associatedWith="" indexName="detail_check_item_id_index" tableName="procedure_inspect_unqualified_detail">
            <column name="check_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-448">
        <createIndex associatedWith="" indexName="detail_defect_id_index" tableName="procedure_inspect_unqualified_detail">
            <column name="defect_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-449">
        <createIndex associatedWith="" indexName="detail_inspect_unqualified_id_index" tableName="procedure_inspect_unqualified_detail">
            <column name="inspect_unqualified_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-450">
        <createIndex associatedWith="" indexName="fqc_check_result_check_staff_id_index" tableName="procedure_fqc_check_result">
            <column name="check_staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-451">
        <createIndex associatedWith="" indexName="fqc_check_result_created_date_index" tableName="procedure_fqc_check_result">
            <column name="created_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-452">
        <createIndex associatedWith="" indexName="fqc_check_result_deal_staff_id_index" tableName="procedure_fqc_check_result">
            <column name="deal_staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-453">
        <createIndex associatedWith="" indexName="fqc_check_result_detail_fqc_check_result_id_index" tableName="procedure_fqc_check_result_detail">
            <column name="fqc_check_result_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-454">
        <createIndex associatedWith="" indexName="fqc_check_result_detail_result_index" tableName="procedure_fqc_check_result_detail">
            <column name="result"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-455">
        <createIndex associatedWith="" indexName="fqc_check_result_detail_sn_index" tableName="procedure_fqc_check_result_detail">
            <column name="sn"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-456">
        <createIndex associatedWith="" indexName="fqc_check_result_original_sub_ws_id_index" tableName="procedure_fqc_check_result">
            <column name="original_sub_ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-457">
        <createIndex associatedWith="" indexName="fqc_check_result_status_index" tableName="procedure_fqc_check_result">
            <column name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-458">
        <createIndex associatedWith="" indexName="fqc_check_result_sub_ws_id_index" tableName="procedure_fqc_check_result">
            <column name="sub_ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-459">
        <createIndex associatedWith="" indexName="organization_id_work_station_index" tableName="base_work_cell">
            <column name="work_station_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-460">
        <createIndex associatedWith="" indexName="pedigree_step_check_item_check_item_id_index" tableName="base_pedigree_step_check_item">
            <column name="check_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-461">
        <createIndex associatedWith="" indexName="pedigree_step_check_item_rule_id_index" tableName="base_pedigree_step_check_item">
            <column name="pedigree_step_check_rule_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-464">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_facility_batch_work_detail_id_index" tableName="procedure_batch_work_detail_facility">
            <column name="batch_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-465">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_facility_facility_id_index" tableName="procedure_batch_work_detail_facility">
            <column name="facility_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-466">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_step_id_index" tableName="procedure_batch_work_detail">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-467">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_sub_work_sheet_id_index" tableName="procedure_batch_work_detail">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-468">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_work_cell_index" tableName="procedure_batch_work_detail">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-469">
        <createIndex associatedWith="" indexName="procedure_batch_work_detail_work_sheet_id_index" tableName="procedure_batch_work_detail">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-477">
        <createIndex associatedWith="" indexName="procedure_check_history_category_index" tableName="procedure_check_history">
            <column name="category"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-478">
        <createIndex associatedWith="" indexName="procedure_check_history_check_item_id_index" tableName="procedure_check_history_detail">
            <column name="check_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-479">
        <createIndex associatedWith="" indexName="procedure_check_history_history_id_index" tableName="procedure_check_history_detail">
            <column name="history_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-480">
        <createIndex associatedWith="" indexName="procedure_check_history_item_check_history_id_index" tableName="procedure_check_history_item_snapshot">
            <column name="check_history_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-481">
        <createIndex associatedWith="" indexName="procedure_check_history_item_check_item_id_index" tableName="procedure_check_history_item_snapshot">
            <column name="check_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-482">
        <createIndex associatedWith="" indexName="procedure_check_history_item_variety_id_index" tableName="procedure_check_history_item_snapshot">
            <column name="variety_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-483">
        <createIndex associatedWith="" indexName="procedure_check_history_operator_id_index" tableName="procedure_check_history">
            <column name="operator_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-484">
        <createIndex associatedWith="" indexName="procedure_check_history_sn_index" tableName="procedure_check_history_detail">
            <column name="sn"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-485">
        <createIndex associatedWith="" indexName="procedure_check_history_step_id_index" tableName="procedure_check_history">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-486">
        <createIndex associatedWith="" indexName="procedure_check_history_sub_work_sheet_id_index" tableName="procedure_check_history">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-487">
        <createIndex associatedWith="" indexName="procedure_check_history_variety_index" tableName="procedure_check_history">
            <column name="variety"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-488">
        <createIndex associatedWith="" indexName="procedure_check_history_work_cell_id_index" tableName="procedure_check_history">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-489">
        <createIndex associatedWith="" indexName="procedure_container_detail_batch_work_detail_index" tableName="procedure_container_detail">
            <column name="batch_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-490">
        <createIndex associatedWith="" indexName="procedure_container_detail_container_code_index" tableName="procedure_container_detail">
            <column name="container_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-491">
        <createIndex associatedWith="" indexName="procedure_container_detail_container_id_index" tableName="procedure_container_detail">
            <column name="container_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-492">
        <createIndex associatedWith="" indexName="procedure_container_detail_facility_container_detail_id_index" tableName="procedure_container_detail_facility">
            <column name="container_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-493">
        <createIndex associatedWith="" indexName="procedure_container_detail_facility_facility_id_index" tableName="procedure_container_detail_facility">
            <column name="facility_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-494">
        <createIndex associatedWith="" indexName="procedure_custom_pedigree_step_pedigree_id_index" tableName="procedure_custom_pedigree_step">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-495">
        <createIndex associatedWith="" indexName="procedure_custom_pedigree_step_step_id_index" tableName="procedure_custom_pedigree_step">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-506">
        <createIndex associatedWith="" indexName="procedure_inspect_task_container_code_index" tableName="procedure_inspect_task">
            <column name="container_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-507">
        <createIndex associatedWith="" indexName="procedure_inspect_task_step_id_index" tableName="procedure_inspect_task">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-508">
        <createIndex associatedWith="" indexName="procedure_inspect_task_sub_work_sheet_id_index" tableName="procedure_inspect_task">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-509">
        <createIndex associatedWith="" indexName="procedure_inspect_task_variety_id_index" tableName="procedure_inspect_task">
            <column name="variety_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-510">
        <createIndex associatedWith="" indexName="procedure_inspect_task_work_sheet_id_id_index" tableName="procedure_inspect_task">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-511">
        <createIndex associatedWith="" indexName="procedure_inspect_unqualified_check_history_id_index" tableName="procedure_inspect_unqualified">
            <column name="check_history_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-512">
        <createIndex associatedWith="" indexName="procedure_latest_check_result_category_index" tableName="procedure_latest_check_result">
            <column name="category"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-513">
        <createIndex associatedWith="" indexName="procedure_latest_check_result_sub_work_sheet_id_index" tableName="procedure_latest_check_result">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-514">
        <createIndex associatedWith="" indexName="procedure_latest_check_result_variety_index" tableName="procedure_latest_check_result">
            <column name="variety"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-515">
        <createIndex associatedWith="" indexName="procedure_latest_check_result_work_cell_id_index" tableName="procedure_latest_check_result">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-539">
        <createIndex associatedWith="" indexName="procedure_pedigree_step_statistics_pedigree_index" tableName="procedure_pedigree_step_statistics">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-540">
        <createIndex associatedWith="" indexName="procedure_pedigree_step_statistics_step_index" tableName="procedure_pedigree_step_statistics">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-541">
        <createIndex associatedWith="" indexName="procedure_pedigree_step_statistics_work_line_index" tableName="procedure_pedigree_step_statistics">
            <column name="work_line_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-542">
        <createIndex associatedWith="" indexName="procedure_production_plan_organization_id_index" tableName="procedure_production_plan">
            <column name="organization_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-543">
        <createIndex associatedWith="" indexName="procedure_production_plan_pedigree_id_index" tableName="procedure_production_plan">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-544">
        <createIndex associatedWith="" indexName="procedure_production_plan_plan_date_index" tableName="procedure_production_plan">
            <column name="plan_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-545">
        <createIndex associatedWith="" indexName="procedure_production_plan_step_group_id_index" tableName="procedure_production_plan">
            <column name="step_group_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-546">
        <createIndex associatedWith="" indexName="procedure_production_plan_work_line_id_index" tableName="procedure_production_plan">
            <column name="work_line_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-547">
        <createIndex associatedWith="" indexName="procedure_roll_back_history_operator_id_index" tableName="procedure_roll_back_history">
            <column name="operator_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-548">
        <createIndex associatedWith="" indexName="procedure_roll_back_history_step_id_index" tableName="procedure_roll_back_history">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-549">
        <createIndex associatedWith="" indexName="procedure_roll_back_history_sub_work_sheet_id_index" tableName="procedure_roll_back_history">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-550">
        <createIndex associatedWith="" indexName="procedure_roll_back_history_work_sheet_id_index" tableName="procedure_roll_back_history">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-551">
        <createIndex associatedWith="" indexName="procedure_sale_order_client_id_index" tableName="procedure_sale_order">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-552">
        <createIndex associatedWith="" indexName="procedure_sale_order_delivery_date_index" tableName="procedure_sale_order">
            <column name="delivery_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-553">
        <createIndex associatedWith="" indexName="procedure_sale_order_pedigree_id_index" tableName="procedure_sale_order">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-554">
        <createIndex associatedWith="" indexName="procedure_sn_rework_container_id_index" tableName="procedure_sn_rework">
            <column name="container_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-555">
        <createIndex associatedWith="" indexName="procedure_sn_rework_rework_ws_id_index" tableName="procedure_sn_rework">
            <column name="rework_ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-556">
        <createIndex associatedWith="" indexName="procedure_sn_rework_sn_work_status_id_index" tableName="procedure_sn_rework">
            <column name="sn_work_status_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-557">
        <createIndex associatedWith="" indexName="procedure_sn_unqualified_item_sn_index" tableName="procedure_sn_unqualified_item">
            <column name="sn"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-558">
        <createIndex associatedWith="" indexName="procedure_sn_unqualified_item_unqualified_item_id_index" tableName="procedure_sn_unqualified_item">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-561">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_container_detail_id_index" tableName="procedure_sn_work_detail">
            <column name="container_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-562">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_facility_facility_id_index" tableName="procedure_sn_work_detail_facility">
            <column name="facility_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-563">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_facility_sn_work_detail_id_index" tableName="procedure_sn_work_detail_facility">
            <column name="sn_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-564">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_sn_index" tableName="procedure_sn_work_detail">
            <column name="sn"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-565">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_step_id_index" tableName="procedure_sn_work_detail">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-566">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_sub_work_sheet_id_index" tableName="procedure_sn_work_detail">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-567">
        <createIndex associatedWith="" indexName="procedure_sn_work_detail_work_sheet_id_index" tableName="procedure_sn_work_detail">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-568">
        <createIndex associatedWith="" indexName="procedure_sn_work_status_lastest_unqualified_item_id_index" tableName="procedure_sn_work_status">
            <column name="latest_unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-569">
        <createIndex associatedWith="" indexName="procedure_sn_work_status_sn_index" tableName="procedure_sn_work_status">
            <column name="sn"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-570">
        <createIndex associatedWith="" indexName="procedure_sn_work_status_status_index" tableName="procedure_sn_work_status">
            <column defaultValueNumeric="0" name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-571">
        <createIndex associatedWith="" indexName="procedure_sn_work_status_sub_work_sheet_id_index" tableName="procedure_sn_work_status">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-572">
        <createIndex associatedWith="" indexName="procedure_sn_work_status_work_sheet_id_index" tableName="procedure_sn_work_status">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-573">
        <createIndex associatedWith="" indexName="procedure_staff_perform_batch_work_detail_id_index" tableName="procedure_staff_perform">
            <column name="batch_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-574">
        <createIndex associatedWith="" indexName="procedure_staff_perform_container_detail_id_index" tableName="procedure_staff_perform">
            <column name="container_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-575">
        <createIndex associatedWith="" indexName="procedure_staff_perform_record_date_index" tableName="procedure_staff_perform">
            <column name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-576">
        <createIndex associatedWith="" indexName="procedure_staff_perform_record_time_index" tableName="procedure_staff_perform">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-577">
        <createIndex associatedWith="" indexName="procedure_staff_perform_sn_work_detail_id_index" tableName="procedure_staff_perform">
            <column name="sn_work_detail_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-578">
        <createIndex associatedWith="" indexName="procedure_staff_perform_staff_id_index" tableName="procedure_staff_perform">
            <column name="staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-579">
        <createIndex associatedWith="" indexName="procedure_staff_perform_step_id_index" tableName="procedure_staff_perform">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-580">
        <createIndex associatedWith="" indexName="procedure_staff_perform_sub_work_sheet_id_index" tableName="procedure_staff_perform">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-581">
        <createIndex associatedWith="" indexName="procedure_staff_perform_unqualified__item_id_index" tableName="procedure_staff_perform_unqualified_item">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-582">
        <createIndex associatedWith="" indexName="procedure_staff_perform_unqualified_item_perform_id_index" tableName="procedure_staff_perform_unqualified_item">
            <column name="staff_perform_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-583">
        <createIndex associatedWith="" indexName="procedure_staff_perform_unqualified_item_record_date_index" tableName="procedure_staff_perform_unqualified_item">
            <column name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-584">
        <createIndex associatedWith="" indexName="procedure_staff_perform_unqualified_item_record_time_index" tableName="procedure_staff_perform_unqualified_item">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-585">
        <createIndex associatedWith="" indexName="procedure_staff_perform_work_cell_id_index" tableName="procedure_staff_perform">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-586">
        <createIndex associatedWith="" indexName="procedure_staff_perform_work_sheet_id_index" tableName="procedure_staff_perform">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-593">
        <createIndex associatedWith="" indexName="procedure_sub_work_sheet_gd_no" tableName="procedure_sub_work_sheet">
            <column name="serial_number"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-594">
        <createIndex associatedWith="" indexName="procedure_sub_work_sheet_is_finish" tableName="procedure_sub_work_sheet">
            <column defaultValueNumeric="0" name="is_finish"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-595">
        <createIndex associatedWith="" indexName="procedure_sub_work_sheet_work_line_id" tableName="procedure_sub_work_sheet">
            <column name="work_line_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-596">
        <createIndex associatedWith="" indexName="procedure_sub_work_sheet_work_sheet_id" tableName="procedure_sub_work_sheet">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-597">
        <createIndex associatedWith="" indexName="procedure_ue_ui_id_index" tableName="procedure_unqualified_event">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-630">
        <createIndex associatedWith="" indexName="procedure_work_cell_staff_status_staff_index" tableName="procedure_work_cell_staff_status">
            <column name="staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-631">
        <createIndex associatedWith="" indexName="procedure_work_cell_staff_status_work_cell_index" tableName="procedure_work_cell_staff_status">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-632">
        <createIndex associatedWith="" indexName="procedure_work_sheet_generate_sub_ws_status_index" tableName="procedure_work_sheet">
            <column defaultValueNumeric="0" name="generate_sub_ws_status"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-633">
        <createIndex associatedWith="" indexName="procedure_work_sheet_is_finish" tableName="procedure_work_sheet">
            <column defaultValueNumeric="0" name="is_finish"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-634">
        <createIndex associatedWith="" indexName="procedure_work_sheet_material_return_material_id_index" tableName="procedure_work_sheet_material_return">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-635">
        <createIndex associatedWith="" indexName="procedure_work_sheet_material_return_operator_id_index" tableName="procedure_work_sheet_material_return">
            <column name="operator_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-636">
        <createIndex associatedWith="" indexName="procedure_work_sheet_material_return_record_date_index" tableName="procedure_work_sheet_material_return">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-637">
        <createIndex associatedWith="" indexName="procedure_work_sheet_material_return_work_sheet_id_index" tableName="procedure_work_sheet_material_return">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-638">
        <createIndex associatedWith="" indexName="procedure_work_sheet_organization_id" tableName="procedure_work_sheet">
            <column name="organization_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-639">
        <createIndex associatedWith="" indexName="procedure_work_sheet_pedigree_id" tableName="procedure_work_sheet">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-640">
        <createIndex associatedWith="" indexName="procedure_work_sheet_sale_order_id_index" tableName="procedure_work_sheet">
            <column name="sale_order_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-641">
        <createIndex associatedWith="" indexName="procedure_work_sheet_statistics_record_date_index" tableName="procedure_work_sheet_statistics">
            <column name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-642">
        <createIndex associatedWith="" indexName="procedure_work_sheet_statistics_work_sheet_index" tableName="procedure_work_sheet_statistics">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-643">
        <createIndex associatedWith="" indexName="procedure_work_sheet_step_statistics_pss_index" tableName="procedure_work_sheet_step_statistics">
            <column name="pedigree_step_statistics_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-644">
        <createIndex associatedWith="" indexName="procedure_work_sheet_step_statistics_step_index" tableName="procedure_work_sheet_step_statistics">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-645">
        <createIndex associatedWith="" indexName="procedure_work_sheet_step_statistics_sub_ws_index" tableName="procedure_work_sheet_step_statistics">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-646">
        <createIndex associatedWith="" indexName="procedure_work_sheet_step_statistics_ws_index" tableName="procedure_work_sheet_step_statistics">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-647">
        <createIndex associatedWith="" indexName="procedure_work_sheet_work_line_id" tableName="procedure_work_sheet">
            <column name="work_line_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-648">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_convert_step_id_index" tableName="procedure_workflow_convert_history">
            <column name="convert_step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-649">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_convert_work_flow_id_index" tableName="procedure_workflow_convert_history">
            <column name="convert_work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-650">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_origin_step_id_index" tableName="procedure_workflow_convert_history">
            <column name="origin_step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-651">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_origin_work_flow_id_index" tableName="procedure_workflow_convert_history">
            <column name="origin_work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-652">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_sub_work_sheet_id_index" tableName="procedure_workflow_convert_history">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-653">
        <createIndex associatedWith="" indexName="procedure_workflow_convert_history_work_sheet_id_index" tableName="procedure_workflow_convert_history">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-654">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_code_index" tableName="procedure_ws_check_material">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-655">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_detail_material_id_index" tableName="procedure_ws_check_material_detail">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-656">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_detail_ws_check_material_id_index" tableName="procedure_ws_check_material_detail">
            <column name="ws_check_material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-657">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_detail_ws_id_index" tableName="procedure_ws_check_material_detail">
            <column name="ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-658">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_status_index" tableName="procedure_ws_check_material">
            <column defaultValueNumeric="0" name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-659">
        <createIndex associatedWith="" indexName="procedure_ws_check_material_ws_id_index" tableName="procedure_ws_check_material">
            <column name="ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-660">
        <createIndex associatedWith="" indexName="procedure_ws_material_batch_batch_index" tableName="procedure_ws_material_batch">
            <column name="batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-661">
        <createIndex associatedWith="" indexName="procedure_ws_material_batch_material_id_indx" tableName="procedure_ws_material_batch">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-662">
        <createIndex associatedWith="" indexName="procedure_ws_material_batch_ws_id_index" tableName="procedure_ws_material_batch">
            <column name="ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-663">
        <createIndex associatedWith="" indexName="procedure_ws_material_material_id" tableName="procedure_ws_material">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-664">
        <createIndex associatedWith="" indexName="procedure_ws_material_origin_material_id" tableName="procedure_ws_material">
            <column name="origin_material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-665">
        <createIndex associatedWith="" indexName="procedure_ws_material_work_sheet_id" tableName="procedure_ws_material">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-666">
        <createIndex associatedWith="" indexName="procedure_ws_rework_original_work_sheet" tableName="procedure_ws_rework">
            <column name="original_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-667">
        <createIndex associatedWith="" indexName="procedure_ws_rework_rework_work_sheet" tableName="procedure_ws_rework">
            <column name="rework_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-668">
        <createIndex associatedWith="" indexName="procedure_ws_step_step" tableName="procedure_ws_step">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-669">
        <createIndex associatedWith="" indexName="procedure_ws_step_step_id" tableName="procedure_ws_step_unqualified_item">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-670">
        <createIndex associatedWith="" indexName="procedure_ws_step_sub_work_sheet" tableName="procedure_ws_step">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-671">
        <createIndex associatedWith="" indexName="procedure_ws_step_unqualified_item_id" tableName="procedure_ws_step_unqualified_item">
            <column name="unqualified_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-672">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_client_index" tableName="procedure_ws_step_work_cell">
            <column name="client_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-673">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_pedigree_index" tableName="procedure_ws_step_work_cell">
            <column name="pedigree_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-674">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_step_index" tableName="procedure_ws_step_work_cell">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-675">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_sub_ws_index" tableName="procedure_ws_step_work_cell">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-676">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_wc_index" tableName="procedure_ws_step_work_cell">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-677">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_cell_ws_index" tableName="procedure_ws_step_work_cell">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-678">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_flow_id_index" tableName="procedure_ws_step">
            <column name="work_flow_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-679">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_sheet" tableName="procedure_ws_step">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-680">
        <createIndex associatedWith="" indexName="procedure_ws_step_work_sheet_id" tableName="procedure_ws_step_unqualified_item">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-681">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_batch_material_id_index" tableName="procedure_ws_work_cell_material_batch">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-682">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_batch_work_cell_id_index" tableName="procedure_ws_work_cell_material_batch">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-683">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_batch_ws_id_index" tableName="procedure_ws_work_cell_material_batch">
            <column name="ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-684">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_return_material_id_index" tableName="procedure_ws_work_cell_material_return">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-685">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_return_operator_id_index" tableName="procedure_ws_work_cell_material_return">
            <column name="operator_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-686">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_return_record_date_index" tableName="procedure_ws_work_cell_material_return">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-687">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_return_work_cell_id_index" tableName="procedure_ws_work_cell_material_return">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-688">
        <createIndex associatedWith="" indexName="procedure_ws_work_cell_material_return_work_sheet_id_index" tableName="procedure_ws_work_cell_material_return">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-689">
        <createIndex associatedWith="" indexName="unqualified_event_deal_staff_id_index" tableName="procedure_unqualified_event">
            <column name="deal_staff_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-690">
        <createIndex associatedWith="" indexName="unqualified_event_deal_time_idnex" tableName="procedure_unqualified_event">
            <column name="deal_time"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-691">
        <createIndex associatedWith="" indexName="unqualified_event_owner_id_index" tableName="procedure_unqualified_event">
            <column name="owner_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-692">
        <createIndex associatedWith="" indexName="unqualified_event_record_time_idnex" tableName="procedure_unqualified_event">
            <column name="record_time"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-693">
        <createIndex associatedWith="" indexName="unqualified_event_status_index" tableName="procedure_unqualified_event">
            <column name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-694">
        <createIndex associatedWith="" indexName="unqualified_event_step_id_index" tableName="procedure_unqualified_event">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1713521050316-695">
        <createIndex associatedWith="" indexName="unqualified_event_sub_ws_id_index" tableName="procedure_unqualified_event">
            <column name="sub_ws_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="202407161404-001" author="YangS">
        <addColumn tableName="procedure_ws_material">
            <column defaultValueBoolean="false" name="back_flush" type="BIT(1)" remarks="是否倒冲物料"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
