<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202505201149" author="ruima">
        <!-- 工序FPP配置表 -->
        <createTable tableName="base_pedigree_step_fpp_config" remarks="工序FPP配置">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_step_id" remarks="产品谱系工序配置ID" type="bigint(20)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="enable" remarks="是否启用" type="bit(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="max_test_times" remarks="最大测试次数" type="tinyint(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="2" name="consecutive_pass_times" remarks="不合格后连续合格多少次算合格"
                    type="tinyint(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>

        <!-- 单支FPP状态表 -->
        <createTable tableName="procedure_sn_step_fpp_status" remarks="单支FPP状态表">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="SN" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="工单Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_flow_id" remarks="工艺路线Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="step_id" remarks="工序Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="test_count" remarks="测试总数" type="tinyint(3)" defaultValueNumeric="1">
                <constraints nullable="true"/>
            </column>
            <column name="rework_time" defaultValueNumeric="0" remarks="返修次数" type="tinyint(3)">
                <constraints nullable="true"/>
            </column>
            <column name="status" remarks="状态(1：测试结果合格,2：测试结果不合格，3：超次数上限不合格)" type="tinyint(2)"
                    defaultValueNumeric="1">
                <constraints nullable="true"/>
            </column>
            <column name="latest_result" type="json" remarks="测试结果列表">
                <constraints nullable="true"/>
            </column>
            <column name="record_date" remarks="更新日期" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <!-- 单支FPP历史记录表 -->
        <createTable tableName="procedure_sn_step_fpp_history" remarks="单支FPP历史记录表">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="SN" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="work_sheet_id" remarks="工单Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_flow_id" remarks="工艺路线Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="step_id" remarks="工序Id" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="test_time" remarks="测试次数" type="tinyint(3)">
                <constraints nullable="true"/>
            </column>
            <column name="rework_time" remarks="返修次数" type="tinyint(3)">
                <constraints nullable="true"/>
            </column>
            <column name="result" defaultValueBoolean="true" remarks="测试结果" type="bit(1)">
                <constraints nullable="true"/>
            </column>
            <column name="record_date" remarks="测试时间" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="test_data" type="json" remarks="测试数据列表">
                <constraints nullable="true"/>
            </column>
            <column name="staff_id" remarks="测试员工ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint tableName="base_pedigree_step_fpp_config" columnNames="pedigree_step_id,deleted"
                             constraintName="base_pedigree_step_fpp_config_unique_index"/>
        <addUniqueConstraint tableName="procedure_sn_step_fpp_status" columnNames="sn,deleted"
                             constraintName="procedure_sn_step_fpp_status_unique_index"/>
        <createIndex tableName="procedure_sn_step_fpp_history" indexName="procedure_sn_step_fpp_history_sn_index">
            <column name="sn"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_history"
                     indexName="procedure_sn_step_fpp_history_work_sheet_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_history"
                     indexName="procedure_sn_step_fpp_history_sub_work_sheet_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_history" indexName="procedure_sn_step_fpp_history_step_id_index">
            <column name="step_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_history" indexName="procedure_sn_step_fpp_history_result_index">
            <column name="result"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_history"
                     indexName="procedure_sn_step_fpp_history_record_date_index">
            <column name="record_date"/>
        </createIndex>

        <createIndex tableName="procedure_sn_step_fpp_status"
                     indexName="procedure_sn_step_fpp_status_work_sheet_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_status"
                     indexName="procedure_sn_step_fpp_status_sub_work_sheet_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_status" indexName="procedure_sn_step_fpp_status_step_id_index">
            <column name="step_id"/>
        </createIndex>
        <createIndex tableName="procedure_sn_step_fpp_status"
                     indexName="procedure_sn_step_fpp_status_record_date_index">
            <column name="record_date"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
