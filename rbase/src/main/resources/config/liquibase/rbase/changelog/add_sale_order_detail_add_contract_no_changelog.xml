<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202505161729" author="rain">

        <addColumn tableName="rbase_procedure_sale_order_detail">
            <column remarks="合同编号" name="contract_no" type="VARCHAR(255)"/>
        </addColumn>

        <sql>
            -- 将销售订单的合同迁移到销售订单详情里面去
            UPDATE rbase_procedure_sale_order_detail sod
                JOIN procedure_sale_order so
            ON sod.sale_order_id = so.id
                SET sod.contract_no = so.contract_no;
        </sql>
    </changeSet>
</databaseChangeLog>