<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

   <changeSet id="202507201920-001" author="YangS">
       <addColumn tableName="procedure_inspect_task">
           <column name="todo_inspected_time" type="datetime" remarks="待检时间"/>
           <column name="work_cell_check_start_rule_id" type="bigint(20)" remarks="质检发起规则id"/>
           <column defaultValueBoolean="true" name="is_extend" type="bit(1)" remarks="是否已宽放，默认已宽放，true"/>
           <column defaultValueNumeric="0" name="extend_time" type="double" remarks="宽放时长"/>
       </addColumn>

       <createIndex tableName="procedure_inspect_task" indexName="inspect_task_status_deleted_index">
           <column name="status"/>
           <column name="deleted"/>
       </createIndex>
   </changeSet>

    <changeSet id="202507201920-002" author="YangS">
        <createTable tableName="procedure_inspect_task_detail" remarks="来料检验历史详情">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="inspect_task_id" type="bigint(20)" remarks="来料检验id">
                <constraints nullable="false"/>
            </column>
            <column name="sn" type="varchar(255)" remarks="sn"/>
            <column name="deleted" type="bigint(20)" defaultValueNumeric="0" remarks="逻辑删除标识"/>
            <column name="created_by" type="varchar(50)" remarks="新建人"/>
            <column name="created_date" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP" remarks="新建时间"/>
            <column name="last_modified_by" type="varchar(50)" remarks="最新修改人"/>
            <column name="last_modified_date" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP" remarks="最新修改时间"/>
            <column name="custom1" type="varchar(255)" remarks="定制字段1"/>
            <column name="custom2" type="varchar(255)" remarks="定制字段2"/>
            <column name="custom3" type="varchar(255)" remarks="定制字段3"/>
            <column name="custom4" type="varchar(255)" remarks="定制字段4"/>
            <column name="custom5" type="varchar(255)" remarks="定制字段5"/>
        </createTable>

        <addUniqueConstraint tableName="procedure_inspect_task_detail" columnNames="inspect_task_id,sn,deleted" constraintName="inspect_task_detail_unique"/>
        <createIndex tableName="procedure_inspect_task_detail" indexName="task_detail_index">
            <column name="inspect_task_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="202507301738-001" author="YangS">
        <addColumn tableName="procedure_check_history">
            <column name="sn" type="varchar(255)" remarks="sn"/>
        </addColumn>

        <createIndex tableName="procedure_check_history" indexName="sn_check_history_index">
            <column name="sn"/>
        </createIndex>

        <addColumn tableName="procedure_inspect_task">
            <column defaultValueNumeric="0" name="number" type="int(11)" remarks="送检数量"/>
            <column name="sn" type="varchar(255)" remarks="sn"/>
            <column name="pedigree_step_check_rule_id" type="bigint(20)" remarks="质检方案Id"/>
        </addColumn>

        <createIndex tableName="procedure_inspect_task" indexName="sn_inspect_task_index">
            <column name="sn"/>
        </createIndex>
        <createIndex tableName="procedure_inspect_task" indexName="check_rule_id_index">
            <column name="pedigree_step_check_rule_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="202508012000-001" author="YangS">
        <addColumn tableName="procedure_check_history">
            <column name="inspect_task_id" type="bigint(20)" remarks="待检任务id"/>
        </addColumn>
    </changeSet>

    <changeSet id="202508041914-001" author="YangS">
        <addColumn tableName="procedure_check_history_detail">
            <column defaultValueBoolean="true" name="display" type="bit(1)" remarks="质检历史中是否展示"/>
        </addColumn>

        <dropNotNullConstraint tableName="procedure_check_history_detail" columnName="check_data" columnDataType="varchar(255)" />
        <dropNotNullConstraint tableName="procedure_check_history_detail" columnName="qualified_range" columnDataType="varchar(255)"/>

        <createIndex tableName="procedure_check_history_detail" indexName="check_history_display_index">
            <column name="history_id"/>
            <column name="display"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
