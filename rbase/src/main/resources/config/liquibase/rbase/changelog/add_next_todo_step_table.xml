<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="zhuhuawu (generated)" id="202504162015">
        <createTable remarks="下个待做工序生产信息表" tableName="procedure_next_todo_step">
            <column name="id" type="BIGINT(20)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="step_id" remarks="工序ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="container_code" remarks="容器号" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
            <column name="sn" remarks="SN号" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
            <column name="number" remarks="数量" type="INT(10)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createIndex tableName="procedure_next_todo_step" indexName="procedure_next_todo_step_step_id_index">
            <column name="step_id"/>
        </createIndex>
        <createIndex tableName="procedure_next_todo_step" indexName="procedure_next_todo_step_sub_ws_id_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_next_todo_step" indexName="procedure_next_todo_step_ws_id_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="procedure_next_todo_step" indexName="procedure_next_todo_step_container_code_index">
            <column name="container_code"/>
        </createIndex>
        <createIndex tableName="procedure_next_todo_step" indexName="procedure_next_todo_step_sn_index">
            <column name="sn"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
