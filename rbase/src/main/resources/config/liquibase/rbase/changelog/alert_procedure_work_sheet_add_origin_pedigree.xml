<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202505111445-002" author="XieMin">
        <!-- 降级型号添加，原始型号原本已经拥有pedigree_id -->
        <addColumn tableName="procedure_work_sheet">
            <column name="origin_pedigree_id" type="BIGINT(20)" remarks="原始产品型号">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="202505141528" author="zorro">
        <addColumn tableName="base_pedigree_step_specification">
            <column name="snapshot_id" type="bigint(20)" remarks="技术指标快照文件ID">
                <constraints nullable="true"/>
            </column>
            <column name="snapshot_path" type="text" remarks="技术指标快照文件路径">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
