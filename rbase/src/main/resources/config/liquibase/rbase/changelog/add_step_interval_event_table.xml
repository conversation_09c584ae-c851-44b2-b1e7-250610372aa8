<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

<changeSet author="zhuhuawu (generated)" id="1713419935352-1">
        <createTable remarks="工序间隔异常记录表" tableName="procedure_step_interval_event">
            <column name="id" type="BIGINT(20)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="owner_id" remarks="责任人ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_cell_id" remarks="责任工位ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="step_id" remarks="责任工序ID" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="pre_step_id" remarks="前置工序ID" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="request_container_id" remarks="请求容器ID" type="BIGINT(20)">
                    <constraints nullable="true"/>
            </column>
            <column name="pre_container_id" remarks="前置容器ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sn" remarks="SN" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time" remarks="发生时间" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="status" remarks="状态(0:待处理；1:放行)" type="TINYINT(3)"/>
            <column name="deal_staff_id" remarks="处理人ID" type="BIGINT(20)">
                <constraints nullable="true"/>
            </column>
            <column name="deal_time" remarks="处理时间" type="timestamp"/>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-2">
        <createIndex indexName="procedure_step_interval_event_batch_index" tableName="procedure_step_interval_event">
            <column name="work_sheet_id"/>
            <column name="step_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-3">
        <createIndex indexName="procedure_step_interval_event_sub_batch_index" tableName="procedure_step_interval_event">
            <column name="sub_work_sheet_id"/>
            <column name="step_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-4">
        <createIndex indexName="procedure_step_interval_event_sub_ws_sn_index" tableName="procedure_step_interval_event">
            <column name="sub_work_sheet_id"/>
            <column name="step_id"/>
            <column name="sn"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-5">
        <createIndex indexName="procedure_step_interval_event_ws_sn_index" tableName="procedure_step_interval_event">
            <column name="work_sheet_id"/>
            <column name="step_id"/>
            <column name="sn"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-6">
        <createIndex indexName="procedure_step_interval_record_time_index" tableName="procedure_step_interval_event">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_time"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-7">
        <createIndex indexName="procedure_step_interval_step_id_index" tableName="procedure_step_interval_event">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-8">
        <createIndex indexName="procedure_step_interval_sub_ws_container_index" tableName="procedure_step_interval_event">
            <column name="sub_work_sheet_id"/>
            <column name="step_id"/>
            <column name="request_container_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-9">
        <createIndex indexName="procedure_step_interval_sub_ws_id_index" tableName="procedure_step_interval_event">
            <column name="sub_work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-10">
        <createIndex indexName="procedure_step_interval_ws_container_index" tableName="procedure_step_interval_event">
            <column name="work_sheet_id"/>
            <column name="step_id"/>
            <column name="request_container_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1713419935352-11">
        <createIndex indexName="procedure_step_interval_ws_id_index" tableName="procedure_step_interval_event">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
