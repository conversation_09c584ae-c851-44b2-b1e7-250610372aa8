<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202406201817-001" author="simon">
        <createTable tableName="procedure_iqc_check_history" remarks="来料检验表">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="serial_number" type="varchar(255)" remarks="检验单号">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="tinyint(4)" defaultValueNumeric="0" remarks="状态(0:待检验;1:已质检)">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" type="bigint(20)" remarks="物料id">
                <constraints nullable="false"/>
            </column>
            <column name="check_rule_id" type="bigint(20)" remarks="质检单id"/>
            <column name="lot" type="varchar(255)" remarks="物料批次"/>
            <column name="number" type="decimal(14,5)" remarks="来料数量"/>
            <column name="client_id" type="bigint(20)" remarks="客户id"/>
            <column name="supplier_id" type="bigint(20)" remarks="供应商id"/>
            <column name="arrival_time" type="timestamp" remarks="来料日期"/>
            <column name="production_time" type="timestamp" remarks="生产日期"/>
            <column name="valid_time" type="timestamp" remarks="有效期"/>
            <column name="operator_id" type="bigint(20)" remarks="检验员id"/>
            <column name="dealer_id" type="bigint(20)" remarks="处理人id"/>
            <column name="check_time" type="timestamp" remarks="检验日期"/>
            <column name="result" type="bit(1)" remarks="检验结果(0:不合格;1:合格)"/>
            <column name="deal_way" type="tinyint(4)" defaultValueNumeric="0" remarks="处理方式"/>
            <column name="note" type="varchar(255)" remarks="描述"/>
            <column name="qualified_number" type="decimal(14,5)" remarks="合格数量"/>
            <column name="unqualified_number" type="decimal(14,5)" remarks="不良数量"/>
            <column name="dtype" remarks="" type="varchar(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
        </createTable>

        <addUniqueConstraint columnNames="serial_number,deleted"
                             constraintName="procedure_iqc_check_history_unique"
                             tableName="procedure_iqc_check_history"/>
    </changeSet>
    <changeSet id="202407021407" author="zorro">
        <createIndex tableName="procedure_iqc_check_history" indexName="procedure_iqc_check_history_material_id_index">
            <column name="material_id"/>
        </createIndex>
        <createIndex tableName="procedure_iqc_check_history" indexName="procedure_iqc_check_history_operator_id_index">
            <column name="operator_id"/>
        </createIndex>
        <createIndex tableName="procedure_iqc_check_history" indexName="procedure_iqc_check_history_check_time_index">
            <column name="check_time"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
