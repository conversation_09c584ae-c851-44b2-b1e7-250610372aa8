<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202408021036" author="zorro">
        <addColumn tableName="procedure_check_history">
            <column name="serial_number" type="varchar(100)" remarks="流水号">
                <constraints nullable="true"/>
            </column>
            <column name="process_mrb_unqualified_item" type="bit(1)" defaultValueBoolean="false" remarks="是否需要根据MRD处理不良项(0:否;1:是)"/>
        </addColumn>
    </changeSet>
    <changeSet id="202408051419" author="zorro">
        <addColumn tableName="procedure_check_history">
            <column name="process_result_info" type="json" remarks="MRB处理结果">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="procedure_check_history">
            <column name="procedure_iqc_check_history" type="json" remarks="MRB处理结果">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="202408060842" author="zorro">
        <addColumn tableName="procedure_check_history">
            <column name="virtual" type="bit(1)" defaultValueBoolean="true" remarks="是否为虚拟SN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropColumn tableName="procedure_check_history" columnName="procedure_iqc_check_history"/>
        <addColumn tableName="procedure_iqc_check_history">
            <column name="process_result_info" type="json" remarks="MRB处理结果">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="202408091348" author="zorro">
        <addColumn tableName="procedure_inspect_task">
            <column name="inspect_parameter_info" type="json" remarks="待检测参数明细信息(MRB指定全检方案时)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="202408091527" author="zorro">
        <renameColumn tableName="procedure_check_history" oldColumnName="virtual" newColumnName="is_virtual" columnDataType="bit(1)" remarks="是否为虚拟SN" />
    </changeSet>
    <changeSet id="202408291719" author="zorro">
        <addColumn tableName="procedure_latest_check_result">
            <column name="serial_number" type="varchar(100)" remarks="单据号">
                <constraints nullable="true"/>
            </column>
            <column name="display" type="bit(1)" defaultValueBoolean="true" remarks="是否显示" />
        </addColumn>
        <createIndex tableName="procedure_latest_check_result" indexName="procedure_latest_check_result_display_index">
            <column name="display"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet id="202409061544" author="zorro">
        <createIndex tableName="procedure_batch_work_detail_material_batch" indexName="procedure_batch_work_detail_material_batch_material_id_index">
            <column name="material_id"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="procedure_batch_work_detail_material_batch" indexName="procedure_batch_work_detail_material_batch_material_batch_index">
            <column name="material_batch"/>
            <column name="deleted"/>
        </createIndex>

        <createIndex tableName="procedure_sn_work_detail_material_batch" indexName="procedure_sn_work_detail_material_batch_material_id__index">
            <column name="material_id"/>
            <column name="deleted"/>
        </createIndex>
        <createIndex tableName="procedure_sn_work_detail_material_batch" indexName="procedure_sn_work_detail_material_batch_material_batch_index">
            <column name="material_batch"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>
    <changeSet id="202409181458" author="zorro">
        <addColumn tableName="procedure_staff_perform">
            <column name="work_hour" type="DOUBLE" remarks="工序耗时" defaultValueNumeric="0.0"/>
        </addColumn>
    </changeSet>
    <changeSet id="202409211006" author="zorro">
        <addColumn tableName="base_pedigree_step_check_item">
            <column name="customize_inspect_number" type="INT" defaultValueNumeric="1" remarks="自定义检验项目的抽检数量"/>
        </addColumn>
    </changeSet>
    <changeSet id="202410241025" author="zorro">
        <addColumn tableName="procedure_ws_step_unqualified_item">
            <column name="operator_id" type="bigint(20)" remarks="最新操作员工ID">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <createIndex tableName="procedure_ws_step_unqualified_item" indexName="procedure_ws_step_unqualified_item_staff_index">
            <column name="operator_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="202410280837" author="zorro">
        <addColumn tableName="base_pedigree_step_check_item">
            <column name="sample_case_id" type="bigint(20)" remarks="抽样方案ID">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="procedure_iqc_check_history">
            <column name="sample_case_id" type="bigint(20)" remarks="抽样方案ID">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="202412102131" author="zorro">
        <dropNotNullConstraint tableName="base_work_cell_step_facility" columnName="step_id" columnDataType="bigint(20)"/>
    </changeSet>
    <changeSet id="202412301426" author="zorro">
        <addColumn tableName="base_work_cell">
            <column name="facility_step_mode" type="tinyint(2)" defaultValueNumeric="0" remarks="请求生产模式(0:系统发起;1:设备发起)"/>
        </addColumn>
    </changeSet>
    <changeSet id="20250106118" author="zorro">
        <addColumn tableName="procedure_inspect_task">
            <column remarks="缓存" type="LONGTEXT" name="cache">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="procedure_iqc_check_history">
            <column remarks="缓存" type="LONGTEXT" name="cache">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
