<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <include file="config/liquibase/rbase/changelog/init_base_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_ws_sn_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_ysn_to_sn_work_detail_and_status.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_custom_index_for_base_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_step_interval_event_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_pedigree_step_interval_config_update_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_work_cell_check_start_rule_iqc_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_pedigree_step_check_rule_iqc_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_iqc_check_history_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_iqc_check_history_detail_table.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/alert_base_pedigree_step_check_item_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_cascade_work_sheet_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_step_reinspect_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_auto_record_column_for_wearing_part_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_serial_number_for_inspect.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_wearing_part_table.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_parallel_step_config_column.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_column_pedigree_stage.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_rbase_base_pedigree_step_inspection_config_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_step_unqualified_desc_column_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_table_request_method.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_next_todo_step_table.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_sale_order_detail_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/alert_procedure_work_sheet_add_origin_pedigree.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_sale_order_detail_add_contract_no_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_inventory_number_to_work_sheet.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_sn_step_fpp_table.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/base_inspection_assignment_config.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_material_return_history_column.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_column_inspect_task.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
