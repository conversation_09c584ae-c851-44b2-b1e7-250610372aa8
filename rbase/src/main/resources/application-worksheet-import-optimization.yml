## 工单导入性能优化配置
#worksheet:
#  import:
#    # 批处理大小（减小以避免事务冲突）
#    batch-size: 5
#    # 最大并行线程数（改为串行处理避免并发冲突）
#    max-parallel-threads: 1
#    # 超时时间（秒）
#    timeout-seconds: 300
#    # 是否启用异步子工单生成
#    enable-async-sub-work-sheet-generation: true
#
## 数据库连接池优化
#spring:
#  datasource:
#    hikari:
#      # 最大连接数（适度增加）
#      maximum-pool-size: 15
#      # 最小空闲连接数
#      minimum-idle: 3
#      # 连接超时时间
#      connection-timeout: 30000
#      # 空闲超时时间
#      idle-timeout: 600000
#      # 最大生命周期
#      max-lifetime: 1800000
#      # 连接测试查询
#      connection-test-query: SELECT 1
#      # 是否自动提交
#      auto-commit: true
#      # 连接池名称
#      pool-name: WorkSheetImportHikariCP
#      # 泄漏检测阈值
#      leak-detection-threshold: 60000
##
### JPA优化配置
##  jpa:
##    properties:
##      hibernate:
##        # 批量插入大小
##        jdbc:
##          batch_size: 50
##        # 启用批量插入
##        order_inserts: true
##        order_updates: true
##        # 二级缓存
##        cache:
##          use_second_level_cache: true
##          use_query_cache: true
##        # 统计信息
##        generate_statistics: false
##        # 格式化SQL
##        format_sql: false
##        # 显示SQL
##        show_sql: false
##
### 日志配置
##logging:
##  level:
##    net.airuima.rbase.service.procedure.aps.WorkSheetService: INFO
##    org.hibernate.SQL: WARN
##    org.hibernate.type.descriptor.sql.BasicBinder: WARN
##    com.zaxxer.hikari: INFO
##
### 线程池监控
##management:
##  endpoints:
##    web:
##      exposure:
##        include: health,info,metrics,threaddump
##  endpoint:
##    health:
##      show-details: always
##  metrics:
##    export:
##      simple:
##        enabled: true
