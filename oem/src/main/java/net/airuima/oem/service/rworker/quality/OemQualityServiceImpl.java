package net.airuima.oem.service.rworker.quality;

import com.google.common.collect.Lists;
import net.airuima.constant.Constants;
import net.airuima.oem.domain.procedure.*;
import net.airuima.oem.dto.OemTodoInspectHistoryDTO;
import net.airuima.oem.dto.rworker.RworkerOemInspectionPlanDTO;
import net.airuima.oem.dto.rworker.RworkerOemInspectionResultDTO;
import net.airuima.oem.proxy.OemQuerySamplingStrategyProxy;
import net.airuima.oem.repository.procedure.*;
import net.airuima.oem.service.procedure.OemInspectService;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerInspectionResultDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStaffRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class OemQualityServiceImpl implements IOemQualityService {


    @Autowired
    private OemInspectService oemInspectService;
    @Autowired
    private OemInspectRepository oemInspectRepository;
    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private OemQuerySamplingStrategyProxy oemQuerySamplingStrategyProxy;
    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private OemReceiptHistoryDetailRepository oemReceiptHistoryDetailRepository;
    @Autowired
    private WorkCellStaffRepository workCellStaffRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private OemInspectDetailRepository oemInspectDetailRepository;
    @Autowired
    private OemInspectWarehouseRepository oemInspectWarehouseRepository;
    @Autowired
    private OemOrderRepository oemOrderRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;


    /**
     * 外协收货待检任务列表
     *
     * @return List<OemTodoInspectHistoryDTO>
     */
    @Override
    public List<OemTodoInspectHistoryDTO> getTodoHistory() {
        return oemInspectService.getTodoHistory();
    }

    /**
     * 外协质检获取质检方案
     *
     * @param rworkerOemInspectionPlanDto 外协信息
     */
    @Override
    public RworkerQualityInspectionPlanDTO qualityInspectionPlan(RworkerOemInspectionPlanDTO rworkerOemInspectionPlanDto) {

        OemInspect oemInspect = oemInspectRepository.findByIdAndDeleted(rworkerOemInspectionPlanDto.getTaskId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("taskNotFound", "未获取到外协待检任务"));
        //外协工单
        OemOrder oemOrder = oemInspect.getOemReceiptHistory().getOemOrder();
        Pedigree pedigree = oemOrder.getWorkSheet().getPedigree();
        Long supplierId = oemOrder.getSupplierId();
        //可能为空
        Step step = oemOrder.getStep();
        List<Long> pedigreeIds = commonService.getAllParent(pedigree);

        List<PedigreeStepCheckRule> pedigreeStepCheckRules = pedigreeStepCheckRuleRepository.findByCategoryAndPedigreeIdInAndStepIdAndSupplierIdAndVarietyIdAndDeleted(
                WorkCellStartCheckEnum.OEM_INSPECTION.getCategory(), pedigreeIds, Objects.nonNull(step) ? step.getId() : null,
                supplierId, rworkerOemInspectionPlanDto.getVarietyId(), Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(pedigreeStepCheckRules)) {
            throw new ResponseException("checkRuleNotFind", "未获取到外协质检方案");
        }
        // 排序
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleListSort = commonService.getPedigreeStepCheckRuleListSort(pedigreeStepCheckRules);
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleListSort.get(pedigreeStepCheckRuleListSort.size() - Constants.INT_ONE);

        RworkerQualityInspectionPlanDTO rworkerQualityInspectionPlanDto = new RworkerQualityInspectionPlanDTO(pedigreeStepCheckRule);

        int inputNumber = oemInspect.getNumber();
        Integer sampleNumber = oemQuerySamplingStrategyProxy.getSampleResult(pedigreeStepCheckRule.getSampleCase(), inputNumber);
        rworkerQualityInspectionPlanDto.setProductWorkSheetId(Objects.nonNull(oemOrder.getOriginalSubWorkSheet()) ? oemOrder.getOriginalSubWorkSheet().getId() : oemOrder.getOriginalWorkSheet().getId())
                .setNumber(inputNumber)
                .getSampleCaseDto()
                .setNumber(Math.min(inputNumber, sampleNumber));

        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            List<RworkerQualityInspectionPlanDTO.CheckItemDTO> checkItems = pedigreeStepCheckItemList.stream().map(pedigreeStepCheckItem -> new RworkerQualityInspectionPlanDTO.CheckItemDTO(pedigreeStepCheckItem, sampleNumber)).collect(Collectors.toList());
            checkItems = checkItems.stream().peek(checkItem -> {
                //添加检测项目关联的文件
                List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(checkItem.getId());
                Optional.ofNullable(documentDTOList).ifPresent(checkItem::setDocumentDtos);
            }).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setCheckItems(checkItems);
        }
        //sn列表
        rworkerQualityInspectionPlanDto.setSnInfoList(
                oemReceiptHistoryDetailRepository.findByOemReceiptHistoryIdAndDeleted(oemInspect.getOemReceiptHistory().getId(), Constants.LONG_ZERO));
        //添加不良项目
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(oemOrder.getOriginalWorkSheet(), oemOrder.getOriginalSubWorkSheet(), step);
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(pedigree, workFlow.getId(), step.getId(), oemOrder.getWorkSheet().getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = unqualifiedItems.stream().map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setUnqualifiedItems(unqualifiedItemDtos);
        }
        //获取可能的质检缓存
        if (Objects.nonNull(rworkerOemInspectionPlanDto.getTaskId())) {
            oemInspectRepository.findByIdAndDeleted(rworkerOemInspectionPlanDto.getTaskId(), Constants.LONG_ZERO).ifPresent(inspectTask -> {
                rworkerQualityInspectionPlanDto.setCache(StringUtils.isNotBlank(inspectTask.getCache()) ? inspectTask.getCache() : null);
            });
        }

        //获取员工对应的 工位
        List<WorkCell> workCells = workCellStaffRepository.findWorkCellByStaffId(rworkerOemInspectionPlanDto.getStaffId());
        if (!ValidateUtils.isValid(workCells)) {
            throw new ResponseException("workCellStaffNotBind", "当前员工未绑定工位禁止质检！");
        }
        rworkerQualityInspectionPlanDto.setWorkCellDtoList(workCells.stream().map(RworkerQualityInspectionPlanDTO.WorkCellDTO::new).toList());
        return rworkerQualityInspectionPlanDto;
    }

    /**
     * 保存外协入库检测记录
     *
     * @param rworkerOemInspectionResultDto 保存外协入库检测记录信息
     */
    @Override
    public void saveInspectionRecord(RworkerOemInspectionResultDTO rworkerOemInspectionResultDto) {

        //外协质检单
        Long taskId = rworkerOemInspectionResultDto.getTaskId();
        OemInspect oemInspect = oemInspectRepository.findByIdAndDeleted(taskId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("taskNotFound", "未获取到外协待检任务"));

        //质检工位
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerOemInspectionResultDto.getWorkCellId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("workCellNotFind", "未获取到质检工位"));

        //获取到外协质检方案
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findByIdAndDeleted(rworkerOemInspectionResultDto.getCheckRuleId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("checkRuleNotFind", "未获取到外协质检方案"));

        //获取质检详情信息
        List<RworkerInspectionResultDTO.SnCheckItemDTO> snCheckItemDtoList = rworkerOemInspectionResultDto.getSnCheckItemDtoList();
        if (!ValidateUtils.isValid(snCheckItemDtoList)) {
            throw new ResponseException("snCheckItemDtoListNotFind", "未获取到质检详情信息");
        }
        //质检过程保存质检单
        processSaveOemInspect(oemInspect, workCell, pedigreeStepCheckRule, rworkerOemInspectionResultDto);

        //处理质检结果
        /**
         *  合格：将不合格的sn变为
         */
        processSaveOemInspectResult(oemInspect, rworkerOemInspectionResultDto);
    }

    /**
     * 外协质检过程保存质检单
     *
     * @param oemInspect                    质检单
     * @param workCell                      质检工位
     * @param pedigreeStepCheckRule         质检方案
     * @param rworkerOemInspectionResultDto 质检详情
     * @return 质检单
     */
    public OemInspect processSaveOemInspect(OemInspect oemInspect, WorkCell workCell, PedigreeStepCheckRule pedigreeStepCheckRule, RworkerOemInspectionResultDTO rworkerOemInspectionResultDto) {
        oemInspect.setStatus(Constants.INT_ONE)
                .setPedigreeStepCheckRule(pedigreeStepCheckRule)
                .setQualifiedNumber(oemInspect.getNumber() - rworkerOemInspectionResultDto.getUnqualifiedNumber())
                .setUnqualifiedNumber(rworkerOemInspectionResultDto.getUnqualifiedNumber())
                .setInspectTime(LocalDateTime.now())
                .setResult(rworkerOemInspectionResultDto.getResult())
                .setWorkCell(workCell)
                .setOperatorId(rworkerOemInspectionResultDto.getOperatorId())
                .setDealWay(Optional.ofNullable(rworkerOemInspectionResultDto.getDealWay()).orElse(Constants.INT_ONE))
                .setCache(null)
                .setDestroyCheck(Boolean.FALSE);
        //TODO: 前端问题需要处理临时处理
        if (0 == oemInspect.getDealWay()){
            oemInspect.setDealWay(Constants.INT_ONE);
        }
        if ((oemInspect.getDealWay() < Constants.INT_ONE ) || (oemInspect.getDealWay() > Constants.INT_SEVEN)) {
            throw new ResponseException("dealWay","处理方式异常");
        }
        oemInspect = oemInspectService.save(oemInspect);
        OemInspect finalOemInspect = oemInspect;
        //质检详情保存
        List<OemInspectDetail> oemInspectDetailList = Lists.newArrayList();
        rworkerOemInspectionResultDto.getSnCheckItemDtoList().forEach(snCheckItemDto -> {
            OemInspectDetail oemInspectDetail = new OemInspectDetail();
            oemInspectDetail.setOemInspect(finalOemInspect);
            //获取不良项目
            UnqualifiedItem unqualifiedItem = ObjectUtils.isEmpty(snCheckItemDto.getUnqualifiedItemId()) ?
                    null : unqualifiedItemRepository.findByIdAndDeleted(snCheckItemDto.getUnqualifiedItemId(), net.airuima.rbase.constant.Constants.LONG_ZERO).orElse(null);
            oemInspectDetail.setSn(snCheckItemDto.getSn()).setUnqualifiedItem(unqualifiedItem);

            //sn检测详情
            List<OemInspectDetailCheckItemJson> checkItemJsonList = Lists.newArrayList();
            snCheckItemDto.getCheckItemResultDtoList().forEach(checkItemResultDto -> {
                OemInspectDetailCheckItemJson oemInspectDetailCheckItemJson = new OemInspectDetailCheckItemJson();
                OemInspectDetailCheckItemJson.CheckItemDTO checkItemDto = new OemInspectDetailCheckItemJson.CheckItemDTO();
                checkItemDto.setId(checkItemResultDto.getId())
                        .setCode(checkItemResultDto.getCode())
                        .setName(checkItemResultDto.getName())
                        .setQualifiedRange(checkItemResultDto.getQualifiedRange());
                oemInspectDetailCheckItemJson.setCheckItem(checkItemDto)
                        .setResult(checkItemResultDto.getResult())
                        .setQualifiedRange(checkItemResultDto.getQualifiedRange())
                        .setCheckData(checkItemResultDto.getCheckData());
                checkItemJsonList.add(oemInspectDetailCheckItemJson);
            });
            oemInspectDetail.setCheckItems(checkItemJsonList);
            oemInspectDetailList.add(oemInspectDetail);
        });
        oemInspectDetailRepository.saveAll(oemInspectDetailList);
        return finalOemInspect;
    }


    /**
     * 检测结果处理
     * 1-合格接收; 不良直接记录不良项目 (如果存在破坏实验，将本次抽检的数量全部开出不良，以不良入库，剩余的合格入库)
     * 2-让步接收; 不管检查不良，全部（合格，不合格）入库（如果有 破坏性实验需要变为不合格入库）作为合格入库
     * 3-判退;    不管检查不良，判退全部退回（不入库），（如果有 破坏性实验需要变为不合格入库）
     * 4-挑选;    不管检查不良，挑选全部入库（如果有 破坏性实验需要变为不合格入库）合格入合格库，不合格退回
     * 5-MRB;  //没有这种处理方式
     * 6-返工；  //合格入库，检测不良退回，（如果有 破坏性实验需要变为不合格入库）
     * 7-报废   //合格入库，检测不良入库，（如果有 破坏性实验需要变为不合格入库）
     *
     * @param oemInspect
     * @param rworkerOemInspectionResultDto
     */
    private void processSaveOemInspectResult(OemInspect oemInspect, RworkerOemInspectionResultDTO rworkerOemInspectionResultDto) {

        //外协入库记录列表
        List<OemInspectWarehouse> oemInspectWarehouseList = Lists.newArrayList();

        //外协质检单绑定的入库sn列表
        List<String> oemReceiptHistoryBindSnList = oemReceiptHistoryDetailRepository.findByOemReceiptHistoryIdAndDeleted(oemInspect.getOemReceiptHistory().getId(), Constants.LONG_ZERO);

        Boolean realSn = ValidateUtils.isValid(oemReceiptHistoryBindSnList);

        //如果存在破坏性实验结果，直接以不良入库
        List<OemInspectWarehouse> destroyOemInspectWarehouseList = processDestroyCheckResult(rworkerOemInspectionResultDto.getDestroyCheckResult(), oemInspect, oemReceiptHistoryBindSnList);

        Integer destroyNumber = ValidateUtils.isValid(destroyOemInspectWarehouseList) ? destroyOemInspectWarehouseList.stream().mapToInt(OemInspectWarehouse::getNumber).sum() : Constants.INT_ZERO;

        //外协质检不合格sn列表
        List<String> oemCheckUnqualifiedSnList = rworkerOemInspectionResultDto.getSnCheckItemDtoList().stream().filter(snCheckItemDto -> Objects.nonNull(snCheckItemDto.getUnqualifiedItemId()))
                .map(RworkerInspectionResultDTO.SnCheckItemDTO::getSn).distinct().collect(Collectors.toList());

        //外协质检sn列表
        List<String> oemCheckSnList = rworkerOemInspectionResultDto.getSnCheckItemDtoList().stream().map(RworkerInspectionResultDTO.SnCheckItemDTO::getSn).distinct().collect(Collectors.toList());

        //验证外协质检sn列表是否属于当前质检单
        validRealSnOemInspectWarehouse(oemReceiptHistoryBindSnList, oemCheckSnList, oemInspect);

        //处理方式：1,合格接收 -> 没有不良 记录质检结果；2：不管检查不良，全部（合格，不合格）入库（如果有 破坏性实验需要变为不合格入库）作为合格入库
        if (oemInspect.getDealWay() == Constants.INT_ONE || oemInspect.getDealWay() == Constants.INT_TWO) {
            // 记录质检入库结果
            if (realSn) {
                //记录合格sn入库
                oemReceiptHistoryBindSnList.forEach(bindingSn -> {
                    buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, bindingSn, null, Constants.INT_ONE));
                });
            } else {
                oemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, null, null, (oemInspect.getNumber() - destroyNumber)));
            }
        }

        //处理方式：3,判退 -> 判退全部开出不良 (直接重新 入库)
        if (oemInspect.getDealWay() == Constants.INT_THREE) {
            //如果没有破坏性实验，则直接返回不入库
            if (!ValidateUtils.isValid(destroyOemInspectWarehouseList)){
                return;
            }
            //不需要处理
        }

        // 6-返工： 合格入库，检测不良退回，（如果有 破坏性实验需要变为不合格入库）
        if (oemInspect.getDealWay() == Constants.INT_SIX) {
            if (realSn) {
                //记录合格sn入库
                oemReceiptHistoryBindSnList.stream().filter(bindingSn -> !oemCheckUnqualifiedSnList.contains(bindingSn)).forEach(bindingSn -> {
                    buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, bindingSn, null, Constants.INT_ONE));
                });
            } else {
                //如果存在破坏性实验则 合格数  =  来料数 - 破坏性实验个数；否则 合格数  =  来料数 - 检测不良
                oemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, null, null, oemInspect.getNumber() - (destroyNumber == Constants.INT_ZERO ? oemCheckUnqualifiedSnList.size() : destroyNumber)));
            }
        }

        //处理方式：7,报废 -> 合格，不合格全部直接入库
        if (oemInspect.getDealWay() == Constants.INT_SEVEN) {
            if (realSn) {
                //记录合格sn入库
                oemReceiptHistoryBindSnList.stream().filter(bindingSn -> !oemCheckUnqualifiedSnList.contains(bindingSn)).forEach(bindingSn -> {
                    buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, bindingSn, null, Constants.INT_ONE));
                });
                //记录不合格入库：存在破坏性实验的 不记录到 oemInspectWarehouseList 列表中因为 已经 以破坏性实验记录了
                rworkerOemInspectionResultDto.getSnCheckItemDtoList().stream().filter(snCheckItemDto -> Objects.nonNull(snCheckItemDto.getUnqualifiedItemId()))
                        .distinct().forEach(unqualifiedSnDto -> {
                            buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, unqualifiedSnDto.getSn(),
                                    unqualifiedItemRepository.findByIdAndDeleted(unqualifiedSnDto.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(null), Constants.INT_ONE));
                        });
            } else {
                //记录合格批量入库个数 来料数量-不合格数
                oemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, null, null, oemInspect.getNumber() - (destroyNumber == Constants.INT_ZERO ? oemCheckUnqualifiedSnList.size() : destroyNumber)));
                //记录不合格入库
                rworkerOemInspectionResultDto.getSnCheckItemDtoList().stream().filter(snCheckItemDto -> Objects.nonNull(snCheckItemDto.getUnqualifiedItemId()))
                        .distinct().collect(Collectors.groupingBy(RworkerInspectionResultDTO.SnCheckItemDTO::getUnqualifiedItemId)).forEach((unqualifiedItemId, unqualifiedItemDtos) -> {
                            buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, null,
                                    unqualifiedItemRepository.findByIdAndDeleted(unqualifiedItemId, Constants.LONG_ZERO).orElse(null), unqualifiedItemDtos.size()));
                        });
            }
        }


        //处理方式：4,挑选 -> 挑选不良进行放工，合格直接入库
        if (oemInspect.getDealWay() == Constants.INT_FOUR) {
            RworkerOemInspectionResultDTO.SelectCheckResult selectCheckResult = rworkerOemInspectionResultDto.getSelectCheckResult();

            if ((selectCheckResult.getQualifiedNumber() + selectCheckResult.getUnqualifiedNumber()) != oemInspect.getNumber()) {
                throw new ResponseException("qualifiedNumberAndUnqualifiedNumberNotEqualInspectNumber", "挑选数量与质检数量不相等");
            }
            //挑选的数量
            oemInspect.setSelectSnList(selectCheckResult.getSelectSnList());
            if (realSn) {
                if (Objects.isNull(selectCheckResult)){
                    throw new ResponseException("notSelectCheckResult", "未指定挑选结果");
                }
                //记录合格sn入库
                oemReceiptHistoryBindSnList.stream().filter(bindingSn -> !selectCheckResult.getSelectSnList().contains(bindingSn)).forEach(bindingSn -> {
                    buildOemInspectWarehouse(realSn, destroyOemInspectWarehouseList, oemInspectWarehouseList, processSaveOemInspectWarehouse(oemInspect, bindingSn, null, Constants.INT_ONE));
                });
            } else {
                //入库数量 = 挑选合格数量
                oemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, null, null, selectCheckResult.getQualifiedNumber()));
            }
        }
        //MRB
        if (oemInspect.getDealWay() == Constants.INT_FIVE) {
            //TODO:暂时不需要处理
            return;
        }
        //破坏性检验
        if (ValidateUtils.isValid(destroyOemInspectWarehouseList)) {
            oemInspectWarehouseList.addAll(destroyOemInspectWarehouseList);
        }
        oemInspectWarehouseRepository.saveAll(oemInspectWarehouseList);

        //反写外协工单的合格数与不合格数
        int qualifiedNumber = oemInspectWarehouseList.stream().filter(OemInspectWarehouse::getResult).mapToInt(OemInspectWarehouse::getNumber).sum();
        int unqualifiedNumber = oemInspectWarehouseList.stream().filter(oemInspectWarehouse -> !oemInspectWarehouse.getResult()).mapToInt(OemInspectWarehouse::getNumber).sum();
        OemOrder oemOrder = oemInspect.getOemReceiptHistory().getOemOrder();
        WorkSheet workSheet = oemOrder.getWorkSheet();
        workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + unqualifiedNumber)
                .setQualifiedNumber(workSheet.getQualifiedNumber() + qualifiedNumber)
                .setStatus(WorkSheetStatusEnum.RECEIVING.getStatus());
        oemOrder.setFinishNumber(oemOrder.getFinishNumber() + unqualifiedNumber + qualifiedNumber);
        if (workSheet.getNumber() == oemOrder.getFinishNumber()) {
            workSheet.setStatus(WorkSheetStatusEnum.FINISH.getStatus()).setActualEndDate(LocalDateTime.now());
            oemOrder.setWorkSheet(workSheet);
        }
        oemOrderRepository.save(oemOrder);
        //工序批次详情记录
        BatchWorkDetail batchWorkDetail = processBatchWorkDetail(oemInspect, oemInspectWarehouseList, realSn);
        //sn详情
        processSn(oemInspect, batchWorkDetail, oemInspectWarehouseList, realSn);
    }


    /**
     * 保存下交记录信息
     *
     * @param oemInspect              外协质检单
     * @param oemInspectWarehouseList 外协质检入库单
     * @return
     */
    public BatchWorkDetail processBatchWorkDetail(OemInspect oemInspect, List<OemInspectWarehouse> oemInspectWarehouseList, Boolean realSn) {

        //反写外协工单的合格数与不合格数
        int qualifiedNumber = oemInspectWarehouseList.stream().filter(OemInspectWarehouse::getResult).mapToInt(OemInspectWarehouse::getNumber).sum();
        int unqualifiedNumber = oemInspectWarehouseList.stream().filter(oemInspectWarehouse -> !oemInspectWarehouse.getResult()).mapToInt(OemInspectWarehouse::getNumber).sum();
        int inputNumber = qualifiedNumber + unqualifiedNumber;

        OemOrder oemOrder = oemInspect.getOemReceiptHistory().getOemOrder();
        SubWorkSheet subWorkSheet = oemOrder.getOriginalSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : oemOrder.getOriginalWorkSheet();
        Step step = oemOrder.getStep();
        BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail()) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());

        long workHour = ChronoUnit.MINUTES.between(oemInspect.getArrivalTime(), oemInspect.getInspectTime());

        batchWorkDetail.setInputNumber(batchWorkDetail.getInputNumber() + inputNumber)
                .setFinishNumber(batchWorkDetail.getFinishNumber() + oemInspect.getNumber())
                .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + unqualifiedNumber)
                .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + qualifiedNumber)
                .setEffectNumber(batchWorkDetail.getEffectNumber() + inputNumber)
                .setStartDate(batchWorkDetail.getId() == null ? oemInspect.getInspectTime() : batchWorkDetail.getStartDate())
                .setStep(step)
                .setOperatorId(oemInspect.getOperatorId())
                .setSubWorkSheet(subWorkSheet)
                .setWorkCell(oemInspect.getWorkCell())
                .setWorkHour(batchWorkDetail.getWorkHour() + workHour)
                .setTransferNumber(batchWorkDetail.getTransferNumber() + qualifiedNumber);

        Optional<WsStep> wsStepOptional = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);

        if (wsStepOptional.isEmpty()) {
            wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (wsStepOptional.isEmpty()) {
            throw new ResponseException("wsStepNotFind", "未获取到工序信息");
        }
        WsStep wsStep = wsStepOptional.get();

        if (!ValidateUtils.isValid(wsStep.getPreStepId())) {
            throw new ResponseException("", "外协工序不能为第一道工序");
        }
        Long preStepId = Arrays.stream(wsStep.getPreStepId().split(",")).toList().stream().map(Long::parseLong).findFirst().get();

        BatchWorkDetail preBatchWorkDetail = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), preStepId, Constants.LONG_ZERO).orElse(new BatchWorkDetail()) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), preStepId, Constants.LONG_ZERO).orElse(new BatchWorkDetail());

        if (preBatchWorkDetail.getFinish() == Constants.INT_ONE && (batchWorkDetail.getQualifiedNumber() + batchWorkDetail.getUnqualifiedNumber() == preBatchWorkDetail.getTransferNumber())) {
            Long stepCompNumber = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.countBySubWorkSheetIdAndFinishAndDeleted(subWorkSheet.getId(), ConstantsEnum.FINISH_STATUS.getCategoryName(), Constants.LONG_ZERO) :
                    batchWorkDetailRepository.countByWorkSheetIdAndFinishAndDeleted(workSheet.getId(), ConstantsEnum.FINISH_STATUS.getCategoryName(), Constants.LONG_ZERO);
            batchWorkDetail
                    .setEndDate(LocalDateTime.now())
                    .setFinish(Constants.INT_ONE);
            if (Objects.nonNull(subWorkSheet)) {
                batchWorkDetail.setSubWorkSheet(subWorkSheetRepository.save(subWorkSheet.setStepCompNumber(Objects.nonNull(stepCompNumber)?stepCompNumber.intValue()+Constants.INT_ONE: Constants.INT_ZERO)));
            }else {
                batchWorkDetail.setWorkSheet(workSheetRepository.save(workSheet.setStepCompNumber(Objects.nonNull(stepCompNumber)?stepCompNumber.intValue()+Constants.INT_ONE: Constants.INT_ZERO)));
            }
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);

        if (ValidateUtils.isValid(oemInspectWarehouseList)) {
            List<WsStepUnqualifiedItem> wsStepUnqualifiedItems = new ArrayList<>();
            oemInspectWarehouseList.stream().filter(oemInspectWarehouse -> Objects.nonNull(oemInspectWarehouse.getUnqualifiedItem()))
                    .collect(Collectors.groupingBy(OemInspectWarehouse::getUnqualifiedItem)).forEach((unqualifiedItem, entityList) -> {
                        WsStepUnqualifiedItem wsStepUnqualifiedItem = !ObjectUtils.isEmpty(subWorkSheet) ?
                                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), step.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem()) :
                                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), step.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());

                        int unqNumber = entityList.stream().mapToInt(OemInspectWarehouse::getNumber).sum();

                        wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() + unqNumber)
                                .setRecordDate(Objects.nonNull(wsStepUnqualifiedItem.getRecordDate()) ? wsStepUnqualifiedItem.getRecordDate() : LocalDate.now())
                                .setFlag(Boolean.FALSE).setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setStep(step)
                                .setUnqualifiedItem(unqualifiedItem).setOperatorId(oemInspect.getOperatorId());

                        wsStepUnqualifiedItems.add(wsStepUnqualifiedItem);
                    });
            wsStepUnqualifiedItemRepository.saveAll(wsStepUnqualifiedItems);
        }

        if (!realSn) {
            saveBatchStaffPerform(batchWorkDetail, inputNumber,qualifiedNumber,unqualifiedNumber,workHour,LocalDate.now(), oemInspectWarehouseList);
        }
        return batchWorkDetail;
    }


    /**
     * 保存sn详情下交记录
     *
     * @param oemInspect              外协质检单
     * @param batchWorkDetail         批次详情
     * @param oemInspectWarehouseList 外协质检入库单
     * @param realSn                  是否真实sn
     */
    public void processSn(OemInspect oemInspect, BatchWorkDetail batchWorkDetail, List<OemInspectWarehouse> oemInspectWarehouseList, Boolean realSn) {
        if (!realSn) {
            return;
        }

        oemInspectWarehouseList.forEach(oemInspectWarehouse -> {

            SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(oemInspectWarehouse.getSn(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("snWorkStatusNotFind", "sn状态不存在"));

            SubWorkSheet subWorkSheet = Objects.nonNull(snWorkStatus.getSubWorkSheet()) ? snWorkStatus.getSubWorkSheet() : null;
            WorkSheet workSheet = Objects.nonNull(snWorkStatus.getSubWorkSheet()) ? snWorkStatus.getSubWorkSheet().getWorkSheet() : snWorkStatus.getWorkSheet();

            //更新SN工作详情
            SnWorkDetail snWorkDetail = new SnWorkDetail();
            snWorkDetail.setEndDate(LocalDateTime.now())
                    .setOperatorId(batchWorkDetail.getOperatorId())
                    .setStartDate(oemInspect.getArrivalTime())
                    .setEndDate(LocalDateTime.now())
                    .setResult(oemInspectWarehouse.getResult() ? Constants.INT_ONE : Constants.INT_ZERO)
                    .setSn(oemInspectWarehouse.getSn())
                    .setWorkSheet(batchWorkDetail.getWorkSheet())
                    .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                    .setWorkCell(batchWorkDetail.getWorkCell())
                    .setStep(batchWorkDetail.getStep())
                    .setYsn(null)
                    .setContainerDetail(null)
                    .setUnqualifiedItem(oemInspectWarehouse.getUnqualifiedItem())
                    .setWorkHour(ChronoUnit.MINUTES.between(oemInspect.getArrivalTime(), LocalDateTime.now()))
                    .setDeleted(Constants.LONG_ZERO);
            snWorkDetailRepository.save(snWorkDetail);

            snWorkStatus.setLatestSnWorkDetail(snWorkDetail);
            if (snWorkDetail.getResult() == Constants.INT_ZERO) {
                snWorkStatus.setEndDate(LocalDateTime.now()).setIsUpdateBatchWorkDetail(Boolean.TRUE)
                        .setStatus(SnWorkStatusEnum.SCRAP.getStatus())
                        .setLatestUnqualifiedItem(snWorkDetail.getUnqualifiedItem())
                        .setLatestReworkSnWorkDetail(snWorkDetail);
            }
            //更新工单、子工单不合格数量
            if (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus()) {
                updateWorkSheetUnqualifiedNumber(subWorkSheet, workSheet);
                // 更新工单统计表
                if (Objects.nonNull(workSheet) && null != workSheet.getId()) {
                    workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), Constants.INT_ZERO, Constants.INT_ONE, OperationEnum.ADD);
                }
                if (Objects.nonNull(subWorkSheet) && null != subWorkSheet.getId()) {
                    workSheetStatisticsServices[0].updateWorkSheetNumber(subWorkSheet.getWorkSheet().getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), Constants.INT_ZERO, Constants.INT_ONE, OperationEnum.ADD);
                }
            }
            snWorkStatusRepository.save(snWorkStatus);
            saveSnStaffPerform(batchWorkDetail, null, snWorkDetail, LocalDate.now());
        });
    }

    /**
     * 破坏性实验结果处理
     *
     * @param destroyCheckResult          破坏性数据
     * @param oemInspect                  外协质检单
     * @param oemReceiptHistoryBindSnList 外协质检单绑定的sn列表
     */
    public List<OemInspectWarehouse> processDestroyCheckResult(RworkerOemInspectionResultDTO.DestroyCheckResult destroyCheckResult, OemInspect oemInspect, List<String> oemReceiptHistoryBindSnList) {

        List<OemInspectWarehouse> destroyOemInspectWarehouseList = Lists.newArrayList();

        //验证是否存在破坏性测试
        if (Objects.isNull(destroyCheckResult) || destroyCheckResult.getUnqualifiedItemId() == null || !ValidateUtils.isValid(destroyCheckResult.getSnList())) {
            return destroyOemInspectWarehouseList;
        }
        //存在sn，需要验证sn的合格性
        validRealSnOemInspectWarehouse(oemReceiptHistoryBindSnList, destroyCheckResult.getSnList(), oemInspect);
        oemInspect.setDestroyCheck(Boolean.TRUE);
        //获取不良项目
        UnqualifiedItem unqualifiedItem = unqualifiedItemRepository.findByIdAndDeleted(destroyCheckResult.getUnqualifiedItemId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("unqualifiedItemNotFind", "未获取到不良项目"));
        oemInspect.setDestroyItem(unqualifiedItem);
        oemInspectRepository.save(oemInspect);
        Boolean realSn = ValidateUtils.isValid(oemReceiptHistoryBindSnList);
        //记录破坏性入库数据 - 真实sn单独记录，非真实sn 记录数量
        if (realSn) {
            destroyCheckResult.getSnList().stream().distinct().forEach(checkSn -> destroyOemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, checkSn, unqualifiedItem, Constants.INT_ONE)));
        } else {
            destroyOemInspectWarehouseList.add(processSaveOemInspectWarehouse(oemInspect, null, unqualifiedItem, (int) destroyCheckResult.getSnList().stream().distinct().count()));
        }
        return destroyOemInspectWarehouseList;
    }


    /**
     * 验证下交sn 是否在收货列表中，是否已入库
     *
     * @param oemReceiptHistoryBindSnList 外协质检单绑定的已收货sn列表
     * @param checkSnList                 质检录入的sn列表
     * @param oemInspect                  外协质检单
     */
    public void validRealSnOemInspectWarehouse(List<String> oemReceiptHistoryBindSnList, List<String> checkSnList, OemInspect oemInspect) {
        if (!ValidateUtils.isValid(oemReceiptHistoryBindSnList) || !ValidateUtils.isValid(checkSnList)) {
            return;
        }
        List<String> notFindBindSn = checkSnList.stream().filter(sn -> !oemReceiptHistoryBindSnList.contains(sn)).toList();
        // 如果存在不在收货列表中的入库不良sn，抛出异常
        if (ValidateUtils.isValid(notFindBindSn)) {
            throw new ResponseException("warehouseUnqSnNotFindBindSnList", "入库不良sn不在收货列表中");
        }
        //验证当前sn列表是否已在当前外协单入库，如果已入库则报错
        List<OemInspectWarehouse> oemInspectWarehouses = oemInspectWarehouseRepository.findByOemOrderIdAndSnInAndDeleted(oemInspect.getOemReceiptHistory().getOemOrder().getId(), checkSnList, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(oemInspectWarehouses)) {
            throw new ResponseException("warehouseSnAlreadyInOemInspect", "sn【" + oemInspectWarehouses.stream().map(OemInspectWarehouse::getSn).collect(Collectors.joining(",")) + "】已在当前外协单入库");
        }
    }


    /**
     * 外协质检入库记录
     *
     * @param oemInspect      外协质检单
     * @param sn              sn
     * @param unqualifiedItem 不良项目
     * @param number          数量
     * @return 入库记录
     */
    public OemInspectWarehouse processSaveOemInspectWarehouse(OemInspect oemInspect, String sn, UnqualifiedItem unqualifiedItem, Integer number) {
        OemInspectWarehouse oemInspectWarehouse = new OemInspectWarehouse();
        oemInspectWarehouse
                .setOemOrder(oemInspect.getOemReceiptHistory().getOemOrder())
                .setOemInspect(oemInspect)
                .setSn(sn)
                .setUnqualifiedItem(unqualifiedItem)
                .setNumber(number)
                .setResult(Objects.isNull(unqualifiedItem));
        return oemInspectWarehouse;
    }

    /**
     * 如果存在破坏性实验结果，则出现的不良不加入到入库列表中（因为破坏性实验已开出不良），否则直接加入到入库列表中
     *
     * @param realSn                         sn真实性
     * @param destroyOemInspectWarehouseList 破坏性实验入库列表
     * @param oemInspectWarehouseList        入库列表
     * @param oemInspectWarehouse            入库记录
     */
    public void buildOemInspectWarehouse(Boolean realSn, List<OemInspectWarehouse> destroyOemInspectWarehouseList, List<OemInspectWarehouse> oemInspectWarehouseList, OemInspectWarehouse oemInspectWarehouse) {
        if (!ValidateUtils.isValid(destroyOemInspectWarehouseList)) {
            oemInspectWarehouseList.add(oemInspectWarehouse);
        } else {
            if (realSn) {
                Optional<OemInspectWarehouse> existEntity = destroyOemInspectWarehouseList.stream().filter(destroyOemInspectWarehouse -> destroyOemInspectWarehouse.getSn().equals(oemInspectWarehouse.getSn())).findFirst();
                if (existEntity.isEmpty()) {
                    oemInspectWarehouseList.add(oemInspectWarehouse);
                }
            }
        }
    }

    /**
     * 保存员工产量及对应不良信息
     *
     * @param batchWorkDetail         批量详情
     * @param oemInspectWarehouseList 批量工序生产数据参数
     * @param workDate                记录日期
     */
    public void saveBatchStaffPerform(BatchWorkDetail batchWorkDetail,int inputNumber,int  qualifiedNumber,int unqualifiedNumber,double workHour,LocalDate workDate, List<OemInspectWarehouse> oemInspectWarehouseList) {
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform.setBatchWorkDetailId(batchWorkDetail.getId())
                .setStaffId(batchWorkDetail.getOperatorId())
                .setStep(batchWorkDetail.getStep())
                .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                .setWorkSheet(batchWorkDetail.getWorkSheet())
                .setWorkCell(batchWorkDetail.getWorkCell())
                .setRecordDate(workDate)
                .setRecordTime(LocalDateTime.now())
                .setWorkHour(workHour)
                .setInputNumber(inputNumber)
                .setQualifiedNumber(qualifiedNumber)
                .setUnqualifiedNumber(unqualifiedNumber).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);
        //保存员工不良明细信息
        oemInspectWarehouseList.stream().filter(oemInspectWarehouse -> Objects.nonNull(oemInspectWarehouse.getUnqualifiedItem()))
                .collect(Collectors.groupingBy(OemInspectWarehouse::getUnqualifiedItem)).forEach((unqualifiedItem, entityList) -> {
                    int unqNumber = entityList.stream().mapToInt(OemInspectWarehouse::getNumber).sum();
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
                    staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                            .setRecordTime(staffPerform.getRecordTime())
                            .setUnqualifiedItem(unqualifiedItem)
                            .setNumber(unqNumber).setDeleted(net.airuima.rbase.constant.Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                });
    }

    /**
     * 保存单支员工产量数据
     *
     * @param batchWorkDetail 批量详情
     * @param containerDetail 容器详情
     * @param snWorkDetail    SN详情
     * @param workDate        记录日期
     */
    private void saveSnStaffPerform(BatchWorkDetail batchWorkDetail,
                                    ContainerDetail containerDetail, SnWorkDetail snWorkDetail, LocalDate workDate) {
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform.setSnWorkDetailId(snWorkDetail.getId())
                .setBatchWorkDetailId(null != batchWorkDetail ? batchWorkDetail.getId() : null)
                .setContainerDetailId(null != containerDetail ? containerDetail.getId() : null)
                .setStaffId(snWorkDetail.getOperatorId())
                .setStep(snWorkDetail.getStep())
                .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setWorkSheet(snWorkDetail.getWorkSheet())
                .setWorkCell(snWorkDetail.getWorkCell())
                .setRecordDate(workDate)
                .setRecordTime(LocalDateTime.now())
                .setWorkHour(staffPerform.getWorkHour() + snWorkDetail.getWorkHour())
                .setInputNumber(Constants.INT_ONE)
                .setQualifiedNumber(snWorkDetail.getResult())
                .setUnqualifiedNumber(Constants.INT_ONE - snWorkDetail.getResult()).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);
        //保存员工不良明细信息
        if (snWorkDetail.getResult() == Constants.INT_ZERO && null != snWorkDetail.getUnqualifiedItem()) {
            StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
            staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                    .setRecordTime(staffPerform.getRecordTime())
                    .setUnqualifiedItem(snWorkDetail.getUnqualifiedItem())
                    .setNumber(Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
            staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
        }
    }

    /**
     * 更新工单、子工单不合格数量
     *
     * @param subWorkSheet         子工单
     * @param workSheet            工单
     */
    private void updateWorkSheetUnqualifiedNumber(SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        if (null != subWorkSheet && null != subWorkSheet.getId()) {
            subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + Constants.INT_ONE);
            if (subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                subWorkSheet.setActualEndDate(LocalDateTime.now());
            }
            subWorkSheetRepository.save(subWorkSheet);
        }
        if (null != workSheet && null != workSheet.getId()) {
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + Constants.INT_ONE);
            if (workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber()) {
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                workSheet.setActualEndDate(LocalDateTime.now());
            }
            workSheetRepository.save(workSheet);
        }
    }


}
