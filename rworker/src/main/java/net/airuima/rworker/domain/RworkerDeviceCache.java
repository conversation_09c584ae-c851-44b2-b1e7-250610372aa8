package net.airuima.rworker.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.Container;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

@Schema(name = "Rworker设备缓存", description = "Rworker设备缓存")
@Entity
@Table(name = "procedure_rworker_device_cache")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
public class RworkerDeviceCache extends CustomBaseEntity implements Serializable {

    /**
     * 总工单
     */
    @ManyToOne
    @Schema(description = "总工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 容器id
     */
    @ManyToOne
    @Schema(description = "容器id")
    @JoinColumn(name = "container_id")
    private Container container;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    @Column(name = "number")
    private int number;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Column(name = "status")
    private int status;

    /**
     * 缓存类型(0:总工单,1:子工单,2:总工单容器,3:子工单容器)
     */
    @Schema(description = "缓存类型")
    @Column(name = "type")
    private int type;

    /**
     * 下交缓存
     */
    @Schema(description = "下交缓存")
    @Column(name = "cache")
    private String cache;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public RworkerDeviceCache setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public RworkerDeviceCache setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public RworkerDeviceCache setStep(Step step) {
        this.step = step;
        return this;
    }

    public Container getContainer() {
        return container;
    }

    public RworkerDeviceCache setContainer(Container container) {
        this.container = container;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public RworkerDeviceCache setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public RworkerDeviceCache setStatus(int status) {
        this.status = status;
        return this;
    }

    public int getType() {
        return type;
    }

    public RworkerDeviceCache setType(int type) {
        this.type = type;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public RworkerDeviceCache setCache(String cache) {
        this.cache = cache;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RworkerDeviceCache rworkerDeviceCache = (RworkerDeviceCache) o;
        if (rworkerDeviceCache.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), rworkerDeviceCache.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
