package net.airuima.rworker.service.rworker.device.impl;

import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rworker.service.rworker.device.IRworkerDeviceService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Rworker设备服务
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class RworkerDeviceServiceImpl implements IRworkerDeviceService {

    /**
     * 缓存触发业务逻辑
     */
    @Override
    public void cacheHandle(RworkerCache rworkerCache) {
    }
}
