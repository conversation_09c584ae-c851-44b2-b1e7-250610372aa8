package net.airuima.rworker.service.rworker.quality.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.LatestCheckResultDealWayEnum;
import net.airuima.rbase.constant.WorkCellStartCheckEnum;
import net.airuima.rbase.constant.WorkCellStartCheckFlagEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.domain.procedure.quality.LatestCheckResult;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendHistory;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.InspectionTaskDTO;
import net.airuima.rbase.repository.base.quality.WorkCellCheckStartRuleRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.quality.LatestCheckResultRepository;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendHistoryRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.rworker.quality.IInspectionService;
import net.airuima.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class InspectionServiceImpl implements IInspectionService {

    @Autowired
    private WorkCellCheckStartRuleRepository workCellCheckStartRuleRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private WorkCellExtendHistoryRepository workCellExtendHistoryRepository;

    /**
     * 首检检测
     *
     * @param productWorkSheetId 待投产子工单/工单主键ID
     * @param workCell           检测工位
     * @param firstTimeWork      是否开班：true：开班 ：false：非开班
     * @return net.airuima.rbase.dto.base.BaseDTO  基础响应信息
     * @date 2023/5/4
     */
    @Override
    public BaseDTO faiInspectionInfo(Boolean firstTimeWork, Long productWorkSheetId, WorkCell workCell) {
        AtomicReference<BaseDTO> baseDto = new AtomicReference<>(new BaseDTO(Constants.OK));
        List<LatestCheckResult> latestCheckResultList = latestCheckResultRepository.findByWorkCellIdAndCategoryAndDeleted(workCell.getId(), WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(latestCheckResultList) && latestCheckResultList.stream().anyMatch(latestCheckResult -> (!latestCheckResult.getResult() && latestCheckResult.getDealWay() != Constants.INT_THREE))) {
            return new BaseDTO(Constants.KO, "当前工位最新首检未通过，请对工位继续首检");
        }
        //验证当前工位存在的待检测任务，是否需要进行首检处理
        baseDto.set(BeanUtil.getHighestPrecedenceBean(IInspectionService.class).validTodoInspectionTask(workCell, WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()));
        if (Constants.KO.equals(baseDto.get().getStatus())){
            return baseDto.get();
        }
        //获取工位发起检测规则
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndIsEnableAndDeleted(workCell.getId(), WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), Boolean.TRUE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)) {
            return baseDto.get();
        }
        //子工单首检
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        subWorkSheetOptional.ifPresent(subWorkSheet -> baseDto.set(checkProcessInspectionSubWorkSheet(subWorkSheet, workCell, WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        //工单首检
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        workSheetOptional.ifPresent(workSheet -> baseDto.set(checkProcessInspectionWorkSheet(workSheet, workCell, WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        return baseDto.get();
    }

    /**
     * 巡检检测
     *
     * @param productWorkSheet 检检工单
     * @param workCell         检测工位
     * @param firstTimeWork    是否开班：true：开班 ：false：非开班
     * @return void
     * @date 2023/5/4
     */
    @Override
    public BaseDTO ipqcInspectionInfo(Boolean firstTimeWork, Long productWorkSheet, WorkCell workCell) {
        AtomicReference<BaseDTO> baseDto = new AtomicReference<>(new BaseDTO(Constants.OK));
        List<LatestCheckResult> latestCheckResultList = latestCheckResultRepository.findByWorkCellIdAndCategoryAndDeleted(workCell.getId(), WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(latestCheckResultList) && latestCheckResultList.stream().anyMatch(latestCheckResult -> (!latestCheckResult.getResult() && latestCheckResult.getDealWay() != Constants.INT_THREE))) {
            return new BaseDTO(Constants.KO, "当前工位最新巡检未通过，请对工位继续巡检");
        }
        //验证当前工位存在的待检测任务，是否需要进行巡检处理
        baseDto.set(BeanUtil.getHighestPrecedenceBean(IInspectionService.class).validTodoInspectionTask(workCell, WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()));
        if (Constants.KO.equals(baseDto.get().getStatus())){
            return baseDto.get();
        }
        //获取工位发起检测规则
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndIsEnableAndDeleted(workCell.getId(), WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), Boolean.TRUE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)) {
            return baseDto.get();
        }
        //子工单巡检
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(productWorkSheet, Constants.LONG_ZERO);
        subWorkSheetOptional.ifPresent(subWorkSheet -> baseDto.set(checkProcessInspectionSubWorkSheet(subWorkSheet, workCell, WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        //工单巡检
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(productWorkSheet, Constants.LONG_ZERO);
        workSheetOptional.ifPresent(workSheet -> baseDto.set(checkProcessInspectionWorkSheet(workSheet, workCell, WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        return baseDto.get();
    }

    /**
     * 验证首检巡检是否已存在首检任务，巡检任务；
     * 有两种情况：
     * 没有宽放时长：存在待首检巡检的直接，预警提示需要对应的检测
     * 有宽放时长且已宽放过，超过待检时间直接 预警提示需要进行对应的检测记录
     * 有宽放时间但未进行宽放处理：放行本轮检测，添加宽放记录，修改待做任务变为已放行
     *
     * @param workCell 工位
     * @param category 检测类型 0 首检，1巡检
     * @return 是否需要检测
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    public BaseDTO validTodoInspectionTask(WorkCell workCell,Integer category) {

        String inspectionType = category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() ?
                WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark() : WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark();

        List<InspectTask> inspectTasks = inspectTaskRepository.findByWorkCellIdAndCategoryAndStatusAndDeleted(workCell.getId(), category,
                Boolean.FALSE, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(inspectTasks)) {
            return new BaseDTO(Constants.OK);
        }
        //存在待检任务且已宽放过
        if (inspectTasks.stream().anyMatch(inspectTask ->
                (inspectTask.getTodoInspectedTime().isBefore(LocalDateTime.now()) &&
                inspectTask.getIsExtend()))) {
            return new BaseDTO(Constants.KO, "工位存在 "+inspectionType+" 待检任务,请先处理");
        }

        // 存在待检任务且未宽放过(宽放后，宽放时间已到)
        List<InspectTask> todoInspectTasks = inspectTasks.stream()
                .filter(this::shouldProcessExtendTime)
                .filter(this::isExtendTimeExpired)
                .map(this::processInspectTaskExtend)
                .toList();

        if (ValidateUtils.isValid(todoInspectTasks)) {
            return new BaseDTO(Constants.KO, "工位存在 " + inspectionType + " 待检任务,请先处理");
        }

        // 处理宽放时间未到的任务
        inspectTasks.stream()
                .filter(this::shouldProcessExtendTime)
                .filter(inspectTask -> !isExtendTimeExpired(inspectTask))
                .forEach(this::processInspectTaskExtend);

        return new BaseDTO(Constants.OK);
    }

    /**
     * 判断是否需要处理宽放时间
     *
     * @param inspectTask 检测任务
     * @return 是否需要处理
     */
    private boolean shouldProcessExtendTime(InspectTask inspectTask) {
        return inspectTask.getTodoInspectedTime().isBefore(LocalDateTime.now()) && !inspectTask.getIsExtend();
    }

    /**
     * 判断宽放时间是否已过期
     *
     * @param inspectTask 检测任务
     * @return 宽放时间是否已过期
     */
    private boolean isExtendTimeExpired(InspectTask inspectTask) {
        return inspectTask.getTodoInspectedTime()
                .plusSeconds((long) (inspectTask.getExtendTime() * 3600))
                .isBefore(LocalDateTime.now());
    }

    /**
     * 处理检测任务的宽放时间
     *
     * @param inspectTask 检测任务
     * @return 处理后的检测任务
     */
    private InspectTask processInspectTaskExtend(InspectTask inspectTask) {
        inspectTask.setIsExtend(Boolean.TRUE)
                .setTodoInspectedTime(inspectTask.getTodoInspectedTime()
                        .plusSeconds((long) (inspectTask.getExtendTime() * 3600)));
        inspectTaskRepository.save(inspectTask);

        // 开启宽放时长记录
        BeanUtil.getHighestPrecedenceBean(IInspectionService.class)
                .wcExtendHistoryRecord(inspectTask.getSubWorkSheet(), inspectTask.getWorkSheet(),
                        inspectTask.getWorkCellCheckStartRule());
        return inspectTask;
    }

    @Override
    public void pqcInspectionInfo() {
        IInspectionService.super.pqcInspectionInfo();
    }

    @Override
    public void fqcInspectionInfo() {
        IInspectionService.super.fqcInspectionInfo();
    }

    @Override
    public void lqcInspectionInfo() {
        IInspectionService.super.lqcInspectionInfo();
    }

    /**
     * 工单 工位检测首检 PQC检测
     *
     * @param workSheet               工单
     * @param workCell                工位
     * @param category                检测类型
     * @param workCellCheckStartRules 工位发起规则
     * @return net.airuima.rbase.web.rest.rworker.quality.dto.RworkerCheckProcessQualityGetDTO
     * <AUTHOR>
     * @date 2023/2/22
     */
    public BaseDTO checkProcessInspectionWorkSheet(WorkSheet workSheet, WorkCell workCell, Integer category, List<WorkCellCheckStartRule> workCellCheckStartRules) {
        BaseDTO baseDto = new BaseDTO(Constants.OK);
        StringBuilder msg = new StringBuilder();
        String singleWsProduct = commonService.getDictionaryData(Constants.KEY_SINGLE_WS_PRODUCT);
        Boolean switchModel = Boolean.FALSE;
        String inspectionType = category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() ?
                WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark() :
                WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark();

        //验证工位发起时机
        for (WorkCellCheckStartRule rule : workCellCheckStartRules) {

            // 获取最新检测结果
            LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(
                    rule.getWorkCell().getId(), rule.getCategory(), rule.getVarietyId(), Constants.LONG_ZERO).orElse(null);

            //处理无最新检测结果的情况
            if(handleNoLatestCheckResult(latestCheckResult, rule, null, workSheet,workCell, category, inspectionType, msg)){
                continue;
            }
            //处理不合格且未放行的情况
            if (handleUnqualifiedResult(latestCheckResult, rule, null, workSheet,workCell, inspectionType, msg)){
                continue;
            }
            // 处理超过待检时间的情况
            if (handleOverdueCheckTime(latestCheckResult, rule, null,workSheet, workCell, inspectionType, msg)) {
                continue;
            }
            // 处理周期检测但无下次待检时间的情况
            if (handlePeriodicCheckWithNoNextTime(latestCheckResult, rule, null,workSheet)) {
                continue;
            }

            Pedigree pedigree = workSheet.getPedigree();
            WorkSheet latestWorkSheet = latestCheckResult.getWorkSheet();
            Pedigree latestPedigree = latestWorkSheet.getPedigree();

            // 处理切换型号的情况
            if (handleModelSwitch(rule, null, workSheet,pedigree, latestPedigree, baseDto, msg)) {
                switchModel = Boolean.TRUE;
                continue;
            }

            // 处理切换工单/子工单的情况
            if (handleWorkSheetSwitch(singleWsProduct, rule, null, workSheet, latestWorkSheet, null, switchModel, baseDto, msg)) {
                continue;
            }
            // 处理指定时间检测的情况
            handleSpecifiedTimeCheck(rule, latestCheckResult, null,workSheet, workCell, msg);
        }
        return ObjectUtils.isEmpty(msg.toString()) ? baseDto : baseDto.setStatus(Constants.KO).setMessage(msg.toString());
    }


    /**
     * 验证子工单是否需要进行质量检测（首检/巡检）
     *
     * @param subWorkSheet            子工单
     * @param workCell                工位
     * @param category                检测类型（0-首检，1-巡检）
     * @param workCellCheckStartRules 工位检测触发规则列表
     * @return 检测结果，OK表示无需检测，KO表示需要检测并包含原因
     * <AUTHOR>
     * @date 2023/2/22
     */
    public BaseDTO checkProcessInspectionSubWorkSheet(SubWorkSheet subWorkSheet, WorkCell workCell, Integer category, List<WorkCellCheckStartRule> workCellCheckStartRules) {

        BaseDTO baseDto = new BaseDTO(Constants.OK);
        StringBuilder msg = new StringBuilder();
        String singleWsProduct = commonService.getDictionaryData(Constants.KEY_SINGLE_WS_PRODUCT);
        Boolean switchModel = Boolean.FALSE;
        String inspectionType = category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() ?
                WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark() :
                WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark();

        // 验证工位发起时机
        for (WorkCellCheckStartRule workCellCheckStartRule : workCellCheckStartRules) {
            // 获取最新检测结果
            LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(
                    workCellCheckStartRule.getWorkCell().getId(),
                    workCellCheckStartRule.getCategory(),
                    workCellCheckStartRule.getVarietyId(),
                    Constants.LONG_ZERO).orElse(null);

            // 处理无检测结果的情况
            if (handleNoLatestCheckResult(latestCheckResult, workCellCheckStartRule, subWorkSheet, null,workCell, category, inspectionType, msg)) {
                continue;
            }

            // 处理不合格且未放行的情况
            if (handleUnqualifiedResult(latestCheckResult, workCellCheckStartRule, subWorkSheet, null,workCell, inspectionType, msg)) {
                continue;
            }

            // 获取相关工单和谱系信息
            Pedigree pedigree = subWorkSheet.getWorkSheet().getPedigree();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            WorkSheet latestWorkSheet = latestCheckResult.getSubWorkSheet().getWorkSheet();
            Pedigree latestPedigree = latestWorkSheet.getPedigree();
            SubWorkSheet latestSubWorkSheet = latestCheckResult.getSubWorkSheet();

            // 处理超过待检时间的情况
            if (handleOverdueCheckTime(latestCheckResult, workCellCheckStartRule, subWorkSheet, null,workCell, inspectionType, msg)) {
                continue;
            }
            // 处理周期检测但无下次待检时间的情况
            if (handlePeriodicCheckWithNoNextTime(latestCheckResult, workCellCheckStartRule, subWorkSheet,null)) {
                continue;
            }

            // 处理切换型号的情况
            if (handleModelSwitch(workCellCheckStartRule, subWorkSheet, null,pedigree, latestPedigree, baseDto, msg)) {
                switchModel = Boolean.TRUE;
                continue;
            }

            // 处理切换工单/子工单的情况
            if (handleWorkSheetSwitch(singleWsProduct, workCellCheckStartRule, subWorkSheet, workSheet, latestWorkSheet, latestSubWorkSheet, switchModel, baseDto, msg)) {
                continue;
            }
            // 处理指定时间检测的情况
            handleSpecifiedTimeCheck(workCellCheckStartRule, latestCheckResult, subWorkSheet,null, workCell, msg);
        }
        return ObjectUtils.isEmpty(msg.toString()) ? baseDto : baseDto.setStatus(Constants.KO).setMessage(msg.toString());

    }


    /**
     * 处理无最新检测结果的情况
     *
     * @param latestCheckResult      最新检查结果
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @param workSheet           工单
     * @param workCell               工位
     * @param category               检查类型
     * @param inspectionType         检查类型
     * @param msg                    异常消息
     * @return 处理结果
     */
    private boolean handleNoLatestCheckResult(LatestCheckResult latestCheckResult, WorkCellCheckStartRule workCellCheckStartRule,
                                              SubWorkSheet subWorkSheet, WorkSheet workSheet,WorkCell workCell, Integer category, String inspectionType, StringBuilder msg) {
        // 无检测结果且有宽放时长
        if (null == latestCheckResult && workCellCheckStartRule.getExtendTime() > Constants.INT_ZERO) {
            LatestCheckResult latestCheck = new LatestCheckResult();
            latestCheck.setCategory(category)
                    .setSubWorkSheet(subWorkSheet)
                    .setWorkSheet(workSheet)
                    .setResult(Boolean.TRUE)
                    .setDealWay(Constants.INT_ONE)
                    .setStatus(Boolean.TRUE)
                    .setDisplay(Boolean.TRUE)
                    .setVarietyId(workCellCheckStartRule.getVarietyId())
                    .setWorkCell(workCell)
                    .setRecordDate(LocalDateTime.now())
                    .setExtendTime(workCellCheckStartRule.getExtendTime())
                    .setNextCheckDate(LocalDateTime.now().plusSeconds((long) (workCellCheckStartRule.getExtendTime() * 3600)));
            latestCheckResultRepository.save(latestCheck);
            // 记录工位宽放时长
            BeanUtil.getHighestPrecedenceBean(IInspectionService.class)
                    .wcExtendHistoryRecord(subWorkSheet, workSheet, workCellCheckStartRule);
            //生成待检任务记录
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,latestCheck.getNextCheckDate(),
                    Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
            return true;
        } else if (Objects.isNull(latestCheckResult)) {
            // 无检测结果且无宽放时长，创建待检任务
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,
                    LocalDateTime.now(),Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
            msg.append("当前工位需要进行").append(inspectionType);
            return true;
        }
        return false;
    }

    /**
     * 处理不合格且未放行的检测结果
     *
     * @param latestCheckResult      最新检查结果
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @param workSheet           工单
     * @param workCell               工位
     * @param inspectionType         检查类型
     * @param msg                    异常消息
     * @return 处理结果
     */
    private boolean handleUnqualifiedResult(LatestCheckResult latestCheckResult, WorkCellCheckStartRule workCellCheckStartRule,
                                            SubWorkSheet subWorkSheet, WorkSheet workSheet,WorkCell workCell, String inspectionType, StringBuilder msg) {
        if (latestCheckResult != null && !latestCheckResult.getResult() && latestCheckResult.getDealWay() != LatestCheckResultDealWayEnum.RELEASE.getCode()) {
            // 生产待检任务
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,LocalDateTime.now(),Boolean.TRUE,
                    workCellCheckStartRule.getExtendTime()));
            msg.append(inspectionType).append(": 最新检测结果不合格且未放行，需要重新检测;");
            return true;
        }
        return false;
    }


    /**
     * 处理超过待检时间的情况
     *
     * @param latestCheckResult      最新检查结果
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @param workCell               工位
     * @param inspectionType         检查类型
     * @param msg                    异常消息
     * @return 处理结果
     */
    private boolean handleOverdueCheckTime(LatestCheckResult latestCheckResult, WorkCellCheckStartRule workCellCheckStartRule,
                                           SubWorkSheet subWorkSheet, WorkSheet workSheet, WorkCell workCell, String inspectionType, StringBuilder msg) {
        if (latestCheckResult.getNextCheckDate() != null && latestCheckResult.getNextCheckDate().isBefore(LocalDateTime.now())) {
            // 检查最新结果宽放时长为0，触发规则为实际执行宽放时长，宽放时长大于0 -> 放行本次触发检查，推延下次待检时间
            if (latestCheckResult.getExtendTime() == Constants.INT_ZERO &&
                    workCellCheckStartRule.getExtendType() == Constants.INT_ONE &&
                    workCellCheckStartRule.getExtendTime() > Constants.INT_ZERO) {

                latestCheckResult.setNextCheckDate(LocalDateTime.now().plusSeconds((long) ((workCellCheckStartRule.getExtendTime()) * 3600)))
                        .setExtendTime(workCellCheckStartRule.getExtendTime());
                latestCheckResultRepository.save(latestCheckResult);
                // 记录工位宽放时长
                BeanUtil.getHighestPrecedenceBean(IInspectionService.class)
                        .wcExtendHistoryRecord(subWorkSheet, workSheet, workCellCheckStartRule);
                //生成待检任务记录
                addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,
                        latestCheckResult.getNextCheckDate(),Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
                return true;
            }
            // 生产待检任务
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,
                    LocalDateTime.now(),Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
            msg.append("超过待检时间").append("工位需要进行：").append(inspectionType).append(" ");
            return true;
        }
        return false;
    }

    /**
     * 处理周期检测但无下次待检时间的情况
     *
     * @param latestCheckResult      最新检查结果
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @return 处理状态
     */
    private boolean handlePeriodicCheckWithNoNextTime(LatestCheckResult latestCheckResult, WorkCellCheckStartRule workCellCheckStartRule, SubWorkSheet subWorkSheet,WorkSheet workSheet) {
        if (workCellCheckStartRule.getFlag() == WorkCellStartCheckFlagEnum.FIXED_INTERVAL.getCategory() && Objects.isNull(latestCheckResult.getNextCheckDate())) {
            // 当前不管是按计划，还是按实际执行，都已当前时间+宽放时长，类型为实际执行的话，需要添加宽放时间，便于二次宽放验证，是否已宽放
            latestCheckResult.setNextCheckDate(LocalDateTime.now().plusSeconds(
                    (long) ((workCellCheckStartRule.getDuration() + workCellCheckStartRule.getExtendTime()) * 3600)));

            if (workCellCheckStartRule.getExtendType() == Constants.INT_ONE) {
                latestCheckResult.setExtendTime(workCellCheckStartRule.getExtendTime());
            }
            latestCheckResultRepository.save(latestCheckResult);
            // 记录工位宽放时长
            BeanUtil.getHighestPrecedenceBean(IInspectionService.class).wcExtendHistoryRecord(subWorkSheet, workSheet, workCellCheckStartRule);
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,latestCheckResult.getNextCheckDate(),Boolean.TRUE,
                    workCellCheckStartRule.getExtendTime()));
            return true;
        }
        return false;
    }

    /**
     * 处理切换型号的情况
     *
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @param pedigree               产品谱系
     * @param latestPedigree         最新检查结果产品谱系
     * @param baseDto                baseDto
     * @param msg                    异常消息
     * @return 处理状态
     */
    private boolean handleModelSwitch(WorkCellCheckStartRule workCellCheckStartRule, SubWorkSheet subWorkSheet,WorkSheet workSheet,
                                      Pedigree pedigree, Pedigree latestPedigree, BaseDTO baseDto, StringBuilder msg) {
        if (workCellCheckStartRule.getFlag() == WorkCellStartCheckFlagEnum.MODEL_CHANGE.getCategory() && !latestPedigree.getId().equals(pedigree.getId())) {
            baseDto = updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, subWorkSheet, workSheet);
            if (baseDto.getStatus().equals(Constants.KO)) {
                msg.append(baseDto.getMessage());
                return true;
            }
        }
        return false;
    }


    /**
     * 处理切换工单/子工单的情况
     *
     * @param singleWsProduct        单工投产
     * @param workCellCheckStartRule 工位发起规则
     * @param subWorkSheet           子工单
     * @param workSheet              工单
     * @param latestWorkSheet        最新工单
     * @param latestSubWorkSheet     最新子工单
     * @param switchModel            切换模式：子工单|工单
     * @param baseDto                baseDto
     * @param msg                    异常信息
     * @return 处理状态
     */
    private boolean handleWorkSheetSwitch(String singleWsProduct, WorkCellCheckStartRule workCellCheckStartRule,
                                          SubWorkSheet subWorkSheet, WorkSheet workSheet, WorkSheet latestWorkSheet,
                                          SubWorkSheet latestSubWorkSheet, Boolean switchModel, BaseDTO baseDto, StringBuilder msg) {
        if (ObjectUtils.isEmpty(singleWsProduct) || !Boolean.parseBoolean(singleWsProduct)) {
            // 非第一次开机若切换不同工单生产时需要进行检测
            if (workCellCheckStartRule.getFlag() == WorkCellStartCheckFlagEnum.MAIN_WORKSHEET_CHANGE.getCategory() &&
                    !latestWorkSheet.getId().equals(workSheet.getId()) &&
                    !switchModel) {

                baseDto = updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, subWorkSheet, workSheet);
                if (baseDto.getStatus().equals(Constants.KO)) {
                    msg.append(baseDto.getMessage());
                    return true;
                }
            }

            // 非第一次开机若切换不同子工单生产时需要检测
            if (Objects.nonNull(subWorkSheet) && Objects.nonNull(latestSubWorkSheet)){
                if (workCellCheckStartRule.getFlag() == WorkCellStartCheckFlagEnum.SUB_WORKSHEET_CHANGE.getCategory() &&
                        !latestSubWorkSheet.getId().equals(subWorkSheet.getId()) &&
                        !switchModel) {

                    baseDto = updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, subWorkSheet, null);
                    if (baseDto.getStatus().equals(Constants.KO)) {
                        msg.append(baseDto.getMessage());
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 处理指定时间检测的情况
     *
     * @param workCellCheckStartRule 发起规则
     * @param latestCheckResult      最新检查记录
     * @param subWorkSheet           子工单
     * @param workCell               工位
     * @param msg                    异常信息
     * @return 处理状态
     */
    private boolean handleSpecifiedTimeCheck(WorkCellCheckStartRule workCellCheckStartRule, LatestCheckResult latestCheckResult,
                                             SubWorkSheet subWorkSheet, WorkSheet workSheet,WorkCell workCell, StringBuilder msg) {
        if (workCellCheckStartRule.getFlag() == WorkCellStartCheckFlagEnum.SPECIFIC_TIME.getCategory()) {
            if (!ValidateUtils.isValid(workCellCheckStartRule.getSpecifyTime())) {
                return true;
            }

            // 获取当前时间最贴近上次检测的指定时间
            LocalDateTime lastNeedCheckLocalDate = this.findLastNeedCheckLocalDate(workCellCheckStartRule.getSpecifyTime());

            // 上次检测的指定时间之后存在的检测记录时间
            if (latestCheckResult.getRecordDate().isAfter(lastNeedCheckLocalDate)) {
                return true;
            }

            // 工位发起存在宽放时长，且宽放时长大于0
            if (Objects.isNull(latestCheckResult.getNextCheckDate()) &&
                    workCellCheckStartRule.getExtendType() == Constants.INT_ONE &&
                    workCellCheckStartRule.getExtendTime() > Constants.INT_ZERO) {

                latestCheckResult.setNextCheckDate(LocalDateTime.now().plusSeconds(
                                (long) ((workCellCheckStartRule.getDuration() + workCellCheckStartRule.getExtendTime()) * 3600)))
                        .setExtendTime(workCellCheckStartRule.getExtendTime());
                latestCheckResultRepository.save(latestCheckResult);

                // 记录工位宽放时长
                BeanUtil.getHighestPrecedenceBean(IInspectionService.class)
                        .wcExtendHistoryRecord(subWorkSheet, workSheet, workCellCheckStartRule);
                addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,
                        LocalDateTime.now(),Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
                return true;
            }

            // 生产待检任务
            addInspectTask(new InspectionTaskDTO(workSheet,subWorkSheet, workCellCheckStartRule,
                    LocalDateTime.now(),Boolean.TRUE,workCellCheckStartRule.getExtendTime()));
            msg.append("指定时间达到，需要重新检测");
            return false;
        }
        return false;
    }


    /**
     * 触发 首检 巡检，生成待检任务
     *
     * @param workCellCheckStartRule 发起规则
     * @param subWorkSheet           子工单
     * @param workSheet              工单
     * @return 状态
     */
    public BaseDTO updateLatestCheckResultByNextCheckDate(WorkCellCheckStartRule workCellCheckStartRule, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //生产待检任务
        if (!ObjectUtils.isEmpty(subWorkSheet)) {
            addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule,LocalDateTime.now(),Boolean.TRUE));
        } else {
            addInspectTask(new InspectionTaskDTO(workSheet,workCellCheckStartRule,LocalDateTime.now(),Boolean.TRUE));
        }
        return new BaseDTO(Constants.KO, WorkCellStartCheckFlagEnum.getRemark(workCellCheckStartRule.getFlag()) + ", 需重新检测;");
    }

    /**
     * 获取前时间 最贴近上次检测的指定时间
     *
     * @param specifyTimes 指定时间列表
     * @return java.time.LocalDateTime
     * <AUTHOR>
     * @date 2022/8/23
     */
    public LocalDateTime findLastNeedCheckLocalDate(String specifyTimes) {
        LocalDateTime nowDateTime = LocalDateTime.now();
        //获取当前时间 拼接成-》HH:mm 格式
        StringBuilder nowTime = new StringBuilder();
        nowTime.append(nowDateTime.getHour() > Constants.INT_NINE ? nowDateTime.getHour() : "0" + nowDateTime.getHour()).append(":").append(nowDateTime.getMinute() > Constants.INT_NINE ? nowDateTime.getMinute() : "0" + nowDateTime.getMinute());
        //指定时间
        List<String> specifyTimeList = Arrays.stream(specifyTimes.split(Constants.STR_COMMA)).collect(Collectors.toList());
        specifyTimeList.add(nowTime.toString());
        //排序升序
        specifyTimeList = specifyTimeList.stream().distinct().sorted().collect(Collectors.toList());

        int count = 0;
        boolean min = false;
        String lastTime = nowTime.toString();

        for (String specifyTime : specifyTimeList) {

            if (specifyTime.equals(nowTime.toString()) && count == 0) {
                min = true;
                lastTime = specifyTimeList.get(specifyTimeList.size() - Constants.INT_ONE);
                break;
            }
            if (specifyTime.equals(nowTime.toString())) {
                break;
            }
            lastTime = specifyTime;
            count++;
        }

        //当前时间为指定时间中最小,说明最靠近上次检测时间得往前推移一天
        LocalDateTime lastDateTime = min ? nowDateTime.plusDays(Constants.NEGATIVE_ONE) : nowDateTime;
        //截取获取 小时 以及 分钟
        List<Integer> hourMinutes = Arrays.stream(Arrays.stream(lastTime.split(":")).mapToInt(Integer::parseInt).toArray()).boxed().collect(Collectors.toList());
        //构造 当前时间 最贴近上次检测的指定时间
        return LocalDateTime.of(lastDateTime.getYear(), lastDateTime.getMonthValue(), lastDateTime.getDayOfMonth(), hourMinutes.get(Constants.INT_ZERO), hourMinutes.get(Constants.INT_ONE));
    }


    /**
     * 首检 巡检添加 待检任务
     *
     * @param inspectionTaskDto 待检任务Dto
     * @return void
     * <AUTHOR>
     * @date 2023/5/5
     */
    public void addInspectTask(InspectionTaskDTO inspectionTaskDto) {

        InspectTask inspectTask = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(inspectionTaskDto.getWorkCell().getId(), inspectionTaskDto.getCategory(),
                ObjectUtils.isEmpty(inspectionTaskDto.getVariety()) ? null : inspectionTaskDto.getVariety().getId(), Boolean.FALSE, Constants.LONG_ZERO).orElse(new InspectTask());

        inspectTask.setCategory(inspectionTaskDto.getCategory())
                .setWorkCell(inspectionTaskDto.getWorkCell())
                .setVarietyId(Optional.ofNullable(inspectionTaskDto.getVariety()).map(VarietyDTO::getId).orElse(null))
                .setWorkSheet(inspectionTaskDto.getWorkSheet())
                .setSubWorkSheet(inspectionTaskDto.getSubWorkSheet())
                .setTodoInspectedTime(Objects.nonNull(inspectionTaskDto.getTodoInspectedTime())
                        ?inspectionTaskDto.getTodoInspectedTime():LocalDateTime.now())
                .setIsExtend(inspectionTaskDto.getExtend())
                .setWorkCellCheckStartRule(inspectionTaskDto.getWorkCellCheckStartRule())
                .setCache(null)
                .setStatus(Boolean.FALSE);
        inspectTaskRepository.save(inspectTask);
    }

    /**
     * 记录工位发起规则中首检巡检 宽放记录
     *
     * @param subWorkSheet           子工单
     * @param workSheet              工单
     * @param workCellCheckStartRule 工位发起规则
     */
    @Override
    public void wcExtendHistoryRecord(SubWorkSheet subWorkSheet, WorkSheet workSheet, WorkCellCheckStartRule workCellCheckStartRule) {

        if (workCellCheckStartRule.getExtendRule() <= Constants.INT_ZERO) {
            return;
        }

        WorkCellExtendHistory workCellExtendHistory = workCellExtendHistoryRepository
                .findByWorkCellIdAndCategoryAndVarietyIdAndResultAndDeleted(workCellCheckStartRule.getWorkCell().getId(),
                        workCellCheckStartRule.getCategory(), workCellCheckStartRule.getVarietyId(), Boolean.FALSE,
                        Constants.LONG_ZERO)
                .orElse(new WorkCellExtendHistory());

        if (Objects.isNull(workCellExtendHistory.getId())) {
            workCellExtendHistory
                    .setSubWorkSheet(subWorkSheet)
                    .setWorkSheet(workSheet)
                    .setWorkCell(workCellCheckStartRule.getWorkCell())
                    .setCategory(workCellCheckStartRule.getCategory())
                    .setVarietyId(workCellCheckStartRule.getVarietyId())
                    .setExtendTime(workCellCheckStartRule.getExtendTime())
                    .setStartTime(LocalDateTime.now())
                    .setExtendStepCategory(workCellCheckStartRule.getExtendStepCategory())
                    .setExtendRule(workCellCheckStartRule.getExtendRule())
                    .setDeleted(Constants.LONG_ZERO);
            workCellExtendHistoryRepository.save(workCellExtendHistory);
        }
    }
}
