package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rworker.process.dto.*;
import net.airuima.rbase.dto.rworker.process.dto.general.FacilityGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.ProcessDocumentGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WsStepGetInfo;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rmps.RbaseRmpsProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.dynamic.IDynamicService;
import net.airuima.rworker.service.rworker.event.IEventService;
import net.airuima.rworker.service.rworker.facility.*;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.rworker.service.rworker.oem.IOemService;
import net.airuima.rworker.service.rworker.pedigree.IRworkerPedigreeConfigService;
import net.airuima.rworker.service.rworker.process.*;
import net.airuima.rworker.service.rworker.quality.IEnvironmentService;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/31
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ContainerProcessRequestServiceImpl implements IContainerProcessRequestService {

    private final Logger log = LoggerFactory.getLogger(ContainerProcessRequestServiceImpl.class);
    private static final String SUB_WORK_SHEET_WAIT_FOR_ANALYZE = "subWorkSheetWaitForAnalyze";
    private static final String ERR_MSG = "容器处于待分析中!";
    private static final String ERR_MSG2 = "容器处于待维修中!";
    private static final String SUB_WORK_SHEET_WAIT_FOR_MAINTAIN = "subWorkSheetWaitForMaintain";
    private static final String REQUEST_CONTAINER_IS_NOT_BINDING = "requestContainerIsNotBinding";
    private static final String REQUEST_CONTAINER_IS_NOT_BINDING_MSG = "请求工序生产的容器尚未绑定";
    private static final String REPAIRE_ANALYSIS = "RepaireAnalysis";


    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private OnlineReworkRuleRepository onlineReworkRuleRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private IBatchProcessRequestService[] batchProcessRequestServices;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IEnvironmentService[] environmentServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IFacilityCalibrateService[] facilityCalibrateServices;
    @Autowired
    private IFacilityInspectionService[] facilityInspectionServices;
    @Autowired
    private IFacilityMaintainService[] facilityMaintainServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IToDoStepConfigService[] toDoStepConfigServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private ISnProcessRequestService[] snProcessRequestServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private IOemService[] oemServices;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private RbaseRmpsProxy rbaseRmpsProxy;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private IParallelStepProcessService[] parallelStepProcessServices;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private IRworkerPedigreeConfigService[] rworkerPedigreeConfigServices;

    /**
     * 验证绑定的容器编码是否合规
     *
     * @param rworkerBindContainerValidateRequestDTO 验证待绑定容器合规性参数DTO
     * @return net.airuima.web.rest.rworker.process.dto.RworkerBindContainerValidateGetDTO 待绑定容器信息
     */
    @Override
    @Klock(keys = {"#rworkerBindContainerValidateRequestDTO.bindContainerCode"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public RworkerBindContainerValidateGetDTO validateBindContainer(RworkerBindContainerValidateRequestDTO rworkerBindContainerValidateRequestDTO) {
        List<String> stepRequestContainerCodeList = rworkerBindContainerValidateRequestDTO.getStepRequestContainerCodeList();
        String bindContainerCode = rworkerBindContainerValidateRequestDTO.getBindContainerCode();
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(bindContainerCode, Constants.LONG_ZERO);
        //绑定新容器时验证容器是否可用
        Container container = containerOptional.orElse(null);
        if (null == container) {
            container = new Container().setName(bindContainerCode).setCode(bindContainerCode).setStatus(Constants.FALSE);
            container.setDeleted(Constants.LONG_ZERO);
            container = containerRepository.save(container);
        }
       //获取 容器的最新投产记录
        Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerCodeAndDeletedOrderByIdDesc(container.getCode(),  Constants.LONG_ZERO);
        containerDetailOptional.ifPresent(containerDetail -> {
            //如果待绑定的容器存在未解绑的容器详情且不属于容器复用的情况则提示容器不可进行绑定
            if (containerDetail.getStatus() == ConstantsEnum.BINDING.getCategoryName() && (!ValidateUtils.isValid(stepRequestContainerCodeList) || !stepRequestContainerCodeList.contains(bindContainerCode))) {
                throw new ResponseException("error.containerIsBinding", "当前容器已被占用!");
            }
            //验证抽检，终检，末检任务（不区分容器是否绑定，都要验证当前容器是否存在待检任务）
            qualityServices[0].checkContainerCodePQCOrFQCOrLQCTdoTask(containerDetail.getContainerCode());
            Optional<CheckHistory> checkHistoryOptional = !ObjectUtils.isEmpty(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Constants.INT_ZERO, Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(containerDetail.getBatchWorkDetail().getWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Constants.INT_ZERO, Constants.LONG_ZERO);
            if (checkHistoryOptional.isPresent()) {
                throw new ResponseException("error.containerWaitCheckHistory", "容器需要进行检测记录处理!");
            }
        });
        //验证容器是否处于维修分析完成
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateBindContainerMaintain(container.getId());
        //验证容器是否复检完成
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateContainerReinspect(container.getId());
        return new RworkerBindContainerValidateGetDTO(container);
    }

    /**
     * 获取容器待做工序信息
     *
     * @param rworkerContainerToDoStepRequestDTO 容器请求工序生产参数
     * @return net.airuima.web.rest.rworker.process.dto.RworkerContainerToDoStepGetDTO 容器待做工序信息
     */
    @Override
    public RworkerContainerToDoStepGetDTO containerToDoStep(RworkerContainerToDoStepRequestDTO rworkerContainerToDoStepRequestDTO) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        RworkerContainerToDoStepGetDTO containerNextToDoStepTempInfo = new RworkerContainerToDoStepGetDTO();
        //验证请求生产的容器列表合规性
        ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo = this.validateRequestContainer(rworkerContainerToDoStepRequestDTO, containerNextToDoStepTempInfo, subWsProductionMode);
        Container container = containerRepository.findByCodeAndDeleted(rworkerContainerToDoStepRequestDTO.getRequestContainerCode(), Constants.LONG_ZERO).orElse(null);

        List<ContainerDetail> containerDetailList = Objects.nonNull(containerNextToDoStepBaseInfo.getSubWorkSheet())
                ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(containerNextToDoStepBaseInfo.getSubWorkSheet().getId(), container.getId(), Constants.LONG_ZERO)
                : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(containerNextToDoStepBaseInfo.getWorkSheet().getId(), container.getId(), Constants.LONG_ZERO);

        //获取工位可生产的工序列表
        List<Step> stepList = workCellStepRepository.findByWorkCellId(rworkerContainerToDoStepRequestDTO.getWorkCellId(), Constants.LONG_ZERO);
        List<Long> stepIdList = stepList.stream().map(Step::getId).toList();
        List<WsStep> wsStepList = containerNextToDoStepBaseInfo.getWsStepList();
        //获取当前(子)工单批量详情列表
        List<BatchWorkDetail> batchWorkDetailList = Objects.nonNull(containerNextToDoStepBaseInfo.getSubWorkSheet())
                ? batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(containerNextToDoStepBaseInfo.getSubWorkSheet().getId(), Constants.LONG_ZERO)
                : batchWorkDetailRepository.findByWorkSheetIdAndDeleted(containerNextToDoStepBaseInfo.getWorkSheet().getId(), Constants.LONG_ZERO);
        //获取当前工位可生产的工序
        WsStep nextToDoWsStep = this.findNextTodoWsStep(containerNextToDoStepBaseInfo, stepList, containerDetailList,batchWorkDetailList);
        Step nextToDoStep = Objects.nonNull(nextToDoWsStep) ? nextToDoWsStep.getStep() : null;
        if (null == nextToDoStep) {
            if (!CollectionUtils.isEmpty(containerNextToDoStepBaseInfo.getNextTodoStepIdList())) {
                List<Step> mayNextStepList = stepRepository.findByIdInAndDeleted(containerNextToDoStepBaseInfo.getNextTodoStepIdList(),Constants.LONG_ZERO);
                Step mayNextStep = mayNextStepList.stream().filter(nextMayToDoStep->stepIdList.contains(nextMayToDoStep.getId())).findFirst().orElse(null);
                if(Objects.nonNull(mayNextStep)){
                    wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(mayNextStep.getId())).findFirst().ifPresent(wsStep -> {
                        wsStepList.stream().filter(wsStep1 -> wsStep.getPreStepId().contains(wsStep1.getStep().getId().toString())).findFirst().ifPresent(wsStep1 -> {
                            throw new ResponseException("error.notExistTodoStep", "前置"+TransferModeEnum.getDescriptionByValue(wsStep1.getTransferType())+"工序尚未完成，当前工位不可生产【"+mayNextStep.getName()+"】工序");
                        });
                    });
                }else {
                    throw new ResponseException("error.notExistTodoStep", "容器下个可能的待做工序为【" + mayNextStepList.stream().map(Step::getName).collect(Collectors.joining(Constants.STR_COMMA)) + "，当前工位不可生产该工序");
                }
            }
            throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
        }
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        //验证外协工序不能进行投产
        oemServices[0].validProcessOemStep(nextToDoWsStep);
        //容器工序请求验证是否存在宽放记录
        qualityServices[0].validContainerWorkCellExtendHistory(containerNextToDoStepBaseInfo.getSubWorkSheet(),containerNextToDoStepBaseInfo.getWorkSheet(), nextToDoWsStep,container.getCode());
        //验证工位首检巡检是否存在待检任务
        BaseDTO baseDto = qualityServices[0].checkWorkCellFaiOrIpqcTodoTask(rworkerContainerToDoStepRequestDTO.getWorkCellId());
        if (baseDto.getStatus().equals(Constants.KO)){
            // 返回包含错误信息的DTO而不是抛异常
            RworkerContainerToDoStepGetDTO errorDTO = new RworkerContainerToDoStepGetDTO();
            errorDTO.setKey(baseDto.getStatus()).setMessage(baseDto.getMessage());
            return errorDTO;
        }
        WorkSheet workSheet = containerNextToDoStepBaseInfo.getWorkSheet();
        //初始化请求生产工序后的通用信息
        RworkerStepProcessBaseDTO stepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(rworkerContainerToDoStepRequestDTO.getStaffId(), Constants.LONG_ZERO);
        WorkCell workCell = workCellRepository.getReferenceById(rworkerContainerToDoStepRequestDTO.getWorkCellId());
        WorkFlow workFlow = null != nextToDoWsStep.getWorkFlow() ? nextToDoWsStep.getWorkFlow() : containerNextToDoStepBaseInfo.getWorkSheet().getWorkFlow();
        stepProcessBaseDTO.setWsStepList(containerNextToDoStepBaseInfo.getWsStepList()).setWorkFlow(workFlow).setStaffDTO(staffDTO).setStep(nextToDoStep).setWsStep(nextToDoWsStep).setWorkSheet(containerNextToDoStepBaseInfo.getWorkSheet()).setSubWorkSheet(containerNextToDoStepBaseInfo.subWorkSheet).setSubWsProductionMode(subWsProductionMode).setWorkCell(workCell);
        RworkerContainerToDoStepGetDTO containerNextToDoStepInfo = new RworkerContainerToDoStepGetDTO(nextToDoWsStep);
        //验证工序时间间隔是否合规
        BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateContainerStepInterval(stepProcessBaseDTO, containerNextToDoStepTempInfo.getContainerInfo().getId(), subWsProductionMode);
        if (Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException(baseResultDTO.getKey(), baseResultDTO.getMessage());
        }
        //判断当前投产工序是否存在转工艺或者为在线返工
        if (workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()
                || !nextToDoWsStep.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())
                || workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()) {
            containerNextToDoStepInfo.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
        }
        String onlineMaintainMaterialControl = null;
        if (workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()) {
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        } else if (workSheet.getCategory() == ConstantsEnum.WORK_SHEET_OFFLINE_CATEGORY.getCategoryName()) {
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL);
        } else if (!nextToDoWsStep.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())) {
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL);
        }
        containerNextToDoStepInfo.setOnlineMaintainMaterialControl(StringUtils.isNotBlank(onlineMaintainMaterialControl) ? Integer.parseInt(onlineMaintainMaterialControl) : net.airuima.constant.Constants.INT_ZERO);
        //验证指定工单工序工位是否合规
        toDoStepStepValidateServices[0].validWsStepWorkCell(stepProcessBaseDTO);
        //验证员工技能是否匹配当前待做工序
        toDoStepStepValidateServices[0].validateStaffSkill(stepProcessBaseDTO);
        //验证工位GRR是否合规
        toDoStepStepValidateServices[0].validateWorkCellGrr(stepProcessBaseDTO);
        //验证环境-温湿度
        BaseWaringDTO baseWaringDTO = environmentServices[0].validateHumiture(stepProcessBaseDTO);
        if (null != baseWaringDTO) {
            containerNextToDoStepInfo.setKey(baseWaringDTO.getKey()).setMessage(baseWaringDTO.getMessage());
        }
        containerNextToDoStepInfo.setWorkFlowId(workFlow.getId());
        //验证环境-洁净度
        environmentServices[0].validateCleanliness(stepProcessBaseDTO);
        containerNextToDoStepInfo.setContainerInfo(containerNextToDoStepTempInfo.getContainerInfo()).setWorkSheetInfo(containerNextToDoStepTempInfo.getWorkSheetInfo());
        //获取流转数
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).getNextToDoStepNumber(containerNextToDoStepInfo, containerNextToDoStepBaseInfo, nextToDoWsStep, nextToDoStep, stepProcessBaseDTO);
        //获取工位工序设备信息
        List<FacilityGetInfo> facilityGetInfoList = facilityServices[0].findStepFacilityInfo(rworkerContainerToDoStepRequestDTO.getWorkCellId(), nextToDoStep.getId());
        containerNextToDoStepInfo.setFacilityInfoList(facilityGetInfoList);
        stepProcessBaseDTO.setFacilityGetInfoList(facilityGetInfoList);
        List<Long> facilityIdList = CollectionUtils.isEmpty(facilityGetInfoList) ? null : facilityGetInfoList.stream().map(FacilityGetInfo::getId).collect(Collectors.toList());
        //验证请求的待做工序的基础信息是否存在未处理的事件
        eventServices[0].validateRequestStepExistUnProcessedEvent(stepProcessBaseDTO);
        //验证工位设备校准
        facilityCalibrateServices[0].validateFacilityCalibrate(rworkerContainerToDoStepRequestDTO.getWorkCellId(), facilityIdList);
        //验证设备基础状态
        facilityInspectionServices[0].validateFacilityBaseStatus(facilityIdList);
        //验证设备点检等是否合规
        facilityInspectionServices[0].validateFacilityPointInspection(facilityIdList);
        //验证设备巡检等是否合规
        facilityInspectionServices[0].validateFacilityPatrolInspection(facilityIdList);
        //验证设备是否存在逾期维保任务
        facilityMaintainServices[0].validateFacilityMaintain(facilityIdList);
        //获取工序不良信息
        containerNextToDoStepInfo.setUnqualifiedItemInfoList(qualityServices[0].findStepUnqualifiedItemInfo(nextToDoStep.getId(), containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow.getId(), containerNextToDoStepBaseInfo.getWorkSheet().getClientId()));
        //获取BOM物料清单列表
        containerNextToDoStepInfo.setBomMaterialInfoList(materialServices[0].findBomMaterialInfo(containerNextToDoStepBaseInfo.getWorkSheet().getId()));
        //返回工单工序快照
        containerNextToDoStepInfo.setWsStepInfoList(containerNextToDoStepTempInfo.getWsStepInfoList());
        //设置工序动态数据信息
        containerNextToDoStepInfo.setStepDynamicDataGetDto(dynamicServices[0].getStepDynamicDataInfo(containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow, nextToDoStep));
        containerNextToDoStepInfo.setStepDynamicDataGetVisibleDtoList(dynamicServices[0].getContainerStepDynamicDataVisibleInfo(containerNextToDoStepBaseInfo.getSubWorkSheet(), containerNextToDoStepBaseInfo.getWorkSheet(), nextToDoStep, containerNextToDoStepTempInfo.getContainerInfo().getId()));
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        containerNextToDoStepInfo.setIsFeedingMaterial(nextToDoWsStep.getIsControlMaterial()).setMaterialControlLevel(materialControlLevel);
        //获取工序技术指标及SOP
        PedigreeStepSpecification pedigreeStepSpecification = toDoStepConfigServices[0].findStepSpecificationSop(containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow, nextToDoStep, containerNextToDoStepBaseInfo.getWorkSheet().getClientId());
        if (null != pedigreeStepSpecification) {
            //设置技术指标
            containerNextToDoStepInfo.setSpecification(pedigreeStepSpecification.getQualification());
            //设置工序SOP信息
            containerNextToDoStepInfo.setStepSopInfoList(toDoStepConfigServices[0].getStepSpecificationSop(pedigreeStepSpecification));
        }
        //获取BOM里定义的工艺图列表
        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(workSheet.getBomInfoId());
        if (!CollectionUtils.isEmpty(documentDTOList)) {
            containerNextToDoStepInfo.setProductsCraftInfoList(documentDTOList.stream().map(ProcessDocumentGetInfo::new).collect(Collectors.toList()));
        }
        //获取当前待做工序的易损件信息
        containerNextToDoStepInfo.setWearingPartGroupInfoList(wearingPartServices[0].getWearingPartInfo(stepProcessBaseDTO));
        //获取工序检查配置信息
        containerNextToDoStepInfo.setPedigreeStepInspectionConfigDtoList(rworkerPedigreeConfigServices[0].getPedigreeStepInspectionConfigs(workSheet.getClientId(),
                workSheet.getPedigree(),workFlow,nextToDoStep));
        //获取容器在当前工序可投产的SN信息列表
        snProcessRequestServices[0].findToDoContainerSn(containerNextToDoStepInfo.getContainerInfo().getId(), containerNextToDoStepInfo, stepProcessBaseDTO);
        //容器烘烤温循相关
        bakeCycleBakeAgeingModelServices[0].bakeCycleBakeAgeingContainerInfo(containerNextToDoStepInfo, stepProcessBaseDTO);
        //请求工序时更新设备状态为正常运行
        facilityServices[0].updateFacilityStatus(facilityIdList, ConstantsEnum.FACILITY_STATUS_RUNNING.getCategoryName());
        return containerNextToDoStepInfo;
    }

    /**
     * 判断请求工序容器的待做工序
     *
     * @param containerNextToDoStepBaseInfo 工序请求缓存区的基础信息，避免反复查询
     * @param stepList                      工位绑定的工序列表
     * @param containerDetailList           容器已完成的工序列表
     * @param batchWorkDetailList 批量详情列表
     * @return 待投产工序快照
     */
    public WsStep findNextTodoWsStep(ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo, List<Step> stepList, List<ContainerDetail> containerDetailList,List<BatchWorkDetail> batchWorkDetailList) {
        ContainerDetail latestCompletedContainerDetail = containerNextToDoStepBaseInfo.getLatestContainerDetail();
        List<WsStep> wsStepList = containerNextToDoStepBaseInfo.getWsStepList();
        // 校验工艺快照列表和工序列表
        if (CollectionUtils.isEmpty(wsStepList) || CollectionUtils.isEmpty(stepList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(containerDetailList) || Objects.isNull(latestCompletedContainerDetail)) {
            return null;
        }
        List<List<WsStep>> lists = commonService.dealWsStep(containerNextToDoStepBaseInfo.getWsStepList());
        Map<Long, ContainerDetail> containerDetailGroup = containerDetailList.stream().collect(Collectors.groupingBy(containerDetail -> containerDetail.getBatchWorkDetail().getStep().getId(), Collectors.collectingAndThen(
                Collectors.toList(), list -> list.get(0))));
        for (List<WsStep> wsSteps : lists) {
            Set<WsStep> mayNextToDoWsSteps = wsSteps.stream().filter(wsStep -> stepList.stream().anyMatch(step -> step.getId().equals(wsStep.getStep().getId()))).collect(Collectors.toSet());
            if (!ValidateUtils.isValid(mayNextToDoWsSteps)) {
                continue;
            }
            for (WsStep wsStep : mayNextToDoWsSteps) {

                if (StringUtils.isBlank(wsStep.getPreStepId()) || !wsStep.getPreStepId().contains(latestCompletedContainerDetail.getBatchWorkDetail().getStep().getId().toString())) {
                    continue;
                }
                //如果当前工序是连续型的并行工序时需要判断与之同级的并行工序（及儿子工序）的完成情况
                if (wsStep.getTransferType() == TransferModeEnum.CONTINUOUS.getValue()
                        && parallelStepProcessServices[0].findContainerNextTodoWsStepContinueWhenContinuousParallelStep(containerNextToDoStepBaseInfo.getSubWorkSheet()
                        , containerNextToDoStepBaseInfo.getWorkSheet()
                        , containerNextToDoStepBaseInfo.getWsStepList(), wsStep, containerDetailList,latestCompletedContainerDetail)) {
                    continue;
                }
                //判断下个待做工序如果是分拆型的并行工序时需要判断与之同级的并行工序（及儿子工序）的完成情况
                if(wsStep.getTransferType() == TransferModeEnum.SPLIT.getValue() && parallelStepProcessServices[0].findContainerNextTodoWsStepContinueWhenSplitParallelStep(containerNextToDoStepBaseInfo.getSubWorkSheet()
                        , containerNextToDoStepBaseInfo.getWorkSheet()
                        , containerNextToDoStepBaseInfo.getWsStepList(), wsStep, containerDetailList,latestCompletedContainerDetail)){
                    continue;
                }
                //若当前工序的前置工序存在多个且前置工序未独立流转的工序时必须要等前置所有工序全部完成才能生产当前工序且当前容器必须是前置工序里流转数最小的工序里的容器
                if(StringUtils.isNotBlank(wsStep.getPreStepId()) && wsStep.getPreStepId().split(Constants.STR_COMMA).length > Constants.INT_ONE){
                    //获取当前工序的前置工序列表
                    List<WsStep> preWsStepList = wsStepList.stream().filter(wsStepTemp -> wsStep.getPreStepId().contains(wsStepTemp.getStep().getId().toString())).toList();
                    //如果前置工序存在独立流转的工序，则需要判断前置工序是否全部完成且当前容器必须是前置工序里流转数最小的工序里的容器
                    if(preWsStepList.stream().anyMatch(preWsStep -> preWsStep.getTransferType() == TransferModeEnum.INDEPENDENT.getValue())){
                        //获取前置工序的批量详情列表
                        List<BatchWorkDetail> preBatchWorkDetailList = batchWorkDetailList.stream().filter(batchWorkDetail -> preWsStepList.stream().anyMatch(preWsStep -> preWsStep.getStep().getId().equals(batchWorkDetail.getStep().getId()))).toList();
                        if(CollectionUtils.isEmpty(preBatchWorkDetailList) || preBatchWorkDetailList.stream().anyMatch(batchWorkDetail -> batchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName())){
                            continue;
                        }
                        //判断流转数是否都相等
                        boolean allEqual = preBatchWorkDetailList.stream()
                                .mapToInt(BatchWorkDetail::getTransferNumber)
                                .allMatch(num -> num == preBatchWorkDetailList.get(0).getTransferNumber());
                        BatchWorkDetail targetBatchWorkDetail;
                        if(allEqual){
                            // 如果流转数都相等，取ID最大的批量详情
                            targetBatchWorkDetail = preBatchWorkDetailList.stream()
                                    .max(Comparator.comparing(BatchWorkDetail::getId))
                                    .get();
                        } else {
                            // 否则取流转数最小的批量详情
                            targetBatchWorkDetail = preBatchWorkDetailList.stream()
                                    .min(Comparator.comparing(BatchWorkDetail::getTransferNumber))
                                    .get();
                        }
                        // 如果当前容器不是目标批量详情里的容器，则不能生产当前工序
                        if(!targetBatchWorkDetail.getId().equals(latestCompletedContainerDetail.getBatchWorkDetail().getId())){
                            continue;
                        }
                    }
                    //如果前置工序存在拆分流转的工序，若前置工序一个都没做那么继续判断下个可生产工序
                    if(preWsStepList.stream().anyMatch(preWsStep -> preWsStep.getTransferType() == TransferModeEnum.SPLIT.getValue()) && preWsStepList.stream().allMatch(preWsStep->!containerDetailGroup.containsKey(preWsStep.getStep().getId()))){
                        continue;
                    }
                    //如果前置工序存在连续流转的工序，若前置工序只要一个没做那么继续判断下个可生产工序
                    if(preWsStepList.stream().anyMatch(preWsStep -> preWsStep.getTransferType() == TransferModeEnum.CONTINUOUS.getValue()) && preWsStepList.stream().anyMatch(preWsStep->!containerDetailGroup.containsKey(preWsStep.getStep().getId()))){
                        continue;
                    }
                }
                //前置工序未完成则继续判断下一个匹配到待做工序
                if (StringUtils.isNotBlank(wsStep.getPreStepId()) && Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA))
                        .anyMatch(preStepId -> containerDetailGroup.get(Long.parseLong(preStepId)) == null)){
                    continue;
                }
                return wsStep;
            }
        }
        return null;
    }

    /**
     * 计算当前容器投产数
     *
     * @param containerNextToDoStepInfo     容器待做工序信息
     * @param containerNextToDoStepBaseInfo 待做工序基础信息
     * @param nextToDoWsStep                待做工序定制工序
     * @param nextToDoStep                  待做工序
     * <AUTHOR>
     * @date 2023/3/18
     **/
    @Override
    public void getNextToDoStepNumber(RworkerContainerToDoStepGetDTO containerNextToDoStepInfo, ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo,
                                      WsStep nextToDoWsStep, Step nextToDoStep, RworkerStepProcessBaseDTO stepProcessBaseDTO) {
        //下个待做工序投产数
        int nextToDoStepNumber = containerNextToDoStepBaseInfo.getTransferNumber();
        //若下个待做工序类型为调整工序则需要根据调整规则获取投产数
        if (nextToDoWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName() || nextToDoWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName()) {
            nextToDoStepNumber = this.findOnlineReworkStepTransferNumber(nextToDoStep.getId(), containerNextToDoStepBaseInfo.getLatestContainerDetail().getId(), stepProcessBaseDTO);
        }
        //投产数需要乘以投产比例作为最终投产数(小数向上取整)
        nextToDoStepNumber = (int) Math.ceil(NumberUtils.multiply(nextToDoStepNumber, nextToDoWsStep.getInputRate()).doubleValue());
        containerNextToDoStepInfo.setNumber(nextToDoStepNumber);
    }

    /**
     * 获取容器请求模式下的在线调整工序的投产数量
     *
     * @param stepId             待做工序主键ID
     * @param requestContainerId 请求容器主键ID
     * @return int 在线调整数量
     */
    @Override
    public int findOnlineReworkStepTransferNumber(long stepId, Long requestContainerId, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(stepId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemList)) {
            throw new ResponseException("error.onlineReworkRuleNotExist", "在线调整工序未配置调整规则");
        }
        rworkerStepProcessBaseDTO.setOnlineReworkUnQualifiedItemList(unqualifiedItemList);
        Long sumNpiNumber = containerDetailUnqualifiedItemRepository.findSumNumberByContainerDetailIdAndUnqualifiedItemInAndDeleted(requestContainerId, unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        return null != sumNpiNumber ? sumNpiNumber.intValue() : Constants.INT_ZERO;
    }

    /**
     * 验证请求生产的容器列表合规性
     *
     * @param containerToDoStepRequestDTO    容器请求工序参数DTO
     * @param rworkerContainerToDoStepGetDTO 容器请求工序返回DTO
     * @param subWsProductionMode            系统配置的投产粒度(子工单或者工单)
     * @return ContainerNextToDoStepBaseInfo
     */
    private ContainerNextToDoStepBaseInfo validateRequestContainer(RworkerContainerToDoStepRequestDTO containerToDoStepRequestDTO, RworkerContainerToDoStepGetDTO rworkerContainerToDoStepGetDTO, boolean subWsProductionMode) {

        Container container = containerRepository.findByCodeAndDeleted(containerToDoStepRequestDTO.getRequestContainerCode(), Constants.LONG_ZERO).orElse(null);
        if (null == container) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, "请求工序生产的容器中存在尚未绑定的容器");
        }
        //验证请求的容器是否存在缓存，如果有缓存则需要提示
        rworkerCacheServices[0].validateCacheWhenRequestTodoStep(Constants.INT_TWO, container.getCode());
        ContainerDetail containerDetail = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeleted(container.getCode(), Constants.INT_ONE, Constants.LONG_ZERO);
        WorkSheet workSheet;
        SubWorkSheet subWorkSheet;
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo = new ContainerNextToDoStepBaseInfo();
        //验证容器是否存在未完成的复检
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateContainerReinspect(container.getId());
        //查询不到绑定状态的容器详情时则需要进一步获取维修历史的工单及判断容器是否可以投产
        if (null == containerDetail) {
            if(!FuncKeyUtil.checkApi(REPAIRE_ANALYSIS)){
                throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
            }
            //验证容器是否处于维修分析完成
            BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateRequestContainerMaintain(subWsProductionMode, Boolean.FALSE, container.getId(), rworkerStepProcessBaseDTO);
            ContainerDetail latestUnBindingContainerDetail = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(container.getCode(), ConstantsEnum.UNBIND.getCategoryName(), Constants.LONG_ZERO).orElse(new ContainerDetail());
            workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
            subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
            containerNextToDoStepBaseInfo.setTransferNumber(latestUnBindingContainerDetail.getInputNumber());
        } else {
            //验证容器是否处于维修分析完成
            BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateRequestContainerMaintain(subWsProductionMode, Boolean.TRUE, container.getId(), rworkerStepProcessBaseDTO);
            Optional<MaintainHistoryDTO> maintainHistoryOptional = rbaseMaintainHistoryProxy.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerDetail.getContainer().getId(), Constants.LONG_ZERO);
            maintainHistoryOptional.ifPresent(maintainHistory -> {
                if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
                    throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
                }
                if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
                    throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
                }
            });
            workSheet = subWsProductionMode ? containerDetail.getBatchWorkDetail().getSubWorkSheet().getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
            subWorkSheet = subWsProductionMode ? containerDetail.getBatchWorkDetail().getSubWorkSheet() : null;
            containerNextToDoStepBaseInfo.setTransferNumber(containerDetail.getTransferNumber());
        }
        List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : null;
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (null == wsStepList) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }
        //验证工单状态
        if (null != subWorkSheet) {
            batchProcessRequestServices[0].validateSubWorkSheetStatus(subWorkSheet);
        } else {
            batchProcessRequestServices[0].validateWorkSheetStatus(workSheet);
        }
        RworkerToDoWsGetDTO rworkerToDoWsGetDTO;
        if (subWsProductionMode) {
            assert subWorkSheet != null;
            rworkerToDoWsGetDTO = new RworkerToDoWsGetDTO(subWorkSheet);
        } else {
            rworkerToDoWsGetDTO = new RworkerToDoWsGetDTO(workSheet);
        }
        rworkerContainerToDoStepGetDTO.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
        rworkerContainerToDoStepGetDTO.setWorkSheetInfo(rworkerToDoWsGetDTO);
        rworkerContainerToDoStepGetDTO.setContainerInfo(new RworkerContainerToDoStepGetDTO.ContainerInfo(container));
        //默认待投产工序列表为工单的第一个工序
        List<Long> nextToDoStepIdList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList());
        //若当前容器处于绑定状态则根据容器最新完成的工序及工单工序快照获取下个待投产工序列表
        if (null != containerDetail) {
            //验证抽检，终检，末检任务
            qualityServices[0].checkContainerCodePQCOrFQCOrLQCTdoTask(containerDetail.getContainerCode());
            Optional<CheckHistory> checkHistoryOptional = !ObjectUtils.isEmpty(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Constants.INT_ZERO, Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndContainerCodeAndCategoryGreaterThanAndDealWayAndDeleted(containerDetail.getBatchWorkDetail().getWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Constants.INT_ZERO, Constants.LONG_ZERO);
            if (checkHistoryOptional.isPresent()) {
                throw new ResponseException("error.containerWaitCheckHistory", "容器需要进行检测记录处理!");
            }
            WsStep latestFinishedWsStep = wsStepList.stream().filter(wsStep -> containerDetail.getBatchWorkDetail().getStep().getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
            if (null == latestFinishedWsStep || StringUtils.isBlank(latestFinishedWsStep.getAfterStepId())) {
                throw new ResponseException("error.afterStepNotExist", "请求容器不存在后置待生产工序");
            }
            nextToDoStepIdList = Arrays.stream(latestFinishedWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }
        return containerNextToDoStepBaseInfo.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setNextTodoStepIdList(nextToDoStepIdList).setWsStepList(wsStepList).setLatestContainerDetail(containerDetail);
    }

    /**
     * 维修分析的容器请求生产工序时验证维修数据获取对应投产工单
     *
     * @param subWsProductionMode       投产粒度(true:子工单,false:工单)
     * @param isBindingContainerRequest 是否为绑定状态的容器请求工序
     * @param containerId               请求容器主键ID
     * @param rworkerStepProcessBaseDTO 生产过程通用基础新信息
     */
    @Override
    public void validateRequestContainerMaintain(boolean subWsProductionMode, boolean isBindingContainerRequest, long containerId, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //通过容器获取最新容器维修记录
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO).orElse(null);
        //容器维修记录为空且容器不存在绑定的容器详情则直接返回
        if (null == maintainHistory && !isBindingContainerRequest) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
        }
        if (null != maintainHistory && maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
        }
        if (null != maintainHistory && maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
        }
        if (null != maintainHistory && !isBindingContainerRequest) {

            List<MaintainHistoryDetailDTO> maintainHistoryDetails = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryIdAndResultAndDeleted(maintainHistory.getId(), MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(maintainHistoryDetails)) {
                if (maintainHistoryDetails.size() > Constants.INT_ONE) {
                    throw new ResponseException("error.reworkSubWorkSheet", "容器维修产生的返工单存在多个" + maintainHistoryDetails
                            .stream().map(maintainHistoryDetail -> maintainHistoryDetail.getWsRework().getReworkWorkSheet().getSerialNumber())
                            .collect(Collectors.joining(Constants.STR_SEMICOLON)) + "不能通过原始容器直接请求!");
                }
                rworkerStepProcessBaseDTO.setWorkSheet(maintainHistoryDetails.get(Constants.INT_ZERO).getWsRework().getReworkWorkSheet());
                rworkerStepProcessBaseDTO.setWorkFlow(rworkerStepProcessBaseDTO.getWorkSheet().getWorkFlow());
                //维修历史的工单若已取消或者完成时需要返回提示信息
                throwPromptMsg(subWsProductionMode, rworkerStepProcessBaseDTO);
                return;
            }
        }
        if (!isBindingContainerRequest) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, "请求工序生产的容器中存在尚未绑定的容器");
        }

    }

    /**
     * 维修历史的工单若已取消或者完成时需要返回提示信息
     *
     * @param subWsProductionMode       投产粒度
     * @param rworkerStepProcessBaseDTO 工序生产过程相关参数类
     */
    private void throwPromptMsg(boolean subWsProductionMode, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        if (subWsProductionMode) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.requestContainerSubWorkSheetNotExist", "请求容器对应子工单不存在"));
            rworkerStepProcessBaseDTO.setSubWorkSheet(subWorkSheet);
            if (rworkerStepProcessBaseDTO.getSubWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || rworkerStepProcessBaseDTO.getSubWorkSheet().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
            }
        } else if (rworkerStepProcessBaseDTO.getWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || rworkerStepProcessBaseDTO.getWorkSheet().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
        }
    }

    /**
     * 绑定容器时验证状态
     *
     * @param containerId 容器主键ID
     */
    @Override
    public void validateBindContainerMaintain(long containerId) {
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO).orElse(null);
        if (null == maintainHistory) {
            return;
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
        }
        if (maintainHistory.getStatus() == MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus()) {

            List<MaintainHistoryDetailDTO> maintainHistoryDetails = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryIdAndResultAndDeleted(maintainHistory.getId(), MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus(), Constants.LONG_ZERO);

            if (ValidateUtils.isValid(maintainHistoryDetails)) {
                //获取系统配置的投产粒度(子工单或者工单)
                boolean subWsProductionMode = commonService.subWsProductionMode();
                maintainHistoryDetails.forEach(maintainHistoryDetail -> {
                    List<WsStep> reWorkSheetStepList = wsStepRepository.findByWorkSheetIdAndDeleted(maintainHistoryDetail.getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO);
                    List<WsStep> firstWsStepList = reWorkSheetStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
                    if (firstWsStepList.stream().anyMatch(wsStep -> wsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName())) {
                        Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO);
                        containerDetailOptional.ifPresent(containerDetail -> {
                            if (!subWsProductionMode && !containerDetail.getBatchWorkDetail().getWorkSheet().getId().equals(maintainHistoryDetail.getWsRework().getReworkWorkSheet().getId())) {
                                throw new ResponseException("error.containerInReworkSheet", "容器在返工单中尚未下交!");
                            }
                            SubWorkSheet subWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(maintainHistoryDetail.getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
                            if (subWsProductionMode && null != subWorkSheet && !containerDetail.getBatchWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                                throw new ResponseException("error.containerInReworkSheet", "容器在返工单中尚未下交!");
                            }
                        });
                    }
                });
            }
        }
    }


    /**
     * 容器请求工序或者绑定新容器时验证容器复检
     *
     * @param containerId 容器ID
     */
    @Override
    public void validateContainerReinspect(long containerId) {
        StepReinspect stepReinspect = stepReinspectRepository.findTop1ByContainerDetailContainerIdAndStatusAndDeleted(containerId, Boolean.FALSE, Constants.LONG_ZERO);
        if (null != stepReinspect) {
            throw new ResponseException("error.containerWaitForAnalyze", "容器尚处于待复检中!");
        }
    }

    /**
     * 容器待做工序相关参数类(主要为后续获取返回Rworker待做工序信息参数服务，避免反复查询)
     */
    public static class ContainerNextToDoStepBaseInfo {
        /**
         * 当前投产工单信息
         */
        private WorkSheet workSheet;

        /**
         * 当前投产子工单信息
         */
        private SubWorkSheet subWorkSheet;

        /**
         * 已完成工序与工序快照对比出下个待做工序列表
         */
        private List<Long> nextTodoStepIdList;

        /**
         * 工单工序快照
         */
        private List<WsStep> wsStepList;

        /**
         * 请求容器最新完成容器详情
         */
        private ContainerDetail latestContainerDetail;

        /**
         * 待流转数量(维修分析开出后则为容器详情的投产数，否则默认为容器详情的待流转数)
         */
        private int transferNumber;

        public WorkSheet getWorkSheet() {
            return workSheet;
        }

        public ContainerNextToDoStepBaseInfo setWorkSheet(WorkSheet workSheet) {
            this.workSheet = workSheet;
            return this;
        }

        public SubWorkSheet getSubWorkSheet() {
            return subWorkSheet;
        }

        public ContainerNextToDoStepBaseInfo setSubWorkSheet(SubWorkSheet subWorkSheet) {
            this.subWorkSheet = subWorkSheet;
            return this;
        }

        public List<Long> getNextTodoStepIdList() {
            return nextTodoStepIdList;
        }

        public ContainerNextToDoStepBaseInfo setNextTodoStepIdList(List<Long> nextTodoStepIdList) {
            this.nextTodoStepIdList = nextTodoStepIdList;
            return this;
        }

        public List<WsStep> getWsStepList() {
            return wsStepList;
        }

        public ContainerNextToDoStepBaseInfo setWsStepList(List<WsStep> wsStepList) {
            this.wsStepList = wsStepList;
            return this;
        }

        public ContainerDetail getLatestContainerDetail() {
            return latestContainerDetail;
        }

        public ContainerNextToDoStepBaseInfo setLatestContainerDetail(ContainerDetail latestContainerDetail) {
            this.latestContainerDetail = latestContainerDetail;
            return this;
        }

        public int getTransferNumber() {
            return transferNumber;
        }

        public ContainerNextToDoStepBaseInfo setTransferNumber(int transferNumber) {
            this.transferNumber = transferNumber;
            return this;
        }
    }
}
