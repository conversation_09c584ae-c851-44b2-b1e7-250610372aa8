package net.airuima.rworker.service.rworker.quality.impl;

import com.google.common.collect.ImmutableBiMap;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.constant.enums.MaintainHistorySourceEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepInspectionConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.quality.*;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.TestDataDTO;
import net.airuima.rbase.dto.qms.TestDataSaveDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.InspectionTaskDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerCheckItemRequestDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerCheckProcessInspectionDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerCheckRuleInfoGetDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerCheckWorkCellStepDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerHumitureSaveRequestDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerInspectionResultDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerInspectionWorkCellStepDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerProductWorkSheetCategoryDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerToCheckWorkCellStepGetDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.oem.RbaseOemProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.qms.RbaseDefectCheckItemProxy;
import net.airuima.rbase.proxy.qms.RbaseDefectProxy;
import net.airuima.rbase.proxy.qms.RbaseVarietyProxy;
import net.airuima.rbase.proxy.rqms.RbaseQmsProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepInspectionConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.WorkCellCheckStartRuleRepository;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.quality.*;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.repository.procedure.scene.NextTodoStepRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.service.procedure.quality.IqcCheckHistoryService;
import net.airuima.rbase.service.procedure.quality.api.ICheckHistoryService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rworker.proxy.RworkerQuerySamplingStrategyProxy;
import net.airuima.rworker.service.enviroment.PHumitureCheckHistoryService;
import net.airuima.rworker.service.enviroment.PHumitureStandardRepository;
import net.airuima.rworker.service.enviroment.dto.HumitureCheckHistoryDTO;
import net.airuima.rworker.service.enviroment.dto.HumitureStandardDTO;
import net.airuima.rworker.service.rworker.event.IEventService;
import net.airuima.rworker.service.rworker.quality.IInspectionService;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.service.rworker.quality.dto.InspectTaskCreationRequest;
import net.airuima.rworker.web.rest.rworker.quality.dto.*;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class QualityServiceImpl implements IQualityService {
    private final String WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP = "WcNotInspectionWsPutIntoProductionStep";
    private final String NOT_EXIST_PRODUCT_WORK_SHEET = "notExistProductWorkSheet";
    private final String NOT_EXIST_PRODUCT_WORK_SHEET_MSG = "检测工单不存在";
    private final String PRODUCTION_WORK_SHEET_IS_NOT_EXIST = "ProductionWorkSheetIsNotExist";
    private final String WARNING = "warning";
    @Value("${spring.application.name}")
    private String applicationName;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkCellCheckStartRuleRepository workCellCheckStartRuleRepository;
    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private CheckHistoryDetailRepository checkHistoryDetailRepository;
    @Autowired
    private StepWarningStandardRepository stepWarningStandardRepository;
    @Autowired
    private UnqualifiedEventRepository unqualifiedEventRepository;
    @Autowired
    private UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private RworkerQuerySamplingStrategyProxy querySamplingStrategyService;
    @Autowired
    private RbaseDefectCheckItemProxy defectCheckItemRepository;
    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    @Autowired
    private RbaseVarietyProxy varietyRepository;
    @Autowired
    private RbaseDefectProxy defectRepository;
    @Autowired
    private CheckHistoryItemSnapshotRepository checkHistoryItemSnapshotRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private CheckHistoryService checkHistoryService;
    @Autowired
    private ICheckHistoryService[] checkHistoryServices;
    @Autowired
    private IInspectionService[] inspectionServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;
    @Autowired
    private PHumitureStandardRepository humitureStandardRepository;
    @Autowired
    private PHumitureCheckHistoryService humitureCheckHistoryService;
    @Autowired
    private IqcCheckHistoryService iqcCheckHistoryService;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private IqcCheckHistoryRepository iqcCheckHistoryRepository;
    @Autowired
    private RbaseQmsProxy rbaseQmsProxy;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private RbaseOemProxy rbaseOemProxy;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private PedigreeStepInspectionConfigRepository pedigreeStepInspectionConfigRepository;
    @Autowired
    private PedigreeStepInspectionHistoryRepository pedigreeStepInspectionHistoryRepository;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private NextTodoStepRepository nextTodoStepRepository;
    @Autowired
    private WorkCellExtendHistoryRepository workCellExtendHistoryRepository;
    @Autowired
    private WorkCellExtendStepDetailRepository workCellExtendStepDetailRepository;
    @Autowired
    private PedigreeStepInspectionHistoryDetailRepository pedigreeStepInspectionHistoryDetailRepository;
    @Autowired
    private InspectTaskDetailRepository inspectTaskDetailRepository;

    /**
     * 获取待投产工序绑定的不良项目信息
     *
     * @param stepId     工序主键ID
     * @param pedigree   产品谱系
     * @param workFlowId 工艺路线主键ID
     * @param clientId   客户主键id
     * @return java.util.List<net.airuima.web.rest.rworker.process.dto.general.UnqualifiedItemGetInfo> 工序不合格项目信息
     */
    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedItemGetInfo> findStepUnqualifiedItemInfo(long stepId, Pedigree pedigree, long workFlowId, Long clientId) {
        //获取工序不良项目,优先获取产品谱系配置的工序不良项目，最后才获取工序默认配置的不良项目
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(pedigree, workFlowId, stepId, clientId);
        if (!ValidateUtils.isValid(unqualifiedItems)) {
            return Lists.newArrayList();
        }
        return unqualifiedItems.stream().map(UnqualifiedItemGetInfo::new).collect(Collectors.toList());
    }


    /**
     * 保存工序生产不良信息
     *
     * @param subWsProductionMode     true:子工单粒度，false:工单粒度
     * @param batchWorkDetail         批量工序生产详情
     * @param unqualifiedItemInfoList 工序对应不良项目信息列表
     */
    @Override
    public void saveBatchWorkDetailUnqualifiedItem(boolean subWsProductionMode, BatchWorkDetail batchWorkDetail, List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        if (!ValidateUtils.isValid(unqualifiedItemInfoList)) {
            return;
        }
        unqualifiedItemInfoList.forEach(unqualifiedItemInfo -> {
            if (unqualifiedItemInfo.getNumber() <= net.airuima.constant.Constants.INT_ZERO) {
                return;
            }
            WsStepUnqualifiedItem wsStepUnqualifiedItem;
            if (subWsProductionMode) {
                wsStepUnqualifiedItem = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
            } else {
                wsStepUnqualifiedItem = wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
            }
            wsStepUnqualifiedItem.setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getId()))
                    .setWorkSheet(subWsProductionMode ? null : batchWorkDetail.getWorkSheet())
                    .setSubWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet() : null)
                    .setStep(batchWorkDetail.getStep())
                    .setNumber(wsStepUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber())
                    .setOperatorId(batchWorkDetail.getOperatorId())
                    .setRecordDate(LocalDate.now())
                    .setDeleted(Constants.LONG_ZERO);
            wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);

            //只有更新当前投产粒度对应的不良，不累计更新其他投产粒度对应的不良文件，以及描述信息
            if (Objects.nonNull(unqualifiedItemInfo.getProductType()) && unqualifiedItemInfo.getProductType() == RequestModeEnum.WORK_SHEET_REQUEST_MODE.getCode()) {
                //更新工序不良上传的关联文件
                if (ValidateUtils.isValid(unqualifiedItemInfo.getDocumentDTOList())) {
                    DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO().setServiceName(StringUtils.upperCase(applicationName))
                            .setRecordId(wsStepUnqualifiedItem.getId());
                    List<DocumentRelationDTO.Document> documentList = unqualifiedItemInfo.getDocumentDTOList().stream()
                            .map(i -> new DocumentRelationDTO.Document().setDocumentId(i.getId())).toList();
                    documentRelationDTO.setDocumentList(documentList);
                    rbaseDocumentProxy.relation(documentRelationDTO);
                }
                //更新工序不良的描述信息
                if (ValidateUtils.isValid(unqualifiedItemInfo.getDescription())) {
                    wsStepUnqualifiedItem.setDescription(ValidateUtils.isValid(wsStepUnqualifiedItem.getDescription()) ?
                            (wsStepUnqualifiedItem.getDescription() + Constants.STR_SEMICOLON + unqualifiedItemInfo.getDescription())
                            : unqualifiedItemInfo.getDescription());
                    wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                }
            }
        });
    }

    /**
     * 保存容器生产详情不良信息
     *
     * @param containerDetail         容器生产详情
     * @param unqualifiedItemInfoList 工序对应不良项目信息列表
     */
    @Override
    public void saveContainerWorkDetailUnqualifiedItem(ContainerDetail containerDetail, List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        if (!ValidateUtils.isValid(unqualifiedItemInfoList)) {
            return;
        }
        List<UnqualifiedItem> unqualifiedItems = unqualifiedItemRepository.findByIdInAndDeleted(unqualifiedItemInfoList.stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItems.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        unqualifiedItemInfoList.forEach(unqualifiedItemInfo -> {
            if (unqualifiedItemInfo.getNumber() <= net.airuima.constant.Constants.INT_ZERO) {
                return;
            }
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            containerDetailUnqualifiedItem.setContainerDetail(containerDetail).setUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemInfo.getId()).get(Constants.INT_ZERO)).setNumber(containerDetailUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);

            //只有更新当前投产粒度对应的不良，不累计更新其他投产粒度对应的不良文件，以及描述信息
            if (Objects.nonNull(unqualifiedItemInfo.getProductType()) && unqualifiedItemInfo.getProductType() == RequestModeEnum.CONTAINER_REQUEST_MODE.getCode()) {
                //更新不良上传的关联文件
                if (ValidateUtils.isValid(unqualifiedItemInfo.getDocumentDTOList())) {
                    DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO().setServiceName(StringUtils.upperCase(applicationName))
                            .setRecordId(containerDetailUnqualifiedItem.getId());
                    List<DocumentRelationDTO.Document> documentList = unqualifiedItemInfo.getDocumentDTOList().stream()
                            .map(i -> new DocumentRelationDTO.Document().setDocumentId(i.getId())).toList();
                    documentRelationDTO.setDocumentList(documentList);
                    rbaseDocumentProxy.relation(documentRelationDTO);
                }
                //更新不良的描述信息
                if (ValidateUtils.isValid(unqualifiedItemInfo.getDescription())) {
                    containerDetailUnqualifiedItem.setDescription(ValidateUtils.isValid(containerDetailUnqualifiedItem.getDescription()) ?
                            (containerDetailUnqualifiedItem.getDescription() + Constants.STR_SEMICOLON + unqualifiedItemInfo.getDescription())
                            : unqualifiedItemInfo.getDescription());
                    containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                }
            }
        });
    }


    /**
     * 保存sn生产详情不良信息
     *
     * @param snWorkDetail            sn生产详情
     * @param unqualifiedItemSaveInfo 工序对应不良项目信息列表
     */
    @Override
    public void saveSnWorkDetailUnqualifiedItem(SnWorkDetail snWorkDetail, UnqualifiedItemSaveInfo unqualifiedItemSaveInfo) {
        if (ObjectUtils.isEmpty(unqualifiedItemSaveInfo)) {
            return;
        }
        SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
        snUnqualifiedItem.setSn(snWorkDetail.getSn()).setWorkSheet(snWorkDetail.getWorkSheet()).setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setStep(snWorkDetail.getStep()).setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemSaveInfo.getId())).setFlag(Boolean.FALSE).setSnWorkDetail(snWorkDetail);
        snUnqualifiedItemRepository.save(snUnqualifiedItem);

        //只有更新当前投产粒度对应的不良，不累计更新其他投产粒度对应的不良文件，以及描述信息
        if (Objects.nonNull(unqualifiedItemSaveInfo.getProductType()) && unqualifiedItemSaveInfo.getProductType() == RequestModeEnum.SN_REQUEST_MODE.getCode()) {
            //更新不良上传的关联文件
            if (ValidateUtils.isValid(unqualifiedItemSaveInfo.getDocumentDTOList())) {
                DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO().setServiceName(StringUtils.upperCase(applicationName))
                        .setRecordId(snUnqualifiedItem.getId());
                List<DocumentRelationDTO.Document> documentList = unqualifiedItemSaveInfo.getDocumentDTOList().stream()
                        .map(i -> new DocumentRelationDTO.Document().setDocumentId(i.getId())).toList();
                documentRelationDTO.setDocumentList(documentList);
                rbaseDocumentProxy.relation(documentRelationDTO);
            }
            //更新不良的描述信息
            if (ValidateUtils.isValid(unqualifiedItemSaveInfo.getDescription())) {
                snUnqualifiedItem.setDescription(ValidateUtils.isValid(snUnqualifiedItem.getDescription()) ?
                        (snUnqualifiedItem.getDescription() + Constants.STR_SEMICOLON + unqualifiedItemSaveInfo.getDescription())
                        : unqualifiedItemSaveInfo.getDescription());
                snUnqualifiedItemRepository.save(snUnqualifiedItem);
            }
        }
    }

    /**
     * 通过检测工位、投产工单获取可检测的生产工位及对应工序列表
     *
     * @param rworkerCheckWorkCellStepDto 请求首检工位绑定工序请求参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerToCheckWorkCellStepGetDTO 投产工单待检工位工序信息
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Override
    public RworkerToCheckWorkCellStepGetDTO getToInspectedWorkCellAndStepInfo(RworkerCheckWorkCellStepDTO rworkerCheckWorkCellStepDto) {
        RworkerToCheckWorkCellStepGetDTO rworkerToCheckWorkCellStepGetDto = new RworkerToCheckWorkCellStepGetDTO();

        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(rworkerCheckWorkCellStepDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet workSheet = subWsProductionMode && !ObjectUtils.isEmpty(subWorkSheet) ? subWorkSheet.getWorkSheet() : workSheetRepository.findBySerialNumberAndDeleted(rworkerCheckWorkCellStepDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);

        if (ObjectUtils.isEmpty(workSheet)) {
            throw new ResponseException(PRODUCTION_WORK_SHEET_IS_NOT_EXIST, NOT_EXIST_PRODUCT_WORK_SHEET_MSG);
        }
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerCheckWorkCellStepDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.InspectedWorkCellIsNotExist", "当前检测工位不存在");
        }
        if (workCell.getCategory() != WorkCellEnum.FIRST_INSPECTION_WC.getCategory() && workCell.getCategory() != WorkCellEnum.PQC_WC.getCategory()) {
            throw new ResponseException("error.NonFirstPQCInspectionWorkCell", "当前工位非(首检/PQC工位)");
        }
        List<Step> stepList = workCellStepRepository.findByWorkCellId(workCell.getId());
        if (!ValidateUtils.isValid(stepList)) {
            throw new ResponseException("error.WorkCellNotBindStep", "工位未绑定任何工序");
        }
        List<WsStep> wsStepList = Lists.newArrayList();
        if (subWsProductionMode) {
            List<WsStep> subWsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(subWsSteps)) {
                wsStepList.addAll(subWsSteps);
            }
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            wsStepList.addAll(wsSteps);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            throw new ResponseException("error.WsNotPutIntoProductionStep", "当前工单不存在投产工序");
        }
        List<Step> todoStepList = wsStepList.stream().filter(wsStep -> stepList.stream().anyMatch(step -> wsStep.getStep().getId().equals(step.getId()))).map(WsStep::getStep).collect(Collectors.toList());
        if (!ValidateUtils.isValid(todoStepList)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前工位不能检测工单投产工序");
        }

        //获取当前检测工位能够检测的生产工位以及对应的工序列表
        List<WorkCellStep> workCellSteps = workCellStepRepository.findByStepIdInAndWorkCellCategoryAndDeleted(todoStepList.stream().map(Step::getId).collect(Collectors.toList()), Constants.INT_ONE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellSteps)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前生产部门的生产工位不能生产检测工位里的任何工序");
        }
        WorkCellCategoryCorrespondenceEnums workCellCategoryCorrespondenceEnum = WorkCellCategoryCorrespondenceEnums.getKey(workCell.getCategory());
        if (ObjectUtils.isEmpty(workCellCategoryCorrespondenceEnum)) {
            throw new ResponseException("error. workCellCategoryCorrespondenceIsNotExist", "工位对应的检测规则不存在");
        }
        List<WorkCellStep> workCellStepList = Lists.newArrayList();
        //获取当前工位同部门，同产线得工位对应工序列表
        if (!ObjectUtils.isEmpty(workCell.getOrganizationId()) && !ObjectUtils.isEmpty(workSheet.getOrganizationId())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getOrganizationId())).filter(workCellStep -> workCellStep.getWorkCell().getOrganizationId().equals(workSheet.getOrganizationId())).collect(Collectors.toList());

        } else if (!ObjectUtils.isEmpty(subWorkSheet) && !ObjectUtils.isEmpty(subWorkSheet.getWorkLine()) && !ObjectUtils.isEmpty(workCell.getWorkLine())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getWorkLine())).filter(workCellStep -> workCellStep.getWorkCell().getWorkLine().getId().equals(subWorkSheet.getWorkLine().getId())).collect(Collectors.toList());

        } else if (!ObjectUtils.isEmpty(workSheet.getWorkLine()) && !ObjectUtils.isEmpty(workCell.getWorkLine())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getWorkLine())).filter(workCellStep -> workCellStep.getWorkCell().getWorkLine().getId().equals(workSheet.getWorkLine().getId())).collect(Collectors.toList());
        }
        if (!ValidateUtils.isValid(workCellStepList)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前生产部门的生产工位不能生产检测工位里的任何工序");
        }
        rworkerToCheckWorkCellStepGetDto = subWsProductionMode ? rworkerToCheckWorkCellStepGetDto.setProductWorkSheetId(subWorkSheet.getId()) : rworkerToCheckWorkCellStepGetDto.setProductWorkSheetId(workSheet.getId());
        //集成可以检查的工位对应工序
        List<RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo> workCellStepInfos = Lists.newArrayList();
        workCellStepList.stream().collect(Collectors.groupingBy(WorkCellStep::getWorkCell)).forEach((workCellTemp, toCheckWorkCellSteps) -> {
            RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo workCellStepInfo = new RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo();
            List<RworkerToCheckWorkCellStepGetDTO.StepInfo> stepInfos = Lists.newArrayList();
            toCheckWorkCellSteps.forEach(workCellStep -> stepInfos.add(new RworkerToCheckWorkCellStepGetDTO.StepInfo(workCellStep.getStep())));
            workCellStepInfo.setWorkCellName(workCellTemp.getName()).setWorkCellId(workCellTemp.getId()).setStepInfoList(stepInfos);
            workCellStepInfos.add(workCellStepInfo);
        });
        //返回首检/巡检类型
        rworkerToCheckWorkCellStepGetDto.setCategory(workCellCategoryCorrespondenceEnum.getCheckRuleCategory());
        return rworkerToCheckWorkCellStepGetDto.setToCheckWorkCellStepInfos(workCellStepInfos);
    }

    /**
     * 通过投产工单及工序获取检测规则及检测项目信息
     *
     * @param rworkerCheckItemRequestDto 请求验证首中末QC检请求参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerCheckRuleInfoGetDTO 检查项目规则
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Override
    public RworkerCheckRuleInfoGetDTO getCheckRuleInfo(RworkerCheckItemRequestDTO rworkerCheckItemRequestDto) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerCheckItemRequestDto.getProductWorkSheetId()) : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerCheckItemRequestDto.getProductWorkSheetId());
        if (ObjectUtils.isEmpty(workSheet)) {
            throw new ResponseException(PRODUCTION_WORK_SHEET_IS_NOT_EXIST, NOT_EXIST_PRODUCT_WORK_SHEET_MSG);
        }
        Step step = stepRepository.findByIdAndDeleted(rworkerCheckItemRequestDto.getStepId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(step)) {
            throw new ResponseException("error.TodoInspectionStepIsNotExist", "当前待检测工序不存在");
        }
        //获取定制工序中工艺路线
        PedigreeStepCheckRule pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(workSheet, subWorkSheet, workSheet.getPedigree(), step, rworkerCheckItemRequestDto.getCategory(), rworkerCheckItemRequestDto.getWorkCellId(), null);
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            throw new ResponseException("error.TodoInspectionStepCheckRuleIsNotExist", "当前待检测工序未配置检测规则");
        }
        //分别获取谱系工序检测规则以及对应的检测项目
        RworkerCheckRuleInfoGetDTO checkRuleInfo = new RworkerCheckRuleInfoGetDTO();
        List<RworkerCheckRuleInfoGetDTO.CheckItemInfo> checkItemInfos = Lists.newArrayList();
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        pedigreeStepCheckItemList = pedigreeStepCheckItemList.stream().filter(pedigreeStepCheckItem -> Objects.equals(
                pedigreeStepCheckItem.getCheckItem().getVariety(), pedigreeStepCheckRule.getVariety())).toList();
        double value = subWsProductionMode ? subWorkSheet.getNumber() * pedigreeStepCheckRule.getRate() : workSheet.getNumber() * pedigreeStepCheckRule.getRate();
        int sampleNumber = pedigreeStepCheckRule.getJudgeWay() == Constants.INT_ZERO ? pedigreeStepCheckRule.getBaseNumber() : (int) Math.ceil(value);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            pedigreeStepCheckItemList.forEach(pedigreeStepCheckItem ->
                    checkItemInfos.add(new RworkerCheckRuleInfoGetDTO.CheckItemInfo(pedigreeStepCheckItem, sampleNumber))
            );
        }
        checkRuleInfo.setNumber(sampleNumber)
                .setJudgeWay(pedigreeStepCheckRule.getJudgeWay())
                .setQualifiedNumber(pedigreeStepCheckRule.getQualifiedNumber())
                .setQualifiedRate(pedigreeStepCheckRule.getQualifiedRate())
                .setCheckItemInfoList(ValidateUtils.isValid(checkItemInfos) ? checkItemInfos : null)
                .setVariety(pedigreeStepCheckRule.getVariety());
        return checkRuleInfo;
    }


    /**
     * 获取生产工序预警标准
     *
     * @param batchStepSaveBaseInfo 工序生产过程通用保存参数
     * @return net.airuima.domain.base.quality.StepWarningStandard 生产工序预警标准
     */
    @Override
    public StepWarningStandard findStepWarningStandard(RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        Step step = batchStepSaveBaseInfo.getStep();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(workSheet.getPedigree());
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != batchStepSaveBaseInfo.getWsStep() && null != batchStepSaveBaseInfo.getWsStep().getWorkFlow() ? batchStepSaveBaseInfo.getWsStep().getWorkFlow() : batchStepSaveBaseInfo.getWorkFlow();
        List<StepWarningStandard> stepWarningStandardList = stepWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), Objects.isNull(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), snapshotWorkFlow.getId(), workSheet.getClientId(), Constants.INT_ZERO, Constants.LONG_ZERO);
        List<StepWarningStandard> stepWarningStandardListSort = Lists.newArrayList();
        if (!ValidateUtils.isValid(stepWarningStandardList)) {
            return null;
        }
        if (stepWarningStandardList.size() == Constants.INT_ONE) {
            return stepWarningStandardList.get(Constants.INT_ZERO);
        }
        //获取优先级排序最高集合
        StepWarningStandard stepWarningStandard = stepWarningStandardList.stream().min(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).orElse(null);
        if (null == stepWarningStandard) {
            return null;
        }
        //取到优先级最高的预警标准列表
        stepWarningStandardList = stepWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == stepWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        //如果优先级最高的只有一个则直接返回
        if (stepWarningStandardList.size() == Constants.INT_ONE) {
            return stepWarningStandardList.get(Constants.INT_ZERO);
        }
        //如果有多个则可能需要对产品谱系进行优先级排序（有可能产品谱系为空）
        List<StepWarningStandard> stepWarningStandardListPedigreeNull = stepWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).toList();
        List<StepWarningStandard> stepWarningStandardListPedigreeNotNull = stepWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNull)) {
            stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            stepWarningStandardListPedigreeNotNull = stepWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).toList();
            stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNotNull);
        }
        return CollectionUtils.isEmpty(stepWarningStandardListSort) ? null : stepWarningStandardListSort.get(stepWarningStandardListSort.size() - Constants.INT_ONE);
    }


    /**
     * 通过工单、工序及不良项目获取产品谱系工序不良项目预警标准
     *
     * @param batchStepSaveBaseInfo 工序生产过程通用保存参数
     * @param unqualifiedItem       不良项目数据
     * @return net.airuima.domain.base.quality.UnqualifiedItemWarningStandard  产品谱系工序不良项目预警标准
     */
    @Override
    public UnqualifiedItemWarningStandard findUnqualifiedItemWaringStandard(RworkerStepProcessBaseDTO batchStepSaveBaseInfo, UnqualifiedItem unqualifiedItem) {
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        Step step = batchStepSaveBaseInfo.getStep();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(batchStepSaveBaseInfo.getWorkSheet().getPedigree());
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != batchStepSaveBaseInfo.getWsStep() && null != batchStepSaveBaseInfo.getWsStep().getWorkFlow() ? batchStepSaveBaseInfo.getWsStep().getWorkFlow() : batchStepSaveBaseInfo.getWorkFlow();
        // 查出规则集合
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), step.getStepGroup() != null ? step.getStepGroup().getId() : null, step.getId(), snapshotWorkFlow.getId(), workSheet.getClientId() != null ? workSheet.getClientId() : null, unqualifiedItem.getId(), unqualifiedItem.getUnqualifiedGroup() != null ? unqualifiedItem.getUnqualifiedGroup().getId() : null, Constants.INT_ONE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemWarningStandardList)) {
            return null;
        }
        if (unqualifiedItemWarningStandardList.size() == Constants.INT_ONE) {
            return unqualifiedItemWarningStandardList.get(Constants.INT_ZERO);
        }
        //获取优先级排序最高集合
        UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = unqualifiedItemWarningStandardList.stream().min(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).orElse(null);
        if (null == unqualifiedItemWarningStandard) {
            return null;
        }
        //取到优先级最高的预警标准列表
        unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == unqualifiedItemWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        //如果优先级最高的只有一个则直接返回
        if (unqualifiedItemWarningStandardList.size() == Constants.INT_ONE) {
            return unqualifiedItemWarningStandardList.get(Constants.INT_ZERO);
        }
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListSort = Lists.newArrayList();
        //如果有多个则可能需要对产品谱系进行优先级排序（有可能产品谱系为空）
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNull = unqualifiedItemWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNull)) {
            unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNotNull);
        }
        return org.apache.commons.collections.CollectionUtils.isEmpty(unqualifiedItemWarningStandardListSort) ? null : unqualifiedItemWarningStandardListSort.get(unqualifiedItemWarningStandardListSort.size() - Constants.INT_ONE);
    }

    /**
     * 保存批量下交待维修分析数据
     *
     * @param batchStepSaveRequestDTO   保存批量工序参数
     * @param rworkerStepProcessBaseDTO 工序生产过程通用基础数据
     * @param batchWorkDetail           批量工序生产详情
     */
    @Override
    public boolean saveBatchProcessMaintainInfo(RworkerBatchStepSaveRequestDTO batchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, BatchWorkDetail batchWorkDetail) {
        if (!ValidateUtils.isValid(batchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return false;
        }
        //只要有不良的处理类型为维修分析则将工单整体开出，待分析状态
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(batchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        UnqualifiedItem matainUnqualifiedItem = unqualifiedItemList.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        if (null == matainUnqualifiedItem) {
            return false;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(batchStepSaveRequestDTO.getNumber())
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(batchWorkDetail.getStep())
                .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                .setWorkSheet(batchWorkDetail.getWorkSheet())
                .setUnqualifiedItem(matainUnqualifiedItem)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        //增加维修分析来源
        maintainHistory.setCheckHistoryId(null)
                .setSource(MaintainHistorySourceEnum.PROCESS_DEFECT.getCode());
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }

    /**
     * 保存容器下交待维修分析数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param bingContainerInfo 容器工序请求生产数据
     * @param containerDetail   容器生产详情
     */
    @Override
    public boolean saveContainerProcessMaintainInfo(RworkerContainerStepSaveRequestDTO.BingContainerInfo bingContainerInfo, ContainerDetail containerDetail) {
        if (!ValidateUtils.isValid(bingContainerInfo.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(bingContainerInfo.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        UnqualifiedItem maintainUnqualifiedItem = unqualifiedItemList.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        if (null == maintainUnqualifiedItem) {
            return Boolean.FALSE;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(bingContainerInfo.getNumber())
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(containerDetail.getBatchWorkDetail().getStep())
                .setSubWorkSheet(containerDetail.getBatchWorkDetail().getSubWorkSheet())
                .setWorkSheet(containerDetail.getBatchWorkDetail().getWorkSheet())
                .setContainerDetail(containerDetail)
                .setUnqualifiedItem(maintainUnqualifiedItem)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        //增加维修分析来源
        maintainHistory.setCheckHistoryId(null)
                .setSource(MaintainHistorySourceEnum.PROCESS_DEFECT.getCode());
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }


    /**
     * 保存SN下交待维修分析数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param snWorkStatus sn生产状态
     */
    @Override
    public boolean saveSnProcessMaintainInfo(SnWorkStatus snWorkStatus) {
        UnqualifiedItem unqualifiedItem = snWorkStatus.getLatestUnqualifiedItem();
        if (unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) {
            return Boolean.FALSE;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(Constants.INT_ONE)
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(snWorkStatus.getLatestSnWorkDetail().getStep())
                .setSubWorkSheet(snWorkStatus.getSubWorkSheet())
                .setWorkSheet(snWorkStatus.getWorkSheet())
                .setContainerDetail(snWorkStatus.getLatestSnWorkDetail().getContainerDetail())
                .setUnqualifiedItem(snWorkStatus.getLatestUnqualifiedItem())
                .setSnWorkStatus(snWorkStatus)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        //增加维修分析来源
        maintainHistory.setCheckHistoryId(null)
                .setSource(MaintainHistorySourceEnum.PROCESS_DEFECT.getCode());
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }

    /**
     * 保存批量下交待复检数据
     *
     * @param batchStepSaveRequestDTO   保存批量工序参数
     * @param rworkerStepProcessBaseDTO 工序生产过程通用基础数据
     * @param batchWorkDetail           批量工序生产详情
     */
    @Override
    public boolean saveBatchProcessReinspectInfo(RworkerBatchStepSaveRequestDTO batchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, BatchWorkDetail batchWorkDetail) {
        if (!ValidateUtils.isValid(batchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(batchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItemList.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        List<StepReinspect> stepReinspectHistories = new ArrayList<>();
        batchStepSaveRequestDTO.getUnqualifiedItemInfoList().forEach(unqualifiedItemSaveInfo -> {
            StepReinspect stepReinspect = new StepReinspect();
            stepReinspect.setWorkSheet(rworkerStepProcessBaseDTO.getWorkSheet()).setSubWorkSheet(rworkerStepProcessBaseDTO.getSubWorkSheet())
                    .setStep(batchWorkDetail.getStep()).setWorkCell(batchWorkDetail.getWorkCell()).setSponsorId(batchWorkDetail.getOperatorId())
                    .setOriginUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemSaveInfo.getId()).get(Constants.INT_ZERO)).setNumber(unqualifiedItemSaveInfo.getNumber())
                    .setSponsorDate(LocalDateTime.now()).setSerialNumber(rworkerStepProcessBaseDTO.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
            stepReinspectHistories.add(stepReinspect);
        });
        stepReinspectRepository.saveAll(stepReinspectHistories);
        return Boolean.TRUE;
    }


    /**
     * 保存容器下交待复检数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param bingContainerInfo 容器工序请求生产数据
     * @param containerDetail   容器生产详情
     */
    @Override
    public boolean saveContainerProcessReinspectInfo(RworkerContainerStepSaveRequestDTO.BingContainerInfo bingContainerInfo, RworkerStepProcessBaseDTO containerStepSaveBaseInfo, ContainerDetail containerDetail) {
        if (!ValidateUtils.isValid(bingContainerInfo.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(bingContainerInfo.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItemList.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        List<StepReinspect> stepReinspectHistories = new ArrayList<>();
        bingContainerInfo.getUnqualifiedItemInfoList().forEach(unqualifiedItemSaveInfo -> {
            StepReinspect stepReinspect = new StepReinspect();
            stepReinspect.setWorkSheet(containerStepSaveBaseInfo.getWorkSheet()).setSubWorkSheet(containerStepSaveBaseInfo.getSubWorkSheet()).setContainerDetail(containerDetail)
                    .setStep(containerDetail.getBatchWorkDetail().getStep()).setWorkCell(containerDetail.getWorkCell()).setSponsorId(containerDetail.getStaffId())
                    .setOriginUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemSaveInfo.getId()).get(Constants.INT_ZERO)).setNumber(unqualifiedItemSaveInfo.getNumber())
                    .setSponsorDate(LocalDateTime.now()).setSerialNumber(containerStepSaveBaseInfo.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(containerStepSaveBaseInfo.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
            stepReinspectHistories.add(stepReinspect);
        });
        stepReinspectRepository.saveAll(stepReinspectHistories);
        return Boolean.TRUE;
    }

    /**
     * 保存SN下交待复检数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param snWorkStatus sn生产状态
     */
    @Override
    public boolean saveSnProcessReinspectInfo(SnWorkStatus snWorkStatus, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
        StepReinspect stepReinspect = new StepReinspect();
        stepReinspect.setWorkSheet(rworkerStepProcessBaseDTO.getWorkSheet()).setSubWorkSheet(rworkerStepProcessBaseDTO.getSubWorkSheet()).setSnWorkStatus(snWorkStatus)
                .setStep(latestSnWorkDetail.getStep()).setWorkCell(latestSnWorkDetail.getWorkCell()).setSponsorId(latestSnWorkDetail.getOperatorId()).setContainerDetail(latestSnWorkDetail.getContainerDetail())
                .setOriginUnqualifiedItem(snWorkStatus.getLatestUnqualifiedItem()).setNumber(Constants.INT_ONE)
                .setSponsorDate(LocalDateTime.now()).setSerialNumber(rworkerStepProcessBaseDTO.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
        stepReinspectRepository.save(stepReinspect);
        return Boolean.TRUE;
    }

    /**
     * 保存工序生产过程中的预警信息
     *
     * @param batchWorkDetail                批量工序生产详情
     * @param batchStepSaveBaseInfo          工序生产过程通用基础数据
     * @param rworkerBatchStepSaveRequestDTO 工序请求保存参数
     */
    @Override
    public void saveStepProcessWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        if (!ValidateUtils.isValid(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return;
        }
        //产品中(子)工单相同工序只预警一次
        List<UnqualifiedEvent> unqualifiedEventList = batchStepSaveBaseInfo.getSubWsProductionMode() ? unqualifiedEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), batchStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO) :
                unqualifiedEventRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), batchStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(unqualifiedEventList)) {
            return;
        }
        //保存工序不良项目预警事件
        boolean isWaringed = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveStepUnqualifiedItemWaringInfo(batchWorkDetail, batchStepSaveBaseInfo, rworkerBatchStepSaveRequestDTO);
        //保存工序良率预警事件
        if (!isWaringed) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveStepPassRateWaringInfo(batchWorkDetail, batchStepSaveBaseInfo);
        }
    }

    /**
     * 保存工序良率预警信息
     *
     * @param batchWorkDetail       批量工序生产详情
     * @param batchStepSaveBaseInfo 工序生产过程通用基础数据
     */
    @Override
    public void saveStepPassRateWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        double qualifiedRate = NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
        StepWarningStandard stepWarningStandard = this.findStepWarningStandard(batchStepSaveBaseInfo);
        //良率预警标准不存在或者完成数小于基数时不做任何判断
        if (null == stepWarningStandard || batchWorkDetail.getFinishNumber() < stepWarningStandard.getBaseNumber()) {
            return;
        }
        //良率小于停线标准则停线
        if (qualifiedRate < stepWarningStandard.getStopRate()) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_PASS_RATE_TYPE.getCategoryName(),
                    qualifiedRate, Constants.INT_ONE - qualifiedRate, batchWorkDetail.getUnqualifiedNumber(), batchStepSaveBaseInfo, null);
            return;
        }
        //良率小于预警标准则预警
        if (qualifiedRate < stepWarningStandard.getWaringRate()) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_PASS_RATE_TYPE.getCategoryName(),
                    qualifiedRate, Constants.INT_ONE - qualifiedRate, batchWorkDetail.getUnqualifiedNumber(), batchStepSaveBaseInfo, null);
        }
    }


    /**
     * 保存工序不良预警信息
     *
     * @param batchWorkDetail                批量工序生产详情
     * @param batchStepSaveBaseInfo          工序生产过程通用基础数据
     * @param rworkerBatchStepSaveRequestDTO 工序请求保存参数
     * @return boolean 是否预警
     */
    @Override
    public boolean saveStepUnqualifiedItemWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        for (UnqualifiedItemSaveInfo unqualifiedItemInfo : rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList()) {
            UnqualifiedItem unqualifiedItem = unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getId());
            UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = this.findUnqualifiedItemWaringStandard(batchStepSaveBaseInfo, unqualifiedItem);
            if (null == unqualifiedItemWarningStandard) {
                continue;
            }
            Long sumUnqualifiedItemNumber = batchStepSaveBaseInfo.getSubWsProductionMode() ? wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchStepSaveBaseInfo.getSubWorkSheet().getId(), batchStepSaveBaseInfo.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO)
                    : wsStepUnqualifiedItemRepository.sumNumberByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchStepSaveBaseInfo.getWorkSheet().getId(), batchStepSaveBaseInfo.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO);
            if (Objects.isNull(sumUnqualifiedItemNumber) || Constants.LONG_ZERO == sumUnqualifiedItemNumber) {
                continue;
            }
            //计算不良率
            double occupancy = NumberUtils.divide(sumUnqualifiedItemNumber, batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
            double qualifiedRate = NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
            //投产数小于基数时则判断不良数量是否超过预警数量
            if (batchWorkDetail.getFinishNumber() < unqualifiedItemWarningStandard.getBaseNumber() && unqualifiedItemInfo.getNumber() > unqualifiedItemWarningStandard.getWaringNumber()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
            //停线：投产数大于基数时则判断不良占比是否超过停线比例
            if (batchWorkDetail.getFinishNumber() >= unqualifiedItemWarningStandard.getBaseNumber() && occupancy > unqualifiedItemWarningStandard.getStopRate()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
            //停线：投产数大于基数时则判断不良占比是否超过预警比例
            if (batchWorkDetail.getFinishNumber() >= unqualifiedItemWarningStandard.getBaseNumber() && occupancy > unqualifiedItemWarningStandard.getWaringRate()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 保存预警停线事件记录
     *
     * @param eventType             类型 0:预警;1:停线
     * @param reasonType            原因类型 0:不良超标;1:合格率不达标
     * @param qualifiedRate         工序合格率
     * @param unqualifiedRate       不良占用率
     * @param batchStepSaveBaseInfo 工序生产过程通用基础数据
     * @param unqualifiedItem       不良项目
     * @return net.airuima.domain.procedure.quality.UnqualifiedEvent 不合格事件
     **/
    @Override
    public UnqualifiedEvent saveUnqualifiedEvent(int eventType, int reasonType, double qualifiedRate, double unqualifiedRate, int unqualifiedNumber, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, UnqualifiedItem unqualifiedItem) {
        SerialNumberDTO serialNumberDto = new SerialNumberDTO(eventType == Constants.INT_ZERO ? Constants.UNQUALIFIED_EVENT_WARING_SERIAL_NUMBER : Constants.UNQUALIFIED_EVENT_STOP_SERIAL_NUMBER, null, null);
        UnqualifiedEvent unqualifiedEvent = new UnqualifiedEvent();
        unqualifiedEvent.setOwnerId(batchStepSaveBaseInfo.getStaffDTO().getId())
                .setEventType(eventType)
                .setReasonType(reasonType)
                .setQualifiedRate(qualifiedRate)
                .setUnqualifiedRate(unqualifiedRate)
                .setStep(batchStepSaveBaseInfo.getStep())
                .setWorkCell(batchStepSaveBaseInfo.getWorkCell())
                .setUnqualifiedItem(unqualifiedItem)
                .setSubWorkSheet(batchStepSaveBaseInfo.getSubWorkSheet())
                .setWorkSheet(batchStepSaveBaseInfo.getWorkSheet())
                .setUnqualifiedNumber(unqualifiedNumber)
                .setStatus(Constants.INT_ZERO)
                .setRecordTime(LocalDateTime.now())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto)).setDeleted(Constants.LONG_ZERO);
        unqualifiedEventRepository.save(unqualifiedEvent);
        boolean stopWorkLine = eventType == ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName() || null == batchStepSaveBaseInfo.getSubWorkSheet();
        //停线或者工单作为投产粒度时都是停工单
        if (stopWorkLine) {
            WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
            workSheetRepository.save(workSheet);
        }
        //预警且子工单作为投产粒度时才将子工单暂停
        if (!stopWorkLine) {
            SubWorkSheet subWorkSheet = batchStepSaveBaseInfo.getSubWorkSheet();
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
            subWorkSheetRepository.save(subWorkSheet);
        }
        //根据事件配置来决定是否启动流程和消息发送
        boolean startProcess = eventServices[0].startProcess(UnqualifiedEvent.class.getSimpleName(), ImmutableBiMap.of(WARNING, !stopWorkLine), unqualifiedEvent.getSerialNumber(), StringUtils.EMPTY);
        if (startProcess) {
            unqualifiedEvent.setStatus(Constants.NEGATIVE_ONE);
            unqualifiedEventRepository.save(unqualifiedEvent);
        }
        return unqualifiedEvent;
    }

    /**
     * 通过投产工单  获取可检测的工序对应的工位列表
     *
     * @param serialNumber 投产工单编码
     * @return java.util.List<net.airuima.web.rest.rworker.quality.dto.RworkerInspectionWorkCellStepDTO> 获取待做工序及工位列表
     * <AUTHOR>
     * @date 2023/4/26
     */
    @Override
    public List<RworkerInspectionWorkCellStepDTO> inspectionWorkCellStep(String serialNumber) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        //工单、子工单
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet workSheet = subWsProductionMode && !ObjectUtils.isEmpty(subWorkSheet) ? subWorkSheet.getWorkSheet() : workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);

        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + serialNumber + "不存在");
        }
        if (Objects.nonNull(subWorkSheet) && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.subWorkSheet.finished", "子工单【" + serialNumber + "】已完成");
        }
        if (Objects.isNull(subWorkSheet) && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.workSheet.finished", "工单【" + serialNumber + "】已完成");
        }
        return subWsProductionMode ? BeanUtil.getHighestPrecedenceBean(IQualityService.class).inspectionWorkCellStepProductWorkSheet(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), subWsProductionMode)
                : BeanUtil.getHighestPrecedenceBean(IQualityService.class).inspectionWorkCellStepProductWorkSheet(workSheet.getId(), workSheet.getSerialNumber(), subWsProductionMode);
    }


    /**
     * 通过 投产工单获取 对应 待检工序及工位信息列表
     *
     * @param productWorkSheetId 工单ID
     * @param serialNumber       工单序列号
     * @param isSubWorkSheet     是否是子工单
     * @return 待检工序及工位信息列表
     */
    @Override
    public List<RworkerInspectionWorkCellStepDTO> inspectionWorkCellStepProductWorkSheet(Long productWorkSheetId, String serialNumber, boolean isSubWorkSheet) {

        List<RworkerInspectionWorkCellStepDTO> rworkerInspectionWorkCellStepDtos = Lists.newArrayList();

        //获取当前工单待做的全部工序
        List<WsStep> wsSteps = isSubWorkSheet
                ? wsStepRepository.findBySubWorkSheetIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO)
                : wsStepRepository.findByWorkSheetIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(wsSteps)) {
            SubWorkSheet subWorkSheet = isSubWorkSheet ? subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO).orElse(null) : null;
            if (!ObjectUtils.isEmpty(subWorkSheet)) {
                wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
            }
        }
        if (!ValidateUtils.isValid(wsSteps)) {
            throw new ResponseException("error.productionSnapshotNotExist", "投产工单" + serialNumber + "工序快照不存在");
        }
        if (ValidateUtils.isValid(wsSteps)) {
            //处理每个待检工序，获取工位信息
            wsSteps.forEach(wsStep -> {
                RworkerInspectionWorkCellStepDTO rworkerInspectionWorkCellStepDto = new RworkerInspectionWorkCellStepDTO();
                rworkerInspectionWorkCellStepDto.setStepDto(new RworkerInspectionWorkCellStepDTO.StepDTO(wsStep.getStep()));
                List<WorkCell> workCells = workCellStepRepository.findByStepIdAndDeletedAndIsEnable(wsStep.getStep().getId(), Boolean.TRUE);
                Optional.ofNullable(workCells).ifPresent(workCellList -> {
                    List<RworkerInspectionWorkCellStepDTO.WorkCellDTO> workCellDtos = workCellList.stream().map(RworkerInspectionWorkCellStepDTO.WorkCellDTO::new).collect(Collectors.toList());
                    rworkerInspectionWorkCellStepDto.setWorkCellDtos(workCellDtos);
                });
                rworkerInspectionWorkCellStepDtos.add(rworkerInspectionWorkCellStepDto);
            });
        }

        return rworkerInspectionWorkCellStepDtos;
    }

    /**
     * 通过 投产工单编码+检测类型+工位+工序 + 容器 获取质检方案
     *
     * @param rworkerProductWorkSheetCategoryDto 获取质检方案参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerQualityInspectionPlanDTO  质检方案
     * <AUTHOR>
     * @date 2023/4/26
     */
    @Override
    public RworkerQualityInspectionPlanDTO qualityInspectionPlan(RworkerProductWorkSheetCategoryDTO rworkerProductWorkSheetCategoryDto) {
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(rworkerProductWorkSheetCategoryDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
        WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findBySerialNumberAndDeleted(rworkerProductWorkSheetCategoryDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + rworkerProductWorkSheetCategoryDto.getSerialNumber() + "不存在");
        }
        Step step = stepRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getStepId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(step)) {
            throw new ResponseException("error.TodoInspectionStepIsNotExist", "当前待检测工序不存在");
        }
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null);
        if (rworkerProductWorkSheetCategoryDto.getCategory() <= Constants.INT_ONE && ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.TodoInspectionWorkCellIsNotExist", "当前待检测工位不存在");
        }
        Optional<InspectTask> inspectTaskOptional = inspectTaskRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getTaskId(), Constants.LONG_ZERO);
        PedigreeStepCheckRule pedigreeStepCheckRule = null;
        if (inspectTaskOptional.isPresent()) {
            pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findByIdAndDeleted(inspectTaskOptional.get().getCheckRuleId(), Constants.LONG_ZERO).orElse(null);
        }
        //获取定制工序中工艺路线
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(workSheet, subWorkSheet, workSheet.getPedigree(), step, rworkerProductWorkSheetCategoryDto.getCategory(), rworkerProductWorkSheetCategoryDto.getWorkCellId(), rworkerProductWorkSheetCategoryDto.getVarietyId());
        }
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            throw new ResponseException("error.TodoInspectionStepCheckRuleIsNotExist", "当前待检测工序未配置质检规则或质检规则已失效");
        }
        RworkerQualityInspectionPlanDTO rworkerQualityInspectionPlanDto = new RworkerQualityInspectionPlanDTO(pedigreeStepCheckRule);

        //todo：ContainerDetail containerDetail = null； 后期删除，当前保留是为了适配已生成的待检任务
        ContainerDetail containerDetail = null;
        int inputNumber = 0;
        //首检巡检  抽检终检，没有容器情况，计算报检数量
        if (rworkerProductWorkSheetCategoryDto.getCategory() <= Constants.INT_ONE) {
            Integer unqualifiedNumber = subWsProductionMode ?
                    batchWorkDetailRepository.sumUnqualifiedNumberBySubWorkSheetId(subWorkSheet.getId()) :
                    batchWorkDetailRepository.sumUnqualifiedNumberByWorkSheetId(workSheet.getId());
            inputNumber = (subWsProductionMode ? subWorkSheet.getNumber() : workSheet.getNumber()) - (ObjectUtils.isEmpty(unqualifiedNumber) ? Constants.INT_ZERO : unqualifiedNumber);
        } else {
            InspectTask inspectTask = inspectTaskOptional.orElseThrow(() -> new ResponseException("error.inspectTaskNotExist", "未获取到待检任务"));

            inputNumber = inspectTask.getNumber();
            //todo: 可能存在更新上线 没有number的值，这里需要添加逻辑处理，适配上线后已生成的待检任务，后期删除一下逻辑(直接删除if判断中的全部内容)
            if (inputNumber == Constants.INT_ZERO && Objects.nonNull(rworkerProductWorkSheetCategoryDto.getContainerCode())) {
                Optional<ContainerDetail> containerDetailOptional = subWsProductionMode ?
                        containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(subWorkSheet.getId(), step.getId(), rworkerProductWorkSheetCategoryDto.getContainerCode(), Constants.LONG_ZERO) :
                        containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(workSheet.getId(), step.getId(), rworkerProductWorkSheetCategoryDto.getContainerCode(), Constants.LONG_ZERO);
                containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("containerDetailNotExist", "未获取到容器对应的生产信息"));
                inputNumber = containerDetail.getQualifiedNumber();
            }
            //todo: 后期删除一下逻辑(直接删除if判断中的全部内容)
            if (inputNumber == Constants.INT_ZERO && Objects.nonNull(inspectTask.getSn())){
                inputNumber = Constants.INT_ONE;
            }
            //todo: 后期删除一下逻辑(直接删除if判断中的全部内容)
            if (inputNumber == Constants.INT_ZERO && Objects.isNull(inspectTask.getContainerCode()) && Objects.isNull(inspectTask.getSn())){
                Integer unqualifiedNumber = subWsProductionMode ?
                        batchWorkDetailRepository.sumUnqualifiedNumberBySubWorkSheetId(subWorkSheet.getId()) :
                        batchWorkDetailRepository.sumUnqualifiedNumberByWorkSheetId(workSheet.getId());
                inputNumber = (subWsProductionMode ? subWorkSheet.getNumber() : workSheet.getNumber()) - (ObjectUtils.isEmpty(unqualifiedNumber) ? Constants.INT_ZERO : unqualifiedNumber);
            }
        }
        //计算报检数量-抽样数量
        Integer sampleNumber = querySamplingStrategyService.getSampleResult(pedigreeStepCheckRule.getSampleCase(), inputNumber);
        rworkerQualityInspectionPlanDto.setProductWorkSheetId(subWsProductionMode ? subWorkSheet.getId() : workSheet.getId())
                .setNumber(inputNumber)
                .getSampleCaseDto()
                .setNumber(Math.min(inputNumber, sampleNumber));
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            List<RworkerQualityInspectionPlanDTO.CheckItemDTO> checkItems = pedigreeStepCheckItemList.stream().map(pedigreeStepCheckItem -> new RworkerQualityInspectionPlanDTO.CheckItemDTO(pedigreeStepCheckItem, sampleNumber)).collect(Collectors.toList());
            checkItems = checkItems.stream().peek(checkItem -> {
                //添加检测项目关联的文件
                List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(checkItem.getId());
                Optional.ofNullable(documentDTOList).ifPresent(checkItem::setDocumentDtos);
                //添加检测项目关联的缺陷原因
                List<DefectDTO> defectList = defectCheckItemRepository.findByCheckItemId(checkItem.getId());
                Optional.ofNullable(defectList).ifPresent(checkItem::setDefects);
            }).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setCheckItems(checkItems);
        }
        //添加sn列表
//        rworkerQualityInspectionPlanDto.setSnInfoList(getSnInfoList(subWsProductionMode, workSheet, subWorkSheet, containerDetail, step));
        rworkerQualityInspectionPlanDto.setSnInfoList(getSnInfoList(rworkerProductWorkSheetCategoryDto.getCategory(),
                subWsProductionMode, workSheet, subWorkSheet, containerDetail, step, inspectTaskOptional));
        //添加不良项目
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(workSheet.getPedigree(), workFlow.getId(), step.getId(), workSheet.getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = unqualifiedItems.stream().map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setUnqualifiedItems(unqualifiedItemDtos);
        }
        //获取可能的质检缓存
        if (Objects.nonNull(rworkerProductWorkSheetCategoryDto.getTaskId())) {
            inspectTaskOptional.ifPresent(inspectTask -> {
                rworkerQualityInspectionPlanDto.setCache(StringUtils.isNotBlank(inspectTask.getCache()) ? inspectTask.getCache() : null);
            });
        } else if (rworkerProductWorkSheetCategoryDto.getCategory() <= 1) {
            //查看是否存在当前工位+检查类型+项目类型+未处理记录 -》存在则调整为已处理
            inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(rworkerProductWorkSheetCategoryDto.getWorkCellId(), rworkerProductWorkSheetCategoryDto.getCategory(),
                    ObjectUtils.isEmpty(rworkerProductWorkSheetCategoryDto.getVarietyId()) ? null : rworkerProductWorkSheetCategoryDto.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO).ifPresent(inspectTask ->
                    rworkerQualityInspectionPlanDto.setCache(inspectTask.getCache())
            );
        }
        return rworkerQualityInspectionPlanDto;
    }

    /**
     * 获取 待检任务的sn列表
     *
     * @param category            待检任务类型
     * @param subWsProductionMode 投产模式
     * @param workSheet           工单
     * @param subWorkSheet        子工单
     * @param containerDetail     容器详情
     * @param step                工序详情
     * @param inspectTaskOptional 待检任务
     * @return List<String>
     */
    public List<String> getSnInfoList(Integer category, Boolean subWsProductionMode, WorkSheet workSheet, SubWorkSheet subWorkSheet,
                                      ContainerDetail containerDetail, Step step, Optional<InspectTask> inspectTaskOptional) {
        if (category <= WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()) {
            return getFirstOrIpqcInspectTaskSnList(subWsProductionMode, workSheet, subWorkSheet, containerDetail, step);
        }
        InspectTask inspectTask = inspectTaskOptional.orElseThrow(() -> new ResponseException("error.inspectTaskNotExist", "未获取到待检任务"));

        List<String> snList = inspectTaskDetailRepository.findSnListByInspectTaskIdAndDeleted(inspectTask.getId(), Constants.LONG_ZERO);

        ///todo: 此处是为了做上线是适配，后期直接返回snList，如果为空则 返回Collections.emptyList()
        if (ValidateUtils.isValid(snList)) {
            return snList;
        } else {
            return getFirstOrIpqcInspectTaskSnList(subWsProductionMode, workSheet, subWorkSheet, containerDetail, step);
        }
    }

    /**
     * 获取当前容器 工单 待检sn列表
     *
     * @param subWsProductionMode 投产粒度
     * @param workSheet           工单
     * @param subWorkSheet        子工单
     * @param containerDetail     容器详情
     * @param step                工序
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Date 2023/5/16
     */
    public List<String> getFirstOrIpqcInspectTaskSnList(Boolean subWsProductionMode, WorkSheet workSheet, SubWorkSheet subWorkSheet, ContainerDetail containerDetail, Step step) {
        List<SnWorkDetail> snWorkDetails = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(containerDetail)) {
            snWorkDetails = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(snWorkDetails)) {
            snWorkDetails = subWsProductionMode ?
                    snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO) :
                    snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (ValidateUtils.isValid(snWorkDetails)) {
            return snWorkDetails.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).map(SnWorkDetail::getSn).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 保存首检巡检 抽检终检检测记录
     *
     * @param rworkerInspectionResultDto 保存首检巡检抽检终检检测结果
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public void saveInspectionRecord(RworkerInspectionResultDTO rworkerInspectionResultDto) {
        //获取投产粒度
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findByIdAndDeleted(rworkerInspectionResultDto.getProductWorkSheetId(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
        WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findByIdAndDeleted(rworkerInspectionResultDto.getProductWorkSheetId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + "不存在");
        }
        if (rworkerInspectionResultDto.getSnCheckItemDtoList().stream().anyMatch(snCheckItemDto -> StringUtils.isBlank(snCheckItemDto.getSn()))) {
            throw new ResponseException("error.checkSnExistEmpty", "检测SN不可为空");
        }
        //工位
        WorkCell workCell = rworkerInspectionResultDto.getCategory() <= Constants.INT_ONE ? workCellRepository.findByIdAndDeleted(rworkerInspectionResultDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null) : null;
        if (rworkerInspectionResultDto.getCategory() <= Constants.INT_ONE && ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.TodoInspectionWorkCellIsNotExist", "当前待检测工位不存在");
        }
        //工序
        Step step = stepRepository.findByIdAndDeleted(rworkerInspectionResultDto.getStepId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.stepIsNotExist", "待检测工序不存在"));
        //检测方案
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findByIdAndDeleted(rworkerInspectionResultDto.getCheckRuleId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.checkRuleIsNotExist", "检测方案不存在"));
        //项目类型
        VarietyDTO variety = varietyRepository.findByIdAndDeleted(rworkerInspectionResultDto.getVarietyId(),
                Constants.LONG_ZERO).orElse(null);

        // 保存检查历史记录
        CheckHistory checkHistory = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveCheckHistoryRecord(rworkerInspectionResultDto, step, workCell, pedigreeStepCheckRule, variety, subWorkSheet, workSheet, subWsProductionMode);

        // 保存检测详情数据
        List<CheckHistoryDetail> checkHistoryDetails = saveCheckHistoryDetailsAndSnapshots(rworkerInspectionResultDto, checkHistory);

        // 更新检测任务
        updateInspectTask(checkHistory, subWsProductionMode);
        List<WsStep> wsStepList = null;
        if (Objects.nonNull(subWorkSheet)) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        //抽检-终检-合格通过直接放行
        if (checkHistory.getCategory() > WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory() && checkHistory.getResult()) {
            checkHistoryServices[0].addReleasedInspectUnqualified(checkHistoryDetails, wsStepList);
        }

        // 首检巡检抽检终检检测记录保存测试数据上传至qms
        BeanUtil.getHighestPrecedenceBean(IQualityService.class).uploadInspectionTestData(checkHistory, pedigreeStepCheckRule, rworkerInspectionResultDto.getSnCheckItemDtoList());

    }

    /**
     * 保存检查历史记录
     *
     * @param rworkerInspectionResultDto 下交保存参数
     * @param step                       工序
     * @param workCell                   工位
     * @param pedigreeStepCheckRule      检测规则
     * @param variety                    项目类型
     * @param subWorkSheet               子工单
     * @param workSheet                  工单
     * @param subWsProductionMode        投产粒度
     * @return net.airuima.domain.procedure.quality.CheckHistory
     * <AUTHOR>
     * @Date 2023/5/6
     */
    @Override
    public CheckHistory saveCheckHistoryRecord(RworkerInspectionResultDTO rworkerInspectionResultDto, Step step,
                                               WorkCell workCell, PedigreeStepCheckRule pedigreeStepCheckRule,
                                               VarietyDTO variety, SubWorkSheet subWorkSheet, WorkSheet workSheet,
                                               boolean subWsProductionMode) {
        CheckHistory checkHistory = new CheckHistory();
        SerialNumberDTO serialNumberDTO = new SerialNumberDTO();
        if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_FAI_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_IPQC_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_PQC_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_FQC_SERIAL_NUMBER);
        } else {
            serialNumberDTO.setCode(Constants.KEY_LQC_SERIAL_NUMBER);
        }
        checkHistory.setCategory(rworkerInspectionResultDto.getCategory())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDTO))
                .setNumber(rworkerInspectionResultDto.getCheckNumber())
                .setOperatorId(rworkerInspectionResultDto.getOperatorId())
                .setQualifiedNumber(rworkerInspectionResultDto.getQualifiedNumber())
                .setVirtual(Boolean.TRUE)
                .setUnqualifiedNumber(rworkerInspectionResultDto.getUnqualifiedNumber())
                .setStep(step).setWorkCell(workCell).setCheckRule(pedigreeStepCheckRule).setVarietyId(Optional.ofNullable(variety).map(VarietyDTO::getId).orElse(null))
                .setSubWorkSheet(subWsProductionMode ? subWorkSheet : null).setWorkSheet(!subWsProductionMode ? workSheet : null)
                .setResult(rworkerInspectionResultDto.getResult()).setInspectNumber(rworkerInspectionResultDto.getInspectNumber())
                .setRecordDate(LocalDateTime.now()).setContainerCode(rworkerInspectionResultDto.getContainerCode())
                .setSn(rworkerInspectionResultDto.getSn()).setInspectTaskId(rworkerInspectionResultDto.getInspectTaskId());
        for (RworkerInspectionResultDTO.SnCheckItemDTO snCheckItemDto : rworkerInspectionResultDto.getSnCheckItemDtoList()) {
            if (snCheckItemDto.getCheckItemResultDtoList().stream().anyMatch(checkItemResultDTO -> !checkItemResultDTO.getVirtual())) {
                checkHistory.setVirtual(Boolean.FALSE);
                break;
            }
        }
        //首检巡检
        if (checkHistory.getCategory() <= 1) {
            //检测合格-》 通过 反之重检
            checkHistory.setDealWay(rworkerInspectionResultDto.getResult() ? 1 : 2);
        } else {
            //检测合格-》 通过 反之待处理
            checkHistory.setDealWay(rworkerInspectionResultDto.getResult() ? 1 : 0);
        }
        checkHistory.setStatus(rworkerInspectionResultDto.getResult() ? Constants.TRUE : Constants.FALSE);
        return checkHistoryRepository.save(checkHistory);
    }

    /**
     * 保存检测详情数据和检测项目快照
     *
     * @param rworkerInspectionResultDto 下交保存参数
     * @param checkHistory               检测历史
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public List<CheckHistoryDetail> saveCheckHistoryDetailsAndSnapshots(RworkerInspectionResultDTO rworkerInspectionResultDto, CheckHistory checkHistory) {
        List<CheckHistoryDetail> checkHistoryDetails = Lists.newArrayList();
        List<CheckHistoryItemSnapshot> checkHistoryItemSnapshots = Lists.newArrayList();
        Map<Long, RworkerInspectionResultDTO.CheckItemResultDTO> checkItemResultGroup = new HashMap<>();
        //抽检、终检、末检时需要检查SN的最新生产状态是否是在当前质检工序上
        if (!checkHistory.getVirtual() && checkHistory.getCategory() > Constants.INT_ONE) {
            Set<String> checkSnList = rworkerInspectionResultDto.getSnCheckItemDtoList().stream().map(RworkerInspectionResultDTO.SnCheckItemDTO::getSn).collect(Collectors.toSet());
            List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(checkSnList.stream().toList(), Constants.LONG_ZERO);
            snWorkStatusList.stream().filter(snWorkStatus ->
                    !snWorkStatus.getLatestSnWorkDetail().getStep().getId().equals(checkHistory.getStep().getId())
                            || (Objects.nonNull(snWorkStatus.getSubWorkSheet()) && !snWorkStatus.getSubWorkSheet().getId().equals(checkHistory.getSubWorkSheet().getId()))
                            || (Objects.isNull(snWorkStatus.getSubWorkSheet()) && !snWorkStatus.getWorkSheet().getId().equals(checkHistory.getWorkSheet().getId()))).findFirst().ifPresent(snWorkStatus -> {
                throw new ResponseException("error.snWorkStatusNotMatchedCheckStep", "当前SN(" + snWorkStatus.getSn() + ")最新生产工序非当前质检工序");
            });
        }
        //保存检测详情数据
        Optional.ofNullable(rworkerInspectionResultDto.getSnCheckItemDtoList()).ifPresent(snCheckItemList -> {
            snCheckItemList.forEach(snCheckItemDto -> {
                UnqualifiedItem unqualifiedItem = ObjectUtils.isEmpty(snCheckItemDto.getUnqualifiedItemId()) ?
                        null : unqualifiedItemRepository.findByIdAndDeleted(snCheckItemDto.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(null);
                snCheckItemDto.getCheckItemResultDtoList().forEach(checkItemResultDto -> {
                    //检测项目详情
                    CheckHistoryDetail checkHistoryDetail = new CheckHistoryDetail();
                    checkHistoryDetail.setSn(snCheckItemDto.getSn())
                            .setCheckHistory(checkHistory)
                            .setCheckItemId(checkItemResultDto.getId())
                            .setCheckData(checkItemResultDto.getCheckData())
                            .setQualifiedRange(checkItemResultDto.getQualifiedRange())
                            .setResult(checkItemResultDto.getResult()).setVirtual(checkItemResultDto.getVirtual())
                            .setUnqualifiedItem(unqualifiedItem)
                            .setDefect(ObjectUtils.isEmpty(checkItemResultDto.getDefectId()) ? null :
                                    defectRepository.findByIdAndDeleted(checkItemResultDto.getDefectId(),
                                            Constants.LONG_ZERO).orElse(null))
                            .setNote(checkItemResultDto.getNote())
                            .setDisplay(Boolean.TRUE)
                            .setDeleted(Constants.LONG_ZERO);
                    checkHistoryDetail = checkHistoryDetailRepository.save(checkHistoryDetail);
                    // 绑定数据
                    if (!CollectionUtils.isEmpty(checkItemResultDto.getDocumentList())) {
                        DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO();
                        documentRelationDTO.setDocumentList(Optional.ofNullable(checkItemResultDto.getDocumentList()).orElse(Collections.emptyList()));
                        documentRelationDTO.setServiceName("mom").setRecordId(checkHistoryDetail.getId());
                        rbaseDocumentProxy.relation(documentRelationDTO);
                    }
                    if (!checkItemResultGroup.containsKey(checkItemResultDto.getId())) {
                        checkItemResultGroup.put(checkItemResultDto.getId(), checkItemResultDto);
                    }
                    checkHistoryDetails.add(checkHistoryDetail);
                });
            });
            //检测项目条件
            checkItemResultGroup.forEach((checkItemId, checkItemResultDto) -> {
                CheckHistoryItemSnapshot checkHistoryItemSnapshot = new CheckHistoryItemSnapshot();
                checkHistoryItemSnapshot.setCheckHistory(checkHistory).setCheckItemId(checkItemResultDto.getId()).setQualifiedRange(checkItemResultDto.getQualifiedRange())
                        .setVariety(ObjectUtils.isEmpty(checkItemResultDto.getVarietyId()) ? null :
                                varietyRepository.findByIdAndDeleted(checkItemResultDto.getVarietyId(),
                                        Constants.LONG_ZERO).orElse(null))
                        .setCheckWay(checkItemResultDto.getInspectWay())
                        .setAnalyseWay(checkItemResultDto.getAnalyseWay()).setFacility(checkItemResultDto.getFacility())
                        .setDeleted(Constants.LONG_ZERO);
                checkHistoryItemSnapshots.add(checkHistoryItemSnapshot);
            });
            checkHistoryItemSnapshotRepository.saveAll(checkHistoryItemSnapshots);
        });
        //（前提是 存在待检任务id，也就是通过请求工序生成）添加属于本次质检任务但是未进行检测的sn进去到详情中，默认合格
        if (Objects.nonNull(checkHistory.getInspectTaskId())){
            List<String> snLists = inspectTaskDetailRepository.findSnListByInspectTaskIdAndDeleted(checkHistory.getInspectTaskId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(snLists)){
                List<CheckHistoryDetail> noCheckHistoryDetailList = snLists.stream().filter(sn -> checkHistoryDetails.stream().noneMatch(checkHistoryDetail -> checkHistoryDetail.getSn().equals(sn)))
                        .map(sn -> {
                            //检测项目详情
                            CheckHistoryDetail checkHistoryDetail = new CheckHistoryDetail();
                            checkHistoryDetail.setSn(sn)
                                    .setCheckHistory(checkHistory)
                                    .setResult(Boolean.TRUE).setVirtual(Boolean.FALSE)
                                    .setDisplay(Boolean.FALSE)
                                    .setDeleted(Constants.LONG_ZERO);
                            return checkHistoryDetail;
                        }).collect(Collectors.toList());
                checkHistoryDetailRepository.saveAll(noCheckHistoryDetailList);
            }
        }
        return checkHistoryDetails;
    }

    /**
     * 更新检测任务
     *
     * @param checkHistory        检测历史
     * @param subWsProductionMode 投产粒度
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public void updateInspectTask(CheckHistory checkHistory, Boolean subWsProductionMode) {
        //首检，巡检
        if (checkHistory.getCategory() <= 1) {
            //更新最新检测记录
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, checkHistory.getCategory());
            //查看是否存在当前工位+检查类型+项目类型+未处理记录 -》存在则调整为已处理
            Optional<InspectTask> inspectTaskOptional = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(checkHistory.getWorkCell().getId(), checkHistory.getCategory(),
                    checkHistory.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO);
            //合格完成修改待检任务状态,不合格清楚缓存重新做
            inspectTaskOptional.ifPresent(inspectTask -> {
                        inspectTask.setCache(null);
                        if (checkHistory.getResult()) {
                            inspectTask.setStatus(Boolean.TRUE);
                        }
                        inspectTaskRepository.save(inspectTask);
                    }
            );
        } else if (checkHistory.getCategory() > Constants.INT_ONE) {
            // 抽检 终检
            InspectTask inspectTask;
            if (Objects.nonNull(checkHistory.getSn())) {
                // SN模式查询
                inspectTask = subWsProductionMode ?
                        inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(
                                checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(),
                                Boolean.FALSE, checkHistory.getCategory(), checkHistory.getSn(), Constants.LONG_ZERO) :
                        inspectTaskRepository.findByWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(
                                checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(),
                                Boolean.FALSE, checkHistory.getCategory(), checkHistory.getSn(), Constants.LONG_ZERO);
            } else {
                // 工单模式或容器模式查询
                inspectTask = subWsProductionMode ?
                        inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(
                                checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(),
                                Boolean.FALSE, checkHistory.getCategory(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null) :
                        inspectTaskRepository.findByWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(
                                checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(),
                                Boolean.FALSE, checkHistory.getCategory(), checkHistory.getContainerCode(), Constants.LONG_ZERO).orElse(null);
            }
            if (Objects.nonNull(inspectTask)) {
                inspectTaskRepository.save(inspectTask.setStatus(Boolean.TRUE).setCache(null));
            }
        }
    }


    /**
     * 更新最新检测记录 - 重置处理状态
     *
     * @param checkHistory 检测记录
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public LatestCheckResult updateLatestCheckResult(CheckHistory checkHistory, Integer category) {
        LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(checkHistory.getWorkCell().getId(), category, checkHistory.getVarietyId(), Constants.LONG_ZERO).orElseGet(LatestCheckResult::new);
        latestCheckResult.setCategory(category)
                .setSerialNumber(checkHistory.getSerialNumber())
                .setRecordDate(LocalDateTime.now())
                .setResult(checkHistory.getResult())
                .setWorkCell(checkHistory.getWorkCell())
                .setSubWorkSheet(checkHistory.getSubWorkSheet())
                .setDisplay(Boolean.TRUE)
                .setWorkSheet(checkHistory.getWorkSheet())
                .setVarietyId(checkHistory.getVarietyId())
                .setDealWay(checkHistory.getDealWay())
                .setExtendTime(Constants.INT_ZERO)
                .setNextCheckDate(null)
                .setStatus(checkHistory.getResult() ? Boolean.TRUE : Boolean.FALSE)
                .setDeleted(Constants.LONG_ZERO);

        //首检巡检 不能进行 界面二次操作
        if (checkHistory.getCategory() <= Constants.INT_ONE) {
            latestCheckResult.setStatus(Boolean.TRUE);
            //检查有无配置周期性配置，若有则更新下次检测时间
            Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), latestCheckResult.getCategory(), net.airuima.constant.Constants.INT_THREE, checkHistory.getVarietyId(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
            workCellCheckStartRuleOptional.ifPresent(workCellCheckStartRule -> {
                Boolean isExtend = Boolean.FALSE;
                if (workCellCheckStartRule.getExtendType() == Constants.INT_ONE) {
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusSeconds((long) ((workCellCheckStartRule.getDuration()) * 3600)));
                } else {
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusSeconds((long) ((workCellCheckStartRule.getDuration() + workCellCheckStartRule.getExtendTime()) * 3600)))
                            .setExtendTime(workCellCheckStartRule.getExtendTime());
                    isExtend = Boolean.TRUE;
                }
                latestCheckResultRepository.save(latestCheckResult);
                //生成待检任务记录
                BeanUtil.getHighestPrecedenceBean(IInspectionService.class)
                        .addInspectTask(new InspectionTaskDTO(checkHistory.getWorkSheet(), checkHistory.getSubWorkSheet(),
                                workCellCheckStartRule, latestCheckResult.getNextCheckDate(), isExtend, workCellCheckStartRule.getExtendTime()));
            });
            //当首检合格时巡检按照周期往后顺延
            if (category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() && checkHistory.getResult()) {
                Optional<WorkCellCheckStartRule> workCellCheckStartRuleOpt = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), net.airuima.constant.Constants.INT_ONE, net.airuima.constant.Constants.INT_THREE, checkHistory.getVarietyId(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
                workCellCheckStartRuleOpt.ifPresent(workCellCheckStartRule ->
                        BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, net.airuima.constant.Constants.INT_ONE)
                );
            }
        }
        //更新首检巡检
        if (latestCheckResult.getResult()) {
            //删除宽放记录，以及宽放记录对应的下交数据
            Optional<WorkCellExtendHistory> workCellExtendHistoryOptional = workCellExtendHistoryRepository.findByWorkCellIdAndCategoryAndVarietyIdAndResultAndDeleted(
                    latestCheckResult.getWorkCell().getId(), latestCheckResult.getCategory(), latestCheckResult.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO);
            workCellExtendHistoryOptional.ifPresent(workCellExtendHistory -> {
                workCellExtendHistory.setResult(Boolean.TRUE).setEndTime(LocalDateTime.now());
                workCellExtendHistoryRepository.save(workCellExtendHistory);
            });
            //将检查后的放宽工位下交数据变更为已处理
            workCellExtendStepDetailRepository.updateStatusByExtendWorkCellId(latestCheckResult.getWorkCell().getId(), Boolean.TRUE, Constants.LONG_ZERO);
        }
        return latestCheckResultRepository.save(latestCheckResult);
    }

    /**
     * 验证工位，工单 是否 需要进行首检
     *
     * @param rworkerCheckProcessInspectionDtoList 请求验证首检巡检参数列表
     * @return net.airuima.dto.base.BaseDTO 基础响应信息
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public BaseDTO checkProcessInspection(List<RworkerCheckProcessInspectionDTO> rworkerCheckProcessInspectionDtoList) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();

        for (RworkerCheckProcessInspectionDTO checkProcessInspectionDto : rworkerCheckProcessInspectionDtoList) {

            SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(checkProcessInspectionDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
            WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
            WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findBySerialNumberAndDeleted(checkProcessInspectionDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);

            if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
                throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + "不存在");
            }
            WorkCell workCell = workCellRepository.findByIdAndDeleted(checkProcessInspectionDto.getWorkCellId(), Constants.LONG_ZERO).orElseThrow(
                    () -> new ResponseException("error.notExistWorkCell", "当前生产工位不存在"));
            Long productWorkSheetId = subWsProductionMode ? subWorkSheet.getId() : workSheet.getId();
            boolean firstTimeWork = rworkerCheckProcessInspectionDtoList.get(Constants.INT_ZERO).getFirstTimeWork();
            //验证是否需要进行首检
            BaseDTO baseDto = inspectionServices[0].faiInspectionInfo(firstTimeWork, productWorkSheetId, workCell);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return baseDto.setMessage("首检：" + baseDto.getMessage());
            }
            //验证是否需要进行巡检
            baseDto = inspectionServices[0].ipqcInspectionInfo(firstTimeWork, productWorkSheetId, workCell);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return baseDto.setMessage("巡检：" + baseDto.getMessage());
            }
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 批量模式 创建待检任务
     *
     * @param subWsProductionMode 投产力度
     * @param subWorkSheet        子工单
     * @param workSheet           工单
     * @param step                工序
     * @param qualifiedNumber     合格数量
     * <AUTHOR>
     * @date 2023/5/9
     */
    @Override
    public boolean createBatchInspectTask(Boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, Integer qualifiedNumber) {
        // 使用静态工厂方法创建请求对象
        InspectTaskCreationRequest request = InspectTaskCreationRequest.forBatchMode(
                subWsProductionMode, subWorkSheet, workSheet, step, qualifiedNumber);

        return createInspectTaskInternal(request);
    }

    /**
     * 容器模式 创建待检任务
     *
     * @param containerDetail 容器生产详情
     * <AUTHOR>
     * @date 2023/5/9
     */
    @Override
    public boolean createContainerInspectTask(ContainerDetail containerDetail) {
        // 从容器详情中提取必要信息
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        WorkSheet workSheet = containerDetail.getBatchWorkDetail().getWorkSheet();
        Step step = containerDetail.getBatchWorkDetail().getStep();
        Boolean subWsProductionMode = Objects.nonNull(subWorkSheet);

        // 使用静态工厂方法创建请求对象
        InspectTaskCreationRequest request = InspectTaskCreationRequest.forContainerMode(
                subWsProductionMode, subWorkSheet, workSheet, step,
                containerDetail.getContainerCode(), containerDetail.getQualifiedNumber());

        return createInspectTaskInternal(request);
    }

    /**
     * 单只模式 创建待检任务
     *
     * @param requestMode         请求模式
     * @param subWsProductionMode 投产力度
     * @param subWorkSheet        子工单
     * @param workSheet           工单
     * @param step                工序
     * @param containerCode       容器
     * @param snList              sn列表
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    public boolean createSnInspectTask(Integer requestMode, Boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet,
                                       Step step, String containerCode, List<String> snList) {

        // 没有合格sn则不生成待检任务
        if (!ValidateUtils.isValid(snList)) {
            return false;
        }

        // 使用静态工厂方法创建请求对象
        InspectTaskCreationRequest request = InspectTaskCreationRequest.forSnMode(
                requestMode, subWsProductionMode, subWorkSheet, workSheet, step, containerCode, snList);

        return createInspectTaskInternal(request);
    }

    /**
     * 创建检测任务的通用内部方法
     * 抽取三个方法的公共逻辑，避免代码重复
     *
     * @param request 检测任务创建请求
     * @return 是否创建成功
     */
    private boolean createInspectTaskInternal(InspectTaskCreationRequest request) {
        // 获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(
                request.getWorkSheet(), request.getSubWorkSheet(), request.getStep());

        // 获取工位检测启动规则
        List<WorkCellCheckStartRule> workCellCheckStartRules = workCellCheckStartRuleRepository
                .findByWorkFlowIdAndStepIdAndIsEnableAndDeleted(
                        snapshotWorkFlow.getId(), request.getStep().getId(), Boolean.TRUE, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(workCellCheckStartRules)) {
            return Boolean.FALSE;
        }

        // 根据功能权限筛选检测规则
        List<WorkCellCheckStartRule> enabledRules = filterEnabledCheckRules(workCellCheckStartRules);

        if (!ValidateUtils.isValid(enabledRules)) {
            // 未获取到当前检测规则，查看是否还存在待检任务
            return checkExistingTodoTasks(request);
        }

        // 为每个启用的检测规则创建检测任务
        createInspectTasksForRules(request, enabledRules);

        return Boolean.TRUE;
    }

    /**
     * 根据功能权限筛选启用的检测规则
     *
     * @param workCellCheckStartRules 所有检测规则
     * @return 启用的检测规则列表
     */
    private List<WorkCellCheckStartRule> filterEnabledCheckRules(List<WorkCellCheckStartRule> workCellCheckStartRules) {
        List<WorkCellCheckStartRule> enabledRules = Lists.newArrayList();

        // 抽检 - PQC (Process Quality Control)
        if (FuncKeyUtil.checkApi(FuncKeyConstants.PQC)) {
            enabledRules.addAll(workCellCheckStartRules.stream()
                    .filter(rule -> rule.getCategory() == Constants.INSPECT_PQC_CATEGORY)
                    .toList());
        }

        // 终检 - FQC (Final Quality Control)
        if (FuncKeyUtil.checkApi(FuncKeyConstants.FQC)) {
            enabledRules.addAll(workCellCheckStartRules.stream()
                    .filter(rule -> rule.getCategory() == Constants.INSPECT_FQC_CATEGORY)
                    .toList());
        }

        // 末检 - LQC (Last Quality Control)
        if (FuncKeyUtil.checkApi(FuncKeyConstants.LQC)) {
            enabledRules.addAll(workCellCheckStartRules.stream()
                    .filter(rule -> rule.getCategory() == Constants.INSPECT_LQC_CATEGORY)
                    .toList());
        }

        return enabledRules;
    }

    /**
     * 检查是否存在待检任务
     * 当未获取到检测规则时，查看是否还存在待检的抽检、终检、末检任务
     *
     * @param request 检测任务创建请求
     * @return 是否存在待检任务
     */
    private boolean checkExistingTodoTasks(InspectTaskCreationRequest request) {
        // 定义需要检查的检测类型
        List<Integer> inspectCategories = Arrays.asList(
                WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()
        );

        Long todoTaskCount;

        // 根据请求模式选择不同的查询方法
        if (request.isWorkSheetRequestMode(request.getRequestMode())) {
            // 工单请求模式
            todoTaskCount = request.getSubWsProductionMode() ?
                    inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(
                            request.getSubWorkSheet().getId(), Boolean.FALSE, inspectCategories, Constants.LONG_ZERO) :
                    inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(
                            request.getWorkSheet().getId(), Boolean.FALSE, inspectCategories, Constants.LONG_ZERO);
        } else if (request.isContainerRequestMode(request.getRequestMode())) {
            // 容器请求模式
            todoTaskCount = inspectTaskRepository.countByContainerCodeAndStatusAndCategoryInAndDeleted(
                    request.getContainerCode(), Boolean.FALSE, inspectCategories, Constants.LONG_ZERO);
        } else if (request.isSnRequestMode(request.getRequestMode())) {
            // SN请求模式
            todoTaskCount = request.getSubWsProductionMode() ?
                    inspectTaskRepository.countBySubWorkSheetIdAndStepIdAndSnInAndStatusAndCategoryInAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(),
                            request.getSnList(), Boolean.FALSE, inspectCategories, Constants.LONG_ZERO) :
                    inspectTaskRepository.countByWorkSheetIdAndStepIdAndSnInAndStatusAndCategoryInAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(),
                            request.getSnList(), Boolean.FALSE, inspectCategories, Constants.LONG_ZERO);
        } else {
            return Boolean.FALSE;
        }

        return Objects.nonNull(todoTaskCount) && (todoTaskCount > Constants.INT_ZERO);
    }

    /**
     * 为每个启用的检测规则创建检测任务
     *
     * @param request      检测任务创建请求
     * @param enabledRules 启用的检测规则列表
     */
    private void createInspectTasksForRules(InspectTaskCreationRequest request, List<WorkCellCheckStartRule> enabledRules) {
        enabledRules.forEach(rule -> {
            // 根据请求模式创建不同类型的检测任务
            if (request.isWorkSheetRequestMode(request.getRequestMode())) {
                createWorkSheetInspectTask(request, rule);
            } else if (request.isContainerRequestMode(request.getRequestMode())) {
                createContainerInspectTask(request, rule);
            } else if (request.isSnRequestMode(request.getRequestMode())) {
                createSnInspectTask(request, rule);
            }
        });
    }

    /**
     * 创建工单模式的检测任务
     */
    private void createWorkSheetInspectTask(InspectTaskCreationRequest request, WorkCellCheckStartRule rule) {
        // 查找或创建检测任务
        InspectTask inspectTask = findOrCreateInspectTask(request, rule, null, null);

        // 设置工单模式特有的属性
        inspectTask.setWorkSheet(request.getWorkSheet())
                .setSubWorkSheet(request.getSubWorkSheet())
                .setStep(request.getStep())
                .setNumber(request.getNumber())
                .setCategory(rule.getCategory())
                .setStatus(Boolean.FALSE)
                .setVarietyId(rule.getVarietyId())
                .setWorkCellCheckStartRule(rule)
                .setTodoInspectedTime(LocalDateTime.now())
                .setIsExtend(Boolean.TRUE)
                .setExtendTime(Constants.DOUBLE_ZERRO)
                .setCache(null);

        // 检查是否已做过质检
        if (shouldSkipInspectTask(request, rule, null, null)) {
            return;
        }
        inspectTaskRepository.save(inspectTask);
        //添加检测任务详情
        addInspectTaskDetail(inspectTask, request.getSnList());
    }

    /**
     * 创建容器模式的检测任务
     */
    private void createContainerInspectTask(InspectTaskCreationRequest request, WorkCellCheckStartRule rule) {
        // 查找或创建检测任务
        InspectTask inspectTask = findOrCreateInspectTask(request, rule, request.getContainerCode(), null);

        // 设置容器模式特有的属性
        inspectTask.setWorkSheet(request.getWorkSheet())
                .setSubWorkSheet(request.getSubWorkSheet())
                .setStep(request.getStep())
                .setNumber(request.getNumber())
                .setCategory(rule.getCategory())
                .setStatus(Boolean.FALSE)
                .setContainerCode(request.getContainerCode())
                .setVarietyId(rule.getVarietyId())
                .setWorkCellCheckStartRule(rule)
                .setTodoInspectedTime(LocalDateTime.now())
                .setExtendTime(Constants.DOUBLE_ZERRO)
                .setIsExtend(Boolean.TRUE)
                .setCache(null);

        // 检查是否已做过质检
        if (shouldSkipInspectTask(request, rule, request.getContainerCode(), null)) {
            return;
        }
        inspectTaskRepository.save(inspectTask);
        //添加检测任务详情
        addInspectTaskDetail(inspectTask, request.getSnList());
    }

    /**
     * 创建SN模式的检测任务
     */
    private void createSnInspectTask(InspectTaskCreationRequest request, WorkCellCheckStartRule rule) {
        // SN模式只处理第一个SN
        String sn = request.getSnList().get(Constants.INT_ZERO);

        // 查找或创建检测任务
        InspectTask inspectTask = findOrCreateInspectTask(request, rule, null, sn);

        // 设置SN模式特有的属性
        inspectTask.setWorkSheet(request.getWorkSheet())
                .setSubWorkSheet(request.getSubWorkSheet())
                .setStep(request.getStep())
                .setNumber(Constants.INT_ONE) // SN模式数量固定为1
                .setCategory(rule.getCategory())
                .setStatus(Boolean.FALSE)
                .setSn(sn)
                .setVarietyId(rule.getVarietyId())
                .setWorkCellCheckStartRule(rule)
                .setTodoInspectedTime(LocalDateTime.now())
                .setExtendTime(Constants.DOUBLE_ZERRO)
                .setIsExtend(Boolean.TRUE)
                .setCache(null);

        // 检查是否已做过质检
        if (shouldSkipInspectTask(request, rule, null, sn)) {
            return;
        }
        inspectTaskRepository.save(inspectTask);
        //添加检测任务详情
        addInspectTaskDetail(inspectTask, Collections.singletonList(sn));
    }

    /**
     * 查找或创建检测任务
     * 根据不同的参数组合查找现有任务，如果不存在则创建新任务
     *
     * @param request       检测任务创建请求
     * @param rule          检测规则
     * @param containerCode 容器编码（可为null）
     * @param sn            SN编码（可为null）
     * @return 检测任务实例
     */
    private InspectTask findOrCreateInspectTask(InspectTaskCreationRequest request, WorkCellCheckStartRule rule,
                                                String containerCode, String sn) {
        if (Objects.nonNull(sn)) {
            // SN模式查询
            InspectTask inspectTask = request.getSubWsProductionMode() ?
                    inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(),
                            Boolean.FALSE, rule.getCategory(), sn, Constants.LONG_ZERO) :
                    inspectTaskRepository.findByWorkSheetIdAndStepIdAndStatusAndCategoryAndSnAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(),
                            Boolean.FALSE, rule.getCategory(), sn, Constants.LONG_ZERO);
            return Objects.isNull(inspectTask) ? new InspectTask() : inspectTask;
        } else {
            // 工单模式或容器模式查询
            return request.getSubWsProductionMode() ?
                    inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(),
                            Boolean.FALSE, rule.getCategory(), containerCode, Constants.LONG_ZERO).orElse(new InspectTask()) :
                    inspectTaskRepository.findByWorkSheetIdAndStepIdAndStatusAndCategoryAndContainerCodeAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(),
                            Boolean.FALSE, rule.getCategory(), containerCode, Constants.LONG_ZERO).orElse(new InspectTask());
        }
    }

    /**
     * 判断是否应该跳过创建检测任务
     * 如果当前工单工序已做过质检且任务ID为空，则跳过
     *
     * @param request       检测任务创建请求
     * @param rule          检测规则
     * @param containerCode 容器编码（可为null）
     * @param sn            SN编码（可为null）
     * @return 是否应该跳过
     */
    private boolean shouldSkipInspectTask(InspectTaskCreationRequest request, WorkCellCheckStartRule rule,
                                          String containerCode, String sn) {
        CheckHistory checkHistory;

        if (Objects.nonNull(sn)) {
            // SN模式检查历史
            checkHistory = !ObjectUtils.isEmpty(request.getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndSnAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), sn, Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndSnAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), sn, Constants.LONG_ZERO);
        } else if (Objects.nonNull(containerCode)) {
            // 容器模式检查历史
            checkHistory = !ObjectUtils.isEmpty(request.getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), containerCode, Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), containerCode, Constants.LONG_ZERO);
        } else {
            // 工单模式检查历史
            checkHistory = !ObjectUtils.isEmpty(request.getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeIsNullAndSnIsNullAndDeleted(
                            request.getSubWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndStepIdAndCategoryAndVarietyIdAndContainerCodeIsNullAndSnIsNullAndDeleted(
                            request.getWorkSheet().getId(), request.getStep().getId(), rule.getCategory(),
                            rule.getVarietyId(), Constants.LONG_ZERO);
        }

        // 如果存在检查历史且任务ID为空，则跳过创建
        return Objects.nonNull(checkHistory);
    }


    /**
     * 添加检测任务详情
     *
     * @param inspectTask 待检任务
     * @param snList      SN编码
     */
    public void addInspectTaskDetail(InspectTask inspectTask, List<String> snList) {
        if (!ValidateUtils.isValid(snList)) {
            return;
        }
        inspectTaskDetailRepository.saveAll(snList.stream().map(sn -> {
            InspectTaskDetail inspectTaskDetail = new InspectTaskDetail();
            inspectTaskDetail.setInspectTask(inspectTask)
                    .setSn(sn)
                    .setDeleted(Constants.LONG_ZERO);
            return inspectTaskDetail;
        }).collect(Collectors.toList()));
    }


    /**
     * 验证是否存在待检任务
     *
     * @param subWsProductionMode 投产力度
     * @param subWorkSheet        子工单
     * @param workSheet           工单
     */
    @Override
    public boolean getTodoInspectTask(Boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //未获取到当前检测规则，查看当前工单是否还存在待检的抽检，终检，末检如果存在则 返回true
        Long todoTaskCount = subWsProductionMode ?
                inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO) :
                inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        return Objects.nonNull(todoTaskCount) && (todoTaskCount > Constants.INT_ZERO) ? Boolean.TRUE : Boolean.FALSE;

    }

    /**
     * 保存温湿度信息
     *
     * @param humitureSaveRequestDTOList 温湿度待保存参数列表
     * @return net.airuima.dto.base.BaseResultDTO 结果信息
     */
    @Override
    public BaseResultDTO saveHumitureRecord(List<RworkerHumitureSaveRequestDTO> humitureSaveRequestDTOList) {
        humitureSaveRequestDTOList.forEach(humitureSaveRequestDTO -> {
            if (StringUtils.isBlank(humitureSaveRequestDTO.getAreaCode())) {
                return;
            }
            if (Objects.isNull(humitureSaveRequestDTO.getTemperature()) || Objects.isNull(humitureSaveRequestDTO.getHumidity())) {
                return;
            }
            OrganizationArea organizationArea = organizationAreaRepository.findByCodeAndDeleted(humitureSaveRequestDTO.getAreaCode(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(organizationArea)) {
                return;
            }
            HumitureStandardDTO humitureStandard = humitureStandardRepository.findByAreaIdAndDeleted(organizationArea.getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(humitureStandard)) {
                return;
            }
            HumitureCheckHistoryDTO entity = new HumitureCheckHistoryDTO();
            entity.setArea(organizationArea).setHumidity(humitureSaveRequestDTO.getHumidity()).setTemperature(humitureSaveRequestDTO.getTemperature()).setRecordDate(LocalDateTime.now());
            try {
                humitureCheckHistoryService.create(entity);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return new BaseResultDTO(Constants.OK);
    }


    /**
     * 待做的来料检验单
     *
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.IqcCheckHistory> 来料检验列表
     */
    @Override
    public List<IqcCheckHistory> getIqcTodo() {
        return iqcCheckHistoryService.todo();
    }

    /**
     * 放行待见任务
     * 首检：
     * 巡检：
     * 抽检：
     * 终检：
     * 末检：
     *
     * @param rworkerCancelTaskDto Rworker质检放行DTO
     */
    @Override
    public void cancelTask(RworkerCancelTaskDTO rworkerCancelTaskDto) {
        InspectTask inspectTask = inspectTaskRepository.findByIdAndDeleted(rworkerCancelTaskDto.getTaskId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("inspectTaskIsEmpty", "待检任务不存在"));
        if (inspectTask.getStatus()) {
            throw new ResponseException("taskStatus", "待检任务已处理,请勿重复处理");
        }
        //创建质检历史
        CheckHistory checkHistory = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveCancelCheckHistoryRecord(inspectTask, rworkerCancelTaskDto.getStaffId());
        //更新最新检测记录
        if (inspectTask.getCategory() <= InspectCategoryEnum.IPQC_INSPECTION.getCategory()) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, checkHistory.getCategory());
        }
        inspectTask.setStatus(Boolean.TRUE).setCache(null);
        inspectTaskRepository.save(inspectTask);
        //抽检、终检及末检可能在最后一个工序时需要更新工单状态
        if (inspectTask.getCategory() > InspectCategoryEnum.IPQC_INSPECTION.getCategory()) {
            SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
            WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
            checkHistoryService.updateWorkSheetStatus(Objects.nonNull(subWorkSheet), subWorkSheet, workSheet);
            //取消抽检、终检、末检任务后若当前工序无其他的抽检、终检、末检任务则需要更新工作台下个待做工序信息
            this.updateNextTodoStepInfoWhenTaskCanceled(inspectTask, subWorkSheet, workSheet);
        }
    }

    /**
     * 取消抽检、终检、末检任务后若当前工序无其他的抽检、终检、末检任务则需要更新工作台下个待做工序信息
     *
     * @param inspectTask  抽检、终检、末检任务
     * @param subWorkSheet 子工单
     * @param workSheet    工单
     * <AUTHOR>
     * @since 1.8.1
     */
    private void updateNextTodoStepInfoWhenTaskCanceled(InspectTask inspectTask, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //如果子工单或工单已完成那么删除所有工作台下个待做工序信息
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            nextTodoStepRepository.deleteByWorkSheetId(workSheet.getId());
            return;
        }
        if (Objects.nonNull(subWorkSheet) && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            nextTodoStepRepository.deleteBySubWorkSheetId(subWorkSheet.getId());
            return;
        }
        long todoTaskCount = Constants.LONG_ZERO;
        //如果容器编码不为空，则查询容器编码下未完成待检任务的个数,否则查询当前工序下未完成待检任务的个数
        if (StringUtils.isNotBlank(inspectTask.getContainerCode())) {
            todoTaskCount = inspectTaskRepository.countByContainerCodeAndStatusAndCategoryInAndDeleted(inspectTask.getContainerCode(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                    WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                    WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        } else {
            todoTaskCount = Objects.nonNull(subWorkSheet) ?
                    inspectTaskRepository.countBySubWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), inspectTask.getStep().getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO) :
                    inspectTaskRepository.countByWorkSheetIdAndStepIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), inspectTask.getStep().getId(), Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(), WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(), WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        }
        //如果未完成待检任务的个数大于0，则直接返回
        if (todoTaskCount > Constants.INT_ZERO) {
            return;
        }
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId();
        List<WsStep> wsSteps = commonService.findBatchWsStep(workSheet, subWorkSheet);
        //如果存在容器时则根据子工单或者工单、工序、容器获取最新的工序生产数据
        if (Objects.nonNull(inspectTask.getContainerCode())) {
            //根据容器编码获取容器生产详情
            ContainerDetail containerDetail = Objects.nonNull(subWorkSheet) ?
                    containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(subWorkSheet.getId(), inspectTask.getStep().getId(), inspectTask.getContainerCode(), Constants.LONG_ZERO).orElse(null) :
                    containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(workSheet.getId(), inspectTask.getStep().getId(), inspectTask.getContainerCode(), Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(containerDetail)) {
                return;
            }
            List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
            //如果有SN生产工序的情况下那么按照SN去更新工作台下个待做工序信息
            if (!CollectionUtils.isEmpty(snWorkDetailList)) {
                snWorkDetailList.forEach(snWorkDetail -> {
                    nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsSteps, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
                });
                return;
            }
            //否则按照容器去更新工作台下个待做工序信息
            nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsSteps, containerDetail, Boolean.FALSE);
            return;
        }
        //否则按照批量去更新工作台下个待做工序信息
        BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), inspectTask.getStep().getId(), Constants.LONG_ZERO).orElse(null) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), inspectTask.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsSteps, batchWorkDetail, Boolean.FALSE);
    }

    /**
     * 放行待检任务，创建检测历史经理
     *
     * @param inspectTask 待检任务
     * @param staffId     员工id
     * @return CheckHistory
     */
    @Override
    public CheckHistory saveCancelCheckHistoryRecord(InspectTask inspectTask, Long staffId) {
        CheckHistory checkHistory = new CheckHistory();
        SerialNumberDTO serialNumberDTO = new SerialNumberDTO();
        serialNumberDTO.setCode(InspectCategoryEnum.getKeyByCategory(inspectTask.getCategory()));
        checkHistory.setCategory(inspectTask.getCategory())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDTO))
                .setNumber(Constants.INT_ZERO)
                .setOperatorId(staffId)
                .setQualifiedNumber(Constants.INT_ZERO)
                .setVirtual(Boolean.FALSE)
                .setUnqualifiedNumber(Constants.INT_ZERO)
                .setStep(inspectTask.getStep()).setWorkCell(Objects.nonNull(inspectTask.getWorkCell()) ? inspectTask.getWorkCell() : null)
                .setVarietyId(inspectTask.getVarietyId())
                .setSubWorkSheet(Objects.nonNull(inspectTask.getSubWorkSheet()) ? inspectTask.getSubWorkSheet() : null)
                .setWorkSheet(Objects.nonNull(inspectTask.getWorkSheet()) ? inspectTask.getWorkSheet() : null)
                .setResult(Constants.TRUE).setInspectNumber(Constants.INT_ZERO)
                .setRecordDate(LocalDateTime.now())
                .setDealWay(Constants.INT_THREE)
                .setStatus(Boolean.TRUE);
        return checkHistoryRepository.save(checkHistory);
    }

    /**
     * 新增或者更新质检缓存
     *
     * @param rworkerQualityCacheSaveDTO 新增或者更新质检缓存参数DTO
     */
    @Override
    public void saveQualityInspectCache(RworkerQualityCacheSaveDTO rworkerQualityCacheSaveDTO) {
        if (StringUtils.isBlank(rworkerQualityCacheSaveDTO.getCache())) {
            return;
        }
        //若首件，巡检，抽检，终检，末检则更新对应的任务缓存
        if (Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() < InspectCategoryEnum.IQC_INSPECTION.getCategory()) {
            inspectTaskRepository.updateInspectTaskCache(rworkerQualityCacheSaveDTO.getId(), rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //若是IQC则更新IQC的记录缓存
        if (Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() == InspectCategoryEnum.IQC_INSPECTION.getCategory()) {
            iqcCheckHistoryRepository.updateCache(rworkerQualityCacheSaveDTO.getId(), rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //Oem质检更新缓存
        if (Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() == InspectCategoryEnum.OEM_INSPECTION.getCategory()) {
            rbaseOemProxy.updateCache(rworkerQualityCacheSaveDTO.getId(), rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //手动发起首检或巡检在保存缓存时需要新增一个首检或巡检任务并缓存
        if (Objects.isNull(rworkerQualityCacheSaveDTO.getId())
                && rworkerQualityCacheSaveDTO.getCategory() <= InspectCategoryEnum.IPQC_INSPECTION.getCategory()
                && Objects.nonNull(rworkerQualityCacheSaveDTO.getFaiIpqcTaskSaveInfo())) {
            RworkerQualityCacheSaveDTO.FaiIpqcTaskSaveInfo faiIpqcTaskSaveInfo = rworkerQualityCacheSaveDTO.getFaiIpqcTaskSaveInfo();
            InspectTask inspectTask = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(faiIpqcTaskSaveInfo.getWorkCellId(), rworkerQualityCacheSaveDTO.getCategory(),
                    faiIpqcTaskSaveInfo.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO).orElse(new InspectTask());
            boolean subWsProductionMode = commonService.subWsProductionMode();
            inspectTask.setCategory(rworkerQualityCacheSaveDTO.getCategory()).setWorkCell(new WorkCell(faiIpqcTaskSaveInfo.getWorkCellId()))
                    .setVarietyId(faiIpqcTaskSaveInfo.getVarietyId()).setWorkSheet(subWsProductionMode ?
                            null : new WorkSheet(faiIpqcTaskSaveInfo.getProductWorkSheetId()))
                    .setSubWorkSheet(subWsProductionMode ? new SubWorkSheet(faiIpqcTaskSaveInfo.getProductWorkSheetId()) : null).setStatus(Boolean.FALSE).setCache(rworkerQualityCacheSaveDTO.getCache())
                    .setIsExtend(Boolean.TRUE)
                    .setExtendTime(Constants.DOUBLE_ZERRO)
                    .setTodoInspectedTime(LocalDateTime.now())
                    .setWorkCellCheckStartRule(null)
                    .setDeleted(Constants.LONG_ZERO);
            inspectTaskRepository.save(inspectTask);
        }
    }


    /**
     * 组装下交保存测试数据，并上传至qms
     *
     * @param testDataSaveDto             上传数据
     * @param stepDynamicDataColumnGetDto 下交工序动态测试数据
     * @return void
     * <AUTHOR>
     * @date 2023/2/9
     */
    @Async
    @Override
    public void uploadQmsTestData(TestDataSaveDTO testDataSaveDto, StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto) {
        if (null == stepDynamicDataColumnGetDto) {
            return;
        }
        testDataSaveDto.setAnalyseCategoryName(stepDynamicDataColumnGetDto.getDynamicDataName()).setAnalyseCategoryCode(stepDynamicDataColumnGetDto.getDynamicDataCode());
        //添加测试数据明细
        List<TestDataDTO> testDataDtoList = Lists.newArrayList();
        this.addTestDataDTO(stepDynamicDataColumnGetDto.getColumnInfoList(), testDataDtoList);
        testDataSaveDto.setTestDataList(testDataDtoList);
        testDataSaveDto.setResult(testDataDtoList.stream().allMatch(TestDataDTO::getTestResult));
        //上传数据至qms
        rbaseQmsProxy.saveInstance(testDataSaveDto);
    }

    /**
     * 添加测试数据
     *
     * @param columnInfoList  下交保存的动态测试数据
     * @param testDataDtoList 上传至qms的数据
     * <AUTHOR>
     * @date 2023/2/9
     */
    public void addTestDataDTO(List<StepDynamicDataColumnGetDTO.ColumnInfo> columnInfoList, List<TestDataDTO> testDataDtoList) {

        if (!net.airuima.rbase.util.ValidateUtils.isValid(columnInfoList)) {
            return;
        }
        columnInfoList.forEach(columnInfo -> {
            if (net.airuima.rbase.util.ValidateUtils.isValid(columnInfo.getColumnInfoList())) {
                this.addTestDataDTO(columnInfo.getColumnInfoList(), testDataDtoList);
            } else if (StringUtils.isNotBlank(columnInfo.getColumnCode()) && !columnInfo.getColumnCode().equals("null")) {
                //添加测试数据
                TestDataDTO testDataDto = new TestDataDTO();
                testDataDto.setTestColumnName(columnInfo.getColumnName()).setTestColumn(columnInfo.getColumnCode())
                        .setTestValue(columnInfo.getColumnValue()).setTestResult(columnInfo.getResult());
                //是否存在测试结果存在则上传
                if (!ObjectUtils.isEmpty(columnInfo.getColumnValue()) && "false".equals(columnInfo.getColumnValue())) {
                    testDataDto.setTestResult(Boolean.FALSE);
                }
                //是否为文件
                if (net.airuima.rbase.util.ValidateUtils.isValid(columnInfo.getDocumentDTOList())) {
                    testDataDto.setTestValue(columnInfo.getDocumentDTOList().toString());
                }
                //递归的获取下层级的动态测试数据
                testDataDtoList.add(testDataDto);
            }
        });
    }

    /**
     * 首检巡检抽检终检检测记录保存测试数据上传至qms
     *
     * @param checkHistory          检测历史
     * @param pedigreeStepCheckRule 质检方案
     * @param snCheckItemDtoList    检测项目列表
     */
    @Override
    public void uploadInspectionTestData(CheckHistory checkHistory, PedigreeStepCheckRule pedigreeStepCheckRule, List<RworkerInspectionResultDTO.SnCheckItemDTO> snCheckItemDtoList) {
        if (CollectionUtils.isEmpty(snCheckItemDtoList)) {
            return;
        }
        // 获取操作的员工
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(checkHistory.getOperatorId(), Constants.LONG_ZERO);
        // 子工单投产 上传子工单号  否则上传工单号
        String workSheetSerialNumber = Objects.isNull(checkHistory.getSubWorkSheet()) ? checkHistory.getWorkSheet().getSerialNumber() : checkHistory.getSubWorkSheet().getSerialNumber();
        // 获取工单
        WorkSheet workSheet = Objects.isNull(checkHistory.getSubWorkSheet()) ? checkHistory.getWorkSheet() : checkHistory.getSubWorkSheet().getWorkSheet();
        // 根据工单获取组织架构
        OrganizationDTO organizationDto = workSheet.getOrganizationDto();
        // 获取工位
        WorkCell workCell = checkHistory.getWorkCell();
        // 获取产品谱系
        Pedigree pedigree = workSheet.getPedigree();
        List<TestDataSaveDTO> testDataSaveDtoList = Lists.newArrayList();
        for (RworkerInspectionResultDTO.SnCheckItemDTO snCheckItemDTO : snCheckItemDtoList) {
            //获取sn检测项目
            // 获取sn
            String sn = snCheckItemDTO.getSn();
            //测试数据列表
            List<TestDataDTO> testDataDtoList = Lists.newArrayList();
            // 获取检测项目结果列表
            List<RworkerInspectionResultDTO.CheckItemResultDTO> checkItemResultDtoList = Optional.ofNullable(snCheckItemDTO.getCheckItemResultDtoList()).orElse(Lists.newArrayList());
            for (RworkerInspectionResultDTO.CheckItemResultDTO checkItemResultDTO : checkItemResultDtoList) {
                // 获取检测项目结果
                // 根据检测项目结果设置测试数据明细
                TestDataDTO testDataDto = new TestDataDTO();
                testDataDto.setTestColumnName(checkItemResultDTO.getName()).setTestColumn(checkItemResultDTO.getCode())
                        .setTestValue(checkItemResultDTO.getCheckData())
                        .setTestResult(Optional.ofNullable(checkItemResultDTO.getResult()).orElse(Boolean.FALSE));
                testDataDtoList.add(testDataDto);
            }
            Boolean snTestResult = testDataDtoList.stream().map(TestDataDTO::getTestResult).allMatch(Boolean.TRUE::equals);
            // 构建qms测试数据
            TestDataSaveDTO testDataSaveDto = new TestDataSaveDTO();
            testDataSaveDto.setWorkSheetSerialNumber(workSheetSerialNumber)
                    .setSn(sn)
                    .setWorkCellName(Optional.ofNullable(workCell).map(WorkCell::getName).orElse(null))
                    .setWorkCellCode(Optional.ofNullable(workCell).map(WorkCell::getCode).orElse(null))
                    .setOrganizationCode(Optional.ofNullable(organizationDto).map(OrganizationDTO::getCode).orElse(null))
                    .setOrganizationName(Optional.ofNullable(organizationDto).map(OrganizationDTO::getName).orElse(null))
                    .setPedigreeCode(pedigree.getCode())
                    .setPedigreeName(pedigree.getName())
                    .setStaffName(Optional.ofNullable(staffDTO).map(StaffDTO::getName).orElse(null))
                    .setStaffCode(Optional.ofNullable(staffDTO).map(StaffDTO::getCode).orElse(null))
                    .setAnalyseCategoryCode(pedigreeStepCheckRule.getCode())
                    .setAnalyseCategoryName(pedigreeStepCheckRule.getName())
                    .setTestDataList(testDataDtoList)
                    .setResult(snTestResult)
                    .setTestDate(LocalDateTime.now());
            testDataSaveDtoList.add(testDataSaveDto);
        }
        //上传数据至qms
        rbaseQmsProxy.batchSaveInstance(testDataSaveDtoList);
    }

    /**
     * 新增工序检查历史
     *
     * @param rworkerInspectionStepDto 新增工序检查历史参数DTO
     * <AUTHOR>
     * @version 1.8.1
     * @since 1.8.1
     */
    @Override
    public void saveStepInspections(RworkerInspectionStepDTO rworkerInspectionStepDto) {
        if (!ValidateUtils.isValid(rworkerInspectionStepDto.getProcessInspectionDatas())) {
            return;
        }

        Step step = stepRepository.findByIdAndDeleted(rworkerInspectionStepDto.getStepId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("stepIsEmpty", "工序不存在"));

        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerInspectionStepDto.getWorkCellId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("workCellIsEmpty", "工位不存在"));

        Map<Long, PedigreeStepInspectionConfig> pedigreeStepInspectionConfigMap = pedigreeStepInspectionConfigRepository.findByIdInAndDeleted(
                        rworkerInspectionStepDto.getProcessInspectionDatas()
                                .stream()
                                .map(RworkerInspectionStepDTO.ProcessInspectionDTO::getInspectionConfigId)
                                .distinct().toList(), Constants.LONG_ZERO)
                .stream().collect(Collectors.toMap(PedigreeStepInspectionConfig::getId, Function.identity()));

        boolean subWsProductionMode = commonService.subWsProductionMode();

        LocalDateTime inspectionTime = LocalDateTime.now();

        //处理批量下交的工单对应的检测信息
        List<PedigreeStepInspectionHistory> pedigreeStepInspectionHistories = rworkerInspectionStepDto.getProcessInspectionDatas().stream().distinct().map(
                processInspectionData -> {
                    PedigreeStepInspectionHistory pedigreeStepInspectionHistory = new PedigreeStepInspectionHistory();
                    SubWorkSheet subWorkSheet = subWsProductionMode ?
                            //子工单
                            subWorkSheetRepository.findByIdAndDeleted(processInspectionData.getProductWorkSheetId(), Constants.LONG_ZERO)
                                    .orElseThrow(() -> new ResponseException("subWorkSheetIsEmpty", "子工单不存在")) : null;
                    WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() :
                            //工单
                            workSheetRepository.findByIdAndDeleted(processInspectionData.getProductWorkSheetId(), Constants.LONG_ZERO)
                                    .orElseThrow(() -> new ResponseException("workSheetIsEmpty", "工单不存在"));

                    PedigreeStepInspectionConfig pedigreeStepInspectionConfig =
                            pedigreeStepInspectionConfigMap.get(processInspectionData.getInspectionConfigId());
                    if (Objects.isNull(pedigreeStepInspectionConfig)) {
                        throw new ResponseException("inspectionConfigIsEmpty", "工序检查配置不存在");
                    }
                    return pedigreeStepInspectionHistory.setWorkSheet(workSheet)
                            .setSubWorkSheet(subWorkSheet)
                            .setInspectionResult(processInspectionData.getInspectionResult())
                            .setInspectionType(pedigreeStepInspectionConfig.getInspectionType())
                            .setInspectorId(rworkerInspectionStepDto.getInspectorId())
                            .setStaffId(rworkerInspectionStepDto.getStaffId())
                            .setStep(step).setWorkCell(workCell).setInspectionTime(inspectionTime)
                            .setNote(processInspectionData.getNote());
                }
        ).collect(Collectors.toList());

        Map<Long, PedigreeStepInspectionHistory> stepInspectionHistoryMap = pedigreeStepInspectionHistoryRepository.saveAll(pedigreeStepInspectionHistories)
                .stream().collect(Collectors.toMap((pedigreeStepInspectionHistory ->
                        Objects.isNull(pedigreeStepInspectionHistory.getSubWorkSheet()) ? pedigreeStepInspectionHistory.getWorkSheet().getId()
                                : pedigreeStepInspectionHistory.getSubWorkSheet().getId()), Function.identity()));

        //处理批量下交的工单对应的检测详情详细
        rworkerInspectionStepDto.getProcessInspectionDatas().forEach(processInspectionDto -> {

            PedigreeStepInspectionHistory pedigreeStepInspectionHistory =
                    stepInspectionHistoryMap.get(processInspectionDto.getProductWorkSheetId());

            List<PedigreeStepInspectionHistoryDetail> pedigreeStepInspectionHistoryDetailList = processInspectionDto.getStepDetailInspectionHandleDtoList().stream().map(stepDetailInspectionHandleDto -> {

                PedigreeStepInspectionHistoryDetail pedigreeStepInspectionHistoryDetail = new PedigreeStepInspectionHistoryDetail();
                pedigreeStepInspectionHistoryDetail.setPedigreeStepInspectionHistory(pedigreeStepInspectionHistory)
                        .setContainerCode(stepDetailInspectionHandleDto.getContainerCode())
                        .setSn(stepDetailInspectionHandleDto.getSn())
                        .setResult(Objects.isNull(stepDetailInspectionHandleDto.getResult()) ? null : stepDetailInspectionHandleDto.getResult())
                        .setHandleType(stepDetailInspectionHandleDto.getHandleType()).setDeleted(Constants.LONG_ZERO);
                return pedigreeStepInspectionHistoryDetail;
            }).collect(Collectors.toList());
            pedigreeStepInspectionHistoryDetailRepository.saveAll(pedigreeStepInspectionHistoryDetailList);
        });
    }


    /**
     * 通过投产工单及检测类型获取手动可检测的工序列表
     *
     * @param serialNumber (子)工单号或容器编码
     * @param requestMode  0:子(工单)号，1:容器号
     * @param category     检验类型(2,末检,3,终检;4,抽检;)
     * @return java.util.List<net.airuima.web.rest.rworker.quality.dto.RworkerInspectionWorkCellStepDTO> 获取待做工序及工位列表
     * <AUTHOR>
     */
    @Override
    public List<RworkerManualInspectionStepDTO> manualInspectionStep(Integer category, Integer requestMode, String serialNumber) {
        if (requestMode == Constants.INT_ZERO) {
            //获取配置的投产粒度是子工单还是工单
            boolean subWsProductionMode = commonService.subWsProductionMode();
            WorkSheet workSheet = null;
            SubWorkSheet subWorkSheet = null;
            List<WsStep> wsStepList = null;
            //根据投产粒度获取对应的子工单、工单及工序快照
            if (subWsProductionMode) {
                subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.subWorkSheetNotExist", "子工单不存在"));
                workSheet = subWorkSheet.getWorkSheet();
                wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            } else {
                workSheet = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.workSheetNotExist", "工单不存在"));
            }
            if (CollectionUtils.isEmpty(wsStepList)) {
                wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            }
            List<Long> stepIdList = wsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
            List<BatchWorkDetail> batchWorkDetailList = subWsProductionMode ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), stepIdList, Constants.LONG_ZERO) : batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), stepIdList, Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(batchWorkDetailList)) {
                throw new ResponseException("error.batchWorkDetailNotExist", "批量生产详情不存在");
            }
            //获取质检发起规则
            List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkFlowIdAndStepIdInAndCategoryAndDeleted(workSheet.getWorkFlow().getId(), stepIdList, category, Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(workCellCheckStartRuleList)) {
                throw new ResponseException("error.allStepCheckStartRuleNotExist", "工单的所有工序不存在质检发起规则");
            }
            List<Long> checkStartRuleStepIds = workCellCheckStartRuleList.stream().map(workCellCheckStartRule -> workCellCheckStartRule.getStep().getId()).toList();
            //过滤出存在质检规则的工单详情
            batchWorkDetailList = batchWorkDetailList.stream().filter(batchWorkDetail -> checkStartRuleStepIds.contains(batchWorkDetail.getStep().getId())).toList();
            if (CollectionUtils.isEmpty(batchWorkDetailList)) {
                return new ArrayList<>();
            }
            //工序详情列表按照ID进行升序排序
            List<BatchWorkDetail> sortedBatchWorkDetailList = batchWorkDetailList.stream()
                    .sorted(Comparator.comparing(BatchWorkDetail::getId))
                    .collect(Collectors.toList());
            return sortedBatchWorkDetailList.stream().map(batchWorkDetail -> new RworkerManualInspectionStepDTO(batchWorkDetail)).collect(Collectors.toList());
        } else {
            ContainerDetail containerDetail = containerDetailRepository.findTop1ByContainerCodeAndDeletedOrderByIdDesc(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.containerDetailNotExist", "容器不存在工序生产信息"));
            WorkSheet workSheet = Objects.nonNull(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ? containerDetail.getBatchWorkDetail().getSubWorkSheet().getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
            //获取质检发起规则
            List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkFlowIdAndStepIdInAndCategoryAndDeleted(workSheet.getWorkFlow().getId(), Collections.singletonList(containerDetail.getBatchWorkDetail().getStep().getId()), category, Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(workCellCheckStartRuleList)) {
                throw new ResponseException("error.allStepCheckStartRuleNotExist", "容器所在工序不存在质检发起规则");
            }
            RworkerManualInspectionStepDTO rworkerManualInspectionStepDTO = new RworkerManualInspectionStepDTO(containerDetail.getBatchWorkDetail()).setContainerDetailId(containerDetail.getId());
            return Collections.singletonList(rworkerManualInspectionStepDTO);
        }
    }

    /**
     * 扫描(子)工单号获取抽检、终检、末检的质检参数
     *
     * @param rworkerManualInspectCaseRequestDTO 手动发起质检请求参数
     * @return net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    public RworkerQualityInspectionPlanDTO manualInspectionCaseWhenBatch(RworkerManualInspectCaseRequestDTO rworkerManualInspectCaseRequestDTO) {
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findByIdAndDeleted(rworkerManualInspectCaseRequestDTO.getRequestId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.subWorkSheetNotExist", "子工单不存在")) : null;
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : workSheetRepository.findByIdAndDeleted(rworkerManualInspectCaseRequestDTO.getRequestId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.workSheetNotExist", "工单不存在"));
        BatchWorkDetail batchWorkDetail = subWsProductionMode ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerManualInspectCaseRequestDTO.getRequestId(), rworkerManualInspectCaseRequestDTO.getStepId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchWorkDetailNotExist", "工序尚未生产"))
                : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerManualInspectCaseRequestDTO.getRequestId(), rworkerManualInspectCaseRequestDTO.getStepId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.batchWorkDetailNotExist", "工序尚未生产"));
        if (batchWorkDetail.getQualifiedNumber() <= Constants.INT_ZERO) {
            throw new ResponseException("error.batchWorkDetailQualifiedNumberIsZero", "工序合格数为0，无法人工发起质检");
        }
        //若当前工序存在容器则需要扫描容器号发起质检
        containerDetailRepository.findTop1ByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO).ifPresent(containerDetail -> {
            throw new ResponseException("error.containderDetailExist", "请扫描容器号进行发起质检");
        });
        //获取当前待检工序的SN生产详情列表
        List<SnWorkDetail> snWorkDetailList = subWsProductionMode ?
                snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), rworkerManualInspectCaseRequestDTO.getStepId(), Constants.LONG_ZERO) :
                snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), rworkerManualInspectCaseRequestDTO.getStepId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(snWorkDetailList)) {
            throw new ResponseException("error.snWorkDetailNotExit", "当前工序不存在SN,，无法人工发起质检");
        }
        return this.addParamatersToManualInspection(rworkerManualInspectCaseRequestDTO, workSheet, subWorkSheet, snWorkDetailList);
    }


    /**
     * 扫描容器号获取抽检、终检、末检的质检参数
     *
     * @param rworkerManualInspectCaseRequestDTO 手动发起质检请求参数
     * @return net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    @Override
    public RworkerQualityInspectionPlanDTO manualInspectionCaseWhenContainer(RworkerManualInspectCaseRequestDTO rworkerManualInspectCaseRequestDTO) {
        ContainerDetail containerDetail = containerDetailRepository.findByIdAndDeleted(rworkerManualInspectCaseRequestDTO.getRequestId(), Constants.LONG_ZERO);
        if (Objects.isNull(containerDetail)) {
            throw new ResponseException("error.containderDetailNotExist", "容器工序生产数据不存在");
        }
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(snWorkDetailList)) {
            throw new ResponseException("error.snWorkDetailNotExit", "当前工序不存在SN,，无法人工发起质检");
        }
        List<SnWorkDetail> qualifiedSnWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).toList();
        if (CollectionUtils.isEmpty(qualifiedSnWorkDetailList)) {
            throw new ResponseException("error.qualifiedSnWorkDetailNotExist", "当前工序不存在合格的SN，无法人工发起质检");
        }
        BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
        SubWorkSheet subWorkSheet = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ? batchWorkDetail.getSubWorkSheet() : null;
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        return this.addParamatersToManualInspection(rworkerManualInspectCaseRequestDTO, workSheet, subWorkSheet, snWorkDetailList);
    }

    /**
     * 组织返回rworker的质检参数
     *
     * @param rworkerManualInspectCaseRequestDTO 手动发起质检请求参数
     * @param workSheet                          工单
     * @param subWorkSheet                       子工单
     * @param snWorkDetailList                   SN生产详情列表
     * @return net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO 返回的质检参数
     * <AUTHOR>
     * @since 1.8.1
     */
    private RworkerQualityInspectionPlanDTO addParamatersToManualInspection(RworkerManualInspectCaseRequestDTO rworkerManualInspectCaseRequestDTO, WorkSheet workSheet, SubWorkSheet subWorkSheet, List<SnWorkDetail> snWorkDetailList) {
        Step step = stepRepository.findByIdAndDeleted(rworkerManualInspectCaseRequestDTO.getStepId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.stepNotExist", "工序不存在"));
        //获取质检发起规则
        WorkCellCheckStartRule workCellCheckStartRule = workCellCheckStartRuleRepository.findByWorkFlowIdAndStepIdAndVarietyIdAndCategoryAndDeleted(workSheet.getWorkFlow().getId(), step.getId(), rworkerManualInspectCaseRequestDTO.getVarietyId(), rworkerManualInspectCaseRequestDTO.getCategory(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.checkStartRuleNotExist", "质检发起规则不存在"));
        //获取质检方案
        PedigreeStepCheckRule pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(workSheet, subWorkSheet, workSheet.getPedigree(), step, rworkerManualInspectCaseRequestDTO.getCategory(), null, rworkerManualInspectCaseRequestDTO.getVarietyId());
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            throw new ResponseException("error.TodoInspectionStepCheckRuleIsNotExist", "当前待检测工序未配置质检规则或质检规则已失效");
        }
        List<SnWorkDetail> qualifiedSnWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).toList();
        if (CollectionUtils.isEmpty(qualifiedSnWorkDetailList)) {
            throw new ResponseException("error.qualifiedSnWorkDetailNotExist", "当前工序不存在合格的SN，无法人工发起质检");
        }
        //获取满足条件能在当前工单当前工序上进行手动发起抽检、终检、末检的SN生产状态列表
        List<Integer> snWorkStatusList = workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()
                ? Arrays.asList(SnWorkStatusEnum.QUALIFIED.getStatus(), SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus())
                : Arrays.asList(SnWorkStatusEnum.QUALIFIED.getStatus(), SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
        List<SnWorkStatus> qualifiedSnWorkStatusList = Objects.nonNull(subWorkSheet)
                ? snWorkStatusRepository.findByLatestSnWorkDetailIdInAndSubWorkSheetIdAndStatusInAndDeleted(qualifiedSnWorkDetailList.stream().map(SnWorkDetail::getId).toList(), subWorkSheet.getId(), snWorkStatusList, Constants.LONG_ZERO)
                : snWorkStatusRepository.findByLatestSnWorkDetailIdInAndWorkSheetIdAndStatusInAndDeleted(qualifiedSnWorkDetailList.stream().map(SnWorkDetail::getId).toList(), workSheet.getId(), snWorkStatusList, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(qualifiedSnWorkStatusList)) {
            throw new ResponseException("error.satisfiedInspectSnWorkStatusNotExist", "当前工序SN生产状态不满足质检条件，无法人工发起质检");
        }

        //需要过滤掉已经在当前工单工序上质检过的SN
        List<CheckHistoryDetail> checkHistoryDetailList = Objects.nonNull(subWorkSheet) ? checkHistoryDetailRepository.findByCheckHistorySubWorkSheetIdAndCheckHistoryStepIdAndCheckHistoryCategoryAndDeleted(subWorkSheet.getId(), step.getId(), rworkerManualInspectCaseRequestDTO.getCategory(), Constants.LONG_ZERO)
                : checkHistoryDetailRepository.findByCheckHistoryWorkSheetIdAndCheckHistoryStepIdAndCheckHistoryCategoryAndDeleted(workSheet.getId(), step.getId(), rworkerManualInspectCaseRequestDTO.getCategory(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(checkHistoryDetailList)) {
            Set<String> checkedSnList = checkHistoryDetailList.stream().map(CheckHistoryDetail::getSn).collect(Collectors.toSet());
            qualifiedSnWorkStatusList = qualifiedSnWorkStatusList.stream().filter(snWorkStatus -> !checkedSnList.contains(snWorkStatus.getSn())).toList();
            if (CollectionUtils.isEmpty(qualifiedSnWorkStatusList)) {
                throw new ResponseException("error.satisfiedInspectSnWorkStatusNotExist", "当前工序不存在待质检的SN，无法人工发起质检");
            }
        }
        //以符合手动发起质检的SN子状态记录个数和用户填写的基数两者最小值作为此次质检抽样的基数
        int baseNumber = Math.min(qualifiedSnWorkStatusList.size(), rworkerManualInspectCaseRequestDTO.getBaseNumber());
        Integer sampleNumber = querySamplingStrategyService.getSampleResult(pedigreeStepCheckRule.getSampleCase(), baseNumber);
        RworkerQualityInspectionPlanDTO rworkerQualityInspectionPlanDto = new RworkerQualityInspectionPlanDTO(pedigreeStepCheckRule);
        rworkerQualityInspectionPlanDto.setProductWorkSheetId(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId())
                .setNumber(baseNumber)
                .getSampleCaseDto()
                .setNumber(Math.min(baseNumber, sampleNumber));
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeStepCheckItemList)) {
            throw new ResponseException("error.pedigreeStepCheckItemNotExist", "当质检方案对应的检验项目不存在");
        }
        List<RworkerQualityInspectionPlanDTO.CheckItemDTO> checkItems = pedigreeStepCheckItemList.stream().map(pedigreeStepCheckItem -> new RworkerQualityInspectionPlanDTO.CheckItemDTO(pedigreeStepCheckItem, sampleNumber)).collect(Collectors.toList());
        checkItems = checkItems.stream().peek(checkItem -> {
            //添加检测项目关联的文件
            List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(checkItem.getId());
            Optional.ofNullable(documentDTOList).ifPresent(checkItem::setDocumentDtos);
            //添加检测项目关联的缺陷原因
            List<DefectDTO> defectList = defectCheckItemRepository.findByCheckItemId(checkItem.getId());
            Optional.ofNullable(defectList).ifPresent(checkItem::setDefects);
        }).collect(Collectors.toList());
        rworkerQualityInspectionPlanDto.setCheckItems(checkItems);
        //添加sn列表
        rworkerQualityInspectionPlanDto.setSnInfoList(qualifiedSnWorkStatusList.stream().map(snWorkStatus -> snWorkStatus.getSn()).toList());
        //添加不良项目
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(workSheet.getPedigree(), workFlow.getId(), step.getId(), workSheet.getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = unqualifiedItems.stream().map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setUnqualifiedItems(unqualifiedItemDtos);
        }
        return rworkerQualityInspectionPlanDto;
    }

    /**
     * 工单 工序请求验证是否存在宽放记录
     *
     * @param subWorkSheet 子工单
     * @param workSheet    工单
     * @param stepId       工序
     */
    @Override
    public void validBatchWorkCellExtendHistory(SubWorkSheet subWorkSheet, WorkSheet workSheet, Long stepId) {


        //获取当前投产工单最近一次的工位投产工序下交记录且状态为未处理
        Optional<WorkCellExtendStepDetail> workCellExtendStepDetailOptional = Objects.nonNull(subWorkSheet) ?
                workCellExtendStepDetailRepository.findTop1BySubWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(subWorkSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO) :
                workCellExtendStepDetailRepository.findTop1ByWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(workSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO);

        if (workCellExtendStepDetailOptional.isEmpty()) {
            return;
        }
        WorkCellExtendStepDetail workCellExtendStepDetail = workCellExtendStepDetailOptional.get();

        WorkCell extendWorkCell = workCellExtendStepDetail.getExtendWorkCell();

        //获取请求工位存在的工位宽放时长的待检历史
        List<WorkCellExtendHistory> workCellExtendHistories = workCellExtendHistoryRepository
                .findByWorkCellIdAndResultAndDeleted(extendWorkCell.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellExtendHistories)) {
            return;
        }

        //获取权重最高的放宽处理方式继续条件卡控
        WorkCellExtendHistory workCellExtendHistory = workCellExtendHistories.stream()
                .max(Comparator.comparing(WorkCellExtendHistory::getExtendRule)).get();

        //不管控，可以走后续工序
        if (workCellExtendHistory.getExtendRule() == Constants.INT_ZERO) {
            return;
        }

        WsStep wsStep = commonService.findWsStep(Objects.isNull(workSheet) ? null : workSheet.getId(), Objects.isNull(subWorkSheet)
                ? null : subWorkSheet.getId(), stepId);

        if (Objects.isNull(wsStep)) {
            throw new ResponseException("wsStepIsEmpty", "投产工单投产工序快照不存在");
        }
        String preStepId = wsStep.getPreStepId();
        //当前工序的前只工序为空则直接放行；都还没有开始做
        if (!ValidateUtils.isValid(preStepId)) {
            return;
        }

        //只可以走放行第一次下交的工序，并且 放宽下交的后续工序属于，指定放行工序类型可以进行工序请求，非对应的工序类型不得请求
        if (workCellExtendHistory.getExtendRule() == Constants.INT_TWO && workCellExtendHistory.getExtendStepCategory().contains(String.valueOf(wsStep.getCategory()))) {
            return;
        }
        throw new ResponseException("workCellExtendStepProcessProhibit", "当前工单已进行 " + extendWorkCell.getName() + "[" + extendWorkCell.getCode() + "]工位宽放待检处理，请先发起首检|巡检后，进行工序请求！");
    }

    /**
     * 容器 工序请求验证是否存在宽放记录
     *
     * @param subWorkSheet  子工单
     * @param workSheet     工单
     * @param wsStep        工序
     * @param containerCode 容器号
     */
    @Override
    public void validContainerWorkCellExtendHistory(SubWorkSheet subWorkSheet, WorkSheet workSheet, WsStep wsStep, String containerCode) {

        //获取当前投产工单最近一次的工位投产工序下交记录且状态为未处理
        Optional<WorkCellExtendStepDetail> workCellExtendStepDetailOptional = Objects.nonNull(subWorkSheet) ?
                workCellExtendStepDetailRepository.findTop1BySubWorkSheetIdAndContainerDetailContainerCodeAndStatusAndDeletedOrderByRecordTimeDesc(subWorkSheet.getId(), containerCode, Boolean.FALSE, Constants.LONG_ZERO) :
                workCellExtendStepDetailRepository.findTop1ByWorkSheetIdAndContainerDetailContainerCodeAndStatusAndDeletedOrderByRecordTimeDesc(workSheet.getId(), containerCode, Boolean.FALSE, Constants.LONG_ZERO);

        if (workCellExtendStepDetailOptional.isEmpty()) {
            return;
        }
        WorkCellExtendStepDetail workCellExtendStepDetail = workCellExtendStepDetailOptional.get();

        WorkCell extendWorkCell = workCellExtendStepDetail.getExtendWorkCell();

        //获取请求工位存在的工位宽放时长的待检历史
        List<WorkCellExtendHistory> workCellExtendHistories = workCellExtendHistoryRepository
                .findByWorkCellIdAndResultAndDeleted(extendWorkCell.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellExtendHistories)) {
            return;
        }
        //获取权重最高的放宽处理方式继续条件卡控
        WorkCellExtendHistory workCellExtendHistory = workCellExtendHistories.stream()
                .max(Comparator.comparing(WorkCellExtendHistory::getExtendRule)).get();

        //不管控，可以走后续工序
        if (workCellExtendHistory.getExtendRule() == Constants.INT_ZERO) {
            return;
        }

        String preStepId = wsStep.getPreStepId();
        //当前工序的前只工序为空则直接放行；都还没有开始做
        if (!ValidateUtils.isValid(preStepId)) {
            return;
        }

        //只可以走放行第一次下交的工序，并且 放宽下交的后续工序属于，指定放行工序类型可以进行工序请求，非对应的工序类型不得请求
        if (workCellExtendHistory.getExtendRule() == Constants.INT_TWO && workCellExtendHistory.getExtendStepCategory().contains(String.valueOf(wsStep.getCategory()))) {
            return;
        }
        throw new ResponseException("workCellExtendContainerProcessProhibit", "当前容器已进行 " + extendWorkCell.getName() + "[" + extendWorkCell.getCode() + "]工位宽放待检处理，请先发起首检|巡检后，进行容器请求！");

    }

    /**
     * sn 工序请求验证是否存在宽放记录
     *
     * @param subWorkSheet 子工单
     * @param workSheet    工单
     * @param stepId       工序
     * @param sn           sn
     */
    @Override
    public void validSnWorkCellExtendHistory(SubWorkSheet subWorkSheet, WorkSheet workSheet, Long stepId, String sn) {

        //获取当前投产工单最近一次的工位投产工序下交记录且状态为未处理
        Optional<WorkCellExtendStepDetail> workCellExtendStepDetailOptional = Objects.nonNull(subWorkSheet) ?
                workCellExtendStepDetailRepository.findTop1BySubWorkSheetIdAndSnWorkDetailSnAndStatusAndDeletedOrderByRecordTimeDesc(subWorkSheet.getId(), sn, Boolean.FALSE, Constants.LONG_ZERO) :
                workCellExtendStepDetailRepository.findTop1ByWorkSheetIdAndSnWorkDetailSnAndStatusAndDeletedOrderByRecordTimeDesc(workSheet.getId(), sn, Boolean.FALSE, Constants.LONG_ZERO);

        if (workCellExtendStepDetailOptional.isEmpty()) {
            return;
        }

        WorkCellExtendStepDetail workCellExtendStepDetail = workCellExtendStepDetailOptional.get();
        WorkCell extendWorkCell = workCellExtendStepDetail.getExtendWorkCell();

        //获取请求工位存在的工位宽放时长的待检历史
        List<WorkCellExtendHistory> workCellExtendHistories = workCellExtendHistoryRepository
                .findByWorkCellIdAndResultAndDeleted(extendWorkCell.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellExtendHistories)) {
            return;
        }

        //获取权重最高的放宽处理方式继续条件卡控
        WorkCellExtendHistory workCellExtendHistory = workCellExtendHistories.stream()
                .max(Comparator.comparing(WorkCellExtendHistory::getExtendRule)).get();

        //不管控，可以走后续工序
        if (workCellExtendHistory.getExtendRule() == Constants.INT_ZERO) {
            return;
        }

        WsStep wsStep = commonService.findWsStep(Objects.isNull(workSheet) ? null : workSheet.getId(), Objects.isNull(subWorkSheet)
                ? null : subWorkSheet.getId(), stepId);

        if (Objects.isNull(wsStep)) {
            throw new ResponseException("wsStepIsEmpty", "投产SN投产工序快照不存在");
        }
        String preStepId = wsStep.getPreStepId();
        //当前工序的前只工序为空则直接放行；都还没有开始做
        if (!ValidateUtils.isValid(preStepId)) {
            return;
        }
        //只可以走放行第一次下交的工序，并且 放宽下交的后续工序属于，指定放行工序类型可以进行工序请求，非对应的工序类型不得请求
        if (workCellExtendHistory.getExtendRule() == Constants.INT_TWO && workCellExtendHistory.getExtendStepCategory().contains(String.valueOf(wsStep.getCategory()))) {
            return;
        }
        throw new ResponseException("workCellExtendSnProcessProhibit", "当前SN已进行 " + extendWorkCell.getName() + "[" + extendWorkCell.getCode() + "]工位宽放待检处理，请先发起首检|巡检后，进行单只请求！");
    }


    /**
     * 验证工位下交工序时 是否存在待做首检巡检任务
     *
     * @param workCellId 工位id
     * @return BaseDTO
     */
    @Override
    public BaseDTO checkWorkCellFaiOrIpqcTodoTask(Long workCellId) {
        Long todoTaskNumber = inspectTaskRepository.countByWorkCellIdAndStatusAndCategoryInAndTodoInspectedTimeLessThanEqualAndDeleted(workCellId, Boolean.FALSE,
                Arrays.asList(WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory())
                , LocalDateTime.now(), Constants.LONG_ZERO);
        if (Objects.nonNull(todoTaskNumber) && todoTaskNumber > Constants.INT_ZERO) {
            return new BaseDTO(Constants.KO, "工位存在待检任务,请先处理");
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 验证容器 是否存在待检的 抽检 末检，终检
     *
     * @param containerCode
     */
    @Override
    public void checkContainerCodePQCOrFQCOrLQCTdoTask(String containerCode) {
        //是否存在待检任务
        Long todoTaskNumber = inspectTaskRepository.countByContainerCodeAndStatusAndCategoryInAndDeleted(containerCode, Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        if (Objects.nonNull(todoTaskNumber) && todoTaskNumber > Constants.INT_ZERO) {
            throw new ResponseException("error.containerWaitInspectTask", "容器需要进行待检任务!");
        }
    }

    /**
     * 验证sn 是否存在待检的 抽检 末检，终检
     *
     * @param sn sn
     */
    @Override
    public void checkSnPQCOrFQCOrLQCTdoTask(String sn) {
        Long snTodoTaskNumber = inspectTaskRepository.countBySnAndStatusAndCategoryInAndDeleted(sn, Boolean.FALSE, Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()), Constants.LONG_ZERO);
        if (Objects.nonNull(snTodoTaskNumber) && snTodoTaskNumber > Constants.INT_ZERO) {
            throw new ResponseException("snToDoTaskNumber", "SN存在待检任务，请先处理");
        }
    }
}
