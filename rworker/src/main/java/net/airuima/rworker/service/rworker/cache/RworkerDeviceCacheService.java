package net.airuima.rworker.service.rworker.cache;

import net.airuima.rworker.domain.RworkerDeviceCache;
import net.airuima.rworker.repository.RworkerDeviceCacheRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class RworkerDeviceCacheService extends CommonJpaService<RworkerDeviceCache> {

    private final RworkerDeviceCacheRepository rworkerDeviceCacheRepository;

    public RworkerDeviceCacheService(RworkerDeviceCacheRepository rworkerDeviceCacheRepository) {
        this.rworkerDeviceCacheRepository = rworkerDeviceCacheRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RworkerDeviceCache> find(Specification<RworkerDeviceCache> spec, Pageable pageable) {
        return rworkerDeviceCacheRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RworkerDeviceCache> find(Specification<RworkerDeviceCache> spec) {
        return rworkerDeviceCacheRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RworkerDeviceCache> findAll(Pageable pageable) {
        return rworkerDeviceCacheRepository.findAll(pageable);
    }
}
