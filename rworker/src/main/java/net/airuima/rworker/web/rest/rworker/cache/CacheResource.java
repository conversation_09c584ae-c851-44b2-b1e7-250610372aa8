package net.airuima.rworker.web.rest.rworker.cache;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.rbase.dto.rworker.cache.dto.RworkerCacheSaveRequestDTO;
import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.device.IRworkerDeviceService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@Tag(name = "RWorker-Web缓存相关Resource", description = "RWorker-Web缓存相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker/cache")
@AuthSkip("IE")
public class CacheResource {

    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private IRworkerDeviceService[] rworkerDeviceServices;


    /**
     * 新增或更新Rworker缓存信息
     *
     * @param rworkerCacheSaveRequestDTO 待保存rworker工作缓存参数
     */
    @Operation(summary = "保存Rworker缓存信息")
    @PreAuthorize("@sc.checkSecurity()")
    @PreventRepeatSubmit
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> createCacheInfo(@RequestBody RworkerCacheSaveRequestDTO rworkerCacheSaveRequestDTO) {
        try {
            RworkerCache rworkerCache = rworkerCacheServices[0].saveInstance(rworkerCacheSaveRequestDTO);
            rworkerDeviceServices[0].cacheHandle(rworkerCache);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过工位ID获取缓存识别码
     *
     * @param workCellId 工位ID
     * @return 缓存识别码
     */
    @Operation(summary = "通过工位ID获取缓存唯一识别码")
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/uuid/{workCellId}")
    public ResponseEntity<ResponseData<String>> uuid(@PathVariable("workCellId") Long workCellId) {
        try {
            return ResponseData.<String>ok(rworkerCacheServices[0].findUuidByWorkCellId(workCellId));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过识别码获取缓存内容
     *
     * @param uuid 缓存识别码
     * @return 缓存内容
     */
    @Operation(summary = "通过缓存识别码uuid获取缓存内容")
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/cache/{uuid}")
    public ResponseEntity<ResponseData<String>> cache(@PathVariable("uuid") String uuid) {
        try {
            return ResponseData.<String>ok(rworkerCacheServices[0].findCacheByUuid(uuid));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
