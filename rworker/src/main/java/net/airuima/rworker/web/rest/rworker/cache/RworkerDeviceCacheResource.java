package net.airuima.rworker.web.rest.rworker.cache;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rworker.domain.RworkerDeviceCache;
import net.airuima.rworker.service.rworker.cache.RworkerDeviceCacheService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Rworker设备缓存")
@RestController
@RequestMapping("/api/rworker-device-cache")
@AuthorityRegion("Rworker")
public class RworkerDeviceCacheResource extends ProtectBaseResource<RworkerDeviceCache> {

    private final RworkerDeviceCacheService rworkerDeviceCacheService;

    public RworkerDeviceCacheResource(RworkerDeviceCacheService rworkerDeviceCacheService) {
        this.rworkerDeviceCacheService = rworkerDeviceCacheService;
        this.mapUri = "/api/rworker-device-cache";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "Rworker设备缓存");
    }
}
