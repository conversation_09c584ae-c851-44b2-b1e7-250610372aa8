package net.airuima.rworker.repository;

import net.airuima.rworker.domain.RworkerDeviceCache;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RworkerDeviceCacheRepository extends LogicDeleteableRepository<RworkerDeviceCache>, JpaSpecificationExecutor<RworkerDeviceCache>, JpaRepository<RworkerDeviceCache, Long> {
}
