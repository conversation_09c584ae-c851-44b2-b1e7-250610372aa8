<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="ruima" id="1753329262730-1">
        <createTable tableName="procedure_rworker_device_cache" remarks="Rworker设备缓存">
            <column autoIncrement="true" name="id" type="bigint(20)">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="总工单ID" type="bigint(20)"/>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="bigint(20)"/>
            <column name="step_id" remarks="工序ID" type="bigint(20)"/>
            <column name="container_id" remarks="容器ID" type="bigint(20)"/>
            <column name="number" remarks="完成数量" type="int(11)"/>
            <column name="status" remarks="状态" type="tinyint(2)"/>
            <column name="type" remarks="缓存类型" type="tinyint(2)"/>
            <column name="cache" remarks="下交缓存" type="longtext"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间"
                    type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
